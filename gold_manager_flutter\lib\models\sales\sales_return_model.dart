import 'package:gold_manager_flutter/models/sales/sales_item_model.dart';

/// 销售退货单模型
class SalesReturn {
  final int? id;
  final String returnNo;
  final int orderId;
  final String orderNo;
  final int customerId;
  final String customerName;
  final int storeId;
  final String storeName;
  final int userId;
  final String userName;
  final double totalAmount;
  final String returnReason;
  final String remark;
  final int status; // 0: 待审核, 1: 已审核, 2: 已取消
  final int createdAt;
  final int updatedAt;
  final List<SalesReturnItem> items;

  const SalesReturn({
    this.id,
    required this.returnNo,
    required this.orderId,
    required this.orderNo,
    required this.customerId,
    required this.customerName,
    required this.storeId,
    required this.storeName,
    required this.userId,
    required this.userName,
    required this.totalAmount,
    required this.returnReason,
    this.remark = '',
    required this.status,
    required this.createdAt,
    required this.updatedAt,
    required this.items,
  });

  /// 退货单状态文本
  String get statusText {
    switch (status) {
      case 0:
        return '待审核';
      case 1:
        return '已审核';
      case 2:
        return '已取消';
      default:
        return '未知状态';
    }
  }

  /// 从JSON构造
  factory SalesReturn.fromJson(Map<String, dynamic> json) {
    return SalesReturn(
      id: json['id'],
      returnNo: json['return_no'],
      orderId: json['order_id'],
      orderNo: json['order_no'],
      customerId: json['customer_id'],
      customerName: json['customer_name'],
      storeId: json['store_id'],
      storeName: json['store_name'],
      userId: json['user_id'],
      userName: json['user_name'],
      totalAmount: json['total_amount'].toDouble(),
      returnReason: json['return_reason'],
      remark: json['remark'] ?? '',
      status: json['status'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
      items: (json['items'] as List<dynamic>)
          .map((item) => SalesReturnItem.fromJson(item))
          .toList(),
    );
  }

  /// 转为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'return_no': returnNo,
      'order_id': orderId,
      'order_no': orderNo,
      'customer_id': customerId,
      'customer_name': customerName,
      'store_id': storeId,
      'store_name': storeName,
      'user_id': userId,
      'user_name': userName,
      'total_amount': totalAmount,
      'return_reason': returnReason,
      'remark': remark,
      'status': status,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'items': items.map((item) => item.toJson()).toList(),
    };
  }

  /// 创建退货单草稿
  factory SalesReturn.draft({
    required int orderId,
    required String orderNo,
    required int customerId,
    required String customerName,
    required int storeId,
    required String storeName,
    required int userId,
    required String userName,
  }) {
    final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    return SalesReturn(
      returnNo: 'DRAFT-${now.toString()}',
      orderId: orderId,
      orderNo: orderNo,
      customerId: customerId,
      customerName: customerName,
      storeId: storeId,
      storeName: storeName,
      userId: userId,
      userName: userName,
      totalAmount: 0,
      returnReason: '',
      status: 0,
      createdAt: now,
      updatedAt: now,
      items: [],
    );
  }

  /// 复制对象并修改部分字段
  SalesReturn copyWith({
    int? id,
    String? returnNo,
    int? orderId,
    String? orderNo,
    int? customerId,
    String? customerName,
    int? storeId,
    String? storeName,
    int? userId,
    String? userName,
    double? totalAmount,
    String? returnReason,
    String? remark,
    int? status,
    int? createdAt,
    int? updatedAt,
    List<SalesReturnItem>? items,
  }) {
    return SalesReturn(
      id: id ?? this.id,
      returnNo: returnNo ?? this.returnNo,
      orderId: orderId ?? this.orderId,
      orderNo: orderNo ?? this.orderNo,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      storeId: storeId ?? this.storeId,
      storeName: storeName ?? this.storeName,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      totalAmount: totalAmount ?? this.totalAmount,
      returnReason: returnReason ?? this.returnReason,
      remark: remark ?? this.remark,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      items: items ?? this.items,
    );
  }
}

/// 销售退货明细项模型
class SalesReturnItem {
  final int? id;
  final int? returnId;
  final int orderId;
  final int orderItemId;
  final int jewelryId;
  final String jewelryName;
  final String jewelryCode;
  final double goldWeight;
  final double silverWeight;
  final double price;
  final double amount;
  final int quantity;
  final String returnReason;
  final String remark;

  const SalesReturnItem({
    this.id,
    this.returnId,
    required this.orderId,
    required this.orderItemId,
    required this.jewelryId,
    required this.jewelryName,
    required this.jewelryCode,
    required this.goldWeight,
    required this.silverWeight,
    required this.price,
    required this.amount,
    required this.quantity,
    required this.returnReason,
    this.remark = '',
  });

  /// 从JSON构造
  factory SalesReturnItem.fromJson(Map<String, dynamic> json) {
    return SalesReturnItem(
      id: json['id'],
      returnId: json['return_id'],
      orderId: json['order_id'],
      orderItemId: json['order_item_id'],
      jewelryId: json['jewelry_id'],
      jewelryName: json['jewelry_name'],
      jewelryCode: json['jewelry_code'],
      goldWeight: json['gold_weight'].toDouble(),
      silverWeight: json['silver_weight'].toDouble(),
      price: json['price'].toDouble(),
      amount: json['amount'].toDouble(),
      quantity: json['quantity'],
      returnReason: json['return_reason'],
      remark: json['remark'] ?? '',
    );
  }

  /// 转为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'return_id': returnId,
      'order_id': orderId,
      'order_item_id': orderItemId,
      'jewelry_id': jewelryId,
      'jewelry_name': jewelryName,
      'jewelry_code': jewelryCode,
      'gold_weight': goldWeight,
      'silver_weight': silverWeight,
      'price': price,
      'amount': amount,
      'quantity': quantity,
      'return_reason': returnReason,
      'remark': remark,
    };
  }

  /// 从销售明细创建退货明细
  factory SalesReturnItem.fromSalesItem(
    SalesItem salesItem, {
    int quantity = 1,
    String returnReason = '',
  }) {
    return SalesReturnItem(
      orderId: salesItem.orderId!,
      orderItemId: salesItem.id!,
      jewelryId: salesItem.jewelryId,
      jewelryName: salesItem.jewelryName,
      jewelryCode: salesItem.jewelryCode,
      goldWeight: salesItem.goldWeight,
      silverWeight: salesItem.silverWeight,
      price: salesItem.price,
      amount: salesItem.price * quantity,
      quantity: quantity,
      returnReason: returnReason,
    );
  }

  /// 复制对象并修改部分字段
  SalesReturnItem copyWith({
    int? id,
    int? returnId,
    int? orderId,
    int? orderItemId,
    int? jewelryId,
    String? jewelryName,
    String? jewelryCode,
    double? goldWeight,
    double? silverWeight,
    double? price,
    double? amount,
    int? quantity,
    String? returnReason,
    String? remark,
  }) {
    return SalesReturnItem(
      id: id ?? this.id,
      returnId: returnId ?? this.returnId,
      orderId: orderId ?? this.orderId,
      orderItemId: orderItemId ?? this.orderItemId,
      jewelryId: jewelryId ?? this.jewelryId,
      jewelryName: jewelryName ?? this.jewelryName,
      jewelryCode: jewelryCode ?? this.jewelryCode,
      goldWeight: goldWeight ?? this.goldWeight,
      silverWeight: silverWeight ?? this.silverWeight,
      price: price ?? this.price,
      amount: amount ?? this.amount,
      quantity: quantity ?? this.quantity,
      returnReason: returnReason ?? this.returnReason,
      remark: remark ?? this.remark,
    );
  }
} 