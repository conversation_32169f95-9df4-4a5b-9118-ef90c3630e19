#include "flutter_window.h"

#include <optional>
#include <flutter/method_channel.h>
#include <flutter/standard_method_codec.h>

#include "flutter/generated_plugin_registrant.h"

FlutterWindow::FlutterWindow(const flutter::DartProject& project)
    : project_(project) {}

FlutterWindow::~FlutterWindow() {}

bool FlutterWindow::OnCreate() {
  if (!Win32Window::OnCreate()) {
    return false;
  }

  RECT frame = GetClientArea();

  // The size here must match the window dimensions to avoid unnecessary surface
  // creation / destruction in the startup path.
  flutter_controller_ = std::make_unique<flutter::FlutterViewController>(
      frame.right - frame.left, frame.bottom - frame.top, project_);
  // Ensure that basic setup of the controller was successful.
  if (!flutter_controller_->engine() || !flutter_controller_->view()) {
    return false;
  }
  RegisterPlugins(flutter_controller_->engine());
  SetChildContent(flutter_controller_->view()->GetNativeWindow());

  // Setup native window control method channel
  SetupWindowControlChannel();

  flutter_controller_->engine()->SetNextFrameCallback([&]() {
    this->Show();
  });

  // Flutter can complete the first frame before the "show window" callback is
  // registered. The following call ensures a frame is pending to ensure the
  // window is shown. It is a no-op if the first frame hasn't completed yet.
  flutter_controller_->ForceRedraw();

  return true;
}

void FlutterWindow::OnDestroy() {
  if (flutter_controller_) {
    flutter_controller_ = nullptr;
  }

  Win32Window::OnDestroy();
}

LRESULT
FlutterWindow::MessageHandler(HWND hwnd, UINT const message,
                              WPARAM const wparam,
                              LPARAM const lparam) noexcept {
  // Give Flutter, including plugins, an opportunity to handle window messages.
  if (flutter_controller_) {
    std::optional<LRESULT> result =
        flutter_controller_->HandleTopLevelWindowProc(hwnd, message, wparam,
                                                      lparam);
    if (result) {
      return *result;
    }
  }

  switch (message) {
    case WM_FONTCHANGE:
      flutter_controller_->engine()->ReloadSystemFonts();
      break;
  }

  return Win32Window::MessageHandler(hwnd, message, wparam, lparam);
}

void FlutterWindow::SetupWindowControlChannel() {
  auto channel = std::make_unique<flutter::MethodChannel<flutter::EncodableValue>>(
      flutter_controller_->engine()->messenger(),
      "com.goldmanager.window_control",
      &flutter::StandardMethodCodec::GetInstance());

  channel->SetMethodCallHandler(
      [this](const flutter::MethodCall<flutter::EncodableValue>& call,
              std::unique_ptr<flutter::MethodResult<flutter::EncodableValue>> result) {
        HandleWindowControlMethod(call, std::move(result));
      });

  // Save channel reference to prevent destruction
  window_control_channel_ = std::move(channel);
}

void FlutterWindow::HandleWindowControlMethod(
    const flutter::MethodCall<flutter::EncodableValue>& method_call,
    std::unique_ptr<flutter::MethodResult<flutter::EncodableValue>> result) {

  const std::string& method = method_call.method_name();

  if (method == "maximizeWindow") {
    MaximizeWindowNative();
    result->Success(flutter::EncodableValue(true));
  } else if (method == "minimizeWindow") {
    MinimizeWindowNative();
    result->Success(flutter::EncodableValue(true));
  } else if (method == "restoreWindow") {
    RestoreWindowNative();
    result->Success(flutter::EncodableValue(true));
  } else if (method == "setWindowSize") {
    const auto* arguments = std::get_if<flutter::EncodableMap>(method_call.arguments());
    if (arguments) {
      auto width_it = arguments->find(flutter::EncodableValue("width"));
      auto height_it = arguments->find(flutter::EncodableValue("height"));

      if (width_it != arguments->end() && height_it != arguments->end()) {
        int width = std::get<int>(width_it->second);
        int height = std::get<int>(height_it->second);
        SetWindowSizeNative(width, height);
        result->Success(flutter::EncodableValue(true));
      } else {
        result->Error("INVALID_ARGUMENTS", "Width and height are required");
      }
    } else {
      result->Error("INVALID_ARGUMENTS", "Arguments must be a map");
    }
  } else if (method == "getWindowSize") {
    auto size = GetWindowSizeNative();
    flutter::EncodableMap size_map;
    size_map[flutter::EncodableValue("width")] = flutter::EncodableValue(size.first);
    size_map[flutter::EncodableValue("height")] = flutter::EncodableValue(size.second);
    result->Success(flutter::EncodableValue(size_map));
  } else if (method == "isMaximized") {
    bool maximized = IsWindowMaximizedNative();
    result->Success(flutter::EncodableValue(maximized));
  } else {
    result->NotImplemented();
  }
}

void FlutterWindow::MaximizeWindowNative() {
  HWND hwnd = GetHandle();
  if (hwnd) {
    // Get monitor info for proper maximization
    HMONITOR monitor = MonitorFromWindow(hwnd, MONITOR_DEFAULTTONEAREST);
    MONITORINFO mi;
    mi.cbSize = sizeof(MONITORINFO);

    if (GetMonitorInfo(monitor, &mi)) {
      // Set window to cover the work area (excluding taskbar)
      SetWindowPos(hwnd, HWND_TOP,
                   mi.rcWork.left, mi.rcWork.top,
                   mi.rcWork.right - mi.rcWork.left,
                   mi.rcWork.bottom - mi.rcWork.top,
                   SWP_SHOWWINDOW | SWP_FRAMECHANGED);
    } else {
      // Fallback to standard maximize
      ShowWindow(hwnd, SW_MAXIMIZE);
    }

    // Force window to update
    UpdateWindow(hwnd);
  }
}

void FlutterWindow::MinimizeWindowNative() {
  HWND hwnd = GetHandle();
  if (hwnd) {
    ShowWindow(hwnd, SW_MINIMIZE);
  }
}

void FlutterWindow::RestoreWindowNative() {
  HWND hwnd = GetHandle();
  if (hwnd) {
    ShowWindow(hwnd, SW_RESTORE);
  }
}

void FlutterWindow::SetWindowSizeNative(int width, int height) {
  HWND hwnd = GetHandle();
  if (hwnd) {
    // Get current window position
    RECT rect;
    GetWindowRect(hwnd, &rect);

    // First try to restore window if it's maximized
    ShowWindow(hwnd, SW_RESTORE);

    // Set new window size while keeping current position
    SetWindowPos(hwnd, HWND_TOP, rect.left, rect.top, width, height,
                 SWP_SHOWWINDOW | SWP_FRAMECHANGED);

    // Force window to update
    UpdateWindow(hwnd);
    InvalidateRect(hwnd, nullptr, TRUE);
  }
}

std::pair<int, int> FlutterWindow::GetWindowSizeNative() {
  HWND hwnd = GetHandle();
  if (hwnd) {
    RECT rect;
    GetWindowRect(hwnd, &rect);
    return std::make_pair(rect.right - rect.left, rect.bottom - rect.top);
  }
  return std::make_pair(0, 0);
}

bool FlutterWindow::IsWindowMaximizedNative() {
  HWND hwnd = GetHandle();
  if (hwnd) {
    WINDOWPLACEMENT placement;
    placement.length = sizeof(WINDOWPLACEMENT);
    if (GetWindowPlacement(hwnd, &placement)) {
      return placement.showCmd == SW_MAXIMIZE;
    }
  }
  return false;
}
