import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../controllers/auth_controller.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/widgets/standard_card.dart';
import '../../../core/widgets/standard_buttons.dart' as custom_buttons;
import '../../../core/widgets/standard_input.dart';

/// 登录页面
class LoginView extends StatefulWidget {
  const LoginView({super.key});

  @override
  State<LoginView> createState() => _LoginViewState();
}

class _LoginViewState extends State<LoginView> {
  // 在StatefulWidget中管理TextEditingController
  late final TextEditingController _usernameController;
  late final TextEditingController _passwordController;
  late final AuthController controller;

  @override
  void initState() {
    super.initState();
    _usernameController = TextEditingController();
    _passwordController = TextEditingController();
    controller = Get.find<AuthController>();
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  /// 处理登录
  void _handleLogin() {
    if (!controller.loginFormKey.currentState!.validate()) {
      return;
    }

    final username = _usernameController.text.trim();
    final password = _passwordController.text;

    controller.login(username, password, onClearForm: () {
      _usernameController.clear();
      _passwordController.clear();
    });
  }

  @override
  Widget build(BuildContext context) {
    // 获取屏幕尺寸以实现响应式布局
    final Size screenSize = MediaQuery.of(context).size;
    final bool isLargeScreen = screenSize.width > 600;

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  maxWidth: isLargeScreen ? 400 : double.infinity,
                ),
                child: StandardCard(
                  padding: const EdgeInsets.all(AppTheme.paddingLarge),
                  borderRadius: AppTheme.borderRadiusLarge,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // 标题和Logo
                      _buildHeader(),
                      const SizedBox(height: AppTheme.paddingXLarge),

                      // 登录表单
                      _buildLoginForm(),
                      const SizedBox(height: AppTheme.paddingLarge),

                      // 登录按钮
                      _buildLoginButton(),

                      // 错误信息
                      _buildErrorMessage(),

                      // 版本信息
                      _buildVersionInfo(),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建页面标题和Logo
  Widget _buildHeader() {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(AppTheme.padding),
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withValues(alpha: 0.1),
            shape: BoxShape.circle,
          ),
          child: const Icon(
            Icons.diamond_outlined,
            size: 64,
            color: AppTheme.primaryColor,
          ),
        ),
        const SizedBox(height: 16),
        Text(
          'app_name'.tr,
          style: const TextStyle(
            fontSize: 28,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryColor,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Text(
          'login_subtitle'.tr,
          style: const TextStyle(
            fontSize: AppTheme.bodySize,
            color: AppTheme.secondaryTextColor,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// 构建登录表单
  Widget _buildLoginForm() {
    return Form(
      key: controller.loginFormKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 用户名输入框
          StandardTextField(
            controller: _usernameController,
            label: 'username'.tr,
            hintText: 'username_hint'.tr,
            prefixIcon: Icons.person,
            validator: controller.validateUsername,
            textInputAction: TextInputAction.next,
          ),
          const SizedBox(height: AppTheme.padding),

          // 密码输入框
          Obx(() => StandardTextField(
            controller: _passwordController,
            label: 'password'.tr,
            hintText: 'password_hint'.tr,
            prefixIcon: Icons.lock,
            suffixIcon: controller.isPasswordVisible.value
                ? Icons.visibility
                : Icons.visibility_off,
            onSuffixIconPressed: controller.togglePasswordVisibility,
            obscureText: !controller.isPasswordVisible.value,
            validator: controller.validatePassword,
            onSubmitted: (_) => _handleLogin(),
            textInputAction: TextInputAction.done,
          )),

          // 记住我选项和忘记密码链接
          Padding(
            padding: const EdgeInsets.only(top: 12.0),
            child: Row(
              children: [
                Obx(() => Checkbox(
                  value: controller.rememberMe.value,
                  onChanged: (_) => controller.toggleRememberMe(),
                  activeColor: AppTheme.primaryColor,
                )),
                Text(
                  'remember_me'.tr,
                  style: const TextStyle(fontSize: AppTheme.captionSize),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    // TODO: 实现忘记密码功能
                  },
                  child: Text(
                    'forgot_password'.tr,
                    style: const TextStyle(
                      color: AppTheme.primaryColor,
                      fontSize: AppTheme.captionSize,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建登录按钮
  Widget _buildLoginButton() {
    return Obx(() => custom_buttons.PrimaryButton(
      text: 'login'.tr,
      onPressed: _handleLogin,
      isLoading: controller.isLoading.value,
      width: double.infinity,
      size: custom_buttons.ButtonSize.large,
    ));
  }

  /// 构建错误信息
  Widget _buildErrorMessage() {
    return Obx(() => controller.errorMessage.value.isNotEmpty
        ? Container(
            margin: const EdgeInsets.only(top: 16.0),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppTheme.errorColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
              border: Border.all(
                color: AppTheme.errorColor.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.error_outline,
                  color: AppTheme.errorColor,
                  size: 20,
                ),
                const SizedBox(width: AppTheme.paddingSmall),
                Expanded(
                  child: Text(
                    controller.errorMessage.value,
                    style: const TextStyle(
                      color: AppTheme.errorColor,
                      fontSize: AppTheme.captionSize,
                    ),
                  ),
                ),
              ],
            ),
          )
        : const SizedBox.shrink());
  }

  /// 构建版本信息
  Widget _buildVersionInfo() {
    return Padding(
      padding: const EdgeInsets.only(top: 24.0),
      child: Text(
        '${'version'.tr}: 0.1.0',
        style: const TextStyle(
          color: AppTheme.secondaryTextColor,
          fontSize: AppTheme.smallSize,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
}