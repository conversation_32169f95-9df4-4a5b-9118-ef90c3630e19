"""
销售管理API的简单测试
用于验证API基本功能是否正常
"""

from fastapi.testclient import TestClient
import pytest
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from main import app

client = TestClient(app)


def test_sales_api_basic():
    """测试销售API基本功能"""
    
    # 测试获取销售明细（无认证，应该返回401）
    response = client.get("/api/v1/sales/items")
    assert response.status_code == 401
    
    # 测试获取销售统计（无认证，应该返回401）
    response = client.get("/api/v1/sales/statistics")
    assert response.status_code == 401
    

def test_sales_api_endpoints_exist():
    """测试销售API端点是否存在"""
    
    # 测试端点存在性（通过OPTIONS请求）
    response = client.options("/api/v1/sales/items")
    # 如果端点存在，不应该返回404
    assert response.status_code != 404
    
    response = client.options("/api/v1/sales/statistics")
    assert response.status_code != 404


if __name__ == "__main__":
    # 运行基本测试
    print("🧪 开始测试销售管理API...")
    
    try:
        test_sales_api_basic()
        print("✅ 基本API认证测试通过")
    except Exception as e:
        print(f"❌ 基本API测试失败: {e}")
    
    try:
        test_sales_api_endpoints_exist()
        print("✅ API端点存在性测试通过")
    except Exception as e:
        print(f"❌ API端点测试失败: {e}")
    
    print("🏁 测试完成")