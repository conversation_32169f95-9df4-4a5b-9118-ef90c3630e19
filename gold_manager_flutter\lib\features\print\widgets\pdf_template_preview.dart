import 'package:flutter/material.dart';

import '../../../models/print/print_template_config.dart';
import '../../../core/constants/border_styles.dart';
import '../../../core/utils/logger_service.dart';

/// PDF模板真实预览组件
/// 
/// 提供与PDF生成完全一致的预览效果
class PdfTemplatePreview extends StatefulWidget {
  /// 模板配置
  final PrintTemplateConfig template;
  
  /// 示例数据
  final Map<String, dynamic> sampleData;
  
  /// 缩放比例
  final double scale;

  const PdfTemplatePreview({
    super.key,
    required this.template,
    required this.sampleData,
    this.scale = 1.0,
  });

  @override
  State<PdfTemplatePreview> createState() => _PdfTemplatePreviewState();
}

class _PdfTemplatePreviewState extends State<PdfTemplatePreview> {
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppBorderStyles.borderColor),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // 预览工具栏
          _buildPreviewToolbar(),
          const Divider(height: 1),
          
          // 预览画布
          Expanded(
            child: _buildPreviewCanvas(),
          ),
        ],
      ),
    );
  }

  /// 构建预览工具栏
  Widget _buildPreviewToolbar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Icon(Icons.preview, color: Colors.blue[600], size: 20),
          const SizedBox(width: 8),
          const Text(
            '真实预览',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const Spacer(),
          Text(
            '比例: ${(widget.scale * 100).toInt()}%',
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
          ),
          const SizedBox(width: 16),
          Text(
            '${widget.template.pageConfig.width.toInt()}mm × ${widget.template.pageConfig.height.toInt()}mm',
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建预览画布
  Widget _buildPreviewCanvas() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // 计算画布尺寸，保持模板页面比例
        final aspectRatio = widget.template.pageConfig.width / widget.template.pageConfig.height;
        double canvasWidth = constraints.maxWidth - 32; // 留出边距
        double canvasHeight = canvasWidth / aspectRatio;

        if (canvasHeight > constraints.maxHeight - 32) {
          canvasHeight = constraints.maxHeight - 32;
          canvasWidth = canvasHeight * aspectRatio;
        }

        // 应用缩放
        canvasWidth *= widget.scale;
        canvasHeight *= widget.scale;

        return Center(
          child: Container(
            width: canvasWidth,
            height: canvasHeight,
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border.all(color: Colors.grey[400]!),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: _buildPdfContent(canvasWidth, canvasHeight),
          ),
        );
      },
    );
  }

  /// 构建PDF内容预览
  Widget _buildPdfContent(double canvasWidth, double canvasHeight) {
    LoggerService.d('🎨 构建PDF内容预览，画布尺寸: ${canvasWidth}x$canvasHeight');

    // 计算像素比例 (画布像素 / 实际mm)
    final pixelRatio = canvasWidth / widget.template.pageConfig.width;

    // 计算边距
    final margins = widget.template.pageConfig.margins;
    final leftMargin = margins.left * pixelRatio;
    final topMargin = margins.top * pixelRatio;
    final rightMargin = margins.right * pixelRatio;
    final bottomMargin = margins.bottom * pixelRatio;

    // 可用内容区域
    final contentWidth = canvasWidth - leftMargin - rightMargin;
    final contentHeight = canvasHeight - topMargin - bottomMargin;

    LoggerService.d('📐 布局计算: pixelRatio=$pixelRatio, contentSize=${contentWidth}x$contentHeight');

    return Container(
      margin: EdgeInsets.only(
        left: leftMargin,
        top: topMargin,
        right: rightMargin,
        bottom: bottomMargin,
      ),
      width: contentWidth,
      height: contentHeight,
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // 公司信息
            _buildCompanyInfo(pixelRatio),

            SizedBox(height: 4 * pixelRatio),

            // 表头信息
            _buildHeaderInfo(pixelRatio),

            SizedBox(height: 4 * pixelRatio),

            // 商品明细表格
            _buildItemTable(pixelRatio, contentWidth),

            SizedBox(height: 4 * pixelRatio),

            // 汇总信息
            _buildSummaryInfo(pixelRatio),

            SizedBox(height: 4 * pixelRatio),

            // 收款信息
            _buildPaymentInfo(pixelRatio),

            SizedBox(height: 8 * pixelRatio),

            // 页脚信息
            _buildFooterInfo(pixelRatio),

            // 底部留白
            SizedBox(height: 8 * pixelRatio),
          ],
        ),
      ),
    );
  }

  /// 构建公司信息
  Widget _buildCompanyInfo(double pixelRatio) {
    final companyInfo = widget.template.companyInfo;

    // 计算合适的字体大小，避免过大
    double calculateFontSize(double baseFontSize, [double factor = 1.0]) {
      final calculatedSize = baseFontSize * factor * pixelRatio * widget.scale;
      // 限制最大字体大小，避免溢出
      return calculatedSize.clamp(8.0, 24.0);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        // 公司名称
        if (companyInfo.companyName.isNotEmpty)
          Text(
            companyInfo.companyName,
            style: TextStyle(
              fontSize: calculateFontSize(companyInfo.fontSize),
              fontWeight: companyInfo.isBold ? FontWeight.bold : FontWeight.normal,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),

        // 公司地址
        if (companyInfo.showAddress)
          Padding(
            padding: EdgeInsets.only(top: 2 * pixelRatio),
            child: Text(
              widget.sampleData['stockOutData']?['store_address'] ?? '广东省深圳市罗湖区黄金珠宝城A座101号',
              style: TextStyle(
                fontSize: calculateFontSize(companyInfo.fontSize, 0.7),
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),

        // 公司电话
        if (companyInfo.showPhone)
          Padding(
            padding: EdgeInsets.only(top: 1 * pixelRatio),
            child: Text(
              '电话: ${widget.sampleData['stockOutData']?['store_phone'] ?? '0755-12345678'}',
              style: TextStyle(
                fontSize: calculateFontSize(companyInfo.fontSize, 0.7),
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
      ],
    );
  }

  /// 构建表头信息
  Widget _buildHeaderInfo(double pixelRatio) {
    final headerConfig = widget.template.headerConfig;
    final stockOutData = widget.sampleData['stockOutData'] as Map<String, dynamic>? ?? {};

    // 计算合适的字体大小
    double calculateFontSize(double baseFontSize) {
      final calculatedSize = baseFontSize * pixelRatio * widget.scale;
      return calculatedSize.clamp(6.0, 16.0);
    }

    final headerItems = <Widget>[];

    // 订单号
    if (headerConfig.showOrderNo) {
      headerItems.add(Text(
        '单号: ${stockOutData['order_no'] ?? 'CK20241201001'}',
        style: TextStyle(
          fontSize: calculateFontSize(headerConfig.fontSize),
        ),
      ));
    }

    // 客户
    if (headerConfig.showCustomer) {
      headerItems.add(Text(
        '客户: ${stockOutData['customer'] ?? '张三'}',
        style: TextStyle(
          fontSize: calculateFontSize(headerConfig.fontSize),
        ),
      ));
    }

    // 销售类型
    if (headerConfig.showSaleType) {
      headerItems.add(Text(
        '类型: ${stockOutData['sale_type'] == 'retail' ? '零售' : '批发'}',
        style: TextStyle(
          fontSize: calculateFontSize(headerConfig.fontSize),
        ),
      ));
    }

    // 日期时间
    if (headerConfig.showDateTime) {
      headerItems.add(Text(
        '时间: ${DateTime.now().toString().substring(0, 19)}',
        style: TextStyle(
          fontSize: calculateFontSize(headerConfig.fontSize),
        ),
      ));
    }

    return Wrap(
      spacing: 8 * pixelRatio,
      runSpacing: 2 * pixelRatio,
      children: headerItems,
    );
  }

  /// 构建商品明细表格
  Widget _buildItemTable(double pixelRatio, double contentWidth) {
    final tableConfig = widget.template.itemTableConfig;
    final items = widget.sampleData['items'] as List<Map<String, dynamic>>? ?? [];

    // 计算合适的字体大小
    double calculateHeaderFontSize() {
      final calculatedSize = tableConfig.headerFontSize * pixelRatio * widget.scale;
      return calculatedSize.clamp(6.0, 14.0);
    }

    double calculateContentFontSize() {
      final calculatedSize = tableConfig.contentFontSize * pixelRatio * widget.scale;
      return calculatedSize.clamp(5.0, 12.0);
    }

    // 获取配置的列宽，与PrintTemplatePreview保持一致
    final actualColumnWidths = tableConfig.getActualColumnWidths();

    // 计算总宽度用于比例分配
    double totalWidth = 0;
    for (final column in tableConfig.visibleColumns) {
      totalWidth += actualColumnWidths[column] ?? 50.0;
    }

    return Container(
      width: contentWidth,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[400]!),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 表头
          Container(
            height: tableConfig.rowHeight * pixelRatio * widget.scale,
            color: Colors.grey[200],
            padding: EdgeInsets.symmetric(
              horizontal: 2 * pixelRatio,
              vertical: 0, // 移除垂直padding，确保行高一致
            ),
            child: Row(
              children: tableConfig.visibleColumns.map((column) {
                // 使用配置的列宽比例，而不是平均分配
                final configWidth = actualColumnWidths[column] ?? 50.0;
                final flex = (configWidth / totalWidth * 1000).round(); // 转换为整数flex值

                return Expanded(
                  flex: flex,
                  child: Center(
                    child: Text(
                      column,
                      style: TextStyle(
                        fontSize: calculateHeaderFontSize(),
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                );
              }).toList(),
            ),
          ),

          // 数据行 - 限制显示行数避免溢出
          ...items.take(2).map((item) {
            return Container(
              height: tableConfig.rowHeight * pixelRatio * widget.scale,
              padding: EdgeInsets.symmetric(
                horizontal: 2 * pixelRatio,
                vertical: 0, // 移除垂直padding，确保行高一致
              ),
              decoration: BoxDecoration(
                border: Border(
                  top: BorderSide(color: Colors.grey[300]!),
                ),
              ),
              child: Row(
                children: tableConfig.visibleColumns.map((column) {
                  String value = '';
                  switch (column) {
                    case '序号':
                      value = '${items.indexOf(item) + 1}';
                      break;
                    case '条码':
                      value = item['barcode']?.toString() ?? '';
                      break;
                    case '商品名称':
                      value = item['product_name']?.toString() ?? item['name']?.toString() ?? '';
                      break;
                    case '规格':
                      value = item['specification']?.toString() ?? item['ring_size']?.toString() ?? '';
                      break;
                    case '数量':
                      final quantity = double.tryParse(item['quantity']?.toString() ?? '0') ?? 0.0;
                      value = quantity.toStringAsFixed(0);
                      break;
                    case '总重(g)':
                      final weight = double.tryParse(item['total_weight']?.toString() ?? '0') ?? 0.0;
                      value = weight.toStringAsFixed(2);
                      break;
                    case '金重(g)':
                      final goldWeight = double.tryParse(item['gold_weight']?.toString() ?? '0') ?? 0.0;
                      value = goldWeight.toStringAsFixed(2);
                      break;
                    case '银重(g)':
                      final silverWeight = double.tryParse(item['silver_weight']?.toString() ?? '0') ?? 0.0;
                      value = silverWeight.toStringAsFixed(2);
                      break;
                    case '单价(¥)':
                      final price = double.tryParse(item['price']?.toString() ?? item['gold_price']?.toString() ?? '0') ?? 0.0;
                      value = price.toStringAsFixed(2);
                      break;
                    case '工费(¥)':
                      final laborCost = double.tryParse(item['labor_cost']?.toString() ?? item['work_price']?.toString() ?? '0') ?? 0.0;
                      value = laborCost.toStringAsFixed(2);
                      break;
                    case '金额(¥)':
                      final amount = double.tryParse(item['amount']?.toString() ?? item['total_amount']?.toString() ?? '0') ?? 0.0;
                      value = amount.toStringAsFixed(2);
                      break;
                    // 兼容旧字段名
                    case '重量':
                      final weight = double.tryParse(item['total_weight']?.toString() ?? '0') ?? 0.0;
                      value = weight.toStringAsFixed(2);
                      break;
                    case '单价':
                      final price = double.tryParse(item['price']?.toString() ?? item['gold_price']?.toString() ?? '0') ?? 0.0;
                      value = price.toStringAsFixed(2);
                      break;
                    case '金额':
                      final amount = double.tryParse(item['amount']?.toString() ?? item['total_amount']?.toString() ?? '0') ?? 0.0;
                      value = amount.toStringAsFixed(2);
                      break;
                    default:
                      value = '-';
                  }

                  // 使用与表头相同的列宽比例
                  final configWidth = actualColumnWidths[column] ?? 50.0;
                  final flex = (configWidth / totalWidth * 1000).round(); // 转换为整数flex值

                  return Expanded(
                    flex: flex,
                    child: Center(
                      child: Text(
                        value,
                        style: TextStyle(
                          fontSize: calculateContentFontSize(),
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  );
                }).toList(),
              ),
            );
          }),
        ],
      ),
    );
  }

  /// 构建汇总信息
  Widget _buildSummaryInfo(double pixelRatio) {
    final summaryConfig = widget.template.summaryConfig;

    // 计算合适的字体大小
    double calculateFontSize() {
      final calculatedSize = summaryConfig.fontSize * pixelRatio * widget.scale;
      return calculatedSize.clamp(6.0, 16.0);
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Text(
          '合计金额: ¥1300.00',
          style: TextStyle(
            fontSize: calculateFontSize(),
            fontWeight: summaryConfig.isBold ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ],
    );
  }

  /// 构建收款信息
  Widget _buildPaymentInfo(double pixelRatio) {
    final paymentConfig = widget.template.paymentInfoConfig;

    // 计算合适的字体大小
    double calculateFontSize() {
      final calculatedSize = paymentConfig.fontSize * pixelRatio * widget.scale;
      return calculatedSize.clamp(6.0, 14.0);
    }

    return Text(
      '现金: ¥800.00  微信: ¥500.00',
      style: TextStyle(
        fontSize: calculateFontSize(),
      ),
    );
  }

  /// 构建页脚信息
  Widget _buildFooterInfo(double pixelRatio) {
    final footerConfig = widget.template.footerConfig;

    // 计算合适的字体大小
    double calculateFontSize([double factor = 1.0]) {
      final calculatedSize = footerConfig.fontSize * factor * pixelRatio * widget.scale;
      return calculatedSize.clamp(5.0, 12.0);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        // 自定义文本
        if (footerConfig.customText?.isNotEmpty == true)
          Text(
            footerConfig.customText!,
            style: TextStyle(
              fontSize: calculateFontSize(),
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),

        // 打印时间
        if (footerConfig.showPrintTime)
          Padding(
            padding: EdgeInsets.only(top: 2 * pixelRatio),
            child: Text(
              '打印时间: ${DateTime.now().toString().substring(0, 19)}',
              style: TextStyle(
                fontSize: calculateFontSize(0.8),
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
      ],
    );
  }
}
