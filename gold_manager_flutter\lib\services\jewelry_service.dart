import 'package:get/get.dart';
import '../core/utils/logger.dart';
import '../core/services/api_client.dart';
import '../core/config/app_config.dart';
import '../models/jewelry/jewelry.dart';
import '../models/jewelry/jewelry_category.dart';
import '../models/common/page_data.dart';


/// 首饰服务
/// 处理首饰相关的业务逻辑
class JewelryService extends GetxService {
  late final ApiClient _apiClient;

  @override
  void onInit() {
    super.onInit();
    _apiClient = Get.find<ApiClient>();
  }

  /// 初始化服务
  Future<JewelryService> init() async {
    LoggerService.d('JewelryService 初始化');
    return this;
  }

  /// 获取首饰列表
  Future<PageData<Jewelry>> getJewelryList(Map<String, dynamic> params) async {
    try {
      // 尝试调用真实API
      try {
        final response = await _apiClient.get(
          '${AppConfig.apiEndpoint['jewelry']}',
          queryParameters: params,
        );

        final responseData = response.data;

        // 🔧 修复：后端返回的格式是 JewelryListResponse
        // 直接从根级别获取 items、total、page 等字段
        final List<dynamic> items = responseData['items'] ?? [];
        final int total = responseData['total'] ?? 0;
        final int currentPage = responseData['page'] ?? 1;
        final int totalPages = responseData['total_pages'] ?? 1;

        LoggerService.d('📊 API响应解析: items=${items.length}, total=$total, page=$currentPage, totalPages=$totalPages');

        final jewelryList = items.map((item) => _mapJewelryFromApi(item)).toList();

        return PageData<Jewelry>(
          data: jewelryList,
          currentPage: currentPage,
          lastPage: totalPages,
          total: total,
        );
      } catch (e) {
        LoggerService.e('API调用失败: $e');
        rethrow;
      }
    } catch (e) {
      LoggerService.e('获取首饰列表失败', e);
      rethrow;
    }
  }

  /// 获取首饰详情
  Future<Jewelry> getJewelryById(int id) async {
    try {
      final response = await _apiClient.get('${AppConfig.apiEndpoint['jewelry']}/$id');

      if (response.statusCode == 200) {
        final data = response.data;
        return _mapJewelryFromApi(data);
      } else {
        throw Exception('获取首饰详情失败: ${response.statusMessage}');
      }
    } catch (e) {
      LoggerService.e('获取首饰详情失败', e);
      rethrow;
    }
  }

  /// 获取首饰分类列表
  Future<List<JewelryCategory>> getCategories() async {
    try {
      final response = await _apiClient.get('${AppConfig.apiEndpoint['jewelry']}/categories');

      if (response.statusCode == 200) {
        final data = response.data;
        // 根据后端API文档，categories接口直接返回数组，不是包装在items中
        final List<dynamic> items = data is List ? data : (data['data'] ?? data['items'] ?? []);
        return items.map((item) => _mapJewelryCategoryFromApi(item)).toList();
      } else {
        throw Exception('获取首饰分类失败: ${response.statusMessage}');
      }
    } catch (e) {
      LoggerService.e('获取首饰分类失败', e);
      rethrow;
    }
  }

  /// 根据条码查询首饰
  Future<Jewelry?> getJewelryByBarcode(String barcode, {int? storeId}) async {
    try {
      final queryParameters = <String, dynamic>{};
      if (storeId != null && storeId > 0) {
        queryParameters['store_id'] = storeId;
      }

      final response = await _apiClient.get(
        '${AppConfig.apiEndpoint['jewelry']}/barcode/$barcode',
        queryParameters: queryParameters.isNotEmpty ? queryParameters : null,
      );

      if (response.statusCode == 200) {
        final data = response.data;
        return _mapJewelryFromApi(data);
      } else if (response.statusCode == 404) {
        return null; // 未找到对应条码的首饰
      } else {
        throw Exception('根据条码查询首饰失败: ${response.statusMessage}');
      }
    } catch (e) {
      LoggerService.e('根据条码查询首饰失败', e);
      rethrow;
    }
  }

  /// 搜索首饰
  Future<List<Jewelry>> searchJewelry(String keyword) async {
    try {
      final response = await _apiClient.get(
        '${AppConfig.apiEndpoint['jewelry']}/search',
        queryParameters: {'keyword': keyword},
      );

      if (response.statusCode == 200) {
        final data = response.data;
        final List<dynamic> items = data['items'] ?? [];
        return items.map((item) => _mapJewelryFromApi(item)).toList();
      } else {
        throw Exception('搜索首饰失败: ${response.statusMessage}');
      }
    } catch (e) {
      LoggerService.e('搜索首饰失败', e);
      rethrow;
    }
  }

  /// 更新首饰信息
  Future<Jewelry> updateJewelry(int id, Map<String, dynamic> data) async {
    try {
      LoggerService.d('🔄 开始更新首饰信息，ID: $id');
      LoggerService.d('📦 请求数据: $data');
      LoggerService.d('🌐 请求URL: ${AppConfig.apiEndpoint['jewelry']}/$id');

      final response = await _apiClient.put(
        '${AppConfig.apiEndpoint['jewelry']}/$id',
        data: data,
      );

      LoggerService.d('📡 API响应状态码: ${response.statusCode}');
      LoggerService.d('📋 API响应数据: ${response.data}');

      if (response.statusCode == 200) {
        final responseData = response.data;
        LoggerService.d('✅ 首饰信息更新成功');
        final updatedJewelry = _mapJewelryFromApi(responseData);
        LoggerService.d('🔍 解析后的商品信息: ${updatedJewelry.toString()}');
        return updatedJewelry;
      } else {
        LoggerService.e('❌ API响应状态码异常: ${response.statusCode}');
        throw Exception('更新首饰信息失败: ${response.statusMessage}');
      }
    } catch (e) {
      LoggerService.e('❌ 更新首饰信息失败', e);
      if (e.toString().contains('DioException')) {
        LoggerService.e('🌐 网络请求异常，请检查网络连接和后端服务');
      }
      rethrow;
    }
  }

  /// 删除首饰
  Future<void> deleteJewelry(int id) async {
    try {
      LoggerService.d('🗑️ 开始删除首饰，ID: $id');

      final response = await _apiClient.delete(
        '${AppConfig.apiEndpoint['jewelry']}/$id',
      );

      if (response.statusCode == 200 || response.statusCode == 204) {
        LoggerService.d('✅ 首饰删除成功');
      } else {
        throw Exception('删除首饰失败: ${response.statusMessage}');
      }
    } catch (e) {
      LoggerService.e('❌ 删除首饰失败', e);
      rethrow;
    }
  }

  /// 获取首饰类别名称
  String getCategoryName(int categoryId) {
    switch (categoryId) {
      case 1:
        return '戒指';
      case 2:
        return '项链';
      case 3:
        return '手镯';
      case 4:
        return '耳环';
      case 5:
        return '吊坠';
      default:
        return '其他';
    }
  }



  /// 从API数据映射首饰对象
  Jewelry _mapJewelryFromApi(Map<String, dynamic> json) {
    // 🔧 修复：使用Jewelry.fromJson方法，确保category_name和store_name字段被正确处理
    LoggerService.d('🔍 API响应数据: ${json.toString()}');

    // 添加调试日志，检查category相关字段
    if (json.containsKey('category_name')) {
      LoggerService.d('✅ API返回category_name: ${json['category_name']}');
    } else {
      LoggerService.w('⚠️ API响应中没有category_name字段');
    }

    if (json.containsKey('category_id')) {
      LoggerService.d('✅ API返回category_id: ${json['category_id']}');
    }

    // 添加调试日志，检查store相关字段
    if (json.containsKey('store_name')) {
      LoggerService.d('✅ API返回store_name: ${json['store_name']}');
    } else {
      LoggerService.w('⚠️ API响应中没有store_name字段');
    }

    if (json.containsKey('store_id')) {
      LoggerService.d('✅ API返回store_id: ${json['store_id']}');
    }

    // 使用Jewelry.fromJson方法，它包含了我们修复的category和store处理逻辑
    final jewelry = Jewelry.fromJson(json);

    LoggerService.d('🔍 解析后的分类信息: ${jewelry.categoryName}');
    LoggerService.d('🔍 解析后的门店信息: ${jewelry.store?.name ?? '无门店信息'}');

    return jewelry;
  }

  /// 从API数据映射首饰分类对象
  JewelryCategory _mapJewelryCategoryFromApi(Map<String, dynamic> json) {
    return JewelryCategory(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      parentId: json['parent_id'] ?? 0,
    );
  }
}