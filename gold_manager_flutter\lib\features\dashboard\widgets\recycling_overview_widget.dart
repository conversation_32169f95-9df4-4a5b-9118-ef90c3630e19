import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../core/theme/app_theme.dart';
import 'metric_card.dart';
import 'chart_card.dart';
import 'chart_theme.dart';
import 'stat_item.dart';
import '../../../widgets/responsive_builder.dart';

/// 回收业务概览组件
/// 展示回收相关的核心指标和处理进度
class RecyclingOverviewWidget extends StatelessWidget {
  /// 回收数据
  final RecyclingOverviewData data;
  
  /// 是否正在加载
  final bool isLoading;
  
  /// 刷新回调
  final VoidCallback? onRefresh;

  const RecyclingOverviewWidget({
    super.key,
    required this.data,
    this.isLoading = false,
    this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, sizingInformation) {
        if (sizingInformation.isMobile) {
          return _buildMobileLayout();
        } else if (sizingInformation.isTablet) {
          return _buildTabletLayout();
        } else {
          return _buildDesktopLayout();
        }
      },
    );
  }

  /// 桌面端布局
  Widget _buildDesktopLayout() {
    return Column(
      children: [
        // 核心指标卡片 - 4列布局
        Row(
          children: [
            Expanded(child: _buildTotalRecyclingCard()),
            const SizedBox(width: 16),
            Expanded(child: _buildProcessingCountCard()),
            const SizedBox(width: 16),
            Expanded(child: _buildRecyclingValueCard()),
            const SizedBox(width: 16),
            Expanded(child: _buildEfficiencyCard()),
          ],
        ),
        const SizedBox(height: 24),
        
        // 图表和统计区域
        Row(
          children: [
            Expanded(flex: 2, child: _buildProcessingProgressChart()),
            const SizedBox(width: 16),
            Expanded(child: _buildRecyclingStatsCard()),
          ],
        ),
      ],
    );
  }

  /// 平板端布局
  Widget _buildTabletLayout() {
    return Column(
      children: [
        // 核心指标卡片 - 2行2列布局
        Row(
          children: [
            Expanded(child: _buildTotalRecyclingCard()),
            const SizedBox(width: 16),
            Expanded(child: _buildProcessingCountCard()),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(child: _buildRecyclingValueCard()),
            const SizedBox(width: 16),
            Expanded(child: _buildEfficiencyCard()),
          ],
        ),
        const SizedBox(height: 24),
        
        // 图表区域
        _buildProcessingProgressChart(),
        const SizedBox(height: 16),
        _buildRecyclingStatsCard(),
      ],
    );
  }

  /// 移动端布局
  Widget _buildMobileLayout() {
    return Column(
      children: [
        // 核心指标卡片 - 垂直布局
        _buildTotalRecyclingCard(),
        const SizedBox(height: 12),
        _buildProcessingCountCard(),
        const SizedBox(height: 12),
        _buildRecyclingValueCard(),
        const SizedBox(height: 12),
        _buildEfficiencyCard(),
        const SizedBox(height: 20),
        
        // 图表区域
        _buildProcessingProgressChart(),
        const SizedBox(height: 16),
        _buildRecyclingStatsCard(),
      ],
    );
  }

  /// 回收总量卡片
  Widget _buildTotalRecyclingCard() {
    return DashboardMetricCard(
      title: '回收总量',
      value: data.totalRecyclingWeight.toStringAsFixed(1),
      unit: 'kg',
      icon: Icons.recycling,
      iconColor: AppTheme.successColor,
      valueColor: AppTheme.successColor,
      trendValue: data.recyclingTrend,
      trendDescription: '较上月',
      isLoading: isLoading,
      onTap: () {
        // TODO: 跳转到回收详情
      },
    );
  }

  /// 处理中数量卡片
  Widget _buildProcessingCountCard() {
    return DashboardMetricCard(
      title: '处理中',
      value: data.processingCount.toString(),
      unit: '单',
      icon: Icons.pending,
      iconColor: AppTheme.warningColor,
      valueColor: AppTheme.warningColor,
      subtitle: '待处理回收单',
      isLoading: isLoading,
      onTap: () {
        // TODO: 跳转到处理中列表
      },
    );
  }

  /// 回收价值卡片
  Widget _buildRecyclingValueCard() {
    return DashboardMetricCard(
      title: '回收价值',
      value: data.totalRecyclingValue.toStringAsFixed(0),
      unit: '元',
      icon: Icons.attach_money,
      iconColor: AppTheme.primaryColor,
      valueColor: AppTheme.primaryColor,
      trendValue: data.valueTrend,
      trendDescription: '较上月',
      isLoading: isLoading,
      onTap: () {
        // TODO: 跳转到价值分析
      },
    );
  }

  /// 处理效率卡片
  Widget _buildEfficiencyCard() {
    return DashboardMetricCard(
      title: '处理效率',
      value: data.processingEfficiency.toStringAsFixed(1),
      unit: '%',
      icon: Icons.speed,
      iconColor: AppTheme.infoColor,
      valueColor: AppTheme.infoColor,
      trendValue: data.efficiencyTrend,
      trendDescription: '较上月',
      isLoading: isLoading,
      onTap: () {
        // TODO: 跳转到效率分析
      },
    );
  }

  /// 处理进度图表
  Widget _buildProcessingProgressChart() {
    return DashboardChartCard(
      title: '处理进度分布',
      subtitle: '按处理状态统计',
      icon: Icons.donut_small,
      height: 300,
      isLoading: isLoading,
      showMoreButton: true,
      onMorePressed: () {
        // TODO: 跳转到处理进度详情
      },
      chart: isLoading ? const SizedBox.shrink() : _buildProgressPieChart(),
    );
  }

  /// 回收统计卡片
  Widget _buildRecyclingStatsCard() {
    return Container(
      height: 300,
      padding: const EdgeInsets.all(16),
      decoration: AppTheme.lightTheme.cardTheme.elevation != null
          ? BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            )
          : null,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          const Row(
            children: [
              Icon(Icons.analytics, size: 20, color: AppTheme.primaryColor),
              SizedBox(width: 8),
              Text(
                '回收统计',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // 统计项列表
          Expanded(
            child: isLoading 
                ? const Center(
                    child: CircularProgressIndicator(
                      color: AppTheme.primaryColor,
                    ),
                  )
                : _buildStatsList(),
          ),
        ],
      ),
    );
  }

  /// 构建进度饼图
  Widget _buildProgressPieChart() {
    final sections = [
      DashboardChartTheme.createPieSection(
        value: data.pendingProgress,
        title: '待处理',
        color: AppTheme.warningColor,
        radius: 60,
      ),
      DashboardChartTheme.createPieSection(
        value: data.processingProgress,
        title: '处理中',
        color: AppTheme.infoColor,
        radius: 60,
      ),
      DashboardChartTheme.createPieSection(
        value: data.completedProgress,
        title: '已完成',
        color: AppTheme.successColor,
        radius: 60,
      ),
    ];

    return PieChart(
      DashboardChartTheme.getDefaultPieChartData(
        sections: sections,
        centerSpaceRadius: 40,
      ),
    );
  }

  /// 构建统计项列表
  Widget _buildStatsList() {
    final stats = [
      StatItemData(
        label: '金属分离',
        value: '${data.metalSeparationCount}单',
        icon: Icons.science,
        color: Colors.amber[700]!,
      ),
      StatItemData(
        label: '翻新加工',
        value: '${data.renovationCount}单',
        icon: Icons.build,
        color: AppTheme.infoColor,
      ),
      StatItemData(
        label: '直接熔炼',
        value: '${data.meltingCount}单',
        icon: Icons.local_fire_department,
        color: AppTheme.errorColor,
      ),
      StatItemData(
        label: '金重回收',
        value: '${data.goldRecoveryWeight.toStringAsFixed(2)}g',
        icon: Icons.monetization_on,
        color: Colors.amber[700]!,
      ),
      StatItemData(
        label: '银重回收',
        value: '${data.silverRecoveryWeight.toStringAsFixed(2)}g',
        icon: Icons.scale,
        color: Colors.grey[600]!,
      ),
      StatItemData(
        label: '回收利润',
        value: '¥${data.recyclingProfit.toStringAsFixed(0)}',
        icon: Icons.trending_up,
        color: AppTheme.successColor,
      ),
    ];

    return ListView.separated(
      itemCount: stats.length,
      separatorBuilder: (context, index) => const SizedBox(height: 12),
      itemBuilder: (context, index) {
        final stat = stats[index];
        return DashboardStatItem(
          label: stat.label,
          value: stat.value,
          icon: stat.icon,
          color: stat.color,
          style: StatItemStyle.compact,
          showBorder: false,
        );
      },
    );
  }
}

/// 回收业务概览数据模型
class RecyclingOverviewData {
  /// 回收总重量(kg)
  final double totalRecyclingWeight;
  
  /// 回收量趋势(百分比)
  final double recyclingTrend;
  
  /// 处理中数量
  final int processingCount;
  
  /// 回收总价值
  final double totalRecyclingValue;
  
  /// 价值趋势(百分比)
  final double valueTrend;
  
  /// 处理效率(百分比)
  final double processingEfficiency;
  
  /// 效率趋势(百分比)
  final double efficiencyTrend;
  
  /// 待处理进度(百分比)
  final double pendingProgress;
  
  /// 处理中进度(百分比)
  final double processingProgress;
  
  /// 已完成进度(百分比)
  final double completedProgress;
  
  /// 金属分离数量
  final int metalSeparationCount;
  
  /// 翻新加工数量
  final int renovationCount;
  
  /// 直接熔炼数量
  final int meltingCount;
  
  /// 金重回收量
  final double goldRecoveryWeight;
  
  /// 银重回收量
  final double silverRecoveryWeight;
  
  /// 回收利润
  final double recyclingProfit;

  const RecyclingOverviewData({
    required this.totalRecyclingWeight,
    required this.recyclingTrend,
    required this.processingCount,
    required this.totalRecyclingValue,
    required this.valueTrend,
    required this.processingEfficiency,
    required this.efficiencyTrend,
    required this.pendingProgress,
    required this.processingProgress,
    required this.completedProgress,
    required this.metalSeparationCount,
    required this.renovationCount,
    required this.meltingCount,
    required this.goldRecoveryWeight,
    required this.silverRecoveryWeight,
    required this.recyclingProfit,
  });

  /// 空数据
  static const RecyclingOverviewData empty = RecyclingOverviewData(
    totalRecyclingWeight: 0,
    recyclingTrend: 0,
    processingCount: 0,
    totalRecyclingValue: 0,
    valueTrend: 0,
    processingEfficiency: 0,
    efficiencyTrend: 0,
    pendingProgress: 0,
    processingProgress: 0,
    completedProgress: 0,
    metalSeparationCount: 0,
    renovationCount: 0,
    meltingCount: 0,
    goldRecoveryWeight: 0,
    silverRecoveryWeight: 0,
    recyclingProfit: 0,
  );

  /// 示例数据
  static const RecyclingOverviewData sample = RecyclingOverviewData(
    totalRecyclingWeight: 125.8,
    recyclingTrend: 15.2,
    processingCount: 8,
    totalRecyclingValue: 185600,
    valueTrend: 12.8,
    processingEfficiency: 92.5,
    efficiencyTrend: 3.2,
    pendingProgress: 25.0,
    processingProgress: 35.0,
    completedProgress: 40.0,
    metalSeparationCount: 12,
    renovationCount: 8,
    meltingCount: 5,
    goldRecoveryWeight: 85.6,
    silverRecoveryWeight: 40.2,
    recyclingProfit: 45600,
  );
}
