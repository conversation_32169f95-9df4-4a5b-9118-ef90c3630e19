import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import 'standard_card.dart';
import 'standard_input.dart';
import 'standard_buttons.dart';

/// 搜索筛选栏组件
/// 遵循UI设计规范：白色背景、底部轻微阴影、响应式布局
class SearchFilterBar extends StatelessWidget {
  /// 搜索控制器
  final TextEditingController? searchController;
  
  /// 搜索提示文本
  final String? searchHint;
  
  /// 搜索回调
  final ValueChanged<String>? onSearch;
  
  /// 搜索变化回调
  final ValueChanged<String>? onSearchChanged;
  
  /// 筛选按钮点击回调
  final VoidCallback? onFilter;
  
  /// 排序按钮点击回调
  final VoidCallback? onSort;
  
  /// 刷新按钮点击回调
  final VoidCallback? onRefresh;
  
  /// 添加按钮点击回调
  final VoidCallback? onAdd;
  
  /// 添加按钮文本
  final String? addButtonText;
  
  /// 是否显示筛选按钮
  final bool showFilter;
  
  /// 是否显示排序按钮
  final bool showSort;
  
  /// 是否显示刷新按钮
  final bool showRefresh;
  
  /// 是否显示添加按钮
  final bool showAdd;
  
  /// 筛选数量（显示在筛选按钮上的红点）
  final int filterCount;
  
  /// 自定义操作按钮
  final List<Widget>? customActions;

  const SearchFilterBar({
    super.key,
    this.searchController,
    this.searchHint,
    this.onSearch,
    this.onSearchChanged,
    this.onFilter,
    this.onSort,
    this.onRefresh,
    this.onAdd,
    this.addButtonText,
    this.showFilter = true,
    this.showSort = true,
    this.showRefresh = true,
    this.showAdd = false,
    this.filterCount = 0,
    this.customActions,
  });

  @override
  Widget build(BuildContext context) {
    return StandardCard(
      margin: EdgeInsets.zero,
      borderRadius: 0,
      enableShadow: true,
      child: LayoutBuilder(
        builder: (context, constraints) {
          final isWideScreen = constraints.maxWidth > 600;
          
          if (isWideScreen) {
            return _buildWideLayout();
          } else {
            return _buildNarrowLayout();
          }
        },
      ),
    );
  }

  /// 构建宽屏布局（水平排列）
  Widget _buildWideLayout() {
    return Padding(
      padding: const EdgeInsets.all(AppTheme.padding),
      child: Row(
        children: [
          // 搜索框
          Expanded(
            flex: 3,
            child: SearchTextField(
              controller: searchController,
              hintText: searchHint ?? '搜索...',
              onSearch: onSearch,
              onChanged: onSearchChanged,
            ),
          ),
          
          const SizedBox(width: AppTheme.padding),
          
          // 操作按钮组
          Row(
            mainAxisSize: MainAxisSize.min,
            children: _buildActionButtons(),
          ),
        ],
      ),
    );
  }

  /// 构建窄屏布局（垂直排列）
  Widget _buildNarrowLayout() {
    return Padding(
      padding: const EdgeInsets.all(AppTheme.padding),
      child: Column(
        children: [
          // 搜索框
          SearchTextField(
            controller: searchController,
            hintText: searchHint ?? '搜索...',
            onSearch: onSearch,
            onChanged: onSearchChanged,
          ),
          
          const SizedBox(height: AppTheme.paddingMedium),
          
          // 操作按钮组
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: _buildActionButtons(),
          ),
        ],
      ),
    );
  }

  /// 构建操作按钮列表
  List<Widget> _buildActionButtons() {
    List<Widget> buttons = [];

    // 筛选按钮
    if (showFilter) {
      buttons.add(
        _buildActionButton(
          icon: Icons.filter_list,
          label: '筛选',
          onPressed: onFilter,
          badge: filterCount > 0 ? filterCount.toString() : null,
        ),
      );
    }

    // 排序按钮
    if (showSort) {
      buttons.add(
        _buildActionButton(
          icon: Icons.sort,
          label: '排序',
          onPressed: onSort,
        ),
      );
    }

    // 刷新按钮
    if (showRefresh) {
      buttons.add(
        _buildActionButton(
          icon: Icons.refresh,
          label: '刷新',
          onPressed: onRefresh,
        ),
      );
    }

    // 添加按钮
    if (showAdd) {
      buttons.add(
        PrimaryButton(
          text: addButtonText ?? '添加',
          icon: Icons.add,
          onPressed: onAdd,
          size: ButtonSize.small,
        ),
      );
    }

    // 自定义操作按钮
    if (customActions != null) {
      buttons.addAll(customActions!);
    }

    // 在按钮之间添加间距
    List<Widget> spacedButtons = [];
    for (int i = 0; i < buttons.length; i++) {
      spacedButtons.add(buttons[i]);
      if (i < buttons.length - 1) {
        spacedButtons.add(const SizedBox(width: AppTheme.paddingSmall));
      }
    }

    return spacedButtons;
  }

  /// 构建操作按钮
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    VoidCallback? onPressed,
    String? badge,
  }) {
    Widget button = Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          icon: Icon(icon),
          onPressed: onPressed,
          color: AppTheme.primaryColor,
          iconSize: 24,
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: AppTheme.smallSize,
            color: AppTheme.secondaryTextColor,
          ),
        ),
      ],
    );

    // 如果有徽章，添加徽章
    if (badge != null) {
      button = Stack(
        children: [
          button,
          Positioned(
            right: 0,
            top: 0,
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: const BoxDecoration(
                color: AppTheme.errorColor,
                shape: BoxShape.circle,
              ),
              constraints: const BoxConstraints(
                minWidth: 16,
                minHeight: 16,
              ),
              child: Text(
                badge,
                style: const TextStyle(
                  color: AppTheme.reverseTextColor,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ],
      );
    }

    return button;
  }
}

/// 简化版搜索栏
/// 只包含搜索框和刷新按钮
class SimpleSearchBar extends StatelessWidget {
  /// 搜索控制器
  final TextEditingController? searchController;
  
  /// 搜索提示文本
  final String? searchHint;
  
  /// 搜索回调
  final ValueChanged<String>? onSearch;
  
  /// 搜索变化回调
  final ValueChanged<String>? onSearchChanged;
  
  /// 刷新按钮点击回调
  final VoidCallback? onRefresh;

  const SimpleSearchBar({
    super.key,
    this.searchController,
    this.searchHint,
    this.onSearch,
    this.onSearchChanged,
    this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    return StandardCard(
      margin: const EdgeInsets.all(AppTheme.padding),
      child: Row(
        children: [
          // 搜索框
          Expanded(
            child: SearchTextField(
              controller: searchController,
              hintText: searchHint ?? '搜索...',
              onSearch: onSearch,
              onChanged: onSearchChanged,
            ),
          ),
          
          // 刷新按钮
          if (onRefresh != null) ...[
            const SizedBox(width: AppTheme.paddingMedium),
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: onRefresh,
              color: AppTheme.primaryColor,
              tooltip: '刷新',
            ),
          ],
        ],
      ),
    );
  }
}
