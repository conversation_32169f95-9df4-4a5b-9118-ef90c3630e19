import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../controllers/settings_controller.dart';
import '../../models/setting_model.dart';

/// 角色权限Tab页面
class RolePermissionTab extends StatelessWidget {
  const RolePermissionTab({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<SettingsController>();
    
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildRoleHeader(context, controller),
          const SizedBox(height: 16),
          Expanded(
            child: _buildRolePermissionContent(context, controller),
          ),
        ],
      ),
    );
  }
  
  /// 构建角色头部
  Widget _buildRoleHeader(BuildContext context, SettingsController controller) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        const Text(
          '角色与权限管理',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        ElevatedButton.icon(
          onPressed: () {
            _showAddRoleDialog(context, controller);
          },
          icon: const Icon(Icons.add),
          label: const Text('添加角色'),
        ),
      ],
    );
  }
  
  /// 构建角色权限内容
  Widget _buildRolePermissionContent(BuildContext context, SettingsController controller) {
    return Obx(() {
      final roles = controller.roleList;
      
      if (roles.isEmpty) {
        return const Center(
          child: Text('没有发现角色数据，请点击刷新按钮重试', style: TextStyle(fontSize: 16)),
        );
      }
      
      return Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 左侧角色列表
          SizedBox(
            width: 240,
            child: Card(
              child: ListView.separated(
                itemCount: roles.length,
                separatorBuilder: (context, index) => const Divider(height: 1),
                itemBuilder: (context, index) {
                  final role = roles[index];
                  
                  return ListTile(
                    title: Text(role.name),
                    subtitle: Text(role.description),
                    trailing: role.isSystemRole
                        ? const Icon(Icons.lock, size: 18, color: Colors.grey)
                        : IconButton(
                            icon: const Icon(Icons.delete, color: Colors.red, size: 20),
                            onPressed: () {
                              _showDeleteRoleDialog(context, controller, role);
                            },
                          ),
                    onTap: () {
                      // 显示角色权限详情
                      _showRolePermissionDetails(context, controller, role);
                    },
                  );
                },
              ),
            ),
          ),
          
          const SizedBox(width: 16),
          
          // 右侧权限展示区域
          Expanded(
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '权限列表',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Expanded(
                      child: _buildPermissionList(controller),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      );
    });
  }
  
  /// 构建权限列表
  Widget _buildPermissionList(SettingsController controller) {
    final permissions = controller.permissionList;
    
    if (permissions.isEmpty) {
      return const Center(
        child: Text('没有发现权限数据'),
      );
    }
    
    // 按分组整理权限
    final Map<String, List<Permission>> permissionGroups = {};
    
    for (final permission in permissions) {
      if (!permissionGroups.containsKey(permission.group)) {
        permissionGroups[permission.group] = [];
      }
      
      permissionGroups[permission.group]!.add(permission);
    }
    
    return ListView.builder(
      itemCount: permissionGroups.length,
      itemBuilder: (context, index) {
        final groupName = permissionGroups.keys.elementAt(index);
        final permissionsInGroup = permissionGroups[groupName]!;
        
        return ExpansionTile(
          title: Text(groupName),
          initiallyExpanded: index == 0,
          children: [
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                childAspectRatio: 3,
                crossAxisSpacing: 8,
                mainAxisSpacing: 8,
              ),
              itemCount: permissionsInGroup.length,
              itemBuilder: (context, i) {
                final permission = permissionsInGroup[i];
                
                return Card(
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          permission.name,
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        Text(
                          permission.code,
                          style: const TextStyle(fontSize: 12, color: Colors.grey),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ],
        );
      },
    );
  }
  
  /// 显示添加角色对话框
  void _showAddRoleDialog(BuildContext context, SettingsController controller) {
    final formKey = GlobalKey<FormState>();
    String name = '';
    String description = '';
    final selectedPermissions = <int>{};
    
    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('添加角色'),
              content: SizedBox(
                width: 500,
                child: Form(
                  key: formKey,
                  child: SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        TextFormField(
                          decoration: const InputDecoration(
                            labelText: '角色名称',
                            border: OutlineInputBorder(),
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return '请输入角色名称';
                            }
                            return null;
                          },
                          onSaved: (value) {
                            name = value ?? '';
                          },
                        ),
                        const SizedBox(height: 16),
                        TextFormField(
                          decoration: const InputDecoration(
                            labelText: '角色描述',
                            border: OutlineInputBorder(),
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return '请输入角色描述';
                            }
                            return null;
                          },
                          onSaved: (value) {
                            description = value ?? '';
                          },
                        ),
                        const SizedBox(height: 24),
                        const Text(
                          '选择权限',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          height: 300,
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade300),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: ListView.builder(
                            itemCount: controller.permissionList.length,
                            itemBuilder: (context, index) {
                              final permission = controller.permissionList[index];
                              final isSelected = selectedPermissions.contains(permission.id);
                              
                              return CheckboxListTile(
                                title: Text(permission.name),
                                subtitle: Text(
                                  '${permission.group} - ${permission.code}',
                                  style: const TextStyle(fontSize: 12),
                                ),
                                value: isSelected,
                                onChanged: (value) {
                                  setState(() {
                                    if (value == true) {
                                      selectedPermissions.add(permission.id);
                                    } else {
                                      selectedPermissions.remove(permission.id);
                                    }
                                  });
                                },
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('取消'),
                ),
                ElevatedButton(
                  onPressed: () async {
                    if (formKey.currentState?.validate() ?? false) {
                      formKey.currentState?.save();
                      
                      Navigator.pop(context);
                      
                      final success = await controller.createRole(
                        name,
                        description,
                        selectedPermissions.toList(),
                      );
                      
                      if (success) {
                        _showMessage(context, '角色创建成功');
                      } else {
                        _showMessage(context, '角色创建失败，请稍后重试');
                      }
                    }
                  },
                  child: const Text('添加'),
                ),
              ],
            );
          },
        );
      },
    );
  }
  
  /// 显示角色权限详情
  void _showRolePermissionDetails(BuildContext context, SettingsController controller, Role role) {
    // 创建一个可修改的权限ID集合
    final selectedPermissions = <int>{};
    for (final permission in role.permissions) {
      selectedPermissions.add(permission.id);
    }
    
    // 是否可编辑
    final canEdit = !role.isSystemRole;
    
    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: Text('角色详情: ${role.name}'),
              content: SizedBox(
                width: 500,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 角色信息
                    Text('描述: ${role.description}'),
                    const SizedBox(height: 4),
                    Text(
                      '系统角色: ${role.isSystemRole ? "是" : "否"}',
                      style: TextStyle(
                        color: role.isSystemRole ? Colors.red : Colors.green,
                      ),
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      '权限列表',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Expanded(
                      child: Container(
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: ListView.builder(
                          itemCount: controller.permissionList.length,
                          itemBuilder: (context, index) {
                            final permission = controller.permissionList[index];
                            final isSelected = selectedPermissions.contains(permission.id);
                            
                            return CheckboxListTile(
                              title: Text(permission.name),
                              subtitle: Text(
                                '${permission.group} - ${permission.code}',
                                style: const TextStyle(fontSize: 12),
                              ),
                              value: isSelected,
                              onChanged: canEdit
                                  ? (value) {
                                      setState(() {
                                        if (value == true) {
                                          selectedPermissions.add(permission.id);
                                        } else {
                                          selectedPermissions.remove(permission.id);
                                        }
                                      });
                                    }
                                  : null,
                            );
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('关闭'),
                ),
                if (canEdit)
                  ElevatedButton(
                    onPressed: () async {
                      Navigator.pop(context);
                      
                      final success = await controller.updateRole(
                        role.id,
                        role.name,
                        role.description,
                        selectedPermissions.toList(),
                      );
                      
                      if (success) {
                        _showMessage(context, '角色权限更新成功');
                      } else {
                        _showMessage(context, '角色权限更新失败，请稍后重试');
                      }
                    },
                    child: const Text('保存更改'),
                  ),
              ],
            );
          },
        );
      },
    );
  }
  
  /// 显示删除角色对话框
  void _showDeleteRoleDialog(BuildContext context, SettingsController controller, Role role) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除角色'),
        content: Text('确定要删除角色"${role.name}"吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            onPressed: () async {
              Navigator.pop(context);
              
              final success = await controller.deleteRole(role.id);
              
              if (success) {
                _showMessage(context, '角色删除成功');
              } else {
                _showMessage(context, '角色删除失败，请稍后重试');
              }
            },
            child: const Text('确定删除'),
          ),
        ],
      ),
    );
  }
  
  /// 显示消息提示
  void _showMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
} 