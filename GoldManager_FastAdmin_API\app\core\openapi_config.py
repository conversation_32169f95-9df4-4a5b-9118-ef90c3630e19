"""
OpenAPI文档配置增强
提供更详细的API文档配置和自定义
"""

from fastapi.openapi.utils import get_openapi
from app.core.config import settings


def get_custom_openapi(app):
    """获取自定义的OpenAPI配置"""
    if app.openapi_schema:
        return app.openapi_schema

    openapi_schema = get_openapi(
        title=settings.PROJECT_NAME,
        version=settings.PROJECT_VERSION,
        description=get_api_description(),
        routes=app.routes,
        servers=get_servers(),
    )

    # 添加自定义配置
    openapi_schema["info"]["x-logo"] = {
        "url": "https://fastapi.tiangolo.com/img/logo-margin/logo-teal.png"
    }

    # 添加联系信息
    openapi_schema["info"]["contact"] = {
        "name": "API Support",
        "email": "<EMAIL>",
        "url": "https://goldmanager.com/support",
    }

    # 添加许可证信息
    openapi_schema["info"]["license"] = {
        "name": "MIT License",
        "url": "https://opensource.org/licenses/MIT",
    }

    # 添加标签描述
    openapi_schema["tags"] = get_api_tags()

    # 添加安全方案
    openapi_schema["components"]["securitySchemes"] = {
        "BearerAuth": {
            "type": "http",
            "scheme": "bearer",
            "bearerFormat": "JWT",
            "description": "使用JWT令牌进行身份验证。在登录后获取token，然后在Authorization头中使用Bearer <token>格式",
        }
    }

    # 添加全局安全要求
    openapi_schema["security"] = [{"BearerAuth": []}]

    # 添加通用响应组件
    if "components" not in openapi_schema:
        openapi_schema["components"] = {}

    openapi_schema["components"]["responses"] = get_common_responses()

    app.openapi_schema = openapi_schema
    return app.openapi_schema


def get_api_description():
    """获取API描述"""
    return """
## 🏆 黄金珠宝管理系统 API

### 📋 系统概述
黄金珠宝管理系统是一个专为珠宝行业设计的现代化管理平台，提供完整的库存管理、销售管理、会员管理等功能。

### 🔧 技术特性
- **高性能**: 基于FastAPI框架，支持异步处理
- **安全可靠**: 多层安全防护，JWT认证，权限控制
- **易于扩展**: 模块化设计，支持插件式扩展
- **完整监控**: 集成Prometheus监控，实时性能指标
- **自动文档**: 自动生成交互式API文档

### 📖 功能模块

#### 🏪 商品管理
- **商品信息**: 完整的商品档案管理，支持条码识别
- **分类管理**: 层次化分类体系，支持多级分类
- **成本计算**: 自动化成本核算，包括金重、工费等
- **库存跟踪**: 实时库存状态，支持多门店管理

#### 📦 库存管理
- **入库管理**: 支持批量入库，自动生成条码
- **出库管理**: 销售出库，调拨出库等多种出库方式
- **退货管理**: 完整的退货流程和记录
- **盘点管理**: 定期盘点，差异分析和调整

#### 🏢 门店管理
- **多门店支持**: 支持连锁门店统一管理
- **门店权限**: 基于门店的数据隔离和权限控制
- **调拨管理**: 门店间商品调拨，自动更新库存

#### 👥 会员管理
- **会员档案**: 完整的会员信息管理
- **积分系统**: 灵活的积分规则和兑换机制
- **消费记录**: 详细的消费历史和统计

#### ♻️ 回收管理
- **旧料回收**: 专业的回收流程管理
- **金银分离**: 精确的材质分离和价值评估
- **成本核算**: 回收成本和收益分析

### 🔐 认证授权

#### JWT令牌认证
系统使用JWT（JSON Web Token）进行身份认证：

1. **登录获取令牌**: 使用用户名密码登录，获取访问令牌
2. **令牌使用**: 在请求头中添加 `Authorization: Bearer <token>`
3. **令牌刷新**: 支持令牌刷新机制，确保会话连续性

#### 权限控制
- **基于角色的权限控制（RBAC）**: 支持角色和权限的灵活配置
- **细粒度权限**: 每个API端点都有对应的权限检查
- **数据隔离**: 基于门店和用户角色的数据访问控制

### 📊 监控指标

系统集成了完整的监控体系：

- **性能指标**: 请求响应时间、吞吐量、错误率
- **业务指标**: 库存变化、销售数据、用户活跃度
- **安全指标**: 登录失败、异常访问、权限违规
- **系统指标**: CPU、内存、磁盘使用情况

### 🚀 快速开始

1. **获取访问令牌**:
   ```bash
   curl -X POST "/api/v1/auth/login" \\
   -H "Content-Type: application/json" \\
   -d '{{"username": "admin", "password": "admin123"}}'
   ```

2. **使用令牌访问API**:
   ```bash
   curl -X GET "/api/v1/jewelry/" \\
   -H "Authorization: Bearer <your-token>"
   ```

3. **查看监控指标**:
   ```bash
   curl -X GET "/metrics"
   ```

### 📞 技术支持

- **邮箱**: <EMAIL>
- **文档**: 查看详细的API文档和示例
- **监控**: 访问 `/metrics` 端点查看系统指标

### 🔄 版本信息

当前版本: {version}

更新内容:
- ✅ 集成FastAPI-Guard安全防护
- ✅ 添加Prometheus监控支持  
- ✅ 引入Ruff代码质量工具
- ✅ 完善API文档和响应示例
- ✅ 优化错误处理和状态码

""".format(version=settings.PROJECT_VERSION)


def get_servers():
    """获取服务器配置"""
    return [
        {"url": f"http://localhost:{settings.PORT}", "description": "开发环境服务器"},
        {"url": f"https://localhost:{settings.PORT}", "description": "开发环境服务器 (HTTPS)"},
    ]


def get_api_tags():
    """获取API标签配置"""
    return [
        {"name": "系统信息", "description": "系统基本信息、健康检查、监控指标等接口"},
        {"name": "认证授权", "description": "用户登录、令牌管理、权限验证等功能"},
        {"name": "商品管理", "description": "商品信息的增删改查、成本计算、库存跟踪等功能"},
        {"name": "商品分类管理", "description": "商品分类的层次化管理、分类属性配置等功能"},
        {"name": "库存管理", "description": "入库、出库、退货、盘点等库存相关操作"},
        {"name": "门店管理", "description": "多门店信息管理、门店权限控制、调拨管理等功能"},
        {"name": "会员管理", "description": "会员档案管理、积分系统、消费记录等功能"},
        {"name": "回收管理", "description": "旧料回收、金银分离、回收成本核算等功能"},
        {"name": "数据统计", "description": "销售报表、库存分析、业务数据统计等功能"},
        {"name": "系统管理", "description": "用户管理、角色权限、系统配置等管理功能"},
    ]


def get_common_responses():
    """获取通用响应组件"""
    return {
        "ValidationError": {
            "description": "请求参数验证失败",
            "content": {
                "application/json": {
                    "schema": {
                        "type": "object",
                        "properties": {
                            "detail": {
                                "type": "array",
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "loc": {"type": "array", "items": {"type": "string"}},
                                        "msg": {"type": "string"},
                                        "type": {"type": "string"},
                                    },
                                },
                            }
                        },
                    }
                }
            },
        },
        "Unauthorized": {
            "description": "未授权访问",
            "content": {
                "application/json": {
                    "schema": {
                        "type": "object",
                        "properties": {"detail": {"type": "string", "example": "请先登录"}},
                    }
                }
            },
        },
        "Forbidden": {
            "description": "权限不足",
            "content": {
                "application/json": {
                    "schema": {
                        "type": "object",
                        "properties": {"detail": {"type": "string", "example": "权限不足"}},
                    }
                }
            },
        },
        "NotFound": {
            "description": "资源不存在",
            "content": {
                "application/json": {
                    "schema": {
                        "type": "object",
                        "properties": {"detail": {"type": "string", "example": "资源不存在"}},
                    }
                }
            },
        },
        "InternalServerError": {
            "description": "服务器内部错误",
            "content": {
                "application/json": {
                    "schema": {
                        "type": "object",
                        "properties": {
                            "code": {"type": "integer", "example": 500},
                            "message": {"type": "string", "example": "服务器内部错误"},
                            "data": {"type": "object", "nullable": True},
                        },
                    }
                }
            },
        },
    }
