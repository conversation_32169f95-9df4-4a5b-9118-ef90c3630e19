import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../core/constants/border_styles.dart';
import '../../../core/theme/app_theme.dart';
import '../../../widgets/responsive_builder.dart';
import '../controllers/inventory_check_operation_controller.dart';

/// 库存盘点操作页面
///
/// 提供扫码盘点、手动录入、差异显示等功能
/// 严格遵循UI设计规范和边框样式标准
class InventoryCheckOperationView extends StatelessWidget {
  final String? tag;

  const InventoryCheckOperationView({super.key, this.tag});

  @override
  Widget build(BuildContext context) {
    final controller = tag != null
        ? Get.find<InventoryCheckOperationController>(tag: tag)
        : Get.find<InventoryCheckOperationController>();

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: Column(
        children: [
          // 盘点单信息区域
          _buildInventoryCheckInfo(controller),
          const Divider(height: 1),
          // 操作工具栏
          _buildToolbar(controller),
          const Divider(height: 1),
          // 主要内容区域
          Expanded(child: _buildMainContent(controller)),
          // 底部操作栏
          _buildBottomActions(controller),
        ],
      ),
    );
  }



  /// 构建盘点单信息区域
  Widget _buildInventoryCheckInfo(InventoryCheckOperationController controller) {
    return Obx(() {
      final check = controller.inventoryCheck.value;
      if (check == null) return const SizedBox.shrink();

      return Container(
        padding: const EdgeInsets.all(16),
        decoration: AppBorderStyles.standardBoxDecoration.copyWith(
          color: Colors.blue[50],
        ),
        child: Row(
          children: [
            // 基本信息
            Expanded(
              flex: 2,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '盘点单号: ${check.checkNo}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '门店: ${check.storeName ?? '未知门店'}',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[700],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '创建时间: ${DateFormat('yyyy-MM-dd HH:mm').format(check.createTime)}',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[700],
                    ),
                  ),
                ],
              ),
            ),
            // 进度信息
            Expanded(
              flex: 1,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '盘点进度',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Colors.grey[700],
                    ),
                  ),
                  const SizedBox(height: 8),
                  LinearProgressIndicator(
                    value: check.progress,
                    backgroundColor: Colors.grey[300],
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.blue[600]!),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${check.checkedCount}/${check.totalCount} (${(check.progress * 100).toStringAsFixed(1)}%)',
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    });
  }

  /// 构建操作工具栏
  Widget _buildToolbar(InventoryCheckOperationController controller) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: AppBorderStyles.tableBorder,
        ),
      ),
      child: Row(
        children: [
          // 扫码盘点输入框 - 使用固定宽度而非Expanded
          _buildSearchField(controller),
          const SizedBox(width: 16),
          // 状态筛选
          SizedBox(
            width: 120,
            child: _buildStatusFilter(controller),
          ),
          const SizedBox(width: 16),
          // 确认盘点按钮
          SizedBox(
            height: 32,
            child: ElevatedButton.icon(
              icon: const Icon(Icons.check_circle, size: 16),
              label: const Text('确认盘点'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue[600],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
                ),
                textStyle: const TextStyle(fontSize: 13),
              ),
              onPressed: () => controller.handleMainScanInput(controller.searchController.text),
            ),
          ),
          const SizedBox(width: 8),
          // 搜索按钮（辅助功能）
          SizedBox(
            height: 32,
            child: OutlinedButton.icon(
              icon: const Icon(Icons.search, size: 16),
              label: const Text('搜索'),
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.grey[600],
                padding: const EdgeInsets.symmetric(horizontal: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
                ),
                side: const BorderSide(color: AppBorderStyles.borderColor),
                textStyle: const TextStyle(fontSize: 13),
              ),
              onPressed: controller.searchItems,
            ),
          ),
          const SizedBox(width: 8),
          // 批量操作按钮
          SizedBox(
            height: 32,
            child: OutlinedButton.icon(
              icon: const Icon(Icons.checklist, size: 16),
              label: const Text('批量'),
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.grey[600],
                padding: const EdgeInsets.symmetric(horizontal: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
                ),
                side: const BorderSide(color: AppBorderStyles.borderColor),
                textStyle: const TextStyle(fontSize: 13),
              ),
              onPressed: controller.showBatchOperationDialog,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建扫码盘点输入字段
  Widget _buildSearchField(InventoryCheckOperationController controller) {
    return Container(
      height: 32,
      width: 280, // 减小输入框宽度，使其更加紧凑
      decoration: AppBorderStyles.standardBoxDecoration,
      child: TextField(
        controller: controller.searchController,
        decoration: const InputDecoration(
          hintText: '扫描或输入条码进行盘点',
          border: InputBorder.none,
          enabledBorder: InputBorder.none,
          focusedBorder: InputBorder.none,
          disabledBorder: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          hintStyle: TextStyle(fontSize: 13, color: Colors.grey),
          isDense: true,
          prefixIcon: Icon(Icons.qr_code_scanner, size: 16, color: Colors.grey),
        ),
        style: const TextStyle(fontSize: 13),
        onSubmitted: (barcode) => controller.handleMainScanInput(barcode),
      ),
    );
  }

  /// 构建状态筛选器
  Widget _buildStatusFilter(InventoryCheckOperationController controller) {
    return Obx(() => Container(
      height: 32,
      decoration: AppBorderStyles.standardBoxDecoration,
      child: DropdownButtonHideUnderline(
        child: DropdownButton<int?>(
          value: controller.selectedStatus.value,
          hint: const Text('全部状态', style: TextStyle(fontSize: 13, color: Colors.grey)),
          isExpanded: true,
          items: const [
            DropdownMenuItem<int?>(
              value: null,
              child: Text('全部状态', style: TextStyle(fontSize: 13)),
            ),
            DropdownMenuItem<int?>(
              value: 0,
              child: Text('未盘点', style: TextStyle(fontSize: 13)),
            ),
            DropdownMenuItem<int?>(
              value: 1,
              child: Text('已盘点', style: TextStyle(fontSize: 13)),
            ),
          ],
          onChanged: (value) {
            controller.selectedStatus.value = value;
            controller.filterItems();
          },
          style: const TextStyle(fontSize: 13, color: Colors.black87),
          icon: const Icon(Icons.arrow_drop_down, size: 20),
          iconSize: 20,
          menuMaxHeight: 300,
          padding: const EdgeInsets.symmetric(horizontal: 8),
        ),
      ),
    ));
  }

  /// 构建主要内容区域
  Widget _buildMainContent(InventoryCheckOperationController controller) {
    return ResponsiveBuilder(
      builder: (context, sizingInformation) {
        if (sizingInformation.isMobile) {
          return _buildMobileItemList(controller);
        } else {
          return _buildDesktopItemTable(controller);
        }
      },
    );
  }

  /// 构建桌面端商品表格
  Widget _buildDesktopItemTable(InventoryCheckOperationController controller) {
    return Obx(() {
      if (controller.isLoading.value) {
        return const Center(child: CircularProgressIndicator());
      }

      if (controller.filteredItems.isEmpty) {
        return const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.inventory_2_outlined, size: 64, color: Colors.grey),
              SizedBox(height: 16),
              Text('暂无盘点商品', style: TextStyle(color: Colors.grey)),
            ],
          ),
        );
      }

      return LayoutBuilder(
        builder: (context, constraints) {
          final availableWidth = constraints.maxWidth;

          // 优化列宽分配 - 调整为95%总宽度，为滚动条预留空间
          final tableWidth = availableWidth * 0.95;       // 表格总宽度95%，预留5%给滚动条
          final statusWidth = tableWidth * 0.06;          // 6% - 状态（缩小）
          final barcodeWidth = tableWidth * 0.15;         // 15% - 条码
          final nameWidth = tableWidth * 0.20;            // 20% - 商品名称
          final categoryWidth = tableWidth * 0.10;        // 10% - 分类
          final ringSizeWidth = tableWidth * 0.08;        // 8% - 圈口号
          final systemStockWidth = tableWidth * 0.08;     // 8% - 系统库存
          final actualStockWidth = tableWidth * 0.08;     // 8% - 实际库存
          final differenceWidth = tableWidth * 0.08;      // 8% - 差异
          final checkTimeWidth = tableWidth * 0.10;       // 10% - 盘点时间
          final actionWidth = tableWidth * 0.07;          // 7% - 操作（增加）

          return Container(
            margin: const EdgeInsets.all(4),
            decoration: AppBorderStyles.elevatedBoxDecoration.copyWith(
              color: Colors.white,
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
              child: SingleChildScrollView(
                scrollDirection: Axis.vertical,
                child: SizedBox(
                  width: tableWidth, // 使用调整后的表格宽度
                  child: DataTable(
                    columnSpacing: 0,
                    horizontalMargin: 0,
                    headingRowHeight: 40,
                    dataRowMinHeight: 38,
                    dataRowMaxHeight: 38,
                    headingTextStyle: const TextStyle(
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                      fontSize: 14,
                    ),
                    dataTextStyle: const TextStyle(
                      fontSize: 13,
                      color: Colors.black87,
                    ),
                    headingRowColor: WidgetStateProperty.all(AppBorderStyles.tableHeaderBackground),
                    border: AppBorderStyles.tableStandardBorder,
                    columns: [
                      DataColumn(
                        label: Container(
                          width: statusWidth,
                          padding: const EdgeInsets.symmetric(horizontal: 4),
                          child: const Text('状态', textAlign: TextAlign.center),
                        ),
                      ),
                      DataColumn(
                        label: Container(
                          width: barcodeWidth,
                          padding: const EdgeInsets.symmetric(horizontal: 4),
                          child: const Text('条码', textAlign: TextAlign.center),
                        ),
                      ),
                      DataColumn(
                        label: Container(
                          width: nameWidth,
                          padding: const EdgeInsets.symmetric(horizontal: 4),
                          child: const Text('商品名称', textAlign: TextAlign.center),
                        ),
                      ),
                      DataColumn(
                        label: Container(
                          width: categoryWidth,
                          padding: const EdgeInsets.symmetric(horizontal: 4),
                          child: const Text('分类', textAlign: TextAlign.center),
                        ),
                      ),
                      DataColumn(
                        label: Container(
                          width: ringSizeWidth,
                          padding: const EdgeInsets.symmetric(horizontal: 4),
                          child: const Text('圈口号', textAlign: TextAlign.center),
                        ),
                      ),
                      DataColumn(
                        label: Container(
                          width: systemStockWidth,
                          padding: const EdgeInsets.symmetric(horizontal: 4),
                          child: const Text('系统库存', textAlign: TextAlign.center),
                        ),
                      ),
                      DataColumn(
                        label: Container(
                          width: actualStockWidth,
                          padding: const EdgeInsets.symmetric(horizontal: 4),
                          child: const Text('实际库存', textAlign: TextAlign.center),
                        ),
                      ),
                      DataColumn(
                        label: Container(
                          width: differenceWidth,
                          padding: const EdgeInsets.symmetric(horizontal: 4),
                          child: const Text('差异', textAlign: TextAlign.center),
                        ),
                      ),
                      DataColumn(
                        label: Container(
                          width: checkTimeWidth,
                          padding: const EdgeInsets.symmetric(horizontal: 4),
                          child: const Text('盘点时间', textAlign: TextAlign.center),
                        ),
                      ),
                      DataColumn(
                        label: Container(
                          width: actionWidth,
                          padding: const EdgeInsets.symmetric(horizontal: 4),
                          child: const Text('操作', textAlign: TextAlign.center),
                        ),
                      ),
                    ],
                    rows: controller.filteredItems.map((item) {
                      return DataRow(
                        cells: [
                          DataCell(
                            Container(
                              width: statusWidth,
                              height: 38,
                              padding: const EdgeInsets.symmetric(horizontal: 4),
                              alignment: Alignment.center,
                              child: _buildItemStatusIcon(item),
                            ),
                          ),
                          DataCell(
                            Container(
                              width: barcodeWidth,
                              height: 38,
                              padding: const EdgeInsets.symmetric(horizontal: 4),
                              alignment: Alignment.center,
                              child: Text(
                                item.barcode,
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                            ),
                          ),
                          DataCell(
                            Container(
                              width: nameWidth,
                              height: 38,
                              padding: const EdgeInsets.symmetric(horizontal: 4),
                              alignment: Alignment.center,
                              child: Text(
                                item.name,
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                            ),
                          ),
                          DataCell(
                            Container(
                              width: categoryWidth,
                              height: 38,
                              padding: const EdgeInsets.symmetric(horizontal: 4),
                              alignment: Alignment.center,
                              child: Text(
                                item.categoryName ?? '-',
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                            ),
                          ),
                          DataCell(
                            Container(
                              width: ringSizeWidth,
                              height: 38,
                              padding: const EdgeInsets.symmetric(horizontal: 4),
                              alignment: Alignment.center,
                              child: Text(
                                item.ringSize ?? '-',
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                            ),
                          ),
                          DataCell(
                            Container(
                              width: systemStockWidth,
                              height: 38,
                              padding: const EdgeInsets.symmetric(horizontal: 4),
                              alignment: Alignment.center,
                              child: Text(
                                item.systemStock.toString(),
                                style: const TextStyle(
                                  fontWeight: FontWeight.w500,
                                  color: AppTheme.primaryColor,
                                ),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                            ),
                          ),
                          DataCell(
                            Container(
                              width: actualStockWidth,
                              height: 38,
                              padding: const EdgeInsets.symmetric(horizontal: 4),
                              alignment: Alignment.center,
                              child: item.status == 1
                                  ? Text(
                                      item.actualStock?.toString() ?? '-',
                                      style: const TextStyle(
                                        fontWeight: FontWeight.w500,
                                        color: Colors.green,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                      maxLines: 1,
                                    )
                                  : const Text('-', style: TextStyle(color: Colors.grey)),
                            ),
                          ),
                          DataCell(
                            Container(
                              width: differenceWidth,
                              height: 38,
                              padding: const EdgeInsets.symmetric(horizontal: 4),
                              alignment: Alignment.center,
                              child: item.status == 1
                                  ? Text(
                                      item.difference?.toString() ?? '-',
                                      style: TextStyle(
                                        fontWeight: FontWeight.w500,
                                        color: (item.difference ?? 0) != 0 ? Colors.red : Colors.green,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                      maxLines: 1,
                                    )
                                  : const Text('-', style: TextStyle(color: Colors.grey)),
                            ),
                          ),
                          DataCell(
                            Container(
                              width: checkTimeWidth,
                              height: 38,
                              padding: const EdgeInsets.symmetric(horizontal: 4),
                              alignment: Alignment.center,
                              child: Text(
                                item.checkTime != null
                                    ? DateFormat('MM-dd HH:mm').format(item.checkTime!)
                                    : '-',
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                            ),
                          ),
                          DataCell(
                            Container(
                              width: actionWidth,
                              height: 38,
                              padding: const EdgeInsets.symmetric(horizontal: 4),
                              alignment: Alignment.center,
                              child: _buildItemActionButton(controller, item),
                            ),
                          ),
                        ],
                      );
                    }).toList(),
                  ),
                ),
              ),
            ),
          );
        },
      );
    });
  }

  /// 构建移动端商品列表
  Widget _buildMobileItemList(InventoryCheckOperationController controller) {
    return Obx(() {
      if (controller.isLoading.value) {
        return const Center(child: CircularProgressIndicator());
      }

      if (controller.filteredItems.isEmpty) {
        return const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.inventory_2_outlined, size: 64, color: Colors.grey),
              SizedBox(height: 16),
              Text('暂无盘点商品', style: TextStyle(color: Colors.grey)),
            ],
          ),
        );
      }

      return ListView.builder(
        padding: const EdgeInsets.all(8),
        itemCount: controller.filteredItems.length,
        itemBuilder: (context, index) {
          final item = controller.filteredItems[index];
          return Card(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppBorderStyles.largeBorderRadius),
            ),
            elevation: 2,
            margin: const EdgeInsets.only(bottom: 8),
            child: InkWell(
              borderRadius: BorderRadius.circular(AppBorderStyles.largeBorderRadius),
              onTap: () => _showItemCheckDialog(controller, item),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Text(
                            item.name,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        _buildItemStatusIcon(item),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text('条码: ${item.barcode}'),
                    Text('分类: ${item.categoryName ?? '-'}'),
                    if (item.ringSize != null) Text('圈口号: ${item.ringSize}'),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Text('系统库存: ${item.systemStock}'),
                        const SizedBox(width: 16),
                        if (item.status == 1) ...[
                          Text('实际库存: ${item.actualStock ?? '-'}'),
                          const SizedBox(width: 16),
                          Text(
                            '差异: ${item.difference ?? '-'}',
                            style: TextStyle(
                              color: (item.difference ?? 0) != 0 ? Colors.red : Colors.green,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ] else
                          const Text('实际库存: 未盘点', style: TextStyle(color: Colors.grey)),
                      ],
                    ),
                    if (item.checkTime != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        '盘点时间: ${DateFormat('yyyy-MM-dd HH:mm').format(item.checkTime!)}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        _buildItemActionButton(controller, item),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      );
    });
  }

  /// 构建商品状态图标
  Widget _buildItemStatusIcon(dynamic item) {
    if (item.status == 1) {
      // 已盘点
      if ((item.difference ?? 0) == 0) {
        return const Icon(Icons.check_circle, color: Colors.green, size: 20);
      } else {
        return const Icon(Icons.warning, color: Colors.red, size: 20);
      }
    } else {
      // 未盘点
      return const Icon(Icons.radio_button_unchecked, color: Colors.grey, size: 20);
    }
  }

  /// 构建商品操作按钮
  Widget _buildItemActionButton(InventoryCheckOperationController controller, dynamic item) {
    if (item.status == 1) {
      // 已盘点，显示重新盘点按钮
      return SizedBox(
        height: 28,
        child: OutlinedButton(
          onPressed: () => _showItemCheckDialog(controller, item),
          style: OutlinedButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
            ),
            side: BorderSide(color: Colors.orange[600]!),
          ),
          child: Text(
            '重盘',
            style: TextStyle(
              fontSize: 12,
              color: Colors.orange[600],
            ),
          ),
        ),
      );
    } else {
      // 未盘点，显示盘点按钮
      return SizedBox(
        height: 28,
        child: ElevatedButton(
          onPressed: () => _showItemCheckDialog(controller, item),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue[600],
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
            ),
          ),
          child: const Text(
            '盘点',
            style: TextStyle(fontSize: 12),
          ),
        ),
      );
    }
  }

  /// 显示商品盘点对话框
  Future<void> _showItemCheckDialog(InventoryCheckOperationController controller, dynamic item) async {
    final actualStockController = TextEditingController(
      text: item.actualStock?.toString() ?? item.systemStock.toString(),
    );
    final remarkController = TextEditingController(text: item.remark ?? '');

    await Get.dialog<bool>(
      AlertDialog(
        title: Text('盘点商品 - ${item.name}'),
        content: SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('条码: ${item.barcode}'),
              Text('系统库存: ${item.systemStock}'),
              const SizedBox(height: 16),
              TextField(
                controller: actualStockController,
                keyboardType: TextInputType.number,
                decoration: AppBorderStyles.standardInputDecoration.copyWith(
                  labelText: '实际库存',
                  hintText: '请输入实际库存数量',
                ),
              ),
              const SizedBox(height: 12),
              TextField(
                controller: remarkController,
                decoration: AppBorderStyles.standardInputDecoration.copyWith(
                  labelText: '备注',
                  hintText: '请输入盘点备注（可选）',
                ),
                maxLines: 2,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              final actualStock = int.tryParse(actualStockController.text);
              if (actualStock == null) {
                Get.snackbar('错误', '请输入有效的库存数量');
                return;
              }
              Get.back(result: true);
              controller.checkItemWithRemark(item, actualStock, remarkController.text);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue[600],
              foregroundColor: Colors.white,
            ),
            child: const Text('确定'),
          ),
        ],
      ),
    );

    actualStockController.dispose();
    remarkController.dispose();
  }

  /// 构建底部操作栏
  Widget _buildBottomActions(InventoryCheckOperationController controller) {
    return Obx(() => Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          top: AppBorderStyles.tableBorder,
        ),
      ),
      child: Row(
        children: [
          // 统计信息
          Expanded(
            child: Row(
              children: [
                Text(
                  '总计: ${controller.totalItems.value} 件',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(width: 16),
                Text(
                  '已盘: ${controller.checkedItems.value} 件',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.green,
                  ),
                ),
                const SizedBox(width: 16),
                Text(
                  '差异: ${controller.differenceItems.value} 件',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.red,
                  ),
                ),
                const SizedBox(width: 32),
                // 分页控件 - 居中显示
                _buildPagination(controller),
              ],
            ),
          ),
          // 操作按钮
          SizedBox(
            height: 36,
            child: OutlinedButton(
              onPressed: () => Get.back(),
              style: OutlinedButton.styleFrom(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
                ),
                side: const BorderSide(color: AppBorderStyles.borderColor),
              ),
              child: const Text('返回'),
            ),
          ),
          const SizedBox(width: 12),
          SizedBox(
            height: 36,
            child: ElevatedButton(
              onPressed: controller.canComplete ? controller.completeInventoryCheck : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green[600],
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
                ),
              ),
              child: const Text('完成盘点'),
            ),
          ),
        ],
      ),
    ));
  }

  /// 构建分页控件 - 参考出库管理的分页功能
  Widget _buildPagination(InventoryCheckOperationController controller) {
    return Obx(() => Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          icon: const Icon(Icons.first_page, size: 18),
          tooltip: '第一页',
          onPressed: controller.currentPage.value > 1
              ? () => controller.goToPage(1)
              : null,
        ),
        IconButton(
          icon: const Icon(Icons.chevron_left, size: 18),
          tooltip: '上一页',
          onPressed: controller.currentPage.value > 1
              ? () => controller.goToPage(controller.currentPage.value - 1)
              : null,
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: Text(
            '${controller.currentPage.value} / ${controller.totalPages.value}',
            style: const TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        IconButton(
          icon: const Icon(Icons.chevron_right, size: 18),
          tooltip: '下一页',
          onPressed: controller.currentPage.value < controller.totalPages.value
              ? () => controller.goToPage(controller.currentPage.value + 1)
              : null,
        ),
        IconButton(
          icon: const Icon(Icons.last_page, size: 18),
          tooltip: '最后一页',
          onPressed: controller.currentPage.value < controller.totalPages.value
              ? () => controller.goToPage(controller.totalPages.value)
              : null,
        ),
      ],
    ));
  }
}
