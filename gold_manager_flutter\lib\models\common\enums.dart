/// 系统中使用的所有枚举类型定义
library;

/// 商品状态枚举
enum JewelryStatus {
  /// 已下架，不能出售
  offShelf(0, '已下架'),

  /// 上架在售，可以出售
  onShelf(1, '上架在售'),

  /// 待出库状态
  pendingOut(2, '待出库');

  /// 状态值
  final int value;

  /// 状态标签
  final String label;

  /// 构造函数
  const JewelryStatus(this.value, this.label);

  /// 根据值获取状态
  static JewelryStatus fromValue(int value) {
    return JewelryStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => JewelryStatus.offShelf,
    );
  }
}

/// 销售类型枚举
enum SalesType {
  retail(0, '零售'),
  wholesale(1, '批发');
  
  final int value;
  final String label;
  
  const SalesType(this.value, this.label);
  
  /// 根据值获取枚举
  static SalesType fromValue(int value) {
    return SalesType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => SalesType.retail,
    );
  }
}

/// 支付方式枚举
enum PaymentMethod {
  cash(0, '现金'),
  alipay(1, '支付宝'),
  wechat(2, '微信'),
  card(3, '银行卡'),
  mixed(4, '混合支付');
  
  final int value;
  final String label;
  
  const PaymentMethod(this.value, this.label);
  
  /// 根据值获取枚举
  static PaymentMethod fromValue(int value) {
    return PaymentMethod.values.firstWhere(
      (method) => method.value == value,
      orElse: () => PaymentMethod.cash,
    );
  }
}

/// 旧料处理方式枚举
enum RecyclingProcessType {
  pending('pending', '待处理'),
  sell('sell', '直接卖出'),
  separate('separate', '金银分离'),
  repair('repair', '维修再销售'),
  resell('resell', '直接二次销售');

  final String value;
  final String label;

  const RecyclingProcessType(this.value, this.label);

  /// 根据值获取枚举
  static RecyclingProcessType fromValue(String value) {
    return RecyclingProcessType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => RecyclingProcessType.pending,
    );
  }
}

/// 旧料回收方式枚举
enum OldMaterialRecyclingType {
  byWeight('by_weight', '按克回收'),
  byPiece('by_piece', '按件回收');

  final String value;
  final String label;

  const OldMaterialRecyclingType(this.value, this.label);

  /// 根据值获取枚举
  static OldMaterialRecyclingType fromValue(String value) {
    return OldMaterialRecyclingType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => OldMaterialRecyclingType.byWeight,
    );
  }
}

/// 贵金属成色枚举
enum MetalPurity {
  goldSilver('金包银', '金包银'),
  au9999('AU9999', 'AU9999'),
  au999('AU999', 'AU999'),
  au990('AU990', 'AU990'),
  au916('AU916', 'AU916'),
  au750('AU750', 'AU750'),
  ag999('AG999', 'AG999'),
  ag925('AG925', 'AG925'),
  pt999('PT999', 'PT999'),
  pt950('PT950', 'PT950'),
  pt900('PT900', 'PT900'),
  pd999('PD999', 'PD999'),
  pd950('PD950', 'PD950'),
  pd900('PD900', 'PD900');

  final String value;
  final String label;

  const MetalPurity(this.value, this.label);

  /// 根据值获取枚举
  static MetalPurity fromValue(String value) {
    return MetalPurity.values.firstWhere(
      (purity) => purity.value == value,
      orElse: () => MetalPurity.goldSilver,
    );
  }

  /// 获取所有成色选项
  static List<Map<String, String>> get options {
    return MetalPurity.values.map((purity) => {
      'value': purity.value,
      'label': purity.label,
    }).toList();
  }
}