import 'package:intl/intl.dart';

/// 数值格式化工具类
class NumberFormatter {
  // 货币格式化器（保留2位小数，千分位分隔符）
  static final NumberFormat _currencyFormatter = NumberFormat('#,##0.00');
  
  // 重量格式化器（保留2位小数）
  static final NumberFormat _weightFormatter = NumberFormat('0.00');
  
  // 价格格式化器（保留2位小数）
  static final NumberFormat _priceFormatter = NumberFormat('0.00');
  
  // 百分比格式化器（保留1位小数）
  static final NumberFormat _percentFormatter = NumberFormat('0.0');

  /// 格式化货币金额
  /// [amount] 金额
  /// [showSymbol] 是否显示货币符号
  /// [defaultValue] 当值为null或0时的默认显示
  static String formatCurrency(
    double? amount, {
    bool showSymbol = true,
    String defaultValue = '-',
  }) {
    if (amount == null || amount == 0) {
      return defaultValue;
    }
    
    final formatted = _currencyFormatter.format(amount);
    return showSymbol ? '¥$formatted' : formatted;
  }

  /// 格式化重量
  /// [weight] 重量
  /// [unit] 单位
  /// [defaultValue] 当值为null或0时的默认显示
  static String formatWeight(
    double? weight, {
    String unit = 'g',
    String defaultValue = '-',
  }) {
    if (weight == null || weight == 0) {
      return defaultValue;
    }
    
    return '${_weightFormatter.format(weight)}$unit';
  }

  /// 格式化价格
  /// [price] 价格
  /// [unit] 单位
  /// [defaultValue] 当值为null或0时的默认显示
  static String formatPrice(
    double? price, {
    String unit = '元/g',
    String defaultValue = '-',
  }) {
    if (price == null || price == 0) {
      return defaultValue;
    }
    
    return '${_priceFormatter.format(price)}$unit';
  }

  /// 格式化百分比
  /// [rate] 百分比值（如：95.5表示95.5%）
  /// [defaultValue] 当值为null或0时的默认显示
  static String formatPercent(
    double? rate, {
    String defaultValue = '-',
  }) {
    if (rate == null || rate == 0) {
      return defaultValue;
    }
    
    return '${_percentFormatter.format(rate)}%';
  }

  /// 格式化折扣信息
  /// [discountRate] 折扣率
  /// [discountAmount] 折扣金额
  static String formatDiscount(
    double? discountRate,
    double? discountAmount,
  ) {
    if ((discountRate == null || discountRate == 0) && 
        (discountAmount == null || discountAmount == 0)) {
      return '-';
    }
    
    final List<String> parts = [];
    
    if (discountRate != null && discountRate > 0) {
      parts.add(formatPercent(discountRate));
    }
    
    if (discountAmount != null && discountAmount > 0) {
      parts.add(formatCurrency(discountAmount));
    }
    
    return parts.join(' / ');
  }

  /// 安全的数值转换
  /// [value] 要转换的值
  /// [defaultValue] 默认值
  static double safeDouble(dynamic value, {double defaultValue = 0.0}) {
    if (value == null) return defaultValue;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value) ?? defaultValue;
    }
    return defaultValue;
  }

  /// 检查数值是否有效（非null且大于0）
  static bool isValidNumber(double? value) {
    return value != null && value > 0;
  }

  /// 格式化数值为字符串（通用方法）
  /// [value] 数值
  /// [decimalPlaces] 小数位数
  /// [showThousandsSeparator] 是否显示千分位分隔符
  /// [defaultValue] 默认值
  static String formatNumber(
    double? value, {
    int decimalPlaces = 2,
    bool showThousandsSeparator = false,
    String defaultValue = '-',
  }) {
    if (value == null || value == 0) {
      return defaultValue;
    }
    
    String pattern = showThousandsSeparator ? '#,##0' : '0';
    if (decimalPlaces > 0) {
      pattern += '.${'0' * decimalPlaces}';
    }
    
    final formatter = NumberFormat(pattern);
    return formatter.format(value);
  }
}
