import 'package:equatable/equatable.dart';

/// 库存盘点统计模型
class InventoryCheckStatistics extends Equatable {
  /// 总盘点单数
  final int totalChecks;
  
  /// 进行中盘点单数
  final int ongoingChecks;
  
  /// 已完成盘点单数
  final int completedChecks;
  
  /// 已取消盘点单数
  final int cancelledChecks;
  
  /// 总商品数
  final int totalItems;
  
  /// 已盘点商品数
  final int checkedItems;
  
  /// 差异商品数
  final int differenceItems;
  
  /// 准确率
  final double accuracyRate;

  const InventoryCheckStatistics({
    required this.totalChecks,
    required this.ongoingChecks,
    required this.completedChecks,
    required this.cancelledChecks,
    required this.totalItems,
    required this.checkedItems,
    required this.differenceItems,
    required this.accuracyRate,
  });

  /// 完成率
  double get completionRate {
    if (totalItems == 0) return 0.0;
    return checkedItems / totalItems;
  }

  /// 完成率百分比文本
  String get completionRateText {
    return '${(completionRate * 100).toStringAsFixed(1)}%';
  }

  /// 准确率百分比文本
  String get accuracyRateText {
    return '${(accuracyRate * 100).toStringAsFixed(1)}%';
  }

  /// 从JSON创建实例
  factory InventoryCheckStatistics.fromJson(Map<String, dynamic> json) {
    return InventoryCheckStatistics(
      totalChecks: json['total_checks'] ?? 0,
      ongoingChecks: json['ongoing_checks'] ?? 0,
      completedChecks: json['completed_checks'] ?? 0,
      cancelledChecks: json['cancelled_checks'] ?? 0,
      totalItems: json['total_items'] ?? 0,
      checkedItems: json['checked_items'] ?? 0,
      differenceItems: json['difference_items'] ?? 0,
      accuracyRate: (json['accuracy_rate'] ?? 0.0).toDouble(),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'total_checks': totalChecks,
      'ongoing_checks': ongoingChecks,
      'completed_checks': completedChecks,
      'cancelled_checks': cancelledChecks,
      'total_items': totalItems,
      'checked_items': checkedItems,
      'difference_items': differenceItems,
      'accuracy_rate': accuracyRate,
    };
  }

  /// 复制并修改部分属性
  InventoryCheckStatistics copyWith({
    int? totalChecks,
    int? ongoingChecks,
    int? completedChecks,
    int? cancelledChecks,
    int? totalItems,
    int? checkedItems,
    int? differenceItems,
    double? accuracyRate,
  }) {
    return InventoryCheckStatistics(
      totalChecks: totalChecks ?? this.totalChecks,
      ongoingChecks: ongoingChecks ?? this.ongoingChecks,
      completedChecks: completedChecks ?? this.completedChecks,
      cancelledChecks: cancelledChecks ?? this.cancelledChecks,
      totalItems: totalItems ?? this.totalItems,
      checkedItems: checkedItems ?? this.checkedItems,
      differenceItems: differenceItems ?? this.differenceItems,
      accuracyRate: accuracyRate ?? this.accuracyRate,
    );
  }

  @override
  List<Object?> get props => [
        totalChecks,
        ongoingChecks,
        completedChecks,
        cancelledChecks,
        totalItems,
        checkedItems,
        differenceItems,
        accuracyRate,
      ];

  @override
  String toString() {
    return 'InventoryCheckStatistics{totalChecks: $totalChecks, completionRate: $completionRateText, accuracyRate: $accuracyRateText}';
  }
}
