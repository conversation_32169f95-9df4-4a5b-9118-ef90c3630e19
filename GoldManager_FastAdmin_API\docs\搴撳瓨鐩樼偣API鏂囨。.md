# 📋 库存盘点API文档

## 📖 功能概述

库存盘点API提供完整的库存盘点业务流程管理，包括盘点单创建、盘点操作、进度跟踪、差异管理等功能。

## 🎯 核心特性

### ✨ 主要功能
- **盘点单管理**: 创建、查询、更新、删除盘点单
- **智能单号生成**: 自动生成CHKYYYYMMDD0001格式的盘点单号
- **盘点流程管理**: 支持单个盘点和批量盘点操作
- **差异管理**: 自动计算系统库存与实际库存差异
- **进度跟踪**: 实时显示盘点进度和完成百分比
- **状态管理**: 进行中 → 已完成 / 已取消
- **统计分析**: 多维度业务统计和分析
- **🔐 JWT身份验证**: 所有API端点都需要有效的JWT令牌
- **🛡️ 权限控制**: 基于角色的细粒度权限管理
- **🏢 数据隔离**: 门店级别的数据访问控制

### 🔐 安全特性

#### JWT身份验证
所有API端点都需要有效的JWT令牌：
```http
Authorization: Bearer <your_jwt_token>
```

#### 权限控制
- `inventory.view`: 查看盘点数据
- `inventory.create`: 创建盘点单
- `inventory.update`: 更新盘点单
- `inventory.delete`: 删除盘点单
- `inventory.check`: 执行盘点操作

#### 数据隔离
- **管理员**: 可以访问所有门店的盘点数据
- **门店用户**: 只能访问自己门店的盘点数据

### 🔄 业务流程
1. **创建盘点单**: 选择门店和商品，系统自动生成盘点单
2. **开始盘点**: 盘点人员扫描商品条码，录入实际库存
3. **差异处理**: 系统自动计算差异，标记异常商品
4. **完成盘点**: 确认所有商品盘点完成，更新盘点状态
5. **统计分析**: 查看盘点结果和差异统计

## 📊 数据模型

### 盘点单主表 (fa_inventory_check)
```sql
CREATE TABLE `fa_inventory_check` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '盘点单ID',
  `check_no` varchar(50) NOT NULL COMMENT '盘点单号',
  `store_id` int(11) NOT NULL COMMENT '盘点门店',
  `status` tinyint(1) DEFAULT '0' COMMENT '状态:0=进行中,1=已完成,2=已取消',
  `start_time` int(10) DEFAULT NULL COMMENT '开始时间',
  `end_time` int(10) DEFAULT NULL COMMENT '结束时间',
  `operator_id` int(11) NOT NULL COMMENT '操作员ID',
  `total_count` int(11) DEFAULT '0' COMMENT '应盘总数',
  `checked_count` int(11) DEFAULT '0' COMMENT '已盘数量',
  `difference_count` int(11) DEFAULT '0' COMMENT '差异数量',
  `remark` text COMMENT '备注',
  `createtime` int(10) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `check_no` (`check_no`)
);
```

### 盘点明细表 (fa_inventory_check_item)
```sql
CREATE TABLE `fa_inventory_check_item` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '明细ID',
  `check_id` int(11) NOT NULL COMMENT '盘点单ID',
  `jewelry_id` int(11) NOT NULL COMMENT '商品ID',
  `barcode` varchar(50) NOT NULL COMMENT '条码',
  `name` varchar(100) NOT NULL COMMENT '商品名称',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `ring_size` varchar(20) DEFAULT NULL COMMENT '圈口号',
  `status` tinyint(1) DEFAULT '0' COMMENT '状态:0=未盘点,1=已盘点',
  `system_stock` int(11) DEFAULT '1' COMMENT '系统库存',
  `actual_stock` int(11) DEFAULT NULL COMMENT '实际库存',
  `difference` int(11) DEFAULT NULL COMMENT '差异',
  `check_time` int(10) DEFAULT NULL COMMENT '盘点时间',
  `check_user_id` int(11) DEFAULT NULL COMMENT '盘点人员ID',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `createtime` int(10) DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`)
);
```

## 🔌 API接口

### 基础URL
```
http://localhost:8000/api/v1/inventory-check
```

### 1. 创建盘点单
```http
POST /api/v1/inventory-check
Authorization: Bearer <your_jwt_token>
Content-Type: application/json

{
  "store_id": 1,
  "remark": "月度盘点",
  "items": [
    {
      "jewelry_id": 1,
      "barcode": "JW001",
      "name": "黄金戒指",
      "category_id": 1,
      "ring_size": "15",
      "system_stock": 1,
      "remark": "正常商品"
    }
  ]
}
```

**权限要求**: `inventory.create`
**数据隔离**: 非管理员用户只能为自己的门店创建盘点单

### 2. 获取盘点单列表
```http
GET /api/v1/inventory-check?page=1&page_size=20&status=0&keyword=CHK20241220
Authorization: Bearer <your_jwt_token>
```

**权限要求**: `inventory.view`
**数据隔离**: 非管理员用户只能查询自己门店的盘点单

**查询参数:**
- `page`: 页码 (默认: 1)
- `page_size`: 每页数量 (默认: 20)
- `keyword`: 关键词搜索(单号、备注)
- `store_id`: 门店ID筛选
- `status`: 状态筛选(0=进行中,1=已完成,2=已取消)
- `operator_id`: 操作员ID筛选
- `start_date`: 开始日期(YYYY-MM-DD)
- `end_date`: 结束日期(YYYY-MM-DD)

### 3. 获取盘点单详情
```http
GET /api/v1/inventory-check/1
Authorization: Bearer <your_jwt_token>

GET /api/v1/inventory-check/by-no/CHK202412200001
Authorization: Bearer <your_jwt_token>
```

**权限要求**: `inventory.view`
**数据隔离**: 非管理员用户只能查询自己门店的盘点单

### 4. 更新盘点单
```http
PUT /api/v1/inventory-check/1
Authorization: Bearer <your_jwt_token>
Content-Type: application/json

{
  "remark": "更新备注信息"
}
```

**权限要求**: `inventory.update`
**数据隔离**: 非管理员用户只能更新自己门店的盘点单

### 5. 删除盘点单
```http
DELETE /api/v1/inventory-check/1
Authorization: Bearer <your_jwt_token>
```

**权限要求**: `inventory.delete`
**数据隔离**: 非管理员用户只能删除自己门店的盘点单

### 6. 更新盘点单状态
```http
PATCH /api/v1/inventory-check/1/status
Content-Type: application/json

{
  "status": 1,
  "remark": "盘点完成"
}
```

### 7. 盘点单个商品
```http
PATCH /api/v1/inventory-check/1/items/1/check?checker_id=1
Content-Type: application/json

{
  "actual_stock": 1,
  "remark": "盘点正常"
}
```

### 8. 批量盘点商品
```http
PATCH /api/v1/inventory-check/1/batch-check?checker_id=1
Content-Type: application/json

{
  "items": [
    {"item_id": 1, "actual_stock": 1, "remark": "正常"},
    {"item_id": 2, "actual_stock": 0, "remark": "缺失"}
  ]
}
```

### 9. 获取盘点统计
```http
GET /api/v1/inventory-check/statistics/summary
```

## 📈 状态说明

### 盘点单状态
- **0 - 进行中**: 盘点单已创建，正在进行盘点
- **1 - 已完成**: 盘点工作已完成
- **2 - 已取消**: 盘点工作已取消

### 盘点明细状态
- **0 - 未盘点**: 商品尚未盘点
- **1 - 已盘点**: 商品已完成盘点

## 🔒 业务规则

### 权限控制
1. **创建盘点单**: 需要有效的操作员ID
2. **修改盘点单**: 只有进行中状态的盘点单才能修改
3. **删除盘点单**: 只有进行中状态的盘点单才能删除
4. **盘点操作**: 只有进行中状态的盘点单才能进行盘点

### 数据验证
1. **门店验证**: 门店必须存在且状态正常
2. **商品验证**: 所有盘点商品必须存在于系统中
3. **操作员验证**: 操作员和盘点员必须存在且状态正常
4. **状态流转**: 严格按照业务流程进行状态变更

### 自动计算
1. **差异计算**: 自动计算实际库存与系统库存的差异
2. **进度统计**: 自动更新已盘数量和差异数量
3. **完成率**: 自动计算盘点进度百分比

## 📊 统计功能

### 基础统计
- 总盘点单数
- 进行中/已完成/已取消数量
- 总盘点商品数
- 已盘点/有差异商品数

### 分布统计
- 状态分布统计
- 门店分布统计
- 进度统计分析

### 业务指标
- 平均盘点进度
- 盘点完成率
- 差异率分析

## 🚀 使用示例

### 完整盘点流程示例
```python
# 1. 创建盘点单
response = requests.post(
    "http://localhost:8000/api/v1/inventory-check?operator_id=1",
    json={
        "store_id": 1,
        "remark": "月度盘点",
        "items": [
            {
                "jewelry_id": 1,
                "barcode": "JW001",
                "name": "黄金戒指",
                "category_id": 1,
                "system_stock": 1
            }
        ]
    }
)
check_id = response.json()["data"]["id"]

# 2. 盘点商品
requests.patch(
    f"http://localhost:8000/api/v1/inventory-check/{check_id}/items/1/check?checker_id=1",
    json={
        "actual_stock": 1,
        "remark": "盘点正常"
    }
)

# 3. 完成盘点
requests.patch(
    f"http://localhost:8000/api/v1/inventory-check/{check_id}/status",
    json={
        "status": 1,
        "remark": "盘点完成"
    }
)

# 4. 查看统计
stats = requests.get("http://localhost:8000/api/v1/inventory-check/statistics/summary")
```

## 🔧 技术特点

- **高性能**: 使用SQLAlchemy优化的数据库查询
- **数据安全**: 完善的权限控制和数据验证
- **易于集成**: RESTful API设计，便于前端集成
- **实时更新**: 盘点进度和统计信息实时更新
- **中文化**: 完全中文化的API文档和错误信息

---

**文档版本**: v1.0  
**最后更新**: 2025-01-25  
**维护团队**: 黄金珠宝管理系统开发团队
