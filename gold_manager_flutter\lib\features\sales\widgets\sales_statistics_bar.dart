import 'package:flutter/material.dart';

import '../../../core/constants/border_styles.dart';
import '../models/sales_statistics.dart';

/// 销售统计汇总栏组件
class SalesStatisticsBar extends StatelessWidget {
  final SalesStatistics statistics;
  final bool isLoading;

  const SalesStatisticsBar({
    super.key,
    required this.statistics,
    required this.isLoading,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: const BoxDecoration(
        color: Color(0xFFF5F5F5),
        border: Border(
          bottom: BorderSide(color: Color(0xFFE0E0E0), width: 1),
        ),
      ),
      child: Row(
        children: [
          // 统计图标
          Icon(
            Icons.bar_chart,
            color: const Color(0xFF1E88E5),
            size: 18,
          ),
          const SizedBox(width: 8),
          
          // 标题
          const Text(
            '统计汇总',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Color(0xFF212121),
            ),
          ),
          const SizedBox(width: 24),
          
          if (isLoading) ...[
            // 加载指示器
            const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                color: Color(0xFF1E88E5),
              ),
            ),
            const SizedBox(width: 8),
            const Text(
              '计算中...',
              style: TextStyle(
                fontSize: 13,
                color: Color(0xFF757575),
              ),
            ),
          ] else ...[
            // 商品件数统计
            _buildStatItem(
              icon: Icons.inventory_2,
              label: '件数',
              value: statistics.totalItems.toString(),
              unit: '件',
              valueColor: const Color(0xFF1E88E5),
            ),
            const SizedBox(width: 24),
            
            // 销售额统计
            _buildStatItem(
              icon: Icons.attach_money,
              label: '销售额',
              value: statistics.formattedSalesAmount,
              valueColor: Colors.green[700]!,
            ),
            const SizedBox(width: 24),
            
            // 利润统计
            _buildStatItem(
              icon: Icons.trending_up,
              label: '利润',
              value: statistics.formattedProfit,
              valueColor: statistics.totalProfit >= 0 ? Colors.green[700]! : Colors.red[700]!,
            ),
            const SizedBox(width: 24),
            
            // 利润率统计
            _buildStatItem(
              icon: Icons.percent,
              label: '利润率',
              value: statistics.formattedProfitRate,
              valueColor: statistics.profitRate >= 0 ? Colors.green[700]! : Colors.red[700]!,
            ),
            
            const Spacer(),
            
            // 类型分布统计（如果有多种类型）
            if (statistics.salesTypeCount.length > 1)
              _buildTypeDistribution(),
          ],
        ],
      ),
    );
  }

  /// 构建统计项
  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
    String? unit,
    required Color valueColor,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
        border: Border.all(color: const Color(0xFFE0E0E0), width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color: const Color(0xFF757575),
          ),
          const SizedBox(width: 6),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                label,
                style: const TextStyle(
                  fontSize: 11,
                  color: Color(0xFF757575),
                  height: 1.0,
                ),
              ),
              const SizedBox(height: 2),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    value,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: valueColor,
                      height: 1.0,
                      fontFamily: 'monospace',
                    ),
                  ),
                  if (unit != null) ...[
                    const SizedBox(width: 2),
                    Text(
                      unit,
                      style: TextStyle(
                        fontSize: 12,
                        color: valueColor,
                        height: 1.0,
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建类型分布统计
  Widget _buildTypeDistribution() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
        border: Border.all(color: const Color(0xFFE0E0E0), width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.pie_chart,
            size: 16,
            color: const Color(0xFF757575),
          ),
          const SizedBox(width: 6),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                '类型分布',
                style: TextStyle(
                  fontSize: 11,
                  color: Color(0xFF757575),
                  height: 1.0,
                ),
              ),
              const SizedBox(height: 2),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: statistics.salesTypeCount.entries.map((entry) {
                  return Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            color: _getTypeColor(entry.key),
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${_getTypeDisplayName(entry.key)}:${entry.value}',
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            height: 1.0,
                            color: Color(0xFF212121),
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 获取类型对应的颜色
  Color _getTypeColor(String type) {
    switch (type) {
      case 'retail':
        return const Color(0xFF1E88E5); // 主蓝色
      case 'wholesale':
        return Colors.green[600]!;
      case 'transfer':
        return Colors.orange[600]!;
      case 'recycling':
        return Colors.purple[600]!;
      default:
        return const Color(0xFF757575);
    }
  }

  /// 获取类型显示名称
  String _getTypeDisplayName(String type) {
    switch (type) {
      case 'retail':
        return '零售';
      case 'wholesale':
        return '批发';
      case 'transfer':
        return '调拨';
      case 'recycling':
        return '回收';
      default:
        return type;
    }
  }
}