import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/border_styles.dart';
import '../../../core/utils/logger.dart';
import '../models/inventory_check.dart';
import '../services/inventory_check_service.dart';
import '../../../models/store/store.dart';
import '../../../services/store_service.dart';
import '../../../services/jewelry_service.dart';

/// 新建盘点单对话框
/// 
/// 提供创建新盘点单的表单界面
/// 严格遵循统一边框调用方式规范
class InventoryCheckCreateDialog extends StatefulWidget {
  const InventoryCheckCreateDialog({super.key});

  @override
  State<InventoryCheckCreateDialog> createState() => _InventoryCheckCreateDialogState();
}

class _InventoryCheckCreateDialogState extends State<InventoryCheckCreateDialog> {
  final _formKey = GlobalKey<FormState>();
  final _remarkController = TextEditingController();
  final _inventoryCheckService = Get.find<InventoryCheckService>();
  final _storeService = Get.find<StoreService>();
  final _jewelryService = Get.find<JewelryService>();

  int? _selectedStoreId;
  List<Store> _stores = [];
  bool _isLoading = false;
  bool _isCreating = false;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  @override
  void dispose() {
    _remarkController.dispose();
    super.dispose();
  }

  /// 初始化数据
  void _initializeData() async {
    await _loadStores();
    _setDefaultStore();
  }

  /// 加载门店列表
  Future<void> _loadStores() async {
    try {
      setState(() => _isLoading = true);

      final stores = await _storeService.getAllStores();

      setState(() {
        _stores = stores;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      LoggerService.e('加载门店列表失败', e);
      Get.snackbar('错误', '加载门店列表失败: ${e.toString()}');
    }
  }

  /// 设置默认门店
  void _setDefaultStore() {
    // 如果用户有分配门店，自动选择该门店
    // 注意：这里需要根据实际的AuthService实现来调整
    // 暂时注释掉，避免编译错误
    // if (_authService.storeId.value != null) {
    //   _selectedStoreId = _authService.storeId.value;
    // }
  }

  /// 创建盘点单
  Future<void> _createInventoryCheck() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedStoreId == null) {
      Get.snackbar('提示', '请选择盘点门店');
      return;
    }

    try {
      setState(() => _isCreating = true);

      LoggerService.d('🔄 开始获取门店在售商品 - 门店ID: $_selectedStoreId');

      // 获取门店的在售商品（分页获取，避免超过API限制）
      List<dynamic> allJewelry = [];
      int currentPage = 1;
      const int pageSize = 100; // 后端API最大限制

      while (true) {
        final jewelryList = await _jewelryService.getJewelryList({
          'store_id': _selectedStoreId,
          'status': 1, // 只获取上架在售的商品
          'page_size': pageSize,
          'page': currentPage,
        });

        allJewelry.addAll(jewelryList.data);

        // 如果当前页数据少于pageSize，说明已经是最后一页
        if (jewelryList.data.length < pageSize) {
          break;
        }

        currentPage++;
      }

      if (allJewelry.isEmpty) {
        Get.snackbar('提示', '该门店暂无在售商品，无法创建盘点单');
        return;
      }

      LoggerService.d('✅ 获取到 ${allJewelry.length} 件在售商品');

      // 构建盘点明细项目
      final items = allJewelry.map((jewelry) {
        return InventoryCheckItemCreate(
          jewelryId: jewelry.id,
          barcode: jewelry.barcode,
          name: jewelry.name,
          categoryId: jewelry.categoryId,
          ringSize: jewelry.ringSize,
          systemStock: 1, // 默认系统库存为1
          remark: null,
        );
      }).toList();

      final request = InventoryCheckCreateRequest(
        storeId: _selectedStoreId!,
        remark: _remarkController.text.trim().isEmpty
            ? null
            : _remarkController.text.trim(),
        items: items,
      );

      LoggerService.d('🔄 开始创建盘点单 - 包含 ${items.length} 件商品');

      final inventoryCheck = await _inventoryCheckService.createInventoryCheck(request);

      Get.back(result: inventoryCheck);
      Get.snackbar('成功', '盘点单创建成功，包含 ${items.length} 件商品');

      LoggerService.d('✅ 盘点单创建成功 - 单号: ${inventoryCheck.checkNo}');
    } catch (e) {
      LoggerService.e('❌ 创建盘点单失败', e);
      Get.snackbar('错误', '创建盘点单失败: ${e.toString()}');
    } finally {
      setState(() => _isCreating = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppBorderStyles.mediumBorderRadius),
      ),
      child: Container(
        width: 480,
        decoration: AppBorderStyles.standardBoxDecoration.copyWith(
          borderRadius: BorderRadius.circular(AppBorderStyles.mediumBorderRadius),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(),
            _buildContent(),
            _buildActions(),
          ],
        ),
      ),
    );
  }

  /// 构建对话框头部
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(AppBorderStyles.mediumBorderRadius),
          topRight: Radius.circular(AppBorderStyles.mediumBorderRadius),
        ),
        border: const Border(
          bottom: AppBorderStyles.tableBorder,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.inventory,
            color: Colors.blue[600],
            size: 24,
          ),
          const SizedBox(width: 12),
          const Text(
            '新建盘点单',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const Spacer(),
          IconButton(
            onPressed: () => Get.back(),
            icon: const Icon(Icons.close, size: 20),
            style: IconButton.styleFrom(
              backgroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建对话框内容
  Widget _buildContent() {
    if (_isLoading) {
      return const Padding(
        padding: EdgeInsets.all(40),
        child: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(20),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildStoreSelector(),
            const SizedBox(height: 16),
            _buildRemarkField(),
            const SizedBox(height: 16),
            _buildInfoTip(),
          ],
        ),
      ),
    );
  }

  /// 构建门店选择器
  Widget _buildStoreSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '盘点门店 *',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          height: 48,
          decoration: AppBorderStyles.standardBoxDecoration,
          child: DropdownButtonHideUnderline(
            child: DropdownButton<int>(
              value: _selectedStoreId,
              hint: const Padding(
                padding: EdgeInsets.symmetric(horizontal: 12),
                child: Text('请选择盘点门店'),
              ),
              isExpanded: true,
              items: _stores.map((store) {
                return DropdownMenuItem<int>(
                  value: store.id,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    child: Text(store.name),
                  ),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedStoreId = value;
                });
              }, // 暂时允许所有用户选择门店
            ),
          ),
        ),
        // 权限提示暂时移除，简化界面
      ],
    );
  }

  /// 构建备注输入框
  Widget _buildRemarkField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '备注信息',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: AppBorderStyles.standardBoxDecoration,
          child: TextField(
            controller: _remarkController,
            maxLines: 3,
            decoration: const InputDecoration(
              hintText: '请输入盘点备注信息（可选）',
              border: InputBorder.none,
              enabledBorder: InputBorder.none,
              focusedBorder: InputBorder.none,
              disabledBorder: InputBorder.none,
              contentPadding: EdgeInsets.all(12),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建信息提示
  Widget _buildInfoTip() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
        border: Border.all(
          color: Colors.blue[200]!,
          width: AppBorderStyles.borderWidth,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            color: Colors.blue[600],
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              '系统将自动盘点所有状态为"上架在售"的商品',
              style: TextStyle(
                fontSize: 12,
                color: Colors.blue[700],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActions() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        border: Border(
          top: AppBorderStyles.tableBorder,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          SizedBox(
            height: 36,
            child: OutlinedButton(
              onPressed: _isCreating ? null : () => Get.back(),
              style: OutlinedButton.styleFrom(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
                ),
                side: const BorderSide(color: AppBorderStyles.borderColor),
              ),
              child: const Text('取消'),
            ),
          ),
          const SizedBox(width: 12),
          SizedBox(
            height: 36,
            child: ElevatedButton(
              onPressed: _isCreating ? null : _createInventoryCheck,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue[600],
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
                ),
              ),
              child: _isCreating
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Text('创建盘点单'),
            ),
          ),
        ],
      ),
    );
  }
}
