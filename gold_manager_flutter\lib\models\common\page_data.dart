/// 分页数据封装类
class PageData<T> {
  /// 数据列表
  final List<T> data;
  
  /// 当前页码
  final int currentPage;
  
  /// 最后一页页码
  final int lastPage;
  
  /// 总记录数
  final int total;
  
  /// 构造函数
  const PageData({
    required this.data,
    required this.currentPage,
    required this.lastPage,
    required this.total,
  });
  
  /// 工厂方法，从JSON创建
  factory PageData.fromJson(
    Map<String, dynamic> json, 
    T Function(Map<String, dynamic>) fromJson
  ) {
    return PageData<T>(
      data: (json['data'] as List)
          .map<T>((item) => fromJson(item as Map<String, dynamic>))
          .toList(),
      currentPage: json['current_page'],
      lastPage: json['last_page'],
      total: json['total'],
    );
  }
  
  /// 是否有下一页
  bool get hasNextPage => currentPage < lastPage;
  
  /// 是否有上一页
  bool get hasPrevPage => currentPage > 1;
} 