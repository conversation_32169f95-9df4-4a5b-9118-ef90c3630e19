"""
数据模型模块
基于FastAdmin数据库结构的SQLAlchemy模型
"""

from .jewelry import Jewelry, JewelryCategory, JewelryGroup, JewelryGroupItem, GroupImages
from .store import Store
from .admin import Admin, AdminLog
from .member import Member
from .stock_in import StockIn, StockInItem
from .stock_out import StockOut, StockOutItem
from .stock_return import StockReturn, StockReturnItem
from .inventory_check import InventoryCheck, InventoryCheckItem
from .recycling import Recycling, RecyclingItem
from .store_transfer import StoreTransfer, StoreTransferItem
from .recycling_process import RecyclingProcess, RecyclingProcessResult, RecyclingToStock, Material

__all__ = [
    # 商品相关
    "Jewelry",
    "JewelryCategory",
    "JewelryGroup",
    "JewelryGroupItem",
    "GroupImages",

    # 门店相关
    "Store",

    # 管理员
    "Admin",
    "AdminLog",

    # 会员
    "Member",

    # 入库管理
    "StockIn",
    "StockInItem",

    # 出库管理
    "StockOut",
    "StockOutItem",

    # 退货管理
    "StockReturn",
    "StockReturnItem",

    # 库存盘点
    "InventoryCheck",
    "InventoryCheckItem",

    # 回收管理
    "Recycling",
    "RecyclingItem",

    # 门店调拨
    "StoreTransfer",
    "StoreTransferItem",
    
    # 回收处理
    "RecyclingProcess",
    "RecyclingProcessResult",
    "RecyclingToStock",
    "Material",
]