"""
销售管理业务服务类
处理销售明细查询、统计分析等业务逻辑
"""

from typing import List, Tuple, Optional, Dict, Any
from decimal import Decimal
from datetime import datetime, date
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import func, desc, and_, or_, case

from ..models.stock_out import StockOut, StockOutItem
from ..models.store_transfer import StoreTransfer, StoreTransferItem
from ..models.recycling import Recycling, RecyclingItem
from ..models.store import Store
from ..models.admin import Admin
from ..models.jewelry import Jewelry, JewelryCategory
from ..schemas.sales import (
    SalesQueryParams,
    SalesItemDetailResponse,
    SalesStatistics,
    SalesTypeStatistics,
    SalesType,
)


class SalesService:
    """销售管理服务类"""

    def __init__(self, db: Session):
        self.db = db

    def get_sales_items(self, params: SalesQueryParams) -> <PERSON><PERSON>[List[SalesItemDetailResponse], int]:
        """获取销售商品明细列表"""
        
        print(f"=== 销售查询调试 ===")
        print(f"查询参数: sales_type={params.sales_type}, page={params.page}, page_size={params.page_size}")
        print(f"其他参数: keyword={params.keyword}, store_id={params.store_id}")
        
        # 根据销售类型确定查询策略
        if params.sales_type == SalesType.all:
            # 查询所有类型 - 使用高效的数据库级别合并
            return self._get_all_sales_items_optimized(params)
            
        elif params.sales_type == SalesType.retail:
            return self._get_stock_out_items(params, "retail")
        elif params.sales_type == SalesType.wholesale:
            return self._get_stock_out_items(params, "wholesale")
        elif params.sales_type == SalesType.transfer:
            return self._get_transfer_items(params)
        elif params.sales_type == SalesType.recycling:
            return self._get_recycling_items(params)
        else:
            return [], 0

    def _get_all_sales_items_optimized(self, params: SalesQueryParams) -> Tuple[List[SalesItemDetailResponse], int]:
        """优化的全部类型查询方法 - 简化版本"""
        
        print(f"=== 优化的全部类型查询 ===")
        
        # 直接查询所有符合条件的出库单明细，不区分类型
        query = (
            self.db.query(StockOutItem)
            .join(StockOut, StockOutItem.stock_out_id == StockOut.id)
            .join(Store, StockOut.store_id == Store.id)
            .join(Admin, StockOut.operator_id == Admin.id)
            .join(Jewelry, StockOutItem.jewelry_id == Jewelry.id)
            .outerjoin(JewelryCategory, Jewelry.category_id == JewelryCategory.id)
            .filter(
                StockOut.status == 2,  # 已通过
                StockOut.payment_status == 1,  # 已收款
            )
        )
        
        # 应用筛选条件
        query = self._apply_common_filters(query, params, StockOut, StockOutItem)
        
        # 获取总数
        total = query.count()
        print(f"全部类型查询总数: {total}")
        
        # 分页和排序
        items_data = (
            query.order_by(desc(StockOut.createtime))
            .offset((params.page - 1) * params.page_size)
            .limit(params.page_size)
            .all()
        )
        
        print(f"实际返回数据条数: {len(items_data)}")
        
        # 转换为响应格式
        items = []
        for item in items_data:
            stock_out = item.stock_out
            jewelry = item.jewelry
            category = jewelry.category if jewelry else None
            
            # 确定销售类型
            if stock_out.recycling_id > 0:
                sales_type = "recycling"
            else:
                sales_type = stock_out.sale_type
            
            # 计算利润
            cost_price = self._calculate_cost_price(item)
            profit = item.total_amount - cost_price
            profit_rate = (profit / cost_price * 100) if cost_price > 0 else 0
            
            sales_item = SalesItemDetailResponse(
                id=item.id,
                order_id=stock_out.id,
                order_no=stock_out.order_no,
                order_type="stock_out",
                sales_type=sales_type,
                jewelry_id=item.jewelry_id,
                barcode=item.barcode,
                jewelry_name=item.name,
                category_name=category.name if category else "",
                ring_size=item.ring_size,
                gold_weight=item.gold_weight,
                silver_weight=item.silver_weight,
                total_weight=item.total_weight,
                gold_price=item.gold_price,
                silver_price=item.silver_price,
                work_price=item.work_price,
                piece_work_price=item.piece_work_price,
                cost_price=cost_price,
                sale_price=item.total_amount,
                profit=profit,
                profit_rate=profit_rate,
                customer=stock_out.customer,
                store_name=stock_out.store.name,
                operator_name=stock_out.operator.nickname or stock_out.operator.username,
                sale_time=datetime.fromtimestamp(stock_out.createtime),
                remark=stock_out.remark,
            )
            items.append(sales_item)
            
        print(f"转换后响应数据条数: {len(items)}")
        return items, total

    def _get_stock_out_items(self, params: SalesQueryParams, sale_type: str) -> Tuple[List[SalesItemDetailResponse], int]:
        """获取出库单商品明细"""
        
        print(f"--- 查询{sale_type}类型出库单 ---")
        
        # 构建查询
        query = (
            self.db.query(StockOutItem)
            .join(StockOut, StockOutItem.stock_out_id == StockOut.id)
            .join(Store, StockOut.store_id == Store.id)
            .join(Admin, StockOut.operator_id == Admin.id)
            .join(Jewelry, StockOutItem.jewelry_id == Jewelry.id)
            .outerjoin(JewelryCategory, Jewelry.category_id == JewelryCategory.id)
            .filter(
                StockOut.status == 2,  # 已通过
                StockOut.payment_status == 1,  # 已收款
                StockOut.sale_type == sale_type,
            )
        )
        
        # 排除回收变现的出库单
        if sale_type in ["retail", "wholesale"]:
            query = query.filter(StockOut.recycling_id == 0)
        
        # 应用筛选条件
        query = self._apply_common_filters(query, params, StockOut, StockOutItem)
        
        # 获取总数
        total = query.count()
        print(f"查询总数: {total}")
        
        # 分页和排序
        items_data = (
            query.order_by(desc(StockOut.createtime))
            .offset((params.page - 1) * params.page_size)
            .limit(params.page_size)
            .all()
        )
        
        print(f"实际返回数据条数: {len(items_data)}")
        
        # 转换为响应格式
        items = []
        for item in items_data:
            stock_out = item.stock_out
            jewelry = item.jewelry
            category = jewelry.category if jewelry else None
            
            # 计算利润
            cost_price = self._calculate_cost_price(item)
            profit = item.total_amount - cost_price
            profit_rate = (profit / cost_price * 100) if cost_price > 0 else 0
            
            sales_item = SalesItemDetailResponse(
                id=item.id,
                order_id=stock_out.id,
                order_no=stock_out.order_no,
                order_type="stock_out",
                sales_type=sale_type,
                jewelry_id=item.jewelry_id,
                barcode=item.barcode,
                jewelry_name=item.name,
                category_name=category.name if category else "",
                ring_size=item.ring_size,
                gold_weight=item.gold_weight,
                silver_weight=item.silver_weight,
                total_weight=item.total_weight,
                gold_price=item.gold_price,
                silver_price=item.silver_price,
                work_price=item.work_price,
                piece_work_price=item.piece_work_price,
                cost_price=cost_price,
                sale_price=item.total_amount,
                profit=profit,
                profit_rate=profit_rate,
                customer=stock_out.customer,
                store_name=stock_out.store.name,
                operator_name=stock_out.operator.nickname or stock_out.operator.username,
                sale_time=datetime.fromtimestamp(stock_out.createtime),
                remark=stock_out.remark,
            )
            items.append(sales_item)
            
        print(f"转换后响应数据条数: {len(items)}")
        return items, total

    def _get_transfer_items(self, params: SalesQueryParams) -> Tuple[List[SalesItemDetailResponse], int]:
        """获取店间调拨商品明细"""
        
        # 构建查询
        query = (
            self.db.query(StoreTransferItem)
            .join(StoreTransfer, StoreTransferItem.transfer_id == StoreTransfer.id)
            .join(Store, StoreTransfer.from_store_id == Store.id)
            .join(Admin, StoreTransfer.admin_id == Admin.id)
            .join(Jewelry, StoreTransferItem.jewelry_id == Jewelry.id)
            .outerjoin(JewelryCategory, Jewelry.category_id == JewelryCategory.id)
            .filter(
                StoreTransfer.status == 1,  # 🔧 修复：已通过状态应该是1，不是2
                # 🔧 修复：暂时移除收款状态限制，因为调拨单可能不需要收款流程
                # StoreTransfer.payment_status == 1,  # 已收款
            )
        )
        
        # 应用筛选条件
        query = self._apply_transfer_filters(query, params, StoreTransfer, StoreTransferItem)
        
        # 获取总数
        total = query.count()
        
        # 分页和排序
        items_data = (
            query.order_by(desc(StoreTransfer.createtime))
            .offset((params.page - 1) * params.page_size)
            .limit(params.page_size)
            .all()
        )
        
        # 转换为响应格式
        items = []
        for item in items_data:
            transfer = item.transfer  # 🔧 修复：正确的关联属性名
            jewelry = item.jewelry
            category = jewelry.category if jewelry else None
            
            # 调拨单的利润计算（如果有调拨价格的话）
            cost_price = self._calculate_transfer_cost_price(item)
            sale_price = item.transfer_price if hasattr(item, 'transfer_price') else cost_price
            profit = sale_price - cost_price
            profit_rate = (profit / cost_price * 100) if cost_price > 0 else 0
            
            # 获取目标门店信息
            to_store = self.db.query(Store).filter(Store.id == transfer.to_store_id).first()
            
            sales_item = SalesItemDetailResponse(
                id=item.id,
                order_id=transfer.id,
                order_no=transfer.transfer_no,
                order_type="store_transfer",
                sales_type="transfer",
                jewelry_id=item.jewelry_id,
                barcode=jewelry.barcode if jewelry else "",
                jewelry_name=jewelry.name if jewelry else "",
                category_name=category.name if category else "",
                ring_size=jewelry.ring_size if jewelry else "",
                gold_weight=jewelry.gold_weight if jewelry else Decimal('0.00'),
                silver_weight=jewelry.silver_weight if jewelry else Decimal('0.00'),
                total_weight=item.total_weight,
                gold_price=item.gold_price,
                silver_price=item.silver_price,
                work_price=item.silver_work_price,  # 🔧 修复：使用正确的字段名
                piece_work_price=item.piece_work_price,
                cost_price=cost_price,
                sale_price=sale_price,
                profit=profit,
                profit_rate=profit_rate,
                customer=f"调拨至 {to_store.name if to_store else '未知门店'}",
                store_name=transfer.from_store.name if transfer.from_store else "",
                operator_name=transfer.admin.nickname or transfer.admin.username,
                sale_time=datetime.fromtimestamp(transfer.createtime),
                remark=transfer.remark,
            )
            items.append(sales_item)
            
        return items, total

    def _get_recycling_items(self, params: SalesQueryParams) -> Tuple[List[SalesItemDetailResponse], int]:
        """获取回收变现商品明细"""
        
        print(f"--- 查询回收变现数据 ---")
        
        # 查询回收变现的出库单明细
        query = (
            self.db.query(StockOutItem)
            .join(StockOut, StockOutItem.stock_out_id == StockOut.id)
            .join(Store, StockOut.store_id == Store.id)
            .join(Admin, StockOut.operator_id == Admin.id)
            .join(Jewelry, StockOutItem.jewelry_id == Jewelry.id)
            .outerjoin(JewelryCategory, Jewelry.category_id == JewelryCategory.id)
            .filter(
                StockOut.status == 2,  # 已通过
                StockOut.payment_status == 1,  # 已收款
                StockOut.recycling_id > 0,  # 关联回收单
            )
        )
        
        # 应用筛选条件
        query = self._apply_common_filters(query, params, StockOut, StockOutItem)
        
        # 获取总数
        total = query.count()
        print(f"回收变现查询总数: {total}")
        
        # 分页和排序
        items_data = (
            query.order_by(desc(StockOut.createtime))
            .offset((params.page - 1) * params.page_size)
            .limit(params.page_size)
            .all()
        )
        
        # 转换为响应格式
        items = []
        for item in items_data:
            stock_out = item.stock_out
            jewelry = item.jewelry
            category = jewelry.category if jewelry else None
            
            # 回收变现的利润计算
            cost_price = self._calculate_cost_price(item)
            profit = item.total_amount - cost_price
            profit_rate = (profit / cost_price * 100) if cost_price > 0 else 0
            
            sales_item = SalesItemDetailResponse(
                id=item.id,
                order_id=stock_out.id,
                order_no=stock_out.order_no,
                order_type="stock_out",
                sales_type="recycling",
                jewelry_id=item.jewelry_id,
                barcode=item.barcode,
                jewelry_name=item.name,
                category_name=category.name if category else "",
                ring_size=item.ring_size,
                gold_weight=item.gold_weight,
                silver_weight=item.silver_weight,
                total_weight=item.total_weight,
                gold_price=item.gold_price,
                silver_price=item.silver_price,
                work_price=item.work_price,
                piece_work_price=item.piece_work_price,
                cost_price=cost_price,
                sale_price=item.total_amount,
                profit=profit,
                profit_rate=profit_rate,
                customer=f"回收变现 ({stock_out.customer})",
                store_name=stock_out.store.name,
                operator_name=stock_out.operator.nickname or stock_out.operator.username,
                sale_time=datetime.fromtimestamp(stock_out.createtime),
                remark=f"回收变现 - {stock_out.remark}",
            )
            items.append(sales_item)
            
        return items, total

    def _apply_common_filters(self, query, params, order_table, item_table):
        """应用通用筛选条件"""
        
        # 关键词搜索
        if params.keyword:
            keyword_filter = or_(
                item_table.barcode.like(f"%{params.keyword}%"),
                item_table.name.like(f"%{params.keyword}%"),
                order_table.order_no.like(f"%{params.keyword}%"),
                order_table.customer.like(f"%{params.keyword}%"),
            )
            query = query.filter(keyword_filter)
        
        # 门店筛选
        if params.store_id:
            query = query.filter(order_table.store_id == params.store_id)
        
        # 时间范围筛选
        if params.start_date:
            start_timestamp = int(datetime.strptime(params.start_date, "%Y-%m-%d").timestamp())
            query = query.filter(order_table.createtime >= start_timestamp)
        
        if params.end_date:
            end_timestamp = int(datetime.strptime(f"{params.end_date} 23:59:59", "%Y-%m-%d %H:%M:%S").timestamp())
            query = query.filter(order_table.createtime <= end_timestamp)
        
        # 操作员筛选
        if params.operator_id:
            query = query.filter(order_table.operator_id == params.operator_id)
        
        # 客户筛选
        if params.customer:
            query = query.filter(order_table.customer.like(f"%{params.customer}%"))
        
        return query

    def _apply_transfer_filters(self, query, params, transfer_table, item_table):
        """应用调拨单特定筛选条件"""
        
        # 关键词搜索
        if params.keyword:
            keyword_filter = or_(
                item_table.barcode.like(f"%{params.keyword}%"),
                item_table.jewelry_name.like(f"%{params.keyword}%"),
                transfer_table.transfer_no.like(f"%{params.keyword}%"),
            )
            query = query.filter(keyword_filter)
        
        # 门店筛选（源门店）
        if params.store_id:
            query = query.filter(transfer_table.from_store_id == params.store_id)
        
        # 时间范围筛选
        if params.start_date:
            start_timestamp = int(datetime.strptime(params.start_date, "%Y-%m-%d").timestamp())
            query = query.filter(transfer_table.createtime >= start_timestamp)
        
        if params.end_date:
            end_timestamp = int(datetime.strptime(f"{params.end_date} 23:59:59", "%Y-%m-%d %H:%M:%S").timestamp())
            query = query.filter(transfer_table.createtime <= end_timestamp)
        
        # 操作员筛选
        if params.operator_id:
            query = query.filter(transfer_table.admin_id == params.operator_id)
        
        return query

    def _calculate_cost_price(self, item) -> Decimal:
        """计算出库单商品成本价"""
        return (
            item.gold_cost + 
            item.silver_cost + 
            item.work_price + 
            item.piece_work_price
        )

    def _calculate_transfer_cost_price(self, item) -> Decimal:
        """计算调拨单商品成本价"""
        # 调拨单的成本价计算逻辑（根据实际业务调整）
        return item.total_cost if hasattr(item, 'total_cost') else Decimal('0.00')

    def get_sales_statistics(self, sales_type=None, store_id=None, start_date=None, end_date=None) -> SalesStatistics:
        """获取销售统计信息"""

        # 构建基础筛选条件
        base_filters = []
        if store_id:
            base_filters.append(StockOut.store_id == store_id)
        if start_date:
            start_timestamp = int(datetime.strptime(start_date, "%Y-%m-%d").timestamp())
            base_filters.append(StockOut.createtime >= start_timestamp)
        if end_date:
            end_timestamp = int(datetime.strptime(f"{end_date} 23:59:59", "%Y-%m-%d %H:%M:%S").timestamp())
            base_filters.append(StockOut.createtime <= end_timestamp)

        # 🔧 修复：根据sales_type参数返回对应的统计数据
        if sales_type and sales_type != "all":
            # 如果指定了特定的销售类型，只返回该类型的统计
            if sales_type == "transfer":
                transfer_stats = self._get_transfer_statistics(base_filters, store_id, start_date, end_date)
                # 为调拨类型构建特殊的今日和本月统计
                today = date.today()
                today_start = int(datetime.combine(today, datetime.min.time()).timestamp())
                today_end = today_start + 86400
                month_start = int(datetime.combine(today.replace(day=1), datetime.min.time()).timestamp())

                # 调拨类型的今日和本月统计（简化处理）
                today_transfer_stats = self._get_transfer_period_statistics(today_start, today_end, base_filters, store_id)
                month_transfer_stats = self._get_transfer_period_statistics(month_start, None, base_filters, store_id)

                return SalesStatistics(
                    total_count=transfer_stats.total_count,
                    total_sales=transfer_stats.total_sales,
                    total_cost=transfer_stats.total_cost,
                    total_profit=transfer_stats.total_profit,
                    average_profit_rate=transfer_stats.profit_rate,
                    by_type=[transfer_stats],
                    today_count=today_transfer_stats["count"],
                    today_sales=today_transfer_stats["sales"],
                    today_profit=today_transfer_stats["profit"],
                    month_count=month_transfer_stats["count"],
                    month_sales=month_transfer_stats["sales"],
                    month_profit=month_transfer_stats["profit"],
                )
            elif sales_type in ["retail", "wholesale"]:
                type_stats = self._get_type_statistics(sales_type, base_filters)
                today = date.today()
                today_start = int(datetime.combine(today, datetime.min.time()).timestamp())
                today_end = today_start + 86400
                month_start = int(datetime.combine(today.replace(day=1), datetime.min.time()).timestamp())

                today_stats = self._get_period_statistics(today_start, today_end, base_filters + [StockOut.sale_type == sales_type])
                month_stats = self._get_period_statistics(month_start, None, base_filters + [StockOut.sale_type == sales_type])

                return SalesStatistics(
                    total_count=type_stats.total_count,
                    total_sales=type_stats.total_sales,
                    total_cost=type_stats.total_cost,
                    total_profit=type_stats.total_profit,
                    average_profit_rate=type_stats.profit_rate,
                    by_type=[type_stats],
                    today_count=today_stats["count"],
                    today_sales=today_stats["sales"],
                    today_profit=today_stats["profit"],
                    month_count=month_stats["count"],
                    month_sales=month_stats["sales"],
                    month_profit=month_stats["profit"],
                )
            elif sales_type == "recycling":
                recycling_stats = self._get_recycling_statistics(base_filters)
                today = date.today()
                today_start = int(datetime.combine(today, datetime.min.time()).timestamp())
                today_end = today_start + 86400
                month_start = int(datetime.combine(today.replace(day=1), datetime.min.time()).timestamp())

                # 回收类型的今日和本月统计（简化处理）
                today_recycling_stats = self._get_recycling_period_statistics(today_start, today_end, base_filters)
                month_recycling_stats = self._get_recycling_period_statistics(month_start, None, base_filters)

                return SalesStatistics(
                    total_count=recycling_stats.total_count,
                    total_sales=recycling_stats.total_sales,
                    total_cost=recycling_stats.total_cost,
                    total_profit=recycling_stats.total_profit,
                    average_profit_rate=recycling_stats.profit_rate,
                    by_type=[recycling_stats],
                    today_count=today_recycling_stats["count"],
                    today_sales=today_recycling_stats["sales"],
                    today_profit=today_recycling_stats["profit"],
                    month_count=month_recycling_stats["count"],
                    month_sales=month_recycling_stats["sales"],
                    month_profit=month_recycling_stats["profit"],
                )

        # 默认返回所有类型的汇总统计
        retail_stats = self._get_type_statistics("retail", base_filters)
        wholesale_stats = self._get_type_statistics("wholesale", base_filters)
        transfer_stats = self._get_transfer_statistics(base_filters, store_id, start_date, end_date)
        recycling_stats = self._get_recycling_statistics(base_filters)

        # 计算总体统计
        total_count = retail_stats.total_count + wholesale_stats.total_count + transfer_stats.total_count + recycling_stats.total_count
        total_sales = retail_stats.total_sales + wholesale_stats.total_sales + transfer_stats.total_sales + recycling_stats.total_sales
        total_cost = retail_stats.total_cost + wholesale_stats.total_cost + transfer_stats.total_cost + recycling_stats.total_cost
        total_profit = total_sales - total_cost
        avg_profit_rate = (total_profit / total_cost * 100) if total_cost > 0 else Decimal('0.00')

        # 今日统计
        today = date.today()
        today_start = int(datetime.combine(today, datetime.min.time()).timestamp())
        today_end = today_start + 86400

        today_stats = self._get_period_statistics(today_start, today_end, base_filters)

        # 本月统计
        month_start = int(datetime.combine(today.replace(day=1), datetime.min.time()).timestamp())
        month_stats = self._get_period_statistics(month_start, None, base_filters)

        return SalesStatistics(
            total_count=total_count,
            total_sales=total_sales,
            total_cost=total_cost,
            total_profit=total_profit,
            average_profit_rate=avg_profit_rate,
            by_type=[retail_stats, wholesale_stats, transfer_stats, recycling_stats],
            today_count=today_stats["count"],
            today_sales=today_stats["sales"],
            today_profit=today_stats["profit"],
            month_count=month_stats["count"],
            month_sales=month_stats["sales"],
            month_profit=month_stats["profit"],
        )

    def _get_type_statistics(self, sale_type: str, base_filters: List) -> SalesTypeStatistics:
        """获取指定类型的统计信息"""
        
        filters = base_filters + [
            StockOut.status == 2,
            StockOut.payment_status == 1,
            StockOut.sale_type == sale_type,
        ]
        
        # 排除回收变现
        if sale_type in ["retail", "wholesale"]:
            filters.append(StockOut.recycling_id == 0)
        
        # 查询统计数据
        result = (
            self.db.query(
                func.count(StockOutItem.id).label("count"),
                func.sum(StockOutItem.total_amount).label("sales"),
                func.sum(StockOutItem.total_cost).label("cost"),
            )
            .join(StockOut, StockOutItem.stock_out_id == StockOut.id)
            .filter(and_(*filters))
            .first()
        )
        
        count = result.count or 0
        sales = result.sales or Decimal('0.00')
        cost = result.cost or Decimal('0.00')
        profit = sales - cost
        profit_rate = (profit / cost * 100) if cost > 0 else Decimal('0.00')
        
        type_names = {
            "retail": "零售",
            "wholesale": "批发",
        }
        
        return SalesTypeStatistics(
            type_name=type_names.get(sale_type, sale_type),
            total_count=count,
            total_sales=sales,
            total_cost=cost,
            total_profit=profit,
            profit_rate=profit_rate,
        )

    def _get_transfer_statistics(self, base_filters: List, store_id=None, start_date=None, end_date=None) -> SalesTypeStatistics:
        """获取调拨统计信息"""

        # 构建调拨单筛选条件
        transfer_filters = [
            StoreTransfer.status == 1,  # 🔧 修复：已通过状态应该是1，不是2
            # 🔧 修复：暂时移除收款状态限制，因为调拨单可能不需要收款流程
            # StoreTransfer.payment_status == 1,
        ]

        # 🔧 修复：正确应用筛选条件
        # 门店筛选（源门店）
        if store_id:
            transfer_filters.append(StoreTransfer.from_store_id == store_id)

        # 时间范围筛选
        if start_date:
            start_timestamp = int(datetime.strptime(start_date, "%Y-%m-%d").timestamp())
            transfer_filters.append(StoreTransfer.createtime >= start_timestamp)

        if end_date:
            end_timestamp = int(datetime.strptime(f"{end_date} 23:59:59", "%Y-%m-%d %H:%M:%S").timestamp())
            transfer_filters.append(StoreTransfer.createtime <= end_timestamp)

        # 查询调拨统计（包含金额计算）
        # 🔧 修复：添加与明细API相同的JOIN条件，确保数据一致性
        result = (
            self.db.query(
                func.count(StoreTransferItem.id).label("count"),
                func.sum(StoreTransferItem.transfer_price).label("sales"),
            )
            .join(StoreTransfer, StoreTransferItem.transfer_id == StoreTransfer.id)
            .join(Jewelry, StoreTransferItem.jewelry_id == Jewelry.id)  # 🔧 添加首饰表JOIN，与明细API保持一致
            .filter(and_(*transfer_filters))
            .first()
        )

        count = result.count or 0
        sales = result.sales or Decimal('0.00')

        return SalesTypeStatistics(
            type_name="店间调拨",
            total_count=count,
            total_sales=sales,
            total_cost=Decimal('0.00'),  # 调拨单通常不计算成本
            total_profit=sales,  # 调拨单的利润等于销售额
            profit_rate=Decimal('0.00'),  # 调拨单不计算利润率
        )

    def _get_transfer_period_statistics(self, start_timestamp: int, end_timestamp: int = None, base_filters: List = None, store_id=None) -> dict:
        """获取调拨类型的时间段统计"""

        # 构建调拨单筛选条件
        transfer_filters = [
            StoreTransfer.status == 1,  # 已通过状态
        ]

        # 添加时间筛选
        if start_timestamp:
            transfer_filters.append(StoreTransfer.createtime >= start_timestamp)
        if end_timestamp:
            transfer_filters.append(StoreTransfer.createtime <= end_timestamp)

        # 添加门店筛选
        if store_id:
            transfer_filters.append(StoreTransfer.from_store_id == store_id)

        # 查询时间段内的调拨统计
        # 🔧 修复：添加与明细API相同的JOIN条件，确保数据一致性
        result = (
            self.db.query(
                func.count(StoreTransferItem.id).label("count"),
                func.sum(StoreTransferItem.transfer_price).label("sales"),
            )
            .join(StoreTransfer, StoreTransferItem.transfer_id == StoreTransfer.id)
            .join(Jewelry, StoreTransferItem.jewelry_id == Jewelry.id)  # 🔧 添加首饰表JOIN，与明细API保持一致
            .filter(and_(*transfer_filters))
            .first()
        )

        count = result.count or 0
        sales = result.sales or Decimal('0.00')

        return {
            "count": count,
            "sales": sales,
            "profit": sales,  # 调拨单的利润等于销售额
        }

    def _get_recycling_statistics(self, base_filters: List) -> SalesTypeStatistics:
        """获取回收变现统计信息"""
        
        filters = base_filters + [
            StockOut.status == 2,
            StockOut.payment_status == 1,
            StockOut.recycling_id > 0,
        ]
        
        # 查询统计数据
        result = (
            self.db.query(
                func.count(StockOutItem.id).label("count"),
                func.sum(StockOutItem.total_amount).label("sales"),
                func.sum(StockOutItem.total_cost).label("cost"),
            )
            .join(StockOut, StockOutItem.stock_out_id == StockOut.id)
            .filter(and_(*filters))
            .first()
        )
        
        count = result.count or 0
        sales = result.sales or Decimal('0.00')
        cost = result.cost or Decimal('0.00')
        profit = sales - cost
        profit_rate = (profit / cost * 100) if cost > 0 else Decimal('0.00')
        
        return SalesTypeStatistics(
            type_name="回收变现",
            total_count=count,
            total_sales=sales,
            total_cost=cost,
            total_profit=profit,
            profit_rate=profit_rate,
        )

    def _get_recycling_period_statistics(self, start_timestamp: int, end_timestamp: int = None, base_filters: List = None) -> dict:
        """获取回收变现类型的时间段统计"""

        # 构建回收单筛选条件
        recycling_filters = [
            StockOut.status == 2,
            StockOut.payment_status == 1,
            StockOut.recycling_id > 0,
        ]

        # 添加时间筛选
        if start_timestamp:
            recycling_filters.append(StockOut.createtime >= start_timestamp)
        if end_timestamp:
            recycling_filters.append(StockOut.createtime <= end_timestamp)

        # 查询时间段内的回收统计
        result = (
            self.db.query(
                func.count(StockOutItem.id).label("count"),
                func.sum(StockOutItem.total_amount).label("sales"),
                func.sum(StockOutItem.total_cost).label("cost"),
            )
            .join(StockOut, StockOutItem.stock_out_id == StockOut.id)
            .filter(and_(*recycling_filters))
            .first()
        )

        count = result.count or 0
        sales = result.sales or Decimal('0.00')
        cost = result.cost or Decimal('0.00')
        profit = sales - cost

        return {
            "count": count,
            "sales": sales,
            "profit": profit,
        }

    def _get_period_statistics(self, start_time: int, end_time: Optional[int], base_filters: List) -> Dict[str, Any]:
        """获取时间段统计"""
        
        filters = base_filters + [
            StockOut.status == 2,
            StockOut.payment_status == 1,
            StockOut.createtime >= start_time,
        ]
        
        if end_time:
            filters.append(StockOut.createtime <= end_time)
        
        # 查询统计数据
        result = (
            self.db.query(
                func.count(StockOutItem.id).label("count"),
                func.sum(StockOutItem.total_amount).label("sales"),
                func.sum(StockOutItem.total_cost).label("cost"),
            )
            .join(StockOut, StockOutItem.stock_out_id == StockOut.id)
            .filter(and_(*filters))
            .first()
        )
        
        count = result.count or 0
        sales = result.sales or Decimal('0.00')
        cost = result.cost or Decimal('0.00')
        profit = sales - cost
        
        return {
            "count": count,
            "sales": sales,
            "profit": profit,
        }

    def get_order_items(self, order_id: int, order_type: str) -> List[SalesItemDetailResponse]:
        """获取指定单据的商品明细"""
        
        if order_type == "stock_out":
            # 创建临时参数对象
            temp_params = SalesQueryParams(page_size=1000)  # 大数量获取所有明细
            
            # 查询出库单明细
            query = (
                self.db.query(StockOutItem)
                .join(StockOut, StockOutItem.stock_out_id == StockOut.id)
                .filter(StockOut.id == order_id)
            )
            
            items_data = query.all()
            items = []
            
            for item in items_data:
                stock_out = item.stock_out
                
                # 确定销售类型
                if stock_out.recycling_id > 0:
                    sales_type = "recycling"
                else:
                    sales_type = stock_out.sale_type
                
                cost_price = self._calculate_cost_price(item)
                profit = item.total_amount - cost_price
                profit_rate = (profit / cost_price * 100) if cost_price > 0 else 0
                
                sales_item = SalesItemDetailResponse(
                    id=item.id,
                    order_id=stock_out.id,
                    order_no=stock_out.order_no,
                    order_type="stock_out",
                    sales_type=sales_type,
                    jewelry_id=item.jewelry_id,
                    barcode=item.barcode,
                    jewelry_name=item.name,
                    category_name="",  # 需要关联查询
                    ring_size=item.ring_size,
                    gold_weight=item.gold_weight,
                    silver_weight=item.silver_weight,
                    total_weight=item.total_weight,
                    gold_price=item.gold_price,
                    silver_price=item.silver_price,
                    work_price=item.work_price,
                    piece_work_price=item.piece_work_price,
                    cost_price=cost_price,
                    sale_price=item.total_amount,
                    profit=profit,
                    profit_rate=profit_rate,
                    customer=stock_out.customer,
                    store_name=stock_out.store.name if stock_out.store else "",
                    operator_name=stock_out.operator.nickname or stock_out.operator.username if stock_out.operator else "",
                    sale_time=datetime.fromtimestamp(stock_out.createtime),
                    remark=stock_out.remark,
                )
                items.append(sales_item)
            
            return items
        
        elif order_type == "store_transfer":
            # 查询调拨单明细（类似逻辑）
            # TODO: 实现调拨单明细查询
            return []
        
        else:
            return []