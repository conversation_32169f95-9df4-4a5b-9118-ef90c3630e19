import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../core/theme/app_colors.dart';
import '../../../widgets/empty_state_widget.dart';
import '../controllers/reports_controller.dart';
import '../models/report_model.dart';

/// 库存报表页面
class InventoryReportPage extends StatelessWidget {
  const InventoryReportPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<ReportsController>();
    
    return Obx(() {
      final report = controller.inventoryReport.value;
      
      // 检查是否有数据
      if (report.items.isEmpty && report.categoryData.isEmpty) {
        return const EmptyStateWidget(
          icon: Icons.inventory_2,
          title: '暂无库存数据',
          message: '所选筛选条件下没有库存记录',
        );
      }
      
      return SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSummaryCards(report),
            const SizedBox(height: 24),
            _buildCategoryDistribution(report.categoryData),
            const SizedBox(height: 24),
            _buildInventoryTable(report.items),
          ],
        ),
      );
    });
  }
  
  /// 构建概览卡片
  Widget _buildSummaryCards(InventoryReportData report) {
    return Wrap(
      spacing: 16,
      runSpacing: 16,
      children: [
        _buildSummaryCard(
          title: '库存总数',
          value: '${NumberFormat('#,##0').format(report.totalCount)}件',
          icon: Icons.inventory,
          color: AppColors.primary,
        ),
        _buildSummaryCard(
          title: '库存总价值',
          value: '¥${NumberFormat('#,##0.00').format(report.totalValue)}',
          icon: Icons.attach_money,
          color: Colors.green,
        ),
        _buildSummaryCard(
          title: '总金重',
          value: '${report.totalGoldWeight.toStringAsFixed(2)}g',
          icon: Icons.monetization_on,
          color: Colors.amber,
        ),
        _buildSummaryCard(
          title: '总银重',
          value: '${report.totalSilverWeight.toStringAsFixed(2)}g',
          icon: Icons.monetization_on,
          color: Colors.blueGrey,
        ),
      ],
    );
  }
  
  /// 构建单个概览卡片
  Widget _buildSummaryCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return SizedBox(
      width: 300,
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(icon, color: color, size: 24),
                  const SizedBox(width: 8),
                  Text(title, style: const TextStyle(
                    fontSize: 16, 
                    fontWeight: FontWeight.w500,
                  )),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                value,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  /// 构建分类分布
  Widget _buildCategoryDistribution(List<CategoryData> categoryData) {
    if (categoryData.isEmpty) {
      return const SizedBox.shrink();
    }
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '库存分类分布',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 24),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: categoryData.length,
              separatorBuilder: (context, index) => const Divider(),
              itemBuilder: (context, index) {
                final category = categoryData[index];
                final color = _getCategoryColor(category.name, index);
                
                return ListTile(
                  leading: Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: color,
                      shape: BoxShape.circle,
                    ),
                  ),
                  title: Text(category.name),
                  subtitle: LinearProgressIndicator(
                    value: category.percentage / 100,
                    backgroundColor: Colors.grey.withOpacity(0.2),
                    valueColor: AlwaysStoppedAnimation<Color>(color),
                  ),
                  trailing: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        '¥${NumberFormat('#,##0.00').format(category.value)}',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '${category.percentage.toStringAsFixed(1)}%',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
  
  /// 根据分类获取颜色
  Color _getCategoryColor(String category, int index) {
    final colors = [
      AppColors.primary,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.teal,
    ];
    
    if (category == '黄金首饰') {
      return Colors.amber;
    } else if (category == '银饰') {
      return Colors.grey;
    } else if (category == '金银混合') {
      return Colors.blueGrey;
    } else {
      return colors[index % colors.length];
    }
  }
  
  /// 构建库存明细表格
  Widget _buildInventoryTable(List<InventoryReportItem> items) {
    if (items.isEmpty) {
      return const SizedBox.shrink();
    }
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '库存明细',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                headingTextStyle: const TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
                dataTextStyle: const TextStyle(
                  color: Colors.black87,
                ),
                columnSpacing: 24,
                columns: const [
                  DataColumn(label: Text('条码')),
                  DataColumn(label: Text('商品名称')),
                  DataColumn(label: Text('分类')),
                  DataColumn(label: Text('金重(g)')),
                  DataColumn(label: Text('银重(g)')),
                  DataColumn(label: Text('总重量(g)')),
                  DataColumn(label: Text('成本价(元)')),
                  DataColumn(label: Text('销售价(元)')),
                  DataColumn(label: Text('数量')),
                  DataColumn(label: Text('总价值(元)')),
                  DataColumn(label: Text('门店')),
                ],
                rows: items.map((item) {
                  return DataRow(
                    cells: [
                      DataCell(Text(item.barcode)),
                      DataCell(Text(item.name)),
                      DataCell(Text(item.categoryName)),
                      DataCell(Text(item.goldWeight.toStringAsFixed(2))),
                      DataCell(Text(item.silverWeight.toStringAsFixed(2))),
                      DataCell(Text(item.totalWeight.toStringAsFixed(2))),
                      DataCell(Text('¥${item.costPrice.toStringAsFixed(2)}')),
                      DataCell(Text('¥${item.salePrice.toStringAsFixed(2)}')),
                      DataCell(Text('${item.quantity}')),
                      DataCell(Text('¥${item.totalValue.toStringAsFixed(2)}')),
                      DataCell(Text(item.storeName)),
                    ],
                  );
                }).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }
} 