import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../config/app_config.dart';
import '../utils/logger_service.dart';

/// 存储服务
/// 封装本地存储操作
class StorageService extends GetxService {
  late SharedPreferences _prefs;

  /// 手动设置SharedPreferences实例
  void setPrefs(SharedPreferences prefs) {
    _prefs = prefs;
    LoggerService.i('StorageService prefs set manually');
  }

  @override
  Future<void> onInit() async {
    super.onInit();
    // 注意：如果已经通过setPrefs设置，这里就不需要再次初始化
    if (!_prefsInitialized()) {
      try {
        _prefs = await SharedPreferences.getInstance();
        LoggerService.i('StorageService initialized via onInit');
      } catch (e) {
        LoggerService.e('StorageService initialization failed', e);
      }
    }
  }

  /// 检查_prefs是否已初始化
  bool _prefsInitialized() {
    try {
      // 尝试一个简单操作来检查_prefs是否已初始化
      _prefs.getKeys();
      return true;
    } catch (e) {
      return false;
    }
  }

  /// 检查键是否存在
  bool hasKey(String key) => _prefs.containsKey(key);

  // String操作
  Future<bool> setString(String key, String value) => _prefs.setString(key, value);
  String? getString(String key) => _prefs.getString(key);

  // int操作
  Future<bool> setInt(String key, int value) => _prefs.setInt(key, value);
  int? getInt(String key) => _prefs.getInt(key);

  // double操作
  Future<bool> setDouble(String key, double value) => _prefs.setDouble(key, value);
  double? getDouble(String key) => _prefs.getDouble(key);

  // bool操作
  Future<bool> setBool(String key, bool value) => _prefs.setBool(key, value);
  bool? getBool(String key) => _prefs.getBool(key);

  // List<String>操作
  Future<bool> setStringList(String key, List<String> value) => _prefs.setStringList(key, value);
  List<String>? getStringList(String key) => _prefs.getStringList(key);

  // 删除操作
  Future<bool> remove(String key) => _prefs.remove(key);

  // 清除所有数据
  Future<bool> clear() => _prefs.clear();

  // 应用特定操作

  /// 获取访问令牌
  String? getToken() => getString('${AppConfig.storagePrefix}${AppConfig.tokenKey}');

  /// 保存访问令牌
  Future<bool> saveToken(String token) => setString('${AppConfig.storagePrefix}${AppConfig.tokenKey}', token);

  /// 获取刷新令牌
  String? getRefreshToken() => getString('${AppConfig.storagePrefix}${AppConfig.refreshTokenKey}');

  /// 保存刷新令牌
  Future<bool> saveRefreshToken(String token) => setString('${AppConfig.storagePrefix}${AppConfig.refreshTokenKey}', token);

  /// 清除所有令牌
  Future<void> clearTokens() async {
    await remove('${AppConfig.storagePrefix}${AppConfig.tokenKey}');
    await remove('${AppConfig.storagePrefix}${AppConfig.refreshTokenKey}');
    LoggerService.i('All tokens cleared');
  }

  /// 删除认证令牌（保持向后兼容）
  Future<bool> removeToken() => remove('${AppConfig.storagePrefix}${AppConfig.tokenKey}');

  /// 获取用户ID
  int? getUserId() => getInt('${AppConfig.storagePrefix}${AppConfig.userIdKey}');

  /// 保存用户ID
  Future<bool> setUserId(int userId) => setInt('${AppConfig.storagePrefix}${AppConfig.userIdKey}', userId);

  /// 获取用户名
  String? getUserName() => getString('${AppConfig.storagePrefix}${AppConfig.userNameKey}');

  /// 保存用户名
  Future<bool> setUserName(String userName) => setString('${AppConfig.storagePrefix}${AppConfig.userNameKey}', userName);

  /// 获取用户昵称
  String? getUserNickname() => getString('${AppConfig.storagePrefix}${AppConfig.userNicknameKey}');

  /// 保存用户昵称
  Future<bool> setUserNickname(String nickname) => setString('${AppConfig.storagePrefix}${AppConfig.userNicknameKey}', nickname);

  /// 获取用户邮箱
  String? getUserEmail() => getString('${AppConfig.storagePrefix}${AppConfig.userEmailKey}');

  /// 保存用户邮箱
  Future<bool> setUserEmail(String email) => setString('${AppConfig.storagePrefix}${AppConfig.userEmailKey}', email);

  /// 获取用户角色
  String? getUserRole() => getString('${AppConfig.storagePrefix}${AppConfig.userRoleKey}');

  /// 保存用户角色
  Future<bool> setUserRole(String userRole) => setString('${AppConfig.storagePrefix}${AppConfig.userRoleKey}', userRole);

  /// 获取门店ID
  int? getStoreId() => getInt('${AppConfig.storagePrefix}${AppConfig.storeIdKey}');

  /// 保存门店ID
  Future<bool> setStoreId(int storeId) => setInt('${AppConfig.storagePrefix}${AppConfig.storeIdKey}', storeId);

  /// 获取门店名称
  String? getStoreName() => getString('${AppConfig.storagePrefix}${AppConfig.storeNameKey}');

  /// 保存门店名称
  Future<bool> setStoreName(String storeName) => setString('${AppConfig.storagePrefix}${AppConfig.storeNameKey}', storeName);

  /// 获取记住我设置
  bool getRememberMe() => getBool('${AppConfig.storagePrefix}${AppConfig.rememberMeKey}') ?? false;

  /// 保存记住我设置
  Future<bool> setRememberMe(bool rememberMe) => setBool('${AppConfig.storagePrefix}${AppConfig.rememberMeKey}', rememberMe);

  /// 获取主题设置
  String? getThemeMode() => getString('${AppConfig.storagePrefix}${AppConfig.themeKey}');

  /// 保存主题设置
  Future<bool> setThemeMode(String themeMode) => setString('${AppConfig.storagePrefix}${AppConfig.themeKey}', themeMode);

  /// 获取语言设置
  String? getLanguage() => getString('${AppConfig.storagePrefix}${AppConfig.languageKey}');

  /// 保存语言设置
  Future<bool> setLanguage(String language) => setString('${AppConfig.storagePrefix}${AppConfig.languageKey}', language);

  /// 🔧 新增：获取用户权限列表
  List<String>? getUserPermissions() => getStringList('${AppConfig.storagePrefix}user_permissions');

  /// 🔧 新增：保存用户权限列表
  Future<bool> setUserPermissions(List<String> permissions) => setStringList('${AppConfig.storagePrefix}user_permissions', permissions);

  /// 清除所有用户数据
  Future<void> clearUserData() async {
    await clearTokens();
    await remove('${AppConfig.storagePrefix}${AppConfig.userIdKey}');
    await remove('${AppConfig.storagePrefix}${AppConfig.userNameKey}');
    await remove('${AppConfig.storagePrefix}${AppConfig.userNicknameKey}');
    await remove('${AppConfig.storagePrefix}${AppConfig.userEmailKey}');
    await remove('${AppConfig.storagePrefix}${AppConfig.userRoleKey}');
    await remove('${AppConfig.storagePrefix}${AppConfig.storeIdKey}');
    await remove('${AppConfig.storagePrefix}${AppConfig.storeNameKey}');
    // 🔧 新增：清除权限信息
    await remove('${AppConfig.storagePrefix}user_permissions');
    LoggerService.i('All user data cleared (including permissions)');
  }
}