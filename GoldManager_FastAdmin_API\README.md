# 黄金珠宝管理系统 API (基于FastAdmin数据库)

## 📖 项目简介

本项目是基于FastAdmin现有数据库结构的现代化API接口，采用Python FastAPI框架开发，完全兼容现有的FastAdmin数据，为移动端和现代前端提供RESTful API服务。

## 🎯 设计目标

- ✅ **完全兼容** - 与FastAdmin数据库100%兼容
- ✅ **数据无损** - 保留所有现有客户数据
- ✅ **渐进升级** - 可以逐步替换前端界面
- ✅ **现代架构** - 采用FastAPI + SQLAlchemy + MySQL

## 🗄️ 数据库模块

### 商品管理
- `fa_jewelry` - 商品主表
- `fa_jewelry_category` - 商品分类
- `fa_jewelry_group` - 商品分组
- `fa_jewelry_group_item` - 分组明细

### 库存管理
- `fa_stock_in` / `fa_stock_in_item` - 入库管理
- `fa_stock_out` / `fa_stock_out_item` - 出库管理
- `fa_stock_return` / `fa_stock_return_item` - 退货管理
- `fa_inventory_check` / `fa_inventory_check_item` - 库存盘点

### 门店管理
- `fa_store` - 门店信息
- `fa_store_transfer` / `fa_store_transfer_item` - 店间调拨

### 回收处理
- `fa_recycling` / `fa_recycling_item` - 旧料回收
- `fa_recycling_process` - 处理记录
- `fa_metal_separation` - 金银分离
- `fa_repair_record` - 维修记录

### 会员管理
- `fa_member` - 会员信息

### 系统管理
- `fa_admin` - 管理员
- `fa_auth_group` / `fa_auth_rule` - 权限管理
- `fa_config` - 系统配置

## 🚀 技术栈

- **后端框架**: FastAPI
- **数据库ORM**: SQLAlchemy
- **数据库**: MySQL
- **数据验证**: Pydantic
- **API文档**: Swagger/OpenAPI
- **认证**: JWT Token

## 📁 项目结构

```
GoldManager_FastAdmin_API/
├── app/
│   ├── models/          # SQLAlchemy数据模型
│   ├── schemas/         # Pydantic验证模型
│   ├── api/            # API路由
│   ├── services/       # 业务逻辑层
│   ├── core/           # 核心配置
│   └── utils/          # 工具函数
├── requirements.txt    # 依赖包
└── main.py            # 应用入口
```

## 📋 开发进度

### ✅ 已完成功能
- [x] **项目初始化** - 完整的FastAPI项目架构
- [x] **数据模型定义** - 商品、门店、管理员、会员等模型
- [x] **商品管理API** - 完整的商品CRUD操作
  - [x] 获取商品分类列表
  - [x] 获取商品列表（分页、筛选、搜索）
  - [x] 根据ID/条码获取商品详情
  - [x] 创建新商品
  - [x] 更新商品信息
  - [x] 删除商品
  - [x] 更新商品状态
  - [x] 计算商品成本明细
- [x] **数据验证** - Pydantic模型和自动校验
- [x] **业务逻辑** - 商品服务和成本计算
- [x] **系统功能** - 健康检查、系统信息
- [x] **文档完善** - 详细的使用指南和API文档

### 🔄 开发中功能
- [ ] 认证和权限系统
- [ ] 门店管理API
- [ ] 库存管理API
- [ ] 会员管理API

### 📅 待开发功能
- [ ] 回收处理API
- [ ] 盘点管理API
- [ ] 调拨管理API
- [ ] 报表统计API
- [ ] 数据导入导出
- [ ] 移动端优化

## 🔧 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 配置数据库
复制 `env_config_example.txt` 为 `.env` 并修改数据库配置：
```bash
cp env_config_example.txt .env
```

### 3. 启动服务
```bash
python main.py
```

### 4. 访问API文档
- Swagger文档: http://localhost:8000/docs
- ReDoc文档: http://localhost:8000/redoc

## 📊 API示例

### 商品管理API
```bash
# 获取商品列表
GET /api/v1/jewelry?page=1&page_size=20&category_id=1&keyword=戒指

# 根据条码查询商品
GET /api/v1/jewelry/barcode/ABC123456

# 创建新商品
POST /api/v1/jewelry
Content-Type: application/json
{
    "barcode": "ABC123456",
    "name": "黄金戒指",
    "category_id": 1,
    "ring_size": "15",
    "gold_weight": 5.2,
    "gold_price": 480.00,
    "store_id": 1,
    "status": 1
}

# 计算成本明细
GET /api/v1/jewelry/1/cost-calculation
```

## 🔗 数据库兼容性

本API完全兼容FastAdmin数据库：
- ✅ 表名保持一致 (`fa_*`)
- ✅ 字段名称和类型一致
- ✅ 时间戳格式兼容
- ✅ 数据完整性保证

## 📝 文档

- [快速开始指南](快速开始指南.md) - 详细的安装和使用说明
- [项目进度报告](项目进度报告.md) - 开发进度和技术细节
- API文档: http://localhost:8000/docs (启动服务后访问)

## 🎉 项目特色

1. **零风险升级** - 与FastAdmin完全兼容，不影响现有业务
2. **现代化架构** - FastAPI高性能异步框架
3. **自动文档** - Swagger自动生成API文档
4. **智能验证** - 自动数据校验和成本计算
5. **详细日志** - 完整的操作和错误日志

## 👥 技术支持

如有问题请查看：
- 使用指南: [快速开始指南.md](快速开始指南.md)
- API文档: http://localhost:8000/docs
- 日志文件: `logs/api.log`

---

**基于FastAdmin数据库的现代化API解决方案，为您的珠宝管理业务提供技术升级支持！** 🚀 