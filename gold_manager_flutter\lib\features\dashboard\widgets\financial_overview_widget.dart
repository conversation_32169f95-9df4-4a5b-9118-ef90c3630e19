import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../core/theme/app_theme.dart';
import 'metric_card.dart';
import 'chart_card.dart';
import 'chart_theme.dart';
import 'stat_item.dart';
import '../../../widgets/responsive_builder.dart';

/// 财务分析概览组件
/// 展示财务相关的核心指标和趋势分析
class FinancialOverviewWidget extends StatelessWidget {
  /// 财务数据
  final FinancialOverviewData data;
  
  /// 是否正在加载
  final bool isLoading;
  
  /// 刷新回调
  final VoidCallback? onRefresh;

  const FinancialOverviewWidget({
    super.key,
    required this.data,
    this.isLoading = false,
    this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, sizingInformation) {
        if (sizingInformation.isMobile) {
          return _buildMobileLayout();
        } else if (sizingInformation.isTablet) {
          return _buildTabletLayout();
        } else {
          return _buildDesktopLayout();
        }
      },
    );
  }

  /// 桌面端布局
  Widget _buildDesktopLayout() {
    return Column(
      children: [
        // 核心指标卡片 - 4列布局
        Row(
          children: [
            Expanded(child: _buildTotalRevenueCard()),
            const SizedBox(width: 16),
            Expanded(child: _buildTotalCostCard()),
            const SizedBox(width: 16),
            Expanded(child: _buildNetProfitCard()),
            const SizedBox(width: 16),
            Expanded(child: _buildProfitMarginCard()),
          ],
        ),
        const SizedBox(height: 24),
        
        // 图表区域 - 2列布局
        Row(
          children: [
            Expanded(child: _buildProfitTrendChart()),
            const SizedBox(width: 16),
            Expanded(child: _buildCashFlowChart()),
          ],
        ),
        const SizedBox(height: 16),
        
        // 详细统计区域
        _buildFinancialStatsCard(),
      ],
    );
  }

  /// 平板端布局
  Widget _buildTabletLayout() {
    return Column(
      children: [
        // 核心指标卡片 - 2行2列布局
        Row(
          children: [
            Expanded(child: _buildTotalRevenueCard()),
            const SizedBox(width: 16),
            Expanded(child: _buildTotalCostCard()),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(child: _buildNetProfitCard()),
            const SizedBox(width: 16),
            Expanded(child: _buildProfitMarginCard()),
          ],
        ),
        const SizedBox(height: 24),
        
        // 图表区域 - 垂直布局
        _buildProfitTrendChart(),
        const SizedBox(height: 16),
        _buildCashFlowChart(),
        const SizedBox(height: 16),
        _buildFinancialStatsCard(),
      ],
    );
  }

  /// 移动端布局
  Widget _buildMobileLayout() {
    return Column(
      children: [
        // 核心指标卡片 - 垂直布局
        _buildTotalRevenueCard(),
        const SizedBox(height: 12),
        _buildTotalCostCard(),
        const SizedBox(height: 12),
        _buildNetProfitCard(),
        const SizedBox(height: 12),
        _buildProfitMarginCard(),
        const SizedBox(height: 20),
        
        // 图表区域
        _buildProfitTrendChart(),
        const SizedBox(height: 16),
        _buildCashFlowChart(),
        const SizedBox(height: 16),
        _buildFinancialStatsCard(),
      ],
    );
  }

  /// 总收入卡片
  Widget _buildTotalRevenueCard() {
    return DashboardMetricCard(
      title: '总收入',
      value: data.totalRevenue.toStringAsFixed(0),
      unit: '元',
      icon: Icons.trending_up,
      iconColor: AppTheme.successColor,
      valueColor: AppTheme.successColor,
      trendValue: data.revenueTrend,
      trendDescription: '较上月',
      isLoading: isLoading,
      onTap: () {
        // TODO: 跳转到收入详情
      },
    );
  }

  /// 总成本卡片
  Widget _buildTotalCostCard() {
    return DashboardMetricCard(
      title: '总成本',
      value: data.totalCost.toStringAsFixed(0),
      unit: '元',
      icon: Icons.trending_down,
      iconColor: AppTheme.errorColor,
      valueColor: AppTheme.errorColor,
      trendValue: data.costTrend,
      trendDescription: '较上月',
      isLoading: isLoading,
      onTap: () {
        // TODO: 跳转到成本详情
      },
    );
  }

  /// 净利润卡片
  Widget _buildNetProfitCard() {
    return DashboardMetricCard(
      title: '净利润',
      value: data.netProfit.toStringAsFixed(0),
      unit: '元',
      icon: Icons.account_balance,
      iconColor: AppTheme.primaryColor,
      valueColor: AppTheme.primaryColor,
      trendValue: data.profitTrend,
      trendDescription: '较上月',
      isLoading: isLoading,
      onTap: () {
        // TODO: 跳转到利润详情
      },
    );
  }

  /// 利润率卡片
  Widget _buildProfitMarginCard() {
    return DashboardMetricCard(
      title: '利润率',
      value: data.profitMargin.toStringAsFixed(1),
      unit: '%',
      icon: Icons.percent,
      iconColor: AppTheme.infoColor,
      valueColor: AppTheme.infoColor,
      trendValue: data.marginTrend,
      trendDescription: '较上月',
      isLoading: isLoading,
      onTap: () {
        // TODO: 跳转到利润率分析
      },
    );
  }

  /// 利润趋势图表
  Widget _buildProfitTrendChart() {
    return DashboardChartCard(
      title: '利润趋势',
      subtitle: '最近12个月利润变化',
      icon: Icons.show_chart,
      height: 300,
      isLoading: isLoading,
      showMoreButton: true,
      onMorePressed: () {
        // TODO: 跳转到详细趋势分析
      },
      chart: isLoading ? const SizedBox.shrink() : _buildProfitLineChart(),
    );
  }

  /// 资金流向图表
  Widget _buildCashFlowChart() {
    return DashboardChartCard(
      title: '资金流向',
      subtitle: '收入与支出构成',
      icon: Icons.donut_small,
      height: 300,
      isLoading: isLoading,
      showMoreButton: true,
      onMorePressed: () {
        // TODO: 跳转到资金流向详情
      },
      chart: isLoading ? const SizedBox.shrink() : _buildCashFlowPieChart(),
    );
  }

  /// 财务统计卡片
  Widget _buildFinancialStatsCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: AppTheme.lightTheme.cardTheme.elevation != null
          ? BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            )
          : null,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          const Row(
            children: [
              Icon(Icons.analytics, size: 20, color: AppTheme.primaryColor),
              SizedBox(width: 8),
              Text(
                '财务统计',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // 统计项网格
          isLoading 
              ? const Center(
                  child: CircularProgressIndicator(
                    color: AppTheme.primaryColor,
                  ),
                )
              : _buildStatsGrid(),
        ],
      ),
    );
  }

  /// 构建利润趋势折线图
  Widget _buildProfitLineChart() {
    final spots = data.profitTrendData.asMap().entries.map((entry) {
      return FlSpot(entry.key.toDouble(), entry.value);
    }).toList();

    return LineChart(
      DashboardChartTheme.getDefaultLineChartData(
        spots: spots,
        lineColor: AppTheme.primaryColor,
        showDots: true,
        showGrid: true,
      ),
    );
  }

  /// 构建资金流向饼图
  Widget _buildCashFlowPieChart() {
    final sections = [
      DashboardChartTheme.createPieSection(
        value: data.salesRevenue,
        title: '销售收入',
        color: AppTheme.successColor,
        radius: 60,
      ),
      DashboardChartTheme.createPieSection(
        value: data.recyclingRevenue,
        title: '回收收入',
        color: AppTheme.infoColor,
        radius: 60,
      ),
      DashboardChartTheme.createPieSection(
        value: data.operatingCost,
        title: '运营成本',
        color: AppTheme.warningColor,
        radius: 60,
      ),
      DashboardChartTheme.createPieSection(
        value: data.materialCost,
        title: '材料成本',
        color: AppTheme.errorColor,
        radius: 60,
      ),
    ];

    return PieChart(
      DashboardChartTheme.getDefaultPieChartData(
        sections: sections,
        centerSpaceRadius: 40,
      ),
    );
  }

  /// 构建统计项网格
  Widget _buildStatsGrid() {
    final stats = [
      StatItemData(
        label: '应收账款',
        value: '¥${data.accountsReceivable.toStringAsFixed(0)}',
        icon: Icons.receipt,
        color: AppTheme.infoColor,
      ),
      StatItemData(
        label: '应付账款',
        value: '¥${data.accountsPayable.toStringAsFixed(0)}',
        icon: Icons.payment,
        color: AppTheme.warningColor,
      ),
      StatItemData(
        label: '现金流',
        value: '¥${data.cashFlow.toStringAsFixed(0)}',
        icon: Icons.account_balance_wallet,
        color: AppTheme.successColor,
      ),
      StatItemData(
        label: '资产总值',
        value: '¥${data.totalAssets.toStringAsFixed(0)}',
        icon: Icons.business,
        color: AppTheme.primaryColor,
      ),
      StatItemData(
        label: '负债总额',
        value: '¥${data.totalLiabilities.toStringAsFixed(0)}',
        icon: Icons.credit_card,
        color: AppTheme.errorColor,
      ),
      StatItemData(
        label: '净资产',
        value: '¥${data.netAssets.toStringAsFixed(0)}',
        icon: Icons.savings,
        color: AppTheme.successColor,
      ),
    ];

    return StatItemWrapper(
      items: stats,
      style: StatItemStyle.compact,
      wrap: true,
      padding: EdgeInsets.zero,
    );
  }
}

/// 财务概览数据模型
class FinancialOverviewData {
  /// 总收入
  final double totalRevenue;
  
  /// 收入趋势(百分比)
  final double revenueTrend;
  
  /// 总成本
  final double totalCost;
  
  /// 成本趋势(百分比)
  final double costTrend;
  
  /// 净利润
  final double netProfit;
  
  /// 利润趋势(百分比)
  final double profitTrend;
  
  /// 利润率(百分比)
  final double profitMargin;
  
  /// 利润率趋势(百分比)
  final double marginTrend;
  
  /// 利润趋势数据(12个月)
  final List<double> profitTrendData;
  
  /// 销售收入占比
  final double salesRevenue;
  
  /// 回收收入占比
  final double recyclingRevenue;
  
  /// 运营成本占比
  final double operatingCost;
  
  /// 材料成本占比
  final double materialCost;
  
  /// 应收账款
  final double accountsReceivable;
  
  /// 应付账款
  final double accountsPayable;
  
  /// 现金流
  final double cashFlow;
  
  /// 资产总值
  final double totalAssets;
  
  /// 负债总额
  final double totalLiabilities;
  
  /// 净资产
  final double netAssets;

  const FinancialOverviewData({
    required this.totalRevenue,
    required this.revenueTrend,
    required this.totalCost,
    required this.costTrend,
    required this.netProfit,
    required this.profitTrend,
    required this.profitMargin,
    required this.marginTrend,
    required this.profitTrendData,
    required this.salesRevenue,
    required this.recyclingRevenue,
    required this.operatingCost,
    required this.materialCost,
    required this.accountsReceivable,
    required this.accountsPayable,
    required this.cashFlow,
    required this.totalAssets,
    required this.totalLiabilities,
    required this.netAssets,
  });

  /// 空数据
  static const FinancialOverviewData empty = FinancialOverviewData(
    totalRevenue: 0,
    revenueTrend: 0,
    totalCost: 0,
    costTrend: 0,
    netProfit: 0,
    profitTrend: 0,
    profitMargin: 0,
    marginTrend: 0,
    profitTrendData: [],
    salesRevenue: 0,
    recyclingRevenue: 0,
    operatingCost: 0,
    materialCost: 0,
    accountsReceivable: 0,
    accountsPayable: 0,
    cashFlow: 0,
    totalAssets: 0,
    totalLiabilities: 0,
    netAssets: 0,
  );

  /// 示例数据
  static const FinancialOverviewData sample = FinancialOverviewData(
    totalRevenue: 1250000,
    revenueTrend: 8.5,
    totalCost: 850000,
    costTrend: 5.2,
    netProfit: 400000,
    profitTrend: 15.8,
    profitMargin: 32.0,
    marginTrend: 2.3,
    profitTrendData: [25000, 28000, 32000, 35000, 38000, 42000, 45000, 48000, 52000, 55000, 58000, 60000],
    salesRevenue: 65.5,
    recyclingRevenue: 34.5,
    operatingCost: 45.2,
    materialCost: 54.8,
    accountsReceivable: 125000,
    accountsPayable: 85000,
    cashFlow: 180000,
    totalAssets: 2500000,
    totalLiabilities: 650000,
    netAssets: 1850000,
  );
}
