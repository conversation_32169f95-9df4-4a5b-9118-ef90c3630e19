import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:window_manager/window_manager.dart';

import '../core/utils/logger.dart';

/// 窗口管理服务
/// 处理应用窗口大小、位置等管理功能
class WindowService extends GetxService {
  // 窗口状态
  final RxBool isMaximized = false.obs;
  final RxBool isMinimized = false.obs;
  final RxBool isFullScreen = false.obs;

  /// 初始化服务
  Future<WindowService> init() async {
    LoggerService.d('WindowService 初始化');
    
    // 只在桌面平台初始化
    if (defaultTargetPlatform == TargetPlatform.windows ||
        defaultTargetPlatform == TargetPlatform.macOS ||
        defaultTargetPlatform == TargetPlatform.linux) {
      await _initWindowState();
    }
    
    return this;
  }

  /// 初始化窗口状态
  Future<void> _initWindowState() async {
    try {
      LoggerService.i('🔍 WindowService开始初始化窗口状态...');

      // 获取初始化前的窗口状态
      final beforeSize = await windowManager.getSize();
      LoggerService.i('📊 初始化前窗口尺寸: ${beforeSize.width}x${beforeSize.height}');

      // 获取当前窗口状态
      isMaximized.value = await windowManager.isMaximized();
      isMinimized.value = await windowManager.isMinimized();
      isFullScreen.value = await windowManager.isFullScreen();

      // 获取初始化后的窗口状态
      final afterSize = await windowManager.getSize();
      LoggerService.i('📊 初始化后窗口尺寸: ${afterSize.width}x${afterSize.height}');

      LoggerService.i('✅ 窗口状态初始化完成: 最大化=${isMaximized.value}, 最小化=${isMinimized.value}, 全屏=${isFullScreen.value}');

      // 检查是否有尺寸变化
      if (beforeSize.width != afterSize.width || beforeSize.height != afterSize.height) {
        LoggerService.w('⚠️ WindowService初始化过程中窗口尺寸发生了变化！');
        LoggerService.w('   变化前: ${beforeSize.width}x${beforeSize.height}');
        LoggerService.w('   变化后: ${afterSize.width}x${afterSize.height}');
      }
    } catch (e) {
      LoggerService.e('❌ 窗口状态初始化失败', e);
    }
  }

  /// 设置登录窗口大小（简化版本）
  Future<void> setLoginWindowSize() async {
    if (!_isDesktopPlatform()) return;

    try {
      LoggerService.i('🔧 窗口大小设置（使用默认大小）...');

      // 现在直接使用启动时的大窗口，不需要动态调整
      // 只确保窗口可见和聚焦
      await windowManager.show();
      await windowManager.focus();

      // 更新状态
      isMaximized.value = false;

      LoggerService.i('✅ 窗口设置完成（使用默认大小）');
    } catch (e) {
      LoggerService.e('❌ 窗口设置失败', e);
    }
  }

  /// 最大化窗口（简化版本 - 现在窗口已经是大尺寸）
  Future<void> maximizeWindow() async {
    if (!_isDesktopPlatform()) return;

    try {
      LoggerService.i('🚀 窗口最大化（简化版本）...');

      // 获取当前窗口状态
      final currentSize = await windowManager.getSize();
      LoggerService.i('📊 当前窗口尺寸: ${currentSize.width.toInt()}x${currentSize.height.toInt()}');

      // 由于现在启动时就是大窗口，只需要尝试标准最大化
      try {
        await windowManager.maximize();
        await Future.delayed(const Duration(milliseconds: 300));

        final isNowMaximized = await windowManager.isMaximized();
        final newSize = await windowManager.getSize();

        LoggerService.i('📊 最大化后状态:');
        LoggerService.i('   - 最大化状态: $isNowMaximized');
        LoggerService.i('   - 窗口尺寸: ${newSize.width.toInt()}x${newSize.height.toInt()}');

        isMaximized.value = true;
      } catch (e) {
        LoggerService.w('⚠️ 最大化操作失败，但窗口已经是大尺寸: $e');
        isMaximized.value = true; // 标记为已处理
      }

      LoggerService.i('✅ 窗口最大化流程完成');

    } catch (e) {
      LoggerService.e('❌ 窗口最大化失败', e);
      // 确保应用功能不受影响
      isMaximized.value = true;
    }
  }



  /// 取消最大化窗口
  Future<void> unmaximizeWindow() async {
    if (!_isDesktopPlatform()) return;
    
    try {
      LoggerService.i('取消窗口最大化...');
      
      await windowManager.unmaximize();
      
      // 更新状态
      isMaximized.value = false;
      
      LoggerService.i('取消窗口最大化完成');
    } catch (e) {
      LoggerService.e('取消窗口最大化失败', e);
    }
  }

  /// 最小化窗口
  Future<void> minimizeWindow() async {
    if (!_isDesktopPlatform()) return;
    
    try {
      await windowManager.minimize();
      isMinimized.value = true;
      LoggerService.d('窗口已最小化');
    } catch (e) {
      LoggerService.e('窗口最小化失败', e);
    }
  }

  /// 恢复窗口
  Future<void> restoreWindow() async {
    if (!_isDesktopPlatform()) return;
    
    try {
      await windowManager.restore();
      isMinimized.value = false;
      LoggerService.d('窗口已恢复');
    } catch (e) {
      LoggerService.e('窗口恢复失败', e);
    }
  }

  /// 切换最大化状态
  Future<void> toggleMaximize() async {
    if (!_isDesktopPlatform()) return;
    
    if (isMaximized.value) {
      await unmaximizeWindow();
    } else {
      await maximizeWindow();
    }
  }

  /// 设置窗口大小
  Future<void> setWindowSize(Size size) async {
    if (!_isDesktopPlatform()) return;
    
    try {
      await windowManager.setSize(size);
      LoggerService.d('窗口大小设置为: ${size.width}x${size.height}');
    } catch (e) {
      LoggerService.e('设置窗口大小失败', e);
    }
  }

  /// 居中窗口
  Future<void> centerWindow() async {
    if (!_isDesktopPlatform()) return;
    
    try {
      await windowManager.center();
      LoggerService.d('窗口已居中');
    } catch (e) {
      LoggerService.e('窗口居中失败', e);
    }
  }

  /// 检查是否为桌面平台
  bool _isDesktopPlatform() {
    return defaultTargetPlatform == TargetPlatform.windows ||
           defaultTargetPlatform == TargetPlatform.macOS ||
           defaultTargetPlatform == TargetPlatform.linux;
  }

  /// 获取当前窗口大小
  Future<Size> getWindowSize() async {
    if (!_isDesktopPlatform()) return const Size(1400, 900);
    
    try {
      return await windowManager.getSize();
    } catch (e) {
      LoggerService.e('获取窗口大小失败', e);
      return const Size(1400, 900);
    }
  }

  /// 获取当前窗口位置
  Future<Offset> getWindowPosition() async {
    if (!_isDesktopPlatform()) return const Offset(0, 0);
    
    try {
      return await windowManager.getPosition();
    } catch (e) {
      LoggerService.e('获取窗口位置失败', e);
      return const Offset(0, 0);
    }
  }

  /// 设置窗口位置
  Future<void> setWindowPosition(Offset position) async {
    if (!_isDesktopPlatform()) return;
    
    try {
      await windowManager.setPosition(position);
      LoggerService.d('窗口位置设置为: (${position.dx}, ${position.dy})');
    } catch (e) {
      LoggerService.e('设置窗口位置失败', e);
    }
  }

  /// 确保窗口在主界面时处于最大化状态
  Future<void> ensureMaximizedForDashboard() async {
    if (!_isDesktopPlatform()) return;

    try {
      // 检查当前是否已经最大化
      final currentlyMaximized = await windowManager.isMaximized();

      if (!currentlyMaximized) {
        LoggerService.i('主界面需要最大化窗口...');
        await maximizeWindow();
      } else {
        LoggerService.d('窗口已经是最大化状态');
        isMaximized.value = true;
      }
    } catch (e) {
      LoggerService.e('确保主界面窗口最大化失败', e);
    }
  }
}
