"""
退货管理数据模型

老板，这个模块定义了退货管理的数据库模型，包括：
1. StockReturn - 退货单主表模型
2. StockReturnItem - 退货商品明细表模型

完全兼容FastAdmin数据库结构，支持退货业务流程管理。
"""

from sqlalchemy import Column, Integer, String, DECIMAL, Text, ForeignKey
from sqlalchemy.orm import relationship
from app.core.database import Base


class StockReturn(Base):
    """退货单主表模型"""
    __tablename__ = "fa_stock_return"

    id = Column(Integer, primary_key=True, index=True, comment="退货单ID")
    order_no = Column(String(20), unique=True, nullable=False, comment="退货单号")
    store_id = Column(Integer, ForeignKey("fa_store.id"), nullable=False, comment="退货门店ID")
    customer = Column(String(50), comment="客户")
    total_amount = Column(DECIMAL(10, 2), default=0.00, comment="应收金额")
    discount_amount = Column(DECIMAL(10, 2), default=0.00, comment="优惠金额")
    actual_amount = Column(DECIMAL(10, 2), default=0.00, comment="实际退款")
    remark = Column(String(255), comment="备注")
    operator_id = Column(Integer, ForeignKey("fa_admin.id"), nullable=False, comment="操作员ID")
    status = Column(Integer, default=1, comment="状态:1=待审核,2=已通过,3=未通过,4=已作废")
    audit_user_id = Column(Integer, ForeignKey("fa_admin.id"), comment="审核人ID")
    audit_time = Column(Integer, comment="审核时间")
    audit_remark = Column(String(255), comment="审核备注")

    # 时间字段
    createtime = Column(Integer, comment="创建时间")
    updatetime = Column(Integer, comment="更新时间")

    # 关联关系
    store = relationship("Store", foreign_keys=[store_id])
    operator = relationship("Admin", foreign_keys=[operator_id])
    auditor = relationship("Admin", foreign_keys=[audit_user_id])
    items = relationship("StockReturnItem", back_populates="stock_return", cascade="all, delete-orphan")


class StockReturnItem(Base):
    """退货商品明细表模型"""
    __tablename__ = "fa_stock_return_item"

    id = Column(Integer, primary_key=True, index=True, comment="退货明细ID")
    stock_return_id = Column(Integer, ForeignKey("fa_stock_return.id"), nullable=False, comment="退货单ID")
    jewelry_id = Column(Integer, ForeignKey("fa_jewelry.id"), comment="商品ID")
    barcode = Column(String(30), nullable=False, comment="商品条码")
    name = Column(String(100), nullable=False, comment="商品名称")
    category_id = Column(Integer, ForeignKey("fa_jewelry_category.id"), comment="分类ID")
    ring_size = Column(String(20), comment="圈口")

    # 重量信息
    gold_weight = Column(DECIMAL(10, 2), default=0.00, comment="金重(g)")
    silver_weight = Column(DECIMAL(10, 2), default=0.00, comment="银重(g)")
    total_weight = Column(DECIMAL(10, 2), default=0.00, comment="总重(g)")

    # 价格信息
    gold_price = Column(DECIMAL(10, 2), default=0.00, comment="金价(元/g)")
    silver_price = Column(DECIMAL(10, 2), default=0.00, comment="银价(元/g)")
    silver_work_price = Column(DECIMAL(10, 2), default=0.00, comment="银工费(元/g)")
    piece_work_price = Column(DECIMAL(10, 2), default=0.00, comment="件工费(元)")

    # 退货信息
    amount = Column(DECIMAL(10, 2), default=0.00, comment="金额")
    source_order_no = Column(String(20), comment="来源出库单号")

    # 时间字段
    createtime = Column(Integer, comment="创建时间")
    updatetime = Column(Integer, comment="更新时间")

    # 关联关系
    stock_return = relationship("StockReturn", back_populates="items")
    jewelry = relationship("Jewelry", foreign_keys=[jewelry_id])
    category = relationship("JewelryCategory", foreign_keys=[category_id])
