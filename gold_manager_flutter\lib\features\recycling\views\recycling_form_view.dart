import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/constants/border_styles.dart';
import '../../../core/utils/logger.dart';
import '../../../services/auth_service.dart';
import '../controllers/recycling_form_controller.dart';

/// 回收单表单页面 - 完全复制出库单表单的UI结构
class RecyclingFormView extends StatefulWidget {
  final String? tag; // 控制器标签，用于标签页模式

  const RecyclingFormView({super.key, this.tag});

  @override
  State<RecyclingFormView> createState() => _RecyclingFormViewState();
}

class _RecyclingFormViewState extends State<RecyclingFormView> {
  // 横向滚动控制器
  final ScrollController _horizontalScrollController = ScrollController();
  // TextEditingController管理器，避免重复创建导致输入错误
  final Map<String, TextEditingController> _textControllers = {};

  @override
  void initState() {
    super.initState();

    // 设置UI更新回调
    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.setUIUpdateCallback(_handleUIUpdate);
    });
  }

  /// 处理UI更新回调
  void _handleUIUpdate(int index, String fieldType, String newValue) {
    final controllerId = '${fieldType}_$index';
    _forceUpdateController(controllerId, newValue);
  }

  @override
  void dispose() {
    _horizontalScrollController.dispose();
    // 清理所有TextEditingController
    for (final controller in _textControllers.values) {
      controller.dispose();
    }
    _textControllers.clear();
    super.dispose();
  }

  // 获取控制器实例
  RecyclingFormController get controller {
    if (widget.tag != null) {
      // 使用标签获取特定的控制器实例
      return Get.find<RecyclingFormController>(tag: widget.tag);
    } else {
      // 获取默认控制器
      return Get.find<RecyclingFormController>();
    }
  }

  /// 获取或创建TextEditingController，避免重复创建导致输入错误
  TextEditingController _getOrCreateController(String id, String initialValue) {
    if (!_textControllers.containsKey(id)) {
      _textControllers[id] = TextEditingController(text: initialValue);
    } else {
      final textController = _textControllers[id]!;

      // 改进更新逻辑：只在文本内容确实不同且用户不在编辑时才更新
      if (textController.text != initialValue) {
        // 检查当前值是否可以解析为相同的数字（避免格式差异导致的重复更新）
        final currentValue = double.tryParse(textController.text);
        final newValue = double.tryParse(initialValue);

        // 只有在数值确实不同时才更新，使用 addPostFrameCallback 避免在 build 过程中调用 setState
        if (currentValue != newValue) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            textController.text = initialValue;
          });
        }
      }
    }
    return _textControllers[id]!;
  }

  /// 强制更新指定控制器的值（用于计算结果同步）
  void _forceUpdateController(String id, String newValue) {
    if (_textControllers.containsKey(id)) {
      final controller = _textControllers[id]!;

      // 延迟更新，确保在下一个事件循环中执行，避免与当前输入事件冲突
      Future.microtask(() {
        if (controller.text != newValue) {
          controller.text = newValue;
          // 将光标移到末尾
          controller.selection = TextSelection.fromPosition(
            TextPosition(offset: newValue.length),
          );
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Form(
        key: controller.formKey,
        child: Column(
          children: [
            _buildFormHeader(),
            Expanded(child: _buildItemList()),
            _buildFormFooter(),
          ],
        ),
      ),
    );
  }

  /// 构建表单头部区域 - 完全复制出库单表单的头部结构
  Widget _buildFormHeader() {
    final authService = Get.find<AuthService>();

    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(bottom: AppBorderStyles.tableBorder),
      ),
      child: Row(
        children: [
          // 新建回收单（图标+标题）
          Icon(Icons.recycling, color: Colors.purple[600], size: 20),
          const SizedBox(width: 8),
          const Text(
            '新建回收单',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),

          const SizedBox(width: 24),

          // 操作员信息标签
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(
                AppBorderStyles.largeBorderRadius,
              ),
              border: Border.all(
                color: Colors.blue[200]!,
                width: AppBorderStyles.borderWidth,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.person, size: 14, color: Colors.blue[600]),
                const SizedBox(width: 4),
                Obx(
                  () => Text(
                    '操作员: ${authService.userNickname.value.isNotEmpty ? authService.userNickname.value : authService.userName.value}',
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.blue[700],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(width: 24),

          // 门店选择
          const Text(
            '门店:',
            style: TextStyle(fontSize: 13, fontWeight: FontWeight.w500),
          ),
          const SizedBox(width: 8),
          SizedBox(
            width: 150,
            height: 32,
            child: Obx(
              () => Container(
                height: 32,
                decoration: AppBorderStyles.standardBoxDecoration,
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<int>(
                    value: controller.selectedStoreId.value == 0
                        ? null
                        : controller.selectedStoreId.value,
                    hint: Container(
                      alignment: Alignment.center,
                      child: const Text(
                        '请选择门店',
                        style: TextStyle(fontSize: 13, color: Colors.grey),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    isExpanded: true,
                    items: controller.storeList.map((store) {
                      return DropdownMenuItem<int>(
                        value: store.id,
                        child: Text(
                          store.name,
                          style: const TextStyle(fontSize: 13),
                          textAlign: TextAlign.center,
                        ),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        controller.selectedStoreId.value = value;
                      }
                    },
                    style: const TextStyle(fontSize: 13, color: Colors.black87),
                    icon: const Icon(Icons.arrow_drop_down, size: 20),
                    iconSize: 20,
                    menuMaxHeight: 300,
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    selectedItemBuilder: (BuildContext context) {
                      return controller.storeList.map<Widget>((store) {
                        return Container(
                          alignment: Alignment.center,
                          child: Text(
                            store.name,
                            style: const TextStyle(
                              fontSize: 13,
                              color: Colors.black87,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        );
                      }).toList();
                    },
                  ),
                ),
              ),
            ),
          ),

          const SizedBox(width: 24),

          // 客户姓名输入
          const Text(
            '客户姓名:',
            style: TextStyle(fontSize: 13, fontWeight: FontWeight.w500),
          ),
          const SizedBox(width: 8),
          SizedBox(
            width: 150,
            height: 32,
            child: Container(
              height: 32,
              decoration: AppBorderStyles.standardBoxDecoration,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                child: TextField(
                  controller: controller.customerNameController,
                  decoration: const InputDecoration(
                    hintText: '输入客户姓名',
                    hintStyle: TextStyle(fontSize: 13, color: Colors.grey),
                    border: InputBorder.none,
                    enabledBorder: InputBorder.none,
                    focusedBorder: InputBorder.none,
                    errorBorder: InputBorder.none,
                    focusedErrorBorder: InputBorder.none,
                    disabledBorder: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(vertical: 8),
                    isDense: true,
                  ),
                  style: const TextStyle(fontSize: 13),
                ),
              ),
            ),
          ),

          const SizedBox(width: 24),

          // 客户电话输入
          const Text(
            '客户电话:',
            style: TextStyle(fontSize: 13, fontWeight: FontWeight.w500),
          ),
          const SizedBox(width: 8),
          SizedBox(
            width: 150,
            height: 32,
            child: Container(
              height: 32,
              decoration: AppBorderStyles.standardBoxDecoration,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                child: TextField(
                  controller: controller.customerPhoneController,
                  decoration: const InputDecoration(
                    hintText: '输入客户电话',
                    hintStyle: TextStyle(fontSize: 13, color: Colors.grey),
                    border: InputBorder.none,
                    enabledBorder: InputBorder.none,
                    focusedBorder: InputBorder.none,
                    errorBorder: InputBorder.none,
                    focusedErrorBorder: InputBorder.none,
                    disabledBorder: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(vertical: 8),
                    isDense: true,
                  ),
                  style: const TextStyle(fontSize: 13),
                  keyboardType: TextInputType.phone,
                ),
              ),
            ),
          ),

          const SizedBox(width: 24),

          // 备注输入
          const Text(
            '备注:',
            style: TextStyle(fontSize: 13, fontWeight: FontWeight.w500),
          ),
          const SizedBox(width: 8),
          SizedBox(
            width: 200,
            height: 32,
            child: Container(
              height: 32,
              decoration: AppBorderStyles.standardBoxDecoration,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                child: TextField(
                  controller: controller.remarkController,
                  decoration: const InputDecoration(
                    hintText: '输入备注信息',
                    hintStyle: TextStyle(fontSize: 13, color: Colors.grey),
                    border: InputBorder.none,
                    enabledBorder: InputBorder.none,
                    focusedBorder: InputBorder.none,
                    errorBorder: InputBorder.none,
                    focusedErrorBorder: InputBorder.none,
                    disabledBorder: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(vertical: 8),
                    isDense: true,
                  ),
                  style: const TextStyle(fontSize: 13),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建物品列表 - 将在下一部分实现
  Widget _buildItemList() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: AppBorderStyles.elevatedBoxDecoration.copyWith(
        color: Colors.white,
      ),
      child: Column(
        children: [
          // 物品明细标题栏
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: const BoxDecoration(
              color: Colors.white,
              border: Border(bottom: AppBorderStyles.tableBorder),
            ),
            child: Row(
              children: [
                Icon(Icons.inventory_2, color: Colors.purple[600], size: 20),
                const SizedBox(width: 8),
                const Text(
                  '回收物品明细',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const Spacer(),
                // 物品数量显示
                Obx(
                  () => Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.purple[50],
                      borderRadius: BorderRadius.circular(
                        AppBorderStyles.borderRadius,
                      ),
                      border: Border.all(color: Colors.purple[200]!, width: 1),
                    ),
                    child: Text(
                      '共 ${controller.itemCount.value} 件物品',
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.purple[700],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                // 添加物品按钮
                SizedBox(
                  height: 32,
                  child: ElevatedButton.icon(
                    icon: const Icon(Icons.add, size: 14),
                    label: const Text('添加物品'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple[600],
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 0,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                      textStyle: const TextStyle(fontSize: 13),
                    ),
                    onPressed: () => controller.addRecyclingItem(),
                  ),
                ),
              ],
            ),
          ),
          // 物品列表内容
          Expanded(
            child: Obx(
              () => controller.itemList.isEmpty
                  ? _buildEmptyItemList()
                  : _buildRecyclingItemTable(),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建空物品列表
  Widget _buildEmptyItemList() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.recycling, size: 64, color: Colors.grey),
          const SizedBox(height: 16),
          const Text(
            '暂无回收物品',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            '请点击下方添加物品按钮开始添加',
            style: TextStyle(fontSize: 14, color: Colors.grey),
          ),
          const SizedBox(height: 16),
          // 添加物品按钮
          ElevatedButton.icon(
            icon: const Icon(Icons.add, size: 16),
            label: const Text('添加物品'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.purple[600],
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(
                  AppBorderStyles.borderRadius,
                ),
              ),
            ),
            onPressed: () => controller.addRecyclingItem(),
          ),
        ],
      ),
    );
  }

  /// 构建回收物品表格 - 完全复制出库单表单的表格结构
  Widget _buildRecyclingItemTable() {
    return LayoutBuilder(
      builder: (context, constraints) {
        final availableWidth = constraints.maxWidth;

        // 修复列宽度分配，确保总和不超过99%
        final indexWidth = availableWidth * 0.04; // 4% - 序号（减小）
        final remarkWidth = availableWidth * 0.10; // 10% - 品名（减小）
        final typeWidth = availableWidth * 0.07; // 7% - 按件/按克（减小）
        final categoryWidth = availableWidth * 0.08; // 8% - 分类（减小）
        final goldWeightWidth = availableWidth * 0.07; // 7% - 金重(g)（减小）
        final goldPriceWidth = availableWidth * 0.08; // 8% - 金价(元/g)（保持）
        final silverWeightWidth = availableWidth * 0.07; // 7% - 银重(g)（减小）
        final silverPriceWidth = availableWidth * 0.08; // 8% - 银价(元/g)（保持）
        final discountWidth = availableWidth * 0.07; // 7% - 折扣(%)（减小）
        final amountWidth = availableWidth * 0.10; // 10% - 金额(元)（保持）
        final finalAmountWidth = availableWidth * 0.11; // 11% - 折后金额(元)（减小）
        final actionWidth = availableWidth * 0.08; // 8% - 操作（保持）
        // 总计：4+10+7+8+7+8+7+8+7+10+11+8 = 95%（预留5%的弹性空间）

        // 总宽度就是可用宽度
        final totalWidth = availableWidth;

        return Container(
          width: double.infinity,
          height: double.infinity,
          margin: const EdgeInsets.all(4),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.03),
                blurRadius: 3,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Column(
            children: [
              // 表格标题行 - 使用统一的边框样式
              Container(
                width: totalWidth,
                color: AppBorderStyles.tableHeaderBackground,
                height: 48,
                child: Row(
                  children: [
                    Container(
                      width: indexWidth,
                      decoration: AppBorderStyles.headerCellDecoration(),
                      child: const Center(
                        child: Text(
                          '序号',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ),
                    Container(
                      width: remarkWidth,
                      decoration: AppBorderStyles.headerCellDecoration(),
                      child: const Center(
                        child: Text(
                          '品名',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ),
                    Container(
                      width: typeWidth,
                      decoration: AppBorderStyles.headerCellDecoration(),
                      child: const Center(
                        child: Text(
                          '按件/按克',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ),
                    Container(
                      width: categoryWidth,
                      decoration: AppBorderStyles.headerCellDecoration(),
                      child: const Center(
                        child: Text(
                          '分类',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ),
                    Container(
                      width: goldWeightWidth,
                      decoration: AppBorderStyles.headerCellDecoration(),
                      child: const Center(
                        child: Text(
                          '金重(g)',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ),
                    Container(
                      width: goldPriceWidth,
                      decoration: AppBorderStyles.headerCellDecoration(),
                      child: const Center(
                        child: Text(
                          '金价(元/g)',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ),
                    Container(
                      width: silverWeightWidth,
                      decoration: AppBorderStyles.headerCellDecoration(),
                      child: const Center(
                        child: Text(
                          '银重(g)',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ),
                    Container(
                      width: silverPriceWidth,
                      decoration: AppBorderStyles.headerCellDecoration(),
                      child: const Center(
                        child: Text(
                          '银价(元/g)',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ),
                    Container(
                      width: discountWidth,
                      decoration: AppBorderStyles.headerCellDecoration(),
                      child: const Center(
                        child: Text(
                          '折扣(%)',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ),
                    Container(
                      width: amountWidth,
                      decoration: AppBorderStyles.headerCellDecoration(),
                      child: const Center(
                        child: Text(
                          '金额(元)',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ),
                    Container(
                      width: finalAmountWidth,
                      decoration: AppBorderStyles.headerCellDecoration(),
                      child: const Center(
                        child: Text(
                          '折后金额(元)',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ),
                    Container(
                      width: actionWidth,
                      decoration: AppBorderStyles.headerCellDecoration(),
                      child: const Center(
                        child: Text(
                          '操作',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // 数据行 - 使用ListView.builder
              Expanded(
                child: Obx(
                  () => ListView.builder(
                    itemCount: controller.itemList.length,
                    itemBuilder: (context, index) {
                      final item = controller.itemList[index];

                      return Container(
                        width: totalWidth,
                        height: 52,
                        decoration: _getRowDecoration(index),
                        child: Row(
                          children: [
                            // 序号
                            Container(
                              width: indexWidth,
                              height: 52,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 4,
                              ),
                              alignment: Alignment.center,
                              child: Text(
                                '${index + 1}',
                                style: const TextStyle(
                                  fontWeight: FontWeight.w500,
                                  color: Colors.black87,
                                ),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                            ),
                            // 品名（原备注列，移动到序号后）
                            Container(
                              width: remarkWidth,
                              height: 52,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 4,
                              ),
                              alignment: Alignment.center,
                              child: _buildEditableCell(
                                width: remarkWidth,
                                value: item.remark,
                                controllerId: 'remark_$index',
                                onChanged: (value) {
                                  controller.updateItemField(
                                    index,
                                    remark: value,
                                  );
                                },
                              ),
                            ),
                            // 按件/按克下拉框
                            Container(
                              width: typeWidth,
                              height: 52,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 4,
                              ),
                              alignment: Alignment.center,
                              child: _buildTypeDropdown(index, item),
                            ),
                            // 分类下拉框
                            Container(
                              width: categoryWidth,
                              height: 52,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 4,
                              ),
                              alignment: Alignment.center,
                              child: _buildCategoryDropdown(index, item),
                            ),
                            // 金重(g)
                            Container(
                              width: goldWeightWidth,
                              height: 52,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 4,
                              ),
                              alignment: Alignment.center,
                              child: _buildEditableCell(
                                width: goldWeightWidth,
                                value: item.goldWeight.toStringAsFixed(2),
                                controllerId: 'goldWeight_$index',
                                keyboardType: TextInputType.number,
                                onChanged: (value) {
                                  final goldWeight =
                                      double.tryParse(value) ?? 0.0;
                                  controller.updateItemField(
                                    index,
                                    goldWeight: goldWeight,
                                  );
                                },
                              ),
                            ),
                            // 金价(元/g)
                            Container(
                              width: goldPriceWidth,
                              height: 52,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 4,
                              ),
                              alignment: Alignment.center,
                              child: _buildEditableCell(
                                width: goldPriceWidth,
                                value: item.goldPrice.toStringAsFixed(2),
                                controllerId: 'goldPrice_$index',
                                keyboardType: TextInputType.number,
                                onChanged: (value) {
                                  final goldPrice =
                                      double.tryParse(value) ?? 0.0;
                                  controller.updateItemField(
                                    index,
                                    goldPrice: goldPrice,
                                  );
                                },
                              ),
                            ),
                            // 银重(g)
                            Container(
                              width: silverWeightWidth,
                              height: 52,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 4,
                              ),
                              alignment: Alignment.center,
                              child: _buildEditableCell(
                                width: silverWeightWidth,
                                value: item.silverWeight.toStringAsFixed(2),
                                controllerId: 'silverWeight_$index',
                                keyboardType: TextInputType.number,
                                onChanged: (value) {
                                  final silverWeight =
                                      double.tryParse(value) ?? 0.0;
                                  controller.updateItemField(
                                    index,
                                    silverWeight: silverWeight,
                                  );
                                },
                              ),
                            ),
                            // 银价(元/g)
                            Container(
                              width: silverPriceWidth,
                              height: 52,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 4,
                              ),
                              alignment: Alignment.center,
                              child: _buildEditableCell(
                                width: silverPriceWidth,
                                value: item.silverPrice.toStringAsFixed(2),
                                controllerId: 'silverPrice_$index',
                                keyboardType: TextInputType.number,
                                onChanged: (value) {
                                  final silverPrice =
                                      double.tryParse(value) ?? 0.0;
                                  controller.updateItemField(
                                    index,
                                    silverPrice: silverPrice,
                                  );
                                },
                              ),
                            ),
                            // 折扣(%)
                            Container(
                              width: discountWidth,
                              height: 52,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 4,
                              ),
                              alignment: Alignment.center,
                              child: _buildEditableCell(
                                width: discountWidth,
                                value: item.discountRate.toStringAsFixed(1),
                                controllerId: 'discount_$index',
                                keyboardType: TextInputType.number,
                                onChanged: (value) {
                                  final discountRate =
                                      double.tryParse(value) ?? 100.0;
                                  controller.updateItemField(
                                    index,
                                    discountRate: discountRate,
                                  );
                                },
                              ),
                            ),
                            // 金额(元) - 只读显示计算结果
                            Container(
                              width: amountWidth,
                              height: 52,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 4,
                              ),
                              alignment: Alignment.center,
                              child: Container(
                                width: amountWidth,
                                height: 52,
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 8,
                                ),
                                alignment: Alignment.center,
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: Colors.blue[50],
                                    border: Border.all(
                                      color: Colors.blue[200]!,
                                      width: 1,
                                    ),
                                    borderRadius: BorderRadius.circular(6),
                                  ),
                                  alignment: Alignment.center,
                                  child: Text(
                                    item.amount.toStringAsFixed(2),
                                    style: TextStyle(
                                      fontSize: 13,
                                      color: Colors.blue[800],
                                      fontFamily: 'monospace',
                                      fontWeight: FontWeight.w600,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                              ),
                            ),
                            // 折后金额(元) - 只读显示计算结果
                            Container(
                              width: finalAmountWidth,
                              height: 52,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 4,
                              ),
                              alignment: Alignment.center,
                              child: Container(
                                width: finalAmountWidth,
                                height: 52,
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 8,
                                ),
                                alignment: Alignment.center,
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: Colors.green[50],
                                    border: Border.all(
                                      color: Colors.green[200]!,
                                      width: 1,
                                    ),
                                    borderRadius: BorderRadius.circular(6),
                                  ),
                                  alignment: Alignment.center,
                                  child: Text(
                                    item.finalAmount.toStringAsFixed(2),
                                    style: TextStyle(
                                      fontSize: 13,
                                      color: Colors.green[800],
                                      fontFamily: 'monospace',
                                      fontWeight: FontWeight.w600,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                              ),
                            ),
                            // 操作
                            Container(
                              width: actionWidth,
                              height: 52,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 4,
                              ),
                              alignment: Alignment.center,
                              child: Center(
                                child: IconButton(
                                  icon: Icon(
                                    Icons.delete_outline,
                                    size: 18,
                                    color: Colors.red[600],
                                  ),
                                  onPressed: () => _confirmRemoveItem(index),
                                  tooltip: '删除物品',
                                  style: IconButton.styleFrom(
                                    backgroundColor: Colors.red[50],
                                    foregroundColor: Colors.red[600],
                                    minimumSize: const Size(32, 32),
                                    maximumSize: const Size(32, 32),
                                    padding: EdgeInsets.zero,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(6),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 构建表单底部 - 将在下一部分实现
  Widget _buildFormFooter() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(top: AppBorderStyles.tableBorder),
      ),
      child: Row(
        children: [
          // 总计信息
          Expanded(
            child: Row(
              children: [
                Icon(Icons.calculate, color: Colors.purple[600], size: 20),
                const SizedBox(width: 8),
                const Text(
                  '总计:',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(width: 16),
                Obx(
                  () => Text(
                    '重量: ${controller.totalWeight.value.toStringAsFixed(2)}g',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Obx(
                  () => Text(
                    '金额: ¥${controller.totalAmount.value.toStringAsFixed(2)}',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.purple[700],
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Obx(
                  () => Text(
                    '折后金额: ¥${controller.totalFinalAmount.value.toStringAsFixed(2)}',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w700,
                      color: Colors.green[700],
                    ),
                  ),
                ),
              ],
            ),
          ),
          // 操作按钮 - 与出库单样式保持一致
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 保存草稿按钮
              SizedBox(
                height: 32,
                child: OutlinedButton(
                  onPressed: () => _saveDraft(),
                  style: OutlinedButton.styleFrom(
                    side: BorderSide(color: Colors.grey[400]!, width: 1),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 0,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(
                        AppBorderStyles.borderRadius,
                      ),
                    ),
                    textStyle: const TextStyle(fontSize: 13),
                  ),
                  child: const Text('保存草稿'),
                ),
              ),

              const SizedBox(width: 16),

              // 提交审核按钮
              SizedBox(
                height: 32,
                child: Obx(
                  () => ElevatedButton(
                    onPressed: controller.isLoading.value
                        ? null
                        : () => _saveRecyclingOrder(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple[600],
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 0,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(
                          AppBorderStyles.borderRadius,
                        ),
                      ),
                      textStyle: const TextStyle(fontSize: 13),
                    ),
                    child: controller.isLoading.value
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.white,
                              ),
                            ),
                          )
                        : const Text('提交审核'),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 保存草稿
  void _saveDraft() {
    // TODO: 实现保存草稿功能
    Get.snackbar('功能提示', '保存草稿功能开发中...');
  }

  /// 保存回收单并提交审核
  Future<void> _saveRecyclingOrder() async {
    try {
      // 验证表单数据
      if (!_validateForm()) {
        return;
      }

      // 显示加载状态
      controller.isLoading.value = true;

      // 调用控制器的保存方法
      final success = await controller.saveRecyclingOrder();

      if (success) {
        // 显示成功提示
        await Get.dialog(
          AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(
                AppBorderStyles.mediumBorderRadius,
              ),
            ),
            title: Row(
              children: [
                Icon(Icons.check_circle, color: Colors.green[600], size: 24),
                const SizedBox(width: 8),
                const Text(
                  '提交成功',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
            content: const Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '回收单提交成功，已自动审核通过！',
                  style: TextStyle(fontSize: 16, color: Colors.black87),
                ),
                SizedBox(height: 8),
                Text(
                  '系统将重置表单，您可以继续创建新的回收单。',
                  style: TextStyle(fontSize: 14, color: Colors.grey),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Get.back(); // 关闭对话框
                  // 重置表单到初始状态
                  controller.resetForm();
                },
                style: TextButton.styleFrom(
                  foregroundColor: Colors.green[600],
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                ),
                child: const Text('确定'),
              ),
            ],
          ),
          barrierDismissible: false,
        );
      }
    } catch (e) {
      // 错误处理已在控制器中完成
      LoggerService.e('保存回收单失败', e);
    } finally {
      controller.isLoading.value = false;
    }
  }

  /// 验证表单数据
  bool _validateForm() {
    // 验证客户信息
    if (controller.customerNameController.text.trim().isEmpty) {
      Get.snackbar(
        '验证失败',
        '请输入客户姓名',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }

    // 验证回收物品
    if (controller.itemList.isEmpty) {
      Get.snackbar(
        '验证失败',
        '请至少添加一个回收物品',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }

    // 验证每个物品的数据
    for (int i = 0; i < controller.itemList.length; i++) {
      final item = controller.itemList[i];

      if (item.remark.trim().isEmpty) {
        Get.snackbar(
          '验证失败',
          '第${i + 1}个物品的品名不能为空',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return false;
      }

      if (item.goldWeight <= 0 && item.silverWeight <= 0) {
        Get.snackbar(
          '验证失败',
          '第${i + 1}个物品的金重或银重必须大于0',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return false;
      }

      if (item.goldWeight > 0 && item.goldPrice <= 0) {
        Get.snackbar(
          '验证失败',
          '第${i + 1}个物品的金价必须大于0',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return false;
      }

      if (item.silverWeight > 0 && item.silverPrice <= 0) {
        Get.snackbar(
          '验证失败',
          '第${i + 1}个物品的银价必须大于0',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return false;
      }
    }

    return true;
  }

  /// 获取行装饰样式 - 与出库单保持一致
  BoxDecoration _getRowDecoration(int index) {
    return const BoxDecoration(
      color: Colors.white, // 始终使用白色背景
      border: Border(
        right: AppBorderStyles.tableBorder,
        bottom: AppBorderStyles.tableBorder,
      ),
    );
  }

  /// 构建可编辑单元格 - 完全复制出库单的样式
  Widget _buildEditableCell({
    required double width,
    required String value,
    required String controllerId,
    required Function(String) onChanged,
    TextAlign textAlign = TextAlign.center,
    TextInputType keyboardType = TextInputType.text,
  }) {
    final textController = _getOrCreateController(controllerId, value);

    return Container(
      width: width,
      height: 52,
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      alignment: Alignment.center,
      child: TextFormField(
        controller: textController,
        textAlign: textAlign,
        keyboardType: keyboardType,
        style: const TextStyle(
          fontSize: 13,
          color: Colors.black87,
          fontFamily: 'monospace', // 使用等宽字体
        ),
        decoration: InputDecoration(
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 10,
            vertical: 8,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
            borderSide: const BorderSide(
              color: AppBorderStyles.borderColor,
              width: AppBorderStyles.borderWidth,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
            borderSide: const BorderSide(
              color: AppBorderStyles.borderColor,
              width: AppBorderStyles.borderWidth,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
            borderSide: const BorderSide(
              color: AppBorderStyles.focusBorderColor,
              width: AppBorderStyles.focusBorderWidth,
            ),
          ),
          isDense: true,
        ),
        onChanged: onChanged,
        onFieldSubmitted: onChanged,
      ),
    );
  }

  /// 构建按件/按克下拉框
  Widget _buildTypeDropdown(int index, dynamic item) {
    return Container(
      width: double.infinity,
      height: 52,
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      alignment: Alignment.center,
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(
            color: AppBorderStyles.borderColor,
            width: AppBorderStyles.borderWidth,
          ),
          borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
          color: Colors.white,
        ),
        child: DropdownButtonHideUnderline(
          child: DropdownButton<String>(
            value: item.type ?? 'by_weight',
            isExpanded: true,
            items: const [
              DropdownMenuItem(
                value: 'by_weight',
                child: Text('按克', style: TextStyle(fontSize: 13)),
              ),
              DropdownMenuItem(
                value: 'by_piece',
                child: Text('按件', style: TextStyle(fontSize: 13)),
              ),
            ],
            onChanged: (value) {
              if (value != null) {
                controller.updateItemField(index, type: value);
              }
            },
            style: const TextStyle(fontSize: 13, color: Colors.black87),
            icon: const Icon(Icons.arrow_drop_down, size: 20),
            iconSize: 20,
            menuMaxHeight: 300,
            padding: const EdgeInsets.symmetric(horizontal: 10),
            selectedItemBuilder: (BuildContext context) {
              return [
                Container(
                  alignment: Alignment.center,
                  child: const Text(
                    '按克',
                    style: TextStyle(fontSize: 13, color: Colors.black87),
                    textAlign: TextAlign.center,
                  ),
                ),
                Container(
                  alignment: Alignment.center,
                  child: const Text(
                    '按件',
                    style: TextStyle(fontSize: 13, color: Colors.black87),
                    textAlign: TextAlign.center,
                  ),
                ),
              ];
            },
          ),
        ),
      ),
    );
  }

  /// 构建商品分类下拉框
  Widget _buildCategoryDropdown(int index, dynamic item) {
    return Container(
      width: double.infinity,
      height: 52,
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      alignment: Alignment.center,
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(
            color: AppBorderStyles.borderColor,
            width: AppBorderStyles.borderWidth,
          ),
          borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
          color: Colors.white,
        ),
        child: Obx(() {
          // 获取分类列表
          final categories = controller.categoryList;

          return DropdownButtonHideUnderline(
            child: DropdownButton<int>(
              value: item.categoryId,
              hint: const Text(
                '请选择分类',
                style: TextStyle(fontSize: 13, color: Colors.grey),
                textAlign: TextAlign.center,
              ),
              isExpanded: true,
              items: categories.map((category) {
                return DropdownMenuItem<int>(
                  value: category.id,
                  child: Text(
                    category.name,
                    style: const TextStyle(fontSize: 13),
                  ),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  controller.updateItemField(index, categoryId: value);
                }
              },
              style: const TextStyle(fontSize: 13, color: Colors.black87),
              icon: const Icon(Icons.arrow_drop_down, size: 20),
              iconSize: 20,
              menuMaxHeight: 300,
              padding: const EdgeInsets.symmetric(horizontal: 10),
              selectedItemBuilder: (BuildContext context) {
                return categories.map((category) {
                  return Container(
                    alignment: Alignment.center,
                    child: Text(
                      category.name,
                      style: const TextStyle(
                        fontSize: 13,
                        color: Colors.black87,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  );
                }).toList();
              },
            ),
          );
        }),
      ),
    );
  }

  /// 确认删除物品 - 与出库单样式保持一致
  void _confirmRemoveItem(int index) {
    Get.dialog(
      AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(
            AppBorderStyles.mediumBorderRadius,
          ),
        ),
        title: Row(
          children: [
            Icon(
              Icons.warning_amber_rounded,
              color: Colors.orange[600],
              size: 24,
            ),
            const SizedBox(width: 8),
            const Text(
              '确认删除',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '确定要删除这个回收物品吗？',
              style: TextStyle(fontSize: 16, color: Colors.black87),
            ),
            const SizedBox(height: 8),
            Text(
              '此操作不可撤销，请谨慎操作。',
              style: TextStyle(fontSize: 14, color: Colors.grey[600]),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            style: TextButton.styleFrom(foregroundColor: Colors.grey[600]),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              controller.removeRecyclingItem(index);
              Get.back();
              Get.snackbar(
                '成功',
                '回收物品已从列表中移除',
                snackPosition: SnackPosition.BOTTOM,
                backgroundColor: Colors.green,
                colorText: Colors.white,
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red[600],
              foregroundColor: Colors.white,
            ),
            child: const Text('确定删除'),
          ),
        ],
      ),
    );
  }
}
