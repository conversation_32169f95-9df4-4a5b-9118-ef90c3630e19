import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../../../core/theme/app_colors.dart';

/// 图片选择器组件
class ImagePickerWidget extends StatelessWidget {
  final List<String> imageUrls; // 已上传的图片URL
  final List<File> tempImages; // 临时图片（尚未上传）
  final Function() onPickImage; // 选择图片回调
  final Function() onTakePhoto; // 拍照回调
  final Function(int) onDeleteTempImage; // 删除临时图片回调
  final Function(int) onDeleteUploadedImage; // 删除已上传图片回调
  final bool isLoading; // 是否正在上传
  final int maxImages; // 最大图片数量

  const ImagePickerWidget({
    super.key,
    required this.imageUrls,
    required this.tempImages,
    required this.onPickImage,
    required this.onTakePhoto,
    required this.onDeleteTempImage,
    required this.onDeleteUploadedImage,
    this.isLoading = false,
    this.maxImages = 6,
  });

  @override
  Widget build(BuildContext context) {
    final totalImages = imageUrls.length + tempImages.length;
    final bool canAddMore = totalImages < maxImages;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              '图片',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            Text(
              '$totalImages/$maxImages',
              style: const TextStyle(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
            childAspectRatio: 1,
          ),
          itemCount: totalImages + (canAddMore ? 1 : 0),
          itemBuilder: (context, index) {
            if (index < imageUrls.length) {
              // 已上传的图片
              return _buildUploadedImageItem(context, index);
            } else if (index < totalImages) {
              // 临时图片
              return _buildTempImageItem(context, index - imageUrls.length);
            } else {
              // 添加按钮
              return _buildAddButton(context);
            }
          },
        ),
      ],
    );
  }

  // 构建已上传图片项
  Widget _buildUploadedImageItem(BuildContext context, int index) {
    return Stack(
      children: [
        GestureDetector(
          onTap: () => _showImagePreview(context, index, true),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppColors.border),
            ),
            clipBehavior: Clip.antiAlias,
            child: CachedNetworkImage(
              imageUrl: imageUrls[index],
              fit: BoxFit.cover,
              width: double.infinity,
              height: double.infinity,
              placeholder: (context, url) => const Center(
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
              errorWidget: (context, url, error) => const Icon(
                Icons.broken_image,
                color: AppColors.error,
              ),
            ),
          ),
        ),
        Positioned(
          top: 0,
          right: 0,
          child: GestureDetector(
            onTap: () => onDeleteUploadedImage(index),
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.6),
                borderRadius: const BorderRadius.only(
                  topRight: Radius.circular(8),
                  bottomLeft: Radius.circular(8),
                ),
              ),
              child: const Icon(
                Icons.close,
                size: 16,
                color: Colors.white,
              ),
            ),
          ),
        ),
      ],
    );
  }

  // 构建临时图片项
  Widget _buildTempImageItem(BuildContext context, int index) {
    return Stack(
      children: [
        GestureDetector(
          onTap: () => _showImagePreview(context, index, false),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppColors.border),
            ),
            clipBehavior: Clip.antiAlias,
            child: Image.file(
              tempImages[index],
              fit: BoxFit.cover,
              width: double.infinity,
              height: double.infinity,
            ),
          ),
        ),
        Positioned(
          top: 0,
          right: 0,
          child: GestureDetector(
            onTap: () => onDeleteTempImage(index),
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.6),
                borderRadius: const BorderRadius.only(
                  topRight: Radius.circular(8),
                  bottomLeft: Radius.circular(8),
                ),
              ),
              child: const Icon(
                Icons.close,
                size: 16,
                color: Colors.white,
              ),
            ),
          ),
        ),
        if (isLoading)
          Positioned.fill(
            child: Container(
              color: Colors.black.withOpacity(0.5),
              child: const Center(
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            ),
          ),
      ],
    );
  }

  // 构建添加按钮
  Widget _buildAddButton(BuildContext context) {
    return GestureDetector(
      onTap: _showImageSourceDialog,
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.backgroundLight,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: AppColors.border),
        ),
        child: const Center(
          child: Icon(
            Icons.add_photo_alternate,
            color: AppColors.textSecondary,
            size: 32,
          ),
        ),
      ),
    );
  }

  // 显示图片来源选择对话框
  void _showImageSourceDialog() {
    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.symmetric(vertical: 20),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.photo_library),
              title: const Text('从相册选择'),
              onTap: () {
                Get.back();
                onPickImage();
              },
            ),
            ListTile(
              leading: const Icon(Icons.photo_camera),
              title: const Text('拍照'),
              onTap: () {
                Get.back();
                onTakePhoto();
              },
            ),
            ListTile(
              leading: const Icon(Icons.close),
              title: const Text('取消'),
              onTap: () => Get.back(),
            ),
          ],
        ),
      ),
    );
  }

  // 显示图片预览
  void _showImagePreview(BuildContext context, int index, bool isUploaded) {
    final List<ImageProvider> imageProviders = [];
    int initialIndex = 0;

    // 添加已上传图片
    for (int i = 0; i < imageUrls.length; i++) {
      imageProviders.add(CachedNetworkImageProvider(imageUrls[i]));
    }

    // 添加临时图片
    for (int i = 0; i < tempImages.length; i++) {
      imageProviders.add(FileImage(tempImages[i]));
    }

    // 确定初始索引
    if (isUploaded) {
      initialIndex = index;
    } else {
      initialIndex = imageUrls.length + index;
    }

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => Scaffold(
          backgroundColor: Colors.black,
          appBar: AppBar(
            backgroundColor: Colors.black,
            foregroundColor: Colors.white,
            title: Text('图片 ${initialIndex + 1}/${imageProviders.length}'),
          ),
          body: PhotoViewGallery.builder(
            itemCount: imageProviders.length,
            builder: (context, i) {
              return PhotoViewGalleryPageOptions(
                imageProvider: imageProviders[i],
                minScale: PhotoViewComputedScale.contained,
                maxScale: PhotoViewComputedScale.covered * 2,
              );
            },
            scrollPhysics: const BouncingScrollPhysics(),
            backgroundDecoration: const BoxDecoration(color: Colors.black),
            pageController: PageController(initialPage: initialIndex),
          ),
        ),
      ),
    );
  }
} 