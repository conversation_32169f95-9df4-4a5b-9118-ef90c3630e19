import 'package:get/get.dart';
import '../core/utils/logger.dart';
import '../models/stock/stock_in.dart';
import '../models/stock/stock_out.dart';
import '../models/stock/stock_out_item.dart';
import '../models/stock/stock_in_item.dart';
import '../models/stock/stock_in_jewelry_item.dart';
import '../models/jewelry/jewelry.dart';
import '../models/jewelry/jewelry_category.dart';
import '../models/common/enums.dart';
import '../models/common/page_data.dart';
import '../models/common/document_status.dart';
import '../models/store/store.dart';
import '../core/services/api_client.dart';
import '../core/config/app_config.dart';

/// 库存服务
/// 处理库存相关的业务逻辑
class StockService extends GetxService {
  final ApiClient _apiClient = Get.find<ApiClient>();

  /// 初始化服务
  Future<StockService> init() async {
    LoggerService.d('StockService 初始化');
    return this;
  }

  /// 获取入库单列表
  Future<PageData<StockIn>> getStockInList(Map<String, dynamic> params) async {
    try {
      // 尝试真实API调用
      try {
        print('🔍 前端API调用 DEBUG:');
        print('   URL: ${AppConfig.apiBaseUrl}${AppConfig.apiEndpoint['stockIn']}');
        print('   参数: $params');

        final response = await _apiClient.get(
          '${AppConfig.apiEndpoint['stockIn']}',
          queryParameters: params,
        );

        print('   响应状态码: ${response.statusCode}');

        if (response.statusCode == 200) {
          final responseData = response.data;
          print('   响应数据类型: ${responseData.runtimeType}');
          print('   响应数据keys: ${responseData is Map ? responseData.keys.toList() : 'NOT_MAP'}');

          final List<dynamic> stockInList = responseData['data'] ?? [];
          print('   stockInList长度: ${stockInList.length}');

          if (stockInList.isNotEmpty) {
            final firstItem = stockInList[0];
            print('   第一条数据类型: ${firstItem.runtimeType}');
            if (firstItem is Map) {
              print('   第一条数据keys: ${firstItem.keys.toList()}');
              print('   第一条数据item_count: ${firstItem['item_count']}');
            }
          }

          final pagination = responseData['pagination'] ?? {};

          return PageData<StockIn>(
            data: stockInList.map((json) => _mapStockInFromApi(json)).toList(),
            currentPage: pagination['page'] ?? 1,
            lastPage: pagination['pages'] ?? 1,
            total: pagination['total'] ?? 0,
          );
        } else {
          print('   ❌ API调用失败: ${response.statusCode} - ${response.statusMessage}');
          throw Exception('API调用失败: ${response.statusMessage}');
        }
      } catch (e) {
        print('   ❌ 异常: $e');
        LoggerService.e('API调用失败: $e');
        rethrow;
      }
    } catch (e) {
      LoggerService.e('获取入库单列表失败', e);
      rethrow;
    }
  }

  /// 数据映射方法：将API返回的JSON数据映射为StockIn对象
  StockIn _mapStockInFromApi(Map<String, dynamic> json) {
    // 调试输出
    print('🔍 DEBUG: 映射入库单数据');
    print('   入库单号: ${json['order_no']}');
    print('   原始item_count: ${json['item_count']}');
    print('   是否包含items字段: ${json.containsKey('items')}');
    print('   所有字段: ${json.keys.toList()}');

    // 创建Store对象（如果有门店信息）
    Store? store;
    if (json['store_name'] != null) {
      store = Store(
        id: json['store_id'] ?? 0,
        name: json['store_name'],
        code: null, // API中没有返回code
        address: null,
        phone: null,
        manager: null,
      );
    }

    // 处理商品明细列表
    List<StockInItem>? items;

    // 优先使用API返回的详细items数据
    if (json['items'] != null && json['items'] is List) {
      final itemsData = json['items'] as List;
      print('   从API获取到 ${itemsData.length} 个详细商品明细');

      items = itemsData.map((itemJson) {
        try {
          return StockInItem.fromJson(itemJson as Map<String, dynamic>);
        } catch (e) {
          print('   ⚠️ 解析商品明细失败: $e');
          print('   原始数据: $itemJson');
          // 返回一个基础的StockInItem对象
          return StockInItem(
            id: itemJson['id'] ?? 0,
            stockInId: json['id'] ?? 0,
            jewelryId: itemJson['jewelry_id'] ?? 0,
            barcode: itemJson['barcode'] ?? '',
            name: itemJson['name'] ?? '',
            categoryId: itemJson['category_id'] ?? 0,
            totalPrice: _parseDouble(itemJson['total_cost']),
          );
        }
      }).toList();
    } else {
      // 如果没有详细items数据，设置为空列表
      print('   ❌ API未返回商品明细数据，设置为空列表');
      items = [];
    }

    final stockIn = StockIn(
      id: json['id'] ?? 0,
      stockInNo: json['order_no'] ?? '',
      storeId: json['store_id'] ?? 0,
      totalAmount: _parseDouble(json['total_amount']),
      operatorId: json['operator_id'] ?? 0,
      status: _parseDocumentStatus(json['status']),
      createTime: _parseDateTime(json['createtime']),
      remark: json['remark'],
      store: store,
      items: items, // 添加items列表
    );

    print('   最终stockIn.itemCount: ${stockIn.itemCount}');
    print('   最终items数量: ${items.length ?? 0}');
    print('');

    return stockIn;
  }

  /// 解析文档状态
  DocumentStatus _parseDocumentStatus(dynamic status) {
    if (status == null) return DocumentStatus.draft;

    switch (status) {
      case 0:
        return DocumentStatus.draft;
      case 1:
        return DocumentStatus.pending;
      case 2:
        return DocumentStatus.approved;
      case 3:
        return DocumentStatus.rejected;
      default:
        return DocumentStatus.draft;
    }
  }

  /// 解析时间戳
  DateTime _parseDateTime(dynamic timestamp) {
    if (timestamp == null) return DateTime.now();

    if (timestamp is int) {
      return DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
    }

    return DateTime.now();
  }

  /// 解析双精度浮点数
  double _parseDouble(dynamic value) {
    if (value == null) return 0.0;

    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value) ?? 0.0;
    }

    return 0.0;
  }

  /// 获取入库单详情
  Future<StockIn> getStockInById(int id) async {
    try {
      LoggerService.d('🔍 获取入库单详情: ID=$id');

      final response = await _apiClient.get('${AppConfig.apiEndpoint['stockIn']}/$id');

      LoggerService.d('📡 入库单详情API响应:');
      LoggerService.d('   状态码: ${response.statusCode}');
      LoggerService.d('   响应数据: ${response.data}');

      if (response.statusCode == 200) {
        final responseData = response.data;

        // 处理不同的API响应格式
        Map<String, dynamic> stockInData;

        if (responseData.containsKey('success') && responseData['success'] == true && responseData['data'] != null) {
          // 格式1: {success: true, data: {id: xxx, ...}}
          stockInData = responseData['data'];
          LoggerService.d('📋 使用包装格式解析入库单详情');
        } else if (responseData.containsKey('id') && responseData['id'] != null) {
          // 格式2: {id: xxx, order_no: xxx, ...} (直接返回入库单对象)
          stockInData = responseData;
          LoggerService.d('📋 使用直接格式解析入库单详情');
        } else {
          LoggerService.e('❌ 无法从响应中解析入库单详情数据');
          LoggerService.e('   完整响应数据: $responseData');
          throw Exception('API返回数据格式错误：无法解析入库单详情数据');
        }

        return _mapStockInFromApi(stockInData);
      } else {
        throw Exception('API调用失败: ${response.statusMessage}');
      }
    } catch (e) {
      LoggerService.e('获取入库单详情失败', e);
      rethrow;
    }
  }

  /// 创建入库单（支持StockInJewelryItem）- 修复版：审核通过后才创建首饰记录
  Future<StockIn?> createStockInWithJewelryItems(StockIn stockIn, List<StockInJewelryItem> jewelryItems) async {
    try {
      LoggerService.d('🌟 开始创建入库单（修复版：不立即创建首饰记录）');
      LoggerService.d('   入库单信息: 门店ID=${stockIn.storeId}, 操作员ID=${stockIn.operatorId}');
      LoggerService.d('   商品数量: ${jewelryItems.length}');

      final List<Map<String, dynamic>> itemsData = [];

      // ✅ 修复：不再立即创建首饰记录，只准备入库单明细数据
      for (int i = 0; i < jewelryItems.length; i++) {
        final item = jewelryItems[i];

        LoggerService.d('📦 处理第${i + 1}个商品明细: ${item.name}');
        LoggerService.d('   条码: ${item.barcode}');
        LoggerService.d('   金重: ${item.goldWeight}g');
        LoggerService.d('   银重: ${item.silverWeight}g');
        LoggerService.d('   金价: ${item.goldPrice}元/g');
        LoggerService.d('   银价: ${item.silverPrice}元/g');
        LoggerService.d('   工费方式: ${item.workType} (0=按克, 1=按件)');
        LoggerService.d('   工费: ${item.workPrice}元');
        LoggerService.d('   电铸费: ${item.platingCost}元');

        // 计算成本 - 使用StockInJewelryItem的内置计算方法
        final goldCost = item.goldCost;
        final silverCost = item.silverCost;
        final totalWeight = item.totalWeight;
        final workCost = item.workCost;
        final totalCost = item.totalCost;

        LoggerService.d('💰 成本计算结果:');
        LoggerService.d('   金成本: $goldCost元');
        LoggerService.d('   银成本: $silverCost元');
        LoggerService.d('   工费成本: $workCost元');
        LoggerService.d('   总成本: $totalCost元');

        // ✅ 修复：构建入库单明细数据，不包含jewelry_id字段（让后端使用占位符值）
        final itemData = {
          // 不发送jewelry_id字段，让后端自动使用占位符值
          'barcode': item.barcode,
          'name': item.name,
          'category_id': item.categoryId,
          'ring_size': item.ringSize,
          'gold_weight': item.goldWeight,
          'gold_price': item.goldPrice,
          'gold_cost': goldCost,
          'silver_weight': item.silverWeight,
          'total_weight': totalWeight,
          'silver_price': item.silverPrice,
          'silver_cost': silverCost,
          'silver_work_type': item.workType, // 银工费方式：0=按克，1=按件
          'silver_work_price': item.workPrice,
          'silver_work_cost': workCost, // 银工费成本
          'plating_cost': item.platingCost,
          'total_cost': totalCost, // 使用计算出的总成本
          'wholesale_work_price': item.wholesaleWorkPrice,
          'retail_work_price': item.retailWorkPrice,
          'piece_work_price': item.pieceWorkPrice,
        };

        LoggerService.d('📋 第${i + 1}个入库单明细数据（无jewelry_id）: $itemData');
        itemsData.add(itemData);

        LoggerService.d('✅ 第${i + 1}个商品明细准备完成，当前明细列表长度: ${itemsData.length}');
      }

      LoggerService.d('📋 所有入库单明细数据准备完成，共${itemsData.length}个');

      // 验证批量处理结果
      if (itemsData.length != jewelryItems.length) {
        throw Exception('批量处理失败：期望${jewelryItems.length}个商品，实际处理${itemsData.length}个');
      }

      // 第二步：创建入库单
      final requestData = {
        'store_id': stockIn.storeId,
        'supplier': '', // 供应商信息
        'remark': stockIn.remark ?? '',
        'items': itemsData,
      };

      LoggerService.d('🌐 创建入库单API请求详情:');
      LoggerService.d('   URL: ${AppConfig.apiEndpoint['stockIn']}?operator_id=${stockIn.operatorId}');
      LoggerService.d('   Method: POST');
      LoggerService.d('   Store ID: ${requestData['store_id']}');
      LoggerService.d('   Items Count: ${(requestData['items'] as List).length}');

      final response = await _apiClient.post(
        '${AppConfig.apiEndpoint['stockIn']}?operator_id=${stockIn.operatorId}',
        data: requestData,
      );

      LoggerService.d('📡 创建入库单API响应详情:');
      LoggerService.d('   Status Code: ${response.statusCode}');
      LoggerService.d('   Status Message: ${response.statusMessage}');
      LoggerService.d('   Response Data: ${response.data}');

      if (response.statusCode == 200) {
        final responseData = response.data;
        LoggerService.d('🔍 分析入库单创建API响应格式:');
        LoggerService.d('   响应数据类型: ${responseData.runtimeType}');
        LoggerService.d('   是否包含success字段: ${responseData.containsKey('success')}');
        LoggerService.d('   是否包含data字段: ${responseData.containsKey('data')}');
        LoggerService.d('   是否包含id字段: ${responseData.containsKey('id')}');

        Map<String, dynamic>? stockInData;

        // 适配不同的API响应格式
        if (responseData.containsKey('success') && responseData['success'] == true && responseData['data'] != null) {
          // 格式1: {success: true, data: {id: xxx, ...}}
          stockInData = responseData['data'];
          LoggerService.d('📋 使用包装格式解析入库单数据');
        } else if (responseData.containsKey('id') && responseData['id'] != null) {
          // 格式2: {id: xxx, order_no: xxx, ...} (直接返回入库单对象)
          stockInData = responseData;
          LoggerService.d('📋 使用直接格式解析入库单数据');
        } else {
          LoggerService.e('❌ 无法从响应中解析入库单数据');
          LoggerService.e('   完整响应数据: $responseData');
          throw Exception('API返回数据格式错误：无法解析入库单数据');
        }

        if (stockInData != null && stockInData['id'] != null) {
          LoggerService.d('✅ 创建入库单成功');
          LoggerService.d('   入库单ID: ${stockInData['id']}');
          LoggerService.d('   入库单号: ${stockInData['order_no'] ?? '未知'}');

          // 验证返回的明细数量
          final returnedItems = stockInData['items'] as List? ?? [];
          LoggerService.d('   返回明细数量: ${returnedItems.length}');
          LoggerService.d('   期望明细数量: ${itemsData.length}');

          if (returnedItems.length != itemsData.length) {
            LoggerService.w('⚠️ 警告：返回的明细数量与期望不符');
          }

          return _mapStockInFromApi(stockInData);
        } else {
          LoggerService.e('❌ 入库单数据无效: $stockInData');
          throw Exception('API返回数据格式错误：入库单数据无效');
        }
      } else {
        LoggerService.e('❌ API调用失败: ${response.statusCode} - ${response.statusMessage}');
        throw Exception('API调用失败: ${response.statusMessage}');
      }
    } catch (e) {
      LoggerService.e('❌ 创建入库单失败', e);

      // 提供更详细的错误信息
      String errorMessage = '创建入库单失败';
      if (e.toString().contains('创建首饰失败')) {
        errorMessage = '创建商品记录失败，请检查商品信息';
      } else if (e.toString().contains('422')) {
        errorMessage = '请求参数验证失败，请检查入库单数据';
      } else if (e.toString().contains('404')) {
        errorMessage = '相关资源不存在，请检查门店和分类信息';
      } else if (e.toString().contains('401')) {
        errorMessage = '认证失败，请重新登录';
      } else if (e.toString().contains('500')) {
        errorMessage = '服务器内部错误，请稍后重试';
      }

      throw Exception(errorMessage);
    }
  }

  /// 创建入库单（兼容旧版本）
  Future<StockIn?> createStockIn(StockIn stockIn) async {
    try {
      LoggerService.d('🚀 开始创建入库单流程');
      LoggerService.d('📊 入库单基本信息:');
      LoggerService.d('   门店ID: ${stockIn.storeId}');
      LoggerService.d('   操作员ID: ${stockIn.operatorId}');
      LoggerService.d('   总金额: ${stockIn.totalAmount}');
      LoggerService.d('   商品数量: ${stockIn.items?.length ?? 0}');
      LoggerService.d('   备注: ${stockIn.remark}');

      // 第一步：为每个商品明细创建首饰记录
      final List<Map<String, dynamic>> itemsWithJewelryIds = [];

      for (int i = 0; i < (stockIn.items?.length ?? 0); i++) {
        final item = stockIn.items![i];
        LoggerService.d('📦 处理第${i + 1}/${stockIn.items!.length}个商品明细:');
        LoggerService.d('   商品名称: ${item.jewelry?.name ?? "未知商品"}');
        LoggerService.d('   条码: ${item.jewelry?.barcode ?? "无条码"}');
        LoggerService.d('   分类ID: ${item.jewelry?.categoryId ?? 0}');
        LoggerService.d('   金重: ${item.jewelry?.goldWeight ?? 0}');
        LoggerService.d('   银重: ${item.jewelry?.silverWeight ?? 0}');
        LoggerService.d('   工费: ${item.jewelry?.workPrice ?? 0}');
        LoggerService.d('   总成本: ${item.amount}');

        if (item.jewelry != null) {
          LoggerService.d('🔍 分析第${i + 1}个商品的数据来源:');
          LoggerService.d('   item类型: ${item.runtimeType}');
          LoggerService.d('   jewelry类型: ${item.jewelry.runtimeType}');

          // 从前端传递的数据中获取实际的价格信息
          // 由于架构限制，我们需要从StockIn对象的扩展信息中获取价格数据
          final jewelryData = item.jewelry!;

          LoggerService.d('📊 从jewelry对象获取基础数据:');
          LoggerService.d('   金重: ${jewelryData.goldWeight}');
          LoggerService.d('   银重: ${jewelryData.silverWeight}');
          LoggerService.d('   工费: ${jewelryData.workPrice}');
          LoggerService.d('   售价: ${jewelryData.salePrice}');

          // 尝试从stockIn的扩展信息中获取价格数据
          // 如果没有扩展信息，使用合理的默认价格
          double goldPrice = 520.0; // 当前金价，应该从系统配置获取
          double silverPrice = 8.0; // 当前银价，应该从系统配置获取
          double wholesaleWorkPrice = jewelryData.workPrice * 0.8; // 批发工费为工费的80%
          double retailWorkPrice = jewelryData.workPrice; // 零售工费为原工费
          double pieceWorkPrice = jewelryData.workPrice * 1.2; // 件工费为工费的120%
          double platingCost = item.amount * 0.05; // 电铸费按总成本的5%计算
          String ringSize = ''; // 圈口号，可以从扩展信息获取
          int silverWorkType = 0; // 默认按克计费

          // 检查是否有扩展的价格信息（通过stockIn的remark或其他字段传递）
          // 这是一个临时解决方案，理想情况下应该修改数据模型
          if (stockIn.remark != null && stockIn.remark!.contains('PRICE_DATA:')) {
            try {
              final priceDataStr = stockIn.remark!.split('PRICE_DATA:')[1].split('|')[0];
              final priceData = priceDataStr.split(',');
              if (priceData.length >= 6) {
                goldPrice = double.tryParse(priceData[0]) ?? goldPrice;
                silverPrice = double.tryParse(priceData[1]) ?? silverPrice;
                wholesaleWorkPrice = double.tryParse(priceData[2]) ?? wholesaleWorkPrice;
                retailWorkPrice = double.tryParse(priceData[3]) ?? retailWorkPrice;
                pieceWorkPrice = double.tryParse(priceData[4]) ?? pieceWorkPrice;
                platingCost = double.tryParse(priceData[5]) ?? platingCost;
                LoggerService.d('📋 从扩展信息获取到价格数据');
              }
            } catch (e) {
              LoggerService.w('解析扩展价格信息失败，使用默认价格: $e');
            }
          }

          LoggerService.d('💰 最终使用的价格信息:');
          LoggerService.d('   金价: $goldPrice元/g');
          LoggerService.d('   银价: $silverPrice元/g');
          LoggerService.d('   批发工费: $wholesaleWorkPrice元');
          LoggerService.d('   零售工费: $retailWorkPrice元');
          LoggerService.d('   件工费: $pieceWorkPrice元');
          LoggerService.d('   电铸费: $platingCost元');

          // 计算成本 - 按照正确的成本计算公式
          final goldCost = jewelryData.goldWeight * goldPrice;
          final silverCost = jewelryData.silverWeight * silverPrice;
          final totalWeight = jewelryData.goldWeight + jewelryData.silverWeight;

          // 计算银工费成本（根据工费方式）
          double silverWorkCost = 0.0;
          if (silverWorkType == 0) {
            // 按克计费：银重×银工费
            silverWorkCost = jewelryData.silverWeight * jewelryData.workPrice;
          } else {
            // 按件计费：银工费
            silverWorkCost = jewelryData.workPrice;
          }

          // 总成本计算公式
          // 按克计费：总成本 = 金重×金价 + 银重×银价 + 银重×银工费 + 电铸费
          // 按件计费：总成本 = 金重×金价 + 银重×银价 + 银工费 + 电铸费
          final calculatedTotalCost = goldCost + silverCost + silverWorkCost + platingCost;

          LoggerService.d('💎 成本计算详情:');
          LoggerService.d('   金成本: $goldCost元 (${jewelryData.goldWeight}g × $goldPrice元/g)');
          LoggerService.d('   银成本: $silverCost元 (${jewelryData.silverWeight}g × $silverPrice元/g)');
          LoggerService.d('   工费成本: $silverWorkCost元 (${silverWorkType == 0 ? "按克" : "按件"})');
          LoggerService.d('   电铸费: $platingCost元');
          LoggerService.d('   总成本: $calculatedTotalCost元');

          // 先创建首饰记录
          final jewelryRequestData = {
            'name': jewelryData.name,
            'barcode': jewelryData.barcode,
            'category_id': jewelryData.categoryId,
            'store_id': stockIn.storeId,
            'ring_size': ringSize,
            'gold_weight': jewelryData.goldWeight,
            'gold_price': goldPrice,
            'gold_cost': goldCost,
            'silver_weight': jewelryData.silverWeight,
            'total_weight': totalWeight,
            'silver_price': silverPrice,
            'silver_cost': silverCost,
            'silver_work_type': silverWorkType, // 银工费方式：0=按克，1=按件
            'silver_work_price': jewelryData.workPrice,
            'silver_work_cost': silverWorkCost, // 银工费成本
            'plating_cost': platingCost,
            'total_cost': calculatedTotalCost, // 使用计算出的总成本
            'wholesale_work_price': wholesaleWorkPrice,
            'retail_work_price': retailWorkPrice,
            'piece_work_price': pieceWorkPrice,
            'status': 1, // 在库状态
          };

          LoggerService.d('💎 创建第${i + 1}个首饰记录:');
          LoggerService.d('   请求数据: $jewelryRequestData');

          final jewelryResponse = await _apiClient.post(
            '${AppConfig.apiEndpoint['jewelry']}',
            data: jewelryRequestData,
          );

          LoggerService.d('📡 首饰创建API响应:');
          LoggerService.d('   状态码: ${jewelryResponse.statusCode}');
          LoggerService.d('   响应数据: ${jewelryResponse.data}');

          if (jewelryResponse.statusCode == 200) {
            final responseData = jewelryResponse.data;
            LoggerService.d('🔍 分析首饰创建API响应格式:');
            LoggerService.d('   响应数据类型: ${responseData.runtimeType}');
            LoggerService.d('   是否包含success字段: ${responseData.containsKey('success')}');
            LoggerService.d('   是否包含data字段: ${responseData.containsKey('data')}');
            LoggerService.d('   是否包含id字段: ${responseData.containsKey('id')}');

            int? jewelryId;

            // 适配不同的API响应格式
            if (responseData.containsKey('success') && responseData['success'] == true && responseData['data'] != null) {
              // 格式1: {success: true, data: {id: xxx, ...}}
              jewelryId = responseData['data']['id'];
              LoggerService.d('📋 使用包装格式解析，首饰ID: $jewelryId');
            } else if (responseData.containsKey('id') && responseData['id'] != null) {
              // 格式2: {id: xxx, barcode: xxx, name: xxx, ...} (直接返回首饰对象)
              jewelryId = responseData['id'];
              LoggerService.d('📋 使用直接格式解析，首饰ID: $jewelryId');
            } else {
              // 尝试从其他可能的字段获取ID
              LoggerService.e('❌ 无法从响应中获取首饰ID');
              LoggerService.e('   完整响应数据: $responseData');
              throw Exception('第${i + 1}个商品创建失败：无法从API响应中获取首饰ID');
            }

            if (jewelryId != null && jewelryId > 0) {
              LoggerService.d('✅ 第${i + 1}个首饰创建成功，ID: $jewelryId');

              // 构建入库单明细数据 - 确保每个商品都是独立的记录
              final itemData = {
                'jewelry_id': jewelryId,
                'barcode': jewelryData.barcode,
                'name': jewelryData.name,
                'category_id': jewelryData.categoryId,
                'ring_size': ringSize,
                'gold_weight': jewelryData.goldWeight,
                'gold_price': goldPrice,
                'gold_cost': goldCost,
                'silver_weight': jewelryData.silverWeight,
                'total_weight': totalWeight,
                'silver_price': silverPrice,
                'silver_cost': silverCost,
                'silver_work_type': silverWorkType, // 银工费方式
                'silver_work_price': jewelryData.workPrice,
                'silver_work_cost': silverWorkCost, // 银工费成本
                'plating_cost': platingCost,
                'total_cost': calculatedTotalCost, // 使用计算出的总成本
                'wholesale_work_price': wholesaleWorkPrice,
                'retail_work_price': retailWorkPrice,
                'piece_work_price': pieceWorkPrice,
              };

              LoggerService.d('📋 第${i + 1}个入库单明细数据: $itemData');
              itemsWithJewelryIds.add(itemData);

              LoggerService.d('✅ 第${i + 1}个商品处理完成，当前明细列表长度: ${itemsWithJewelryIds.length}');
            } else {
              throw Exception('第${i + 1}个商品创建失败：获取到的首饰ID无效 ($jewelryId)');
            }
          } else {
            throw Exception('第${i + 1}个商品创建失败：${jewelryResponse.statusMessage}');
          }
        } else {
          throw Exception('商品明细缺少jewelry信息');
        }
      }

      LoggerService.d('💎 所有首饰记录创建完成，共${itemsWithJewelryIds.length}个');

      // 验证批量处理结果
      if (itemsWithJewelryIds.length != (stockIn.items?.length ?? 0)) {
        throw Exception('批量处理失败：期望${stockIn.items?.length ?? 0}个商品，实际处理${itemsWithJewelryIds.length}个');
      }

      // 验证每个明细数据的完整性
      for (int i = 0; i < itemsWithJewelryIds.length; i++) {
        final itemData = itemsWithJewelryIds[i];
        LoggerService.d('🔍 验证第${i + 1}个明细数据:');
        LoggerService.d('   jewelry_id: ${itemData['jewelry_id']}');
        LoggerService.d('   barcode: ${itemData['barcode']}');
        LoggerService.d('   name: ${itemData['name']}');
        LoggerService.d('   total_cost: ${itemData['total_cost']}');

        if (itemData['jewelry_id'] == null || itemData['barcode'] == null || itemData['name'] == null) {
          throw Exception('第${i + 1}个明细数据不完整');
        }
      }

      // 第二步：创建入库单
      final requestData = {
        'store_id': stockIn.storeId,
        'supplier': '', // 供应商信息
        'remark': stockIn.remark ?? '',
        'items': itemsWithJewelryIds,
      };

      LoggerService.d('🌐 创建入库单API请求详情:');
      LoggerService.d('   URL: ${AppConfig.apiEndpoint['stockIn']}?operator_id=${stockIn.operatorId}');
      LoggerService.d('   Method: POST');
      LoggerService.d('   Store ID: ${requestData['store_id']}');
      LoggerService.d('   Items Count: ${(requestData['items'] as List).length}');
      LoggerService.d('   完整请求数据: $requestData');

      final response = await _apiClient.post(
        '${AppConfig.apiEndpoint['stockIn']}?operator_id=${stockIn.operatorId}',
        data: requestData,
      );

      LoggerService.d('📡 创建入库单API响应详情:');
      LoggerService.d('   Status Code: ${response.statusCode}');
      LoggerService.d('   Status Message: ${response.statusMessage}');
      LoggerService.d('   Response Data: ${response.data}');

      if (response.statusCode == 200) {
        final responseData = response.data;
        LoggerService.d('🔍 分析入库单创建API响应格式:');
        LoggerService.d('   响应数据类型: ${responseData.runtimeType}');
        LoggerService.d('   是否包含success字段: ${responseData.containsKey('success')}');
        LoggerService.d('   是否包含data字段: ${responseData.containsKey('data')}');
        LoggerService.d('   是否包含id字段: ${responseData.containsKey('id')}');

        Map<String, dynamic>? stockInData;

        // 适配不同的API响应格式
        if (responseData.containsKey('success') && responseData['success'] == true && responseData['data'] != null) {
          // 格式1: {success: true, data: {id: xxx, ...}}
          stockInData = responseData['data'];
          LoggerService.d('📋 使用包装格式解析入库单数据');
        } else if (responseData.containsKey('id') && responseData['id'] != null) {
          // 格式2: {id: xxx, order_no: xxx, ...} (直接返回入库单对象)
          stockInData = responseData;
          LoggerService.d('📋 使用直接格式解析入库单数据');
        } else {
          LoggerService.e('❌ 无法从响应中解析入库单数据');
          LoggerService.e('   完整响应数据: $responseData');
          throw Exception('API返回数据格式错误：无法解析入库单数据');
        }

        if (stockInData != null && stockInData['id'] != null) {
          LoggerService.d('✅ 创建入库单成功');
          LoggerService.d('   入库单ID: ${stockInData['id']}');
          LoggerService.d('   入库单号: ${stockInData['order_no'] ?? '未知'}');

          // 验证返回的明细数量
          final returnedItems = stockInData['items'] as List? ?? [];
          LoggerService.d('   返回明细数量: ${returnedItems.length}');
          LoggerService.d('   期望明细数量: ${itemsWithJewelryIds.length}');

          if (returnedItems.length != itemsWithJewelryIds.length) {
            LoggerService.w('⚠️ 警告：返回的明细数量与期望不符');
          }

          return _mapStockInFromApi(stockInData);
        } else {
          LoggerService.e('❌ 入库单数据无效: $stockInData');
          throw Exception('API返回数据格式错误：入库单数据无效');
        }
      } else {
        LoggerService.e('❌ API调用失败: ${response.statusCode} - ${response.statusMessage}');
        throw Exception('API调用失败: ${response.statusMessage}');
      }
    } catch (e) {
      LoggerService.e('❌ 创建入库单失败', e);

      // 提供更详细的错误信息
      String errorMessage = '创建入库单失败';
      if (e.toString().contains('创建首饰失败')) {
        errorMessage = '创建商品记录失败，请检查商品信息';
      } else if (e.toString().contains('422')) {
        errorMessage = '请求参数验证失败，请检查入库单数据';
      } else if (e.toString().contains('404')) {
        errorMessage = '相关资源不存在，请检查门店和分类信息';
      } else if (e.toString().contains('401')) {
        errorMessage = '认证失败，请重新登录';
      } else if (e.toString().contains('500')) {
        errorMessage = '服务器内部错误，请稍后重试';
      }

      throw Exception(errorMessage);
    }
  }

  /// 更新入库单
  Future<bool> updateStockIn(StockIn stockIn) async {
    try {
      LoggerService.d('🔄 更新入库单API请求详情:');
      LoggerService.d('   入库单ID: ${stockIn.id}');
      LoggerService.d('   门店ID: ${stockIn.storeId}');
      LoggerService.d('   商品数量: ${stockIn.items?.length ?? 0}');
      LoggerService.d('   当前状态: ${stockIn.status}');

      // 验证入库单状态，只有待审核状态或草稿状态的入库单才能编辑
      if (!stockIn.status.canEdit) {
        throw Exception('只有草稿或待审核状态的入库单才能编辑，当前状态：${stockIn.status.label}');
      }

      // 验证必要字段
      if (stockIn.id <= 0) {
        throw Exception('入库单ID无效');
      }

      if (stockIn.storeId <= 0) {
        throw Exception('请选择门店');
      }

      if (stockIn.items == null || stockIn.items!.isEmpty) {
        throw Exception('请添加商品明细');
      }

      // 🔍 数据完整性检查
      LoggerService.d('🔍 开始数据完整性检查...');
      for (int i = 0; i < stockIn.items!.length; i++) {
        final item = stockIn.items![i];
        LoggerService.d('   检查第${i + 1}个商品: ${item.name}');

        if (item.barcode.isEmpty) {
          throw Exception('第${i + 1}个商品的条码不能为空');
        }

        if (item.name.isEmpty) {
          throw Exception('第${i + 1}个商品的名称不能为空');
        }

        if (item.categoryId <= 0) {
          throw Exception('第${i + 1}个商品的分类ID无效');
        }

        if (item.totalCost == null || item.totalCost! <= 0) {
          throw Exception('第${i + 1}个商品的总成本无效');
        }

        LoggerService.d('   ✅ 第${i + 1}个商品数据检查通过');
      }
      LoggerService.d('✅ 所有商品数据完整性检查通过');

      // 构建更新请求数据，确保格式完全符合API要求
      final requestData = {
        'store_id': stockIn.storeId,
        'supplier': '', // StockIn模型中没有supplier字段，使用空字符串
        'remark': stockIn.remark ?? '',
        'items': stockIn.items!.map((item) {
          LoggerService.d('🔍 处理商品明细: ${item.name}');
          LoggerService.d('   jewelryId: ${item.jewelryId}');
          LoggerService.d('   barcode: ${item.barcode}');
          LoggerService.d('   totalCost: ${item.totalCost}');

          // 🔑 关键修复：完整的字段映射，包含所有工费相关字段
          LoggerService.d('   🔍 更新前字段值检查:');
          LoggerService.d('     silverWorkType: ${item.silverWorkType}');
          LoggerService.d('     silverWorkPrice: ${item.silverWorkPrice}');
          LoggerService.d('     platingCost: ${item.platingCost}');
          LoggerService.d('     wholesaleWorkPrice: ${item.wholesaleWorkPrice}');
          LoggerService.d('     retailWorkPrice: ${item.retailWorkPrice}');
          LoggerService.d('     pieceWorkPrice: ${item.pieceWorkPrice}');

          final Map<String, dynamic> itemData = {
            // 基础字段
            'barcode': item.barcode,
            'name': item.name,
            'category_id': item.categoryId,
            'ring_size': item.ringSize ?? '',

            // 金重相关字段
            'gold_weight': double.tryParse(item.goldWeight?.toString() ?? '0') ?? 0.0,
            'gold_price': double.tryParse(item.goldPrice?.toString() ?? '0') ?? 0.0,
            'gold_cost': double.tryParse(item.goldCost?.toString() ?? '0') ?? 0.0,

            // 银重相关字段
            'silver_weight': double.tryParse(item.silverWeight?.toString() ?? '0') ?? 0.0,
            'total_weight': double.tryParse(item.totalWeight?.toString() ?? '0') ?? 0.0,
            'silver_price': double.tryParse(item.silverPrice?.toString() ?? '0') ?? 0.0,
            'silver_cost': double.tryParse(item.silverCost?.toString() ?? '0') ?? 0.0,

            // 🚨 关键修复：工费相关字段（之前缺失导致数据丢失）
            'silver_work_type': item.silverWorkType ?? 0, // 工费方式：0=按克，1=按件
            'silver_work_price': double.tryParse(item.silverWorkPrice?.toString() ?? '0') ?? 0.0,
            'silver_work_cost': double.tryParse(item.silverWorkCost?.toString() ?? '0') ?? 0.0,
            'plating_cost': double.tryParse(item.platingCost?.toString() ?? '0') ?? 0.0,

            // 🚨 关键修复：各类工费字段（之前缺失导致数据丢失）
            'wholesale_work_price': double.tryParse(item.wholesaleWorkPrice?.toString() ?? '0') ?? 0.0,
            'retail_work_price': double.tryParse(item.retailWorkPrice?.toString() ?? '0') ?? 0.0,
            'piece_work_price': double.tryParse(item.pieceWorkPrice?.toString() ?? '0') ?? 0.0,

            // 成本和价格字段
            'total_cost': double.tryParse(item.totalCost?.toString() ?? '0') ?? 0.0,
            'price': double.tryParse(item.amount.toString()) ?? 0.0,
          };

          LoggerService.d('   ✅ 更新后字段值确认:');
          LoggerService.d('     silver_work_type: ${itemData['silver_work_type']}');
          LoggerService.d('     silver_work_price: ${itemData['silver_work_price']}');
          LoggerService.d('     plating_cost: ${itemData['plating_cost']}');
          LoggerService.d('     wholesale_work_price: ${itemData['wholesale_work_price']}');
          LoggerService.d('     retail_work_price: ${itemData['retail_work_price']}');
          LoggerService.d('     piece_work_price: ${itemData['piece_work_price']}');

          // 🔑 关键修复：jewelry_id字段处理
          // 根据后端逻辑，jewelry_id在未审核时使用占位符值-1
          // 只有在审核通过后才会有真正的jewelry_id
          if (item.jewelryId > 0) {
            // 有效的jewelry_id，直接使用
            itemData['jewelry_id'] = item.jewelryId;
            LoggerService.d('   ✅ 使用有效的jewelry_id: ${item.jewelryId}');
          } else {
            // 无效或为0的jewelry_id，使用后端的占位符值-1
            itemData['jewelry_id'] = -1; // 后端的PLACEHOLDER_JEWELRY_ID
            LoggerService.d('   🔧 使用占位符jewelry_id: -1');
          }

          // 对item.id也做类似处理，只有当ID大于0时才添加
          if (item.id > 0) {
            itemData['id'] = item.id;
            LoggerService.d('   ✅ 使用有效的item.id: ${item.id}');
          }

          LoggerService.d('   📋 最终itemData: $itemData');
          return itemData;
        }).toList(),
      };

      final url = '${AppConfig.apiEndpoint['stockIn']}/${stockIn.id}';
      
      LoggerService.d('🔄 更新入库单API请求详情:');
      LoggerService.d('   URL: $url');
      LoggerService.d('   Method: PUT');
      LoggerService.d('   请求数据: $requestData');

      // 重要：注意PUT方法的使用和数据传递
      final response = await _apiClient.put(url, data: requestData);

      LoggerService.d('📡 更新入库单API响应详情:');
      LoggerService.d('   Status Code: ${response.statusCode}');
      LoggerService.d('   Status Message: ${response.statusMessage}');
      LoggerService.d('   响应数据: ${response.data}');

      if (response.statusCode == 200) {
        final responseData = response.data;
        if (responseData != null && responseData['success'] == true) {
          LoggerService.d('✅ 更新入库单成功');
          LoggerService.d('   更新后的入库单: ${responseData['data']?['order_no'] ?? '未知'}');
          return true;
        } else {
          final errorMsg = responseData?['message'] ?? '更新失败';
          LoggerService.e('❌ 更新入库单失败: $errorMsg');
          throw Exception(errorMsg);
        }
      } else {
        LoggerService.e('❌ 更新入库单HTTP错误: ${response.statusCode} - ${response.statusMessage}');

        // 根据状态码提供更具体的错误信息
        String errorMessage;
        switch (response.statusCode) {
          case 400:
            errorMessage = '请求参数错误，请检查输入数据';
            break;
          case 401:
            errorMessage = '认证失败，请重新登录';
            break;
          case 403:
            errorMessage = '权限不足，无法更新入库单';
            break;
          case 404:
            errorMessage = '入库单不存在';
            break;
          case 422:
            errorMessage = '数据验证失败，请检查输入格式';
            break;
          case 500:
            errorMessage = '服务器内部错误，请稍后重试';
            break;
          default:
            errorMessage = 'HTTP错误: ${response.statusCode}';
        }
        throw Exception(errorMessage);
      }
    } catch (e) {
      LoggerService.e('❌ 更新入库单异常', e);

      // 提供更详细的错误信息
      if (e.toString().contains('DioException')) {
        LoggerService.e('   网络请求异常: $e');
        throw Exception('网络连接失败，请检查网络设置');
      } else if (e.toString().contains('SocketException')) {
        LoggerService.e('   网络连接异常: $e');
        throw Exception('无法连接到服务器，请检查网络');
      } else if (e.toString().contains('TimeoutException')) {
        LoggerService.e('   请求超时: $e');
        throw Exception('请求超时，请稍后重试');
      } else if (e.toString().contains('首饰ID') && e.toString().contains('不存在')) {
        LoggerService.e('   首饰ID验证失败: $e');
        throw Exception('商品数据异常，请重新加载入库单后再试');
      } else if (e.toString().contains('jewelry_id')) {
        LoggerService.e('   商品ID相关错误: $e');
        throw Exception('商品信息不完整，请检查商品数据');
      } else {
        // 如果是我们自己抛出的异常，直接重新抛出
        rethrow;
      }
    }
  }

  /// 删除入库单
  Future<bool> deleteStockIn(int id) async {
    try {
      final url = '${AppConfig.apiEndpoint['stockIn']}/$id';

      LoggerService.d('🗑️ 删除入库单API请求详情:');
      LoggerService.d('   URL: $url');
      LoggerService.d('   Method: DELETE');
      LoggerService.d('   入库单ID: $id');

      final response = await _apiClient.delete(url);

      LoggerService.d('📡 删除入库单API响应详情:');
      LoggerService.d('   Status Code: ${response.statusCode}');
      LoggerService.d('   Status Message: ${response.statusMessage}');
      LoggerService.d('   Response Data: ${response.data}');

      if (response.statusCode == 200) {
        final responseData = response.data;
        if (responseData != null && responseData['success'] == true) {
          LoggerService.d('✅ 删除入库单成功');
          return true;
        } else {
          final errorMsg = responseData?['message'] ?? '删除失败';
          LoggerService.e('❌ 删除入库单失败: $errorMsg');
          throw Exception(errorMsg);
        }
      } else {
        LoggerService.e('❌ API调用失败: ${response.statusCode} - ${response.statusMessage}');
        throw Exception('API调用失败: ${response.statusMessage}');
      }
    } catch (e) {
      LoggerService.e('❌ 删除入库单异常', e);

      // 提供更详细的错误信息
      if (e.toString().contains('DioException')) {
        LoggerService.e('   网络请求异常: $e');
      } else if (e.toString().contains('404')) {
        LoggerService.e('   入库单不存在: $e');
      } else if (e.toString().contains('403')) {
        LoggerService.e('   权限不足: $e');
      } else if (e.toString().contains('400')) {
        LoggerService.e('   入库单状态不允许删除: $e');
      } else if (e.toString().contains('422')) {
        LoggerService.e('   数据验证失败: $e');
      }

      rethrow;
    }
  }

  /// 提交入库单审核
  Future<bool> submitStockInForApproval(int id, int auditorId) async {
    try {
      final url = '${AppConfig.apiEndpoint['stockIn']}/$id/status?auditor_id=$auditorId';
      final requestData = {
        'status': 1, // 1=待审核状态（提交审核）
        'audit_note': '提交审核',
      };

      LoggerService.d('🌐 API请求详情:');
      LoggerService.d('   URL: $url');
      LoggerService.d('   Method: PATCH');
      LoggerService.d('   Data: $requestData');

      final response = await _apiClient.patch(url, data: requestData);

      LoggerService.d('📡 API响应详情:');
      LoggerService.d('   Status Code: ${response.statusCode}');
      LoggerService.d('   Status Message: ${response.statusMessage}');
      LoggerService.d('   Response Data: ${response.data}');

      if (response.statusCode == 200) {
        LoggerService.d('✅ 提交入库单审核成功');
        return true;
      } else {
        LoggerService.e('❌ API调用失败: ${response.statusCode} - ${response.statusMessage}');
        throw Exception('API调用失败: ${response.statusMessage}');
      }
    } catch (e) {
      LoggerService.e('❌ 提交入库单审核失败', e);

      // 提供更详细的错误信息
      if (e.toString().contains('DioException')) {
        LoggerService.e('   网络请求异常: $e');
      } else if (e.toString().contains('422')) {
        LoggerService.e('   请求参数验证失败: $e');
      }

      rethrow;
    }
  }

  /// 审核入库单
  Future<bool> approveStockIn(int id, bool isApproved, String? reason, int auditorId) async {
    try {
      final url = '${AppConfig.apiEndpoint['stockIn']}/$id/status?auditor_id=$auditorId';
      final requestData = {
        'status': isApproved ? 2 : 3, // 2=已通过，3=已拒绝
        'audit_note': reason ?? (isApproved ? '审核通过' : '审核拒绝'),
      };

      LoggerService.d('🚀 [前端审核] 开始审核入库单');
      LoggerService.d('   入库单ID: $id');
      LoggerService.d('   审核员ID: $auditorId');
      LoggerService.d('   审核结果: ${isApproved ? "通过" : "拒绝"}');
      LoggerService.d('   审核备注: ${reason ?? "无"}');
      LoggerService.d('   API URL: $url');
      LoggerService.d('   HTTP Method: PATCH');
      LoggerService.d('   请求数据: $requestData');

      final response = await _apiClient.patch(url, data: requestData);

      LoggerService.d('📡 [前端审核] API响应详情:');
      LoggerService.d('   Status Code: ${response.statusCode}');
      LoggerService.d('   Status Message: ${response.statusMessage}');
      LoggerService.d('   Response Headers: ${response.headers}');
      LoggerService.d('   Response Data: ${response.data}');

      if (response.statusCode == 200) {
        final responseData = response.data;
        if (responseData != null && responseData['success'] == true) {
          LoggerService.d('✅ [前端审核] 审核成功');
          LoggerService.d('   响应消息: ${responseData['message']}');
          LoggerService.d('   返回数据: ${responseData['data'] != null ? "有数据" : "无数据"}');
          return true;
        } else {
          final errorMsg = responseData?['message'] ?? '审核失败';
          LoggerService.e('❌ [前端审核] 审核失败: $errorMsg');
          LoggerService.e('   完整响应: $responseData');
          throw Exception(errorMsg);
        }
      } else {
        LoggerService.e('❌ [前端审核] HTTP错误: ${response.statusCode} - ${response.statusMessage}');
        LoggerService.e('   错误响应: ${response.data}');
        throw Exception('API调用失败: ${response.statusMessage}');
      }
    } catch (e) {
      LoggerService.e('❌ [前端审核] 异常捕获', e);

      // 提供更详细的错误信息
      if (e.toString().contains('DioException')) {
        LoggerService.e('   网络请求异常: $e');
      } else if (e.toString().contains('404')) {
        LoggerService.e('   入库单不存在: $e');
      } else if (e.toString().contains('403')) {
        LoggerService.e('   权限不足: $e');
      } else if (e.toString().contains('401')) {
        LoggerService.e('   身份验证失败: $e');
      } else if (e.toString().contains('400')) {
        LoggerService.e('   请求参数错误: $e');
      } else if (e.toString().contains('422')) {
        LoggerService.e('   数据验证失败: $e');
      } else if (e.toString().contains('500')) {
        LoggerService.e('   服务器内部错误: $e');
      }

      rethrow;
    }
  }

  /// 取消审核入库单（撤销已审核状态）
  Future<bool> cancelApproveStockIn(int id, String reason, int auditorId) async {
    try {
      final url = '${AppConfig.apiEndpoint['stockIn']}/$id/status?auditor_id=$auditorId';
      final requestData = {
        'status': 1, // 1=待审核状态（撤销审核回到待审核）
        'audit_note': '取消审核: $reason',
      };

      LoggerService.d('🚀 [前端取消审核] 开始取消审核入库单');
      LoggerService.d('   入库单ID: $id');
      LoggerService.d('   审核员ID: $auditorId');
      LoggerService.d('   取消原因: $reason');
      LoggerService.d('   API URL: $url');
      LoggerService.d('   HTTP Method: PATCH');
      LoggerService.d('   请求数据: $requestData');

      final response = await _apiClient.patch(url, data: requestData);

      LoggerService.d('📡 [前端取消审核] API响应详情:');
      LoggerService.d('   Status Code: ${response.statusCode}');
      LoggerService.d('   Status Message: ${response.statusMessage}');
      LoggerService.d('   Response Headers: ${response.headers}');
      LoggerService.d('   Response Data: ${response.data}');

      if (response.statusCode == 200) {
        final responseData = response.data;
        if (responseData != null && responseData['success'] == true) {
          LoggerService.d('✅ [前端取消审核] 取消审核成功');
          LoggerService.d('   响应消息: ${responseData['message']}');
          LoggerService.d('   返回数据: ${responseData['data'] != null ? "有数据" : "无数据"}');
          return true;
        } else {
          final errorMsg = responseData?['message'] ?? '取消审核失败';
          LoggerService.e('❌ [前端取消审核] 取消审核失败: $errorMsg');
          LoggerService.e('   完整响应: $responseData');
          throw Exception(errorMsg);
        }
      } else {
        LoggerService.e('❌ [前端取消审核] HTTP错误: ${response.statusCode} - ${response.statusMessage}');
        LoggerService.e('   错误响应: ${response.data}');
        throw Exception('API调用失败: ${response.statusMessage}');
      }

    } catch (e) {
      LoggerService.e('❌ [前端取消审核] 异常捕获', e);

      // 提供更详细的错误信息
      if (e.toString().contains('DioException')) {
        LoggerService.e('   网络请求异常: $e');
      } else if (e.toString().contains('404')) {
        LoggerService.e('   入库单不存在: $e');
      } else if (e.toString().contains('403')) {
        LoggerService.e('   权限不足: $e');
      } else if (e.toString().contains('401')) {
        LoggerService.e('   身份验证失败: $e');
      } else if (e.toString().contains('400')) {
        LoggerService.e('   请求参数错误: $e');
      } else if (e.toString().contains('422')) {
        LoggerService.e('   数据验证失败: $e');
      } else if (e.toString().contains('500')) {
        LoggerService.e('   服务器内部错误: $e');
      }

      rethrow;
    }
  }

  /// 获取出库单列表
  Future<PageData<StockOut>> getStockOutList(Map<String, dynamic> params) async {
    try {
      final response = await _apiClient.get(
        '${AppConfig.apiEndpoint['stockOut']}',
        queryParameters: params,
      );

      if (response.statusCode == 200) {
        final responseData = response.data;
        final List<dynamic> stockOutList = responseData['data'] ?? [];
        final pagination = responseData['pagination'] ?? {};

        return PageData<StockOut>(
          data: stockOutList.map((json) => _mapStockOutFromApi(json)).toList(),
          currentPage: pagination['page'] ?? 1,
          lastPage: pagination['pages'] ?? 1,
          total: pagination['total'] ?? 0,
        );
      } else {
        throw Exception('API调用失败: ${response.statusMessage}');
      }
    } catch (e) {
      LoggerService.e('获取出库单列表失败', e);
      rethrow;
    }
  }

  /// 获取出库单详情
  Future<StockOut> getStockOutById(int id) async {
    try {
      final response = await _apiClient.get('${AppConfig.apiEndpoint['stockOut']}/$id');

      if (response.statusCode == 200) {
        final data = response.data;
        LoggerService.d('🔍 [DEBUG] API原始响应数据: ${data.toString()}');
        LoggerService.d('🔍 [DEBUG] 响应数据类型: ${data.runtimeType}');

        // 检查响应数据是否为空
        if (data == null) {
          LoggerService.e('❌ 获取出库单详情失败: 服务器返回空数据');
          throw Exception('获取出库单详情失败: 服务器返回空数据');
        }

        LoggerService.d('🔍 [DEBUG] items字段类型: ${data['items']?.runtimeType}');
        LoggerService.d('🔍 [DEBUG] items字段值: ${data['items']}');

        // 直接使用StockOut.fromJson方法，它能正确处理items字段
        return StockOut.fromJson(data);
      } else {
        throw Exception('API调用失败: ${response.statusMessage}');
      }
    } catch (e) {
      LoggerService.e('获取出库单详情失败', e);
      rethrow;
    }
  }

  /// 创建出库单并收款 - 根据收款方案.md实现简化流程
  ///
  /// 简化的业务流程：
  /// 1. 创建出库单（状态为待审核）
  /// 2. 立即调用收款API（同时完成收款确认和审核通过）
  Future<int?> createStockOutWithPayment(StockOut stockOut, Map<String, dynamic> paymentData) async {
    try {
      LoggerService.d('🚀 [收款结算] 开始创建出库单并收款');
      LoggerService.d('📦 [收款结算] 出库单数据: 门店ID=${stockOut.storeId}, 商品数量=${stockOut.items?.length ?? 0}');
      LoggerService.d('💰 [收款结算] 收款数据: $paymentData');

      // 第一步：创建出库单
      final createResponse = await _apiClient.post(
        '${AppConfig.apiEndpoint['stockOut']}?operator_id=${stockOut.operatorId}',
        data: {
          'store_id': stockOut.storeId,
          'customer': stockOut.customer ?? '',
          'sale_type': stockOut.saleType ?? 'retail',
          'remark': stockOut.remark,
          'items': stockOut.items?.map((item) => _buildStockOutItemData(item)).toList() ?? [],
        },
      );

      LoggerService.d('📡 [收款结算] 创建出库单API响应: ${createResponse.statusCode}');

      if (createResponse.statusCode != 200) {
        throw Exception('创建出库单失败: ${createResponse.statusMessage}');
      }

      // 检查响应数据是否为空
      if (createResponse.data == null) {
        LoggerService.e('❌ [收款结算] 创建出库单失败: 服务器返回空数据');
        throw Exception('创建出库单失败: 服务器返回空数据');
      }

      LoggerService.d('📋 [收款结算] 创建出库单响应数据类型: ${createResponse.data.runtimeType}');
      LoggerService.d('📋 [收款结算] 创建出库单响应数据: ${createResponse.data}');
      LoggerService.d('📋 [收款结算] 响应状态码: ${createResponse.statusCode}');
      LoggerService.d('📋 [收款结算] 响应头: ${createResponse.headers}');

      // 检查响应数据结构
      int? stockOutId;
      if (createResponse.data is Map<String, dynamic>) {
        final responseData = createResponse.data as Map<String, dynamic>;
        LoggerService.d('📋 [收款结算] 响应数据是Map，键: ${responseData.keys.toList()}');

        // 尝试不同的可能字段
        if (responseData.containsKey('id')) {
          stockOutId = responseData['id'] as int?;
          LoggerService.d('📋 [收款结算] 从id字段获取: $stockOutId');
        } else if (responseData.containsKey('data') && responseData['data'] is Map) {
          final dataMap = responseData['data'] as Map<String, dynamic>;
          stockOutId = dataMap['id'] as int?;
          LoggerService.d('📋 [收款结算] 从data.id字段获取: $stockOutId');
        } else {
          LoggerService.e('❌ [收款结算] 响应数据中没有找到id字段');
          LoggerService.e('📋 [收款结算] 可用字段: ${responseData.keys.toList()}');
        }
      } else {
        LoggerService.e('❌ [收款结算] 响应数据不是Map类型: ${createResponse.data.runtimeType}');
      }
      if (stockOutId == null) {
        LoggerService.e('❌ [收款结算] 创建出库单失败: 无法获取出库单ID');
        LoggerService.e('📋 [收款结算] 响应数据详情: ${createResponse.data}');
        throw Exception('创建出库单失败: 无法获取出库单ID');
      }

      LoggerService.d('✅ [收款结算] 出库单创建成功，ID: $stockOutId');

      // 第二步：立即调用收款API
      LoggerService.d('💰 [收款结算] 开始调用收款API');
      final paymentResponse = await _apiClient.patch(
        '${AppConfig.apiEndpoint['stockOut']}/$stockOutId/payment',
        data: paymentData,
      );

      LoggerService.d('📡 [收款结算] 收款API响应: ${paymentResponse.statusCode}');

      if (paymentResponse.statusCode != 200) {
        LoggerService.e('❌ [收款结算] 收款处理失败: ${paymentResponse.statusMessage}');
        throw Exception('收款处理失败: ${paymentResponse.statusMessage}');
      }

      LoggerService.d('✅ [收款结算] 收款处理成功');
      return stockOutId;
    } catch (e) {
      LoggerService.e('❌ [收款结算] 创建出库单并收款失败', e);
      rethrow;
    }
  }

  /// 构建出库单明细数据
  Map<String, dynamic> _buildStockOutItemData(StockOutItem item) {
    return {
      'jewelry_id': item.jewelryId,
      'barcode': item.barcode,
      'name': item.jewelry?.name ?? '',
      'category_id': item.jewelry?.categoryId ?? 0,
      'ring_size': item.jewelry?.ringSize ?? '',
      'gold_weight': (item.jewelry?.goldWeight ?? 0.0).toString(),
      'gold_price': (item.jewelry?.goldPrice ?? 0.0).toString(),
      'silver_weight': (item.jewelry?.silverWeight ?? 0.0).toString(),
      'silver_price': (item.jewelry?.silverPrice ?? 0.0).toString(),
      'total_weight': (item.jewelry?.totalWeight ?? 0.0).toString(),
      'piece_work_price': (item.jewelry?.pieceWorkPrice ?? 0.0).toString(),
      'total_amount': item.amount.toString(),
      'sale_type': 'retail', // 默认零售类型
      'work_price': (item.jewelry?.workPrice ?? 0.0).toString(),
    };
  }

  /// 创建出库单（原有方法，保留用于草稿保存）
  Future<bool> createStockOut(StockOut stockOut) async {
    try {
      final response = await _apiClient.post(
        '${AppConfig.apiEndpoint['stockOut']}?operator_id=${stockOut.operatorId}',
        data: {
          'store_id': stockOut.storeId,
          'customer': stockOut.customer ?? '',
          'sale_type': stockOut.saleType ?? 'retail',
          'remark': stockOut.remark,
          'items': stockOut.items?.map((item) => {
            'jewelry_id': item.jewelryId,
            'barcode': item.barcode,
            'amount': item.amount,
          }).toList() ?? [],
        },
      );

      if (response.statusCode == 200) {
        return true;
      } else {
        throw Exception('API调用失败: ${response.statusMessage}');
      }
    } catch (e) {
      LoggerService.e('创建出库单失败', e);
      rethrow;
    }
  }

  /// 更新出库单
  Future<bool> updateStockOut(StockOut stockOut) async {
    try {
      final response = await _apiClient.put(
        '${AppConfig.apiEndpoint['stockOut']}/${stockOut.id}',
        data: {
          'store_id': stockOut.storeId,
          'customer': stockOut.customer ?? '',
          'sale_type': stockOut.saleType ?? 'retail',
          'remark': stockOut.remark,
          'items': stockOut.items?.map((item) => {
            'jewelry_id': item.jewelryId,
            'barcode': item.barcode,
            'amount': item.amount,
          }).toList() ?? [],
        },
      );

      if (response.statusCode == 200) {
        return true;
      } else {
        throw Exception('API调用失败: ${response.statusMessage}');
      }
    } catch (e) {
      LoggerService.e('更新出库单失败', e);
      rethrow;
    }
  }

  /// 删除出库单
  Future<bool> deleteStockOut(int id) async {
    try {
      final response = await _apiClient.delete('${AppConfig.apiEndpoint['stockOut']}/$id');

      if (response.statusCode == 200) {
        return true;
      } else {
        throw Exception('API调用失败: ${response.statusMessage}');
      }
    } catch (e) {
      LoggerService.e('删除出库单失败', e);
      rethrow;
    }
  }


  /// 审核出库单
  Future<bool> approveStockOut(int id, bool isApproved, String? reason) async {
    try {
      // 根据后端API文档，审核员ID通过查询参数传递
      final response = await _apiClient.patch(
        '${AppConfig.apiEndpoint['stockOut']}/$id/audit?auditor_id=1',
        data: {
          'status': isApproved ? 2 : 3, // 2=已通过, 3=未通过
          'audit_remark': reason,
        },
      );

      if (response.statusCode == 200) {
        return true;
      } else {
        throw Exception('API调用失败: ${response.statusMessage}');
      }
    } catch (e) {
      LoggerService.e('审核出库单失败', e);
      rethrow;
    }
  }

  /// 从API数据映射出库单对象
  StockOut _mapStockOutFromApi(Map<String, dynamic>? json) {
    // 空值检查
    if (json == null) {
      throw ArgumentError('_mapStockOutFromApi: json参数不能为null');
    }
    // 创建Store对象（如果有门店信息）
    Store? store;
    if (json['store_name'] != null) {
      store = Store(
        id: json['store_id'] ?? 0,
        name: json['store_name'],
        code: null,
        address: null,
        phone: null,
        manager: null,
      );
    }

    // 处理商品明细数据
    List<StockOutItem>? items;
    if (json['items'] != null && json['items'] is List) {
      items = (json['items'] as List).map((itemJson) => _mapStockOutItemFromApi(itemJson)).toList();
    }

    return StockOut(
      id: json['id'] ?? 0,
      stockOutNo: json['order_no'] ?? '',
      storeId: json['store_id'] ?? 0,
      totalAmount: _parseDouble(json['total_amount']),
      operatorId: json['operator_id'] ?? 0,
      status: _parseDocumentStatus(json['status']),
      createTime: _parseDateTime(json['createtime']),
      remark: json['remark'],
      customer: json['customer'],
      saleType: json['sale_type'],
      totalWeight: _parseDouble(json['total_weight']),
      store: store,
      items: items,
      // 添加支付相关字段
      paymentStatus: json['payment_status'],
      paymentTime: _parseDateTime(json['payment_time']),
      paymentMethod: json['payment_method'],
      paymentRemark: json['payment_remark'],
      cashAmount: _parseDouble(json['cash_amount']),
      wechatAmount: _parseDouble(json['wechat_amount']),
      alipayAmount: _parseDouble(json['alipay_amount']),
      cardAmount: _parseDouble(json['card_amount']),
      discountAmount: _parseDouble(json['discount_amount']),
      actualAmount: _parseDouble(json['actual_amount']),
      operatorName: json['operator_name'],
    );
  }

  /// 从API数据映射出库单商品明细对象
  StockOutItem _mapStockOutItemFromApi(Map<String, dynamic>? json) {
    // 空值检查
    if (json == null) {
      throw ArgumentError('_mapStockOutItemFromApi: json参数不能为null');
    }
    // 处理首饰信息
    Jewelry? jewelry;
    if (json['jewelry'] != null) {
      final jewelryJson = json['jewelry'];

      // 处理分类信息
      JewelryCategory? category;
      if (jewelryJson['category'] != null) {
        final categoryJson = jewelryJson['category'];
        category = JewelryCategory(
          id: categoryJson['id'] ?? 0,
          name: categoryJson['name'] ?? '',
          parentId: categoryJson['pid'] ?? categoryJson['parent_id'] ?? 0,
          sortOrder: categoryJson['weigh'] ?? categoryJson['sort_order'] ?? 0,
          isActive: (categoryJson['status'] ?? 1) == 1,
        );
      }

      jewelry = Jewelry(
        id: jewelryJson['id'] ?? 0,
        name: jewelryJson['name'] ?? '',
        barcode: jewelryJson['barcode'] ?? '',
        categoryId: jewelryJson['category_id'] ?? 0,
        goldWeight: _parseDouble(jewelryJson['gold_weight']),
        silverWeight: _parseDouble(jewelryJson['silver_weight']),
        totalWeight: _parseDouble(jewelryJson['total_weight']),
        goldPrice: _parseDouble(jewelryJson['gold_price']),
        silverPrice: _parseDouble(jewelryJson['silver_price']),
        workPrice: _parseDouble(jewelryJson['work_price']),
        retailWorkPrice: _parseDouble(jewelryJson['retail_work_price']),
        wholesaleWorkPrice: _parseDouble(jewelryJson['wholesale_work_price']),
        pieceWorkPrice: _parseDouble(jewelryJson['piece_work_price']),
        salePrice: _parseDouble(jewelryJson['sale_price']),
        storeId: jewelryJson['store_id'] ?? 0,
        status: JewelryStatus.fromValue(jewelryJson['status'] ?? 1),
        createTime: _parseDateTime(jewelryJson['createtime']),
        category: category,
      );
    }

    return StockOutItem(
      id: json['id'] ?? 0,
      stockOutId: json['stock_out_id'] ?? 0,
      jewelryId: json['jewelry_id'] ?? 0,
      barcode: json['barcode'] ?? '',
      amount: _parseDouble(json['amount']),
      jewelry: jewelry,
    );
  }
}