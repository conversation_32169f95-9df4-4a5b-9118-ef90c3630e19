import 'package:flutter/material.dart';

/// 黄金珠宝管理系统统一边框样式常量
/// 
/// 这个文件定义了整个应用中使用的所有边框样式常量
/// 使用这些常量可以确保UI的一致性，并且简化未来的样式调整
class AppBorderStyles {
  // 私有构造函数，防止实例化
  AppBorderStyles._();

  /// 标准边框颜色
  static const Color borderColor = Color(0xFFE0E0E0); // 浅灰色边框

  /// 焦点边框颜色
  static const Color focusBorderColor = Color(0xFF1E88E5); // 蓝色焦点边框

  /// 标准边框宽度
  static const double borderWidth = 1.0;
  
  /// 焦点边框宽度
  static const double focusBorderWidth = 2.0;

  /// 标准圆角半径
  static const double borderRadius = 6.0;

  /// 中等圆角半径
  static const double mediumBorderRadius = 8.0;

  /// 大圆角半径
  static const double largeBorderRadius = 12.0;

  /// 标准边框
  static const BorderSide standardBorder = BorderSide(
    color: borderColor,
    width: borderWidth,
  );

  /// 焦点边框
  static const BorderSide focusBorder = BorderSide(
    color: focusBorderColor,
    width: focusBorderWidth,
  );

  /// 表格边框
  static const BorderSide tableBorder = BorderSide(
    color: Color(0xFFEEEEEE),
    width: 0.5,
  );

  /// 表格边框 - 暗色
  static const BorderSide tableBorderDark = BorderSide(
    color: Color(0xFFDDDDDD),
    width: 0.5,
  );

  /// 表格表头背景色
  static const Color tableHeaderBackground = Color(0xFFF5F5F5);

  /// 表格奇数行背景色
  static const Color tableOddRowBackground = Colors.white;
  
  /// 表格偶数行背景色
  static const Color tableEvenRowBackground = Color(0xFFF9F9F9);

  /// 标准输入框边框样式
  static InputDecoration get standardInputDecoration => InputDecoration(
    contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(borderRadius),
      borderSide: standardBorder,
    ),
    enabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(borderRadius),
      borderSide: standardBorder,
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(borderRadius),
      borderSide: focusBorder,
    ),
    errorBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(borderRadius),
      borderSide: const BorderSide(color: Colors.red, width: borderWidth),
    ),
    focusedErrorBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(borderRadius),
      borderSide: const BorderSide(color: Colors.red, width: focusBorderWidth),
    ),
  );

  /// 紧凑型输入框边框样式
  static InputDecoration get compactInputDecoration => InputDecoration(
    contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(borderRadius),
      borderSide: standardBorder,
    ),
    enabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(borderRadius),
      borderSide: standardBorder,
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(borderRadius),
      borderSide: focusBorder,
    ),
    errorBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(borderRadius),
      borderSide: const BorderSide(color: Colors.red, width: borderWidth),
    ),
    focusedErrorBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(borderRadius),
      borderSide: const BorderSide(color: Colors.red, width: focusBorderWidth),
    ),
    isDense: true,
  );

  /// 表格标准边框
  static TableBorder get tableStandardBorder => TableBorder.all(
    color: borderColor,
    width: 0.5,
  );

  /// 标准边框的盒子装饰
  static BoxDecoration get standardBoxDecoration => BoxDecoration(
    border: Border.all(color: borderColor, width: borderWidth),
    borderRadius: BorderRadius.circular(borderRadius),
  );

  /// 带阴影的盒子装饰
  static BoxDecoration get elevatedBoxDecoration => BoxDecoration(
    border: Border.all(color: borderColor, width: borderWidth),
    borderRadius: BorderRadius.circular(borderRadius),
    boxShadow: [
      BoxShadow(
        color: Colors.black.withOpacity(0.05),
        blurRadius: 4,
        offset: const Offset(0, 2),
      ),
    ],
  );

  /// 表格单元格样式
  static BoxDecoration cellDecoration({bool isEven = false}) => BoxDecoration(
    color: isEven ? tableEvenRowBackground : tableOddRowBackground,
    border: const Border(
      right: tableBorder,
      bottom: tableBorder,
    ),
  );

  /// 表格表头单元格样式
  static BoxDecoration headerCellDecoration() => const BoxDecoration(
    color: tableHeaderBackground,
    border: Border(
      right: tableBorderDark,
      bottom: tableBorderDark,
    ),
  );
} 