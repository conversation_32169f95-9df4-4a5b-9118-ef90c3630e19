"""
销售管理相关的数据模型和验证
"""

from typing import Optional, List
from enum import Enum
from pydantic import BaseModel, Field
from decimal import Decimal
from datetime import datetime


class SalesType(str, Enum):
    """销售类型枚举"""
    all = "all"              # 全部
    retail = "retail"        # 零售
    wholesale = "wholesale"  # 批发
    transfer = "transfer"    # 店间调拨
    recycling = "recycling"  # 回收变现


class SalesItemDetailResponse(BaseModel):
    """销售商品明细响应"""
    # 基础信息
    id: int = Field(..., description="明细ID")
    order_id: int = Field(..., description="单据ID")
    order_no: str = Field(..., description="单据号")
    order_type: str = Field(..., description="单据类型")
    sales_type: str = Field(..., description="销售类型")
    
    # 商品信息
    jewelry_id: int = Field(..., description="商品ID")
    barcode: str = Field(..., description="商品条码")
    jewelry_name: str = Field(..., description="商品名称")
    category_name: str = Field(None, description="分类名称")
    ring_size: Optional[str] = Field(None, description="圈口号")
    
    # 重量信息
    gold_weight: Decimal = Field(0.00, description="金重(克)")
    silver_weight: Decimal = Field(0.00, description="银重(克)")
    total_weight: Decimal = Field(0.00, description="总重(克)")
    
    # 价格信息
    gold_price: Decimal = Field(0.00, description="金价(元/克)")
    silver_price: Decimal = Field(0.00, description="银价(元/克)")
    work_price: Decimal = Field(0.00, description="工费(元)")
    piece_work_price: Decimal = Field(0.00, description="件工费(元)")
    
    # 成本和销售
    cost_price: Decimal = Field(0.00, description="成本价(元)")
    sale_price: Decimal = Field(0.00, description="销售价(元)")
    profit: Decimal = Field(0.00, description="利润(元)")
    profit_rate: Decimal = Field(0.00, description="利润率(%)")
    
    # 销售信息
    customer: Optional[str] = Field(None, description="客户")
    store_name: str = Field(..., description="门店名称")
    operator_name: str = Field(..., description="操作员")
    sale_time: datetime = Field(..., description="销售时间")
    
    # 备注
    remark: Optional[str] = Field(None, description="备注")

    class Config:
        from_attributes = True


class SalesQueryParams(BaseModel):
    """销售查询参数"""
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(20, ge=1, le=100, description="每页数量")
    sales_type: SalesType = Field(SalesType.retail, description="销售类型")
    keyword: Optional[str] = Field(None, description="关键词搜索")
    store_id: Optional[int] = Field(None, description="门店ID")
    start_date: Optional[str] = Field(None, description="开始日期")
    end_date: Optional[str] = Field(None, description="结束日期")
    operator_id: Optional[int] = Field(None, description="操作员ID")
    customer: Optional[str] = Field(None, description="客户")
    min_profit: Optional[float] = Field(None, description="最小利润")
    max_profit: Optional[float] = Field(None, description="最大利润")


class SalesTypeStatistics(BaseModel):
    """销售类型统计"""
    type_name: str = Field(..., description="类型名称")
    total_count: int = Field(0, description="总件数")
    total_sales: Decimal = Field(0.00, description="总销售额")
    total_cost: Decimal = Field(0.00, description="总成本")
    total_profit: Decimal = Field(0.00, description="总利润")
    profit_rate: Decimal = Field(0.00, description="利润率(%)")


class SalesStatistics(BaseModel):
    """销售统计信息"""
    # 总体统计
    total_count: int = Field(0, description="总件数")
    total_sales: Decimal = Field(0.00, description="总销售额")
    total_cost: Decimal = Field(0.00, description="总成本")
    total_profit: Decimal = Field(0.00, description="总利润")
    average_profit_rate: Decimal = Field(0.00, description="平均利润率(%)")
    
    # 分类统计
    by_type: List[SalesTypeStatistics] = Field([], description="按类型统计")
    
    # 今日统计
    today_count: int = Field(0, description="今日销售件数")
    today_sales: Decimal = Field(0.00, description="今日销售额")
    today_profit: Decimal = Field(0.00, description="今日利润")
    
    # 本月统计
    month_count: int = Field(0, description="本月销售件数")
    month_sales: Decimal = Field(0.00, description="本月销售额")
    month_profit: Decimal = Field(0.00, description="本月利润")