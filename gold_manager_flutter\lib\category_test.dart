import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:hive_flutter/hive_flutter.dart';

import 'features/recycling/services/category_service.dart';
import 'features/recycling/presentation/pages/category_list_page.dart';
import 'core/services/api_service.dart';
import 'core/services/storage_service.dart';
import 'services/auth_service.dart';
import 'core/utils/logger_service.dart';
import 'services/stock_service.dart';
import 'services/store_service.dart';
import 'services/jewelry_service.dart';

void main() async {
  // 确保Flutter已初始化
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // 初始化日志
    LoggerService.init();
    LoggerService.i('开始初始化服务...');

    // 初始化本地存储
    await initStorage();

    // 注册服务
    await registerServices();

    LoggerService.i('所有服务初始化完成');

    // 运行应用
    runApp(const CategoryTestApp());
  } catch (e, stackTrace) {
    print('初始化失败: $e\n$stackTrace');
  }
}

/// 初始化本地存储
Future<void> initStorage() async {
  try {
    // 初始化SharedPreferences
    final prefs = await SharedPreferences.getInstance();

    // 创建存储服务实例
    final storageService = StorageService();
    // 手动设置SharedPreferences实例
    storageService.setPrefs(prefs);

    // 注册存储服务
    Get.put(storageService, permanent: true);
    LoggerService.i('StorageService initialized');

    // 初始化Hive (如果需要)
    await Hive.initFlutter();
  } catch (e) {
    LoggerService.e('初始化存储服务失败', e);
    rethrow;
  }
}

/// 注册所有服务
Future<void> registerServices() async {
  try {
    // API服务
    final apiService = ApiService();
    Get.put(apiService, permanent: true);
    LoggerService.i('ApiService initialized');

    // 认证服务
    final authService = AuthService();
    Get.put(authService, permanent: true);
    LoggerService.i('AuthService initialized');

    try {
      // 库存服务
      Get.put(StockService(), permanent: true);
      LoggerService.i('StockService initialized');
    } catch (e) {
      LoggerService.w('StockService初始化失败，继续其他服务', e);
    }

    try {
      // 门店服务
      Get.put(StoreService(), permanent: true);
      LoggerService.i('StoreService initialized');
    } catch (e) {
      LoggerService.w('StoreService初始化失败，继续其他服务', e);
    }

    try {
      // 首饰服务
      Get.put(JewelryService(), permanent: true);
      LoggerService.i('JewelryService initialized');
    } catch (e) {
      LoggerService.w('JewelryService初始化失败，继续其他服务', e);
    }

    // 旧料分类服务
    Get.put(CategoryService(), permanent: true);
    LoggerService.i('CategoryService initialized');
  } catch (e) {
    LoggerService.e('注册服务失败', e);
    rethrow;
  }
}

class CategoryTestApp extends StatelessWidget {
  const CategoryTestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: '旧料分类管理测试',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      home: const CategoryListPage(),
    );
  }
}