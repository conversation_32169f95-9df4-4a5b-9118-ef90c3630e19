import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:window_manager/window_manager.dart';

import '../../../core/theme/app_theme.dart';
import '../controllers/dashboard_controller.dart';
import '../../../widgets/responsive_builder.dart';
import '../../../services/window_service.dart';
import '../../../core/utils/logger.dart';
// 导入真实的业务页面
import 'dashboard_overview_view.dart';
import '../../jewelry/views/jewelry_list_view.dart';
import '../../stock/views/stock_container_view.dart';
import '../../sales/views/sales_management_view.dart';
import '../../recycling/views/recycling_container_view.dart';
import '../../settings/views/settings_view.dart';
// import '../../reports/views/reports_page.dart';  // 暂时注释，有fl_chart依赖问题

/// 仪表盘视图
/// 主应用框架，包含导航抽屉和主要内容区域
class DashboardView extends StatefulWidget {
  const DashboardView({super.key});

  @override
  State<DashboardView> createState() => _DashboardViewState();
}

class _DashboardViewState extends State<DashboardView> {
  late DashboardController controller;

  @override
  void initState() {
    super.initState();
    controller = Get.find<DashboardController>();

    // 🔥 关键修复：强制刷新UI监听
    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.printFullDebugState();
      // 强制触发一次状态更新
      setState(() {});
    });
  }

  /// 确保窗口在主界面时处于最大化状态
  Future<void> _ensureWindowMaximized() async {
    try {
      LoggerService.i('🏠 Dashboard开始确保窗口最大化...');

      // 检查Dashboard加载前的窗口状态
      final beforeDashboardSize = await windowManager.getSize();
      final beforeDashboardMaximized = await windowManager.isMaximized();
      LoggerService.i(
        '📊 Dashboard加载前窗口状态: ${beforeDashboardSize.width}x${beforeDashboardSize.height}, 最大化: $beforeDashboardMaximized',
      );

      final windowService = Get.find<WindowService>();
      await windowService.ensureMaximizedForDashboard();

      // 检查Dashboard窗口操作后的状态
      await Future.delayed(const Duration(milliseconds: 200));
      final afterDashboardSize = await windowManager.getSize();
      final afterDashboardMaximized = await windowManager.isMaximized();
      LoggerService.i(
        '📊 Dashboard窗口操作后状态: ${afterDashboardSize.width}x${afterDashboardSize.height}, 最大化: $afterDashboardMaximized',
      );

      // 检查是否有尺寸变化
      if (beforeDashboardSize.width != afterDashboardSize.width ||
          beforeDashboardSize.height != afterDashboardSize.height) {
        LoggerService.w('⚠️ Dashboard窗口操作导致了尺寸变化！');
        LoggerService.w(
          '   变化前: ${beforeDashboardSize.width}x${beforeDashboardSize.height}',
        );
        LoggerService.w(
          '   变化后: ${afterDashboardSize.width}x${afterDashboardSize.height}',
        );
      }
    } catch (e) {
      // 如果WindowService未找到或出错，不影响主界面显示
      LoggerService.w('❌ 确保窗口最大化失败', e);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // 移除AppBar，释放56px垂直空间
      appBar: null,

      // 响应式侧边导航抽屉（小屏幕使用）
      drawer: MediaQuery.of(context).size.width < 768
          ? Drawer(child: _buildDrawerContent(context))
          : null,

      // 主体布局 - 响应式设计
      body: ScreenTypeLayout(
        mobile: _buildMobileLayout(context),
        tablet: _buildTabletLayout(context),
        desktop: _buildDesktopLayout(context),
        mobileBreakpoint: 768, // 768px以下为移动端
        tabletBreakpoint: 1366, // 1366px以下为平板端，可以测试收缩功能
      ),

      // 移除全局FloatingActionButton，避免与各界面具体操作按钮功能重复
      // 各界面应使用自己的专用操作按钮，提供更明确的功能指示
    );
  }

  /// 构建内容区域顶部状态栏（增强版 - 包含导航控制）
  Widget _buildStatusBar(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 1,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        children: [
          // 导航控制按钮（替代AppBar功能）
          if (screenWidth < 768) // 移动端：抽屉按钮
            IconButton(
              icon: const Icon(Icons.menu, size: 20),
              onPressed: () => Scaffold.of(context).openDrawer(),
              tooltip: '打开菜单',
              padding: const EdgeInsets.all(4),
              constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
            )
          else if (screenWidth < 1366) // 平板端：侧边栏切换按钮
            IconButton(
              icon: const Icon(Icons.menu, size: 20),
              onPressed: controller.toggleSidebar,
              tooltip: '切换侧边栏',
              padding: const EdgeInsets.all(4),
              constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
            ),

          // 间距
          if (screenWidth < 1366) const SizedBox(width: 8),

          // 面包屑导航
          Expanded(
            child: Obx(
              () => Row(
                children: [
                  Text(
                    'dashboard'.tr,
                    style: const TextStyle(
                      color: AppTheme.secondaryTextColor,
                      fontSize: 14,
                    ),
                  ),
                  const Icon(
                    Icons.chevron_right,
                    size: 16,
                    color: AppTheme.secondaryTextColor,
                  ),
                  Flexible(
                    child: Text(
                      controller.menuItems.isEmpty
                          ? ''
                          : controller
                                .menuItems[controller.selectedIndex.value]
                                .title,
                      style: const TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 14,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // 当前门店
          Row(
            children: [
              const Icon(
                Icons.store,
                size: 16,
                color: AppTheme.secondaryTextColor,
              ),
              const SizedBox(width: 4),
              Obx(
                () => Text(
                  controller.storeName.value,
                  style: const TextStyle(
                    color: AppTheme.secondaryTextColor,
                    fontSize: 14,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建永久导航抽屉（宽屏）
  Widget _buildPermanentDrawer(BuildContext context) {
    return SizedBox(
      width: 260,
      child: Card(
        margin: EdgeInsets.zero,
        elevation: 4.0,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topRight: Radius.circular(12),
            bottomRight: Radius.circular(12),
          ),
        ),
        child: _buildDrawerContent(context),
      ),
    );
  }

  /// 构建抽屉内容
  Widget _buildDrawerContent(BuildContext context) {
    return Column(
      children: [
        // 用户信息头部
        Container(
          padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 16),
          decoration: const BoxDecoration(
            color: AppTheme.primaryColor,
            borderRadius: BorderRadius.only(topRight: Radius.circular(12)),
          ),
          child: Column(
            children: [
              // 用户头像
              const CircleAvatar(
                radius: 40,
                backgroundColor: Colors.white,
                child: Icon(
                  Icons.person,
                  size: 48,
                  color: AppTheme.primaryColor,
                ),
              ),
              const SizedBox(height: 12),

              // 用户名
              Obx(
                () => Text(
                  controller.userName.value,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(height: 4),

              // 用户角色
              Obx(
                () => Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    controller.userNickname.value.isNotEmpty
                        ? controller.userNickname.value
                        : controller.userRole.value,
                    style: const TextStyle(color: Colors.white, fontSize: 12),
                  ),
                ),
              ),
            ],
          ),
        ), // 菜单列表
        Expanded(
          child: Obx(
            () => ListView.builder(
              key: ValueKey(
                'menu-${controller.selectedIndex.value}-${controller.uiRefreshTrigger.value}',
              ), // 🔥 强制刷新key
              padding: const EdgeInsets.symmetric(vertical: 8),
              itemCount: controller.menuItems.length,
              itemBuilder: (context, index) {
                final item = controller.menuItems[index];
                final isSelected = controller.selectedIndex.value == index;

                // 🔥 调试：打印每个菜单项的选中状态
                if (isSelected) {
                  print('🎯 UI渲染中：[$index] ${item.title} 被选中 ✅');
                }

                return Container(
                  margin: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? AppTheme.primaryColor.withValues(alpha: 0.15)
                        : Colors.transparent,
                    borderRadius: BorderRadius.circular(8),
                    border: isSelected
                        ? Border.all(
                            color: AppTheme.primaryColor.withValues(alpha: 0.3),
                            width: 1,
                          )
                        : null,
                  ),
                  child: ListTile(
                    leading: Icon(
                      item.icon,
                      color: isSelected
                          ? AppTheme.primaryColor
                          : Colors.grey[600],
                      size: 22,
                    ),
                    title: Text(
                      item.title,
                      style: TextStyle(
                        fontWeight: isSelected
                            ? FontWeight.w600
                            : FontWeight.w500,
                        color: isSelected
                            ? AppTheme.primaryColor
                            : Colors.grey[700],
                        fontSize: 15,
                      ),
                    ),
                    selected: isSelected,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 4,
                    ),
                    onTap: () {
                      controller.changeTabIndex(index);
                      if (MediaQuery.of(context).size.width < 600) {
                        Navigator.pop(context);
                      }
                    },
                  ),
                );
              },
            ),
          ),
        ),

        // 分隔线
        const Divider(height: 1),

        // 底部操作区
        ListTile(
          leading: const Icon(Icons.logout),
          title: Text('logout'.tr),
          onTap: controller.logout,
        ),

        // 版本信息
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(
            '${'version'.tr}: 0.1.0',
            style: const TextStyle(
              color: AppTheme.secondaryTextColor,
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }

  /// 构建占位内容（开发用）
  Widget _buildPlaceholderContent(String title) {
    return Container(
      color: AppTheme.backgroundColor,
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 页面标题
          Text(
            title,
            style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 24),

          // 占位内容
          Expanded(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor.withValues(alpha: 0.1),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.construction,
                      size: 64,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                  const SizedBox(height: 24),
                  Text(
                    'under_construction'.tr,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'coming_soon'.tr,
                    style: const TextStyle(
                      color: AppTheme.secondaryTextColor,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 移动端布局（小屏幕）
  Widget _buildMobileLayout(BuildContext context) {
    return Column(
      children: [
        // 内容区域顶部状态栏
        _buildStatusBar(context),

        // 主要内容
        Expanded(child: _buildMainContent()),
      ],
    );
  }

  /// 平板端布局（中等屏幕）
  Widget _buildTabletLayout(BuildContext context) {
    return Row(
      children: [
        // 可收缩的侧边导航
        _buildCollapsibleSidebar(context),

        // 主内容区域
        Expanded(
          child: Column(
            children: [
              // 内容区域顶部状态栏
              _buildStatusBar(context),

              // 主要内容
              Expanded(child: _buildMainContent()),
            ],
          ),
        ),
      ],
    );
  }

  /// 桌面端布局（大屏幕）
  Widget _buildDesktopLayout(BuildContext context) {
    return Row(
      children: [
        // 永久侧边导航
        _buildPermanentDrawer(context),

        // 主内容区域
        Expanded(
          child: Column(
            children: [
              // 内容区域顶部状态栏
              _buildStatusBar(context),

              // 主要内容
              Expanded(child: _buildMainContent()),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建主要内容区域
  Widget _buildMainContent() {
    return Container(
      color: AppTheme.backgroundColor,
      child: PageView(
        controller: controller.pageController,
        physics: const NeverScrollableScrollPhysics(),
        children: [
          // 真实的业务页面
          const DashboardOverviewView(), // 0: 仪表盘
          const JewelryListView(), // 1: 首饰管理
          const StockContainerView(), // 2: 库存管理（容器）
          const SalesManagementView(), // 3: 销售管理（销售明细管理）
          const RecyclingContainerView(), // 4: 旧料回收（容器）
          _buildPlaceholderContent('统计分析'), // 5: 统计分析
          const SettingsView(), // 6: 系统设置
        ],
      ),
    );
  }

  /// 构建可收缩的侧边栏（平板用）
  Widget _buildCollapsibleSidebar(BuildContext context) {
    return Obx(
      () => AnimatedContainer(
        duration: const Duration(milliseconds: 250), // 稍微延长主容器动画时间
        width: controller.isSidebarExpanded.value ? 260 : 70,
        curve: Curves.easeInOut, // 添加缓动曲线
        child: Card(
          margin: EdgeInsets.zero,
          elevation: 4.0,
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
              topRight: Radius.circular(12),
              bottomRight: Radius.circular(12),
            ),
          ),
          child: Column(
            children: [
              // 侧边栏头部
              _buildSidebarHeader(context),

              // 菜单列表
              Expanded(child: _buildCollapsibleMenuList(context)),

              // 底部操作
              _buildSidebarFooter(context),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建侧边栏头部
  Widget _buildSidebarHeader(BuildContext context) {
    return Obx(
      () => Container(
        height: 72, // 固定高度
        decoration: const BoxDecoration(
          color: AppTheme.primaryColor,
          borderRadius: BorderRadius.only(topRight: Radius.circular(12)),
        ),
        child: controller.isSidebarExpanded.value
            ? Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    // 用户头像
                    const CircleAvatar(
                      radius: 20,
                      backgroundColor: Colors.white,
                      child: Icon(
                        Icons.person,
                        size: 24,
                        color: AppTheme.primaryColor,
                      ),
                    ),

                    // 用户信息
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.only(left: 12),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              controller.userName.value,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                            Text(
                              controller.userNickname.value.isNotEmpty
                                  ? controller.userNickname.value
                                  : controller.userRole.value,
                              style: const TextStyle(
                                color: Colors.white70,
                                fontSize: 12,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                    ),

                    // 切换按钮
                    IconButton(
                      icon: const Icon(Icons.chevron_left, color: Colors.white),
                      onPressed: controller.toggleSidebar,
                      tooltip: '收起侧边栏',
                    ),
                  ],
                ),
              )
            : Container(
                width: double.infinity,
                height: double.infinity,
                alignment: Alignment.center,
                child: GestureDetector(
                  onTap: controller.toggleSidebar,
                  child: Container(
                    width: 40,
                    height: 40,
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.person,
                      size: 24,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                ),
              ),
      ),
    );
  }

  /// 构建可收缩的菜单列表
  Widget _buildCollapsibleMenuList(BuildContext context) {
    return Obx(
      () => ListView.builder(
        key: ValueKey(
          'collapsible-menu-${controller.selectedIndex.value}-${controller.uiRefreshTrigger.value}',
        ), // 🔥 强制刷新key
        padding: const EdgeInsets.symmetric(vertical: 8),
        itemCount: controller.menuItems.length,
        itemBuilder: (context, index) {
          final item = controller.menuItems[index];
          final isSelected = controller.selectedIndex.value == index;

          // 🔥 调试：打印每个菜单项的选中状态
          if (isSelected) {
            print('🎯 可收缩菜单UI渲染中：[$index] ${item.title} 被选中 ✅');
          }

          return Container(
            margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: isSelected
                  ? AppTheme.primaryColor.withValues(alpha: 0.15)
                  : Colors.transparent,
              borderRadius: BorderRadius.circular(8),
              border: isSelected
                  ? Border.all(
                      color: AppTheme.primaryColor.withValues(alpha: 0.3),
                      width: 1,
                    )
                  : null,
            ),
            child: AnimatedSwitcher(
              duration: const Duration(milliseconds: 200),
              child: controller.isSidebarExpanded.value
                  ? ListTile(
                      key: const ValueKey('expanded'),
                      leading: Icon(
                        item.icon,
                        color: isSelected
                            ? AppTheme.primaryColor
                            : Colors.grey[600],
                        size: 22,
                      ),
                      title: Text(
                        item.title,
                        style: TextStyle(
                          fontWeight: isSelected
                              ? FontWeight.w600
                              : FontWeight.w500,
                          color: isSelected
                              ? AppTheme.primaryColor
                              : Colors.grey[700],
                          fontSize: 15,
                        ),
                      ),
                      selected: isSelected,
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 4,
                      ),
                      onTap: () => controller.changeTabIndex(index),
                    )
                  : Container(
                      key: const ValueKey('collapsed'),
                      height: 56,
                      alignment: Alignment.center,
                      decoration: isSelected
                          ? BoxDecoration(
                              color: AppTheme.primaryColor.withValues(
                                alpha: 0.1,
                              ),
                              borderRadius: BorderRadius.circular(6),
                            )
                          : null,
                      child: Tooltip(
                        message: item.title,
                        child: IconButton(
                          icon: Icon(
                            item.icon,
                            color: isSelected
                                ? AppTheme.primaryColor
                                : Colors.grey[600],
                            size: 22,
                          ),
                          onPressed: () => controller.changeTabIndex(index),
                        ),
                      ),
                    ),
            ),
          );
        },
      ),
    );
  }

  /// 构建侧边栏底部
  Widget _buildSidebarFooter(BuildContext context) {
    return Column(
      children: [
        const Divider(height: 1),

        // 搜索功能 - 从顶部导航栏移动过来
        Container(
          margin: const EdgeInsets.all(8),
          child: AnimatedSwitcher(
            duration: const Duration(milliseconds: 200),
            child: controller.isSidebarExpanded.value
                ? ListTile(
                    key: const ValueKey('search-expanded'),
                    leading: const Icon(Icons.search),
                    title: const Text('全局搜索'),
                    onTap: () {
                      // TODO: 实现全局搜索
                      Get.snackbar('搜索', '全局搜索功能正在开发中');
                    },
                  )
                : Container(
                    key: const ValueKey('search-collapsed'),
                    height: 56,
                    alignment: Alignment.center,
                    child: Tooltip(
                      message: '全局搜索',
                      child: IconButton(
                        icon: const Icon(Icons.search),
                        onPressed: () {
                          // TODO: 实现全局搜索
                          Get.snackbar('搜索', '全局搜索功能正在开发中');
                        },
                      ),
                    ),
                  ),
          ),
        ),

        // 通知功能 - 从顶部导航栏移动过来
        Container(
          margin: const EdgeInsets.all(8),
          child: AnimatedSwitcher(
            duration: const Duration(milliseconds: 200),
            child: controller.isSidebarExpanded.value
                ? ListTile(
                    key: const ValueKey('notifications-expanded'),
                    leading: const Badge(
                      isLabelVisible: true,
                      label: Text('2'),
                      child: Icon(Icons.notifications),
                    ),
                    title: const Text('通知消息'),
                    onTap: () {
                      // TODO: 显示通知列表
                      Get.snackbar('通知', '通知功能正在开发中');
                    },
                  )
                : Container(
                    key: const ValueKey('notifications-collapsed'),
                    height: 56,
                    alignment: Alignment.center,
                    child: Tooltip(
                      message: '通知消息',
                      child: Badge(
                        isLabelVisible: true,
                        label: const Text('2'),
                        child: IconButton(
                          icon: const Icon(Icons.notifications),
                          onPressed: () {
                            // TODO: 显示通知列表
                            Get.snackbar('通知', '通知功能正在开发中');
                          },
                        ),
                      ),
                    ),
                  ),
          ),
        ),

        // 退出登录 - 添加动画
        Container(
          margin: const EdgeInsets.all(8),
          child: AnimatedSwitcher(
            duration: const Duration(milliseconds: 200),
            child: controller.isSidebarExpanded.value
                ? ListTile(
                    key: const ValueKey('logout-expanded'),
                    leading: const Icon(Icons.logout),
                    title: Text('logout'.tr),
                    onTap: controller.logout,
                  )
                : Container(
                    key: const ValueKey('logout-collapsed'),
                    height: 56,
                    alignment: Alignment.center,
                    child: Tooltip(
                      message: 'logout'.tr,
                      child: IconButton(
                        icon: const Icon(Icons.logout),
                        onPressed: controller.logout,
                      ),
                    ),
                  ),
          ),
        ),

        // 版本信息（仅展开时显示）- 添加淡入淡出动画
        AnimatedOpacity(
          duration: const Duration(milliseconds: 200),
          opacity: controller.isSidebarExpanded.value ? 1.0 : 0.0,
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            height: controller.isSidebarExpanded.value ? null : 0,
            child: controller.isSidebarExpanded.value
                ? Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Text(
                      '${'version'.tr}: 0.1.0',
                      style: const TextStyle(
                        color: AppTheme.secondaryTextColor,
                        fontSize: 12,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  )
                : const SizedBox.shrink(),
          ),
        ),
      ],
    );
  }
}
