"""
会员管理API端点
提供会员的CRUD操作、积分管理和统计功能
"""

from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from ....core.database import get_db
from ....core.dependencies import get_current_user
from ....schemas.auth import CurrentUserResponse
from ....schemas.member import (
    MemberCreate,
    MemberUpdate,
    MemberResponse,
    MemberListResponse,
    MemberStatistics,
    MemberPointsUpdate
)
from ....services.member_service import MemberService

# 创建路由器
router = APIRouter()


@router.get("", response_model=MemberListResponse)
async def get_members(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    status: Optional[int] = Query(None, ge=0, le=1, description="状态筛选:0=禁用,1=正常"),
    level: Optional[int] = Query(None, ge=1, le=5, description="等级筛选:1-5"),
    keyword: Optional[str] = Query(None, description="搜索关键词(会员卡号、姓名、电话)"),
    db: Session = Depends(get_db),
    current_user: CurrentUserResponse = Depends(get_current_user)
):
    """
    获取会员列表

    支持以下筛选条件:
    - status: 按状态筛选(0=禁用,1=正常)
    - level: 按等级筛选(1=普通会员,2=银卡,3=金卡,4=白金,5=钻石)
    - keyword: 按会员卡号、姓名或电话搜索
    """
    service = MemberService(db)
    return await service.get_members(
        page=page,
        page_size=page_size,
        status=status,
        level=level,
        keyword=keyword
    )


@router.get("/statistics", response_model=MemberStatistics)
async def get_member_statistics(
    db: Session = Depends(get_db),
    current_user: CurrentUserResponse = Depends(get_current_user)
):
    """获取会员统计信息"""
    service = MemberService(db)
    return await service.get_member_statistics()


@router.get("/card/{card_no}", response_model=MemberResponse)
async def get_member_by_card_no(
    card_no: str,
    db: Session = Depends(get_db),
    current_user: CurrentUserResponse = Depends(get_current_user)
):
    """根据会员卡号获取会员详情"""
    service = MemberService(db)
    member = await service.get_member_by_card_no(card_no)
    if not member:
        raise HTTPException(status_code=404, detail="会员卡号不存在")
    return member


@router.get("/{member_id}", response_model=MemberResponse)
async def get_member(
    member_id: int,
    db: Session = Depends(get_db),
    current_user: CurrentUserResponse = Depends(get_current_user)
):
    """获取单个会员详情"""
    service = MemberService(db)
    member = await service.get_member_by_id(member_id)
    if not member:
        raise HTTPException(status_code=404, detail="会员不存在")
    return member


@router.post("", response_model=MemberResponse)
async def create_member(
    member_data: MemberCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUserResponse = Depends(get_current_user)
):
    """创建新会员"""
    service = MemberService(db)

    try:
        return await service.create_member(member_data)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="创建会员失败")


@router.put("/{member_id}", response_model=MemberResponse)
async def update_member(
    member_id: int,
    member_data: MemberUpdate,
    db: Session = Depends(get_db),
    current_user: CurrentUserResponse = Depends(get_current_user)
):
    """更新会员信息"""
    service = MemberService(db)

    try:
        member = await service.update_member(member_id, member_data)
        if not member:
            raise HTTPException(status_code=404, detail="会员不存在")
        return member
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="更新会员失败")


@router.delete("/{member_id}")
async def delete_member(
    member_id: int,
    db: Session = Depends(get_db),
    current_user: CurrentUserResponse = Depends(get_current_user)
):
    """删除会员"""
    service = MemberService(db)

    try:
        success = await service.delete_member(member_id)
        if not success:
            raise HTTPException(status_code=404, detail="会员不存在")
        return {"message": "会员删除成功"}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="删除会员失败")


@router.patch("/{member_id}/status", response_model=MemberResponse)
async def update_member_status(
    member_id: int,
    status: int = Query(..., ge=0, le=1, description="状态:0=禁用,1=正常"),
    db: Session = Depends(get_db),
    current_user: CurrentUserResponse = Depends(get_current_user)
):
    """更新会员状态"""
    service = MemberService(db)

    try:
        member = await service.update_member_status(member_id, status)
        if not member:
            raise HTTPException(status_code=404, detail="会员不存在")
        return member
    except Exception as e:
        raise HTTPException(status_code=500, detail="更新会员状态失败")


@router.patch("/{member_id}/points", response_model=MemberResponse)
async def update_member_points(
    member_id: int,
    points_data: MemberPointsUpdate,
    db: Session = Depends(get_db),
    current_user: CurrentUserResponse = Depends(get_current_user)
):
    """
    更新会员积分

    积分变化规则:
    - 正数表示增加积分
    - 负数表示扣除积分
    - 系统会自动根据积分调整会员等级
    """
    service = MemberService(db)

    try:
        member = await service.update_member_points(member_id, points_data)
        if not member:
            raise HTTPException(status_code=404, detail="会员不存在")
        return member
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="更新会员积分失败")