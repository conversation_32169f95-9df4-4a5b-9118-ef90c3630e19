import 'package:dio/dio.dart';
import 'package:get/get.dart';
import '../core/services/api_service.dart';
import '../models/jewelry_category.dart';
import '../core/utils/logger_service.dart';

/// 首饰分类服务类
class JewelryCategoryService extends GetxService {
  final ApiService _apiService;
  
  /// 构造函数
  JewelryCategoryService({required ApiService apiService}) 
      : _apiService = apiService {
    LoggerService.i('JewelryCategoryService 初始化');
  }
  
  /// 获取首饰分类列表
  Future<List<JewelryCategory>> getCategories({
    Map<String, dynamic>? queryParameters,
  }) async {
    try {
      final response = await _apiService.get(
        '/jewelry/categories',
        queryParameters: queryParameters,
      );

      // 根据后端API文档，categories接口直接返回数组
      final List<dynamic> data = response.data is List ? response.data : (response.data['data'] ?? []);
      return data.map((json) => JewelryCategory.fromJson(json)).toList();
    } catch (e) {
      LoggerService.e('获取首饰分类列表失败', e);
      throw _handleError(e);
    }
  }
  
  /// 获取首饰分类详情
  Future<JewelryCategory> getCategoryDetail(int id) async {
    try {
      final response = await _apiService.get(
        '/jewelry/category/detail',
        queryParameters: {'id': id},
      );
      
      return JewelryCategory.fromJson(response.data['data']);
    } catch (e) {
      LoggerService.e('获取首饰分类详情失败', e);
      throw _handleError(e);
    }
  }
  
  /// 添加首饰分类
  Future<bool> addCategory(JewelryCategory category) async {
    try {
      final response = await _apiService.post(
        '/jewelry/category/add',
        data: category.toJson(),
      );
      
      return response.data['code'] == 1;
    } catch (e) {
      LoggerService.e('添加首饰分类失败', e);
      throw _handleError(e);
    }
  }
  
  /// 编辑首饰分类
  Future<bool> editCategory(JewelryCategory category) async {
    try {
      final response = await _apiService.post(
        '/jewelry/category/edit',
        data: category.toJson(),
      );
      
      return response.data['code'] == 1;
    } catch (e) {
      LoggerService.e('编辑首饰分类失败', e);
      throw _handleError(e);
    }
  }
  
  /// 删除首饰分类
  Future<bool> deleteCategory(int id) async {
    try {
      final response = await _apiService.post(
        '/jewelry/category/delete',
        data: {'id': id},
      );
      
      return response.data['code'] == 1;
    } catch (e) {
      LoggerService.e('删除首饰分类失败', e);
      throw _handleError(e);
    }
  }
  
  /// 错误处理
  Exception _handleError(dynamic error) {
    if (error is DioException) {
      final response = error.response;
      if (response != null) {
        final data = response.data;
        if (data != null && data['msg'] != null) {
          return Exception(data['msg']);
        }
      }
      return Exception('网络请求失败：${error.message}');
    }
    return Exception('未知错误：$error');
  }
} 