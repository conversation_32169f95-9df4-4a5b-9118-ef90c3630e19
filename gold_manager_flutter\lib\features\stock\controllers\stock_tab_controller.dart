import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../../../core/widgets/tab_manager.dart';
import '../../../core/utils/logger.dart';
import '../../../services/auth_service.dart';
import '../../../services/batch_import_service.dart';
import '../../../services/store_service.dart';
import '../../../services/stock_service.dart';
import '../../../models/store/store.dart';
import '../../../models/jewelry/jewelry_category.dart';
import '../../../models/stock/stock_in_jewelry_item.dart';
import '../../../models/stock/batch_import_result.dart';
import '../../../models/stock/stock_in.dart';
import '../../../models/stock/stock_in_item.dart';
import '../../../models/common/document_status.dart';
import '../../../models/common/enums.dart';
import '../../../models/jewelry/jewelry.dart';
import '../../../services/jewelry_service.dart';
import '../controllers/stock_in_controller.dart';
import '../controllers/stock_in_form_controller.dart';
import '../controllers/stock_out_controller.dart';
import '../controllers/stock_out_form_controller.dart';
import '../controllers/stock_query_controller.dart';
import '../controllers/inventory_check_controller.dart';
import '../controllers/inventory_check_operation_controller.dart';
import '../controllers/store_transfer_controller.dart';
import '../controllers/store_transfer_form_controller.dart';
import '../services/inventory_check_service.dart';
import 'package:gold_manager_flutter/features/stock/services/store_transfer_service.dart';
import '../../recycling/controllers/recycling_form_controller.dart' hide Store;
import '../views/stock_list_view.dart';
import '../views/stock_in_view.dart';
import '../views/stock_in_form_view.dart';
import '../views/stock_out_view.dart';
import '../views/stock_out_form_view.dart';
import '../views/stock_query_view.dart';
import '../views/inventory_check_view.dart';
import '../views/inventory_check_operation_view.dart';
import '../views/store_transfer_view.dart';
import '../views/store_transfer_form_view.dart';
import '../../recycling/views/recycling_form_view.dart';
import '../../../core/constants/border_styles.dart';
import '../../../widgets/loading_state.dart';

/// 库存管理标签页控制器
class StockTabController extends GetxController {
  /// 标签页管理器
  late final TabManagerController tabManager;

  /// 缓存的标签页内容，避免重复创建
  final Map<String, Widget> _cachedTabContents = {};

  /// 服务依赖
  late final AuthService _authService;
  late final StoreService _storeService;
  late final JewelryService _jewelryService;
  late final BatchImportService _batchImportService;

  /// 门店数据
  final RxList<Store> storeList = <Store>[].obs;
  final RxInt selectedStoreId = 0.obs;
  final RxBool isLoadingStores = false.obs;

  /// 商品分类数据
  final RxList<JewelryCategory> categoryList = <JewelryCategory>[].obs;
  final RxBool isLoadingCategories = false.obs;

  /// 入库单商品明细
  final RxList<StockInJewelryItem> stockInItems = <StockInJewelryItem>[].obs;
  final RxDouble totalAmount = 0.0.obs;
  final RxInt totalQuantity = 0.obs;

  /// 工费方式选项
  final List<Map<String, dynamic>> workTypeOptions = [
    {'value': 0, 'label': '按克'},
    {'value': 1, 'label': '按件'},
  ];

  /// 表单字段控制器
  final Map<String, TextEditingController> _textControllers = {};

  /// 备注字段
  final RxString remark = ''.obs;

  /// 表格水平滚动控制器 - 用于同步表头和内容滚动
  final ScrollController _tableScrollController = ScrollController();

  /// 表头滚动控制器 - 与内容滚动同步
  final ScrollController _headerScrollController = ScrollController();

  @override
  void onInit() {
    super.onInit();
    tabManager = TabManagerController();

    // 初始化服务依赖
    _authService = Get.find<AuthService>();
    _storeService = Get.find<StoreService>();
    _jewelryService = Get.find<JewelryService>();
    _batchImportService = Get.find<BatchImportService>();

    // 初始化门店数据
    _initializeStoreData();

    // 初始化商品分类数据
    _initializeCategoryData();

    // 预初始化所有控制器，避免切换时的延迟
    _preInitializeControllers();

    // 设置滚动同步
    _setupScrollSync();

    // 默认打开库存总览标签页
    openStockOverview();
  }

  /// 设置滚动同步
  void _setupScrollSync() {
    // 使用标志位防止循环同步
    bool isSyncing = false;

    // 监听内容滚动，同步表头
    _tableScrollController.addListener(() {
      if (isSyncing) return; // 防止循环同步

      if (_headerScrollController.hasClients &&
          _headerScrollController.offset != _tableScrollController.offset) {
        isSyncing = true;
        try {
          // 检查滚动位置是否在有效范围内
          final targetOffset = _tableScrollController.offset;
          final maxScrollExtent =
              _headerScrollController.position.maxScrollExtent;
          final minScrollExtent =
              _headerScrollController.position.minScrollExtent;

          // 确保目标位置在有效范围内
          final clampedOffset = targetOffset.clamp(
            minScrollExtent,
            maxScrollExtent,
          );

          // 使用jumpTo进行即时同步，避免动画延迟导致的边界问题
          _headerScrollController.jumpTo(clampedOffset);
        } catch (e) {
          // 如果同步失败，记录错误但不中断操作
          LoggerService.w('表头滚动同步失败: $e');
        } finally {
          // 立即重置标志位，避免阻塞后续滚动
          isSyncing = false;
        }
      }
    });

    // 监听表头滚动，同步内容
    _headerScrollController.addListener(() {
      if (isSyncing) return; // 防止循环同步

      if (_tableScrollController.hasClients &&
          _tableScrollController.offset != _headerScrollController.offset) {
        isSyncing = true;
        try {
          // 检查滚动位置是否在有效范围内
          final targetOffset = _headerScrollController.offset;
          final maxScrollExtent =
              _tableScrollController.position.maxScrollExtent;
          final minScrollExtent =
              _tableScrollController.position.minScrollExtent;

          // 确保目标位置在有效范围内
          final clampedOffset = targetOffset.clamp(
            minScrollExtent,
            maxScrollExtent,
          );

          // 使用jumpTo进行即时同步，避免动画延迟导致的边界问题
          _tableScrollController.jumpTo(clampedOffset);
        } catch (e) {
          // 如果同步失败，记录错误但不中断操作
          LoggerService.w('内容滚动同步失败: $e');
        } finally {
          // 立即重置标志位，避免阻塞后续滚动
          isSyncing = false;
        }
      }
    });
  }

  /// 初始化门店数据
  void _initializeStoreData() {
    // 根据用户权限设置默认门店
    if (_authService.userRole.value != 'admin' &&
        !_authService.hasPermission('super.admin')) {
      // 普通员工：自动选择用户所属门店
      selectedStoreId.value = _authService.storeId.value;
    }

    // 获取门店列表
    fetchStores();
  }

  /// 获取门店列表
  Future<void> fetchStores() async {
    try {
      isLoadingStores.value = true;
      final stores = await _storeService.getAllStores();
      storeList.value = stores;

      LoggerService.d('获取门店列表成功，共${stores.length}个门店');
    } catch (e) {
      LoggerService.e('获取门店列表失败', e);
      Get.snackbar(
        '错误',
        '获取门店列表失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoadingStores.value = false;
    }
  }

  /// 初始化商品分类数据
  void _initializeCategoryData() {
    // 获取商品分类列表
    fetchCategories();
  }

  /// 获取商品分类列表
  Future<void> fetchCategories() async {
    try {
      isLoadingCategories.value = true;
      final categories = await _jewelryService.getCategories();
      categoryList.value = categories;
      LoggerService.d('获取商品分类成功，共${categories.length}个分类');
    } catch (e) {
      LoggerService.e('获取商品分类失败', e);
    } finally {
      isLoadingCategories.value = false;
    }
  }

  /// 预初始化控制器，避免切换时的延迟
  void _preInitializeControllers() {
    // 延迟初始化控制器，避免启动时的性能问题
    Future.delayed(const Duration(milliseconds: 500), () {
      try {
        // 使用lazyPut避免立即初始化，只有在需要时才创建
        if (!Get.isRegistered<StockInController>()) {
          Get.lazyPut<StockInController>(
            () => StockInController(),
            fenix: true,
          );
        }

        if (!Get.isRegistered<StockOutController>()) {
          Get.lazyPut<StockOutController>(
            () => StockOutController(),
            fenix: true,
          );
        }

        if (!Get.isRegistered<StockQueryController>()) {
          Get.lazyPut<StockQueryController>(() => StockQueryController());
        }
      } catch (e) {
        LoggerService.e('预初始化控制器失败', e);
      }
    });
  }

  /// 打开库存总览标签页
  void openStockOverview() {
    final tab = TabData(
      id: 'stock_overview',
      title: '库存总览',
      icon: Icons.dashboard,
      content: const StockListView(),
      canClose: false, // 总览页面不能关闭
    );
    tabManager.addTab(tab);
  }

  /// 打开入库管理标签页
  void openStockIn() {
    final tab = TabData(
      id: 'stock_in',
      title: '入库管理',
      icon: Icons.add_box,
      content: _getCachedTabContent('stock_in', () => _buildStockInWrapper()),
      canClose: true,
    );
    tabManager.addTab(tab);
  }

  /// 打开出库管理标签页
  void openStockOut() {
    final tab = TabData(
      id: 'stock_out',
      title: '出库管理',
      icon: Icons.logout,
      content: _getCachedTabContent('stock_out', () => _buildStockOutWrapper()),
      canClose: true,
    );
    tabManager.addTab(tab);
  }

  /// 打开库存查询标签页
  void openStockQuery() {
    try {
      LoggerService.d('🔄 开始打开库存查询标签页');

      // 🔧 修复：确保控制器的正确生命周期管理
      // 1. 先清理可能存在的旧控制器实例
      if (Get.isRegistered<StockQueryController>()) {
        Get.delete<StockQueryController>();
        LoggerService.d('🗑️ 清理旧的StockQueryController实例');
      }

      // 2. 立即创建新的控制器实例，确保StockQueryView构建时能找到
      final newController = StockQueryController();
      Get.put<StockQueryController>(newController, permanent: false);
      LoggerService.d('✅ 创建新的StockQueryController实例');

      // 3. 移除缓存的标签页内容，确保重新创建
      _cachedTabContents.remove('stock_query');

      // 4. 创建标签页
      final tab = TabData(
        id: 'stock_query',
        title: '库存查询',
        icon: Icons.search,
        content: _buildStockQueryWrapper(), // 直接构建，不使用缓存
        canClose: true,
      );

      // 5. 添加标签页
      tabManager.addTab(tab);

      LoggerService.d('✅ 库存查询标签页创建成功');
    } catch (e) {
      LoggerService.e('❌ 打开库存查询标签页失败', e);

      // 错误处理：确保至少有一个可用的控制器
      if (!Get.isRegistered<StockQueryController>()) {
        Get.lazyPut<StockQueryController>(() => StockQueryController());
        LoggerService.d('🔧 应急创建StockQueryController实例');
      }

      Get.snackbar(
        '错误',
        '打开库存查询页面失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 获取缓存的标签页内容，如果不存在则创建
  Widget _getCachedTabContent(String tabId, Widget Function() builder) {
    if (!_cachedTabContents.containsKey(tabId)) {
      _cachedTabContents[tabId] = builder();
    }
    return _cachedTabContents[tabId]!;
  }

  /// 打开库存盘点标签页
  void openStockInventory() {
    try {
      LoggerService.d('🔄 开始打开库存盘点标签页');

      // 确保控制器的正确生命周期管理
      if (Get.isRegistered<InventoryCheckController>()) {
        Get.delete<InventoryCheckController>();
        LoggerService.d('🗑️ 清理旧的InventoryCheckController实例');
      }

      // 确保InventoryCheckService已注册
      if (!Get.isRegistered<InventoryCheckService>()) {
        Get.put(InventoryCheckService(), permanent: false);
        LoggerService.d('✅ 注册InventoryCheckService');
      }

      // 立即创建新的控制器实例
      final newController = InventoryCheckController();
      Get.put<InventoryCheckController>(newController, permanent: false);
      LoggerService.d('✅ 创建新的InventoryCheckController实例');

      // 移除缓存的标签页内容，确保重新创建
      _cachedTabContents.remove('stock_inventory');

      // 创建标签页
      final tab = TabData(
        id: 'stock_inventory',
        title: '库存盘点',
        icon: Icons.content_paste_search,
        content: _buildInventoryCheckWrapper(), // 直接构建，不使用缓存
        canClose: true,
      );

      // 添加标签页
      tabManager.addTab(tab);

      LoggerService.d('✅ 库存盘点标签页创建成功');
    } catch (e) {
      LoggerService.e('❌ 打开库存盘点标签页失败', e);

      // 错误处理：确保至少有一个可用的控制器
      if (!Get.isRegistered<InventoryCheckController>()) {
        Get.lazyPut<InventoryCheckController>(() => InventoryCheckController());
        LoggerService.d('🔧 应急创建InventoryCheckController实例');
      }

      Get.snackbar(
        '错误',
        '打开库存盘点页面失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 打开盘点操作标签页
  /// 在新标签页中打开指定盘点单的操作界面
  void openInventoryCheckOperation(dynamic inventoryCheck) {
    try {
      LoggerService.d('🔄 开始打开盘点操作标签页: ${inventoryCheck.checkNo}');

      // 生成唯一的标签页ID
      final operationTabId = 'inventory_check_operation_${inventoryCheck.id}';

      // 检查是否已经存在该标签页
      final existingTabIndex = tabManager.tabs.indexWhere(
        (tab) => tab.id == operationTabId,
      );
      if (existingTabIndex != -1) {
        // 如果标签页已存在，直接切换到该标签页
        tabManager.switchToTab(existingTabIndex);
        LoggerService.d('✅ 切换到已存在的盘点操作标签页: ${inventoryCheck.checkNo}');
        return;
      }

      // 为这个标签页创建专用的控制器实例
      final operationController = InventoryCheckOperationController();
      Get.put<InventoryCheckOperationController>(
        operationController,
        tag: operationTabId,
        permanent: false,
      );

      // 初始化控制器的checkId
      operationController.initializeWithCheckId(inventoryCheck.id.toString());

      LoggerService.d('✅ 成功注册盘点操作控制器: $operationTabId');

      // 创建标签页
      final tab = TabData(
        id: operationTabId,
        title: '盘点操作 - ${inventoryCheck.checkNo}',
        icon: Icons.inventory_2,
        content: _buildInventoryCheckOperationWrapper(
          operationTabId,
          inventoryCheck.id,
        ),
        canClose: true,
        onClose: () {
          // 标签页关闭时清理控制器
          try {
            if (Get.isRegistered<InventoryCheckOperationController>(
              tag: operationTabId,
            )) {
              Get.delete<InventoryCheckOperationController>(
                tag: operationTabId,
              );
              LoggerService.d('🗑️ 清理盘点操作控制器: $operationTabId');
            }
          } catch (e) {
            LoggerService.w('清理盘点操作控制器失败: $e');
          }
        },
      );

      // 添加标签页
      tabManager.addTab(tab);

      LoggerService.d('✅ 盘点操作标签页创建成功: ${inventoryCheck.checkNo}');
    } catch (e) {
      LoggerService.e('❌ 打开盘点操作标签页失败', e);

      Get.snackbar(
        '错误',
        '打开盘点操作页面失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 打开新建入库单表单（在新标签页中）
  void openStockInForm() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final tab = TabData(
      id: 'stock_in_form_$timestamp', // 使用时间戳确保唯一性
      title: '新建入库单',
      icon: Icons.add_box,
      content: _buildStockInFormWrapper(), // 每次都创建新的表单实例
      canClose: true,
    );
    tabManager.addTab(tab);
  }

  /// 打开新建出库单表单（在新标签页中）
  void openStockOutForm() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final formTabId = 'stock_out_form_$timestamp';

    try {
      // 为这个标签页创建专用的控制器实例
      final stockOutFormController = StockOutFormController();
      Get.put<StockOutFormController>(
        stockOutFormController,
        tag: formTabId,
        permanent: true,
      );

      LoggerService.d('✅ 成功注册出库单表单控制器: $formTabId');

      final tab = TabData(
        id: formTabId, // 使用时间戳确保唯一性
        title: '新建出库单',
        icon: Icons.logout,
        content: _buildStockOutFormWrapper(formTabId), // 传递标签ID
        canClose: true,
        onClose: () {
          // 清理控制器资源
          try {
            if (Get.isRegistered<StockOutFormController>(tag: formTabId)) {
              Get.delete<StockOutFormController>(tag: formTabId);
              LoggerService.d('✅ 已清理出库单表单控制器: $formTabId');
            }
          } catch (e) {
            LoggerService.w('清理出库单表单控制器失败: $e');
          }
        },
      );
      tabManager.addTab(tab);
    } catch (e) {
      LoggerService.e('❌ 注册出库单表单控制器失败: $e');

      Get.snackbar(
        '错误',
        '创建新建出库单失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 打开编辑出库单表单（在新标签页中）
  void openStockOutEditForm(int stockOutId, String stockOutNo) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final formTabId = 'stock_out_edit_${stockOutId}_$timestamp';

    try {
      // 为这个标签页创建专用的控制器实例
      final stockOutFormController = StockOutFormController();
      Get.put<StockOutFormController>(
        stockOutFormController,
        tag: formTabId,
        permanent: true,
      );

      // 设置编辑模式和出库单ID
      stockOutFormController.setEditMode(stockOutId);

      // 设置保存成功后的回调，用于刷新出库列表
      stockOutFormController.onSaveSuccess = () {
        LoggerService.d('出库单编辑成功，刷新出库列表');
        try {
          final stockOutController = Get.find<StockOutController>();
          stockOutController.fetchStockOutList();
        } catch (e) {
          LoggerService.w('无法找到StockOutController进行刷新: $e');
        }
      };

      LoggerService.d('✅ 成功注册出库单编辑表单控制器: $formTabId, 出库单ID: $stockOutId');

      final tab = TabData(
        id: formTabId, // 使用出库单ID和时间戳确保唯一性
        title: '编辑出库单 - $stockOutNo',
        icon: Icons.edit,
        content: _buildStockOutFormWrapper(formTabId), // 传递标签ID
        canClose: true,
        onClose: () {
          // 清理控制器资源
          try {
            if (Get.isRegistered<StockOutFormController>(tag: formTabId)) {
              Get.delete<StockOutFormController>(tag: formTabId);
              LoggerService.d('✅ 已清理出库单编辑表单控制器: $formTabId');
            }
          } catch (e) {
            LoggerService.w('清理出库单编辑表单控制器失败: $e');
          }
        },
      );
      tabManager.addTab(tab);
    } catch (e) {
      LoggerService.e('❌ 注册出库单编辑表单控制器失败: $e');

      Get.snackbar(
        '错误',
        '打开编辑出库单失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 打开新建库存调拨表单（在新标签页中）
  void openStoreTransferForm() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final formTabId = 'store_transfer_form_$timestamp';

    try {
      // 为这个标签页创建专用的控制器实例
      final storeTransferFormController = StoreTransferFormController();
      Get.put<StoreTransferFormController>(
        storeTransferFormController,
        tag: formTabId,
        permanent: true,
      );

      LoggerService.d('✅ 成功注册库存调拨表单控制器: $formTabId');

      final tab = TabData(
        id: formTabId, // 使用时间戳确保唯一性
        title: '新建库存调拨',
        icon: Icons.swap_horiz,
        content: _buildStoreTransferFormWrapper(formTabId), // 传递标签ID
        canClose: true,
        onClose: () {
          // 标签页关闭时清理控制器
          try {
            if (Get.isRegistered<StoreTransferFormController>(tag: formTabId)) {
              Get.delete<StoreTransferFormController>(tag: formTabId);
              LoggerService.d('🗑️ 清理库存调拨表单控制器: $formTabId');
            }
          } catch (e) {
            LoggerService.w('清理库存调拨表单控制器失败: $e');
          }
        },
      );
      tabManager.addTab(tab);
    } catch (e) {
      LoggerService.e('❌ 注册库存调拨表单控制器失败: $e');

      Get.snackbar(
        '错误',
        '创建新建库存调拨失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 打开新建回收单表单（在新标签页中）
  void openRecyclingForm() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final formTabId = 'recycling_form_$timestamp';

    try {
      // 为这个标签页创建专用的控制器实例
      final recyclingFormController = RecyclingFormController();
      Get.put<RecyclingFormController>(
        recyclingFormController,
        tag: formTabId,
        permanent: true,
      );

      LoggerService.d('✅ 成功注册回收单表单控制器: $formTabId');

      final tab = TabData(
        id: formTabId, // 使用时间戳确保唯一性
        title: '新建回收单',
        icon: Icons.recycling,
        content: _buildRecyclingFormWrapper(formTabId), // 传递标签ID
        canClose: true,
        onClose: () {
          // 标签页关闭时清理控制器
          try {
            if (Get.isRegistered<RecyclingFormController>(tag: formTabId)) {
              Get.delete<RecyclingFormController>(tag: formTabId);
              LoggerService.d('🗑️ 清理回收单表单控制器: $formTabId');
            }
          } catch (e) {
            LoggerService.w('清理回收单表单控制器失败: $e');
          }
        },
      );
      tabManager.addTab(tab);
    } catch (e) {
      LoggerService.e('❌ 注册回收单表单控制器失败: $e');

      Get.snackbar(
        '错误',
        '创建新建回收单失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 打开编辑入库单表单标签页
  void openStockInEditForm(int id) {
    // 编辑入库单表单标签页ID
    final editFormTabId = 'stock_in_edit_$id';

    // 检查是否已经存在该标签页
    final existingTabIndex = tabManager.tabs.indexWhere(
      (tab) => tab.id == editFormTabId,
    );
    if (existingTabIndex != -1) {
      // 如果标签页已存在，先关闭它，确保重新加载数据
      tabManager.closeTab(existingTabIndex);
    }

    // 获取在editStockIn方法中已创建的控制器
    StockInFormController? editController;
    try {
      // 尝试获取已注册的控制器
      editController = Get.find<StockInFormController>(tag: editFormTabId);
      LoggerService.d('✅ 成功获取到预创建的编辑控制器: $editFormTabId');
    } catch (e) {
      // 如果找不到控制器，说明出现了错误
      LoggerService.e('❌ 未找到预创建的控制器，这可能是一个错误: $e');

      // 为防止应用崩溃，创建一个新的控制器实例
      editController = StockInFormController();
      Get.put(editController, tag: editFormTabId, permanent: true);
      editController.isEditing.value = true;
      LoggerService.w('🆕 创建了应急控制器实例: $editFormTabId');
    }

    // 创建一个可观察的加载状态变量，带超时保护
    final RxBool isLoadingData = true.obs;
    Timer? loadingTimeoutTimer;

    // 先创建并添加带有加载指示器的标签页
    tabManager.addTab(
      TabData(
        id: editFormTabId,
        title: '编辑入库单',
        icon: Icons.edit_document,
        content: Obx(
          () => Stack(
            children: [
              // 底层显示StockInFormView（初始时会显示空表单）
              StockInFormView(tag: editFormTabId),
              // 只在加载中时显示加载指示器，带超时处理
              if (isLoadingData.value)
                LoadingState(
                  text: '正在加载商品数据...',
                  timeoutSeconds: 45,
                  onTimeout: () {
                    LoggerService.e('❌ 编辑页面加载超时: $editFormTabId');
                    isLoadingData.value = false;
                    Get.snackbar(
                      '加载超时',
                      '数据加载超时，请检查网络连接或稍后重试',
                      snackPosition: SnackPosition.BOTTOM,
                      backgroundColor: Colors.orange,
                      colorText: Colors.white,
                    );
                  },
                ),
            ],
          ),
        ),
        canClose: true,
      ),
    );

    // 设置加载超时保护（双重保护）
    loadingTimeoutTimer = Timer(const Duration(seconds: 50), () {
      if (isLoadingData.value) {
        LoggerService.e('❌ 编辑页面加载超时保护触发: $editFormTabId');
        isLoadingData.value = false;
      }
    });

    // 在后台异步加载数据，使用microtask确保UI更新优先
    Future.microtask(() async {
      try {
        // 显式指定控制器变量，确保使用的是正确的实例
        if (editController != null) {
          // 分批加载数据，避免UI阻塞
          await editController.loadStockInById(id);

          // 加载完成后，更新加载状态，移除加载指示器
          isLoadingData.value = false;
          loadingTimeoutTimer?.cancel();
          LoggerService.d('✅ 入库单数据加载完成');
        } else {
          // 这种情况理论上不应该发生
          LoggerService.e('❌ 控制器为null，无法加载数据');
          isLoadingData.value = false;
          loadingTimeoutTimer?.cancel();

          Get.snackbar(
            '错误',
            '加载数据失败：控制器初始化错误',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
        }
      } catch (e) {
        // 显示错误信息
        LoggerService.e('❌ 加载入库单数据失败: $e');

        Get.snackbar(
          '错误',
          '加载入库单数据失败: ${e.toString()}',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );

        // 错误情况下也要移除加载指示器
        isLoadingData.value = false;
        loadingTimeoutTimer?.cancel();
      }
    });
  }

  /// 打开库存调拨标签页
  void openStockTransfer() {
    try {
      LoggerService.d('🔄 开始打开库存调拨标签页');

      // 确保控制器的正确生命周期管理
      if (Get.isRegistered<StoreTransferController>()) {
        Get.delete<StoreTransferController>();
        LoggerService.d('🗑️ 清理旧的StoreTransferController实例');
      }

      // 确保StoreTransferService已注册
      if (!Get.isRegistered<StoreTransferService>()) {
        Get.put(StoreTransferService(), permanent: false);
        LoggerService.d('✅ 注册StoreTransferService');
      }

      // 立即创建新的控制器实例
      final newController = StoreTransferController();
      Get.put<StoreTransferController>(newController, permanent: false);
      LoggerService.d('✅ 创建新的StoreTransferController实例');

      // 移除缓存的标签页内容，确保重新创建
      _cachedTabContents.remove('stock_transfer');

      // 创建标签页
      final tab = TabData(
        id: 'stock_transfer',
        title: '库存调拨',
        icon: Icons.swap_horiz,
        content: _buildStoreTransferWrapper(), // 直接构建，不使用缓存
        canClose: true,
      );

      // 添加标签页
      tabManager.addTab(tab);

      LoggerService.d('✅ 库存调拨标签页创建成功');
    } catch (e) {
      LoggerService.e('❌ 打开库存调拨标签页失败', e);

      // 错误处理：确保至少有一个可用的控制器
      if (!Get.isRegistered<StoreTransferController>()) {
        Get.lazyPut<StoreTransferController>(() => StoreTransferController());
        LoggerService.d('🔧 应急创建StoreTransferController实例');
      }

      Get.snackbar(
        '错误',
        '打开库存调拨页面失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 打开库存报表标签页
  void openStockReport() {
    final tab = TabData(
      id: 'stock_report',
      title: '库存报表',
      icon: Icons.bar_chart,
      content: _buildPlaceholderContent('库存报表', '库存报表功能正在开发中...'),
      canClose: true,
    );
    tabManager.addTab(tab);
  }

  /// 构建入库管理包装器
  Widget _buildStockInWrapper() {
    return const StockInView();
  }

  /// 构建出库管理包装器
  Widget _buildStockOutWrapper() {
    return const StockOutView();
  }

  /// 构建库存查询包装器
  Widget _buildStockQueryWrapper() {
    return const StockQueryView();
  }

  /// 构建库存盘点包装器
  Widget _buildInventoryCheckWrapper() {
    return const InventoryCheckView();
  }

  /// 构建库存调拨包装器
  Widget _buildStoreTransferWrapper() {
    return const StoreTransferView();
  }

  /// 构建盘点操作包装器
  Widget _buildInventoryCheckOperationWrapper(String tabId, int checkId) {
    return InventoryCheckOperationView(
      key: ValueKey(tabId), // 使用标签页ID作为key，确保每个标签页独立
      tag: tabId, // 传递tag参数，确保找到正确的控制器实例
    );
  }

  /// 构建新建入库单表单包装器
  Widget _buildStockInFormWrapper() {
    return Column(
      children: [
        _buildStockInFormHeader(),
        Expanded(child: _buildStockInFormContent()),
      ],
    );
  }

  /// 构建新建出库单表单包装器
  Widget _buildStockOutFormWrapper(String formTabId) {
    return _buildStockOutFormContent(formTabId);
  }

  /// 构建出库单表单内容
  Widget _buildStockOutFormContent(String formTabId) {
    return Container(
      color: Colors.grey[50],
      child: StockOutFormView(tag: formTabId),
    );
  }

  /// 构建新建库存调拨表单包装器
  Widget _buildStoreTransferFormWrapper(String formTabId) {
    return _buildStoreTransferFormContent(formTabId);
  }

  /// 构建库存调拨表单内容
  Widget _buildStoreTransferFormContent(String formTabId) {
    return Container(
      color: Colors.grey[50],
      child: StoreTransferFormView(tag: formTabId),
    );
  }

  /// 构建新建回收单表单包装器
  Widget _buildRecyclingFormWrapper(String formTabId) {
    return _buildRecyclingFormContent(formTabId);
  }

  /// 构建回收单表单内容
  Widget _buildRecyclingFormContent(String formTabId) {
    return Container(
      color: Colors.grey[50],
      child: RecyclingFormView(tag: formTabId),
    );
  }

  /// 构建入库单表单内容
  Widget _buildStockInFormContent() {
    return Container(
      color: Colors.grey[50],
      child: Column(
        children: [
          // 商品列表区域
          Expanded(child: _buildItemListSection()),
          // 底部汇总区域
          _buildFormSummarySection(),
        ],
      ),
    );
  }

  /// 构建紧凑型门店选择器（用于单行布局）
  Widget _buildCompactStoreSelector() {
    return Obx(() {
      // 检查用户权限
      final isAdmin =
          _authService.userRole.value == 'admin' ||
          _authService.hasPermission('super.admin');
      final currentUserStoreId = _authService.storeId.value;
      final currentStoreName = _authService.storeName.value;

      if (!isAdmin && currentUserStoreId > 0) {
        // 普通员工：显示当前用户所属门店，不可选择
        return Container(
          height: 32, // 紧凑高度
          decoration: BoxDecoration(
            border: Border.all(color: AppBorderStyles.borderColor),
            borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
            color: Colors.grey[50],
          ),
          child: Row(
            children: [
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  currentStoreName,
                  style: const TextStyle(fontSize: 13, color: Colors.black87),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const Icon(Icons.lock, size: 14, color: Colors.grey),
              const SizedBox(width: 8),
            ],
          ),
        );
      } else {
        // 管理员：可以选择门店
        if (isLoadingStores.value) {
          return Container(
            height: 32,
            decoration: BoxDecoration(
              border: Border.all(color: AppBorderStyles.borderColor),
              borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
            ),
            child: const Center(
              child: SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            ),
          );
        }

        // 构建下拉选项
        List<DropdownMenuItem<int>> items = [];

        // 管理员显示"全部门店"选项
        if (isAdmin) {
          items.add(
            const DropdownMenuItem<int>(
              value: 0,
              child: Text(
                '全部门店',
                style: TextStyle(fontSize: 13),
                textAlign: TextAlign.center,
              ),
            ),
          );
        }

        // 添加门店选项
        items.addAll(
          storeList.map(
            (store) => DropdownMenuItem<int>(
              value: store.id,
              child: Text(
                store.name,
                style: const TextStyle(fontSize: 13),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        );

        return Container(
          height: 32,
          decoration: AppBorderStyles.standardBoxDecoration,
          child: DropdownButtonHideUnderline(
            child: DropdownButton<int>(
              value: selectedStoreId.value == 0 ? null : selectedStoreId.value,
              hint: const Padding(
                padding: EdgeInsets.symmetric(horizontal: 8),
                child: Text(
                  '请选择',
                  style: TextStyle(fontSize: 13, color: Colors.grey),
                ),
              ),
              selectedItemBuilder: (BuildContext context) {
                return items.map<Widget>((DropdownMenuItem<int> item) {
                  return Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    child: Align(
                      alignment: Alignment.center,
                      child: Text(
                        item.child is Text ? (item.child as Text).data! : '',
                        style: const TextStyle(
                          fontSize: 13,
                          color: Colors.black87,
                        ),
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.center,
                      ),
                    ),
                  );
                }).toList();
              },
              isExpanded: true,
              items: items,
              onChanged: (value) {
                selectedStoreId.value = value ?? 0;
              },
              style: const TextStyle(fontSize: 13, color: Colors.black87),
              dropdownColor: Colors.white,
              icon: const Padding(
                padding: EdgeInsets.only(right: 8),
                child: Icon(
                  Icons.arrow_drop_down,
                  color: Colors.grey,
                  size: 18,
                ),
              ),
            ),
          ),
        );
      }
    });
  }

  /// 构建紧凑型备注输入框（用于单行布局）
  Widget _buildCompactRemarkField() {
    // 为备注字段创建一个专用的控制器
    if (!_textControllers.containsKey('remarkField')) {
      _textControllers['remarkField'] = TextEditingController(
        text: remark.value,
      );
    }

    // 获取或更新控制器
    final controller = _textControllers['remarkField']!;

    return Container(
      height: 32, // 与门店下拉框相同的高度
      decoration: AppBorderStyles.standardBoxDecoration,
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 8,
          vertical: 6,
        ), // 调整垂直内边距以适应32px高度
        child: TextField(
          controller: controller, // 使用控制器
          decoration: const InputDecoration(
            hintText: '可选',
            border: InputBorder.none,
            enabledBorder: InputBorder.none,
            focusedBorder: InputBorder.none,
            disabledBorder: InputBorder.none,
            errorBorder: InputBorder.none,
            focusedErrorBorder: InputBorder.none,
            contentPadding: EdgeInsets.zero,
            hintStyle: TextStyle(fontSize: 13, color: Colors.grey),
            isDense: true,
          ),
          style: const TextStyle(fontSize: 13),
          onChanged: (value) => remark.value = value,
        ),
      ),
    );
  }

  /// 构建门店选择器
  Widget _buildStoreSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '门店 *',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Obx(() {
          // 检查用户权限
          final isAdmin =
              _authService.userRole.value == 'admin' ||
              _authService.hasPermission('super.admin');
          final currentUserStoreId = _authService.storeId.value;
          final currentStoreName = _authService.storeName.value;

          if (!isAdmin && currentUserStoreId > 0) {
            // 普通员工：显示当前用户所属门店，不可选择
            return Container(
              height: 40,
              decoration: BoxDecoration(
                border: Border.all(color: AppBorderStyles.borderColor),
                borderRadius: BorderRadius.circular(
                  AppBorderStyles.mediumBorderRadius,
                ),
                color: Colors.grey[50],
              ),
              child: Row(
                children: [
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      currentStoreName,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                  const Icon(Icons.lock, size: 16, color: Colors.grey),
                  const SizedBox(width: 12),
                ],
              ),
            );
          } else {
            // 管理员：可以选择门店
            if (isLoadingStores.value) {
              return Container(
                height: 40,
                decoration: BoxDecoration(
                  border: Border.all(color: AppBorderStyles.borderColor),
                  borderRadius: BorderRadius.circular(
                    AppBorderStyles.mediumBorderRadius,
                  ),
                ),
                child: const Center(
                  child: SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                ),
              );
            }

            // 构建下拉选项
            List<DropdownMenuItem<int>> items = [];

            // 管理员显示"全部门店"选项
            if (isAdmin) {
              items.add(
                const DropdownMenuItem<int>(
                  value: 0,
                  child: Text('全部门店', style: TextStyle(fontSize: 14)),
                ),
              );
            }

            // 添加门店选项
            items.addAll(
              storeList.map(
                (store) => DropdownMenuItem<int>(
                  value: store.id,
                  child: Text(store.name, style: const TextStyle(fontSize: 14)),
                ),
              ),
            );

            return Container(
              height: 40,
              decoration: BoxDecoration(
                border: Border.all(color: AppBorderStyles.borderColor),
                borderRadius: BorderRadius.circular(
                  AppBorderStyles.mediumBorderRadius,
                ),
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<int>(
                  value: selectedStoreId.value == 0
                      ? null
                      : selectedStoreId.value,
                  hint: const Padding(
                    padding: EdgeInsets.symmetric(horizontal: 12),
                    child: Text(
                      '请选择门店',
                      style: TextStyle(fontSize: 14, color: Colors.grey),
                    ),
                  ),
                  selectedItemBuilder: (BuildContext context) {
                    return items.map<Widget>((DropdownMenuItem<int> item) {
                      return Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 12),
                        child: Align(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            item.child is Text
                                ? (item.child as Text).data!
                                : '',
                            style: const TextStyle(
                              fontSize: 14,
                              color: Colors.black87,
                            ),
                          ),
                        ),
                      );
                    }).toList();
                  },
                  isExpanded: true,
                  items: items,
                  onChanged: (value) {
                    selectedStoreId.value = value ?? 0;
                  },
                  style: const TextStyle(fontSize: 14, color: Colors.black87),
                  dropdownColor: Colors.white,
                  icon: const Padding(
                    padding: EdgeInsets.only(right: 12),
                    child: Icon(Icons.arrow_drop_down, color: Colors.grey),
                  ),
                ),
              ),
            );
          }
        }),
      ],
    );
  }

  /// 构建备注输入框
  Widget _buildRemarkField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '备注',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          height: 40,
          decoration: BoxDecoration(
            border: Border.all(color: AppBorderStyles.borderColor),
            borderRadius: BorderRadius.circular(
              AppBorderStyles.mediumBorderRadius,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            child: TextField(
              decoration: const InputDecoration(
                hintText: '可选',
                border: InputBorder.none,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                disabledBorder: InputBorder.none,
                errorBorder: InputBorder.none,
                focusedErrorBorder: InputBorder.none,
                contentPadding: EdgeInsets.zero,
                hintStyle: TextStyle(fontSize: 14, color: Colors.grey),
                isDense: true,
              ),
              style: const TextStyle(fontSize: 14),
              onChanged: (value) => remark.value = value,
            ),
          ),
        ),
      ],
    );
  }

  /// 构建商品列表区域
  Widget _buildItemListSection() {
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          // 商品列表头部
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                const Text(
                  '商品明细',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const Spacer(),
                // 重置按钮
                OutlinedButton.icon(
                  icon: const Icon(Icons.refresh, size: 16),
                  label: const Text('重置'),
                  onPressed: () => _resetForm(),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Colors.blue[600],
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                // 批量导入按钮
                ElevatedButton.icon(
                  icon: const Icon(Icons.upload_file, size: 16),
                  label: const Text('批量导入'),
                  onPressed: () => _showBatchImportDialog(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange[600],
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                // 添加新行按钮
                ElevatedButton.icon(
                  icon: const Icon(Icons.add, size: 16),
                  label: const Text('添加新行'),
                  onPressed: () => _addNewStockInItem(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green[600],
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ],
            ),
          ),
          const Divider(height: 1),
          // 商品列表内容
          Expanded(
            child: Obx(
              () => stockInItems.isEmpty
                  ? _buildEmptyItemList()
                  : _buildStockInItemTable(),
            ),
          ),
        ],
      ),
    );
  }

  /// 重置表单
  void _resetForm() {
    // 显示确认对话框
    Get.dialog(
      AlertDialog(
        title: const Text('确认重置'),
        content: const Text('确定要重置表单吗？所有未保存的数据将丢失。'),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('取消')),
          ElevatedButton(
            onPressed: () {
              Get.back();
              _doResetForm();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  /// 执行重置表单操作
  void _doResetForm() {
    // 清空商品列表
    stockInItems.clear();

    // 重置门店选择
    // 普通员工：恢复到默认门店
    // 管理员：恢复到"请选择"状态
    if (_authService.userRole.value != 'admin' &&
        !_authService.hasPermission('super.admin')) {
      selectedStoreId.value = _authService.storeId.value;
    } else {
      selectedStoreId.value = 0;
    }

    // 清空备注变量
    remark.value = '';

    // 创建一个备注专用的控制器，并赋空值
    final remarkController = TextEditingController(text: '');
    // 更新文本控制器映射中的备注控制器
    _textControllers['remark'] = remarkController;

    // 更新所有与备注相关的控制器
    for (final key in _textControllers.keys.toList()) {
      if (key.toLowerCase().contains('remark')) {
        _textControllers[key]!.text = '';
      }
    }

    // 重新计算总计
    _calculateTotals();

    // 显示提示
    Get.snackbar(
      '提示',
      '表单已重置',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.green,
      colorText: Colors.white,
      duration: const Duration(seconds: 2),
    );
  }

  /// 查找表单中的备注输入框
  TextField? _findRemarkTextField() {
    try {
      // 获取当前上下文
      final context = Get.context;
      if (context == null) return null;

      // 尝试查找备注输入框
      TextField? remarkField;

      void findTextField(Element element) {
        if (element.widget is TextField) {
          final textField = element.widget as TextField;
          // 判断是否是备注输入框
          final decoration = textField.decoration;
          if (decoration != null &&
              (decoration.hintText == '可选' ||
                  (decoration.labelText != null &&
                      (decoration.labelText!.contains('备注') ||
                          decoration.labelText!.toLowerCase().contains(
                            'remark',
                          ))))) {
            remarkField = textField;
            return;
          }
        }
        element.visitChildren(findTextField);
      }

      (context as Element).visitChildElements(findTextField);

      return remarkField;
    } catch (e) {
      // 忽略可能的错误
      return null;
    }
  }

  /// 构建空商品列表
  Widget _buildEmptyItemList() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.inventory_2_outlined, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            '暂无商品',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.grey,
            ),
          ),
          SizedBox(height: 8),
          Text(
            '点击"添加新行"按钮录入商品信息',
            style: TextStyle(fontSize: 14, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  /// 构建表单底部汇总区域
  Widget _buildFormSummarySection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(
        horizontal: 16.0,
        vertical: 12.0,
      ), // 从all(16.0)改为水平16.0、垂直12.0，与编辑入库单保持一致
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(top: BorderSide(color: Colors.grey[200]!, width: 1)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.03), // 从0.05降低到0.03，与编辑入库单保持一致
            blurRadius: 3, // 从4减小到3，与编辑入库单保持一致
            offset: const Offset(0, -1), // 从(0,-2)减小到(0,-1)，与编辑入库单保持一致
          ),
        ],
      ),
      child: Obx(
        () => Row(
          children: [
            // 左侧汇总信息
            const Text(
              '商品件数: ',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
            Text(
              '${totalQuantity.value}',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
            const SizedBox(width: 24),
            const Text(
              '总金额: ',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
            Text(
              '¥${totalAmount.value.toStringAsFixed(2)}',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.green,
              ),
            ),
            // 弹性空间，将按钮推到右侧
            const Spacer(),
            // 右侧按钮组
            OutlinedButton.icon(
              icon: const Icon(Icons.close, size: 16),
              label: const Text('取消'),
              onPressed: () {
                // 关闭当前标签页
                final currentIndex = tabManager.activeTabIndex.value;
                tabManager.closeTab(currentIndex);
              },
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            ),
            const SizedBox(width: 16),
            ElevatedButton(
              onPressed: () => saveDraft(),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
              child: const Text('保存草稿'),
            ),
            const SizedBox(width: 16),
            ElevatedButton(
              onPressed: () => submitForReview(),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue[600],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
              child: const Text('提交审核'),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建入库单表单头部（单行布局）
  Widget _buildStockInFormHeader() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(bottom: AppBorderStyles.tableBorder),
      ),
      child: Row(
        children: [
          // 🔷 新建入库单（图标+标题）
          Icon(Icons.add_box, color: Colors.blue[600], size: 20),
          const SizedBox(width: 8),
          const Text(
            '新建入库单',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(width: 24),

          // 👤 操作员: xxx（操作员信息标签）
          Obx(
            () => Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: Colors.blue[200]!, width: 1),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.person, size: 14, color: Colors.blue[600]),
                  const SizedBox(width: 4),
                  Text(
                    '操作员: ${_authService.userNickname.value.isNotEmpty ? _authService.userNickname.value : _authService.userName.value}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.blue[700],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(width: 24),

          // 门店: （标签）
          const Text(
            '门店:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(width: 8),

          // [门店下拉框]（控件）
          SizedBox(
            width: 150, // 固定宽度
            child: _buildCompactStoreSelector(),
          ),
          const SizedBox(width: 24),

          // 备注: （标签）
          const Text(
            '备注:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(width: 8),

          // [备注编辑框]（控件）
          SizedBox(
            width: 200, // 固定宽度
            child: _buildCompactRemarkField(),
          ),
        ],
      ),
    );
  }

  /// 构建占位内容
  Widget _buildPlaceholderContent(String title, String message) {
    return Column(
      children: [
        _buildTabPageHeader(title, Icons.construction),
        Expanded(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.construction, size: 64, color: Colors.grey[400]),
                const SizedBox(height: 16),
                Text(
                  message,
                  style: TextStyle(fontSize: 16, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// 构建标签页页面头部
  Widget _buildTabPageHeader(String title, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(bottom: AppBorderStyles.tableBorder),
      ),
      child: Row(
        children: [
          Icon(icon, color: Colors.blue[600], size: 20),
          const SizedBox(width: 8),
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const Spacer(),
          _buildQuickActionButtons(title),
        ],
      ),
    );
  }

  /// 构建快捷操作按钮
  Widget _buildQuickActionButtons(String pageTitle) {
    List<Widget> buttons = [];

    // 根据页面类型添加相应的操作按钮
    switch (pageTitle) {
      case '入库管理':
        buttons.add(
          ElevatedButton.icon(
            icon: const Icon(Icons.add, size: 16),
            label: const Text('新建入库单'),
            onPressed: () => openStockInForm(),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue[600],
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            ),
          ),
        );
        break;
      case '出库管理':
        buttons.add(
          ElevatedButton.icon(
            icon: const Icon(Icons.add, size: 16),
            label: const Text('新建出库单'),
            onPressed: () => openStockOutForm(),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red[600],
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            ),
          ),
        );
        break;
      case '库存查询':
        buttons.add(
          OutlinedButton.icon(
            icon: const Icon(Icons.refresh, size: 16),
            label: const Text('刷新数据'),
            onPressed: () => _refreshStockQueryData(),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            ),
          ),
        );
        break;
    }

    // 添加通用的刷新按钮
    if (buttons.isNotEmpty) {
      buttons.add(const SizedBox(width: 8));
    }
    buttons.add(
      IconButton(
        icon: const Icon(Icons.refresh),
        tooltip: '刷新',
        onPressed: () => _refreshCurrentPage(),
      ),
    );

    return Row(children: buttons);
  }

  /// 刷新当前页面数据
  Future<void> _refreshCurrentPage() async {
    try {
      // 获取当前活动的标签页
      final currentTab = tabManager.currentTab;
      if (currentTab == null) {
        Get.snackbar('提示', '没有活动的标签页');
        return;
      }

      LoggerService.d('🔄 开始刷新页面: ${currentTab.title}');

      // 不显示额外的加载指示器，让各个页面控制器自己管理loading状态
      // 这样避免重复的加载指示器问题

      // 根据标签页ID调用相应的刷新逻辑
      switch (currentTab.id) {
        case 'stock_in':
          await _refreshStockInData();
          break;
        case 'stock_out':
          await _refreshStockOutData();
          break;
        case 'stock_query':
          await _refreshStockQueryData();
          break;
        case 'stock_overview':
          await _refreshStockOverviewData();
          break;
        default:
          LoggerService.w('⚠️ 未知的标签页类型: ${currentTab.id}');
          break;
      }

      // 显示成功提示
      Get.snackbar(
        '成功',
        '${currentTab.title}数据已刷新',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
        duration: const Duration(seconds: 2),
      );

      LoggerService.d('✅ 页面刷新完成: ${currentTab.title}');
    } catch (e) {
      LoggerService.e('❌ 页面刷新失败', e);
      Get.snackbar(
        '错误',
        '页面刷新失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );
    }
  }

  /// 根据路由打开对应的标签页
  void openTabByRoute(String route) {
    switch (route) {
      case '/stock/in':
        openStockIn();
        break;
      case '/stock/out':
        openStockOut();
        break;
      case '/stock/query':
        openStockQuery();
        break;
      case '/stock/inventory':
        openStockInventory();
        break;
      case '/stock/transfer':
        openStockTransfer();
        break;
      case '/stock/report':
        openStockReport();
        break;
      default:
        openStockOverview();
    }
  }

  /// 添加新的入库商品明细行
  Future<void> _addNewStockInItem() async {
    final barcode = await _generateBarcode();
    final newItem = StockInJewelryItem(id: 0, stockInId: 0, barcode: barcode);
    stockInItems.add(newItem);
    _calculateTotals();

    // 移除不必要的提示框，直接添加商品行即可
    // 用户可以通过表格变化直观地看到新行已添加
  }

  /// 生成条码（异步版本，支持数据库检查）
  /// 格式：YYMMDD + 4位流水号 (例如：2505270001)
  Future<String> _generateBarcode() async {
    final now = DateTime.now();

    // 生成日期部分：YYMMDD
    final year = now.year % 100; // 取年份后两位
    final month = now.month;
    final day = now.day;
    final datePrefix =
        '${year.toString().padLeft(2, '0')}${month.toString().padLeft(2, '0')}${day.toString().padLeft(2, '0')}';

    // 从0001开始生成流水号，确保格式正确
    int sequence = 1; // 从1开始，格式化为0001

    // 循环查找可用的流水号
    while (sequence <= 9999) {
      final sequenceStr = sequence.toString().padLeft(4, '0');
      final barcode = '$datePrefix$sequenceStr';

      // 检查条码唯一性（包括当前表格中的条码和数据库中的条码）
      if (await _validateBarcodeUniqueness(barcode, -1)) {
        return barcode;
      }

      sequence++; // 如果重复，流水号加1
    }

    // 如果当天的所有流水号都被占用（极不可能的情况），使用时间戳作为后备方案
    final fallbackSequence = (now.millisecondsSinceEpoch % 10000)
        .toString()
        .padLeft(4, '0');
    final fallbackBarcode = '$datePrefix$fallbackSequence';

    LoggerService.w('当天流水号已满，使用后备方案: $fallbackBarcode');
    return fallbackBarcode;
  }

  /// 构建入库单商品明细表格
  Widget _buildStockInItemTable() {
    return Container(
      margin: const EdgeInsets.all(4), // 从16减小到4，与编辑入库单保持一致
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8), // 从12减小到8，与编辑入库单保持一致
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.03), // 从0.05降低到0.03，与编辑入库单保持一致
            blurRadius: 3, // 从4减小到3，与编辑入库单保持一致
            offset: const Offset(0, 1), // 从(0,2)减小到(0,1)，与编辑入库单保持一致
          ),
        ],
      ),
      child: Column(
        children: [
          // 表格头部 - 使用固定高度确保对齐
          Container(
            height: 48, // 固定表头高度
            decoration: const BoxDecoration(
              color: AppBorderStyles.tableHeaderBackground,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(8), // 从12减小到8，与编辑入库单保持一致
                topRight: Radius.circular(8), // 从12减小到8，与编辑入库单保持一致
              ),
              border: Border(bottom: AppBorderStyles.tableBorderDark),
            ),
            child: _buildTableHeader(),
          ),
          // 表格内容区域 - 使用Expanded确保适应可用空间
          Expanded(child: _buildTableContent()),
        ],
      ),
    );
  }

  /// 构建表格头部（支持水平滚动和响应式布局）
  Widget _buildTableHeader() {
    return LayoutBuilder(
      builder: (context, constraints) {
        final availableWidth = constraints.maxWidth;
        final tableWidth = _getTableWidth();
        final needsHorizontalScroll = tableWidth > availableWidth;

        if (needsHorizontalScroll) {
          // 大表格，需要水平滚动
          return _buildScrollableTableHeader();
        } else {
          // 小屏幕，使用响应式表头
          return _buildResponsiveTableHeader(availableWidth);
        }
      },
    );
  }

  /// 构建可滚动表格头部
  Widget _buildScrollableTableHeader() {
    return SingleChildScrollView(
      controller: _headerScrollController, // 使用表头滚动控制器
      scrollDirection: Axis.horizontal,
      physics: const AlwaysScrollableScrollPhysics(), // 强制启用滚动
      child: SizedBox(
        width: _getTableWidth(), // 确保表头宽度与内容一致
        child: Row(
          children: [
            _buildHeaderCell('序号', 60),
            _buildHeaderCell('条码', 120), // 与编辑入库单保持一致
            _buildHeaderCell('商品名称', 150), // 与编辑入库单保持一致
            _buildHeaderCell('分类', 100), // 改为100px与编辑入库单保持一致
            _buildHeaderCell('圈口号', 80), // 与编辑入库单保持一致
            _buildHeaderCell('金重(g)', 90), // 与编辑入库单保持一致
            _buildHeaderCell('金价(元/g)', 90), // 与编辑入库单保持一致
            _buildHeaderCell('银重(g)', 90), // 与编辑入库单保持一致
            _buildHeaderCell('银价(元/g)', 90), // 与编辑入库单保持一致
            _buildHeaderCell('总重(g)', 90), // 与编辑入库单保持一致
            _buildHeaderCell('工费方式', 90),
            _buildHeaderCell('工费(元)', 90), // 与编辑入库单保持一致
            _buildHeaderCell('电铸费(元)', 90), // 与编辑入库单保持一致
            _buildHeaderCell('批发工费(元)', 90), // 与编辑入库单保持一致
            _buildHeaderCell('零售工费(元)', 90), // 与编辑入库单保持一致
            _buildHeaderCell('件工费(元)', 90), // 与编辑入库单保持一致
            _buildHeaderCell('总成本(元)', 100), // 与编辑入库单保持一致
            _buildHeaderCell('操作', 70), // 与编辑入库单保持一致
          ],
        ),
      ),
    );
  }

  /// 构建响应式表格头部
  Widget _buildResponsiveTableHeader(double availableWidth) {
    final columnWidths = _calculateResponsiveColumnWidths(availableWidth);

    return SizedBox(
      width: availableWidth,
      child: Row(
        children: [
          _buildHeaderCell('序号', columnWidths['序号']!),
          _buildHeaderCell('条码', columnWidths['条码']!),
          _buildHeaderCell('商品名称', columnWidths['商品名称']!),
          _buildHeaderCell('分类', columnWidths['分类']!),
          _buildHeaderCell('金重(g)', columnWidths['金重']!),
          _buildHeaderCell('银重(g)', columnWidths['银重']!),
          _buildHeaderCell('工费(元)', columnWidths['工费']!),
          _buildHeaderCell('总成本(元)', columnWidths['总成本']!),
          _buildHeaderCell('操作', columnWidths['操作']!),
        ],
      ),
    );
  }

  /// 构建表格内容（支持水平滚动，优化小屏幕显示）
  Widget _buildTableContent() {
    return LayoutBuilder(
      builder: (context, constraints) {
        final availableWidth = constraints.maxWidth;
        final tableWidth = _getTableWidth();
        final needsHorizontalScroll = tableWidth > availableWidth;

        LoggerService.d('📱 响应式表格布局分析:');
        LoggerService.d('   可用宽度: ${availableWidth}px');
        LoggerService.d('   表格宽度: ${tableWidth}px');
        LoggerService.d('   需要水平滚动: $needsHorizontalScroll');

        return Container(
          decoration: BoxDecoration(
            border: Border.all(color: AppBorderStyles.borderColor, width: 1),
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(8), // 从12减小到8，与编辑入库单保持一致
              bottomRight: Radius.circular(8), // 从12减小到8，与编辑入库单保持一致
            ),
          ),
          child: needsHorizontalScroll
              ? _buildScrollableTable(availableWidth)
              : _buildResponsiveTable(availableWidth),
        );
      },
    );
  }

  /// 构建可滚动表格（大表格，需要水平滚动）
  Widget _buildScrollableTable(double availableWidth) {
    return RawScrollbar(
      controller: _tableScrollController, // 绑定滚动控制器到滚动条
      thumbVisibility: true, // 强制显示滚动条
      trackVisibility: true, // 显示滚动轨道
      thickness: 6.0, // 从8.0减小到6.0，与编辑入库单保持一致
      radius: const Radius.circular(4.0), // 从6.0减小到4.0，与编辑入库单保持一致
      interactive: true, // 启用交互式滚动条
      // 优化滚动条边界处理
      thumbColor: Colors.grey[400], // 设置滚动条颜色
      trackColor: Colors.grey[200], // 设置轨道颜色
      child: SingleChildScrollView(
        controller: _tableScrollController, // 使用内容滚动控制器
        scrollDirection: Axis.horizontal,
        child: SizedBox(
          width: _getTableWidth(), // 计算表格总宽度
          // 通过固定高度强制显示垂直滚动条
          height: availableWidth - 10, // 从50减小到10，减少底部空白区域，与编辑入库单保持一致
          child: Stack(
            children: [
              // 垂直滚动视图
              SingleChildScrollView(
                scrollDirection: Axis.vertical,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: stockInItems.asMap().entries.map((entry) {
                    final index = entry.key;
                    final item = entry.value;
                    return _buildStockInItemRow(item, index);
                  }).toList(),
                ),
              ),
              // 添加垂直滚动条
              Positioned(
                right: 0,
                top: 0,
                bottom: 0,
                child: Container(
                  width: 6, // 从8减小到6，与编辑入库单保持一致
                  decoration: BoxDecoration(
                    color: Colors.grey.withOpacity(
                      0.2,
                    ), // 从0.3降低到0.2，与编辑入库单保持一致
                    borderRadius: BorderRadius.circular(3), // 从4减小到3，与编辑入库单保持一致
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建响应式表格（小屏幕，自适应宽度）
  Widget _buildResponsiveTable(double availableWidth) {
    return SingleChildScrollView(
      scrollDirection: Axis.vertical,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: stockInItems.asMap().entries.map((entry) {
          final index = entry.key;
          final item = entry.value;
          return _buildResponsiveStockInItemRow(item, index, availableWidth);
        }).toList(),
      ),
    );
  }

  /// 计算表格总宽度
  double _getTableWidth() {
    // 与编辑入库单表格保持完全一致的列宽度：
    // 序号(60) + 条码(120) + 商品名称(150) + 分类(100) + 圈口号(80) +
    // 金重(90) + 金价(90) + 银重(90) + 银价(90) + 总重(90) +
    // 工费方式(90) + 工费(90) + 电铸费(90) + 批发工费(90) +
    // 零售工费(90) + 件工费(90) + 总成本(100) + 操作(70)
    const totalWidth =
        60.0 +
        120.0 +
        150.0 +
        100.0 +
        80.0 +
        90.0 +
        90.0 +
        90.0 +
        90.0 +
        90.0 +
        90.0 +
        90.0 +
        90.0 +
        90.0 +
        90.0 +
        90.0 +
        100.0 +
        70.0; // 总计: 1680px

    // 调试输出
    LoggerService.d('表格总宽度: ${totalWidth}px');

    return totalWidth;
  }

  /// 获取或创建TextEditingController
  TextEditingController _getOrCreateController(String id, String initialValue) {
    if (!_textControllers.containsKey(id)) {
      _textControllers[id] = TextEditingController(text: initialValue);
    } else {
      // 只有在控制器没有焦点且值确实不同时才更新
      // 这样可以避免在用户输入过程中被中断
      final controller = _textControllers[id]!;
      if (!controller.selection.isValid && controller.text != initialValue) {
        controller.text = initialValue;
      }
    }
    return _textControllers[id]!;
  }

  /// 构建表格头部单元格 - 与编辑入库单保持一致
  Widget _buildHeaderCell(String text, double width) {
    return Container(
      width: width,
      height: 48, // 固定高度与表头容器一致
      padding: const EdgeInsets.symmetric(horizontal: 4), // 与编辑入库单保持一致
      decoration: AppBorderStyles.headerCellDecoration(),
      alignment: Alignment.center, // 确保文本垂直居中
      child: Text(
        text,
        style: const TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 14, // 字体大小保持不变
          color: Colors.black87, // 与编辑入库单保持一致
        ),
        textAlign: TextAlign.center,
        overflow: TextOverflow.ellipsis,
        maxLines: 1,
      ),
    );
  }

  /// 构建入库单商品明细行（固定宽度版本）
  Widget _buildStockInItemRow(StockInJewelryItem item, int index) {
    return Container(
      height: 52, // 固定行高
      decoration: BoxDecoration(
        border: const Border(bottom: AppBorderStyles.tableBorder),
        color: index.isEven
            ? AppBorderStyles.tableEvenRowBackground
            : AppBorderStyles.tableOddRowBackground,
      ),
      child: Row(
        children: [
          // 序号
          _buildDataCell('${index + 1}', 60, isEditable: false),
          // 条码
          _buildEditableCell(
            item.barcode,
            120,
            onChanged: (value) {
              item.barcode = value;
              _validateAndUpdate(item);
            },
            errorText: item.validationErrors['barcode'],
            controllerId: 'barcode_${item.id}_$index',
          ),
          // 商品名称
          _buildEditableCell(
            item.name,
            150,
            onChanged: (value) {
              item.name = value;
              _validateAndUpdate(item);
            },
            errorText: item.validationErrors['name'],
            controllerId: 'name_${item.id}_$index',
          ),
          // 分类
          _buildCategoryDropdownCell(item, 100), // 改为100px与编辑入库单保持一致
          // 圈口号
          _buildEditableCell(
            item.ringSize,
            80, // 与编辑入库单保持一致
            onChanged: (value) {
              item.ringSize = value;
              _validateAndUpdate(item);
            },
            controllerId: 'ringSize_${item.id}_$index',
          ),
          // 金重
          _buildNumberCell(
            item.goldWeight,
            90, // 与编辑入库单保持一致
            onChanged: (value) {
              item.goldWeight = value;
              _validateAndUpdate(item);
            },
            errorText: item.validationErrors['goldWeight'],
            controllerId: 'goldWeight_${item.id}_$index',
          ),
          // 金价
          _buildNumberCell(
            item.goldPrice,
            90, // 与编辑入库单保持一致
            onChanged: (value) {
              item.goldPrice = value;
              _validateAndUpdate(item);
            },
            errorText: item.validationErrors['goldPrice'],
            controllerId: 'goldPrice_${item.id}_$index',
          ),
          // 银重
          _buildNumberCell(
            item.silverWeight,
            90, // 与编辑入库单保持一致
            onChanged: (value) {
              item.silverWeight = value;
              _validateAndUpdate(item);
            },
            errorText: item.validationErrors['silverWeight'],
            controllerId: 'silverWeight_${item.id}_$index',
          ),
          // 银价
          _buildNumberCell(
            item.silverPrice,
            90, // 与编辑入库单保持一致
            onChanged: (value) {
              item.silverPrice = value;
              _validateAndUpdate(item);
            },
            errorText: item.validationErrors['silverPrice'],
            controllerId: 'silverPrice_${item.id}_$index',
          ),
          // 总重（自动计算）
          _buildDataCell(
            item.totalWeight.toStringAsFixed(2),
            90, // 与编辑入库单保持一致
            isEditable: false,
            textColor: const Color(0xFF1E88E5), // 符合UI规范的主色调
          ),
          // 工费方式
          _buildWorkTypeDropdownCell(item, 90), // 缩小工费方式列宽度
          // 工费
          _buildNumberCell(
            item.workPrice,
            90, // 与编辑入库单保持一致
            onChanged: (value) {
              item.workPrice = value;
              _validateAndUpdate(item);
            },
            errorText: item.validationErrors['workPrice'],
            controllerId: 'workPrice_${item.id}_$index',
          ),
          // 电铸费
          _buildNumberCell(
            item.platingCost,
            90, // 与编辑入库单保持一致
            onChanged: (value) {
              item.platingCost = value;
              _validateAndUpdate(item);
            },
            errorText: item.validationErrors['platingCost'],
            controllerId: 'platingCost_${item.id}_$index',
          ),
          // 批发工费
          _buildNumberCell(
            item.wholesaleWorkPrice,
            90, // 与编辑入库单保持一致
            onChanged: (value) {
              item.wholesaleWorkPrice = value;
              _validateAndUpdate(item);
            },
            controllerId: 'wholesaleWorkPrice_${item.id}_$index',
          ),
          // 零售工费
          _buildNumberCell(
            item.retailWorkPrice,
            90, // 与编辑入库单保持一致
            onChanged: (value) {
              item.retailWorkPrice = value;
              _validateAndUpdate(item);
            },
            controllerId: 'retailWorkPrice_${item.id}_$index',
          ),
          // 件工费
          _buildNumberCell(
            item.pieceWorkPrice,
            90, // 与编辑入库单保持一致
            onChanged: (value) {
              item.pieceWorkPrice = value;
              _validateAndUpdate(item);
            },
            controllerId: 'pieceWorkPrice_${item.id}_$index',
          ),
          // 总成本（自动计算）
          _buildDataCell(
            item.totalCost.toStringAsFixed(2),
            100, // 与编辑入库单保持一致
            isEditable: false,
            textColor: const Color(0xFF388E3C), // 符合UI规范的成功色
            fontWeight: FontWeight.w600,
          ),
          // 操作
          _buildActionCell(index, 70), // 与编辑入库单保持一致
        ],
      ),
    );
  }

  /// 构建响应式入库单商品明细行（小屏幕优化版本）
  Widget _buildResponsiveStockInItemRow(
    StockInJewelryItem item,
    int index,
    double availableWidth,
  ) {
    // 计算响应式列宽度
    final columnWidths = _calculateResponsiveColumnWidths(availableWidth);

    return Container(
      height: 52, // 固定行高，与新建入库单保持一致
      decoration: BoxDecoration(
        border: const Border(bottom: AppBorderStyles.tableBorder),
        color: index.isEven
            ? AppBorderStyles.tableEvenRowBackground
            : AppBorderStyles.tableOddRowBackground,
      ),
      child: Row(
        children: [
          // 序号
          _buildDataCell(
            '${index + 1}',
            columnWidths['序号']!,
            isEditable: false,
          ),
          // 条码
          _buildEditableCell(
            item.barcode,
            columnWidths['条码']!,
            onChanged: (value) {
              item.barcode = value;
              _validateAndUpdate(item);
            },
            errorText: item.validationErrors['barcode'],
            controllerId: 'barcode_${item.id}_$index',
          ),
          // 商品名称
          _buildEditableCell(
            item.name,
            columnWidths['商品名称']!,
            onChanged: (value) {
              item.name = value;
              _validateAndUpdate(item);
            },
            errorText: item.validationErrors['name'],
            controllerId: 'name_${item.id}_$index',
          ),
          // 分类
          _buildCategoryDropdownCell(item, columnWidths['分类']!),
          // 金重
          _buildNumberCell(
            item.goldWeight,
            columnWidths['金重']!,
            onChanged: (value) {
              item.goldWeight = value;
              _validateAndUpdate(item);
            },
            errorText: item.validationErrors['goldWeight'],
            controllerId: 'goldWeight_${item.id}_$index',
          ),
          // 银重
          _buildNumberCell(
            item.silverWeight,
            columnWidths['银重']!,
            onChanged: (value) {
              item.silverWeight = value;
              _validateAndUpdate(item);
            },
            errorText: item.validationErrors['silverWeight'],
            controllerId: 'silverWeight_${item.id}_$index',
          ),
          // 工费
          _buildNumberCell(
            item.workPrice,
            columnWidths['工费']!,
            onChanged: (value) {
              item.workPrice = value;
              _validateAndUpdate(item);
            },
            errorText: item.validationErrors['workPrice'],
            controllerId: 'workPrice_${item.id}_$index',
          ),
          // 总成本（自动计算）
          _buildDataCell(
            item.totalCost.toStringAsFixed(2),
            columnWidths['总成本']!,
            isEditable: false,
            textColor: const Color(0xFF388E3C), // 符合UI规范的成功色
            fontWeight: FontWeight.w600,
          ),
          // 操作 - 确保删除按钮始终可见
          _buildResponsiveActionCell(index, columnWidths['操作']!),
        ],
      ),
    );
  }

  /// 计算响应式列宽度
  Map<String, double> _calculateResponsiveColumnWidths(double availableWidth) {
    // 为小屏幕优化的列配置
    // 只显示最重要的列，确保删除按钮可见
    final Map<String, double> columnWidths = {};

    // 预留操作列宽度（删除按钮）
    const actionWidth = 80.0;
    final contentWidth = availableWidth - actionWidth - 32; // 减去边距

    // 按重要性分配宽度
    columnWidths['序号'] = contentWidth * 0.08; // 8%
    columnWidths['条码'] = contentWidth * 0.20; // 20%
    columnWidths['商品名称'] = contentWidth * 0.25; // 25%
    columnWidths['分类'] = contentWidth * 0.12; // 12%
    columnWidths['金重'] = contentWidth * 0.12; // 12%
    columnWidths['银重'] = contentWidth * 0.12; // 12%
    columnWidths['工费'] = contentWidth * 0.11; // 11%
    columnWidths['总成本'] = contentWidth * 0.15; // 15%
    columnWidths['操作'] = actionWidth; // 固定80px

    LoggerService.d('📱 响应式列宽度分配:');
    columnWidths.forEach((key, value) {
      LoggerService.d('   $key: ${value.toStringAsFixed(1)}px');
    });

    return columnWidths;
  }

  /// 构建操作单元格
  Widget _buildActionCell(int index, double width) {
    return Container(
      width: width,
      height: 52, // 固定高度与数据行一致，与新建入库单保持一致
      padding: const EdgeInsets.symmetric(
        horizontal: 8,
        vertical: 8,
      ), // 与编辑入库单保持一致
      decoration: const BoxDecoration(
        border: Border(right: AppBorderStyles.tableBorder),
      ),
      alignment: Alignment.center, // 确保按钮垂直居中
      child: Center(
        child: IconButton(
          icon: const Icon(
            Icons.delete_outline,
            color: Color(0xFFD32F2F), // 符合UI规范的错误色
            size: 20,
          ),
          onPressed: () => _removeStockInItem(index),
          tooltip: '删除商品',
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
          style: IconButton.styleFrom(
            backgroundColor: Colors.transparent,
            foregroundColor: const Color(0xFFD32F2F),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(
                AppBorderStyles.mediumBorderRadius,
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建响应式操作单元格（确保删除按钮可见）
  Widget _buildResponsiveActionCell(int index, double width) {
    return Container(
      width: width,
      height: 52, // 固定高度与数据行一致，与新建入库单保持一致
      padding: const EdgeInsets.symmetric(
        horizontal: 8,
        vertical: 6,
      ), // 调整内边距以适应新高度
      decoration: const BoxDecoration(
        border: Border(right: AppBorderStyles.tableBorder),
      ),
      alignment: Alignment.center, // 确保按钮垂直居中
      child: Center(
        child: IconButton(
          icon: const Icon(
            Icons.delete_outline,
            color: Color(0xFFD32F2F), // 符合UI规范的错误色
            size: 18, // 稍微减小图标尺寸以适应小屏幕
          ),
          onPressed: () => _removeStockInItem(index),
          tooltip: '删除商品',
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(
            minWidth: 28,
            minHeight: 28,
          ), // 减小最小尺寸
          style: IconButton.styleFrom(
            backgroundColor: Colors.transparent,
            foregroundColor: const Color(0xFFD32F2F),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(
                AppBorderStyles.borderRadius,
              ), // 减小圆角
            ),
          ),
        ),
      ),
    );
  }

  /// 计算总计
  void _calculateTotals() {
    totalQuantity.value = stockInItems.length;
    totalAmount.value = stockInItems.fold(
      0.0,
      (sum, item) => sum + item.totalCost,
    );
  }

  /// 验证并更新数据
  void _validateAndUpdate(StockInJewelryItem item) {
    item.validate();
    _calculateTotals();
    stockInItems.refresh(); // 触发UI更新
  }

  /// 构建数据单元格 - 与编辑入库单保持一致
  Widget _buildDataCell(
    String text,
    double width, {
    bool isEditable = true,
    Color? textColor,
    FontWeight? fontWeight,
  }) {
    return Container(
      width: width,
      height: 52, // 固定高度
      padding: const EdgeInsets.symmetric(horizontal: 4), // 与编辑入库单保持一致
      decoration: const BoxDecoration(
        border: Border(right: AppBorderStyles.tableBorder),
      ),
      alignment: Alignment.center, // 确保文本垂直居中
      child: Text(
        text,
        style: TextStyle(
          fontSize: 12, // 与编辑入库单保持一致
          color: textColor ?? Colors.black87, // 与编辑入库单保持一致
          fontWeight: fontWeight,
        ),
        textAlign: TextAlign.center,
        overflow: TextOverflow.ellipsis,
        maxLines: 1,
      ),
    );
  }

  /// 构建可编辑单元格
  Widget _buildEditableCell(
    String value,
    double width, {
    required Function(String) onChanged,
    String? errorText,
    String? controllerId,
  }) {
    // 获取或创建控制器
    final controller = _getOrCreateController(controllerId ?? 'default', value);

    return Container(
      width: width,
      height: 52, // 固定高度与数据行一致，与新建入库单保持一致
      padding: const EdgeInsets.symmetric(
        horizontal: 8,
        vertical: 8,
      ), // 与编辑入库单保持一致
      decoration: const BoxDecoration(
        border: Border(right: AppBorderStyles.tableBorder),
      ),
      alignment: Alignment.center, // 确保输入框垂直居中
      child: TextField(
        controller: controller,
        style: const TextStyle(
          fontSize: 14, // 符合UI规范的次要文本大小
          color: Color(0xFF212121), // 符合UI规范的主要文本色
        ),
        textAlign: TextAlign.center, // 文本居中对齐
        decoration: InputDecoration(
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
            borderSide: BorderSide(
              color: errorText != null
                  ? const Color(0xFFD32F2F)
                  : AppBorderStyles.borderColor,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
            borderSide: BorderSide(
              color: errorText != null
                  ? const Color(0xFFD32F2F)
                  : AppBorderStyles.borderColor,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
            borderSide: BorderSide(
              color: errorText != null
                  ? const Color(0xFFD32F2F)
                  : AppBorderStyles.focusBorderColor,
              width: AppBorderStyles.focusBorderWidth,
            ),
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 8,
            vertical: 6,
          ), // 调整内边距以适应新高度
          isDense: true,
          errorText: errorText,
          errorStyle: const TextStyle(fontSize: 10),
        ),
        onChanged: onChanged,
      ),
    );
  }

  /// 构建数字输入单元格
  Widget _buildNumberCell(
    double value,
    double width, {
    required Function(double) onChanged,
    String? errorText,
    String? controllerId,
  }) {
    // 创建专用的数字输入控制器，避免频繁更新导致输入中断
    final uniqueId = controllerId ?? 'number_default';

    // 只在控制器不存在时创建，避免重复创建
    if (!_textControllers.containsKey(uniqueId)) {
      final displayValue = value == 0.0 ? '' : value.toString();
      _textControllers[uniqueId] = TextEditingController(text: displayValue);
    }

    final controller = _textControllers[uniqueId]!;

    return Container(
      width: width,
      height: 52, // 固定高度与数据行一致，与新建入库单保持一致
      padding: const EdgeInsets.symmetric(
        horizontal: 8,
        vertical: 8,
      ), // 与编辑入库单保持一致
      decoration: const BoxDecoration(
        border: Border(right: AppBorderStyles.tableBorder),
      ),
      alignment: Alignment.center, // 确保输入框垂直居中
      child: TextField(
        controller: controller,
        style: const TextStyle(
          fontSize: 14, // 符合UI规范的次要文本大小
          color: Color(0xFF212121), // 符合UI规范的主要文本色
        ),
        textAlign: TextAlign.center, // 文本居中对齐
        decoration: InputDecoration(
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
            borderSide: BorderSide(
              color: errorText != null
                  ? const Color(0xFFD32F2F)
                  : AppBorderStyles.borderColor,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
            borderSide: BorderSide(
              color: errorText != null
                  ? const Color(0xFFD32F2F)
                  : AppBorderStyles.borderColor,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
            borderSide: BorderSide(
              color: errorText != null
                  ? const Color(0xFFD32F2F)
                  : AppBorderStyles.focusBorderColor,
              width: AppBorderStyles.focusBorderWidth,
            ),
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 8,
            vertical: 6,
          ), // 调整内边距以适应新高度
          isDense: true,
          errorText: errorText,
          errorStyle: const TextStyle(fontSize: 10),
        ),
        keyboardType: const TextInputType.numberWithOptions(decimal: true),
        inputFormatters: [
          // 允许数字、小数点，支持多位数字和小数
          FilteringTextInputFormatter.allow(
            RegExp(r'^\d*\.?\d{0,3}$'),
          ), // 最多3位小数
        ],
        onChanged: (text) {
          // 优化数字输入处理：支持完整的数值范围
          if (text.isEmpty) {
            onChanged(0.0);
            return;
          }

          // 处理只输入小数点的情况
          if (text == '.') {
            return; // 允许用户继续输入
          }

          // 解析数值
          final newValue = double.tryParse(text);
          if (newValue != null) {
            onChanged(newValue);
          }
        },
        onTap: () {
          // 当用户点击输入框时，如果是默认值0，清空输入框便于输入
          if (controller.text == '0' || controller.text == '0.0') {
            controller.clear();
          }
        },
      ),
    );
  }

  /// 构建分类下拉单元格
  Widget _buildCategoryDropdownCell(StockInJewelryItem item, double width) {
    return Container(
      width: width,
      height: 52, // 固定高度与数据行一致，与新建入库单保持一致
      padding: const EdgeInsets.symmetric(
        horizontal: 8,
        vertical: 8,
      ), // 与编辑入库单保持一致
      decoration: const BoxDecoration(
        border: Border(right: AppBorderStyles.tableBorder),
      ),
      alignment: Alignment.center, // 确保下拉框垂直居中
      child: Obx(() {
        // 如果分类列表为空，显示加载中提示
        if (categoryList.isEmpty) {
          return Container(
            decoration: BoxDecoration(
              border: Border.all(color: AppBorderStyles.borderColor),
              borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
            ),
            child: const Center(
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 8),
                child: Text(
                  '加载中...',
                  style: TextStyle(fontSize: 12, color: Colors.grey),
                ),
              ),
            ),
          );
        }

        return DropdownButtonFormField<int?>(
          value: item.categoryId > 0 ? item.categoryId : null,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
              borderSide: AppBorderStyles.standardBorder,
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
              borderSide: AppBorderStyles.standardBorder,
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
              borderSide: AppBorderStyles.focusBorder,
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 8,
              vertical: 6,
            ), // 调整内边距以适应新高度
            isDense: true,
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
              borderSide: BorderSide(color: Colors.red[300]!),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
              borderSide: BorderSide(color: Colors.red[500]!, width: 1.5),
            ),
          ),
          style: const TextStyle(
            fontSize: 14, // 符合UI规范的次要文本大小
            color: Color(0xFF212121), // 符合UI规范的主要文本色
          ),
          dropdownColor: Colors.white,
          icon: const Icon(Icons.arrow_drop_down, size: 20),
          isExpanded: true, // 确保下拉框占满整个容器
          alignment: Alignment.center, // 下拉框文字居中
          selectedItemBuilder: (context) {
            // 自定义选中项的样式
            return [
              const DropdownMenuItem<int?>(
                value: null,
                child: Center(
                  child: Text(
                    '请选择',
                    style: TextStyle(fontSize: 14, color: Color(0xFF757575)),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
              ...categoryList.map(
                (category) => DropdownMenuItem<int?>(
                  value: category.id,
                  child: Center(
                    child: Text(
                      category.name,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Color(0xFF212121),
                      ),
                      textAlign: TextAlign.center,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
              ),
            ];
          },
          items: [
            const DropdownMenuItem<int?>(
              value: null,
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 4),
                child: Text(
                  '请选择',
                  style: TextStyle(fontSize: 14, color: Color(0xFF757575)),
                ),
              ),
            ),
            ...categoryList.map(
              (category) => DropdownMenuItem<int?>(
                value: category.id,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 4),
                  child: Text(
                    category.name,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF212121),
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
            ),
          ],
          onChanged: (value) {
            item.categoryId = value ?? 0;
            if (value != null && value > 0) {
              final selectedCategory = categoryList.firstWhere(
                (cat) => cat.id == value,
                orElse: () =>
                    const JewelryCategory(id: 0, name: '', parentId: 0),
              );
              item.categoryName = selectedCategory.name;
            } else {
              item.categoryName = '';
            }
            _validateAndUpdate(item);
          },
        );
      }),
    );
  }

  /// 构建工费方式下拉单元格
  Widget _buildWorkTypeDropdownCell(StockInJewelryItem item, double width) {
    return Container(
      width: width,
      height: 52, // 固定高度与数据行一致，与新建入库单保持一致
      padding: const EdgeInsets.symmetric(
        horizontal: 8,
        vertical: 8,
      ), // 与编辑入库单保持一致
      decoration: const BoxDecoration(
        border: Border(right: AppBorderStyles.tableBorder),
      ),
      alignment: Alignment.center, // 确保下拉框垂直居中
      child: DropdownButtonFormField<int>(
        value: item.workType,
        decoration: InputDecoration(
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
            borderSide: AppBorderStyles.standardBorder,
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
            borderSide: AppBorderStyles.standardBorder,
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
            borderSide: AppBorderStyles.focusBorder,
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 8,
            vertical: 6,
          ), // 调整内边距以适应新高度
          isDense: true,
        ),
        style: const TextStyle(
          fontSize: 14, // 符合UI规范的次要文本大小
          color: Color(0xFF212121), // 符合UI规范的主要文本色
        ),
        alignment: Alignment.center, // 下拉框文字居中
        items: workTypeOptions
            .map(
              (option) => DropdownMenuItem<int>(
                value: option['value'],
                child: Center(
                  child: Text(
                    option['label'],
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF212121),
                    ),
                  ),
                ),
              ),
            )
            .toList(),
        onChanged: (value) {
          item.workType = value ?? 0;
          _validateAndUpdate(item);
        },
      ),
    );
  }

  /// 删除入库单商品明细
  void _removeStockInItem(int index) {
    if (index >= 0 && index < stockInItems.length) {
      final item = stockInItems[index];
      stockInItems.removeAt(index);
      _calculateTotals();

      Get.snackbar(
        '提示',
        '已删除商品: ${item.name.isNotEmpty ? item.name : '未命名商品'}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
    }
  }

  /// 验证条码唯一性（包括数据库检查）
  Future<bool> _validateBarcodeUniqueness(
    String barcode,
    int currentIndex,
  ) async {
    LoggerService.d('🔍 开始验证条码唯一性: $barcode (索引: $currentIndex)');

    // 1. 检查当前表格中的条码重复
    for (int i = 0; i < stockInItems.length; i++) {
      if (i != currentIndex && stockInItems[i].barcode == barcode) {
        LoggerService.w('❌ 条码重复：在表格第${i + 1}行发现相同条码 $barcode');
        return false;
      }
    }
    LoggerService.d('✅ 表格中条码检查通过');

    // 2. 检查数据库中是否存在重复条码
    try {
      final jewelryService = Get.find<JewelryService>();
      LoggerService.d('🌐 开始查询数据库中的条码: $barcode');

      final existingJewelry = await jewelryService.getJewelryByBarcode(barcode);

      if (existingJewelry != null) {
        LoggerService.w('❌ 条码重复：数据库中已存在条码 $barcode');
        return false;
      } else {
        LoggerService.d('✅ 数据库中条码检查通过：条码 $barcode 不存在，可以使用');
        return true;
      }
    } catch (e) {
      LoggerService.d('🔍 条码检测异常分析: ${e.toString()}');

      // 检查是否是404错误（表示条码不存在，可以使用）
      if (e.toString().contains('404') || e.toString().contains('商品不存在')) {
        LoggerService.d('✅ 404错误表示条码不存在，可以使用: $barcode');
        return true;
      }

      // 其他错误（网络错误、服务器错误等）
      LoggerService.e('❌ 条码重复检测失败，网络或服务器错误', e);
      // 网络错误时，为了不阻塞用户操作，允许使用该条码
      // 但会在日志中记录，后续可以通过服务端验证
      LoggerService.w('⚠️ 由于网络错误，暂时允许使用条码 $barcode，请确保条码唯一性');
      return true;
    }
  }

  /// 验证整个表单
  bool validateForm() {
    bool isValid = true;
    List<String> errors = [];

    // 验证门店选择
    if (selectedStoreId.value <= 0) {
      errors.add('请选择门店');
      isValid = false;
    }

    // 验证商品明细
    if (stockInItems.isEmpty) {
      errors.add('请至少添加一个商品');
      isValid = false;
    } else {
      // 验证每个商品明细
      for (int i = 0; i < stockInItems.length; i++) {
        final item = stockInItems[i];

        // 验证条码唯一性（暂时跳过异步检查，在提交时再进行完整验证）
        // 只检查当前表格中的重复
        bool isDuplicate = false;
        for (int j = 0; j < stockInItems.length; j++) {
          if (j != i && stockInItems[j].barcode == item.barcode) {
            isDuplicate = true;
            break;
          }
        }
        if (isDuplicate) {
          errors.add('第${i + 1}行：条码重复');
          isValid = false;
        }

        // 验证商品数据
        if (!item.validate()) {
          errors.add('第${i + 1}行：${item.validationErrors.values.first}');
          isValid = false;
        }
      }
    }

    // 显示错误信息
    if (!isValid) {
      Get.snackbar(
        '验证失败',
        errors.join('\n'),
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        duration: const Duration(seconds: 5),
      );
    }

    return isValid;
  }

  /// 完整的表单验证（包括数据库条码重复检测）
  Future<bool> _validateFormWithDatabaseCheck() async {
    bool isValid = true;
    List<String> errors = [];

    // 验证门店选择
    if (selectedStoreId.value <= 0) {
      errors.add('请选择门店');
      isValid = false;
    }

    // 验证商品明细
    if (stockInItems.isEmpty) {
      errors.add('请至少添加一个商品');
      isValid = false;
    } else {
      // 验证每个商品明细
      for (int i = 0; i < stockInItems.length; i++) {
        final item = stockInItems[i];

        // 验证条码唯一性（包括数据库检查）
        if (!(await _validateBarcodeUniqueness(item.barcode, i))) {
          errors.add('第${i + 1}行：条码重复或已存在');
          isValid = false;
        }

        // 验证商品数据
        if (!item.validate()) {
          errors.add('第${i + 1}行：${item.validationErrors.values.first}');
          isValid = false;
        }
      }
    }

    // 显示错误信息
    if (!isValid) {
      Get.snackbar(
        '验证失败',
        errors.join('\n'),
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        duration: const Duration(seconds: 5),
      );
    }

    return isValid;
  }

  /// 保存草稿
  Future<void> saveDraft() async {
    if (!validateForm()) {
      return;
    }

    try {
      // TODO: 实现保存草稿API调用
      Get.snackbar(
        '成功',
        '草稿保存成功',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      LoggerService.e('保存草稿失败', e);
      Get.snackbar(
        '错误',
        '保存草稿失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 提交审核
  Future<void> submitForReview() async {
    // 先进行完整的表单验证（包括数据库条码重复检测）
    if (!(await _validateFormWithDatabaseCheck())) {
      return;
    }

    try {
      LoggerService.d('🚀 开始提交审核流程');
      LoggerService.d('📊 表单数据统计:');
      LoggerService.d('   门店ID: ${selectedStoreId.value}');
      LoggerService.d('   操作员ID: ${_authService.userId.value}');
      LoggerService.d('   商品数量: ${stockInItems.length}');
      LoggerService.d('   总金额: ${totalAmount.value}');
      LoggerService.d('   备注: ${remark.value}');

      // 验证商品数据完整性
      for (int i = 0; i < stockInItems.length; i++) {
        final item = stockInItems[i];
        LoggerService.d('📦 验证第${i + 1}个商品数据:');
        LoggerService.d('   条码: ${item.barcode}');
        LoggerService.d('   名称: ${item.name}');
        LoggerService.d('   分类ID: ${item.categoryId}');
        LoggerService.d('   金重: ${item.goldWeight}');
        LoggerService.d('   银重: ${item.silverWeight}');
        LoggerService.d('   工费: ${item.workPrice}');
        LoggerService.d('   总成本: ${item.totalCost}');
      }

      // 先创建入库单
      final stockIn = StockIn(
        id: 0,
        stockInNo: '',
        storeId: selectedStoreId.value,
        totalAmount: totalAmount.value,
        operatorId: _authService.userId.value,
        status: DocumentStatus.draft,
        createTime: DateTime.now(),
        remark: remark.value,
        items: stockInItems.asMap().entries.map((entry) {
          final index = entry.key;
          final item = entry.value;

          LoggerService.d('🔧 构建第${index + 1}个StockInItem数据:');

          // 创建一个扩展的Jewelry对象，包含StockInJewelryItem的所有价格信息
          // 使用Map来传递额外的价格数据，这样StockService可以获取到完整信息
          final jewelry = Jewelry(
            id: item.id > 0 ? item.id : (index + 1), // 使用索引确保唯一性
            name: item.name.isNotEmpty ? item.name : '默认商品名称',
            barcode: item.barcode.isNotEmpty
                ? item.barcode
                : 'DEFAULT_${DateTime.now().millisecondsSinceEpoch}_$index',
            categoryId: item.categoryId > 0 ? item.categoryId : 1, // 确保分类ID不为0
            goldWeight: item.goldWeight,
            goldPrice: item.goldPrice,
            silverWeight: item.silverWeight,
            silverPrice: item.silverPrice,
            totalWeight: item.totalWeight, // 使用StockInJewelryItem的总重量
            workPrice: item.workPrice,
            retailWorkPrice: 0.0, // 入库时没有零售工费信息，使用默认值
            wholesaleWorkPrice: 0.0, // 入库时没有批发工费信息，使用默认值
            pieceWorkPrice: item.pieceWorkPrice,
            salePrice: item.totalCost,
            storeId: selectedStoreId.value,
            status: JewelryStatus.onShelf,
            createTime: DateTime.now(),
            category: JewelryCategory(
              id: item.categoryId > 0 ? item.categoryId : 1,
              name: item.categoryName.isNotEmpty ? item.categoryName : '默认分类',
              parentId: 0,
              sortOrder: 0,
              isActive: true,
            ),
          );

          LoggerService.d('🔧 第${index + 1}个商品的价格信息:');
          LoggerService.d('   金价: ${item.goldPrice}元/g');
          LoggerService.d('   银价: ${item.silverPrice}元/g');
          LoggerService.d('   工费方式: ${item.workType} (0=按克, 1=按件)');
          LoggerService.d('   工费: ${item.workPrice}元');
          LoggerService.d('   电铸费: ${item.platingCost}元');
          LoggerService.d('   批发工费: ${item.wholesaleWorkPrice}元');
          LoggerService.d('   零售工费: ${item.retailWorkPrice}元');
          LoggerService.d('   件工费: ${item.pieceWorkPrice}元');

          final stockInItem = StockInItem(
            id: 0,
            stockInId: 0,
            jewelryId: jewelry.id,
            barcode: jewelry.barcode,
            name: jewelry.name,
            categoryId: jewelry.category?.id ?? 0,
            categoryName: jewelry.category?.name,
            totalPrice: item.totalCost,
            jewelry: jewelry,
          );

          LoggerService.d('✅ 第${index + 1}个StockInItem构建完成:');
          LoggerService.d('   Jewelry ID: ${jewelry.id}');
          LoggerService.d('   条码: ${jewelry.barcode}');
          LoggerService.d('   金额: ${stockInItem.amount}');

          return stockInItem;
        }).toList(),
      );

      LoggerService.d('📋 入库单对象构建完成，包含${stockIn.items?.length ?? 0}个商品明细');

      // 创建入库单 - 使用新的方法，直接传递StockInJewelryItem数据
      final stockService = Get.find<StockService>();
      final createdStockIn = await stockService.createStockInWithJewelryItems(
        stockIn,
        stockInItems,
      );

      if (createdStockIn != null) {
        // 获取创建的入库单ID
        final stockInId = createdStockIn.id;
        final auditorId = _authService.userId.value ?? 1;

        // 提交审核
        final submitResult = await stockService.submitStockInForApproval(
          stockInId,
          auditorId,
        );

        if (submitResult) {
          LoggerService.d('✅ 入库单提交审核成功，开始页面跳转和数据刷新流程');

          // 显示成功提示
          Get.snackbar(
            '成功',
            '入库单已提交审核，正在跳转到入库管理页面...',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.green,
            colorText: Colors.white,
            duration: const Duration(seconds: 2),
          );

          // 清空表单
          _clearForm();

          // 延迟执行页面跳转和数据刷新，确保用户能看到成功提示
          Future.delayed(const Duration(milliseconds: 500), () async {
            await _handleSuccessfulSubmission();
          });
        } else {
          Get.snackbar(
            '错误',
            '提交审核失败',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
        }
      } else {
        Get.snackbar(
          '错误',
          '创建入库单失败',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      LoggerService.e('提交审核失败', e);
      Get.snackbar(
        '错误',
        '提交审核失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 处理提交成功后的页面跳转和数据刷新
  Future<void> _handleSuccessfulSubmission() async {
    try {
      LoggerService.d('🔄 开始处理提交成功后的页面跳转和数据刷新');

      // 1. 关闭当前的"新建入库单"标签页
      await _closeCurrentStockInFormTab();

      // 2. 切换到入库管理标签页（如果不存在则创建）
      await _switchToStockInManagement();

      // 3. 刷新入库管理页面的数据
      await _refreshStockInData();

      LoggerService.d('✅ 页面跳转和数据刷新完成');
    } catch (e) {
      LoggerService.e('❌ 处理提交成功后的操作失败', e);
      // 即使跳转失败，也不影响用户，只记录错误
    }
  }

  /// 关闭当前的"新建入库单"标签页
  Future<void> _closeCurrentStockInFormTab() async {
    try {
      LoggerService.d('🗂️ 查找并关闭"新建入库单"标签页');

      // 查找所有"新建入库单"相关的标签页
      final tabsToClose = <int>[];
      for (int i = 0; i < tabManager.tabs.length; i++) {
        final tab = tabManager.tabs[i];
        if (tab.id.startsWith('stock_in_form_') && tab.canClose) {
          tabsToClose.add(i);
          LoggerService.d('   找到标签页: ${tab.title} (ID: ${tab.id})');
        }
      }

      // 从后往前关闭，避免索引变化问题
      for (int i = tabsToClose.length - 1; i >= 0; i--) {
        final tabIndex = tabsToClose[i];
        LoggerService.d('   关闭标签页索引: $tabIndex');
        tabManager.closeTab(tabIndex);
      }

      LoggerService.d('✅ 成功关闭 ${tabsToClose.length} 个"新建入库单"标签页');
    } catch (e) {
      LoggerService.e('❌ 关闭标签页失败', e);
    }
  }

  /// 切换到入库管理标签页
  Future<void> _switchToStockInManagement() async {
    try {
      LoggerService.d('📋 切换到入库管理标签页');

      // 查找是否已存在入库管理标签页
      final existingIndex = tabManager.tabs.indexWhere(
        (tab) => tab.id == 'stock_in',
      );

      if (existingIndex != -1) {
        // 如果已存在，直接切换到该标签页
        LoggerService.d('   入库管理标签页已存在，切换到索引: $existingIndex');
        tabManager.switchToTab(existingIndex);
      } else {
        // 如果不存在，创建新的入库管理标签页
        LoggerService.d('   入库管理标签页不存在，创建新标签页');
        openStockIn();
      }

      LoggerService.d('✅ 成功切换到入库管理标签页');
    } catch (e) {
      LoggerService.e('❌ 切换到入库管理标签页失败', e);
    }
  }

  /// 刷新入库管理页面的数据
  Future<void> _refreshStockInData() async {
    try {
      LoggerService.d('🔄 刷新入库管理页面数据');

      // 获取入库管理控制器并刷新数据
      if (Get.isRegistered<StockInController>()) {
        final stockInController = Get.find<StockInController>();
        LoggerService.d('   找到入库管理控制器，开始刷新数据');
        await stockInController.fetchStockInList();
        LoggerService.d('✅ 入库管理数据刷新完成');
      } else {
        LoggerService.w('⚠️ 入库管理控制器未注册，跳过数据刷新');
      }
    } catch (e) {
      LoggerService.e('❌ 刷新入库管理数据失败', e);
    }
  }

  /// 刷新出库管理页面的数据
  Future<void> _refreshStockOutData() async {
    try {
      LoggerService.d('🔄 刷新出库管理页面数据');

      // 获取出库管理控制器并刷新数据
      if (Get.isRegistered<StockOutController>()) {
        final stockOutController = Get.find<StockOutController>();
        LoggerService.d('   找到出库管理控制器，开始刷新数据');
        await stockOutController.fetchStockOutList();
        LoggerService.d('✅ 出库管理数据刷新完成');
      } else {
        LoggerService.w('⚠️ 出库管理控制器未注册，跳过数据刷新');
      }
    } catch (e) {
      LoggerService.e('❌ 刷新出库管理数据失败', e);
    }
  }

  /// 刷新库存查询页面的数据
  Future<void> _refreshStockQueryData() async {
    try {
      LoggerService.d('🔄 刷新库存查询页面数据');

      // 获取库存查询控制器并刷新数据
      if (Get.isRegistered<StockQueryController>()) {
        final stockQueryController = Get.find<StockQueryController>();
        LoggerService.d('   找到库存查询控制器，开始刷新数据');
        await stockQueryController.fetchJewelryList();
        LoggerService.d('✅ 库存查询数据刷新完成');
      } else {
        LoggerService.w('⚠️ 库存查询控制器未注册，跳过数据刷新');
      }
    } catch (e) {
      LoggerService.e('❌ 刷新库存查询数据失败', e);
      // 如果是专用刷新按钮调用，显示错误提示
      Get.snackbar(
        '错误',
        '刷新库存查询数据失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );
    }
  }

  /// 刷新库存总览页面的数据
  Future<void> _refreshStockOverviewData() async {
    try {
      LoggerService.d('🔄 刷新库存总览页面数据');

      // 库存总览页面使用StockQueryController来显示首饰数据
      if (Get.isRegistered<StockQueryController>()) {
        final stockQueryController = Get.find<StockQueryController>();
        LoggerService.d('   找到库存总览控制器，开始刷新数据');
        await stockQueryController.fetchJewelryList();
        LoggerService.d('✅ 库存总览数据刷新完成');
      } else {
        LoggerService.w('⚠️ 库存总览控制器未注册，跳过数据刷新');
      }
    } catch (e) {
      LoggerService.e('❌ 刷新库存总览数据失败', e);
    }
  }

  /// 清空表单
  void _clearForm() {
    stockInItems.clear();
    selectedStoreId.value = _authService.storeId.value; // 重置为用户默认门店
    remark.value = '';
    totalAmount.value = 0.0;
    totalQuantity.value = 0;
  }

  /// 批量导入商品（预留功能）
  Future<void> importJewelryFromExcel() async {
    Get.snackbar(
      '提示',
      '批量导入功能正在开发中',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.blue,
      colorText: Colors.white,
    );
  }

  /// 导出商品明细到Excel（预留功能）
  Future<void> exportJewelryToExcel() async {
    if (stockInItems.isEmpty) {
      Get.snackbar(
        '提示',
        '暂无商品明细可导出',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      return;
    }

    Get.snackbar(
      '提示',
      '导出功能正在开发中',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.blue,
      colorText: Colors.white,
    );
  }

  /// 复制商品行
  Future<void> _duplicateStockInItem(int index) async {
    if (index >= 0 && index < stockInItems.length) {
      final originalItem = stockInItems[index];
      final newBarcode = await _generateBarcode(); // 生成新的条码
      final duplicatedItem = originalItem.copyWith(id: 0, barcode: newBarcode);
      stockInItems.insert(index + 1, duplicatedItem);
      _calculateTotals();

      Get.snackbar(
        '提示',
        '已复制商品: ${originalItem.name.isNotEmpty ? originalItem.name : '未命名商品'}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.blue,
        colorText: Colors.white,
      );
    }
  }

  /// 批量删除商品
  void _batchDeleteItems(List<int> indices) {
    if (indices.isEmpty) return;

    // 按索引倒序删除，避免索引变化问题
    indices.sort((a, b) => b.compareTo(a));

    for (final index in indices) {
      if (index >= 0 && index < stockInItems.length) {
        stockInItems.removeAt(index);
      }
    }

    _calculateTotals();

    Get.snackbar(
      '提示',
      '已删除 ${indices.length} 个商品',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.orange,
      colorText: Colors.white,
    );
  }

  /// 获取表单数据用于API提交
  Map<String, dynamic> getFormData() {
    return {
      'store_id': selectedStoreId.value,
      'remark': remark.value,
      'total_amount': totalAmount.value,
      'total_quantity': totalQuantity.value,
      'items': stockInItems.map((item) => item.toJson()).toList(),
    };
  }

  /// 从表单数据加载（用于草稿恢复）
  void loadFormData(Map<String, dynamic> data) {
    selectedStoreId.value = data['store_id'] ?? 0;
    remark.value = data['remark'] ?? '';

    if (data['items'] != null) {
      final items = (data['items'] as List)
          .map((item) => StockInJewelryItem.fromJson(item))
          .toList();
      stockInItems.value = items;
      _calculateTotals();
    }
  }

  /// 显示批量导入对话框
  Future<void> _showBatchImportDialog() async {
    try {
      // 选择文件
      final file = await _batchImportService.pickFile();
      if (file == null) {
        return;
      }

      // 显示加载对话框
      Get.dialog(
        const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('正在解析文件...'),
            ],
          ),
        ),
        barrierDismissible: false,
      );

      // 解析文件
      BatchImportPreviewData? previewData;
      final fileName = file.path.toLowerCase();

      if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls')) {
        previewData = await _batchImportService.parseExcelFile(file);
      } else if (fileName.endsWith('.csv')) {
        previewData = await _batchImportService.parseCsvFile(file);
      }

      // 关闭加载对话框
      Get.back();

      if (previewData == null) {
        Get.snackbar(
          '错误',
          '文件解析失败，请检查文件格式',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return;
      }

      // 显示预览对话框
      _showImportPreviewDialog(previewData);
    } catch (e) {
      // 关闭可能存在的加载对话框
      if (Get.isDialogOpen == true) {
        Get.back();
      }

      LoggerService.e('批量导入失败', e);
      Get.snackbar(
        '错误',
        '批量导入失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 计算表格实际需要的宽度
  double _calculateTableWidth(List<String> headers) {
    // 根据表头数量和内容估算宽度
    double totalWidth = 0;

    for (String header in headers) {
      // 根据表头内容估算列宽
      switch (header) {
        case '条码':
          totalWidth += 120;
          break;
        case '商品名称':
          totalWidth += 140;
          break;
        case '分类':
        case '类别':
          totalWidth += 80;
          break;
        case '圈口号':
          totalWidth += 70;
          break;
        case '金重(g)':
        case '银重(g)':
        case '总重(g)':
          totalWidth += 80;
          break;
        case '金价(元/g)':
        case '银价(元/g)':
          totalWidth += 90;
          break;
        case '工费(元)':
        case '工费':
          totalWidth += 80;
          break;
        case '工费方式':
          totalWidth += 80;
          break;
        case '电铸费(元)':
        case '批发工费(元)':
        case '零售工费(元)':
        case '件工费(元)':
          totalWidth += 90;
          break;
        default:
          // 默认列宽
          totalWidth += math.max(60, header.length * 8 + 20);
      }
    }

    // 添加列间距和边距
    totalWidth += (headers.length - 1) * 8; // 列间距
    totalWidth += 16; // 水平边距

    return totalWidth;
  }

  /// 显示导入预览对话框（极致紧凑布局，消除空白区域）
  void _showImportPreviewDialog(BatchImportPreviewData previewData) {
    // 计算表格实际需要的宽度
    final tableWidth = _calculateTableWidth(previewData.headers);
    final dialogWidth = (tableWidth + 60).clamp(
      600.0,
      Get.width * 0.9,
    ); // 最小600px，最大90%屏幕宽度

    Get.dialog(
      AlertDialog(
        title: const Text(
          '导入预览',
          style: TextStyle(fontSize: 15, fontWeight: FontWeight.w600),
        ),
        titlePadding: const EdgeInsets.fromLTRB(16, 12, 16, 4), // 进一步减少内边距
        contentPadding: const EdgeInsets.fromLTRB(16, 4, 16, 4), // 极小内边距
        actionsPadding: const EdgeInsets.fromLTRB(16, 4, 16, 12), // 极小内边距
        insetPadding: const EdgeInsets.symmetric(
          horizontal: 20,
          vertical: 40,
        ), // 减少对话框外边距
        content: SizedBox(
          width: dialogWidth, // 根据内容动态调整宽度
          height: Get.height * 0.75, // 增加高度利用率
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min, // 使用最小尺寸
            children: [
              // 统计信息（极致紧凑布局）
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 10,
                  vertical: 6,
                ), // 进一步减少内边距
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(4), // 更小圆角
                ),
                child: IntrinsicHeight(
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          '总行数: ${previewData.totalRowCount}',
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      VerticalDivider(width: 1, color: Colors.grey[300]),
                      Expanded(
                        child: Text(
                          '有效: ${previewData.validRowCount}',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: Colors.green[600],
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      VerticalDivider(width: 1, color: Colors.grey[300]),
                      Expanded(
                        child: Text(
                          '无效: ${previewData.invalidRowCount}',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: Colors.red[600],
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 8), // 进一步减少间距
              // 错误详情（如果有无效数据，极致紧凑布局）
              if (previewData.invalidRowCount > 0) ...[
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 10,
                    vertical: 6,
                  ), // 进一步减少内边距
                  decoration: BoxDecoration(
                    color: Colors.red[50],
                    borderRadius: BorderRadius.circular(4), // 更小圆角
                    border: Border.all(
                      color: Colors.red[200]!,
                      width: 0.5,
                    ), // 更细边框
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.error_outline,
                            color: Colors.red[600],
                            size: 14,
                          ), // 更小图标
                          const SizedBox(width: 4),
                          Text(
                            '错误详情',
                            style: TextStyle(
                              fontSize: 11, // 更小字体
                              fontWeight: FontWeight.w600,
                              color: Colors.red[600],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4), // 更小间距
                      ConstrainedBox(
                        constraints: const BoxConstraints(
                          maxHeight: 60,
                        ), // 进一步减少高度
                        child: SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: previewData.validationResults
                                .where((result) => !result.isValid)
                                .take(5) // 只显示前5个错误，避免占用过多空间
                                .map(
                                  (result) => Padding(
                                    padding: const EdgeInsets.only(
                                      bottom: 1,
                                    ), // 极小行间距
                                    child: Text(
                                      '第${result.rowIndex}行: ${result.errors.join(', ')}',
                                      style: TextStyle(
                                        fontSize: 10, // 更小字体
                                        color: Colors.red[700],
                                      ),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                )
                                .toList(),
                          ),
                        ),
                      ),
                      if (previewData.validationResults
                              .where((result) => !result.isValid)
                              .length >
                          5)
                        Text(
                          '... 还有${previewData.validationResults.where((result) => !result.isValid).length - 5}个错误',
                          style: TextStyle(fontSize: 9, color: Colors.red[500]),
                        ),
                    ],
                  ),
                ),
                const SizedBox(height: 6), // 进一步减少间距
              ],
              // 数据预览表格（极致紧凑布局，消除空白）
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      '数据预览（前20行）',
                      style: TextStyle(
                        fontSize: 11, // 更小字体
                        fontWeight: FontWeight.w600,
                        color: Colors.grey[700],
                      ),
                    ),
                    const SizedBox(height: 4), // 更小间距
                    Expanded(
                      child: Container(
                        width: double.infinity, // 确保容器占满宽度
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: AppBorderStyles.borderColor,
                            width: 0.5,
                          ),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(4),
                          child: SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: ConstrainedBox(
                              constraints: BoxConstraints(
                                minWidth: dialogWidth - 32, // 确保表格至少占满对话框宽度
                              ),
                              child: SingleChildScrollView(
                                child: DataTable(
                                  columnSpacing: 6, // 进一步减少列间距
                                  horizontalMargin: 4, // 进一步减少水平边距
                                  headingRowHeight: 28, // 进一步减少表头行高
                                  dataRowMinHeight: 24, // 进一步减少数据行高
                                  dataRowMaxHeight: 24,
                                  headingRowColor: WidgetStateProperty.all(
                                    AppBorderStyles.tableHeaderBackground,
                                  ),
                                  border: TableBorder.all(
                                    color: AppBorderStyles.borderColor,
                                    width: 0.5,
                                  ), // 使用统一边框样式
                                  columns: previewData.headers
                                      .map(
                                        (header) => DataColumn(
                                          label: Text(
                                            header,
                                            style: const TextStyle(
                                              fontSize: 10, // 更小字体
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                        ),
                                      )
                                      .toList(),
                                  rows: previewData.rows
                                      .take(20) // 增加显示行数到20行
                                      .toList()
                                      .asMap()
                                      .entries
                                      .map((entry) {
                                        final index = entry.key;
                                        final row = entry.value;
                                        final validation = previewData
                                            .validationResults[index];

                                        return DataRow(
                                          color: WidgetStateProperty.all(
                                            validation.isValid
                                                ? (index.isEven
                                                      ? AppBorderStyles
                                                            .tableEvenRowBackground
                                                      : AppBorderStyles
                                                            .tableOddRowBackground) // 使用统一表格行背景色
                                                : Colors.red[50],
                                          ),
                                          cells: row
                                              .map(
                                                (cell) => DataCell(
                                                  Text(
                                                    cell,
                                                    style: TextStyle(
                                                      fontSize: 9, // 更小字体
                                                      color: validation.isValid
                                                          ? Colors.black87
                                                          : Colors.red[700],
                                                    ),
                                                  ),
                                                ),
                                              )
                                              .toList(),
                                        );
                                      })
                                      .toList(),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    if (previewData.rows.length > 20) ...[
                      Padding(
                        padding: const EdgeInsets.only(top: 2),
                        child: Text(
                          '注：仅显示前20行数据，共${previewData.rows.length}行',
                          style: const TextStyle(
                            fontSize: 9,
                            color: Colors.grey,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: [
          // 紧凑的按钮布局
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextButton(
                onPressed: () => Get.back(),
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ), // 减少按钮内边距
                ),
                child: const Text('取消', style: TextStyle(fontSize: 13)),
              ),
              const SizedBox(width: 8),
              ElevatedButton(
                onPressed: previewData.validRowCount > 0
                    ? () => _executeImport(previewData)
                    : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green[600],
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ), // 减少按钮内边距
                ),
                child: const Text('确认导入', style: TextStyle(fontSize: 13)),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 执行导入
  Future<void> _executeImport(BatchImportPreviewData previewData) async {
    Get.back(); // 关闭预览对话框

    // 显示进度对话框
    Get.dialog(
      AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            const Text('正在导入数据...'),
            const SizedBox(height: 8),
            Text('共 ${previewData.validRowCount} 条有效数据'),
          ],
        ),
      ),
      barrierDismissible: false,
    );

    try {
      // 执行导入
      final result = await _batchImportService.executeImport(previewData, (
        importData,
      ) async {
        // 将导入的数据转换为StockInJewelryItem并添加到当前表单
        for (final data in importData) {
          // 调试日志：打印导入的原始数据
          LoggerService.d('导入数据转换', '原始数据: $data');

          // 根据分类名称查找分类ID
          final categoryName = data['category']?.toString() ?? '默认分类';
          final category = categoryList.firstWhere(
            (cat) => cat.name == categoryName,
            orElse: () => categoryList.isNotEmpty
                ? categoryList.first
                : const JewelryCategory(id: 1, name: '默认分类', parentId: 0),
          );

          final item = StockInJewelryItem(
            id: 0,
            stockInId: 0,
            barcode:
                data['barcode']?.toString() ??
                'IMPORT_${DateTime.now().millisecondsSinceEpoch}',
            name: data['name']?.toString() ?? '导入商品',
            categoryId: category.id,
            categoryName: category.name,
            ringSize: data['ringSize']?.toString() ?? '',
            goldWeight: (data['goldWeight'] as double?) ?? 0.0,
            goldPrice: (data['goldPrice'] as double?) ?? 0.0,
            silverWeight: (data['silverWeight'] as double?) ?? 0.0,
            silverPrice: (data['silverPrice'] as double?) ?? 0.0,
            workType: (data['workType'] as int?) ?? 0,
            workPrice: (data['laborCost'] as double?) ?? 0.0,
            platingCost: (data['platingCost'] as double?) ?? 0.0,
            wholesaleWorkPrice: (data['wholesaleWorkPrice'] as double?) ?? 0.0,
            retailWorkPrice: (data['retailWorkPrice'] as double?) ?? 0.0,
            pieceWorkPrice: (data['pieceWorkPrice'] as double?) ?? 0.0,
          );

          // 调试日志：打印转换后的StockInJewelryItem
          LoggerService.d(
            '导入数据转换',
            '转换后商品: ${item.name}, 金重: ${item.goldWeight}, 银重: ${item.silverWeight}, 工费: ${item.workPrice}, 工费方式: ${item.workType}',
          );

          stockInItems.add(item);
        }
        _calculateTotals();
      });

      // 关闭进度对话框
      Get.back();

      // 显示结果
      Get.snackbar(
        '导入完成',
        '成功导入 ${result.successCount} 条数据，失败 ${result.failureCount} 条',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: result.isAllSuccess ? Colors.green : Colors.orange,
        colorText: Colors.white,
      );
    } catch (e) {
      // 关闭进度对话框
      Get.back();

      LoggerService.e('执行导入失败', e);
      Get.snackbar(
        '错误',
        '导入失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  @override
  void onClose() {
    // 清理缓存和控制器
    _cachedTabContents.clear();
    _textControllers.forEach((key, controller) {
      controller.dispose();
    });
    _textControllers.clear();

    // 清理滚动控制器
    _tableScrollController.dispose();
    _headerScrollController.dispose();

    tabManager.dispose();
    super.onClose();
  }
}
