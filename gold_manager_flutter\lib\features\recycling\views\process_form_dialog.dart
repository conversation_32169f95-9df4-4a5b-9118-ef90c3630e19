/// 创建处理工单对话框
/// 从回收单创建处理工单

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../../core/constants/border_styles.dart';
import '../../../core/widgets/custom_card.dart';
import '../../../core/widgets/custom_text_field.dart';
import '../../../core/widgets/custom_dropdown.dart';
import '../../../models/recycling/recycling_model.dart';
import '../../../models/recycling/recycling_process_new.dart';
import '../../../models/store/store.dart';
import '../../../services/recycling_process_service.dart';
import '../../../services/store_service.dart';
import '../../../services/auth_service.dart';
import '../controllers/recycling_process_new_controller.dart';

class ProcessFormDialog extends StatefulWidget {
  final RecyclingOrder recycling;

  const ProcessFormDialog({
    Key? key,
    required this.recycling,
  }) : super(key: key);

  @override
  State<ProcessFormDialog> createState() => _ProcessFormDialogState();
}

class _ProcessFormDialogState extends State<ProcessFormDialog> {
  final RecyclingProcessNewController controller = Get.find<RecyclingProcessNewController>();
  final StoreService _storeService = Get.find<StoreService>();
  final AuthService _authService = Get.find<AuthService>();
  final RecyclingProcessService _processService = Get.find<RecyclingProcessService>();

  final _formKey = GlobalKey<FormState>();
  final remarkController = TextEditingController();
  
  int selectedStoreId = 0;
  ProcessType selectedProcessType = ProcessType.separation;
  List<int> selectedItemIds = [];
  List<Map<String, dynamic>> availableItems = [];
  List<Store> stores = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _initData();
  }

  @override
  void dispose() {
    remarkController.dispose();
    super.dispose();
  }

  Future<void> _initData() async {
    try {
      // 获取门店列表
      stores = await _storeService.getAllStores();
      if (!_authService.hasPermission('super.admin') && _authService.storeId.value > 0) {
        selectedStoreId = _authService.storeId.value;
      }

      // 获取可处理的回收明细
      availableItems = await _processService.getAvailableRecyclingItems(widget.recycling.id);
      
      setState(() {
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      Get.snackbar('错误', '加载数据失败');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
      ),
      child: Container(
        width: 800,
        constraints: const BoxConstraints(maxHeight: 700),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildHeader(),
              if (isLoading)
                const Expanded(
                  child: Center(child: CircularProgressIndicator()),
                )
              else
                Flexible(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildRecyclingInfo(),
                        const SizedBox(height: 24),
                        _buildProcessInfo(),
                        const SizedBox(height: 24),
                        _buildItemSelector(),
                        const SizedBox(height: 24),
                        _buildRemark(),
                      ],
                    ),
                  ),
                ),
              if (!isLoading) _buildFooter(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(AppBorderStyles.borderRadius),
          topRight: Radius.circular(AppBorderStyles.borderRadius),
        ),
      ),
      child: Row(
        children: [
          Icon(Icons.add_task, color: Colors.blue.shade700),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              '创建处理工单',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.blue.shade700,
              ),
            ),
          ),
          IconButton(
            onPressed: () => Get.back(),
            icon: const Icon(Icons.close),
            tooltip: '关闭',
          ),
        ],
      ),
    );
  }

  Widget _buildRecyclingInfo() {
    return CustomCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '回收单信息',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 24,
              runSpacing: 8,
              children: [
                _buildInfo('回收单号', widget.recycling.orderNo),
                _buildInfo('客户', widget.recycling.customerName),
                _buildInfo('门店', widget.recycling.storeName),
                _buildInfo('总金额', '¥${widget.recycling.totalAmount.toStringAsFixed(2)}'),
                _buildInfo('创建时间', DateFormat('yyyy-MM-dd HH:mm').format(widget.recycling.createdAt)),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfo(String label, String value) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          '$label：',
          style: const TextStyle(color: Colors.grey),
        ),
        Text(
          value,
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
      ],
    );
  }

  Widget _buildProcessInfo() {
    return CustomCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '处理信息',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<int>(
                    value: selectedStoreId,
                    items: [
                      const DropdownMenuItem(value: 0, child: Text('请选择处理门店')),
                      ...stores.map((store) => DropdownMenuItem(
                        value: store.id,
                        child: Text(store.name),
                      )),
                    ],
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          selectedStoreId = value;
                        });
                      }
                    },
                    decoration: const InputDecoration(
                      hintText: '处理门店',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) => value == null || value == 0 ? '请选择处理门店' : null,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: CustomDropdown<ProcessType>(
                    value: selectedProcessType,
                    items: ProcessType.values.map((type) => DropdownMenuItem(
                      value: type,
                      child: Text(type.label),
                    )).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          selectedProcessType = value;
                        });
                      }
                    },
                    hint: '处理类型',
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildItemSelector() {
    if (availableItems.isEmpty) {
      return CustomCard(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Icon(Icons.info_outline, size: 48, color: Colors.grey.shade400),
              const SizedBox(height: 8),
              Text(
                '没有可处理的回收明细',
                style: TextStyle(color: Colors.grey.shade600),
              ),
              const SizedBox(height: 4),
              Text(
                '所有明细可能已经在处理中或已处理',
                style: TextStyle(fontSize: 12, color: Colors.grey.shade500),
              ),
            ],
          ),
        ),
      );
    }

    return CustomCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  '选择回收明细',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    setState(() {
                      if (selectedItemIds.length == availableItems.length) {
                        selectedItemIds.clear();
                      } else {
                        selectedItemIds = availableItems.map((item) => item['id'] as int).toList();
                      }
                    });
                  },
                  child: Text(
                    selectedItemIds.length == availableItems.length ? '取消全选' : '全选',
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            ...availableItems.map((item) => _buildItemCheckbox(item)),
            if (selectedItemIds.isNotEmpty) ...[
              const SizedBox(height: 12),
              _buildSelectedSummary(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildItemCheckbox(Map<String, dynamic> item) {
    final isSelected = selectedItemIds.contains(item['id']);
    
    return InkWell(
      onTap: () {
        setState(() {
          if (isSelected) {
            selectedItemIds.remove(item['id']);
          } else {
            selectedItemIds.add(item['id']);
          }
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        margin: const EdgeInsets.only(bottom: 8),
        decoration: BoxDecoration(
          color: isSelected ? Colors.blue.shade50 : Colors.grey.shade50,
          borderRadius: BorderRadius.circular(AppBorderStyles.smallBorderRadius),
          border: Border.all(
            color: isSelected ? Colors.blue : Colors.grey.shade300,
          ),
        ),
        child: Row(
          children: [
            Checkbox(
              value: isSelected,
              onChanged: (value) {
                setState(() {
                  if (value == true) {
                    selectedItemIds.add(item['id']);
                  } else {
                    selectedItemIds.remove(item['id']);
                  }
                });
              },
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item['name'] ?? '未命名物品',
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(height: 4),
                  Wrap(
                    spacing: 16,
                    children: [
                      if ((item['gold_weight'] ?? 0) > 0)
                        Text(
                          '金重: ${item['gold_weight']}g',
                          style: const TextStyle(fontSize: 12, color: Colors.grey),
                        ),
                      if ((item['silver_weight'] ?? 0) > 0)
                        Text(
                          '银重: ${item['silver_weight']}g',
                          style: const TextStyle(fontSize: 12, color: Colors.grey),
                        ),
                      Text(
                        '金额: ¥${item['amount'] ?? 0}',
                        style: const TextStyle(fontSize: 12, color: Colors.grey),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSelectedSummary() {
    final selectedItems = availableItems.where((item) => selectedItemIds.contains(item['id'])).toList();
    final totalGoldWeight = selectedItems.fold<double>(
      0,
      (sum, item) => sum + (item['gold_weight'] ?? 0),
    );
    final totalSilverWeight = selectedItems.fold<double>(
      0,
      (sum, item) => sum + (item['silver_weight'] ?? 0),
    );
    final totalAmount = selectedItems.fold<double>(
      0,
      (sum, item) => sum + (item['amount'] ?? 0),
    );

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(AppBorderStyles.smallBorderRadius),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '已选择 ${selectedItemIds.length} 项',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          Wrap(
            spacing: 16,
            children: [
              if (totalGoldWeight > 0)
                Text('金重: ${totalGoldWeight.toStringAsFixed(2)}g'),
              if (totalSilverWeight > 0)
                Text('银重: ${totalSilverWeight.toStringAsFixed(2)}g'),
              Text('金额: ¥${totalAmount.toStringAsFixed(2)}'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRemark() {
    return CustomCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: CustomTextField(
          controller: remarkController,
          label: '备注',
          maxLines: 3,
        ),
      ),
    );
  }

  Widget _buildFooter() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(AppBorderStyles.borderRadius),
          bottomRight: Radius.circular(AppBorderStyles.borderRadius),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          ElevatedButton(
            onPressed: availableItems.isEmpty ? null : () => Get.back(),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.grey,
            ),
            child: const Text('取消'),
          ),
          const SizedBox(width: 16),
          ElevatedButton(
            onPressed: availableItems.isEmpty ? null : _onConfirm,
            child: const Text('创建工单'),
          ),
        ],
      ),
    );
  }

  void _onConfirm() async {
    if (!_formKey.currentState!.validate()) return;

    if (selectedItemIds.isEmpty) {
      Get.snackbar('提示', '请选择要处理的回收明细');
      return;
    }

    // 更新控制器状态
    controller.createStoreId.value = selectedStoreId;
    controller.selectedItemIds.value = selectedItemIds;
    controller.createProcessType.value = selectedProcessType;
    controller.remarkController.text = remarkController.text;

    // 创建工单
    await controller.createProcess(widget.recycling.id);
  }
}