/// 分页响应数据模型
class PaginatedResponse<T> {
  final List<T> items;
  final int total;
  final int page;
  final int pageSize;
  final int totalPages;

  PaginatedResponse({
    required this.items,
    required this.total,
    required this.page,
    required this.pageSize,
    required this.totalPages,
  });

  /// 从JSON创建实例
  factory PaginatedResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Map<String, dynamic>) fromJsonT,
  ) {
    return PaginatedResponse<T>(
      items: (json['items'] as List<dynamic>?)
          ?.map((item) => fromJsonT(item as Map<String, dynamic>))
          .toList() ?? [],
      total: json['total'] ?? 0,
      page: json['page'] ?? 1,
      pageSize: json['page_size'] ?? 20,
      totalPages: json['total_pages'] ?? 1,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson(Map<String, dynamic> Function(T) toJsonT) {
    return {
      'items': items.map((item) => toJsonT(item)).toList(),
      'total': total,
      'page': page,
      'page_size': pageSize,
      'total_pages': totalPages,
    };
  }

  /// 是否为空
  bool get isEmpty => items.isEmpty;

  /// 是否有数据
  bool get isNotEmpty => items.isNotEmpty;

  /// 是否有下一页
  bool get hasNextPage => page < totalPages;

  /// 是否有上一页
  bool get hasPreviousPage => page > 1;

  /// 当前页第一条记录的索引（从0开始）
  int get startIndex => (page - 1) * pageSize;

  /// 当前页最后一条记录的索引（从0开始）
  int get endIndex => startIndex + items.length - 1;

  /// 创建空的分页响应
  factory PaginatedResponse.empty() {
    return PaginatedResponse<T>(
      items: [],
      total: 0,
      page: 1,
      pageSize: 20,
      totalPages: 0,
    );
  }

  /// 复制并修改部分字段
  PaginatedResponse<T> copyWith({
    List<T>? items,
    int? total,
    int? page,
    int? pageSize,
    int? totalPages,
  }) {
    return PaginatedResponse<T>(
      items: items ?? this.items,
      total: total ?? this.total,
      page: page ?? this.page,
      pageSize: pageSize ?? this.pageSize,
      totalPages: totalPages ?? this.totalPages,
    );
  }

  @override
  String toString() {
    return 'PaginatedResponse(items: ${items.length}, total: $total, page: $page, pageSize: $pageSize, totalPages: $totalPages)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
  
    return other is PaginatedResponse<T> &&
      other.items == items &&
      other.total == total &&
      other.page == page &&
      other.pageSize == pageSize &&
      other.totalPages == totalPages;
  }

  @override
  int get hashCode {
    return items.hashCode ^
      total.hashCode ^
      page.hashCode ^
      pageSize.hashCode ^
      totalPages.hashCode;
  }
}