import 'package:flutter/material.dart';
import '../../../core/constants/border_styles.dart';
import '../../../core/theme/app_theme.dart';
import '../../../widgets/responsive_builder.dart';

/// 仪表盘核心指标卡片
/// 用于展示关键业务指标，支持趋势指示和响应式设计
class DashboardMetricCard extends StatelessWidget {
  /// 指标标题
  final String title;
  
  /// 主要数值
  final String value;
  
  /// 数值单位(可选)
  final String? unit;
  
  /// 副标题或描述(可选)
  final String? subtitle;
  
  /// 图标
  final IconData icon;
  
  /// 图标颜色
  final Color? iconColor;
  
  /// 数值颜色
  final Color? valueColor;
  
  /// 趋势值(可选，如 +12.5)
  final double? trendValue;
  
  /// 趋势描述(可选，如 "较昨日")
  final String? trendDescription;
  
  /// 是否正在加载
  final bool isLoading;
  
  /// 点击回调
  final VoidCallback? onTap;
  
  /// 卡片背景色
  final Color? backgroundColor;
  
  /// 卡片高度
  final double? height;

  const DashboardMetricCard({
    super.key,
    required this.title,
    required this.value,
    required this.icon,
    this.unit,
    this.subtitle,
    this.iconColor,
    this.valueColor,
    this.trendValue,
    this.trendDescription,
    this.isLoading = false,
    this.onTap,
    this.backgroundColor,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, sizingInformation) {
        // 根据屏幕尺寸调整卡片高度和字体大小
        final cardHeight = height ?? _getCardHeight(sizingInformation);
        final titleFontSize = _getTitleFontSize(sizingInformation);
        final valueFontSize = _getValueFontSize(sizingInformation);
        final iconSize = _getIconSize(sizingInformation);
        
        return Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: onTap,
            borderRadius: BorderRadius.circular(AppBorderStyles.mediumBorderRadius),
            child: Container(
              height: cardHeight,
              decoration: AppBorderStyles.elevatedBoxDecoration.copyWith(
                color: backgroundColor ?? Colors.white,
                borderRadius: BorderRadius.circular(AppBorderStyles.mediumBorderRadius),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: isLoading ? _buildLoadingContent() : _buildContent(
                  titleFontSize: titleFontSize,
                  valueFontSize: valueFontSize,
                  iconSize: iconSize,
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// 构建卡片内容
  Widget _buildContent({
    required double titleFontSize,
    required double valueFontSize,
    required double iconSize,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 顶部：图标和标题
        Row(
          children: [
            Icon(
              icon,
              size: iconSize,
              color: iconColor ?? AppTheme.primaryColor,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                title,
                style: TextStyle(
                  fontSize: titleFontSize,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        
        const Spacer(),
        
        // 中部：主要数值
        Row(
          crossAxisAlignment: CrossAxisAlignment.baseline,
          textBaseline: TextBaseline.alphabetic,
          children: [
            Flexible(
              child: Text(
                value,
                style: TextStyle(
                  fontSize: valueFontSize,
                  fontWeight: FontWeight.bold,
                  color: valueColor ?? AppTheme.primaryColor,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            if (unit != null) ...[
              const SizedBox(width: 4),
              Text(
                unit!,
                style: TextStyle(
                  fontSize: titleFontSize - 2,
                  fontWeight: FontWeight.w400,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ],
        ),
        
        // 副标题
        if (subtitle != null) ...[
          const SizedBox(height: 4),
          Text(
            subtitle!,
            style: TextStyle(
              fontSize: titleFontSize - 2,
              fontWeight: FontWeight.w400,
              color: Colors.grey[600],
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ],
        
        const Spacer(),
        
        // 底部：趋势指示器
        if (trendValue != null) _buildTrendIndicator(),
      ],
    );
  }

  /// 构建趋势指示器
  Widget _buildTrendIndicator() {
    final isPositive = trendValue! >= 0;
    final trendColor = isPositive ? AppTheme.successColor : AppTheme.errorColor;
    final trendIcon = isPositive ? Icons.trending_up : Icons.trending_down;
    
    return Row(
      children: [
        Icon(
          trendIcon,
          size: 16,
          color: trendColor,
        ),
        const SizedBox(width: 4),
        Text(
          '${isPositive ? '+' : ''}${trendValue!.toStringAsFixed(1)}%',
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: trendColor,
          ),
        ),
        if (trendDescription != null) ...[
          const SizedBox(width: 4),
          Expanded(
            child: Text(
              trendDescription!,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w400,
                color: Colors.grey[600],
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ],
    );
  }

  /// 构建加载状态内容
  Widget _buildLoadingContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 顶部占位
        Row(
          children: [
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(4),
              ),
            ),
            const SizedBox(width: 8),
            Container(
              width: 80,
              height: 14,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ],
        ),
        
        const Spacer(),
        
        // 中部数值占位
        Container(
          width: 100,
          height: 24,
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        
        const Spacer(),
        
        // 底部趋势占位
        Container(
          width: 60,
          height: 12,
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: BorderRadius.circular(4),
          ),
        ),
      ],
    );
  }

  /// 获取卡片高度
  double _getCardHeight(SizingInformation sizingInformation) {
    if (sizingInformation.isDesktop) return 120;
    if (sizingInformation.isTablet) return 110;
    return 100;
  }

  /// 获取标题字体大小
  double _getTitleFontSize(SizingInformation sizingInformation) {
    if (sizingInformation.isDesktop) return 14;
    if (sizingInformation.isTablet) return 13;
    return 12;
  }

  /// 获取数值字体大小
  double _getValueFontSize(SizingInformation sizingInformation) {
    if (sizingInformation.isDesktop) return 24;
    if (sizingInformation.isTablet) return 22;
    return 20;
  }

  /// 获取图标大小
  double _getIconSize(SizingInformation sizingInformation) {
    if (sizingInformation.isDesktop) return 20;
    if (sizingInformation.isTablet) return 18;
    return 16;
  }
}

/// 简化版统计卡片
/// 用于较小的统计项展示
class SimpleMetricCard extends StatelessWidget {
  final String label;
  final String value;
  final IconData icon;
  final Color? color;
  final VoidCallback? onTap;

  const SimpleMetricCard({
    super.key,
    required this.label,
    required this.value,
    required this.icon,
    this.color,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final cardColor = color ?? AppTheme.primaryColor;
    
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
            border: Border.all(color: AppBorderStyles.borderColor),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(icon, size: 16, color: cardColor),
              const SizedBox(width: 8),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    label,
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                      color: Colors.black87,
                    ),
                  ),
                  Text(
                    value,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: cardColor,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
