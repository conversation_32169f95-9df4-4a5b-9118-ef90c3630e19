"""
回收管理数据模型
对应FastAdmin数据库中的回收管理相关表结构

老板，这个模块定义了回收管理的数据模型：
1. Recycling - 回收单主表
2. RecyclingItem - 回收明细表

完全匹配FastAdmin数据库结构。
"""

from sqlalchemy import Column, Integer, String, Text, ForeignKey, DateTime, Numeric
from sqlalchemy.orm import relationship
from ..core.database import Base


class Recycling(Base):
    """回收单主表 - 对应 fa_recycling"""
    __tablename__ = "fa_recycling"

    id = Column(Integer, primary_key=True, index=True, comment="回收单ID")
    recycle_no = Column(String(50), unique=True, nullable=False, comment="回收单号")
    store_id = Column(Integer, ForeignKey("fa_store.id"), nullable=False, comment="门店ID")
    member_id = Column(Integer, ForeignKey("fa_member.id"), comment="会员ID")
    customer_name = Column(String(50), comment="客户姓名")
    phone = Column(String(20), comment="联系电话")
    gold_weight = Column(Numeric(10, 3), default=0.000, comment="金重(克)")
    gold_price = Column(Numeric(10, 2), default=0.00, comment="金价(克价)")
    gold_amount = Column(Numeric(10, 2), default=0.00, comment="金价金额")
    silver_weight = Column(Numeric(10, 3), default=0.000, comment="银重(克)")
    silver_price = Column(Numeric(10, 2), default=0.00, comment="银价(克价)")
    silver_amount = Column(Numeric(10, 2), default=0.00, comment="银价金额")
    price = Column(Numeric(10, 2), default=0.00, comment="回收价格")
    total_amount = Column(Numeric(10, 2), default=0.00, comment="总金额")
    operator_id = Column(Integer, ForeignKey("fa_admin.id"), nullable=False, comment="操作员ID")
    status = Column(Integer, default=1, comment="状态:0=作废,1=正常")
    remark = Column(Text, comment="备注")
    createtime = Column(Integer, comment="创建时间")
    updatetime = Column(Integer, comment="更新时间")
    discount_amount = Column(Numeric(10, 2), default=0.00, comment="折后总金额")

    # 关联关系
    store = relationship("Store", back_populates="recyclings")
    member = relationship("Member", back_populates="recyclings")
    operator = relationship("Admin", foreign_keys=[operator_id], back_populates="recyclings")
    items = relationship("RecyclingItem", back_populates="recycling", cascade="all, delete-orphan")


class RecyclingItem(Base):
    """回收明细表 - 对应 fa_recycling_item"""
    __tablename__ = "fa_recycling_item"

    id = Column(Integer, primary_key=True, index=True, comment="明细ID")
    recycling_id = Column(Integer, ForeignKey("fa_recycling.id"), nullable=False, comment="回收单ID")
    name = Column(String(100), nullable=False, comment="物品名称")
    category_id = Column(Integer, ForeignKey("fa_jewelry_category.id"), nullable=False, comment="分类ID")
    gold_weight = Column(Numeric(10, 2), default=0.00, comment="金重(克)")
    gold_price = Column(Numeric(10, 2), default=0.00, comment="金价(克价)")
    gold_amount = Column(Numeric(10, 2), default=0.00, comment="金价金额")
    silver_weight = Column(Numeric(10, 2), default=0.00, comment="银重(克)")
    silver_price = Column(Numeric(10, 2), default=0.00, comment="银价(克价)")
    silver_amount = Column(Numeric(10, 2), default=0.00, comment="银价金额")
    total_amount = Column(Numeric(10, 2), default=0.00, comment="总金额")
    discount_rate = Column(Numeric(10, 2), default=100.00, comment="折扣率(%)")
    discount_amount = Column(Numeric(10, 2), default=0.00, comment="折后金额")
    remark = Column(String(255), comment="备注")
    createtime = Column(Integer, comment="创建时间")
    process_status = Column(Integer, default=0, comment="处理状态：0=未处理，1=处理中，2=已处理，3=已入库")
    process_id = Column(Integer, ForeignKey("fa_recycling_process.id"), comment="关联的处理工单ID")

    # 关联关系
    recycling = relationship("Recycling", back_populates="items")
    category = relationship("JewelryCategory", back_populates="recycling_items")
    process = relationship("RecyclingProcess", backref="recycling_items")
