import '../../core/utils/number_utils.dart';

/// 入库单商品明细模型 - 专门用于金包银首饰业务
class StockInJewelryItem {
  /// 明细ID
  final int id;

  /// 入库单ID
  final int stockInId;

  /// 条码号
  String barcode;

  /// 商品名称
  String name;

  /// 分类ID
  int categoryId;

  /// 分类名称
  String categoryName;

  /// 圈口号
  String ringSize;

  /// 金重(g)
  double goldWeight;

  /// 金价(元/g)
  double goldPrice;

  /// 银重(g)
  double silverWeight;

  /// 银价(元/g)
  double silverPrice;

  /// 总重(g) - 自动计算
  double get totalWeight => goldWeight + silverWeight;

  /// 工费方式 (0=按克, 1=按件)
  int workType;

  /// 工费(元)
  double workPrice;

  /// 电铸费(元)
  double platingCost;

  /// 批发工费(元)
  double wholesaleWorkPrice;

  /// 零售工费(元)
  double retailWorkPrice;

  /// 件工费(元)
  double pieceWorkPrice;

  /// 金成本
  double get goldCost => goldWeight * goldPrice;

  /// 银成本
  double get silverCost => silverWeight * silverPrice;

  /// 工费成本
  double get workCost {
    if (workType == 0) {
      // 按克计算
      return totalWeight * workPrice;
    } else {
      // 按件计算
      return workPrice;
    }
  }

  /// 总成本
  double get totalCost => goldCost + silverCost + workCost + platingCost;

  /// 是否有验证错误
  bool hasValidationErrors = false;

  /// 验证错误信息
  Map<String, String> validationErrors = {};

  StockInJewelryItem({
    this.id = 0,
    this.stockInId = 0,
    this.barcode = '',
    this.name = '',
    this.categoryId = 0,
    this.categoryName = '',
    this.ringSize = '',
    this.goldWeight = 0.0,
    this.goldPrice = 0.0,
    this.silverWeight = 0.0,
    this.silverPrice = 0.0,
    this.workType = 0,
    this.workPrice = 0.0,
    this.platingCost = 0.0,
    this.wholesaleWorkPrice = 0.0,
    this.retailWorkPrice = 0.0,
    this.pieceWorkPrice = 0.0,
  });

  /// 从JSON构造
  factory StockInJewelryItem.fromJson(Map<String, dynamic> json) {
    return StockInJewelryItem(
      id: json['id'] ?? 0,
      stockInId: json['stock_in_id'] ?? 0,
      barcode: json['barcode'] ?? '',
      name: json['name'] ?? '',
      categoryId: json['category_id'] ?? 0,
      categoryName: json['category_name'] ?? '',
      ringSize: json['ring_size'] ?? '',
      goldWeight: NumberUtils.toDouble(json['gold_weight']),
      goldPrice: NumberUtils.toDouble(json['gold_price']),
      silverWeight: NumberUtils.toDouble(json['silver_weight']),
      silverPrice: NumberUtils.toDouble(json['silver_price']),
      workType: json['work_type'] ?? 0,
      workPrice: NumberUtils.toDouble(json['work_price']),
      platingCost: NumberUtils.toDouble(json['plating_cost']),
      wholesaleWorkPrice: NumberUtils.toDouble(json['wholesale_work_price']),
      retailWorkPrice: NumberUtils.toDouble(json['retail_work_price']),
      pieceWorkPrice: NumberUtils.toDouble(json['piece_work_price']),
    );
  }

  /// 转为JSON - 修复字段映射
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'stock_in_id': stockInId,
      'barcode': barcode,
      'name': name,
      'category_id': categoryId,
      'ring_size': ringSize,
      'gold_weight': goldWeight,
      'gold_price': goldPrice,
      'gold_cost': goldCost, // 自动计算的金成本
      'silver_weight': silverWeight,
      'total_weight': totalWeight, // 自动计算的总重
      'silver_price': silverPrice,
      'silver_cost': silverCost, // 自动计算的银成本
      'silver_work_type': workType, // 修复字段名
      'silver_work_price': workPrice,
      'plating_cost': platingCost,
      'total_cost': totalCost, // 自动计算的总成本
      'wholesale_work_price': wholesaleWorkPrice,
      'retail_work_price': retailWorkPrice,
      'piece_work_price': pieceWorkPrice,
    };
  }

  /// 验证数据
  bool validate() {
    validationErrors.clear();
    hasValidationErrors = false;

    // 条码验证
    if (barcode.trim().isEmpty) {
      validationErrors['barcode'] = '条码不能为空';
    }

    // 商品名称验证
    if (name.trim().isEmpty) {
      validationErrors['name'] = '商品名称不能为空';
    }

    // 分类验证
    if (categoryId <= 0) {
      validationErrors['categoryId'] = '请选择商品分类';
    }

    // 金重验证
    if (goldWeight < 0) {
      validationErrors['goldWeight'] = '金重不能为负数';
    }

    // 金价验证
    if (goldWeight > 0 && goldPrice <= 0) {
      validationErrors['goldPrice'] = '金重大于0时，金价必须大于0';
    }

    // 银重验证
    if (silverWeight < 0) {
      validationErrors['silverWeight'] = '银重不能为负数';
    }

    // 银价验证
    if (silverWeight > 0 && silverPrice <= 0) {
      validationErrors['silverPrice'] = '银重大于0时，银价必须大于0';
    }

    // 总重验证
    if (totalWeight <= 0) {
      validationErrors['totalWeight'] = '金重和银重不能都为0';
    }

    // 工费验证
    if (workPrice < 0) {
      validationErrors['workPrice'] = '工费不能为负数';
    }

    // 电铸费验证
    if (platingCost < 0) {
      validationErrors['platingCost'] = '电铸费不能为负数';
    }

    hasValidationErrors = validationErrors.isNotEmpty;
    return !hasValidationErrors;
  }

  /// 复制对象
  StockInJewelryItem copyWith({
    int? id,
    int? stockInId,
    String? barcode,
    String? name,
    int? categoryId,
    String? categoryName,
    String? ringSize,
    double? goldWeight,
    double? goldPrice,
    double? silverWeight,
    double? silverPrice,
    int? workType,
    double? workPrice,
    double? platingCost,
    double? wholesaleWorkPrice,
    double? retailWorkPrice,
    double? pieceWorkPrice,
  }) {
    return StockInJewelryItem(
      id: id ?? this.id,
      stockInId: stockInId ?? this.stockInId,
      barcode: barcode ?? this.barcode,
      name: name ?? this.name,
      categoryId: categoryId ?? this.categoryId,
      categoryName: categoryName ?? this.categoryName,
      ringSize: ringSize ?? this.ringSize,
      goldWeight: goldWeight ?? this.goldWeight,
      goldPrice: goldPrice ?? this.goldPrice,
      silverWeight: silverWeight ?? this.silverWeight,
      silverPrice: silverPrice ?? this.silverPrice,
      workType: workType ?? this.workType,
      workPrice: workPrice ?? this.workPrice,
      platingCost: platingCost ?? this.platingCost,
      wholesaleWorkPrice: wholesaleWorkPrice ?? this.wholesaleWorkPrice,
      retailWorkPrice: retailWorkPrice ?? this.retailWorkPrice,
      pieceWorkPrice: pieceWorkPrice ?? this.pieceWorkPrice,
    );
  }
}
