# FastAPI核心框架
fastapi==0.104.1
uvicorn[standard]==0.24.0

# 数据库相关
sqlalchemy==2.0.23
pymysql==1.1.0
cryptography==41.0.7
alembic==1.12.1

# 数据验证和序列化
pydantic==2.5.0
pydantic-settings==2.1.0

# 认证和安全
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# 时间处理
python-dateutil==2.8.2

# 工具库
python-dotenv==1.0.0
typing-extensions==4.8.0

# 测试框架
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2

# 日志
loguru==0.7.2

# 跨域支持
fastapi-cors==0.0.6

# 分页支持
fastapi-pagination==0.12.13

# Excel处理
openpyxl==3.1.2
xlsxwriter==3.1.9

# 图片处理
Pillow==10.1.0

# Redis缓存
redis==5.0.1

# 数据备份
schedule==1.2.0

# 数据处理
pandas==2.1.4
aiofiles==23.2.1