"""
会员管理相关的数据验证模型
定义会员的创建、更新、响应等数据结构
"""

from typing import Optional, List
from pydantic import BaseModel, Field, validator
from datetime import date, datetime
import re


class MemberBase(BaseModel):
    """会员基础模型"""
    card_no: str = Field(..., min_length=1, max_length=20, description="会员卡号")
    name: str = Field(..., min_length=1, max_length=50, description="会员姓名")
    phone: Optional[str] = Field(None, max_length=20, description="联系电话")
    birthday: Optional[date] = Field(None, description="生日")
    points: int = Field(0, ge=0, description="积分")
    level: int = Field(1, ge=1, le=5, description="会员等级(1-5)")
    status: int = Field(1, ge=0, le=1, description="状态:0=禁用,1=正常")


class MemberCreate(MemberBase):
    """创建会员的请求模型"""
    
    @validator('card_no')
    def validate_card_no(cls, v):
        if not v or not v.strip():
            raise ValueError('会员卡号不能为空')
        card_no = v.strip()
        # 会员卡号格式验证：只允许字母数字和短横线
        if not re.match(r'^[A-Za-z0-9\-]+$', card_no):
            raise ValueError('会员卡号只能包含字母、数字和短横线')
        return card_no
    
    @validator('name')
    def validate_name(cls, v):
        if not v or not v.strip():
            raise ValueError('会员姓名不能为空')
        return v.strip()
    
    @validator('phone')
    def validate_phone(cls, v):
        if v and len(v.strip()) > 0:
            phone = v.strip()
            # 简单的手机号验证：11位数字或带区号的座机
            if not re.match(r'^1[3-9]\d{9}$|^\d{3,4}-?\d{7,8}$', phone):
                raise ValueError('电话号码格式不正确')
            return phone
        return v
    
    @validator('points')
    def validate_points(cls, v):
        if v < 0:
            raise ValueError('积分不能为负数')
        return v


class MemberUpdate(BaseModel):
    """更新会员的请求模型"""
    card_no: Optional[str] = Field(None, min_length=1, max_length=20, description="会员卡号")
    name: Optional[str] = Field(None, min_length=1, max_length=50, description="会员姓名")
    phone: Optional[str] = Field(None, max_length=20, description="联系电话")
    birthday: Optional[date] = Field(None, description="生日")
    points: Optional[int] = Field(None, ge=0, description="积分")
    level: Optional[int] = Field(None, ge=1, le=5, description="会员等级(1-5)")
    status: Optional[int] = Field(None, ge=0, le=1, description="状态:0=禁用,1=正常")
    
    @validator('card_no')
    def validate_card_no(cls, v):
        if v is not None and (not v or not v.strip()):
            raise ValueError('会员卡号不能为空')
        if v:
            card_no = v.strip()
            if not re.match(r'^[A-Za-z0-9\-]+$', card_no):
                raise ValueError('会员卡号只能包含字母、数字和短横线')
            return card_no
        return v
    
    @validator('name')
    def validate_name(cls, v):
        if v is not None and (not v or not v.strip()):
            raise ValueError('会员姓名不能为空')
        return v.strip() if v else v
    
    @validator('phone')
    def validate_phone(cls, v):
        if v and len(v.strip()) > 0:
            phone = v.strip()
            if not re.match(r'^1[3-9]\d{9}$|^\d{3,4}-?\d{7,8}$', phone):
                raise ValueError('电话号码格式不正确')
            return phone
        return v
    
    @validator('points')
    def validate_points(cls, v):
        if v is not None and v < 0:
            raise ValueError('积分不能为负数')
        return v


class MemberResponse(MemberBase):
    """会员响应模型"""
    id: int = Field(..., description="会员ID")
    createtime: Optional[int] = Field(None, description="创建时间戳")
    updatetime: Optional[int] = Field(None, description="更新时间戳")
    
    # 扩展信息
    level_name: Optional[str] = Field(None, description="会员等级名称")
    age: Optional[int] = Field(None, description="年龄")
    
    class Config:
        from_attributes = True


class MemberListResponse(BaseModel):
    """会员列表响应模型"""
    items: List[MemberResponse] = Field(..., description="会员列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页")
    page_size: int = Field(..., description="每页数量")
    total_pages: int = Field(..., description="总页数")


class MemberStatistics(BaseModel):
    """会员统计信息模型"""
    total_members: int = Field(0, description="会员总数")
    active_members: int = Field(0, description="活跃会员数")
    disabled_members: int = Field(0, description="禁用会员数")
    total_points: int = Field(0, description="积分总数")
    level_distribution: dict = Field({}, description="等级分布")
    avg_points: float = Field(0.0, description="平均积分")


class MemberPointsUpdate(BaseModel):
    """会员积分更新模型"""
    points: int = Field(..., description="积分变化量(正数增加，负数减少)")
    reason: Optional[str] = Field(None, max_length=200, description="变更原因")
    
    @validator('points')
    def validate_points(cls, v):
        if v == 0:
            raise ValueError('积分变化量不能为0')
        return v 