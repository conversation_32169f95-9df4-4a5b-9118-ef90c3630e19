import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../models/jewelry_category.dart';
import '../../../services/jewelry_category_service.dart';
import '../../../core/utils/logger_service.dart';

/// 首饰分类控制器
class JewelryCategoryController extends GetxController {
  final JewelryCategoryService _categoryService;
  
  // 反应式变量
  final RxList<JewelryCategory> categories = <JewelryCategory>[].obs;
  final RxBool isLoading = false.obs;
  final RxBool isProcessing = false.obs;
  final RxString errorMessage = ''.obs;
  final RxBool hasError = false.obs;
  
  // 选中分类
  final Rx<JewelryCategory?> selectedCategory = Rx<JewelryCategory?>(null);
  
  /// 表单控制器
  final nameController = TextEditingController();
  final codeController = TextEditingController();
  final descriptionController = TextEditingController();
  final Rx<int?> parentId = Rx<int?>(null);
  final Rx<int> sort = 0.obs;
  final Rx<bool> isActive = true.obs;
  final Rx<Color> selectedColor = const Color(0xFF1E88E5).obs;
  
  /// 构造函数
  JewelryCategoryController({required JewelryCategoryService categoryService})
      : _categoryService = categoryService;
  
  @override
  void onInit() {
    super.onInit();
    fetchCategories();
  }
  
  @override
  void onClose() {
    nameController.dispose();
    codeController.dispose();
    descriptionController.dispose();
    super.onClose();
  }
  
  /// 获取所有分类
  Future<void> fetchCategories() async {
    isLoading.value = true;
    hasError.value = false;
    errorMessage.value = '';
    
    try {
      final result = await _categoryService.getCategories();
      categories.assignAll(result);
    } catch (e) {
      hasError.value = true;
      errorMessage.value = e.toString();
      LoggerService.e('获取分类失败', e);
    } finally {
      isLoading.value = false;
    }
  }
  
  /// 获取分类详情
  Future<void> fetchCategoryDetail(int id) async {
    try {
      final category = await _categoryService.getCategoryDetail(id);
      selectedCategory.value = category;
      _fillFormWithCategory(category);
    } catch (e) {
      Get.snackbar('错误', '获取分类详情失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      LoggerService.e('获取分类详情失败', e);
    }
  }
  
  /// 添加分类
  Future<bool> addCategory() async {
    if (!_validateForm()) return false;
    
    isProcessing.value = true;
    try {
      final newCategory = _getFormCategory();
      final result = await _categoryService.addCategory(newCategory);
      if (result) {
        await fetchCategories();
        _resetForm();
        return true;
      }
      return false;
    } catch (e) {
      Get.snackbar('错误', '添加分类失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      LoggerService.e('添加分类失败', e);
      return false;
    } finally {
      isProcessing.value = false;
    }
  }
  
  /// 编辑分类
  Future<bool> editCategory() async {
    if (!_validateForm() || selectedCategory.value == null) return false;
    
    isProcessing.value = true;
    try {
      final updatedCategory = _getFormCategory(id: selectedCategory.value!.id);
      final result = await _categoryService.editCategory(updatedCategory);
      if (result) {
        await fetchCategories();
        return true;
      }
      return false;
    } catch (e) {
      Get.snackbar('错误', '编辑分类失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      LoggerService.e('编辑分类失败', e);
      return false;
    } finally {
      isProcessing.value = false;
    }
  }
  
  /// 删除分类
  Future<bool> deleteCategory(int id) async {
    isProcessing.value = true;
    try {
      final result = await _categoryService.deleteCategory(id);
      if (result) {
        await fetchCategories();
        if (selectedCategory.value?.id == id) {
          selectedCategory.value = null;
          _resetForm();
        }
        return true;
      }
      return false;
    } catch (e) {
      Get.snackbar('错误', '删除分类失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      LoggerService.e('删除分类失败', e);
      return false;
    } finally {
      isProcessing.value = false;
    }
  }
  
  /// 选择分类
  void selectCategory(JewelryCategory category) {
    selectedCategory.value = category;
    _fillFormWithCategory(category);
  }
  
  /// 清除选择
  void clearSelection() {
    selectedCategory.value = null;
    _resetForm();
  }
  
  /// 表单验证
  bool _validateForm() {
    if (nameController.text.isEmpty) {
      Get.snackbar('错误', '分类名称不能为空',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }
    
    if (codeController.text.isEmpty) {
      Get.snackbar('错误', '分类编码不能为空',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }
    
    return true;
  }
  
  /// 从表单获取分类对象
  JewelryCategory _getFormCategory({int id = 0}) {
    final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    return JewelryCategory(
      id: id,
      name: nameController.text,
      code: codeController.text,
      description: descriptionController.text.isEmpty ? null : descriptionController.text,
      parentId: parentId.value,
      sort: sort.value,
      isActive: isActive.value,
      createTime: id == 0 ? now : selectedCategory.value?.createTime ?? now,
      updateTime: now,
      color: selectedColor.value,
    );
  }
  
  /// 使用分类填充表单
  void _fillFormWithCategory(JewelryCategory category) {
    nameController.text = category.name;
    codeController.text = category.code;
    descriptionController.text = category.description ?? '';
    parentId.value = category.parentId;
    sort.value = category.sort;
    isActive.value = category.isActive;
    selectedColor.value = category.color ?? const Color(0xFF1E88E5);
  }
  
  /// 重置表单
  void _resetForm() {
    nameController.clear();
    codeController.clear();
    descriptionController.clear();
    parentId.value = null;
    sort.value = 0;
    isActive.value = true;
    selectedColor.value = const Color(0xFF1E88E5);
  }
  
  /// 获取父分类选项
  List<JewelryCategory> getParentCategoryOptions() {
    if (selectedCategory.value == null) {
      return categories;
    }
    
    // 过滤掉当前分类及其子分类
    return categories.where((c) => c.id != selectedCategory.value!.id).toList();
  }
} 