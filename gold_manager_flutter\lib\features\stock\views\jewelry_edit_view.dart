import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

import '../../../core/constants/border_styles.dart';
import '../../../core/theme/app_theme.dart';
import '../../../models/common/enums.dart';
import '../../../widgets/responsive_builder.dart';
import '../controllers/jewelry_edit_controller.dart';

/// 商品编辑页面
class JewelryEditView extends GetView<JewelryEditController> {
  const JewelryEditView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('编辑商品'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        elevation: 1,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
        actions: [
          Obx(() => TextButton.icon(
            icon: controller.isLoading.value 
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.save),
            label: Text(controller.isLoading.value ? '保存中...' : '保存'),
            onPressed: controller.isLoading.value ? null : controller.saveJewelry,
          )),
        ],
      ),
      body: ScreenTypeLayout(
        mobile: _buildMobileLayout(),
        tablet: _buildDesktopLayout(),
        desktop: _buildDesktopLayout(),
      ),
    );
  }

  /// 构建移动端布局
  Widget _buildMobileLayout() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: controller.formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildBasicInfoSection(),
            const SizedBox(height: 16),
            _buildWeightSection(),
            const SizedBox(height: 16),
            _buildPriceSection(),
            const SizedBox(height: 24),
            _buildSaveButton(),
          ],
        ),
      ),
    );
  }

  /// 构建桌面端布局
  Widget _buildDesktopLayout() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Center(
        child: Container(
          constraints: const BoxConstraints(maxWidth: 800),
          child: Form(
            key: controller.formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildBasicInfoSection(),
                const SizedBox(height: 24),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(child: _buildWeightSection()),
                    const SizedBox(width: 24),
                    Expanded(child: _buildPriceSection()),
                  ],
                ),
                const SizedBox(height: 32),
                _buildSaveButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建基本信息区域
  Widget _buildBasicInfoSection() {
    return Container(
      decoration: AppBorderStyles.elevatedBoxDecoration.copyWith(
        color: Colors.white,
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: Colors.blue[600], size: 20),
                const SizedBox(width: 8),
                const Text(
                  '基本信息',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildTextField(
              label: '商品名称',
              controller: controller.nameController,
              validator: (value) => value?.isEmpty == true ? '请输入商品名称' : null,
            ),
            const SizedBox(height: 16),
            _buildTextField(
              label: '商品条码',
              controller: controller.barcodeController,
              validator: (value) => value?.isEmpty == true ? '请输入商品条码' : null,
            ),
            const SizedBox(height: 16),
            _buildDropdownField(
              label: '商品分类',
              value: controller.selectedCategoryId.value,
              items: controller.categoryList.map((category) => DropdownMenuItem<int>(
                value: category.id,
                child: Text(category.name),
              )).toList(),
              onChanged: (value) => controller.selectedCategoryId.value = value ?? 0,
            ),
            const SizedBox(height: 16),
            _buildTextField(
              label: '圈口号',
              controller: controller.ringSizeController,
            ),
            const SizedBox(height: 16),
            _buildDropdownField(
              label: '商品状态',
              value: controller.selectedStatus.value,
              items: JewelryStatus.values.map((status) => DropdownMenuItem<JewelryStatus>(
                value: status,
                child: Text(status.label),
              )).toList(),
              onChanged: (value) => controller.selectedStatus.value = value ?? JewelryStatus.onShelf,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建重量信息区域
  Widget _buildWeightSection() {
    return Container(
      decoration: AppBorderStyles.elevatedBoxDecoration.copyWith(
        color: Colors.white,
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.scale, color: Colors.amber[600], size: 20),
                const SizedBox(width: 8),
                const Text(
                  '重量信息',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildNumberField(
              label: '金重(g)',
              controller: controller.goldWeightController,
              validator: (value) => value?.isEmpty == true ? '请输入金重' : null,
            ),
            const SizedBox(height: 16),
            _buildNumberField(
              label: '银重(g)',
              controller: controller.silverWeightController,
              validator: (value) => value?.isEmpty == true ? '请输入银重' : null,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建价格信息区域
  Widget _buildPriceSection() {
    return Container(
      decoration: AppBorderStyles.elevatedBoxDecoration.copyWith(
        color: Colors.white,
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.monetization_on, color: Colors.green[600], size: 20),
                const SizedBox(width: 8),
                const Text(
                  '价格信息',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildNumberField(
              label: '金价(元)',
              controller: controller.goldPriceController,
              validator: (value) => value?.isEmpty == true ? '请输入金价' : null,
            ),
            const SizedBox(height: 16),
            _buildNumberField(
              label: '银价(元)',
              controller: controller.silverPriceController,
              validator: (value) => value?.isEmpty == true ? '请输入银价' : null,
            ),
            const SizedBox(height: 16),
            _buildNumberField(
              label: '工费(元)',
              controller: controller.workPriceController,
              validator: (value) => value?.isEmpty == true ? '请输入工费' : null,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建文本输入框
  Widget _buildTextField({
    required String label,
    required TextEditingController controller,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          decoration: AppBorderStyles.standardInputDecoration.copyWith(
            hintText: '请输入$label',
          ),
          validator: validator,
        ),
      ],
    );
  }

  /// 构建数字输入框
  Widget _buildNumberField({
    required String label,
    required TextEditingController controller,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          decoration: AppBorderStyles.standardInputDecoration.copyWith(
            hintText: '请输入$label',
          ),
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
          ],
          validator: validator,
        ),
      ],
    );
  }

  /// 构建下拉选择框
  Widget _buildDropdownField<T>({
    required String label,
    required T? value,
    required List<DropdownMenuItem<T>> items,
    required void Function(T?) onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: AppBorderStyles.standardBoxDecoration,
          child: DropdownButtonHideUnderline(
            child: DropdownButtonFormField<T>(
              value: value,
              decoration: const InputDecoration(
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              items: items,
              onChanged: onChanged,
            ),
          ),
        ),
      ],
    );
  }

  /// 构建保存按钮
  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      height: 48,
      child: Obx(() => ElevatedButton.icon(
        icon: controller.isLoading.value 
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Icon(Icons.save),
        label: Text(controller.isLoading.value ? '保存中...' : '保存商品'),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppTheme.primaryColor,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
          ),
          textStyle: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        onPressed: controller.isLoading.value ? null : controller.saveJewelry,
      )),
    );
  }
}
