import 'dart:async';
import 'dart:convert';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/stock/stock_in_draft.dart';
import '../core/utils/logger.dart';

/// 草稿服务
class DraftService extends GetxService {
  static const String DRAFT_KEY_PREFIX = 'stock_in_draft_';
  static const String DRAFT_LIST_KEY = 'stock_in_draft_list';
  
  Timer? _autoSaveTimer;
  Timer? _cleanupTimer;

  @override
  void onInit() {
    super.onInit();
    _startCleanupTimer();
  }

  @override
  void onClose() {
    _autoSaveTimer?.cancel();
    _cleanupTimer?.cancel();
    super.onClose();
  }

  /// 保存草稿
  Future<bool> saveDraft(StockInDraft draft) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // 保存草稿数据
      final draftJson = jsonEncode(draft.toJson());
      await prefs.setString('$DRAFT_KEY_PREFIX${draft.id}', draftJson);
      
      // 更新草稿列表
      await _updateDraftList(draft.id);
      
      LoggerService.d('草稿保存成功: ${draft.id}');
      return true;
    } catch (e) {
      LoggerService.e('保存草稿失败', e);
      return false;
    }
  }

  /// 获取草稿
  Future<StockInDraft?> getDraft(String draftId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final draftJson = prefs.getString('$DRAFT_KEY_PREFIX$draftId');
      
      if (draftJson != null) {
        final draftData = jsonDecode(draftJson) as Map<String, dynamic>;
        final draft = StockInDraft.fromJson(draftData);
        
        // 检查是否过期
        if (draft.isExpired) {
          await deleteDraft(draftId);
          return null;
        }
        
        return draft;
      }
      
      return null;
    } catch (e) {
      LoggerService.e('获取草稿失败', e);
      return null;
    }
  }

  /// 获取所有草稿列表
  Future<List<StockInDraft>> getAllDrafts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final draftIds = prefs.getStringList(DRAFT_LIST_KEY) ?? [];
      
      final List<StockInDraft> drafts = [];
      
      for (final draftId in draftIds) {
        final draft = await getDraft(draftId);
        if (draft != null) {
          drafts.add(draft);
        }
      }
      
      // 按更新时间倒序排列
      drafts.sort((a, b) => b.updateTime.compareTo(a.updateTime));
      
      return drafts;
    } catch (e) {
      LoggerService.e('获取草稿列表失败', e);
      return [];
    }
  }

  /// 删除草稿
  Future<bool> deleteDraft(String draftId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // 删除草稿数据
      await prefs.remove('$DRAFT_KEY_PREFIX$draftId');
      
      // 从草稿列表中移除
      final draftIds = prefs.getStringList(DRAFT_LIST_KEY) ?? [];
      draftIds.remove(draftId);
      await prefs.setStringList(DRAFT_LIST_KEY, draftIds);
      
      LoggerService.d('草稿删除成功: $draftId');
      return true;
    } catch (e) {
      LoggerService.e('删除草稿失败', e);
      return false;
    }
  }

  /// 清理过期草稿
  Future<void> cleanupExpiredDrafts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final draftIds = prefs.getStringList(DRAFT_LIST_KEY) ?? [];
      
      final List<String> validDraftIds = [];
      
      for (final draftId in draftIds) {
        final draft = await getDraft(draftId);
        if (draft != null && !draft.isExpired) {
          validDraftIds.add(draftId);
        } else {
          // 删除过期草稿
          await prefs.remove('$DRAFT_KEY_PREFIX$draftId');
          LoggerService.d('清理过期草稿: $draftId');
        }
      }
      
      // 更新草稿列表
      await prefs.setStringList(DRAFT_LIST_KEY, validDraftIds);
      
      LoggerService.d('草稿清理完成，剩余 ${validDraftIds.length} 个草稿');
    } catch (e) {
      LoggerService.e('清理过期草稿失败', e);
    }
  }

  /// 开始自动保存
  void startAutoSave(Function() saveCallback) {
    _autoSaveTimer?.cancel();
    _autoSaveTimer = Timer.periodic(
      const Duration(seconds: 30),
      (_) {
        try {
          saveCallback();
        } catch (e) {
          LoggerService.e('自动保存失败', e);
        }
      },
    );
    LoggerService.d('自动保存已启动，间隔30秒');
  }

  /// 停止自动保存
  void stopAutoSave() {
    _autoSaveTimer?.cancel();
    _autoSaveTimer = null;
    LoggerService.d('自动保存已停止');
  }

  /// 更新草稿列表
  Future<void> _updateDraftList(String draftId) async {
    final prefs = await SharedPreferences.getInstance();
    final draftIds = prefs.getStringList(DRAFT_LIST_KEY) ?? [];
    
    if (!draftIds.contains(draftId)) {
      draftIds.add(draftId);
      await prefs.setStringList(DRAFT_LIST_KEY, draftIds);
    }
  }

  /// 启动清理定时器（每天清理一次）
  void _startCleanupTimer() {
    _cleanupTimer = Timer.periodic(
      const Duration(hours: 24),
      (_) => cleanupExpiredDrafts(),
    );
    
    // 启动时立即执行一次清理
    cleanupExpiredDrafts();
  }

  /// 生成草稿ID
  static String generateDraftId() {
    return 'draft_${DateTime.now().millisecondsSinceEpoch}';
  }
}
