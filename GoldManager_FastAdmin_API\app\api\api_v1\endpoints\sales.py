"""
销售管理API路由
提供销售明细查询、统计分析等功能
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from ....core.database import get_db
from ....core.dependencies import require_permission
from ....schemas.auth import CurrentUserResponse
from ....services.sales_service import SalesService
from ....schemas.sales import (
    SalesItemDetailResponse,
    SalesQueryParams,
    SalesStatistics,
    SalesType,
)
from ....schemas.common import PaginatedResponse

router = APIRouter()


@router.get(
    "/items",
    response_model=PaginatedResponse[SalesItemDetailResponse],
    summary="获取销售明细列表",
    description="获取商品级别的销售明细，支持多种筛选条件",
)
def get_sales_items(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    sales_type: SalesType = Query(SalesType.all, description="销售类型"),
    keyword: str = Query(None, description="关键词搜索(条码、商品名称、客户、出库单号)"),
    store_id: int = Query(None, description="门店ID筛选"),
    start_date: str = Query(None, description="开始日期(YYYY-MM-DD)"),
    end_date: str = Query(None, description="结束日期(YYYY-MM-DD)"),
    operator_id: int = Query(None, description="操作员ID筛选"),
    customer: str = Query(None, description="客户筛选"),
    min_profit: float = Query(None, description="最小利润筛选"),
    max_profit: float = Query(None, description="最大利润筛选"),
    current_user: CurrentUserResponse = Depends(require_permission("sales.view")),
    db: Session = Depends(get_db),
):
    """获取销售商品明细列表"""
    try:
        service = SalesService(db)
        params = SalesQueryParams(
            page=page,
            page_size=page_size,
            sales_type=sales_type,
            keyword=keyword,
            store_id=store_id,
            start_date=start_date,
            end_date=end_date,
            operator_id=operator_id,
            customer=customer,
            min_profit=min_profit,
            max_profit=max_profit,
        )

        items, total = service.get_sales_items(params)

        return PaginatedResponse(
            success=True,
            message="获取销售明细成功",
            data=items,
            pagination={
                "page": page,
                "page_size": page_size,
                "total": total,
                "pages": (total + page_size - 1) // page_size,
            },
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取销售明细失败: {str(e)}")


@router.get(
    "/statistics",
    response_model=SalesStatistics,
    summary="获取销售统计信息",
    description="获取销售统计数据，按销售类型分类统计",
)
def get_sales_statistics(
    sales_type: Optional[SalesType] = Query(None, description="销售类型筛选"),
    store_id: int = Query(None, description="门店ID筛选"),
    start_date: str = Query(None, description="开始日期(YYYY-MM-DD)"),
    end_date: str = Query(None, description="结束日期(YYYY-MM-DD)"),
    current_user: CurrentUserResponse = Depends(require_permission("sales.view")),
    db: Session = Depends(get_db),
):
    """获取销售统计信息"""
    try:
        service = SalesService(db)
        return service.get_sales_statistics(
            sales_type=sales_type,
            store_id=store_id,
            start_date=start_date,
            end_date=end_date
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取销售统计失败: {str(e)}")


@router.get(
    "/order/{order_id}/items",
    response_model=List[SalesItemDetailResponse],
    summary="获取单据商品明细",
    description="获取指定出库单或调拨单的商品明细",
)
def get_order_items(
    order_id: int,
    order_type: str = Query("stock_out", description="单据类型: stock_out|store_transfer"),
    current_user: CurrentUserResponse = Depends(require_permission("sales.view")),
    db: Session = Depends(get_db),
):
    """获取指定单据的商品明细"""
    try:
        service = SalesService(db)
        items = service.get_order_items(order_id, order_type)
        return items
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取单据明细失败: {str(e)}")