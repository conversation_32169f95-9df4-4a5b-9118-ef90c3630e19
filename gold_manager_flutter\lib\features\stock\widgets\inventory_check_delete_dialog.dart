import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/border_styles.dart';
import '../../../core/utils/logger.dart';
import '../models/inventory_check.dart';
import '../services/inventory_check_service.dart';

/// 删除盘点单确认对话框
/// 
/// 实现删除确认对话框
/// 集成后端API调用删除盘点记录
/// 添加安全确认机制
class InventoryCheckDeleteDialog extends StatefulWidget {
  final InventoryCheck inventoryCheck;

  const InventoryCheckDeleteDialog({
    super.key,
    required this.inventoryCheck,
  });

  @override
  State<InventoryCheckDeleteDialog> createState() => _InventoryCheckDeleteDialogState();
}

class _InventoryCheckDeleteDialogState extends State<InventoryCheckDeleteDialog> {
  final _inventoryCheckService = Get.find<InventoryCheckService>();
  final _confirmController = TextEditingController();
  
  bool _isDeleting = false;
  bool _confirmationValid = false;

  @override
  void initState() {
    super.initState();
    _confirmController.addListener(_validateConfirmation);
  }

  @override
  void dispose() {
    _confirmController.removeListener(_validateConfirmation);
    _confirmController.dispose();
    super.dispose();
  }

  /// 验证确认输入
  void _validateConfirmation() {
    final text = _confirmController.text.trim();
    setState(() {
      _confirmationValid = text == '确认删除' || text == widget.inventoryCheck.checkNo;
    });
  }

  /// 删除盘点单
  Future<void> _deleteInventoryCheck() async {
    if (!_confirmationValid) {
      Get.snackbar('提示', '请输入正确的确认文本');
      return;
    }

    try {
      setState(() => _isDeleting = true);

      await _inventoryCheckService.deleteInventoryCheck(widget.inventoryCheck.id);

      Get.back(result: true);
      Get.snackbar('成功', '盘点单删除成功');
      
      LoggerService.d('✅ 盘点单删除成功 - 单号: ${widget.inventoryCheck.checkNo}');
    } catch (e) {
      LoggerService.e('❌ 删除盘点单失败', e);
      Get.snackbar('错误', '删除盘点单失败: ${e.toString()}');
    } finally {
      setState(() => _isDeleting = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppBorderStyles.mediumBorderRadius),
      ),
      child: Container(
        width: 480,
        decoration: AppBorderStyles.standardBoxDecoration.copyWith(
          borderRadius: BorderRadius.circular(AppBorderStyles.mediumBorderRadius),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(),
            _buildContent(),
            _buildActions(),
          ],
        ),
      ),
    );
  }

  /// 构建对话框头部
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.red[50],
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(AppBorderStyles.mediumBorderRadius),
          topRight: Radius.circular(AppBorderStyles.mediumBorderRadius),
        ),
        border: const Border(
          bottom: AppBorderStyles.tableBorder,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.warning,
            color: Colors.red[600],
            size: 24,
          ),
          const SizedBox(width: 12),
          const Text(
            '删除盘点单',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const Spacer(),
          IconButton(
            onPressed: () => Get.back(),
            icon: const Icon(Icons.close, size: 20),
            style: IconButton.styleFrom(
              backgroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建对话框内容
  Widget _buildContent() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWarningInfo(),
          const SizedBox(height: 16),
          _buildInventoryCheckInfo(),
          const SizedBox(height: 16),
          _buildConfirmationInput(),
          const SizedBox(height: 16),
          _buildDangerWarning(),
        ],
      ),
    );
  }

  /// 构建警告信息
  Widget _buildWarningInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red[50],
        borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
        border: Border.all(
          color: Colors.red[200]!,
          width: AppBorderStyles.borderWidth,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: Colors.red[600],
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '危险操作警告',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.red[700],
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '删除盘点单将永久移除所有相关数据，此操作不可撤销！',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.red[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建盘点单信息
  Widget _buildInventoryCheckInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: AppBorderStyles.standardBoxDecoration.copyWith(
        color: Colors.grey[50],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '即将删除的盘点单',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 12),
          _buildInfoRow('盘点单号', widget.inventoryCheck.checkNo),
          _buildInfoRow('盘点门店', widget.inventoryCheck.storeName ?? '-'),
          _buildInfoRow('盘点状态', widget.inventoryCheck.statusText),
          _buildInfoRow('应盘数量', widget.inventoryCheck.totalCount.toString()),
          _buildInfoRow('已盘数量', widget.inventoryCheck.checkedCount.toString()),
        ],
      ),
    );
  }

  /// 构建信息行
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 12,
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建确认输入框
  Widget _buildConfirmationInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '安全确认',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          '请输入 "确认删除" 或盘点单号 "${widget.inventoryCheck.checkNo}" 来确认删除操作：',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: AppBorderStyles.standardBoxDecoration.copyWith(
            border: Border.all(
              color: _confirmationValid ? Colors.green : Colors.red,
              width: 1,
            ),
          ),
          child: TextField(
            controller: _confirmController,
            decoration: const InputDecoration(
              hintText: '请输入确认文本',
              border: InputBorder.none,
              enabledBorder: InputBorder.none,
              focusedBorder: InputBorder.none,
              disabledBorder: InputBorder.none,
              contentPadding: EdgeInsets.all(12),
            ),
          ),
        ),
        if (_confirmController.text.isNotEmpty && !_confirmationValid)
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Text(
              '确认文本不正确',
              style: TextStyle(
                fontSize: 12,
                color: Colors.red[600],
              ),
            ),
          ),
      ],
    );
  }

  /// 构建危险警告
  Widget _buildDangerWarning() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.orange[50],
        borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
        border: Border.all(
          color: Colors.orange[200]!,
          width: AppBorderStyles.borderWidth,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            color: Colors.orange[600],
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              '删除后将无法恢复盘点数据，请谨慎操作',
              style: TextStyle(
                fontSize: 12,
                color: Colors.orange[700],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActions() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        border: Border(
          top: AppBorderStyles.tableBorder,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          SizedBox(
            height: 36,
            child: OutlinedButton(
              onPressed: _isDeleting ? null : () => Get.back(),
              style: OutlinedButton.styleFrom(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
                ),
                side: const BorderSide(color: AppBorderStyles.borderColor),
              ),
              child: const Text('取消'),
            ),
          ),
          const SizedBox(width: 12),
          SizedBox(
            height: 36,
            child: ElevatedButton(
              onPressed: (_isDeleting || !_confirmationValid) ? null : _deleteInventoryCheck,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red[600],
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
                ),
              ),
              child: _isDeleting
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Text('确认删除'),
            ),
          ),
        ],
      ),
    );
  }
}
