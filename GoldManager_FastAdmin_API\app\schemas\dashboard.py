"""
仪表板数据验证模型
定义仪表板API的请求和响应数据结构
"""

from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from decimal import Decimal
from datetime import datetime


# 基础统计数据模型
class BaseStatistics(BaseModel):
    """基础统计数据"""
    total: int = Field(description="总数")
    today: int = Field(description="今日数量")
    this_month: int = Field(description="本月数量")
    growth_rate: float = Field(description="增长率(%)")


class AmountStatistics(BaseModel):
    """金额统计数据"""
    total_amount: Decimal = Field(description="总金额")
    today_amount: Decimal = Field(description="今日金额")
    this_month_amount: Decimal = Field(description="本月金额")
    growth_rate: float = Field(description="增长率(%)")


# 仪表板概览响应模型
class DashboardOverviewResponse(BaseModel):
    """仪表板概览响应"""
    # 基础统计
    total_jewelry: int = Field(description="商品总数")
    total_stores: int = Field(description="门店总数")
    total_members: int = Field(description="会员总数")
    total_admins: int = Field(description="管理员总数")
    
    # 销售统计
    today_sales_amount: Decimal = Field(description="今日销售额")
    today_sales_count: int = Field(description="今日销售单数")
    month_sales_amount: Decimal = Field(description="本月销售额")
    month_sales_count: int = Field(description="本月销售单数")
    
    # 库存统计
    total_inventory_value: Decimal = Field(description="库存总值")
    low_stock_count: int = Field(description="低库存商品数")
    
    # 待处理业务
    pending_stock_in: int = Field(description="待审核入库单")
    pending_stock_out: int = Field(description="待审核出库单")
    pending_returns: int = Field(description="待处理退货单")
    pending_transfers: int = Field(description="待审核调拨单")
    
    # 最近活动
    recent_activities_count: int = Field(description="最近活动数量")
    
    class Config:
        json_schema_extra = {
            "example": {
                "total_jewelry": 1250,
                "total_stores": 5,
                "total_members": 3200,
                "total_admins": 12,
                "today_sales_amount": 25600.00,
                "today_sales_count": 15,
                "month_sales_amount": 456000.00,
                "month_sales_count": 280,
                "total_inventory_value": 2500000.00,
                "low_stock_count": 8,
                "pending_stock_in": 3,
                "pending_stock_out": 7,
                "pending_returns": 2,
                "pending_transfers": 1,
                "recent_activities_count": 25
            }
        }


# 销售统计响应模型
class SalesStatisticsResponse(BaseModel):
    """销售统计响应"""
    total_amount: Decimal = Field(description="总销售额")
    total_orders: int = Field(description="总订单数")
    average_order_value: Decimal = Field(description="平均客单价")
    total_profit: Decimal = Field(description="总利润")
    profit_margin: float = Field(description="利润率(%)")
    
    # 时间段对比
    period_comparison: Dict[str, Any] = Field(description="时间段对比数据")
    
    class Config:
        json_schema_extra = {
            "example": {
                "total_amount": 456000.00,
                "total_orders": 280,
                "average_order_value": 1628.57,
                "total_profit": 125000.00,
                "profit_margin": 27.4,
                "period_comparison": {
                    "current_period": {"amount": 456000.00, "orders": 280},
                    "previous_period": {"amount": 398000.00, "orders": 245},
                    "growth_rate": 14.6
                }
            }
        }


# 库存统计响应模型
class InventoryStatisticsResponse(BaseModel):
    """库存统计响应"""
    total_value: Decimal = Field(description="库存总值")
    total_items: int = Field(description="商品总数")
    total_weight: Decimal = Field(description="总重量(克)")
    
    # 分类统计
    category_distribution: List[Dict[str, Any]] = Field(description="分类分布")
    
    # 库存预警
    low_stock_items: int = Field(description="低库存商品数")
    out_of_stock_items: int = Field(description="缺货商品数")
    
    # 库存流转
    monthly_turnover_rate: float = Field(description="月库存周转率")
    
    class Config:
        json_schema_extra = {
            "example": {
                "total_value": 2500000.00,
                "total_items": 1250,
                "total_weight": 15600.50,
                "category_distribution": [
                    {"category_name": "戒指", "count": 450, "value": 850000.00},
                    {"category_name": "项链", "count": 320, "value": 720000.00}
                ],
                "low_stock_items": 8,
                "out_of_stock_items": 2,
                "monthly_turnover_rate": 2.3
            }
        }


# 会员统计响应模型
class MemberStatisticsResponse(BaseModel):
    """会员统计响应"""
    total_members: int = Field(description="会员总数")
    new_members: int = Field(description="新增会员")
    active_members: int = Field(description="活跃会员")
    
    # 等级分布
    level_distribution: List[Dict[str, Any]] = Field(description="等级分布")
    
    # 消费统计
    total_consumption: Decimal = Field(description="总消费金额")
    average_consumption: Decimal = Field(description="平均消费金额")
    
    # 积分统计
    total_points_issued: int = Field(description="总发放积分")
    total_points_used: int = Field(description="总使用积分")
    
    class Config:
        json_schema_extra = {
            "example": {
                "total_members": 3200,
                "new_members": 45,
                "active_members": 1280,
                "level_distribution": [
                    {"level": 1, "name": "普通会员", "count": 1800},
                    {"level": 2, "name": "银卡会员", "count": 800},
                    {"level": 3, "name": "金卡会员", "count": 400}
                ],
                "total_consumption": 2800000.00,
                "average_consumption": 875.00,
                "total_points_issued": 156000,
                "total_points_used": 89000
            }
        }


# 财务统计响应模型
class FinancialStatisticsResponse(BaseModel):
    """财务统计响应"""
    # 收入统计
    total_revenue: Decimal = Field(description="总收入")
    sales_revenue: Decimal = Field(description="销售收入")
    other_revenue: Decimal = Field(description="其他收入")
    
    # 成本统计
    total_cost: Decimal = Field(description="总成本")
    goods_cost: Decimal = Field(description="商品成本")
    operating_cost: Decimal = Field(description="运营成本")
    
    # 利润统计
    gross_profit: Decimal = Field(description="毛利润")
    net_profit: Decimal = Field(description="净利润")
    profit_margin: float = Field(description="利润率(%)")
    
    # 回收业务
    recycling_amount: Decimal = Field(description="回收金额")
    recycling_profit: Decimal = Field(description="回收利润")
    
    class Config:
        json_schema_extra = {
            "example": {
                "total_revenue": 456000.00,
                "sales_revenue": 456000.00,
                "other_revenue": 0.00,
                "total_cost": 331000.00,
                "goods_cost": 298000.00,
                "operating_cost": 33000.00,
                "gross_profit": 158000.00,
                "net_profit": 125000.00,
                "profit_margin": 27.4,
                "recycling_amount": 85000.00,
                "recycling_profit": 12000.00
            }
        }


# 最近活动响应模型
class ActivityItem(BaseModel):
    """活动项目"""
    id: int = Field(description="记录ID")
    type: str = Field(description="活动类型")
    title: str = Field(description="活动标题")
    description: str = Field(description="活动描述")
    amount: Optional[Decimal] = Field(None, description="涉及金额")
    operator: str = Field(description="操作员")
    store_name: str = Field(description="门店名称")
    created_time: datetime = Field(description="创建时间")


class RecentActivitiesResponse(BaseModel):
    """最近活动响应"""
    activities: List[ActivityItem] = Field(description="活动列表")
    total_count: int = Field(description="总数量")
    
    class Config:
        json_schema_extra = {
            "example": {
                "activities": [
                    {
                        "id": 1,
                        "type": "stock_out",
                        "title": "出库单 OUT202412250001",
                        "description": "销售黄金戒指 1件",
                        "amount": 3200.00,
                        "operator": "张三",
                        "store_name": "旗舰店",
                        "created_time": "2024-12-25T10:30:00"
                    }
                ],
                "total_count": 25
            }
        }


# 销售趋势响应模型
class TrendDataPoint(BaseModel):
    """趋势数据点"""
    date: str = Field(description="日期")
    amount: Decimal = Field(description="金额")
    count: int = Field(description="数量")


class SalesTrendResponse(BaseModel):
    """销售趋势响应"""
    period: str = Field(description="统计周期")
    data_points: List[TrendDataPoint] = Field(description="趋势数据")
    total_amount: Decimal = Field(description="总金额")
    total_count: int = Field(description="总数量")
    average_daily: Decimal = Field(description="日均金额")
    
    class Config:
        json_schema_extra = {
            "example": {
                "period": "month",
                "data_points": [
                    {"date": "2024-12-01", "amount": 15000.00, "count": 8},
                    {"date": "2024-12-02", "amount": 18500.00, "count": 12}
                ],
                "total_amount": 456000.00,
                "total_count": 280,
                "average_daily": 15200.00
            }
        }


# 门店对比响应模型
class StoreComparisonItem(BaseModel):
    """门店对比项目"""
    store_id: int = Field(description="门店ID")
    store_name: str = Field(description="门店名称")
    sales_amount: Decimal = Field(description="销售额")
    order_count: int = Field(description="订单数")
    average_order_value: Decimal = Field(description="客单价")
    growth_rate: float = Field(description="增长率(%)")


class StoreComparisonResponse(BaseModel):
    """门店对比响应"""
    stores: List[StoreComparisonItem] = Field(description="门店列表")
    total_amount: Decimal = Field(description="总销售额")
    total_orders: int = Field(description="总订单数")
    
    class Config:
        json_schema_extra = {
            "example": {
                "stores": [
                    {
                        "store_id": 1,
                        "store_name": "旗舰店",
                        "sales_amount": 180000.00,
                        "order_count": 120,
                        "average_order_value": 1500.00,
                        "growth_rate": 15.2
                    }
                ],
                "total_amount": 456000.00,
                "total_orders": 280
            }
        }


# 分类排行响应模型
class CategoryRankingItem(BaseModel):
    """分类排行项目"""
    category_id: int = Field(description="分类ID")
    category_name: str = Field(description="分类名称")
    sales_amount: Decimal = Field(description="销售额")
    sales_count: int = Field(description="销售数量")
    profit: Decimal = Field(description="利润")
    profit_margin: float = Field(description="利润率(%)")


class CategoryRankingResponse(BaseModel):
    """分类排行响应"""
    categories: List[CategoryRankingItem] = Field(description="分类列表")
    total_amount: Decimal = Field(description="总销售额")
    total_count: int = Field(description="总销售数量")


# 低库存预警响应模型
class LowStockItem(BaseModel):
    """低库存商品"""
    jewelry_id: int = Field(description="商品ID")
    barcode: str = Field(description="条码")
    name: str = Field(description="商品名称")
    category_name: str = Field(description="分类名称")
    store_name: str = Field(description="门店名称")
    current_stock: int = Field(description="当前库存")
    min_stock: int = Field(description="最低库存")
    last_sale_date: Optional[datetime] = Field(None, description="最后销售日期")


class LowStockAlertsResponse(BaseModel):
    """低库存预警响应"""
    items: List[LowStockItem] = Field(description="低库存商品列表")
    total_count: int = Field(description="总数量")
    threshold: int = Field(description="预警阈值")


# 会员增长响应模型
class MemberGrowthDataPoint(BaseModel):
    """会员增长数据点"""
    date: str = Field(description="日期")
    new_members: int = Field(description="新增会员")
    total_members: int = Field(description="累计会员")


class MemberGrowthResponse(BaseModel):
    """会员增长响应"""
    period: str = Field(description="统计周期")
    data_points: List[MemberGrowthDataPoint] = Field(description="增长数据")
    total_new: int = Field(description="总新增")
    growth_rate: float = Field(description="增长率(%)")
