import 'package:get/get.dart';
import 'package:gold_manager_flutter/core/utils/api_provider.dart';
import 'package:gold_manager_flutter/features/sales/controllers/sales_return_controller.dart';
import 'package:gold_manager_flutter/features/sales/controllers/sales_controller.dart';
import 'package:gold_manager_flutter/services/sales_return_service.dart';
import 'package:gold_manager_flutter/services/sales_order_service.dart';
import '../services/sales_service.dart';

/// 销售模块绑定类，用于依赖注入
class SalesBinding extends Bindings {
  @override
  void dependencies() {
    // 确保ApiProvider已注册
    if (!Get.isRegistered<ApiProvider>()) {
      Get.put(ApiProvider());
    }
    
    // 注册销售管理服务
    if (!Get.isRegistered<SalesService>()) {
      Get.put(SalesService());
    }

    // 注册销售订单服务（用于销售退货功能）
    if (!Get.isRegistered<SalesOrderService>()) {
      Get.put(SalesOrderService());
    }

    if (!Get.isRegistered<SalesReturnService>()) {
      Get.put(SalesReturnService());
    }
    
    // 注册控制器
    Get.lazyPut<SalesController>(() => SalesController());
    Get.lazyPut<SalesReturnController>(() => SalesReturnController());
  }
} 