import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../models/recycling/recycling_item.dart';
import '../../controllers/recycling_process_controller.dart';
import 'recycling_process_list_page.dart';

/// 回收物品详情页面
class RecyclingItemDetailPage extends StatelessWidget {
  final RecyclingItem item;
  
  const RecyclingItemDetailPage({
    super.key,
    required this.item,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('回收物品详情'),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildItemInfoCard(),
              const SizedBox(height: 16),
              _buildWeightAndPriceCard(),
              const SizedBox(height: 16),
              _buildProcessInfoCard(),
              const SizedBox(height: 24),
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  // 物品基本信息卡片
  Widget _buildItemInfoCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 物品图片
                item.imageUrl != null
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.network(
                          item.imageUrl!,
                          width: 100,
                          height: 100,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return _buildPlaceholderImage();
                          },
                        ),
                      )
                    : _buildPlaceholderImage(),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        item.name,
                        style: AppTextStyles.subtitle1,
                      ),
                      const SizedBox(height: 8),
                      if (item.description != null && item.description!.isNotEmpty)
                        Text(
                          item.description!,
                          style: AppTextStyles.body2.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                      const SizedBox(height: 8),
                      Text(
                        '回收日期: ${_formatDate(item.createTime)}',
                        style: AppTextStyles.body2,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // 金银重量和价格卡片
  Widget _buildWeightAndPriceCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '重量与价格',
              style: AppTextStyles.subtitle1,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    '金重',
                    '${item.goldWeight.toStringAsFixed(2)}g',
                    Icons.scale,
                    AppColors.warning,
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(
                    '银重',
                    '${item.silverWeight.toStringAsFixed(2)}g',
                    Icons.scale,
                    AppColors.info,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    '金价',
                    '¥${item.goldPrice.toStringAsFixed(2)}/g',
                    Icons.attach_money,
                    AppColors.warning,
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(
                    '银价',
                    '¥${item.silverPrice.toStringAsFixed(2)}/g',
                    Icons.attach_money,
                    AppColors.info,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.success.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    '回收金额',
                    style: AppTextStyles.subtitle2,
                  ),
                  Text(
                    '¥${item.amount.toStringAsFixed(2)}',
                    style: AppTextStyles.subtitle1.copyWith(
                      color: AppColors.success,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 处理信息卡片
  Widget _buildProcessInfoCard() {
    final Color statusColor = _getProcessStatusColor();
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  '处理信息',
                  style: AppTextStyles.subtitle1,
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: statusColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: statusColor,
                      width: 1,
                    ),
                  ),
                  child: Text(
                    _getProcessStatusText(),
                    style: TextStyle(
                      color: statusColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoItem(
              '处理方式',
              item.processType.label,
              _getProcessTypeIcon(),
              _getProcessTypeColor(),
            ),
            const SizedBox(height: 16),
            if (item.processTime != null)
              _buildInfoItem(
                '处理时间',
                _formatDateTime(item.processTime!),
                Icons.access_time,
                AppColors.primary,
              ),
            if (item.processResult > 0)
              Container(
                padding: const EdgeInsets.all(12),
                margin: const EdgeInsets.only(top: 16),
                decoration: BoxDecoration(
                  color: AppColors.success.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      '处理收益',
                      style: AppTextStyles.subtitle2,
                    ),
                    Text(
                      '¥${item.processResult.toStringAsFixed(2)}',
                      style: AppTextStyles.subtitle1.copyWith(
                        color: AppColors.success,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  // 操作按钮组
  Widget _buildActionButtons() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          Expanded(
            child: _buildActionButton(
              icon: Icons.engineering,
              label: '旧料处理',
              color: AppColors.primary,
              onPressed: _navigateToProcess,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildActionButton(
              icon: Icons.edit,
              label: '编辑',
              color: AppColors.info,
              onPressed: () => _editRecyclingItem(),
            ),
          ),
        ],
      ),
    );
  }

  // 创建操作按钮
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return ElevatedButton.icon(
      icon: Icon(icon, color: Colors.white),
      label: Text(
        label,
        style: const TextStyle(color: Colors.white),
      ),
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  // 信息项
  Widget _buildInfoItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: color,
            size: 20,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: const TextStyle(
                  fontSize: 12,
                  color: AppColors.textSecondary,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // 占位图片
  Widget _buildPlaceholderImage() {
    return Container(
      width: 100,
      height: 100,
      decoration: BoxDecoration(
        color: AppColors.backgroundDark,
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Icon(
        Icons.image,
        color: AppColors.textSecondary,
        size: 40,
      ),
    );
  }

  // 跳转到旧料处理页面
  void _navigateToProcess() {
    // 创建旧料处理控制器
    final processController = RecyclingProcessController(
      recyclingItem: item,
    );
    
    // 跳转到处理列表页面
    Get.to(
      () => RecyclingProcessListPage(controller: processController),
      transition: Transition.rightToLeft,
    );
  }

  // 编辑回收物品
  void _editRecyclingItem() {
    // TODO: 实现编辑功能
    Get.snackbar(
      '提示',
      '编辑功能正在开发中',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  // 格式化日期
  String _formatDate(DateTime? date) {
    if (date == null) return '未知';
    return DateFormat('yyyy-MM-dd').format(date);
  }

  // 格式化日期时间
  String _formatDateTime(DateTime dateTime) {
    return DateFormat('yyyy-MM-dd HH:mm').format(dateTime);
  }

  // 获取处理状态文本
  String _getProcessStatusText() {
    switch (item.processStatus) {
      case 0:
        return '未处理';
      case 1:
        return '处理中';
      case 2:
        return '已处理';
      default:
        return '未知';
    }
  }

  // 获取处理状态颜色
  Color _getProcessStatusColor() {
    switch (item.processStatus) {
      case 0:
        return AppColors.warning;
      case 1:
        return AppColors.info;
      case 2:
        return AppColors.success;
      default:
        return AppColors.textSecondary;
    }
  }

  // 获取处理类型图标
  IconData _getProcessTypeIcon() {
    switch (item.processType.value) {
      case 'sell':
        return Icons.attach_money;
      case 'separate':
        return Icons.science;
      case 'repair':
        return Icons.build;
      case 'resell':
        return Icons.shopping_bag;
      default:
        return Icons.help_outline;
    }
  }

  // 获取处理类型颜色
  Color _getProcessTypeColor() {
    switch (item.processType.value) {
      case 'sell':
        return AppColors.success;
      case 'separate':
        return AppColors.primary;
      case 'repair':
        return AppColors.warning;
      case 'resell':
        return AppColors.info;
      default:
        return AppColors.textSecondary;
    }
  }
} 