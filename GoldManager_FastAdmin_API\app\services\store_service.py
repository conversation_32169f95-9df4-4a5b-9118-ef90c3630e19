"""
门店管理业务逻辑服务
提供门店相关的数据操作和业务逻辑
"""

import time
import math
from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc
from loguru import logger

from ..models.store import Store
from ..models.jewelry import Jewelry
from ..models.admin import Admin
from ..schemas.store import (
    StoreCreate,
    StoreUpdate,
    StoreResponse,
    StoreListResponse,
    StoreStatistics
)


class StoreService:
    """门店管理服务类"""

    def __init__(self, db: Session):
        self.db = db

    async def get_stores(
        self,
        page: int = 1,
        page_size: int = 20,
        status: Optional[int] = None,
        keyword: Optional[str] = None,
        current_user_store_id: Optional[int] = None,
        is_admin: bool = False
    ) -> StoreListResponse:
        """
        获取门店列表

        Args:
            page: 页码
            page_size: 每页数量
            status: 状态筛选
            keyword: 搜索关键词（门店名称、地址）
            current_user_store_id: 当前用户所属门店ID
            is_admin: 是否为管理员
        """
        try:
            # 构建查询条件
            query = self.db.query(Store)

            # 根据用户权限过滤门店
            if not is_admin and current_user_store_id is not None:
                # 非管理员用户只能看到自己所属的门店
                query = query.filter(Store.id == current_user_store_id)

            # 状态筛选
            if status is not None:
                query = query.filter(Store.status == status)

            # 关键词搜索
            if keyword:
                keyword = f"%{keyword.strip()}%"
                query = query.filter(
                    or_(
                        Store.name.like(keyword),
                        Store.address.like(keyword),
                        Store.phone.like(keyword)
                    )
                )

            # 计算总数
            total = query.count()

            # 分页查询
            offset = (page - 1) * page_size
            stores = query.order_by(desc(Store.id)).offset(offset).limit(page_size).all()

            # 获取每个门店的统计信息
            store_responses = []
            for store in stores:
                # 统计商品数量
                jewelry_count = self.db.query(Jewelry).filter(Jewelry.store_id == store.id).count()

                # 统计管理员数量
                admin_count = self.db.query(Admin).filter(Admin.store_id == store.id).count()

                # 构建响应对象
                store_response = StoreResponse(
                    id=store.id,
                    name=store.name,
                    address=store.address,
                    phone=store.phone,
                    status=store.status,
                    createtime=store.createtime,
                    updatetime=store.updatetime,
                    jewelry_count=jewelry_count,
                    admin_count=admin_count
                )
                store_responses.append(store_response)

            # 计算总页数
            total_pages = math.ceil(total / page_size) if total > 0 else 1

            logger.info(f"获取门店列表: 页码={page}, 每页={page_size}, 总数={total}")

            return StoreListResponse(
                items=store_responses,
                total=total,
                page=page,
                page_size=page_size,
                total_pages=total_pages
            )

        except Exception as e:
            logger.error(f"获取门店列表失败: {str(e)}")
            raise e

    async def get_store_by_id(self, store_id: int) -> Optional[StoreResponse]:
        """根据ID获取门店详情"""
        try:
            store = self.db.query(Store).filter(Store.id == store_id).first()
            if not store:
                return None

            # 获取统计信息
            jewelry_count = self.db.query(Jewelry).filter(Jewelry.store_id == store.id).count()
            admin_count = self.db.query(Admin).filter(Admin.store_id == store.id).count()

            logger.info(f"获取门店详情: ID={store_id}")

            return StoreResponse(
                id=store.id,
                name=store.name,
                address=store.address,
                phone=store.phone,
                status=store.status,
                createtime=store.createtime,
                updatetime=store.updatetime,
                jewelry_count=jewelry_count,
                admin_count=admin_count
            )

        except Exception as e:
            logger.error(f"获取门店详情失败: store_id={store_id}, error={str(e)}")
            raise e

    async def get_store_by_name(self, name: str) -> Optional[Store]:
        """根据名称获取门店（用于重复性检查）"""
        try:
            return self.db.query(Store).filter(Store.name == name.strip()).first()
        except Exception as e:
            logger.error(f"根据名称获取门店失败: name={name}, error={str(e)}")
            raise e

    async def create_store(self, store_data: StoreCreate) -> StoreResponse:
        """创建新门店"""
        try:
            # 检查门店名称是否重复
            existing_store = await self.get_store_by_name(store_data.name)
            if existing_store:
                raise ValueError("门店名称已存在")

            # 创建门店对象
            current_time = int(time.time())

            db_store = Store(
                name=store_data.name.strip(),
                address=store_data.address.strip() if store_data.address else None,
                phone=store_data.phone.strip() if store_data.phone else None,
                status=store_data.status,
                createtime=current_time,
                updatetime=current_time
            )

            # 保存到数据库
            self.db.add(db_store)
            self.db.commit()
            self.db.refresh(db_store)

            logger.info(f"创建门店成功: ID={db_store.id}, 名称={db_store.name}")

            return StoreResponse(
                id=db_store.id,
                name=db_store.name,
                address=db_store.address,
                phone=db_store.phone,
                status=db_store.status,
                createtime=db_store.createtime,
                updatetime=db_store.updatetime,
                jewelry_count=0,
                admin_count=0
            )

        except Exception as e:
            self.db.rollback()
            logger.error(f"创建门店失败: error={str(e)}")
            raise e

    async def update_store(self, store_id: int, store_data: StoreUpdate) -> Optional[StoreResponse]:
        """更新门店信息"""
        try:
            # 获取现有门店
            store = self.db.query(Store).filter(Store.id == store_id).first()
            if not store:
                return None

            # 检查名称重复（如果更新了名称）
            if store_data.name and store_data.name.strip() != store.name:
                existing_store = await self.get_store_by_name(store_data.name)
                if existing_store and existing_store.id != store_id:
                    raise ValueError("门店名称已存在")

            # 更新字段
            if store_data.name is not None:
                store.name = store_data.name.strip()
            if store_data.address is not None:
                store.address = store_data.address.strip() if store_data.address else None
            if store_data.phone is not None:
                store.phone = store_data.phone.strip() if store_data.phone else None
            if store_data.status is not None:
                store.status = store_data.status

            # 更新时间戳
            store.updatetime = int(time.time())

            # 保存更改
            self.db.commit()
            self.db.refresh(store)

            # 获取统计信息
            jewelry_count = self.db.query(Jewelry).filter(Jewelry.store_id == store.id).count()
            admin_count = self.db.query(Admin).filter(Admin.store_id == store.id).count()

            logger.info(f"更新门店成功: ID={store_id}")

            return StoreResponse(
                id=store.id,
                name=store.name,
                address=store.address,
                phone=store.phone,
                status=store.status,
                createtime=store.createtime,
                updatetime=store.updatetime,
                jewelry_count=jewelry_count,
                admin_count=admin_count
            )

        except Exception as e:
            self.db.rollback()
            logger.error(f"更新门店失败: store_id={store_id}, error={str(e)}")
            raise e

    async def delete_store(self, store_id: int) -> bool:
        """删除门店"""
        try:
            store = self.db.query(Store).filter(Store.id == store_id).first()
            if not store:
                return False

            # 检查是否有关联数据
            jewelry_count = self.db.query(Jewelry).filter(Jewelry.store_id == store_id).count()
            if jewelry_count > 0:
                raise ValueError(f"该门店下还有 {jewelry_count} 个商品，无法删除")

            admin_count = self.db.query(Admin).filter(Admin.store_id == store_id).count()
            if admin_count > 0:
                raise ValueError(f"该门店下还有 {admin_count} 个管理员，无法删除")

            # 执行删除
            self.db.delete(store)
            self.db.commit()

            logger.info(f"删除门店成功: ID={store_id}, 名称={store.name}")
            return True

        except Exception as e:
            self.db.rollback()
            logger.error(f"删除门店失败: store_id={store_id}, error={str(e)}")
            raise e

    async def get_store_statistics(self, store_id: int) -> Optional[StoreStatistics]:
        """获取门店统计信息"""
        try:
            store = self.db.query(Store).filter(Store.id == store_id).first()
            if not store:
                return None

            # 统计商品信息
            total_jewelry = self.db.query(Jewelry).filter(Jewelry.store_id == store_id).count()
            active_jewelry = self.db.query(Jewelry).filter(
                and_(Jewelry.store_id == store_id, Jewelry.status == 1)
            ).count()

            # 统计管理员信息
            total_admins = self.db.query(Admin).filter(Admin.store_id == store_id).count()
            active_admins = self.db.query(Admin).filter(
                and_(Admin.store_id == store_id, Admin.status == 1)
            ).count()

            logger.info(f"获取门店统计: ID={store_id}")

            return StoreStatistics(
                store_id=store.id,
                store_name=store.name,
                total_jewelry=total_jewelry,
                active_jewelry=active_jewelry,
                total_admins=total_admins,
                active_admins=active_admins
            )

        except Exception as e:
            logger.error(f"获取门店统计失败: store_id={store_id}, error={str(e)}")
            raise e

    async def update_store_status(self, store_id: int, status: int) -> Optional[StoreResponse]:
        """更新门店状态"""
        try:
            store = self.db.query(Store).filter(Store.id == store_id).first()
            if not store:
                return None

            store.status = status
            store.updatetime = int(time.time())

            self.db.commit()
            self.db.refresh(store)

            # 获取统计信息
            jewelry_count = self.db.query(Jewelry).filter(Jewelry.store_id == store.id).count()
            admin_count = self.db.query(Admin).filter(Admin.store_id == store.id).count()

            logger.info(f"更新门店状态成功: ID={store_id}, 状态={status}")

            return StoreResponse(
                id=store.id,
                name=store.name,
                address=store.address,
                phone=store.phone,
                status=store.status,
                createtime=store.createtime,
                updatetime=store.updatetime,
                jewelry_count=jewelry_count,
                admin_count=admin_count
            )

        except Exception as e:
            self.db.rollback()
            logger.error(f"更新门店状态失败: store_id={store_id}, error={str(e)}")
            raise e