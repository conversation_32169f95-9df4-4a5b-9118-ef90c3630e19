import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../core/constants/border_styles.dart';
import '../../../core/theme/app_theme.dart';
import '../../../services/auth_service.dart';
import '../../../widgets/empty_state.dart';
import '../../../widgets/loading_state.dart';
import '../controllers/sales_controller.dart';
import '../models/sales_item_detail.dart';

class SalesManagementView extends StatelessWidget {
  const SalesManagementView({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<SalesController>();

    return Material(
      color: AppTheme.backgroundColor,
      child: Column(
        children: [
          _buildFilterSection(controller),
          const Divider(height: 1),
          Expanded(
            child: Obx(() {
              if (controller.isLoading.value && controller.salesItems.isEmpty) {
                return const LoadingState(text: '加载中...', timeoutSeconds: 30);
              }

              if (controller.salesItems.isEmpty) {
                return const EmptyState(
                  icon: Icons.receipt_long,
                  title: '暂无销售数据',
                  message: '请调整筛选条件后重试',
                );
              }

              return _buildSalesTable(controller);
            }),
          ),
        ],
      ),
    );
  }

  /// 构建筛选区域 - 复用出库管理页面的单行布局结构
  Widget _buildFilterSection(SalesController controller) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: AppBorderStyles.tableBorder,
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center, // 确保所有控件垂直居中对齐
        children: [
          // 图标和标题 - 复用出库管理页面的样式
          Icon(
            Icons.receipt_long,
            color: Colors.blue[600],
            size: 20
          ),
          const SizedBox(width: 8),
          const Text(
            '销售管理',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(width: 24),

          // 操作员信息标签 - 复用出库管理页面的样式
          Obx(() {
            final authService = Get.find<AuthService>();
            final operatorName = authService.userNickname.value.isNotEmpty
                ? authService.userNickname.value
                : authService.userName.value;
            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(AppBorderStyles.largeBorderRadius), // 使用统一的大圆角
                border: Border.all(color: Colors.blue[200]!, width: AppBorderStyles.borderWidth), // 使用统一边框宽度
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.person, size: 14, color: Colors.blue[600]),
                  const SizedBox(width: 4),
                  Text(
                    '操作员: $operatorName',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.blue[700],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            );
          }),
          const SizedBox(width: 24),

          // 销售类型标签和选择器
          const Text(
            '类型:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(width: 8),
          SizedBox(
            width: 120,
            child: _buildCompactSalesTypeSelector(controller),
          ),
          const SizedBox(width: 24),

          // 门店标签和选择器 - 复用出库管理页面的样式
          const Text(
            '门店:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(width: 8),
          SizedBox(
            width: 150,
            child: _buildCompactStoreSelector(controller),
          ),
          const SizedBox(width: 24),

          // 搜索框 - 紧凑型设计
          const Text(
            '搜索:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(width: 8),
          SizedBox(
            width: 200,
            child: _buildCompactSearchField(controller),
          ),
          const SizedBox(width: 24),

          // 日期范围选择器 - 紧凑型设计
          const Text(
            '日期:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(width: 8),
          SizedBox(
            width: 200,
            child: _buildCompactDateRangePicker(controller),
          ),
          const SizedBox(width: 24),

          // 操作按钮组
          _buildResetButton(controller),
          const SizedBox(width: 8),
          _buildSearchButton(controller),
        ],
      ),
    );
  }

  /// 构建紧凑型销售类型选择器
  Widget _buildCompactSalesTypeSelector(SalesController controller) {
    return Obx(() => Container(
      height: 32, // 紧凑高度，与出库管理保持一致
      decoration: AppBorderStyles.standardBoxDecoration,
      child: DropdownButtonHideUnderline(
        child: DropdownButton<SalesType>(
          value: controller.selectedSalesType.value,
          isExpanded: true,
          items: SalesType.values.map((type) => DropdownMenuItem<SalesType>(
            value: type,
            child: Text(type.displayName, style: const TextStyle(fontSize: 13)),
          )).toList(),
          onChanged: (value) {
            if (value != null) {
              controller.setSalesTypeFilter(value);
            }
          },
          style: const TextStyle(fontSize: 13, color: Colors.black87),
          icon: const Icon(Icons.arrow_drop_down, size: 16),
          iconSize: 16,
          menuMaxHeight: 300,
          padding: const EdgeInsets.symmetric(horizontal: 8),
        ),
      ),
    ));
  }

  /// 构建紧凑型搜索字段（单行布局）
  Widget _buildCompactSearchField(SalesController controller) {
    return Container(
      height: 32, // 紧凑高度
      decoration: AppBorderStyles.standardBoxDecoration,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: TextField(
          controller: controller.searchController,
          decoration: const InputDecoration(
            hintText: '出库单号、商品名称',
            border: InputBorder.none,
            enabledBorder: InputBorder.none,
            focusedBorder: InputBorder.none,
            disabledBorder: InputBorder.none,
            errorBorder: InputBorder.none,
            focusedErrorBorder: InputBorder.none,
            contentPadding: EdgeInsets.zero,
            hintStyle: TextStyle(fontSize: 13, color: Colors.grey),
            isDense: true,
          ),
          style: const TextStyle(fontSize: 13),
          onSubmitted: (_) => controller.fetchSalesData(refresh: true),
        ),
      ),
    );
  }

  /// 构建紧凑型门店选择器
  Widget _buildCompactStoreSelector(SalesController controller) {
    return Obx(() {
      final authService = Get.find<AuthService>();
      final isAdmin = authService.userRole.value == 'admin' || authService.hasPermission('super.admin');
      final currentUserStoreId = authService.storeId.value;
      final currentStoreName = authService.storeName.value;

      if (!isAdmin && currentUserStoreId > 0) {
        return Container(
          height: 32,
          decoration: BoxDecoration(
            border: Border.all(color: AppBorderStyles.borderColor),
            borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
            color: Colors.grey[50],
          ),
          child: Row(
            children: [
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  currentStoreName,
                  style: const TextStyle(
                    fontSize: 13,
                    color: Colors.black87,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const Icon(Icons.lock, size: 14, color: Colors.grey),
              const SizedBox(width: 8),
            ],
          ),
        );
      } else {
        return Container(
          height: 32,
          decoration: AppBorderStyles.standardBoxDecoration,
          child: DropdownButtonHideUnderline(
            child: DropdownButton<int>(
              value: controller.selectedStoreId.value == 0 ? null : controller.selectedStoreId.value,
              hint: const Text('全部门店', style: TextStyle(fontSize: 13, color: Colors.grey)),
              isExpanded: true,
              items: [
                const DropdownMenuItem<int>(
                  value: null,
                  child: Text('全部门店', style: TextStyle(fontSize: 13)),
                ),
                ...controller.storeList.map((store) => DropdownMenuItem<int>(
                  value: store.id,
                  child: Text(store.name, style: const TextStyle(fontSize: 13)),
                )),
              ],
              onChanged: (value) {
                controller.setStoreFilter(value ?? 0);
              },
              style: const TextStyle(fontSize: 13, color: Colors.black87),
              icon: const Icon(Icons.arrow_drop_down, size: 16),
              iconSize: 16,
              menuMaxHeight: 300,
              padding: const EdgeInsets.symmetric(horizontal: 8),
            ),
          ),
        );
      }
    });
  }

  /// 构建紧凑型日期范围选择器
  Widget _buildCompactDateRangePicker(SalesController controller) {
    return Obx(() {
      return InkWell(
        onTap: () => _showDateRangePicker(Get.context!, controller),
        child: Container(
          height: 32,
          decoration: AppBorderStyles.standardBoxDecoration,
          child: Row(
            children: [
              const SizedBox(width: 8),
              Icon(Icons.calendar_today, size: 14, color: Colors.grey[600]),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  (controller.startDate.value != null && controller.endDate.value != null)
                      ? '${DateFormat('MM-dd').format(controller.startDate.value!)}-${DateFormat('MM-dd').format(controller.endDate.value!)}'
                      : '选择',
                  style: TextStyle(
                    fontSize: 13,
                    color: (controller.startDate.value != null && controller.endDate.value != null)
                        ? Colors.black87
                        : Colors.grey[600],
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      );
    });
  }

  /// 显示日期范围选择器
  Future<void> _showDateRangePicker(BuildContext context, SalesController controller) async {
    final result = await showDialog<Map<String, DateTime?>>(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return _SalesDateRangePickerDialog(
          initialStartDate: controller.startDate.value,
          initialEndDate: controller.endDate.value,
        );
      },
    );

    if (result != null) {
      controller.setDateRangeFilter(result['start'], result['end']);
      controller.applyFilters();
    }
  }

  /// 构建重置按钮 - 紧凑型设计
  Widget _buildResetButton(SalesController controller) {
    return SizedBox(
      height: 32, // 强制高度为32px，与其他控件保持一致
      child: OutlinedButton.icon(
        icon: const Icon(Icons.refresh, size: 14),
        label: const Text('重置'),
        style: OutlinedButton.styleFrom(
          foregroundColor: Colors.grey[600],
          padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 12), // 移除垂直内边距，由SizedBox控制高度
          minimumSize: const Size(0, 32), // 固定高度32px
          maximumSize: const Size(double.infinity, 32), // 限制最大高度
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
          ),
          side: const BorderSide(color: AppBorderStyles.borderColor),
          textStyle: const TextStyle(fontSize: 13), // 紧凑字体
        ),
        onPressed: controller.resetFilters,
      ),
    );
  }

  /// 构建搜索按钮 - 紧凑型设计
  Widget _buildSearchButton(SalesController controller) {
    return SizedBox(
      height: 32, // 强制高度为32px，与其他控件保持一致
      child: ElevatedButton.icon(
        icon: const Icon(Icons.search, size: 14),
        label: const Text('搜索'),
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF1E88E5), // 使用UI规范主色调
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 12), // 移除垂直内边距，由SizedBox控制高度
          minimumSize: const Size(0, 32), // 固定高度32px
          maximumSize: const Size(double.infinity, 32), // 限制最大高度
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
          ),
          textStyle: const TextStyle(fontSize: 13), // 紧凑字体
        ),
        onPressed: () => controller.fetchSalesData(refresh: true),
      ),
    );
  }

  /// 构建销售表格 - 调整布局结构
  Widget _buildSalesTable(SalesController controller) {
    return Column(
      children: [
        // 表格内容
        Expanded(
          child: _buildResponsiveTable(controller),
        ),
        // 底部汇总和分页区域
        _buildBottomSummaryAndPagination(controller),
      ],
    );
  }



  /// 构建底部汇总和分页区域 - 完全参考新建出库单界面的样式
  Widget _buildBottomSummaryAndPagination(SalesController controller) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
      decoration: BoxDecoration(
        color: Colors.white,
        border: const Border(
          top: AppBorderStyles.tableBorder,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            blurRadius: 3,
            offset: const Offset(0, -1),
          ),
        ],
      ),
      child: Row(
        children: [
          // 💰 汇总信息 - 完全复用新建出库单的样式
          Icon(Icons.calculate, color: Colors.green[600], size: 20),
          const SizedBox(width: 8),
          const Text(
            '汇总信息',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(width: 24),

          // 统计数据项 - 使用新建出库单的样式
          Obx(() {
            final statistics = controller.statistics.value;
            final isLoading = controller.isLoading.value;

            if (isLoading) {
              return const Row(
                children: [
                  SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                  SizedBox(width: 8),
                  Text('统计计算中...', style: TextStyle(fontSize: 13, color: Colors.grey)),
                ],
              );
            }

            return Row(
              children: [
                // 总件数
                _buildSummaryItem(
                  '总件数',
                  '${statistics.totalItems}件',
                  Icons.inventory_2,
                  Colors.blue,
                ),
                const SizedBox(width: 16),

                // 销售额
                _buildSummaryItem(
                  '销售额',
                  statistics.formattedSalesAmount,
                  Icons.attach_money,
                  Colors.green,
                ),
                const SizedBox(width: 16),

                // 利润
                _buildSummaryItem(
                  '利润',
                  statistics.formattedProfit,
                  Icons.trending_up,
                  Colors.orange,
                ),
                const SizedBox(width: 16),

                // 利润率
                _buildSummaryItem(
                  '利润率',
                  statistics.formattedProfitRate,
                  Icons.percent,
                  Colors.purple,
                ),
              ],
            );
          }),

          const Spacer(),

          // 分页控件
          _buildCompactPagination(controller),
        ],
      ),
    );
  }

  /// 构建汇总项 - 完全复用新建出库单的样式
  Widget _buildSummaryItem(String label, String value, IconData icon, MaterialColor color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color[50],
        borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
        border: Border.all(color: color[200]!, width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color[600]),
          const SizedBox(width: 4),
          Text('$label: ', style: const TextStyle(fontSize: 13, color: Colors.black87)),
          Text(
            value,
            style: TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.w600,
              color: color[700],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建紧凑型分页控件
  Widget _buildCompactPagination(SalesController controller) {
    return Obx(() {
      if (controller.totalPages.value <= 1) {
        return const SizedBox.shrink();
      }

      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 页面信息
          Text(
            '第 ${controller.currentPage.value} 页，共 ${controller.totalPages.value} 页',
            style: TextStyle(
              fontSize: 13,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(width: 16),

          // 分页控件
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 首页按钮
              IconButton(
                onPressed: controller.currentPage.value > 1
                    ? () => controller.goToPage(1)
                    : null,
                icon: const Icon(Icons.first_page, size: 16),
                tooltip: '首页',
                constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                padding: EdgeInsets.zero,
              ),
              // 上一页按钮
              IconButton(
                onPressed: controller.currentPage.value > 1
                    ? () => controller.goToPage(controller.currentPage.value - 1)
                    : null,
                icon: const Icon(Icons.chevron_left, size: 16),
                tooltip: '上一页',
                constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                padding: EdgeInsets.zero,
              ),
              // 页码显示
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: Text(
                  '${controller.currentPage.value}/${controller.totalPages.value}',
                  style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
                ),
              ),
              // 下一页按钮
              IconButton(
                onPressed: controller.currentPage.value < controller.totalPages.value
                    ? () => controller.goToPage(controller.currentPage.value + 1)
                    : null,
                icon: const Icon(Icons.chevron_right, size: 16),
                tooltip: '下一页',
                constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                padding: EdgeInsets.zero,
              ),
              // 末页按钮
              IconButton(
                onPressed: controller.currentPage.value < controller.totalPages.value
                    ? () => controller.goToPage(controller.totalPages.value)
                    : null,
                icon: const Icon(Icons.last_page, size: 16),
                tooltip: '末页',
                constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                padding: EdgeInsets.zero,
              ),
            ],
          ),
        ],
      );
    });
  }



  /// 构建响应式表格 - 复用出库管理页面的样式
  Widget _buildResponsiveTable(SalesController controller) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // 获取可用宽度
        final availableWidth = constraints.maxWidth;

        // 计算列宽 - 根据出库管理页面的比例调整
        final orderNoWidth = availableWidth * 0.12; // 出库单号
        final typeWidth = availableWidth * 0.08; // 类型
        final barcodeWidth = availableWidth * 0.12; // 条码
        final nameWidth = availableWidth * 0.20; // 商品名称
        final storeWidth = availableWidth * 0.12; // 门店
        final salePriceWidth = availableWidth * 0.10; // 销售价
        final costPriceWidth = availableWidth * 0.10; // 成本价
        final profitWidth = availableWidth * 0.10; // 利润
        final actionWidth = availableWidth * 0.06; // 操作

        // 字体大小根据屏幕宽度调整
        final fontSize = availableWidth > 1200 ? 13.0 : 12.0;

        return Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius), // 使用标准圆角
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius), // 使用标准圆角
            child: SingleChildScrollView(
              scrollDirection: Axis.vertical,
              child: SizedBox(
                width: availableWidth,
                child: DataTable(
                  columnSpacing: 0, // 移除列间距，让列宽完全由我们控制
                  horizontalMargin: 0, // 移除水平边距
                  headingRowHeight: 44, // 与出库管理页面保持一致的表头高度
                  dataRowMinHeight: 48, // 与出库管理页面保持一致的数据行高度
                  dataRowMaxHeight: 48,
                  headingTextStyle: TextStyle(
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                    fontSize: fontSize,
                  ),
                  dataTextStyle: TextStyle(
                    fontSize: fontSize,
                    color: Colors.black87,
                  ),
                  headingRowColor: WidgetStateProperty.all(AppBorderStyles.tableHeaderBackground), // 使用统一的表头背景色
                  border: AppBorderStyles.tableStandardBorder, // 使用统一的表格边框
                  columns: [
                    DataColumn(
                      label: Container(
                        width: orderNoWidth,
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: const Text(
                          '出库单号',
                          style: TextStyle(fontWeight: FontWeight.w600),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                    DataColumn(
                      label: Container(
                        width: typeWidth,
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: const Text(
                          '类型',
                          style: TextStyle(fontWeight: FontWeight.w600),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                    DataColumn(
                      label: Container(
                        width: barcodeWidth,
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: const Text(
                          '条码',
                          style: TextStyle(fontWeight: FontWeight.w600),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                    DataColumn(
                      label: Container(
                        width: nameWidth,
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: const Text(
                          '商品名称',
                          style: TextStyle(fontWeight: FontWeight.w600),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                    DataColumn(
                      label: Container(
                        width: storeWidth,
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: const Text(
                          '门店',
                          style: TextStyle(fontWeight: FontWeight.w600),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                    DataColumn(
                      label: Container(
                        width: salePriceWidth,
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: const Text(
                          '销售价',
                          style: TextStyle(fontWeight: FontWeight.w600),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                    DataColumn(
                      label: Container(
                        width: costPriceWidth,
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: const Text(
                          '成本价',
                          style: TextStyle(fontWeight: FontWeight.w600),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                    DataColumn(
                      label: Container(
                        width: profitWidth,
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: const Text(
                          '利润',
                          style: TextStyle(fontWeight: FontWeight.w600),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                    DataColumn(
                      label: Container(
                        width: actionWidth,
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: const Text(
                          '操作',
                          style: TextStyle(fontWeight: FontWeight.w600),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ],
            rows: controller.salesItems.asMap().entries.map((entry) {
              final index = entry.key;
              final item = entry.value;
              final isEvenRow = index % 2 == 0;
              
              return DataRow(
                color: WidgetStateProperty.all(
                  isEvenRow ? const Color(0xFFF3E5F5) : Colors.white
                ),
                cells: [
                  DataCell(
                    Container(
                      width: orderNoWidth,
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: Text(
                        item.orderNo,
                        style: const TextStyle(
                          fontWeight: FontWeight.w500,
                          color: Color(0xFF1E88E5),
                          decoration: TextDecoration.underline,
                        ),
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    onTap: () {
                      // TODO: 显示出库单详情
                    },
                  ),
                  DataCell(
                    Container(
                      width: typeWidth,
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: Center(
                        child: _buildSalesTypeBadge(_getSalesTypeFromString(item.salesType)),
                      ),
                    ),
                  ),
                  DataCell(
                    Container(
                      width: barcodeWidth,
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: Text(
                        item.productCode,
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                  DataCell(
                    Container(
                      width: nameWidth,
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: Text(
                        item.productName,
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                  DataCell(
                    Container(
                      width: storeWidth,
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: Text(
                        item.formattedStoreName,
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                  DataCell(
                    Container(
                      width: salePriceWidth,
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: Text(
                        '¥${item.totalAmount.toStringAsFixed(2)}',
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          color: Colors.green[700],
                        ),
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                  DataCell(
                    Container(
                      width: costPriceWidth,
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: Text(
                        '¥${item.costPrice.toStringAsFixed(2)}',
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          color: Colors.grey[700],
                        ),
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                  DataCell(
                    Container(
                      width: profitWidth,
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            item.profit >= 0 ? Icons.trending_up : Icons.trending_down,
                            size: 14,
                            color: item.profit >= 0 ? Colors.green[700] : Colors.red[700],
                          ),
                          const SizedBox(width: 2),
                          Flexible(
                            child: Text(
                              '¥${item.profit.toStringAsFixed(2)}',
                              style: TextStyle(
                                fontWeight: FontWeight.w600,
                                color: item.profit >= 0 ? Colors.green[700] : Colors.red[700],
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  DataCell(
                    Container(
                      width: actionWidth,
                      padding: const EdgeInsets.symmetric(horizontal: 4),
                      child: Center(
                        child: ElevatedButton(
                          onPressed: () {
                            // TODO: 显示商品详情
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF1E88E5),
                            foregroundColor: Colors.white,
                            minimumSize: const Size(50, 24),
                            maximumSize: const Size(50, 24),
                            padding: EdgeInsets.zero,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
                            ),
                          ),
                          child: const Text(
                            '查看',
                            style: TextStyle(
                              fontSize: 11,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              );
                  }).toList(),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// 构建销售类型徽章
  Widget _buildSalesTypeBadge(SalesType type) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: _getTypeColor(type).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
        border: Border.all(color: _getTypeColor(type), width: 1),
      ),
      child: Text(
        type.displayName,
        style: TextStyle(
          color: _getTypeColor(type),
          fontSize: 11,
          fontWeight: FontWeight.w500,
        ),
        overflow: TextOverflow.ellipsis,
        maxLines: 1,
      ),
    );
  }

  /// 获取类型对应的颜色
  Color _getTypeColor(SalesType type) {
    switch (type) {
      case SalesType.all:
        return Colors.grey[600]!;
      case SalesType.retail:
        return const Color(0xFF1E88E5);
      case SalesType.wholesale:
        return Colors.green[600]!;
      case SalesType.transfer:
        return Colors.orange[600]!;
      case SalesType.recycling:
        return Colors.purple[600]!;
    }
  }

  /// 将字符串转换为SalesType枚举
  SalesType _getSalesTypeFromString(String typeStr) {
    switch (typeStr.toLowerCase()) {
      case 'all':
      case '全部':
        return SalesType.all;
      case 'retail':
      case '零售':
        return SalesType.retail;
      case 'wholesale':
      case '批发':
        return SalesType.wholesale;
      case 'transfer':
      case '调拨':
        return SalesType.transfer;
      case 'recycling':
      case '回收':
        return SalesType.recycling;
      default:
        return SalesType.all;
    }
  }
}

/// 销售管理日期范围选择器对话框
class _SalesDateRangePickerDialog extends StatefulWidget {
  final DateTime? initialStartDate;
  final DateTime? initialEndDate;

  const _SalesDateRangePickerDialog({
    this.initialStartDate,
    this.initialEndDate,
  });

  @override
  State<_SalesDateRangePickerDialog> createState() => _SalesDateRangePickerDialogState();
}

class _SalesDateRangePickerDialogState extends State<_SalesDateRangePickerDialog> {
  DateTime? _startDate;
  DateTime? _endDate;

  @override
  void initState() {
    super.initState();
    _startDate = widget.initialStartDate;
    _endDate = widget.initialEndDate;
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppBorderStyles.largeBorderRadius),
      ),
      child: Container(
        width: 450,
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.date_range, color: AppTheme.primaryColor, size: 24),
                const SizedBox(width: 12),
                const Text(
                  '选择日期范围',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.close, size: 24),
                  onPressed: () => Navigator.of(context).pop(),
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                ),
              ],
            ),
            const SizedBox(height: 24),
            Row(
              children: [
                Expanded(
                  child: _buildDateSelector(
                    '开始日期',
                    _startDate,
                    () => _selectStartDate(),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildDateSelector(
                    '结束日期',
                    _endDate,
                    () => _selectEndDate(),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            _buildQuickSelectButtons(),
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('取消'),
                ),
                const SizedBox(width: 12),
                TextButton(
                  onPressed: _clearDates,
                  child: const Text('清空'),
                ),
                const SizedBox(width: 12),
                ElevatedButton(
                  onPressed: _confirmSelection,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  ),
                  child: const Text('确定'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateSelector(String label, DateTime? date, VoidCallback onTap) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
          child: Container(
            width: double.infinity,
            height: 40,
            padding: const EdgeInsets.symmetric(horizontal: 12),
            decoration: AppBorderStyles.standardBoxDecoration,
            child: Row(
              children: [
                Icon(Icons.calendar_today, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    date != null
                        ? DateFormat('yyyy-MM-dd').format(date)
                        : '请选择日期',
                    style: TextStyle(
                      fontSize: 14,
                      color: date != null ? Colors.black87 : Colors.grey[600],
                    ),
                  ),
                ),
                Icon(Icons.arrow_drop_down, size: 20, color: Colors.grey[600]),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildQuickSelectButtons() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '快捷选择',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            _buildQuickButton('今天', () => _setToday()),
            _buildQuickButton('昨天', () => _setYesterday()),
            _buildQuickButton('本周', () => _setThisWeek()),
            _buildQuickButton('本月', () => _setThisMonth()),
            _buildQuickButton('上月', () => _setLastMonth()),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickButton(String text, VoidCallback onPressed) {
    return OutlinedButton(
      onPressed: onPressed,
      style: OutlinedButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        minimumSize: const Size(0, 32),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
        ),
      ),
      child: Text(
        text,
        style: const TextStyle(fontSize: 12),
      ),
    );
  }

  Future<void> _selectStartDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _startDate ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (picked != null) {
      setState(() {
        _startDate = picked;
        if (_endDate != null && _endDate!.isBefore(picked)) {
          _endDate = picked;
        }
      });
    }
  }

  Future<void> _selectEndDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _endDate ?? DateTime.now(),
      firstDate: _startDate ?? DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (picked != null) {
      setState(() {
        _endDate = picked;
      });
    }
  }

  void _clearDates() {
    setState(() {
      _startDate = null;
      _endDate = null;
    });
  }

  void _confirmSelection() {
    Navigator.of(context).pop({
      'start': _startDate,
      'end': _endDate,
    });
  }

  void _setToday() {
    final today = DateTime.now();
    setState(() {
      _startDate = DateTime(today.year, today.month, today.day);
      _endDate = DateTime(today.year, today.month, today.day, 23, 59, 59);
    });
  }

  void _setYesterday() {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    setState(() {
      _startDate = DateTime(yesterday.year, yesterday.month, yesterday.day);
      _endDate = DateTime(yesterday.year, yesterday.month, yesterday.day, 23, 59, 59);
    });
  }

  void _setThisWeek() {
    final now = DateTime.now();
    final weekday = now.weekday;
    final startOfWeek = now.subtract(Duration(days: weekday - 1));
    final endOfWeek = startOfWeek.add(const Duration(days: 6));

    setState(() {
      _startDate = DateTime(startOfWeek.year, startOfWeek.month, startOfWeek.day);
      _endDate = DateTime(endOfWeek.year, endOfWeek.month, endOfWeek.day, 23, 59, 59);
    });
  }

  void _setThisMonth() {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 0);

    setState(() {
      _startDate = startOfMonth;
      _endDate = DateTime(endOfMonth.year, endOfMonth.month, endOfMonth.day, 23, 59, 59);
    });
  }

  void _setLastMonth() {
    final now = DateTime.now();
    final startOfLastMonth = DateTime(now.year, now.month - 1, 1);
    final endOfLastMonth = DateTime(now.year, now.month, 0);

    setState(() {
      _startDate = startOfLastMonth;
      _endDate = DateTime(endOfLastMonth.year, endOfLastMonth.month, endOfLastMonth.day, 23, 59, 59);
    });
  }
}