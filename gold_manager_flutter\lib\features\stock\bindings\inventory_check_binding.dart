import 'package:get/get.dart';

import '../services/inventory_check_service.dart';
import '../controllers/inventory_check_controller.dart';

/// 库存盘点绑定
class InventoryCheckBinding extends Bindings {
  @override
  void dependencies() {
    // 注册服务 - 使用新版本的InventoryCheckService
    Get.lazyPut<InventoryCheckService>(() => InventoryCheckService());

    // 注册控制器
    Get.lazyPut<InventoryCheckController>(() => InventoryCheckController());
  }
}
