import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/utils/logger.dart';
import '../../../core/constants/app_permissions.dart';
import '../../../models/stock/inventory_check_statistics.dart';
import '../../../models/store/store.dart';
import '../services/inventory_check_service.dart';
import '../../../services/store_service.dart';
import '../../../services/auth_service.dart';
import '../widgets/inventory_check_create_dialog.dart';
import '../widgets/inventory_check_detail_dialog.dart';
import '../widgets/inventory_check_edit_dialog.dart';
import '../widgets/inventory_check_delete_dialog.dart';
import '../models/inventory_check.dart' as new_models;
import 'stock_tab_controller.dart';

/// 库存盘点控制器
class InventoryCheckController extends GetxController {
  // 服务
  final InventoryCheckService _inventoryCheckService = Get.find<InventoryCheckService>();
  final StoreService _storeService = Get.find<StoreService>();
  final AuthService _authService = Get.find<AuthService>();

  // 状态变量
  final RxBool isLoading = false.obs;
  final RxList<new_models.InventoryCheck> inventoryChecks = <new_models.InventoryCheck>[].obs;
  final RxList<Store> storeList = <Store>[].obs;

  // 筛选条件
  final TextEditingController searchController = TextEditingController();
  final Rx<DateTime?> startDate = Rx<DateTime?>(null);
  final Rx<DateTime?> endDate = Rx<DateTime?>(null);
  final RxnInt selectedStoreId = RxnInt();
  final RxnInt selectedStatus = RxnInt();
  final RxnInt selectedOperatorId = RxnInt();

  // 统计信息
  final Rx<InventoryCheckStatistics?> statistics = Rx<InventoryCheckStatistics?>(null);
  final RxInt totalCount = 0.obs;
  final RxInt ongoingCount = 0.obs;
  final RxInt completedCount = 0.obs;
  final RxInt differenceCount = 0.obs;

  // 分页
  final RxInt currentPage = 1.obs;
  final RxInt totalPages = 1.obs;
  final int pageSize = 20;

  /// 检查是否有权限
  bool get canCreate => _authService.hasPermission(AppPermissions.stockCheck);
  bool get canEdit => _authService.hasPermission(AppPermissions.stockCheck);
  bool get canDelete => _authService.hasPermission(AppPermissions.stockCheck);
  bool get canView => _authService.hasPermission(AppPermissions.stockView);

  /// 检查是否可以盘点指定的盘点单
  bool canCheck(new_models.InventoryCheck check) {
    // 基础权限检查
    if (!_authService.hasPermission(AppPermissions.stockCheck)) {
      return false;
    }

    // 只有进行中的盘点单可以盘点
    if (check.status != 0) {
      return false;
    }

    // 权限控制：非管理员用户只能盘点自己门店的商品
    if (!isAdmin) {
      return check.storeId == _authService.storeId.value;
    }

    return true;
  }

  /// 是否为管理员
  bool get isAdmin => _authService.userRole.value == 'admin';

  /// 门店下拉框是否可用
  bool get storeDropdownEnabled => isAdmin;

  @override
  void onInit() {
    super.onInit();
    LoggerService.d('InventoryCheckController 初始化');
    _initializeData();
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }

  /// 初始化数据
  void _initializeData() {
    // 权限控制：非管理员用户只能查看自己门店
    if (!isAdmin) {
      selectedStoreId.value = _authService.storeId.value;
    }

    // 延迟初始化，确保权限已加载
    Future.delayed(const Duration(milliseconds: 100), () {
      if (_authService.hasPermission('store.view')) {
        fetchStores();
      }
      if (canView) {
        fetchInventoryChecks();
        fetchStatistics();
      }
    });
  }

  /// 获取门店列表
  Future<void> fetchStores() async {
    try {
      final stores = await _storeService.getAllStores();
      storeList.value = stores;
    } catch (e) {
      LoggerService.e('获取门店列表失败', e);
      Get.snackbar(
        '错误',
        '获取门店列表失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 获取盘点单列表
  Future<void> fetchInventoryChecks() async {
    try {
      isLoading.value = true;

      final result = await _inventoryCheckService.getInventoryCheckList(
        page: currentPage.value,
        pageSize: pageSize,
        keyword: searchController.text.trim().isEmpty ? null : searchController.text.trim(),
        storeId: selectedStoreId.value,
        status: selectedStatus.value,
        operatorId: selectedOperatorId.value,
        startDate: startDate.value?.toIso8601String().split('T')[0],
        endDate: endDate.value?.toIso8601String().split('T')[0],
      );

      inventoryChecks.value = result.items;
      totalPages.value = result.totalPages;

      _calculateStatistics();

      LoggerService.d('✅ 盘点单列表获取成功: ${result.items.length} 条记录');
    } catch (e) {
      LoggerService.e('获取盘点单列表失败', e);
      Get.snackbar(
        '错误',
        '获取盘点单列表失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// 获取统计信息
  Future<void> fetchStatistics() async {
    try {
      final result = await _inventoryCheckService.getInventoryCheckStatistics(
        storeId: selectedStoreId.value,
        startDate: startDate.value?.toIso8601String().split('T')[0],
        endDate: endDate.value?.toIso8601String().split('T')[0],
      );

      // 新版本返回Map<String, dynamic>，需要转换
      // statistics.value = result;
      LoggerService.d('✅ 盘点统计获取成功: $result');
    } catch (e) {
      LoggerService.e('获取盘点统计失败', e);
      // 统计失败不影响主要功能，只记录日志
    }
  }

  /// 计算本地统计信息
  void _calculateStatistics() {
    totalCount.value = inventoryChecks.length;
    ongoingCount.value = inventoryChecks.where((check) => check.status == 0).length;
    completedCount.value = inventoryChecks.where((check) => check.status == 1).length;
    differenceCount.value = inventoryChecks
        .map((check) => check.differenceCount)
        .fold(0, (sum, count) => sum + count);
  }

  /// 搜索盘点单
  void searchInventoryChecks() {
    currentPage.value = 1;
    fetchInventoryChecks();
  }

  /// 重置筛选条件
  void resetFilters() {
    searchController.clear();
    selectedStatus.value = null;
    startDate.value = null;
    endDate.value = null;

    // 非管理员用户保持门店筛选
    if (isAdmin) {
      selectedStoreId.value = null;
    }

    currentPage.value = 1;
    fetchInventoryChecks();
  }

  /// 分页
  void goToPage(int page) {
    if (page < 1 || page > totalPages.value) return;
    currentPage.value = page;
    fetchInventoryChecks();
  }

  /// 创建盘点单
  Future<void> createInventoryCheck() async {
    try {
      final result = await Get.dialog<new_models.InventoryCheck>(
        const InventoryCheckCreateDialog(),
      );

      if (result != null) {
        Get.snackbar(
          '成功',
          '盘点单创建成功: ${result.checkNo}',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );

        // 刷新列表
        fetchInventoryChecks();
        fetchStatistics();
      }
    } catch (e) {
      LoggerService.e('创建盘点单失败', e);
      Get.snackbar(
        '错误',
        '创建盘点单失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 查看盘点单详情
  Future<void> viewInventoryCheck(new_models.InventoryCheck check) async {
    try {
      await Get.dialog(
        InventoryCheckDetailDialog(inventoryCheck: check),
      );
    } catch (e) {
      LoggerService.e('查看盘点单详情失败', e);
      Get.snackbar(
        '错误',
        '查看盘点单详情失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 编辑盘点单
  Future<void> editInventoryCheck(new_models.InventoryCheck check) async {
    if (!check.canEdit) {
      Get.snackbar(
        '提示',
        '该盘点单已完成或已取消，无法编辑',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      return;
    }

    try {
      final result = await Get.dialog<new_models.InventoryCheck>(
        InventoryCheckEditDialog(inventoryCheck: check),
      );

      if (result != null) {
        Get.snackbar(
          '成功',
          '盘点单更新成功',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );

        // 刷新列表
        fetchInventoryChecks();
        fetchStatistics();
      }
    } catch (e) {
      LoggerService.e('编辑盘点单失败', e);
      Get.snackbar(
        '错误',
        '编辑盘点单失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 删除盘点单
  Future<void> deleteInventoryCheck(new_models.InventoryCheck check) async {
    if (!check.canDelete) {
      Get.snackbar(
        '提示',
        '该盘点单无法删除',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      return;
    }

    try {
      final confirmed = await Get.dialog<bool>(
        InventoryCheckDeleteDialog(inventoryCheck: check),
      );

      if (confirmed == true) {
        Get.snackbar(
          '成功',
          '盘点单删除成功',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );

        // 刷新列表
        fetchInventoryChecks();
        fetchStatistics();
      }
    } catch (e) {
      LoggerService.e('删除盘点单失败', e);
      Get.snackbar(
        '错误',
        '删除盘点单失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 完成盘点
  /// 将盘点单状态更新为已完成
  Future<void> completeInventoryCheck(new_models.InventoryCheck check) async {
    final confirmed = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('确认完成盘点'),
        content: Text('确定要完成盘点单 ${check.checkNo} 吗？完成后将无法再修改。'),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            child: const Text('确定'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _inventoryCheckService.completeInventoryCheck(check.id);

        Get.snackbar(
          '成功',
          '盘点单已完成',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );

        fetchInventoryChecks();
        fetchStatistics();
      } catch (e) {
        LoggerService.e('完成盘点失败', e);
        Get.snackbar(
          '错误',
          '完成盘点失败: ${e.toString()}',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    }
  }

  /// 取消盘点
  /// 将盘点单状态更新为已取消
  Future<void> cancelInventoryCheck(new_models.InventoryCheck check) async {
    final confirmed = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('确认取消盘点'),
        content: Text('确定要取消盘点单 ${check.checkNo} 吗？取消后将无法恢复。'),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            child: const Text('确定'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _inventoryCheckService.cancelInventoryCheck(check.id);

        Get.snackbar(
          '成功',
          '盘点单已取消',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );

        fetchInventoryChecks();
        fetchStatistics();
      } catch (e) {
        LoggerService.e('取消盘点失败', e);
        Get.snackbar(
          '错误',
          '取消盘点失败: ${e.toString()}',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    }
  }

  /// 创建新的盘点单
  /// 支持选择门店和商品范围
  Future<void> createNewInventoryCheck({
    required int storeId,
    String? remark,
  }) async {
    try {
      isLoading.value = true;

      final request = new_models.InventoryCheckCreateRequest(
        storeId: storeId,
        remark: remark,
      );

      final newCheck = await _inventoryCheckService.createInventoryCheck(request);

      Get.snackbar(
        '成功',
        '盘点单创建成功: ${newCheck.checkNo}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );

      // 刷新列表
      fetchInventoryChecks();
      fetchStatistics();

      // 跳转到盘点详情页面
      viewInventoryCheck(newCheck);

    } catch (e) {
      LoggerService.e('创建盘点单失败', e);
      Get.snackbar(
        '错误',
        '创建盘点单失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// 更新盘点单备注
  /// 只能更新备注信息
  Future<void> updateInventoryCheckRemark(new_models.InventoryCheck check, String remark) async {
    try {
      final request = new_models.InventoryCheckUpdateRequest(remark: remark);
      await _inventoryCheckService.updateInventoryCheck(check.id, request);

      Get.snackbar(
        '成功',
        '盘点单备注更新成功',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );

      fetchInventoryChecks();
    } catch (e) {
      LoggerService.e('更新盘点单备注失败', e);
      Get.snackbar(
        '错误',
        '更新盘点单备注失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 开始盘点
  /// 在新标签页中打开盘点操作界面
  Future<void> startInventoryCheck(new_models.InventoryCheck check) async {
    if (!canCheck(check)) {
      Get.snackbar(
        '提示',
        '无权限盘点此盘点单',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      return;
    }

    try {
      // 获取库存管理标签页控制器
      final stockTabController = Get.find<StockTabController>();

      // 在新标签页中打开盘点操作界面
      stockTabController.openInventoryCheckOperation(check);

      LoggerService.d('✅ 成功在新标签页中打开盘点操作: ${check.checkNo}');

    } catch (e) {
      LoggerService.e('打开盘点操作标签页失败', e);

      // 如果标签页方式失败，回退到原有的页面跳转方式
      try {
        final result = await Get.toNamed('/stock/inventory-check/operation/${check.id}');

        // 如果盘点有更新，刷新列表
        if (result == true) {
          fetchInventoryChecks();
          fetchStatistics();
        }
      } catch (fallbackError) {
        LoggerService.e('回退方式也失败', fallbackError);
        Get.snackbar(
          '错误',
          '打开盘点操作界面失败: ${e.toString()}',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    }
  }
}
