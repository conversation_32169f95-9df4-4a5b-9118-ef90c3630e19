import 'package:get/get.dart';
import '../../../core/config/app_config.dart';
import '../../../core/services/api_service.dart';
import '../../../models/recycling/metal_separation.dart';

/// 金属分离服务类
class MetalSeparationService extends GetxService {
  final ApiService _apiService = Get.find<ApiService>();
  
  /// 获取金属分离记录列表
  Future<List<MetalSeparation>> getMetalSeparationList(int recyclingItemId) async {
    try {
      final response = await _apiService.get(
        '${AppConfig.apiEndpoint['metalSeparation']}/index',
        queryParameters: {
          'recycling_item_id': recyclingItemId,
        },
      );
      
      if (response.data['code'] == 1) {
        final List<dynamic> list = response.data['data'];
        return list.map((item) => MetalSeparation.fromJson(item)).toList();
      } else {
        Get.log('获取金属分离记录失败: ${response.data['msg']}', isError: true);
        return [];
      }
    } catch (e) {
      Get.log('获取金属分离记录异常: $e', isError: true);
      return [];
    }
  }
  
  /// 获取金属分离记录详情
  Future<MetalSeparation?> getMetalSeparationDetail(int id) async {
    try {
      final response = await _apiService.get(
        '${AppConfig.apiEndpoint['metalSeparation']}/detail',
        queryParameters: {'id': id},
      );
      
      if (response.data['code'] == 1) {
        return MetalSeparation.fromJson(response.data['data']);
      } else {
        Get.log('获取金属分离记录详情失败: ${response.data['msg']}', isError: true);
        return null;
      }
    } catch (e) {
      Get.log('获取金属分离记录详情异常: $e', isError: true);
      return null;
    }
  }
  
  /// 添加金属分离记录
  Future<bool> addMetalSeparation(MetalSeparation metalSeparation) async {
    try {
      final response = await _apiService.post(
        '${AppConfig.apiEndpoint['metalSeparation']}/add',
        data: metalSeparation.toJson(),
      );
      
      if (response.data['code'] == 1) {
        return true;
      } else {
        Get.log('添加金属分离记录失败: ${response.data['msg']}', isError: true);
        return false;
      }
    } catch (e) {
      Get.log('添加金属分离记录异常: $e', isError: true);
      return false;
    }
  }
  
  /// 更新金属分离记录
  Future<bool> updateMetalSeparation(MetalSeparation metalSeparation) async {
    try {
      final response = await _apiService.post(
        '${AppConfig.apiEndpoint['metalSeparation']}/edit',
        data: metalSeparation.toJson(),
      );
      
      if (response.data['code'] == 1) {
        return true;
      } else {
        Get.log('更新金属分离记录失败: ${response.data['msg']}', isError: true);
        return false;
      }
    } catch (e) {
      Get.log('更新金属分离记录异常: $e', isError: true);
      return false;
    }
  }
  
  /// 删除金属分离记录
  Future<bool> deleteMetalSeparation(int id) async {
    try {
      final response = await _apiService.post(
        '${AppConfig.apiEndpoint['metalSeparation']}/delete',
        data: {'id': id},
      );
      
      if (response.data['code'] == 1) {
        return true;
      } else {
        Get.log('删除金属分离记录失败: ${response.data['msg']}', isError: true);
        return false;
      }
    } catch (e) {
      Get.log('删除金属分离记录异常: $e', isError: true);
      return false;
    }
  }
  
  /// 计算金属分离损耗率
  double calculateLossRate(double originalWeight, double separatedWeight) {
    if (originalWeight <= 0) return 0;
    return ((originalWeight - separatedWeight) / originalWeight * 100).clamp(0, 100);
  }
  
  /// 计算分离的总价值
  double calculateTotalValue(double goldWeight, double goldPrice, double silverWeight, double silverPrice) {
    return (goldWeight * goldPrice) + (silverWeight * silverPrice);
  }
} 