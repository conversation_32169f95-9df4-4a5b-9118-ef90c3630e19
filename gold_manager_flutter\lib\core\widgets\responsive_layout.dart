import 'package:flutter/material.dart';

/// 响应式布局组件
/// 根据屏幕尺寸自动选择合适的布局
class ScreenTypeLayout extends StatelessWidget {
  /// 移动端布局 (< 600px)
  final Widget mobile;
  
  /// 平板端布局 (600px - 1000px)
  final Widget? tablet;
  
  /// 桌面端布局 (> 1000px)
  final Widget? desktop;

  const ScreenTypeLayout({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final width = constraints.maxWidth;
        
        // 桌面端 (> 1000px)
        if (width > 1000 && desktop != null) {
          return desktop!;
        }
        
        // 平板端 (600px - 1000px)
        if (width > 600 && tablet != null) {
          return tablet!;
        }
        
        // 移动端 (< 600px) 或默认
        return mobile;
      },
    );
  }
}

/// 响应式值选择器
/// 根据屏幕尺寸返回不同的值
class ResponsiveValue<T> {
  final T mobile;
  final T? tablet;
  final T? desktop;

  const ResponsiveValue({
    required this.mobile,
    this.tablet,
    this.desktop,
  });

  /// 根据当前屏幕尺寸获取对应的值
  T getValue(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    
    // 桌面端
    if (width > 1000 && desktop != null) {
      return desktop!;
    }
    
    // 平板端
    if (width > 600 && tablet != null) {
      return tablet!;
    }
    
    // 移动端或默认
    return mobile;
  }
}

/// 屏幕类型枚举
enum ScreenType {
  mobile,
  tablet,
  desktop,
}

/// 响应式工具类
class ResponsiveUtils {
  /// 获取当前屏幕类型
  static ScreenType getScreenType(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    
    if (width > 1000) {
      return ScreenType.desktop;
    } else if (width > 600) {
      return ScreenType.tablet;
    } else {
      return ScreenType.mobile;
    }
  }

  /// 是否为移动端
  static bool isMobile(BuildContext context) {
    return getScreenType(context) == ScreenType.mobile;
  }

  /// 是否为平板端
  static bool isTablet(BuildContext context) {
    return getScreenType(context) == ScreenType.tablet;
  }

  /// 是否为桌面端
  static bool isDesktop(BuildContext context) {
    return getScreenType(context) == ScreenType.desktop;
  }

  /// 获取响应式边距
  static EdgeInsets getResponsivePadding(BuildContext context) {
    final screenType = getScreenType(context);
    
    switch (screenType) {
      case ScreenType.mobile:
        return const EdgeInsets.all(16.0);
      case ScreenType.tablet:
        return const EdgeInsets.all(24.0);
      case ScreenType.desktop:
        return const EdgeInsets.all(32.0);
    }
  }

  /// 获取响应式字体大小
  static double getResponsiveFontSize(BuildContext context, {
    double mobile = 14.0,
    double? tablet,
    double? desktop,
  }) {
    final screenType = getScreenType(context);
    
    switch (screenType) {
      case ScreenType.mobile:
        return mobile;
      case ScreenType.tablet:
        return tablet ?? mobile * 1.1;
      case ScreenType.desktop:
        return desktop ?? mobile * 1.2;
    }
  }

  /// 获取响应式列数
  static int getResponsiveColumns(BuildContext context, {
    int mobile = 1,
    int? tablet,
    int? desktop,
  }) {
    final screenType = getScreenType(context);
    
    switch (screenType) {
      case ScreenType.mobile:
        return mobile;
      case ScreenType.tablet:
        return tablet ?? 2;
      case ScreenType.desktop:
        return desktop ?? 3;
    }
  }

  /// 获取响应式间距
  static double getResponsiveSpacing(BuildContext context, {
    double mobile = 8.0,
    double? tablet,
    double? desktop,
  }) {
    final screenType = getScreenType(context);
    
    switch (screenType) {
      case ScreenType.mobile:
        return mobile;
      case ScreenType.tablet:
        return tablet ?? mobile * 1.5;
      case ScreenType.desktop:
        return desktop ?? mobile * 2.0;
    }
  }
}

/// 响应式容器组件
class ResponsiveContainer extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final double? maxWidth;

  const ResponsiveContainer({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.maxWidth,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      constraints: BoxConstraints(
        maxWidth: maxWidth ?? double.infinity,
      ),
      padding: padding ?? ResponsiveUtils.getResponsivePadding(context),
      margin: margin,
      child: child,
    );
  }
}

/// 响应式网格组件
class ResponsiveGrid extends StatelessWidget {
  final List<Widget> children;
  final int? mobileColumns;
  final int? tabletColumns;
  final int? desktopColumns;
  final double spacing;
  final double runSpacing;

  const ResponsiveGrid({
    super.key,
    required this.children,
    this.mobileColumns,
    this.tabletColumns,
    this.desktopColumns,
    this.spacing = 16.0,
    this.runSpacing = 16.0,
  });

  @override
  Widget build(BuildContext context) {
    final columns = ResponsiveUtils.getResponsiveColumns(
      context,
      mobile: mobileColumns ?? 1,
      tablet: tabletColumns,
      desktop: desktopColumns,
    );

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: columns,
        crossAxisSpacing: spacing,
        mainAxisSpacing: runSpacing,
        childAspectRatio: 1.0,
      ),
      itemCount: children.length,
      itemBuilder: (context, index) => children[index],
    );
  }
}
