"""
回收处理业务服务
处理回收物品的金银分离、翻新加工、熔炼等业务流程
"""

import json
import time
from typing import List, Optional, Tuple
from decimal import Decimal
from datetime import datetime
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, desc

from ..models.recycling_process import RecyclingProcess, RecyclingProcessResult, RecyclingToStock, Material
from ..models.recycling import Recycling, RecyclingItem
from ..models.jewelry import Jewelry, JewelryCategory
from ..models.stock_in import StockIn, StockInItem
from ..models.store import Store
from ..models.admin import Admin
from ..schemas.recycling_process import (
    RecyclingProcessCreate, RecyclingProcessUpdate, ProcessCompleteRequest,
    RecyclingToStockCreate, MaterialCreate, MaterialUpdate,
    RecyclingProcessQueryParams
)
from ..services.stock_in_service import StockInService
from ..services.jewelry_service import JewelryService


class RecyclingProcessService:
    """回收处理服务类"""
    
    def __init__(self, db: Session):
        self.db = db
        self.stock_in_service = StockInService(db)
        self.jewelry_service = JewelryService(db)
    
    def generate_process_no(self) -> str:
        """生成处理单号"""
        now = datetime.now()
        prefix = f"PRC{now.year}{now.month:02d}{now.day:02d}"
        
        # 查询今天已有的处理单数量
        today_start = int(datetime(now.year, now.month, now.day).timestamp())
        today_end = today_start + 86400
        
        count = self.db.query(RecyclingProcess).filter(
            and_(
                RecyclingProcess.createtime >= today_start,
                RecyclingProcess.createtime < today_end
            )
        ).count()
        
        return f"{prefix}{count + 1:04d}"
    
    def create_process(self, process_data: RecyclingProcessCreate, operator_id: int) -> RecyclingProcess:
        """创建处理工单"""
        try:
            # 验证回收单
            recycling = self.db.query(Recycling).filter(
                Recycling.id == process_data.recycling_id
            ).first()
            if not recycling:
                raise ValueError(f"回收单ID {process_data.recycling_id} 不存在")
            
            # 验证回收明细
            items = self.db.query(RecyclingItem).filter(
                RecyclingItem.id.in_(process_data.recycling_item_ids)
            ).all()
            
            if len(items) != len(process_data.recycling_item_ids):
                raise ValueError("部分回收明细不存在")
            
            # 检查明细是否已被处理
            for item in items:
                if item.process_status > 0:
                    raise ValueError(f"明细 {item.name} 已在处理中或已处理")
            
            # 计算总重量和预估重量
            total_weight = sum(Decimal(str(item.gold_weight or 0)) + Decimal(str(item.silver_weight or 0)) for item in items)
            estimated_gold_weight = sum(Decimal(str(item.gold_weight or 0)) for item in items)
            estimated_silver_weight = sum(Decimal(str(item.silver_weight or 0)) for item in items)
            
            current_time = int(time.time())
            
            # 创建处理工单
            process = RecyclingProcess(
                process_no=self.generate_process_no(),
                store_id=process_data.store_id,
                source_store_id=recycling.store_id,
                recycling_id=process_data.recycling_id,
                recycling_item_ids=json.dumps(process_data.recycling_item_ids),
                process_type=process_data.process_type,
                total_weight=total_weight,
                estimated_gold_weight=estimated_gold_weight,
                estimated_silver_weight=estimated_silver_weight,
                status=1,  # 待处理
                operator_id=operator_id,
                remark=process_data.remark,
                createtime=current_time,
                updatetime=current_time
            )
            
            self.db.add(process)
            self.db.flush()
            
            # 更新回收明细状态
            for item in items:
                item.process_status = 1  # 处理中
                item.process_id = process.id
            
            self.db.commit()
            self.db.refresh(process)
            
            return process
            
        except Exception as e:
            self.db.rollback()
            raise e
    
    def get_process_list(self, params: RecyclingProcessQueryParams) -> Tuple[List[RecyclingProcess], int]:
        """获取处理工单列表"""
        query = self.db.query(RecyclingProcess)
        
        # 关联查询
        query = query.options(
            joinedload(RecyclingProcess.store),
            joinedload(RecyclingProcess.source_store),
            joinedload(RecyclingProcess.recycling),
            joinedload(RecyclingProcess.operator),
            joinedload(RecyclingProcess.processor),
            joinedload(RecyclingProcess.results)
        )
        
        # 关键词搜索
        if params.keyword:
            keyword = f"%{params.keyword}%"
            query = query.filter(
                or_(
                    RecyclingProcess.process_no.like(keyword),
                    RecyclingProcess.remark.like(keyword)
                )
            )
        
        # 门店筛选
        if params.store_id:
            query = query.filter(RecyclingProcess.store_id == params.store_id)
        
        # 状态筛选
        if params.status is not None:
            query = query.filter(RecyclingProcess.status == params.status)
        
        # 处理类型筛选
        if params.process_type:
            query = query.filter(RecyclingProcess.process_type == params.process_type)
        
        # 日期范围筛选
        if params.start_date:
            try:
                start_timestamp = int(datetime.strptime(params.start_date, "%Y-%m-%d").timestamp())
                query = query.filter(RecyclingProcess.createtime >= start_timestamp)
            except ValueError:
                pass
        
        if params.end_date:
            try:
                end_timestamp = int(datetime.strptime(params.end_date, "%Y-%m-%d").timestamp()) + 86400
                query = query.filter(RecyclingProcess.createtime < end_timestamp)
            except ValueError:
                pass
        
        # 获取总数
        total = query.count()
        
        # 分页和排序
        query = query.order_by(desc(RecyclingProcess.createtime))
        offset = (params.page - 1) * params.page_size
        processes = query.offset(offset).limit(params.page_size).all()
        
        return processes, total
    
    def get_process_by_id(self, process_id: int) -> Optional[RecyclingProcess]:
        """根据ID获取处理工单详情"""
        return self.db.query(RecyclingProcess).options(
            joinedload(RecyclingProcess.store),
            joinedload(RecyclingProcess.source_store),
            joinedload(RecyclingProcess.recycling).joinedload(Recycling.items),
            joinedload(RecyclingProcess.operator),
            joinedload(RecyclingProcess.processor),
            joinedload(RecyclingProcess.results)
        ).filter(RecyclingProcess.id == process_id).first()
    
    def update_process(self, process_id: int, process_data: RecyclingProcessUpdate) -> Optional[RecyclingProcess]:
        """更新处理工单"""
        try:
            process = self.db.query(RecyclingProcess).filter(
                RecyclingProcess.id == process_id
            ).first()
            
            if not process:
                return None
            
            # 只有待处理状态才能更新
            if process.status != 1:
                raise ValueError("只有待处理状态的工单才能修改")
            
            # 更新字段
            if process_data.store_id is not None:
                process.store_id = process_data.store_id
            
            if process_data.processor_id is not None:
                process.processor_id = process_data.processor_id
            
            if process_data.remark is not None:
                process.remark = process_data.remark
            
            process.updatetime = int(time.time())
            
            self.db.commit()
            self.db.refresh(process)
            
            return process
            
        except Exception as e:
            self.db.rollback()
            raise e
    
    def start_process(self, process_id: int, processor_id: int) -> Optional[RecyclingProcess]:
        """开始处理"""
        try:
            process = self.db.query(RecyclingProcess).filter(
                RecyclingProcess.id == process_id
            ).first()
            
            if not process:
                return None
            
            if process.status != 1:
                raise ValueError("只有待处理状态的工单才能开始处理")
            
            process.status = 2  # 处理中
            process.processor_id = processor_id
            process.start_time = int(time.time())
            process.updatetime = int(time.time())
            
            self.db.commit()
            self.db.refresh(process)
            
            return process
            
        except Exception as e:
            self.db.rollback()
            raise e
    
    def complete_process(self, process_id: int, data: ProcessCompleteRequest) -> Optional[RecyclingProcess]:
        """完成处理"""
        try:
            process = self.db.query(RecyclingProcess).filter(
                RecyclingProcess.id == process_id
            ).first()
            
            if not process:
                return None
            
            if process.status != 2:
                raise ValueError("只有处理中的工单才能完成")
            
            current_time = int(time.time())
            
            # 创建处理结果
            for result_data in data.results:
                # 计算总成本
                total_cost = (
                    Decimal(str(result_data.process_cost)) +
                    Decimal(str(result_data.labor_cost)) +
                    Decimal(str(result_data.other_cost))
                )
                
                result = RecyclingProcessResult(
                    process_id=process_id,
                    result_type=result_data.result_type,
                    name=result_data.name,
                    weight=result_data.weight,
                    purity=result_data.purity,
                    loss_weight=result_data.loss_weight,
                    loss_rate=result_data.loss_rate,
                    process_cost=result_data.process_cost,
                    labor_cost=result_data.labor_cost,
                    other_cost=result_data.other_cost,
                    total_cost=total_cost,
                    createtime=current_time
                )
                self.db.add(result)
            
            # 更新工单状态
            process.status = 3  # 已完成
            process.end_time = current_time
            process.updatetime = current_time
            
            if data.processor_id:
                process.processor_id = data.processor_id
            
            # 更新回收明细状态
            item_ids = json.loads(process.recycling_item_ids)
            self.db.query(RecyclingItem).filter(
                RecyclingItem.id.in_(item_ids)
            ).update(
                {"process_status": 2},  # 已处理
                synchronize_session=False
            )
            
            self.db.commit()
            self.db.refresh(process)
            
            return process
            
        except Exception as e:
            self.db.rollback()
            raise e
    
    def cancel_process(self, process_id: int) -> bool:
        """取消处理工单"""
        try:
            process = self.db.query(RecyclingProcess).filter(
                RecyclingProcess.id == process_id
            ).first()
            
            if not process:
                return False
            
            if process.status == 3:
                raise ValueError("已完成的工单不能取消")
            
            # 恢复回收明细状态
            if process.recycling_item_ids:
                item_ids = json.loads(process.recycling_item_ids)
                self.db.query(RecyclingItem).filter(
                    RecyclingItem.id.in_(item_ids)
                ).update(
                    {"process_status": 0, "process_id": None},
                    synchronize_session=False
                )
            
            process.status = 0  # 已取消
            process.updatetime = int(time.time())
            
            self.db.commit()
            
            return True
            
        except Exception as e:
            self.db.rollback()
            raise e
    
    def convert_to_stock(self, convert_data: RecyclingToStockCreate, operator_id: int) -> RecyclingToStock:
        """将处理结果转为库存"""
        try:
            # 验证处理结果
            result = self.db.query(RecyclingProcessResult).filter(
                RecyclingProcessResult.id == convert_data.process_result_id
            ).first()
            
            if not result:
                raise ValueError("处理结果不存在")
            
            if result.is_converted:
                raise ValueError("该处理结果已转换为库存")
            
            current_time = int(time.time())
            convert_no = self.generate_convert_no()
            
            # 计算成本
            process = result.process
            recycling = process.recycling
            
            # 基础成本：回收成本按重量比例分摊
            weight_ratio = Decimal(str(result.weight)) / Decimal(str(process.total_weight))
            recycling_cost = Decimal(str(recycling.total_amount)) * weight_ratio
            
            # 加上处理成本
            unit_cost = (recycling_cost + Decimal(str(result.total_cost))) / Decimal(str(result.weight))
            total_cost = unit_cost * Decimal(str(result.weight)) * convert_data.quantity
            
            # 成本明细
            cost_detail = {
                "recycling_cost": float(recycling_cost),
                "process_cost": float(result.process_cost),
                "labor_cost": float(result.labor_cost),
                "other_cost": float(result.other_cost),
                "total_cost": float(total_cost)
            }
            
            # 创建转换记录
            conversion = RecyclingToStock(
                convert_no=convert_no,
                store_id=convert_data.store_id,
                process_result_id=convert_data.process_result_id,
                stock_type=convert_data.stock_type,
                quantity=convert_data.quantity,
                weight=result.weight * convert_data.quantity,
                unit_cost=unit_cost,
                total_cost=total_cost,
                cost_detail=json.dumps(cost_detail),
                status=1,
                operator_id=operator_id,
                remark=convert_data.remark,
                createtime=current_time
            )
            
            self.db.add(conversion)
            self.db.flush()
            
            # 根据库存类型处理
            if convert_data.stock_type == 1:  # 原料库存
                # 创建或更新原料
                material = self._create_or_update_material(
                    result, convert_data.material_type, convert_data.store_id, unit_cost
                )
                conversion.material_id = material.id
                
            elif convert_data.stock_type == 2:  # 商品库存
                # 创建商品和入库单
                stock_in, jewelry = self._create_jewelry_stock_in(
                    result, convert_data.jewelry_info, convert_data.store_id, 
                    unit_cost, operator_id
                )
                conversion.stock_in_id = stock_in.id
                conversion.jewelry_id = jewelry.id
            
            # 更新处理结果状态
            result.is_converted = True
            result.convert_time = current_time
            result.target_type = convert_data.stock_type
            result.target_id = conversion.id
            
            # 更新回收明细状态
            item_ids = json.loads(process.recycling_item_ids)
            self.db.query(RecyclingItem).filter(
                RecyclingItem.id.in_(item_ids)
            ).update(
                {"process_status": 3},  # 已入库
                synchronize_session=False
            )
            
            self.db.commit()
            self.db.refresh(conversion)
            
            return conversion
            
        except Exception as e:
            self.db.rollback()
            raise e
    
    def _create_or_update_material(self, result: RecyclingProcessResult, material_type: int, 
                                  store_id: int, unit_cost: Decimal) -> Material:
        """创建或更新原料"""
        # 查找已有原料
        material = self.db.query(Material).filter(
            and_(
                Material.type == material_type,
                Material.store_id == store_id,
                Material.purity == result.purity,
                Material.status == 1
            )
        ).first()
        
        current_time = int(time.time())
        
        if material:
            # 更新库存和成本
            old_stock = Decimal(str(material.current_stock))
            old_cost = Decimal(str(material.unit_cost))
            new_stock = old_stock + Decimal(str(result.weight))
            
            # 加权平均成本
            material.unit_cost = (old_stock * old_cost + Decimal(str(result.weight)) * unit_cost) / new_stock
            material.current_stock = new_stock
            material.updatetime = current_time
        else:
            # 创建新原料
            material_no = self.generate_material_no()
            type_names = {1: "黄金", 2: "白银", 3: "其他"}
            
            material = Material(
                material_no=material_no,
                name=f"{type_names.get(material_type, '其他')}-{result.purity or 99.9}%",
                type=material_type,
                purity=result.purity,
                unit="克",
                store_id=store_id,
                current_stock=result.weight,
                unit_cost=unit_cost,
                status=1,
                createtime=current_time,
                updatetime=current_time
            )
            self.db.add(material)
            self.db.flush()
        
        return material
    
    def _create_jewelry_stock_in(self, result: RecyclingProcessResult, jewelry_info: dict,
                               store_id: int, unit_cost: Decimal, operator_id: int) -> Tuple[StockIn, Jewelry]:
        """创建商品和入库单"""
        # 创建商品
        jewelry_data = {
            "barcode": self.jewelry_service.generate_barcode(),
            "name": jewelry_info.get("name", result.name),
            "category_id": jewelry_info.get("category_id", 1),
            "store_id": store_id,
            "gold_weight": result.weight if result.result_type == 1 else 0,
            "silver_weight": result.weight if result.result_type == 2 else 0,
            "total_weight": result.weight,
            "cost_price": unit_cost,
            "gold_price": jewelry_info.get("gold_price", 0),
            "work_price": jewelry_info.get("work_price", 0),
            "ring_size": jewelry_info.get("ring_size"),
            "remark": f"回收处理转入：{result.process.process_no}",
            "status": 1  # 上架
        }
        
        jewelry = self.jewelry_service.create_jewelry(jewelry_data)
        
        # 创建入库单
        stock_in_data = {
            "store_id": store_id,
            "stock_type": "recycling",  # 回收转入
            "remark": f"回收处理转入：{result.process.process_no}",
            "items": [{
                "jewelry_id": jewelry.id,
                "cost_price": unit_cost,
                "sale_price": jewelry_info.get("sale_price", unit_cost * Decimal("1.5"))
            }]
        }
        
        stock_in = self.stock_in_service.create_stock_in(stock_in_data, operator_id)
        
        return stock_in, jewelry
    
    def generate_convert_no(self) -> str:
        """生成转换单号"""
        now = datetime.now()
        prefix = f"CVT{now.year}{now.month:02d}{now.day:02d}"
        
        today_start = int(datetime(now.year, now.month, now.day).timestamp())
        today_end = today_start + 86400
        
        count = self.db.query(RecyclingToStock).filter(
            and_(
                RecyclingToStock.createtime >= today_start,
                RecyclingToStock.createtime < today_end
            )
        ).count()
        
        return f"{prefix}{count + 1:04d}"
    
    def generate_material_no(self) -> str:
        """生成原料编号"""
        now = datetime.now()
        prefix = f"MAT{now.year}{now.month:02d}{now.day:02d}"
        
        today_start = int(datetime(now.year, now.month, now.day).timestamp())
        today_end = today_start + 86400
        
        count = self.db.query(Material).filter(
            and_(
                Material.createtime >= today_start,
                Material.createtime < today_end
            )
        ).count()
        
        return f"{prefix}{count + 1:04d}"