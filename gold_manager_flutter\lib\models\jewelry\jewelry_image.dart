
/// 首饰图片模型
class JewelryImage {
  /// 图片ID
  final int id;
  
  /// 首饰ID
  final int jewelryId;
  
  /// 图片URL
  final String url;
  
  /// 缩略图URL
  final String? thumbnailUrl;
  
  /// 是否为主图
  final bool isMain;
  
  /// 排序顺序
  final int sortOrder;
  
  /// 构造函数
  const JewelryImage({
    required this.id,
    required this.jewelryId,
    required this.url,
    this.thumbnailUrl,
    this.isMain = false,
    this.sortOrder = 0,
  });
  
  /// 从JSON构造
  factory JewelryImage.fromJson(Map<String, dynamic> json) {
    return JewelryImage(
      id: json['id'],
      jewelryId: json['jewelry_id'],
      url: json['url'],
      thumbnailUrl: json['thumbnail_url'],
      isMain: json['is_main'] ?? false,
      sortOrder: json['sort_order'] ?? 0,
    );
  }
  
  /// 转为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'jewelry_id': jewelryId,
      'url': url,
      'thumbnail_url': thumbnailUrl,
      'is_main': isMain,
      'sort_order': sortOrder,
    };
  }
  
  /// 复制并修改属性
  JewelryImage copyWith({
    int? id,
    int? jewelryId,
    String? url,
    String? thumbnailUrl,
    bool? isMain,
    int? sortOrder,
  }) {
    return JewelryImage(
      id: id ?? this.id,
      jewelryId: jewelryId ?? this.jewelryId,
      url: url ?? this.url,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      isMain: isMain ?? this.isMain,
      sortOrder: sortOrder ?? this.sortOrder,
    );
  }
} 