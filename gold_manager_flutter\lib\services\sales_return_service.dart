import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:gold_manager_flutter/core/services/api_service.dart';
import 'package:gold_manager_flutter/models/sales/sales_return_model.dart';

/// 销售退货服务
class SalesReturnService extends GetxService {
  final ApiService _apiService = Get.find<ApiService>();

  /// 获取退货单列表
  Future<List<SalesReturn>> getSalesReturnList({
    int page = 1,
    int limit = 20,
    Map<String, dynamic>? filters,
  }) async {
    try {
      final queryParams = <String, dynamic>{'page': page, 'limit': limit};

      if (filters != null) {
        queryParams.addAll(filters);
      }

      final response = await _apiService.get(
        '/api/v1/stock-return/',
        queryParameters: queryParams,
      );

      if (response.statusCode == 200 && response.data['success'] == true) {
        final List<dynamic> data = response.data['data'];
        return data.map((json) => SalesReturn.fromJson(json)).toList();
      } else {
        throw Exception(response.data['message'] ?? '获取退货单列表失败');
      }
    } catch (e) {
      if (e is DioException) {
        throw Exception('网络错误：${e.message}');
      }
      rethrow;
    }
  }

  /// 获取退货单详情
  Future<SalesReturn> getSalesReturnDetail(int id) async {
    try {
      final response = await _apiService.get('/api/v1/stock-return/$id');

      if (response.statusCode == 200 && response.data['success'] == true) {
        final dynamic data = response.data['data'];
        return SalesReturn.fromJson(data);
      } else {
        throw Exception(response.data['message'] ?? '获取退货单详情失败');
      }
    } catch (e) {
      if (e is DioException) {
        throw Exception('网络错误：${e.message}');
      }
      rethrow;
    }
  }

  /// 创建退货单
  Future<SalesReturn> createSalesReturn(SalesReturn salesReturn) async {
    try {
      final response = await _apiService.post(
        '/api/v1/stock-return/',
        data: salesReturn.toJson(),
      );

      if (response.statusCode == 200 && response.data['success'] == true) {
        final dynamic data = response.data['data'];
        return SalesReturn.fromJson(data);
      } else {
        throw Exception(response.data['message'] ?? '创建退货单失败');
      }
    } catch (e) {
      if (e is DioException) {
        throw Exception('网络错误：${e.message}');
      }
      rethrow;
    }
  }

  /// 更新退货单
  Future<SalesReturn> updateSalesReturn(SalesReturn salesReturn) async {
    try {
      final response = await _apiService.put(
        '/api/v1/stock-return/${salesReturn.id}',
        data: salesReturn.toJson(),
      );

      if (response.statusCode == 200 && response.data['success'] == true) {
        final dynamic data = response.data['data'];
        return SalesReturn.fromJson(data);
      } else {
        throw Exception(response.data['message'] ?? '更新退货单失败');
      }
    } catch (e) {
      if (e is DioException) {
        throw Exception('网络错误：${e.message}');
      }
      rethrow;
    }
  }

  /// 审核退货单
  Future<bool> approveSalesReturn(int id, String remark) async {
    try {
      final response = await _apiService.patch(
        '/api/v1/stock-return/$id/audit',
        data: {
          'status': 2, // 2表示已通过
          'audit_remark': remark,
        },
      );

      if (response.statusCode == 200 && response.data['success'] == true) {
        return true;
      } else {
        throw Exception(response.data['message'] ?? '审核退货单失败');
      }
    } catch (e) {
      if (e is DioException) {
        throw Exception('网络错误：${e.message}');
      }
      rethrow;
    }
  }

  /// 取消退货单
  Future<bool> cancelSalesReturn(int id, String remark) async {
    try {
      final response = await _apiService.patch(
        '/api/v1/stock-return/$id/audit',
        data: {
          'status': 4, // 4表示已作废
          'audit_remark': remark,
        },
      );

      if (response.statusCode == 200 && response.data['success'] == true) {
        return true;
      } else {
        throw Exception(response.data['message'] ?? '取消退货单失败');
      }
    } catch (e) {
      if (e is DioException) {
        throw Exception('网络错误：${e.message}');
      }
      rethrow;
    }
  }

  /// 删除退货单
  Future<bool> deleteSalesReturn(int id) async {
    try {
      final response = await _apiService.delete('/api/v1/stock-return/$id');

      if (response.statusCode == 200 && response.data['message'] != null) {
        return true;
      } else {
        throw Exception(response.data['message'] ?? '删除退货单失败');
      }
    } catch (e) {
      if (e is DioException) {
        throw Exception('网络错误：${e.message}');
      }
      rethrow;
    }
  }
}
