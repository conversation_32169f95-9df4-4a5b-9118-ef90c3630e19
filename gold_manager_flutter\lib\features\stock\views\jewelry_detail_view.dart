import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../core/constants/border_styles.dart';
import '../../../models/jewelry/jewelry.dart';
import '../../../models/common/enums.dart';
import '../../../widgets/responsive_builder.dart';
import '../../../services/auth_service.dart';
import '../../../services/jewelry_service.dart';
import 'jewelry_edit_dialog.dart';

/// 商品详情查看弹窗
class JewelryDetailDialog extends StatelessWidget {
  final Jewelry jewelry;
  final VoidCallback? onUpdated;

  const JewelryDetailDialog({
    super.key,
    required this.jewelry,
    this.onUpdated,
  });

  /// 显示商品详情弹窗
  static Future<void> show(BuildContext context, Jewelry jewelry, {VoidCallback? onUpdated}) {
    return showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => JewelryDetailDialog(
        jewelry: jewelry,
        onUpdated: onUpdated,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: EdgeInsets.symmetric(
        horizontal: MediaQuery.of(context).size.width > 800 ? 80 : 16,
        vertical: 20,
      ),
      child: Container(
        constraints: const BoxConstraints(
          maxWidth: 1200,
          maxHeight: 700,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(AppBorderStyles.largeBorderRadius),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 40,
              offset: const Offset(0, 20),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildModernHeader(context),
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.fromLTRB(24, 16, 24, 16),
                child: ScreenTypeLayout(
                  mobile: _buildMobileLayout(),
                  tablet: _buildDesktopLayout(),
                  desktop: _buildDesktopLayout(),
                ),
              ),
            ),
            _buildModernFooter(context),
          ],
        ),
      ),
    );
  }

  /// 构建现代化弹窗头部
  Widget _buildModernHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.fromLTRB(24, 20, 24, 16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.blue[50] ?? Colors.blue.shade50,
            Colors.white,
          ],
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(AppBorderStyles.largeBorderRadius),
          topRight: Radius.circular(AppBorderStyles.largeBorderRadius),
        ),
        border: Border(
          bottom: BorderSide(
            color: Colors.grey[200] ?? Colors.grey.shade200,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // 主标题区域
          Expanded(
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.blue[600] ?? Colors.blue.shade600,
                        Colors.blue[700] ?? Colors.blue.shade700,
                      ],
                    ),
                    borderRadius: BorderRadius.circular(AppBorderStyles.mediumBorderRadius),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.blue.withValues(alpha: 0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.diamond,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '商品详情',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.w700,
                          color: Colors.black87,
                          letterSpacing: -0.5,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        jewelry.name,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Colors.grey[600],
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          // 状态和操作区域
          Row(
            children: [
              _buildModernStatusBadge(jewelry.status),
              const SizedBox(width: 16),
              Container(
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(AppBorderStyles.mediumBorderRadius),
                ),
                child: IconButton(
                  icon: const Icon(Icons.close_rounded),
                  onPressed: () => Navigator.of(context).pop(),
                  tooltip: '关闭',
                  style: IconButton.styleFrom(
                    foregroundColor: Colors.grey[600],
                    padding: const EdgeInsets.all(12),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建现代化弹窗底部操作按钮
  Widget _buildModernFooter(BuildContext context) {
    final authService = Get.find<AuthService>();

    return Container(
      padding: const EdgeInsets.fromLTRB(24, 16, 24, 20),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(AppBorderStyles.largeBorderRadius),
          bottomRight: Radius.circular(AppBorderStyles.largeBorderRadius),
        ),
        border: Border(
          top: BorderSide(
            color: Colors.grey[200] ?? Colors.grey.shade200,
            width: 1,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 左侧信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '条码: ${jewelry.barcode}',
                  style: TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  '创建时间: ${_formatDateTime(jewelry.createTime)}',
                  style: TextStyle(
                    fontSize: 11,
                    color: Colors.grey[500],
                  ),
                ),
              ],
            ),
          ),
          // 右侧操作按钮
          Row(
            children: [
              // 编辑按钮
              if (authService.hasPermission('jewelry.edit'))
                Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.blue[500] ?? Colors.blue.shade500,
                        Colors.blue[600] ?? Colors.blue.shade600,
                      ],
                    ),
                    borderRadius: BorderRadius.circular(AppBorderStyles.mediumBorderRadius),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.blue.withValues(alpha: 0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: ElevatedButton.icon(
                    icon: const Icon(Icons.edit_rounded, size: 18),
                    label: const Text('编辑'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.transparent,
                      foregroundColor: Colors.white,
                      shadowColor: Colors.transparent,
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 14),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(AppBorderStyles.mediumBorderRadius),
                      ),
                    ),
                    onPressed: () => _editJewelry(),
                  ),
                ),

              if (authService.hasPermission('jewelry.edit')) const SizedBox(width: 12),

              // 删除按钮
              if (authService.hasPermission('jewelry.delete'))
                Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.red[500] ?? Colors.red.shade500,
                        Colors.red[600] ?? Colors.red.shade600,
                      ],
                    ),
                    borderRadius: BorderRadius.circular(AppBorderStyles.mediumBorderRadius),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.red.withValues(alpha: 0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: ElevatedButton.icon(
                    icon: const Icon(Icons.delete_rounded, size: 18),
                    label: const Text('删除'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.transparent,
                      foregroundColor: Colors.white,
                      shadowColor: Colors.transparent,
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 14),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(AppBorderStyles.mediumBorderRadius),
                      ),
                    ),
                    onPressed: () => _deleteJewelry(),
                  ),
                ),

              if (authService.hasPermission('jewelry.delete')) const SizedBox(width: 12),

              // 关闭按钮
              OutlinedButton.icon(
                icon: const Icon(Icons.close_rounded, size: 18),
                label: const Text('关闭'),
                onPressed: () => Navigator.of(context).pop(),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 14),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppBorderStyles.mediumBorderRadius),
                  ),
                  side: BorderSide(color: Colors.grey[300] ?? Colors.grey.shade300),
                  foregroundColor: Colors.grey[700],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }



  /// 构建移动端布局
  Widget _buildMobileLayout() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildModernBasicInfoCard(),
        const SizedBox(height: 16),
        _buildStoreInfoCard(),
        const SizedBox(height: 16),
        _buildModernWeightInfoCard(),
        const SizedBox(height: 16),
        _buildModernCostInfoCard(),
      ],
    );
  }

  /// 构建桌面端布局
  Widget _buildDesktopLayout() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 第一行：基本信息和重量信息
        IntrinsicHeight(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Expanded(
                flex: 3,
                child: Column(
                  children: [
                    _buildModernBasicInfoCard(),
                    const SizedBox(height: 16),
                    SizedBox(
                      height: 140,
                      child: _buildStoreInfoCard(),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                flex: 2,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Expanded(
                      flex: 3,
                      child: _buildModernWeightInfoCard(),
                    ),
                    const SizedBox(height: 16),
                    SizedBox(
                      height: 140,
                      child: _buildModernCostInfoCard(),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建现代化基本信息卡片
  Widget _buildModernBasicInfoCard() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white,
            Colors.blue[50] ?? Colors.blue.shade50,
          ],
        ),
        borderRadius: BorderRadius.circular(AppBorderStyles.largeBorderRadius),
        border: Border.all(
          color: Colors.blue[100] ?? Colors.blue.shade100,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withValues(alpha: 0.08),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 卡片标题
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.blue[500] ?? Colors.blue.shade500,
                        Colors.blue[600] ?? Colors.blue.shade600,
                      ],
                    ),
                    borderRadius: BorderRadius.circular(AppBorderStyles.mediumBorderRadius),
                  ),
                  child: const Icon(
                    Icons.info_rounded,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 10),
                const Text(
                  '基本信息',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                    color: Colors.black87,
                    letterSpacing: -0.3,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // 商品名称行 - 包含名称、条码、分类、创建时间、圈口号
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(AppBorderStyles.mediumBorderRadius),
                border: Border.all(
                  color: Colors.blue[200] ?? Colors.blue.shade200,
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 商品名称（带图标）
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              Colors.amber[500] ?? Colors.amber.shade500,
                              Colors.amber[600] ?? Colors.amber.shade600,
                            ],
                          ),
                          borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
                        ),
                        child: const Icon(
                          Icons.inventory_2_rounded,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '商品名称',
                              style: TextStyle(
                                fontSize: 13,
                                fontWeight: FontWeight.w500,
                                color: Colors.grey[600],
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              jewelry.name,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Colors.black87,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  // 条码和分类同行显示
                  Row(
                    children: [
                      Expanded(
                        child: _buildCompactInfoItem('商品条码', jewelry.barcode, Icons.qr_code_rounded, Colors.purple),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildCompactInfoItem('商品分类', jewelry.category?.name ?? '未知', Icons.category_rounded, Colors.orange),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  // 创建时间和圈口号同行显示
                  Row(
                    children: [
                      Expanded(
                        child: _buildCompactInfoItem('创建时间', _formatDateTime(jewelry.createTime), Icons.schedule_rounded, Colors.grey),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildCompactInfoItem('圈口号', jewelry.ringSize ?? '-', Icons.radio_button_unchecked_rounded, Colors.green),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建现代化重量信息卡片
  Widget _buildModernWeightInfoCard() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white,
            Colors.amber[50] ?? Colors.amber.shade50,
          ],
        ),
        borderRadius: BorderRadius.circular(AppBorderStyles.largeBorderRadius),
        border: Border.all(
          color: Colors.amber[100] ?? Colors.amber.shade100,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.amber.withValues(alpha: 0.08),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 卡片标题
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.amber[500] ?? Colors.amber.shade500,
                        Colors.amber[600] ?? Colors.amber.shade600,
                      ],
                    ),
                    borderRadius: BorderRadius.circular(AppBorderStyles.mediumBorderRadius),
                  ),
                  child: const Icon(
                    Icons.scale_rounded,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 10),
                const Text(
                  '重量信息',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                    color: Colors.black87,
                    letterSpacing: -0.3,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // 重量数据网格 - 使用Expanded确保充分利用空间
            Expanded(
              child: Column(
                children: [
                  // 金重与金价同行
                  Expanded(
                    child: _buildWeightWithPriceCard('金重', jewelry.goldWeight.toStringAsFixed(2), 'g', '金价', jewelry.goldPrice.toStringAsFixed(2), Colors.amber, Icons.monetization_on_rounded),
                  ),
                  const SizedBox(height: 12),
                  // 银重与银价同行
                  Expanded(
                    child: _buildWeightWithPriceCard('银重', jewelry.silverWeight.toStringAsFixed(2), 'g', '银价', jewelry.silverPrice.toStringAsFixed(2), Colors.grey, Icons.circle_rounded),
                  ),
                  const SizedBox(height: 12),
                  // 总重单独显示
                  Expanded(
                    child: _buildWeightDataCard('总重', jewelry.totalWeight.toStringAsFixed(2), 'g', Colors.blue, Icons.fitness_center_rounded),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建现代化费用信息卡片
  Widget _buildModernCostInfoCard() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white,
            Colors.green[50] ?? Colors.green.shade50,
          ],
        ),
        borderRadius: BorderRadius.circular(AppBorderStyles.largeBorderRadius),
        border: Border.all(
          color: Colors.green[100] ?? Colors.green.shade100,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.green.withValues(alpha: 0.08),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 卡片标题
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.green[500] ?? Colors.green.shade500,
                        Colors.green[600] ?? Colors.green.shade600,
                      ],
                    ),
                    borderRadius: BorderRadius.circular(AppBorderStyles.mediumBorderRadius),
                  ),
                  child: const Icon(
                    Icons.attach_money_rounded,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
                const SizedBox(width: 8),
                const Text(
                  '费用信息',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w700,
                    color: Colors.black87,
                    letterSpacing: -0.3,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            // 费用信息行 - 使用Expanded确保充分利用空间
            Expanded(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Expanded(
                    child: _buildPriceItem('工费', jewelry.workPrice.toStringAsFixed(2), Colors.orange),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildPriceItem('件工费', jewelry.pieceWorkPrice.toStringAsFixed(2), Colors.purple),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildPriceItem('总成本', jewelry.totalCost.toStringAsFixed(2), Colors.green),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建门店信息卡片（简化版）
  Widget _buildStoreInfoCard() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white,
            Colors.purple[50] ?? Colors.purple.shade50,
          ],
        ),
        borderRadius: BorderRadius.circular(AppBorderStyles.largeBorderRadius),
        border: Border.all(
          color: Colors.purple[100] ?? Colors.purple.shade100,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.purple.withValues(alpha: 0.08),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 卡片标题
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.purple[500] ?? Colors.purple.shade500,
                        Colors.purple[600] ?? Colors.purple.shade600,
                      ],
                    ),
                    borderRadius: BorderRadius.circular(AppBorderStyles.mediumBorderRadius),
                  ),
                  child: const Icon(
                    Icons.store_rounded,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
                const SizedBox(width: 8),
                const Text(
                  '门店信息',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w700,
                    color: Colors.black87,
                    letterSpacing: -0.3,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            // 所属门店信息
            Expanded(
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(AppBorderStyles.mediumBorderRadius),
                  border: Border.all(
                    color: Colors.purple[200] ?? Colors.purple.shade200,
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(6),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            Colors.purple[400] ?? Colors.purple.shade400,
                            Colors.purple[500] ?? Colors.purple.shade500,
                          ],
                        ),
                        borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
                      ),
                      child: const Icon(
                        Icons.business_rounded,
                        color: Colors.white,
                        size: 14,
                      ),
                    ),
                    const SizedBox(width: 10),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            '所属门店',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                              color: Colors.grey[600],
                            ),
                          ),
                          const SizedBox(height: 2),
                          Text(
                            jewelry.store?.name ?? '未知',
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: Colors.black87,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }



  /// 构建现代化状态徽章
  Widget _buildModernStatusBadge(JewelryStatus status) {
    Color statusColor = _getStatusColor(status);
    String statusText = status.label;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            statusColor.withValues(alpha: 0.1),
            statusColor.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(AppBorderStyles.largeBorderRadius),
        border: Border.all(color: statusColor.withValues(alpha: 0.3), width: 1),
        boxShadow: [
          BoxShadow(
            color: statusColor.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: statusColor,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            statusText,
            style: TextStyle(
              color: statusColor,
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }



  /// 构建重量数据卡片
  Widget _buildWeightDataCard(String label, String value, String unit, MaterialColor color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white,
            color[50] ?? color.shade50,
          ],
        ),
        borderRadius: BorderRadius.circular(AppBorderStyles.mediumBorderRadius),
        border: Border.all(
          color: color[100] ?? color.shade100,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  color[400] ?? color.shade400,
                  color[500] ?? color.shade500,
                ],
              ),
              borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
            ),
            child: Icon(
              icon,
              color: Colors.white,
              size: 18,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 3),
                RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text: value,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w700,
                          color: color[700] ?? color.shade700,
                        ),
                      ),
                      TextSpan(
                        text: ' $unit',
                        style: TextStyle(
                          fontSize: 13,
                          fontWeight: FontWeight.w500,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建价格项目
  Widget _buildPriceItem(String label, String value, MaterialColor color) {
    return Container(
      height: double.infinity,
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white,
            color[50] ?? color.shade50,
          ],
        ),
        borderRadius: BorderRadius.circular(AppBorderStyles.mediumBorderRadius),
        border: Border.all(
          color: color[200] ?? color.shade200,
          width: 1,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      color[400] ?? color.shade400,
                      color[500] ?? color.shade500,
                    ],
                  ),
                  borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
                ),
                child: const Icon(
                  Icons.attach_money_rounded,
                  color: Colors.white,
                  size: 12,
                ),
              ),
              const SizedBox(width: 6),
              Expanded(
                child: Text(
                  label,
                  style: TextStyle(
                    fontSize: 11,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey[600],
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),
          Text(
            '¥$value',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w700,
              color: color[700] ?? color.shade700,
            ),
          ),
        ],
      ),
    );
  }



  /// 构建紧凑型信息项目（用于同行显示）
  Widget _buildCompactInfoItem(String label, String value, IconData icon, MaterialColor color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
        border: Border.all(
          color: color[100] ?? color.shade100,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: color[50] ?? color.shade50,
              borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
            ),
            child: Icon(
              icon,
              color: color[600] ?? color.shade600,
              size: 14,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 11,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建重量与价格组合卡片（同行显示）
  Widget _buildWeightWithPriceCard(String weightLabel, String weightValue, String weightUnit,
                                   String priceLabel, String priceValue, MaterialColor color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white,
            color[50] ?? color.shade50,
          ],
        ),
        borderRadius: BorderRadius.circular(AppBorderStyles.mediumBorderRadius),
        border: Border.all(
          color: color[100] ?? color.shade100,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // 重量信息
          Expanded(
            flex: 3,
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        color[400] ?? color.shade400,
                        color[500] ?? color.shade500,
                      ],
                    ),
                    borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
                  ),
                  child: Icon(
                    icon,
                    color: Colors.white,
                    size: 18,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        weightLabel,
                        style: TextStyle(
                          fontSize: 13,
                          fontWeight: FontWeight.w500,
                          color: Colors.grey[600],
                        ),
                      ),
                      const SizedBox(height: 3),
                      RichText(
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: weightValue,
                              style: TextStyle(
                                fontSize: 15,
                                fontWeight: FontWeight.w700,
                                color: color[700] ?? color.shade700,
                              ),
                            ),
                            TextSpan(
                              text: ' $weightUnit',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          // 分隔线
          Container(
            width: 1,
            height: 40,
            color: color[200] ?? color.shade200,
            margin: const EdgeInsets.symmetric(horizontal: 12),
          ),
          // 价格信息
          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  priceLabel,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '¥$priceValue',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: color[700] ?? color.shade700,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 获取状态颜色
  Color _getStatusColor(JewelryStatus status) {
    switch (status) {
      case JewelryStatus.onShelf:
        return Colors.green;
      case JewelryStatus.offShelf:
        return Colors.grey;
      case JewelryStatus.pendingOut:
        return Colors.orange;
    }
  }

  /// 格式化日期时间
  String _formatDateTime(DateTime? dateTime) {
    if (dateTime == null) return '-';
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
           '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// 编辑商品
  void _editJewelry() {
    // 关闭当前详情弹窗
    Get.back();
    // 显示编辑弹窗
    JewelryEditDialog.show(
      Get.context!,
      jewelry,
      onUpdated: onUpdated,
    );
  }

  /// 删除商品
  void _deleteJewelry() {
    Get.dialog(
      AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
        ),
        title: Row(
          children: [
            Icon(Icons.warning, color: Colors.red[600], size: 24),
            const SizedBox(width: 8),
            const Text('确认删除'),
          ],
        ),
        content: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.red[50],
            borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
            border: Border.all(color: Colors.red[200] ?? Colors.red.shade200, width: 1),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('确定要删除以下商品吗？此操作不可撤销。'),
              const SizedBox(height: 8),
              Text(
                '商品名称：${jewelry.name}',
                style: const TextStyle(fontWeight: FontWeight.w500),
              ),
              Text(
                '商品条码：${jewelry.barcode}',
                style: const TextStyle(fontWeight: FontWeight.w500),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            style: TextButton.styleFrom(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
              ),
            ),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => _confirmDelete(),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
              ),
            ),
            child: const Text('确认删除'),
          ),
        ],
      ),
    );
  }

  /// 确认删除商品
  void _confirmDelete() async {
    Get.back(); // 关闭确认对话框

    try {
      // 显示加载提示
      Get.dialog(
        const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('正在删除...'),
            ],
          ),
        ),
        barrierDismissible: false,
      );

      // 调用删除API
      final jewelryService = Get.find<JewelryService>();
      await jewelryService.deleteJewelry(jewelry.id);

      Get.back(); // 关闭加载对话框
      Get.back(); // 关闭详情弹窗

      Get.snackbar(
        '成功',
        '商品删除成功',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
        icon: const Icon(Icons.check_circle, color: Colors.white),
      );

      // 调用回调刷新列表
      onUpdated?.call();
    } catch (e) {
      Get.back(); // 关闭加载对话框

      Get.snackbar(
        '错误',
        '删除失败：${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        icon: const Icon(Icons.error, color: Colors.white),
      );
    }
  }
}
