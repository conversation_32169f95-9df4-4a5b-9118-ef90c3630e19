"""
API v1 主路由
包含所有模块的路由
"""

from fastapi import APIRouter
from .endpoints import (
    jewelry,
    store,
    admin,
    member,
    auth,
    dashboard,
    stock_in,
    stock_out,
    stock_return,
    inventory_check,
    recycling,
    store_transfer,
    data_export,
    recycling_process,
    sales
)

# 创建API路由器
api_router = APIRouter()

# 包含各个模块的路由
api_router.include_router(auth.router, prefix="/auth", tags=["认证"])
api_router.include_router(dashboard.router, prefix="/dashboard", tags=["仪表板"])
api_router.include_router(jewelry.router, prefix="/jewelry", tags=["商品管理"])
api_router.include_router(store.router, prefix="/store", tags=["门店管理"])
api_router.include_router(admin.router, prefix="/admin", tags=["管理员"])
api_router.include_router(member.router, prefix="/member", tags=["会员管理"])
api_router.include_router(stock_in.router, prefix="/stock-in", tags=["库存管理"])
api_router.include_router(stock_out.router, prefix="/stock-out", tags=["库存管理"])
api_router.include_router(stock_return.router, prefix="/stock-return", tags=["库存管理"])
api_router.include_router(inventory_check.router, prefix="/inventory-check", tags=["库存管理"])
api_router.include_router(recycling.router, prefix="/recycling", tags=["库存管理"])
api_router.include_router(recycling_process.router, prefix="/recycling", tags=["回收处理"])
api_router.include_router(store_transfer.router, prefix="/store-transfer", tags=["库存管理"])
api_router.include_router(data_export.router, prefix="/export", tags=["数据导入导出"])
api_router.include_router(sales.router, prefix="/sales", tags=["销售管理"])