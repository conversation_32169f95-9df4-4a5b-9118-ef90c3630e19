"""
管理员管理业务逻辑服务
提供管理员相关的数据操作和业务逻辑
"""

import time
import math
import hashlib
import random
import string
from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc
from loguru import logger
from datetime import datetime, timedelta

from ..models.admin import Admin
from ..models.store import Store
from ..schemas.admin import (
    AdminCreate, 
    AdminUpdate, 
    AdminResponse, 
    AdminListResponse,
    AdminStatistics,
    AdminPasswordUpdate,
    AdminLoginInfo
)


class AdminService:
    """管理员管理服务类"""
    
    # 状态名称映射
    STATUS_NAMES = {
        "normal": "正常",
        "hidden": "禁用"
    }
    
    def __init__(self, db: Session):
        self.db = db
    
    def _generate_salt(self) -> str:
        """生成密码盐"""
        return ''.join(random.choices(string.ascii_letters + string.digits, k=6))
    
    def _hash_password(self, password: str, salt: str) -> str:
        """密码加密"""
        return hashlib.md5((password + salt).encode()).hexdigest()
    
    def _verify_password(self, password: str, salt: str, hashed: str) -> bool:
        """验证密码"""
        return self._hash_password(password, salt) == hashed
    
    def _format_timestamp(self, timestamp: Optional[int]) -> Optional[str]:
        """格式化时间戳"""
        if not timestamp:
            return None
        try:
            return datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
        except:
            return None
    
    def _build_admin_response(self, admin: Admin) -> AdminResponse:
        """构建管理员响应对象"""
        # 获取门店名称
        store_name = None
        if admin.store_id:
            store = self.db.query(Store).filter(Store.id == admin.store_id).first()
            if store:
                store_name = store.name
        
        return AdminResponse(
            id=admin.id,
            username=admin.username,
            nickname=admin.nickname,
            email=admin.email,
            mobile=admin.mobile,
            status=admin.status,
            store_id=admin.store_id,
            avatar=admin.avatar,
            loginfailure=admin.loginfailure,
            logintime=admin.logintime,
            loginip=admin.loginip,
            createtime=admin.createtime,
            updatetime=admin.updatetime,
            store_name=store_name,
            last_login_time=self._format_timestamp(admin.logintime),
            status_name=self.STATUS_NAMES.get(admin.status, admin.status)
        )
    
    async def get_admins(
        self, 
        page: int = 1, 
        page_size: int = 20,
        status: Optional[str] = None,
        store_id: Optional[int] = None,
        keyword: Optional[str] = None
    ) -> AdminListResponse:
        """
        获取管理员列表
        
        Args:
            page: 页码
            page_size: 每页数量
            status: 状态筛选
            store_id: 门店筛选
            keyword: 搜索关键词（用户名、昵称、邮箱、手机号）
        """
        try:
            # 构建查询条件
            query = self.db.query(Admin)
            
            # 状态筛选
            if status is not None:
                query = query.filter(Admin.status == status)
            
            # 门店筛选
            if store_id is not None:
                query = query.filter(Admin.store_id == store_id)
            
            # 关键词搜索
            if keyword:
                keyword = f"%{keyword.strip()}%"
                query = query.filter(
                    or_(
                        Admin.username.like(keyword),
                        Admin.nickname.like(keyword),
                        Admin.email.like(keyword),
                        Admin.mobile.like(keyword)
                    )
                )
            
            # 计算总数
            total = query.count()
            
            # 分页查询
            offset = (page - 1) * page_size
            admins = query.order_by(desc(Admin.id)).offset(offset).limit(page_size).all()
            
            # 构建响应对象
            admin_responses = [self._build_admin_response(admin) for admin in admins]
            
            # 计算总页数
            total_pages = math.ceil(total / page_size) if total > 0 else 1
            
            logger.info(f"获取管理员列表: 页码={page}, 每页={page_size}, 总数={total}")
            
            return AdminListResponse(
                items=admin_responses,
                total=total,
                page=page,
                page_size=page_size,
                total_pages=total_pages
            )
            
        except Exception as e:
            logger.error(f"获取管理员列表失败: {str(e)}")
            raise e
    
    async def get_admin_by_id(self, admin_id: int) -> Optional[AdminResponse]:
        """根据ID获取管理员详情"""
        try:
            admin = self.db.query(Admin).filter(Admin.id == admin_id).first()
            if not admin:
                return None
            
            logger.info(f"获取管理员详情: ID={admin_id}")
            return self._build_admin_response(admin)
            
        except Exception as e:
            logger.error(f"获取管理员详情失败: admin_id={admin_id}, error={str(e)}")
            raise e
    
    async def get_admin_by_username(self, username: str) -> Optional[Admin]:
        """根据用户名获取管理员（用于重复性检查）"""
        try:
            return self.db.query(Admin).filter(Admin.username == username.strip()).first()
        except Exception as e:
            logger.error(f"根据用户名获取管理员失败: username={username}, error={str(e)}")
            raise e
    
    async def check_username_exists(self, username: str, exclude_id: Optional[int] = None) -> bool:
        """检查用户名是否已存在"""
        try:
            query = self.db.query(Admin).filter(Admin.username == username.strip())
            if exclude_id:
                query = query.filter(Admin.id != exclude_id)
            return query.first() is not None
        except Exception as e:
            logger.error(f"检查用户名失败: username={username}, error={str(e)}")
            raise e
    
    async def create_admin(self, admin_data: AdminCreate) -> AdminResponse:
        """创建新管理员"""
        try:
            # 检查用户名是否重复
            if await self.check_username_exists(admin_data.username):
                raise ValueError("用户名已存在")
            
            # 检查门店是否存在
            if admin_data.store_id:
                store = self.db.query(Store).filter(Store.id == admin_data.store_id).first()
                if not store:
                    raise ValueError("指定的门店不存在")
            
            # 生成密码盐和加密密码
            salt = self._generate_salt()
            hashed_password = self._hash_password(admin_data.password, salt)
            
            # 创建管理员对象
            current_time = int(time.time())
            
            db_admin = Admin(
                username=admin_data.username.strip(),
                nickname=admin_data.nickname.strip(),
                password=hashed_password,
                salt=salt,
                email=admin_data.email.strip() if admin_data.email else "",
                mobile=admin_data.mobile.strip() if admin_data.mobile else "",
                status=admin_data.status,
                store_id=admin_data.store_id,
                loginfailure=0,
                createtime=current_time,
                updatetime=current_time
            )
            
            # 保存到数据库
            self.db.add(db_admin)
            self.db.commit()
            self.db.refresh(db_admin)
            
            logger.info(f"创建管理员成功: ID={db_admin.id}, 用户名={db_admin.username}, 昵称={db_admin.nickname}")
            
            return self._build_admin_response(db_admin)
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"创建管理员失败: error={str(e)}")
            raise e
    
    async def update_admin(self, admin_id: int, admin_data: AdminUpdate) -> Optional[AdminResponse]:
        """更新管理员信息"""
        try:
            # 获取现有管理员
            admin = self.db.query(Admin).filter(Admin.id == admin_id).first()
            if not admin:
                return None
            
            # 检查用户名重复（如果更新了用户名）
            if admin_data.username and admin_data.username.strip() != admin.username:
                if await self.check_username_exists(admin_data.username, admin_id):
                    raise ValueError("用户名已存在")
            
            # 检查门店是否存在（如果更新了门店）
            if admin_data.store_id is not None and admin_data.store_id != admin.store_id:
                if admin_data.store_id > 0:
                    store = self.db.query(Store).filter(Store.id == admin_data.store_id).first()
                    if not store:
                        raise ValueError("指定的门店不存在")
            
            # 更新字段
            if admin_data.username is not None:
                admin.username = admin_data.username.strip()
            if admin_data.nickname is not None:
                admin.nickname = admin_data.nickname.strip()
            if admin_data.email is not None:
                admin.email = admin_data.email.strip() if admin_data.email else ""
            if admin_data.mobile is not None:
                admin.mobile = admin_data.mobile.strip() if admin_data.mobile else ""
            if admin_data.status is not None:
                admin.status = admin_data.status
            if admin_data.store_id is not None:
                admin.store_id = admin_data.store_id if admin_data.store_id > 0 else None
            
            # 更新密码（如果提供了新密码）
            if admin_data.password:
                salt = self._generate_salt()
                admin.password = self._hash_password(admin_data.password, salt)
                admin.salt = salt
            
            # 更新时间戳
            admin.updatetime = int(time.time())
            
            # 保存更改
            self.db.commit()
            self.db.refresh(admin)
            
            logger.info(f"更新管理员成功: ID={admin_id}")
            
            return self._build_admin_response(admin)
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"更新管理员失败: admin_id={admin_id}, error={str(e)}")
            raise e
    
    async def delete_admin(self, admin_id: int) -> bool:
        """删除管理员"""
        try:
            admin = self.db.query(Admin).filter(Admin.id == admin_id).first()
            if not admin:
                return False
            
            # 这里可以添加更多的关联数据检查
            # 例如检查是否有操作日志、关联业务数据等
            # 目前先简单删除
            
            # 执行删除
            self.db.delete(admin)
            self.db.commit()
            
            logger.info(f"删除管理员成功: ID={admin_id}, 用户名={admin.username}, 昵称={admin.nickname}")
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"删除管理员失败: admin_id={admin_id}, error={str(e)}")
            raise e
    
    async def update_admin_status(self, admin_id: int, status: str) -> Optional[AdminResponse]:
        """更新管理员状态"""
        try:
            admin = self.db.query(Admin).filter(Admin.id == admin_id).first()
            if not admin:
                return None
            
            admin.status = status
            admin.updatetime = int(time.time())
            
            self.db.commit()
            self.db.refresh(admin)
            
            logger.info(f"更新管理员状态成功: ID={admin_id}, 状态={status}")
            
            return self._build_admin_response(admin)
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"更新管理员状态失败: admin_id={admin_id}, error={str(e)}")
            raise e
    
    async def update_admin_password(self, admin_id: int, password_data: AdminPasswordUpdate) -> Optional[AdminResponse]:
        """更新管理员密码"""
        try:
            admin = self.db.query(Admin).filter(Admin.id == admin_id).first()
            if not admin:
                return None
            
            # 验证原密码
            if not self._verify_password(password_data.old_password, admin.salt, admin.password):
                raise ValueError("原密码不正确")
            
            # 生成新的盐和密码
            salt = self._generate_salt()
            hashed_password = self._hash_password(password_data.new_password, salt)
            
            # 更新密码
            admin.password = hashed_password
            admin.salt = salt
            admin.updatetime = int(time.time())
            
            self.db.commit()
            self.db.refresh(admin)
            
            logger.info(f"更新管理员密码成功: ID={admin_id}")
            
            return self._build_admin_response(admin)
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"更新管理员密码失败: admin_id={admin_id}, error={str(e)}")
            raise e
    
    async def update_login_info(self, admin_id: int, login_info: AdminLoginInfo) -> bool:
        """更新管理员登录信息"""
        try:
            admin = self.db.query(Admin).filter(Admin.id == admin_id).first()
            if not admin:
                return False
            
            current_time = int(time.time())
            
            if login_info.success:
                # 登录成功
                admin.logintime = current_time
                admin.loginip = login_info.login_ip
                admin.loginfailure = 0  # 重置失败次数
            else:
                # 登录失败
                admin.loginfailure += 1
            
            admin.updatetime = current_time
            
            self.db.commit()
            
            logger.info(f"更新管理员登录信息: ID={admin_id}, 成功={login_info.success}")
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"更新管理员登录信息失败: admin_id={admin_id}, error={str(e)}")
            raise e
    
    async def get_admin_statistics(self) -> AdminStatistics:
        """获取管理员统计信息"""
        try:
            # 基础统计
            total_admins = self.db.query(Admin).count()
            normal_admins = self.db.query(Admin).filter(Admin.status == "normal").count()
            hidden_admins = self.db.query(Admin).filter(Admin.status == "hidden").count()
            
            # 门店分布统计
            store_distribution = {}
            store_counts = self.db.query(
                Store.name, 
                func.count(Admin.id).label('admin_count')
            ).outerjoin(Admin, Store.id == Admin.store_id)\
             .group_by(Store.id, Store.name).all()
            
            for store_name, admin_count in store_counts:
                store_distribution[store_name or "未分配门店"] = admin_count
            
            # 近7天登录统计
            seven_days_ago = int(time.time()) - (7 * 24 * 60 * 60)
            recent_logins = self.db.query(Admin).filter(
                Admin.logintime >= seven_days_ago
            ).count()
            
            logger.info("获取管理员统计信息成功")
            
            return AdminStatistics(
                total_admins=total_admins,
                normal_admins=normal_admins,
                hidden_admins=hidden_admins,
                store_distribution=store_distribution,
                recent_logins=recent_logins
            )
            
        except Exception as e:
            logger.error(f"获取管理员统计失败: error={str(e)}")
            raise e 