/// 处理工单详情对话框
/// 显示处理工单的详细信息

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../../core/constants/border_styles.dart';
import '../../../core/widgets/custom_card.dart';
import '../../../models/recycling/recycling_process_new.dart';

class ProcessDetailDialog extends StatelessWidget {
  final RecyclingProcessNew process;

  const ProcessDetailDialog({
    Key? key,
    required this.process,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
      ),
      child: Container(
        width: 800,
        constraints: const BoxConstraints(maxHeight: 600),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(),
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildBasicInfo(),
                    const SizedBox(height: 24),
                    _buildWeightInfo(),
                    const SizedBox(height: 24),
                    _buildTimeInfo(),
                    if (process.remark != null && process.remark!.isNotEmpty) ...[
                      const SizedBox(height: 24),
                      _buildRemark(),
                    ],
                    if (process.results != null && process.results!.isNotEmpty) ...[
                      const SizedBox(height: 24),
                      _buildResults(),
                    ],
                  ],
                ),
              ),
            ),
            _buildFooter(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(AppBorderStyles.borderRadius),
          topRight: Radius.circular(AppBorderStyles.borderRadius),
        ),
      ),
      child: Row(
        children: [
          Icon(Icons.engineering, color: Colors.blue.shade700),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              '处理工单详情 - ${process.processNo}',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.blue.shade700,
              ),
            ),
          ),
          IconButton(
            onPressed: () => Get.back(),
            icon: const Icon(Icons.close),
            tooltip: '关闭',
          ),
        ],
      ),
    );
  }

  Widget _buildBasicInfo() {
    return CustomCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionTitle('基本信息'),
            const SizedBox(height: 16),
            _buildInfoGrid([
              _InfoItem('处理单号', process.processNo),
              _InfoItem('处理类型', process.processType.label, 
                color: _getProcessTypeColor(process.processType)),
              _InfoItem('状态', process.status.label,
                color: Color(int.parse(process.getStatusColor().replaceAll('#', '0xFF')))),
              _InfoItem('回收单号', process.recyclingNo ?? '-'),
              _InfoItem('处理门店', process.store?.name ?? '-'),
              _InfoItem('来源门店', process.sourceStore?.name ?? '-'),
            ]),
          ],
        ),
      ),
    );
  }

  Widget _buildWeightInfo() {
    return CustomCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionTitle('重量信息'),
            const SizedBox(height: 16),
            _buildInfoGrid([
              _InfoItem('总重量', '${process.totalWeight.toStringAsFixed(2)} g'),
              _InfoItem('预估金重', '${process.estimatedGoldWeight.toStringAsFixed(2)} g'),
              _InfoItem('预估银重', '${process.estimatedSilverWeight.toStringAsFixed(2)} g'),
            ]),
          ],
        ),
      ),
    );
  }

  Widget _buildTimeInfo() {
    return CustomCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionTitle('时间信息'),
            const SizedBox(height: 16),
            _buildInfoGrid([
              _InfoItem('创建时间', DateFormat('yyyy-MM-dd HH:mm:ss').format(process.createTime)),
              _InfoItem('开始时间', process.startTime != null 
                ? DateFormat('yyyy-MM-dd HH:mm:ss').format(process.startTime!)
                : '-'),
              _InfoItem('完成时间', process.endTime != null
                ? DateFormat('yyyy-MM-dd HH:mm:ss').format(process.endTime!)
                : '-'),
              _InfoItem('操作员', process.operatorName ?? '-'),
              _InfoItem('处理人', process.processorName ?? '-'),
            ]),
          ],
        ),
      ),
    );
  }

  Widget _buildRemark() {
    return CustomCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionTitle('备注'),
            const SizedBox(height: 8),
            Text(
              process.remark!,
              style: const TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResults() {
    return CustomCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionTitle('处理结果'),
            const SizedBox(height: 16),
            ...process.results!.map((result) => _buildResultItem(result)),
          ],
        ),
      ),
    );
  }

  Widget _buildResultItem(ProcessResult result) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(AppBorderStyles.smallBorderRadius),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                result.name,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: result.isConverted ? Colors.green.shade100 : Colors.orange.shade100,
                  borderRadius: BorderRadius.circular(AppBorderStyles.smallBorderRadius),
                ),
                child: Text(
                  result.isConverted ? '已入库' : '待入库',
                  style: TextStyle(
                    fontSize: 12,
                    color: result.isConverted ? Colors.green : Colors.orange,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 16,
            runSpacing: 8,
            children: [
              _buildResultInfo('类型', result.resultType.label),
              _buildResultInfo('重量', '${result.weight.toStringAsFixed(2)} g'),
              if (result.purity != null)
                _buildResultInfo('纯度', '${result.purity!.toStringAsFixed(1)}%'),
              _buildResultInfo('损耗', '${result.lossWeight.toStringAsFixed(2)} g (${result.lossRate.toStringAsFixed(1)}%)'),
              _buildResultInfo('加工成本', '¥${result.processCost.toStringAsFixed(2)}'),
              _buildResultInfo('人工成本', '¥${result.laborCost.toStringAsFixed(2)}'),
              _buildResultInfo('其他成本', '¥${result.otherCost.toStringAsFixed(2)}'),
              _buildResultInfo('总成本', '¥${result.totalCost.toStringAsFixed(2)}'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildResultInfo(String label, String value) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          '$label: ',
          style: const TextStyle(
            fontSize: 13,
            color: Colors.grey,
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            fontSize: 13,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildInfoGrid(List<_InfoItem> items) {
    return Wrap(
      spacing: 32,
      runSpacing: 12,
      children: items.map((item) => SizedBox(
        width: 200,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${item.label}：',
              style: const TextStyle(
                color: Colors.grey,
                fontSize: 14,
              ),
            ),
            Expanded(
              child: Text(
                item.value,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: item.color,
                ),
              ),
            ),
          ],
        ),
      )).toList(),
    );
  }

  Widget _buildFooter() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(AppBorderStyles.borderRadius),
          bottomRight: Radius.circular(AppBorderStyles.borderRadius),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          ElevatedButton(
            onPressed: () => Get.back(),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.grey,
            ),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  Color _getProcessTypeColor(ProcessType type) {
    switch (type) {
      case ProcessType.separation:
        return Colors.purple;
      case ProcessType.refurbish:
        return Colors.teal;
      case ProcessType.melt:
        return Colors.deepOrange;
    }
  }
}

class _InfoItem {
  final String label;
  final String value;
  final Color? color;

  _InfoItem(this.label, this.value, {this.color});
}