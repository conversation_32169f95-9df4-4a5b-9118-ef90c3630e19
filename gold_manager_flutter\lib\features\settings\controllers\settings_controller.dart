import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../models/setting_model.dart';
import '../services/settings_service.dart';

/// 系统设置控制器
class SettingsController extends GetxController with GetSingleTickerProviderStateMixin {
  // 服务实例
  final SettingsService _settingsService = SettingsService();
  
  // Tab控制器
  late TabController tabController;
  
  // 加载状态
  final RxBool isLoading = false.obs;
  
  // 系统设置
  final Rx<SystemSettings> settings = Rx<SystemSettings>(SystemSettings.defaultSettings());
  
  // 用户列表
  final RxList<User> userList = <User>[].obs;
  
  // 角色列表
  final RxList<Role> roleList = <Role>[].obs;
  
  // 权限列表
  final RxList<Permission> permissionList = <Permission>[].obs;
  
  // 备份列表
  final RxList<BackupRecord> backupList = <BackupRecord>[].obs;
  
  // 系统日志
  final RxList<SystemLog> systemLogs = <SystemLog>[].obs;
  
  // 分页
  final RxInt currentPage = 1.obs;
  final RxInt totalPages = 1.obs;
  final RxInt totalItems = 0.obs;
  
  // 搜索关键字
  final RxString searchKeyword = ''.obs;
  
  // 筛选条件
  final RxInt selectedRoleId = 0.obs;
  final RxInt selectedStoreId = 0.obs;
  
  @override
  void onInit() {
    super.onInit();
    
    // 初始化Tab控制器
    tabController = TabController(length: 5, vsync: this);
    
    // 加载初始数据
    loadSettings();
    
    // 监听Tab变化
    tabController.addListener(_handleTabChange);
  }
  
  @override
  void onClose() {
    tabController.dispose();
    super.onClose();
  }
  
  /// 处理Tab变化
  void _handleTabChange() {
    if (!tabController.indexIsChanging) {
      return;
    }
    
    switch (tabController.index) {
      case 0: // 基本设置
        // 已在初始化时加载
        break;
      case 1: // 用户管理
        loadUserList();
        break;
      case 2: // 角色权限
        loadRoleList();
        loadPermissionList();
        break;
      case 3: // 备份恢复
        loadBackupList();
        break;
      case 4: // 系统日志
        loadSystemLogs();
        break;
    }
  }
  
  /// 加载系统设置
  Future<void> loadSettings() async {
    try {
      isLoading.value = true;
      final data = await _settingsService.getSettings();
      settings.value = data;
    } catch (e) {
      print('加载系统设置失败: $e');
      Get.snackbar('加载失败', '加载系统设置数据失败，请稍后重试',
          snackPosition: SnackPosition.BOTTOM);
    } finally {
      isLoading.value = false;
    }
  }
  
  /// 保存系统设置
  Future<bool> saveSettings() async {
    try {
      isLoading.value = true;
      
      final success = await _settingsService.saveSettings(settings.value);
      
      if (success) {
        Get.snackbar('保存成功', '系统设置已成功保存',
            snackPosition: SnackPosition.BOTTOM);
      } else {
        Get.snackbar('保存失败', '系统设置保存失败，请稍后重试',
            snackPosition: SnackPosition.BOTTOM);
      }
      
      return success;
    } catch (e) {
      print('保存系统设置失败: $e');
      Get.snackbar('保存失败', '系统设置保存失败，请稍后重试',
          snackPosition: SnackPosition.BOTTOM);
      return false;
    } finally {
      isLoading.value = false;
    }
  }
  
  /// 重置系统设置
  Future<void> resetSettings() async {
    try {
      isLoading.value = true;
      
      final defaultSettings = await _settingsService.resetSettings();
      settings.value = defaultSettings;
      
      Get.snackbar('重置成功', '系统设置已重置为默认值',
          snackPosition: SnackPosition.BOTTOM);
    } catch (e) {
      print('重置系统设置失败: $e');
      Get.snackbar('重置失败', '系统设置重置失败，请稍后重试',
          snackPosition: SnackPosition.BOTTOM);
    } finally {
      isLoading.value = false;
    }
  }
  
  /// 更新基本设置
  void updateGeneralSettings(GeneralSettings generalSettings) {
    settings.value = settings.value.copyWith(generalSettings: generalSettings);
  }
  
  /// 更新价格设置
  void updatePriceSettings(PriceSettings priceSettings) {
    settings.value = settings.value.copyWith(priceSettings: priceSettings);
  }
  
  /// 更新显示设置
  void updateDisplaySettings(DisplaySettings displaySettings) {
    settings.value = settings.value.copyWith(displaySettings: displaySettings);
  }
  
  /// 更新同步设置
  void updateSyncSettings(SyncSettings syncSettings) {
    settings.value = settings.value.copyWith(syncSettings: syncSettings);
  }
  
  /// 更新备份设置
  void updateBackupSettings(BackupSettings backupSettings) {
    settings.value = settings.value.copyWith(backupSettings: backupSettings);
  }
  
  // ------ 用户管理 ------
  
  /// 加载用户列表
  Future<void> loadUserList({int page = 1}) async {
    try {
      isLoading.value = true;
      currentPage.value = page;
      
      final users = await _settingsService.getUserList(
        page: page,
        pageSize: settings.value.generalSettings.pageSize,
        keyword: searchKeyword.value.isNotEmpty ? searchKeyword.value : null,
        roleId: selectedRoleId.value > 0 ? selectedRoleId.value : null,
        storeId: selectedStoreId.value > 0 ? selectedStoreId.value : null,
      );
      
      userList.value = users;
    } catch (e) {
      print('加载用户列表失败: $e');
      Get.snackbar('加载失败', '加载用户列表失败，请稍后重试',
          snackPosition: SnackPosition.BOTTOM);
    } finally {
      isLoading.value = false;
    }
  }
  
  /// 搜索用户
  void searchUsers(String keyword) {
    searchKeyword.value = keyword;
    loadUserList(page: 1);
  }
  
  /// 筛选用户
  void filterUsers({int? roleId, int? storeId}) {
    if (roleId != null) {
      selectedRoleId.value = roleId;
    }
    
    if (storeId != null) {
      selectedStoreId.value = storeId;
    }
    
    loadUserList(page: 1);
  }
  
  /// 创建用户
  Future<bool> createUser(Map<String, dynamic> userData) async {
    try {
      isLoading.value = true;
      
      final success = await _settingsService.createUser(userData);
      
      if (success) {
        Get.snackbar('创建成功', '用户已成功创建',
            snackPosition: SnackPosition.BOTTOM);
        await loadUserList();
      } else {
        Get.snackbar('创建失败', '用户创建失败，请稍后重试',
            snackPosition: SnackPosition.BOTTOM);
      }
      
      return success;
    } catch (e) {
      print('创建用户失败: $e');
      Get.snackbar('创建失败', '用户创建失败，请稍后重试',
          snackPosition: SnackPosition.BOTTOM);
      return false;
    } finally {
      isLoading.value = false;
    }
  }
  
  /// 更新用户
  Future<bool> updateUser(int userId, Map<String, dynamic> userData) async {
    try {
      isLoading.value = true;
      
      final success = await _settingsService.updateUser(userId, userData);
      
      if (success) {
        Get.snackbar('更新成功', '用户信息已成功更新',
            snackPosition: SnackPosition.BOTTOM);
        await loadUserList();
      } else {
        Get.snackbar('更新失败', '用户信息更新失败，请稍后重试',
            snackPosition: SnackPosition.BOTTOM);
      }
      
      return success;
    } catch (e) {
      print('更新用户失败: $e');
      Get.snackbar('更新失败', '用户信息更新失败，请稍后重试',
          snackPosition: SnackPosition.BOTTOM);
      return false;
    } finally {
      isLoading.value = false;
    }
  }
  
  /// 删除用户
  Future<bool> deleteUser(int userId) async {
    try {
      isLoading.value = true;
      
      final success = await _settingsService.deleteUser(userId);
      
      if (success) {
        Get.snackbar('删除成功', '用户已成功删除',
            snackPosition: SnackPosition.BOTTOM);
        await loadUserList();
      } else {
        Get.snackbar('删除失败', '用户删除失败，请稍后重试',
            snackPosition: SnackPosition.BOTTOM);
      }
      
      return success;
    } catch (e) {
      print('删除用户失败: $e');
      Get.snackbar('删除失败', '用户删除失败，请稍后重试',
          snackPosition: SnackPosition.BOTTOM);
      return false;
    } finally {
      isLoading.value = false;
    }
  }
  
  /// 重置用户密码
  Future<bool> resetUserPassword(int userId) async {
    try {
      isLoading.value = true;
      
      final success = await _settingsService.resetUserPassword(userId);
      
      if (success) {
        Get.snackbar('重置成功', '用户密码已重置',
            snackPosition: SnackPosition.BOTTOM);
      } else {
        Get.snackbar('重置失败', '用户密码重置失败，请稍后重试',
            snackPosition: SnackPosition.BOTTOM);
      }
      
      return success;
    } catch (e) {
      print('重置用户密码失败: $e');
      Get.snackbar('重置失败', '用户密码重置失败，请稍后重试',
          snackPosition: SnackPosition.BOTTOM);
      return false;
    } finally {
      isLoading.value = false;
    }
  }
  
  // ------ 角色权限管理 ------
  
  /// 加载角色列表
  Future<void> loadRoleList() async {
    try {
      isLoading.value = true;
      
      final roles = await _settingsService.getRoleList();
      roleList.value = roles;
    } catch (e) {
      print('加载角色列表失败: $e');
      Get.snackbar('加载失败', '加载角色列表失败，请稍后重试',
          snackPosition: SnackPosition.BOTTOM);
    } finally {
      isLoading.value = false;
    }
  }
  
  /// 加载权限列表
  Future<void> loadPermissionList() async {
    try {
      isLoading.value = true;
      
      final permissions = await _settingsService.getPermissionList();
      permissionList.value = permissions;
    } catch (e) {
      print('加载权限列表失败: $e');
      Get.snackbar('加载失败', '加载权限列表失败，请稍后重试',
          snackPosition: SnackPosition.BOTTOM);
    } finally {
      isLoading.value = false;
    }
  }
  
  /// 创建角色
  Future<bool> createRole(String name, String description, List<int> permissionIds) async {
    try {
      isLoading.value = true;
      
      final success = await _settingsService.createRole(name, description, permissionIds);
      
      if (success) {
        Get.snackbar('创建成功', '角色已成功创建',
            snackPosition: SnackPosition.BOTTOM);
        await loadRoleList();
      } else {
        Get.snackbar('创建失败', '角色创建失败，请稍后重试',
            snackPosition: SnackPosition.BOTTOM);
      }
      
      return success;
    } catch (e) {
      print('创建角色失败: $e');
      Get.snackbar('创建失败', '角色创建失败，请稍后重试',
          snackPosition: SnackPosition.BOTTOM);
      return false;
    } finally {
      isLoading.value = false;
    }
  }
  
  /// 更新角色
  Future<bool> updateRole(int roleId, String name, String description, List<int> permissionIds) async {
    try {
      isLoading.value = true;
      
      final success = await _settingsService.updateRole(roleId, name, description, permissionIds);
      
      if (success) {
        Get.snackbar('更新成功', '角色已成功更新',
            snackPosition: SnackPosition.BOTTOM);
        await loadRoleList();
      } else {
        Get.snackbar('更新失败', '角色更新失败，请稍后重试',
            snackPosition: SnackPosition.BOTTOM);
      }
      
      return success;
    } catch (e) {
      print('更新角色失败: $e');
      Get.snackbar('更新失败', '角色更新失败，请稍后重试',
          snackPosition: SnackPosition.BOTTOM);
      return false;
    } finally {
      isLoading.value = false;
    }
  }
  
  /// 删除角色
  Future<bool> deleteRole(int roleId) async {
    try {
      isLoading.value = true;
      
      final success = await _settingsService.deleteRole(roleId);
      
      if (success) {
        Get.snackbar('删除成功', '角色已成功删除',
            snackPosition: SnackPosition.BOTTOM);
        await loadRoleList();
      } else {
        Get.snackbar('删除失败', '角色删除失败，请稍后重试',
            snackPosition: SnackPosition.BOTTOM);
      }
      
      return success;
    } catch (e) {
      print('删除角色失败: $e');
      Get.snackbar('删除失败', '角色删除失败，请稍后重试',
          snackPosition: SnackPosition.BOTTOM);
      return false;
    } finally {
      isLoading.value = false;
    }
  }
  
  // ------ 备份恢复 ------
  
  /// 加载备份列表
  Future<void> loadBackupList() async {
    try {
      isLoading.value = true;
      
      final backups = await _settingsService.getBackupList();
      backupList.value = backups;
    } catch (e) {
      print('加载备份列表失败: $e');
      Get.snackbar('加载失败', '加载备份列表失败，请稍后重试',
          snackPosition: SnackPosition.BOTTOM);
    } finally {
      isLoading.value = false;
    }
  }
  
  /// 创建备份
  Future<bool> createBackup(String description) async {
    try {
      isLoading.value = true;
      
      final success = await _settingsService.createBackup(description);
      
      if (success) {
        Get.snackbar('备份成功', '系统数据已成功备份',
            snackPosition: SnackPosition.BOTTOM);
        await loadBackupList();
      } else {
        Get.snackbar('备份失败', '系统数据备份失败，请稍后重试',
            snackPosition: SnackPosition.BOTTOM);
      }
      
      return success;
    } catch (e) {
      print('创建备份失败: $e');
      Get.snackbar('备份失败', '系统数据备份失败，请稍后重试',
          snackPosition: SnackPosition.BOTTOM);
      return false;
    } finally {
      isLoading.value = false;
    }
  }
  
  /// 下载备份
  Future<String?> downloadBackup(int backupId) async {
    try {
      isLoading.value = true;
      
      final filePath = await _settingsService.downloadBackup(backupId);
      
      if (filePath != null) {
        Get.snackbar('下载成功', '备份文件已下载到: $filePath',
            snackPosition: SnackPosition.BOTTOM);
      } else {
        Get.snackbar('下载失败', '备份文件下载失败，请稍后重试',
            snackPosition: SnackPosition.BOTTOM);
      }
      
      return filePath;
    } catch (e) {
      print('下载备份失败: $e');
      Get.snackbar('下载失败', '备份文件下载失败，请稍后重试',
          snackPosition: SnackPosition.BOTTOM);
      return null;
    } finally {
      isLoading.value = false;
    }
  }
  
  /// 恢复备份
  Future<bool> restoreBackup(int backupId) async {
    try {
      isLoading.value = true;
      
      final success = await _settingsService.restoreBackup(backupId);
      
      if (success) {
        Get.snackbar('恢复成功', '系统数据已成功恢复',
            snackPosition: SnackPosition.BOTTOM);
      } else {
        Get.snackbar('恢复失败', '系统数据恢复失败，请稍后重试',
            snackPosition: SnackPosition.BOTTOM);
      }
      
      return success;
    } catch (e) {
      print('恢复备份失败: $e');
      Get.snackbar('恢复失败', '系统数据恢复失败，请稍后重试',
          snackPosition: SnackPosition.BOTTOM);
      return false;
    } finally {
      isLoading.value = false;
    }
  }
  
  /// 上传备份文件
  Future<bool> uploadBackup(File backupFile, String description) async {
    try {
      isLoading.value = true;
      
      final success = await _settingsService.uploadBackup(backupFile, description);
      
      if (success) {
        Get.snackbar('上传成功', '备份文件已成功上传',
            snackPosition: SnackPosition.BOTTOM);
        await loadBackupList();
      } else {
        Get.snackbar('上传失败', '备份文件上传失败，请稍后重试',
            snackPosition: SnackPosition.BOTTOM);
      }
      
      return success;
    } catch (e) {
      print('上传备份失败: $e');
      Get.snackbar('上传失败', '备份文件上传失败，请稍后重试',
          snackPosition: SnackPosition.BOTTOM);
      return false;
    } finally {
      isLoading.value = false;
    }
  }
  
  /// 删除备份
  Future<bool> deleteBackup(int backupId) async {
    try {
      isLoading.value = true;
      
      final success = await _settingsService.deleteBackup(backupId);
      
      if (success) {
        Get.snackbar('删除成功', '备份记录已成功删除',
            snackPosition: SnackPosition.BOTTOM);
        await loadBackupList();
      } else {
        Get.snackbar('删除失败', '备份记录删除失败，请稍后重试',
            snackPosition: SnackPosition.BOTTOM);
      }
      
      return success;
    } catch (e) {
      print('删除备份失败: $e');
      Get.snackbar('删除失败', '备份记录删除失败，请稍后重试',
          snackPosition: SnackPosition.BOTTOM);
      return false;
    } finally {
      isLoading.value = false;
    }
  }
  
  // ------ 系统日志 ------
  
  /// 加载系统日志
  Future<void> loadSystemLogs({
    int page = 1,
    String? actionType,
    String? keyword,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      isLoading.value = true;
      currentPage.value = page;
      
      final String? formattedStartDate = startDate != null
          ? DateFormat('yyyy-MM-dd').format(startDate)
          : null;
      
      final String? formattedEndDate = endDate != null
          ? DateFormat('yyyy-MM-dd').format(endDate)
          : null;
      
      final logs = await _settingsService.getSystemLogs(
        page: page,
        pageSize: settings.value.generalSettings.pageSize,
        actionType: actionType,
        keyword: keyword ?? searchKeyword.value,
        startDate: formattedStartDate,
        endDate: formattedEndDate,
      );
      
      systemLogs.value = logs;
    } catch (e) {
      print('加载系统日志失败: $e');
      Get.snackbar('加载失败', '加载系统日志失败，请稍后重试',
          snackPosition: SnackPosition.BOTTOM);
    } finally {
      isLoading.value = false;
    }
  }
  
  /// 搜索系统日志
  void searchLogs(String keyword) {
    searchKeyword.value = keyword;
    loadSystemLogs(page: 1);
  }
  
  /// 清除系统日志
  Future<bool> clearSystemLogs() async {
    try {
      isLoading.value = true;
      
      final success = await _settingsService.clearSystemLogs();
      
      if (success) {
        Get.snackbar('清除成功', '系统日志已成功清除',
            snackPosition: SnackPosition.BOTTOM);
        systemLogs.clear();
      } else {
        Get.snackbar('清除失败', '系统日志清除失败，请稍后重试',
            snackPosition: SnackPosition.BOTTOM);
      }
      
      return success;
    } catch (e) {
      print('清除系统日志失败: $e');
      Get.snackbar('清除失败', '系统日志清除失败，请稍后重试',
          snackPosition: SnackPosition.BOTTOM);
      return false;
    } finally {
      isLoading.value = false;
    }
  }
} 