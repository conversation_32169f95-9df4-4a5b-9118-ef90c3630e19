import 'dart:async';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import '../utils/logger_service.dart';

/// 加载状态管理器
/// 统一管理应用中的加载状态，防止加载动画卡住
class LoadingManager extends GetxService {
  // 活跃的加载任务
  final Map<String, Timer> _activeTasks = {};
  final Map<String, Completer<void>> _taskCompleters = {};
  
  // 默认超时时间（秒）
  static const int defaultTimeoutSeconds = 45;
  
  /// 开始一个加载任务
  /// [taskId] 任务唯一标识
  /// [timeoutSeconds] 超时时间，默认45秒
  /// [onTimeout] 超时回调
  Future<void> startTask(
    String taskId, {
    int timeoutSeconds = defaultTimeoutSeconds,
    VoidCallback? onTimeout,
  }) async {
    LoggerService.d('🔄 [LoadingManager] 开始加载任务: $taskId (超时: ${timeoutSeconds}s)');
    
    // 如果任务已存在，先取消它
    if (_activeTasks.containsKey(taskId)) {
      await cancelTask(taskId);
    }
    
    // 创建任务完成器
    final completer = Completer<void>();
    _taskCompleters[taskId] = completer;
    
    // 设置超时定时器
    final timer = Timer(Duration(seconds: timeoutSeconds), () {
      if (_activeTasks.containsKey(taskId)) {
        LoggerService.w('⏰ [LoadingManager] 任务超时: $taskId');
        _handleTaskTimeout(taskId, onTimeout);
      }
    });
    
    _activeTasks[taskId] = timer;
    return completer.future;
  }
  
  /// 完成一个加载任务
  void completeTask(String taskId) {
    LoggerService.d('✅ [LoadingManager] 完成加载任务: $taskId');
    
    // 取消超时定时器
    _activeTasks[taskId]?.cancel();
    _activeTasks.remove(taskId);
    
    // 完成任务
    final completer = _taskCompleters.remove(taskId);
    if (completer != null && !completer.isCompleted) {
      completer.complete();
    }
  }
  
  /// 取消一个加载任务
  Future<void> cancelTask(String taskId) async {
    LoggerService.d('❌ [LoadingManager] 取消加载任务: $taskId');
    
    // 取消超时定时器
    _activeTasks[taskId]?.cancel();
    _activeTasks.remove(taskId);
    
    // 取消任务
    final completer = _taskCompleters.remove(taskId);
    if (completer != null && !completer.isCompleted) {
      completer.completeError('Task cancelled');
    }
  }
  
  /// 处理任务超时
  void _handleTaskTimeout(String taskId, VoidCallback? onTimeout) {
    LoggerService.e('⏰ [LoadingManager] 任务超时处理: $taskId');

    // 清理任务
    _activeTasks.remove(taskId);
    final completer = _taskCompleters.remove(taskId);

    // 完成任务（超时）
    if (completer != null && !completer.isCompleted) {
      completer.completeError('Task timeout');
    }

    // 执行超时回调
    onTimeout?.call();

    // 显示超时提示（仅在非测试环境下）
    try {
      if (Get.context != null && Get.isSnackbarOpen != true) {
        Get.snackbar(
          '加载超时',
          '操作超时，请检查网络连接或稍后重试',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.orange,
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
        );
      }
    } catch (e) {
      // 在测试环境或其他无法显示snackbar的情况下，只记录日志
      LoggerService.w('无法显示超时提示: $e');
    }
  }
  
  /// 检查任务是否活跃
  bool isTaskActive(String taskId) {
    return _activeTasks.containsKey(taskId);
  }
  
  /// 获取活跃任务数量
  int get activeTaskCount => _activeTasks.length;
  
  /// 获取所有活跃任务ID
  List<String> get activeTaskIds => _activeTasks.keys.toList();
  
  /// 取消所有任务
  void cancelAllTasks() {
    LoggerService.d('🧹 [LoadingManager] 取消所有加载任务');
    
    final taskIds = _activeTasks.keys.toList();
    for (final taskId in taskIds) {
      cancelTask(taskId);
    }
  }
  
  @override
  void onClose() {
    cancelAllTasks();
    super.onClose();
  }
}

/// 加载状态混入
/// 为控制器提供统一的加载状态管理
mixin LoadingStateMixin on GetxController {
  final RxBool isLoading = false.obs;
  final LoadingManager _loadingManager = Get.find<LoadingManager>();
  
  /// 执行带加载状态的异步操作
  /// [operation] 要执行的异步操作
  /// [taskId] 任务ID，默认使用控制器类名
  /// [timeoutSeconds] 超时时间
  /// [showGlobalLoading] 是否显示全局加载状态
  Future<T> executeWithLoading<T>(
    Future<T> Function() operation, {
    String? taskId,
    int timeoutSeconds = LoadingManager.defaultTimeoutSeconds,
    bool showGlobalLoading = false,
  }) async {
    final actualTaskId = taskId ?? '${runtimeType}_${DateTime.now().millisecondsSinceEpoch}';
    
    try {
      // 设置加载状态
      isLoading.value = true;
      
      // 开始加载任务
      final taskFuture = _loadingManager.startTask(
        actualTaskId,
        timeoutSeconds: timeoutSeconds,
        onTimeout: () {
          isLoading.value = false;
        },
      );
      
      // 执行操作
      final result = await operation();
      
      // 完成任务
      _loadingManager.completeTask(actualTaskId);
      
      return result;
    } catch (e) {
      // 取消任务
      await _loadingManager.cancelTask(actualTaskId);
      rethrow;
    } finally {
      // 重置加载状态
      isLoading.value = false;
    }
  }
  
  @override
  void onClose() {
    // 取消所有相关任务
    final taskIds = _loadingManager.activeTaskIds
        .where((id) => id.startsWith(runtimeType.toString()))
        .toList();
    
    for (final taskId in taskIds) {
      _loadingManager.cancelTask(taskId);
    }
    
    super.onClose();
  }
}
