import 'package:get/get.dart';
import '../core/utils/logger.dart';
import '../core/services/api_client.dart';
import '../core/config/app_config.dart';
import '../models/jewelry/category.dart';

/// 分类服务
/// 处理商品分类相关的业务逻辑
class CategoryService extends GetxService {
  late final ApiClient _apiClient;

  @override
  void onInit() {
    super.onInit();
    _apiClient = Get.find<ApiClient>();
  }

  /// 初始化服务
  Future<CategoryService> init() async {
    LoggerService.d('CategoryService 初始化');
    return this;
  }

  /// 获取分类列表
  Future<List<Category>> getCategories() async {
    try {
      LoggerService.d('🔄 开始获取分类列表');
      
      final response = await _apiClient.get('${AppConfig.apiEndpoint['jewelry']}/categories');

      if (response.statusCode == 200) {
        final data = response.data;
        // 根据后端API文档，categories接口直接返回数组，不是包装在items中
        final List<dynamic> items = data is List ? data : (data['data'] ?? data['items'] ?? []);
        
        final categories = items.map((item) => _mapCategoryFromApi(item)).toList();
        LoggerService.d('✅ 分类列表获取成功，共${categories.length}个分类');
        
        return categories;
      } else {
        throw Exception('获取分类列表失败: ${response.statusMessage}');
      }
    } catch (e) {
      LoggerService.e('❌ 获取分类列表失败', e);
      rethrow;
    }
  }

  /// 获取分类详情
  Future<Category> getCategoryById(int id) async {
    try {
      LoggerService.d('🔄 开始获取分类详情，ID: $id');
      
      final response = await _apiClient.get('${AppConfig.apiEndpoint['jewelry']}/categories/$id');

      if (response.statusCode == 200) {
        final data = response.data;
        final category = _mapCategoryFromApi(data);
        LoggerService.d('✅ 分类详情获取成功');
        
        return category;
      } else {
        throw Exception('获取分类详情失败: ${response.statusMessage}');
      }
    } catch (e) {
      LoggerService.e('❌ 获取分类详情失败', e);
      rethrow;
    }
  }

  /// 创建分类
  Future<Category> createCategory(Map<String, dynamic> data) async {
    try {
      LoggerService.d('🔄 开始创建分类，数据: $data');
      
      final response = await _apiClient.post(
        '${AppConfig.apiEndpoint['jewelry']}/categories',
        data: data,
      );

      if (response.statusCode == 201 || response.statusCode == 200) {
        final responseData = response.data;
        final category = _mapCategoryFromApi(responseData);
        LoggerService.d('✅ 分类创建成功');
        
        return category;
      } else {
        throw Exception('创建分类失败: ${response.statusMessage}');
      }
    } catch (e) {
      LoggerService.e('❌ 创建分类失败', e);
      rethrow;
    }
  }

  /// 更新分类
  Future<Category> updateCategory(int id, Map<String, dynamic> data) async {
    try {
      LoggerService.d('🔄 开始更新分类，ID: $id, 数据: $data');
      
      final response = await _apiClient.put(
        '${AppConfig.apiEndpoint['jewelry']}/categories/$id',
        data: data,
      );

      if (response.statusCode == 200) {
        final responseData = response.data;
        final category = _mapCategoryFromApi(responseData);
        LoggerService.d('✅ 分类更新成功');
        
        return category;
      } else {
        throw Exception('更新分类失败: ${response.statusMessage}');
      }
    } catch (e) {
      LoggerService.e('❌ 更新分类失败', e);
      rethrow;
    }
  }

  /// 删除分类
  Future<void> deleteCategory(int id) async {
    try {
      LoggerService.d('🗑️ 开始删除分类，ID: $id');
      
      final response = await _apiClient.delete(
        '${AppConfig.apiEndpoint['jewelry']}/categories/$id',
      );

      if (response.statusCode == 200 || response.statusCode == 204) {
        LoggerService.d('✅ 分类删除成功');
      } else {
        throw Exception('删除分类失败: ${response.statusMessage}');
      }
    } catch (e) {
      LoggerService.e('❌ 删除分类失败', e);
      rethrow;
    }
  }

  /// 从API数据映射分类对象
  Category _mapCategoryFromApi(Map<String, dynamic> json) {
    return Category(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      parentId: json['parent_id'] ?? 0,
      description: json['description'],
      createTime: json['createtime'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(json['createtime'] * 1000)
          : null,
      updateTime: json['updatetime'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(json['updatetime'] * 1000)
          : null,
    );
  }
}
