import 'dart:io';
import 'package:excel/excel.dart';
import 'package:file_picker/file_picker.dart';
import 'package:get/get.dart';

import '../core/utils/logger_service.dart';

/// Excel导出服务
/// 
/// 提供出库单商品明细的Excel导出功能
class ExcelService extends GetxService {
  
  /// 导出出库单商品明细到Excel文件
  /// 
  /// [items] 商品明细数据列表
  /// [stockOutData] 出库单基本信息（可选）
  /// [paymentData] 收款信息（可选）
  /// 
  /// 返回 true 表示导出成功，false 表示导出失败
  Future<bool> exportStockOutItems({
    required List<Map<String, dynamic>> items,
    Map<String, dynamic>? stockOutData,
    Map<String, dynamic>? paymentData,
  }) async {
    try {
      LoggerService.d('🔄 开始导出出库单明细到Excel...');
      LoggerService.d('📊 商品明细数量: ${items.length}');
      LoggerService.d('📊 出库单数据: ${stockOutData != null ? "有" : "无"}');
      LoggerService.d('📊 收款数据: ${paymentData != null ? "有" : "无"}');

      // 🔧 数据验证
      if (items.isEmpty) {
        LoggerService.w('⚠️ 商品明细列表为空，无法导出Excel');
        return false;
      }

      // 验证数据结构
      for (int i = 0; i < items.length; i++) {
        final item = items[i];
        LoggerService.d('📊 第${i + 1}个商品数据: ${item.keys.toList()}');

        // 检查必要字段
        if (!item.containsKey('barcode') || !item.containsKey('name')) {
          LoggerService.w('⚠️ 第${i + 1}个商品缺少必要字段');
        }
      }

      // 创建Excel工作簿
      LoggerService.d('📊 正在创建Excel工作簿...');
      final excel = Excel.createExcel();

      // 🔧 修复错误1：避免删除默认Sheet1，直接使用并重命名
      LoggerService.d('📊 正在设置工作表...');
      Sheet sheet;

      try {
        // 方案1：尝试重命名默认Sheet1为"出库单明细"
        if (excel.tables.containsKey('Sheet1')) {
          LoggerService.d('📊 检测到默认Sheet1，尝试重命名...');
          // 先获取Sheet1的引用
          final defaultSheet = excel.tables['Sheet1'];
          if (defaultSheet != null) {
            // 删除原有的Sheet1引用并创建新的
            excel.tables.remove('Sheet1');
            excel.tables['出库单明细'] = defaultSheet;
            sheet = defaultSheet;
            LoggerService.d('✅ 成功将Sheet1重命名为"出库单明细"');
          } else {
            // 如果获取失败，创建新的工作表
            sheet = excel['出库单明细'];
            LoggerService.d('✅ 创建新的"出库单明细"工作表');
          }
        } else {
          // 如果没有默认Sheet1，直接创建新工作表
          sheet = excel['出库单明细'];
          LoggerService.d('✅ 创建新的"出库单明细"工作表');
        }
      } catch (e) {
        // 如果重命名失败，使用备用方案：直接创建新工作表
        LoggerService.w('⚠️ 重命名Sheet1失败，使用备用方案: $e');
        sheet = excel['出库单明细'];
        LoggerService.d('✅ 使用备用方案创建"出库单明细"工作表');
      }

      // 设置表头
      LoggerService.d('📊 正在设置表头...');
      _setHeaders(sheet);

      // 填充数据
      LoggerService.d('📊 正在填充数据...');
      _fillData(sheet, items);

      // 如果有出库单信息，添加汇总信息
      if (stockOutData != null) {
        LoggerService.d('📊 正在添加汇总信息...');
        _addSummaryInfo(sheet, stockOutData, paymentData, items);
      }

      // 设置列宽
      LoggerService.d('📊 正在设置列宽...');
      _setColumnWidths(sheet);

      // 生成文件名
      final fileName = _generateFileName();
      LoggerService.d('📊 生成文件名: $fileName');

      // 保存文件
      LoggerService.d('📊 正在保存文件...');
      final success = await _saveExcelFile(excel, fileName);
      
      if (success) {
        LoggerService.d('✅ Excel文件导出成功: $fileName');
      } else {
        LoggerService.e('❌ Excel文件导出失败');
      }
      
      return success;
      
    } catch (e, stackTrace) {
      // 🔧 修复错误处理：分别记录错误信息和堆栈跟踪
      LoggerService.e('❌ Excel导出过程中发生错误: $e');
      LoggerService.e('❌ 错误堆栈跟踪: ${stackTrace.toString()}');

      // 🔧 增强错误分析：检查具体错误类型
      if (e.toString().contains('Unsupported operation: Cannot remove from an unmodifiable list')) {
        LoggerService.e('❌ 检测到Excel Sheet删除错误，这是已知问题，已通过重命名方案修复');
      } else if (e.toString().contains('StackTrace')) {
        LoggerService.e('❌ 检测到StackTrace相关错误，可能是日志记录问题');
      } else if (e.toString().contains('permission')) {
        LoggerService.e('❌ 检测到权限相关错误，可能是文件保存权限问题');
      } else if (e.toString().contains('excel') || e.toString().contains('Excel')) {
        LoggerService.e('❌ 检测到Excel相关错误，可能是Excel包兼容性问题');
      } else if (e.toString().contains('unmodifiable')) {
        LoggerService.e('❌ 检测到不可修改列表错误，可能是Excel包API限制');
      } else {
        LoggerService.e('❌ 未知错误类型，需要进一步分析');
      }

      return false;
    }
  }
  
  /// 设置Excel表头
  void _setHeaders(Sheet sheet) {
    final headers = [
      '序号',
      '条码',
      '商品名称',
      '分类',
      '圈口',
      '金重(克)',
      '银重(克)',
      '总重量(克)',
      '金价(元/克)',
      '银价(元/克)',
      '工费(元)',
      '件工费(元)',
      '总成本(元)',
    ];
    
    // 设置表头行
    for (int i = 0; i < headers.length; i++) {
      final cell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0));
      cell.value = headers[i];
      
      // 设置表头样式
      cell.cellStyle = CellStyle(
        bold: true,
        backgroundColorHex: '#E3F2FD', // 浅蓝色背景
        horizontalAlign: HorizontalAlign.Center,
        verticalAlign: VerticalAlign.Center,
      );
    }
  }
  
  /// 填充商品明细数据
  void _fillData(Sheet sheet, List<Map<String, dynamic>> items) {
    try {
      LoggerService.d('📊 开始填充${items.length}行商品数据');

      for (int i = 0; i < items.length; i++) {
        final item = items[i];
        final rowIndex = i + 1; // 跳过表头行

        LoggerService.d('📊 正在处理第${i + 1}行数据: ${item['barcode'] ?? 'N/A'}');

        // 填充每一列的数据
        final rowData = [
          i + 1, // 序号
          item['barcode'] ?? '',
          item['name'] ?? '',
          item['category'] ?? '',
          item['ring_size'] ?? '',
          _parseDouble(item['gold_weight']),
          _parseDouble(item['silver_weight']),
          _parseDouble(item['total_weight']),
          _parseDouble(item['gold_price']),
          _parseDouble(item['silver_price']),
          _parseDouble(item['work_price']),
          _parseDouble(item['piece_work_price']),
          _parseDouble(item['total_amount']),
        ];

        for (int j = 0; j < rowData.length; j++) {
          try {
            final cell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: j, rowIndex: rowIndex));
            cell.value = rowData[j];

            // 设置数据行样式
            cell.cellStyle = CellStyle(
              horizontalAlign: j == 0 || j >= 5 ? HorizontalAlign.Center : HorizontalAlign.Left,
              verticalAlign: VerticalAlign.Center,
            );
          } catch (e) {
            LoggerService.e('❌ 设置第${i + 1}行第${j + 1}列数据失败: $e');
            throw Exception('Excel单元格设置失败: 行$rowIndex, 列$j, 值: ${rowData[j]}');
          }
        }
      }

      LoggerService.d('✅ 商品数据填充完成');
    } catch (e) {
      LoggerService.e('❌ 填充商品数据时发生错误: $e');
      rethrow;
    }
  }
  
  /// 添加汇总信息
  void _addSummaryInfo(
    Sheet sheet, 
    Map<String, dynamic> stockOutData, 
    Map<String, dynamic>? paymentData,
    List<Map<String, dynamic>> items,
  ) {
    final lastDataRow = items.length + 1; // 最后一行数据的行号
    final summaryStartRow = lastDataRow + 2; // 汇总信息开始行号
    
    // 计算汇总数据
    final totalItems = items.length;
    final totalGoldWeight = items.fold<double>(0.0, (sum, item) => sum + _parseDouble(item['gold_weight']));
    final totalSilverWeight = items.fold<double>(0.0, (sum, item) => sum + _parseDouble(item['silver_weight']));
    final totalWeight = items.fold<double>(0.0, (sum, item) => sum + _parseDouble(item['total_weight']));
    final totalAmount = items.fold<double>(0.0, (sum, item) => sum + _parseDouble(item['total_amount']));
    
    // 添加汇总标题
    final titleCell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: summaryStartRow));
    titleCell.value = '汇总信息';
    titleCell.cellStyle = CellStyle(
      bold: true,
      fontSize: 14,
      backgroundColorHex: '#FFF3E0', // 浅橙色背景
    );
    
    // 添加汇总数据
    final summaryData = [
      ['商品件数:', '$totalItems 件'],
      ['总金重:', '${totalGoldWeight.toStringAsFixed(2)} 克'],
      ['总银重:', '${totalSilverWeight.toStringAsFixed(2)} 克'],
      ['总重量:', '${totalWeight.toStringAsFixed(2)} 克'],
      ['总金额:', '¥${totalAmount.toStringAsFixed(2)}'],
    ];
    
    for (int i = 0; i < summaryData.length; i++) {
      final rowIndex = summaryStartRow + 1 + i;
      
      // 标签列
      final labelCell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: rowIndex));
      labelCell.value = summaryData[i][0];
      labelCell.cellStyle = CellStyle(bold: true);
      
      // 数值列
      final valueCell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: rowIndex));
      valueCell.value = summaryData[i][1];
    }
    
    // 如果有收款信息，添加收款汇总
    if (paymentData != null) {
      final paymentStartRow = summaryStartRow + summaryData.length + 2;
      
      final paymentTitleCell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: paymentStartRow));
      paymentTitleCell.value = '收款信息';
      paymentTitleCell.cellStyle = CellStyle(
        bold: true,
        fontSize: 14,
        backgroundColorHex: '#E8F5E8', // 浅绿色背景
      );
      
      // 🔧 修复问题5：正确解析收款信息数据结构
      // PaymentController.getPaymentData()返回的数据结构是嵌套的
      final paymentInfo = paymentData['payment_info'] ?? paymentData;
      final paymentInfoList = [
        ['收款方式:', paymentInfo['payment_method'] ?? ''],
        ['实收金额:', '¥${paymentInfo['actual_amount'] ?? '0.00'}'],
        ['收款备注:', paymentInfo['payment_remark'] ?? ''],
      ];

      for (int i = 0; i < paymentInfoList.length; i++) {
        final rowIndex = paymentStartRow + 1 + i;
        
        final labelCell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: rowIndex));
        labelCell.value = paymentInfoList[i][0];
        labelCell.cellStyle = CellStyle(bold: true);

        final valueCell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: rowIndex));
        valueCell.value = paymentInfoList[i][1];
      }
    }
  }
  
  /// 设置列宽
  void _setColumnWidths(Sheet sheet) {
    // 注意：excel包2.1.0可能不支持直接设置列宽
    // 列宽将使用Excel的默认自动调整功能
    // 如果需要精确控制列宽，可能需要使用其他Excel库

    // TODO: 如果找到正确的列宽设置方法，可以在这里实现
    // 目前跳过列宽设置，使用默认宽度
    LoggerService.d('📊 Excel列宽设置已跳过，使用默认宽度');
  }
  
  /// 生成文件名
  String _generateFileName() {
    final now = DateTime.now();
    final dateStr = '${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}';
    final timeStr = '${now.hour.toString().padLeft(2, '0')}${now.minute.toString().padLeft(2, '0')}${now.second.toString().padLeft(2, '0')}';
    return '出库单明细_${dateStr}_$timeStr.xlsx';
  }
  
  /// 保存Excel文件
  Future<bool> _saveExcelFile(Excel excel, String fileName) async {
    try {
      LoggerService.d('📊 正在生成Excel文件字节数据...');

      // 获取Excel文件的字节数据
      final List<int>? fileBytes = excel.save();
      if (fileBytes == null) {
        LoggerService.e('❌ 无法生成Excel文件字节数据');
        return false;
      }

      LoggerService.d('✅ Excel文件字节数据生成成功，大小: ${fileBytes.length} bytes');

      // 🔧 修复问题3：所有平台都使用文件选择器，让用户自行选择保存位置
      return await _saveWithFilePicker(fileBytes, fileName);

    } catch (e) {
      LoggerService.e('❌ 保存Excel文件时发生错误: $e');

      // 🔧 增强错误分析
      if (e.toString().contains('save')) {
        LoggerService.e('❌ Excel.save()方法调用失败，可能是Excel包版本问题');
      } else if (e.toString().contains('memory')) {
        LoggerService.e('❌ 内存不足，无法生成Excel文件');
      }

      return false;
    }
  }
  

  
  /// 使用文件选择器保存（其他平台）
  Future<bool> _saveWithFilePicker(List<int> fileBytes, String fileName) async {
    try {
      // 使用saveFile方法获取用户选择的保存路径
      final result = await FilePicker.platform.saveFile(
        dialogTitle: '保存Excel文件',
        fileName: fileName,
        type: FileType.custom,
        allowedExtensions: ['xlsx'],
      );

      if (result != null) {
        // 用户选择了保存路径，将数据写入文件
        final file = File(result);
        await file.writeAsBytes(fileBytes);
        LoggerService.d('✅ 文件已保存到: $result');
        return true;
      } else {
        LoggerService.d('ℹ️ 用户取消了文件保存');
        return false;
      }

    } catch (e) {
      LoggerService.e('❌ 使用文件选择器保存失败: $e');
      return false;
    }
  }
  
  /// 解析字符串为double类型
  double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value) ?? 0.0;
    }
    return 0.0;
  }
}
