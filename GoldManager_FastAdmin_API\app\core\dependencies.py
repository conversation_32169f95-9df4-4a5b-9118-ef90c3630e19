"""
FastAPI依赖项
提供认证、权限验证等依赖注入
"""

from typing import Optional
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session

from .database import get_db
from .security import jwt_manager, permission_manager
from ..models.admin import Admin
from ..schemas.auth import CurrentUserResponse
from ..services.auth_service import AuthService


# HTTP Bearer认证方案
security = HTTPBearer(auto_error=False)


async def get_current_user(
    request: Request,
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: Session = Depends(get_db)
) -> CurrentUserResponse:
    """获取当前认证用户"""

    # 检查是否提供了令牌
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="未提供认证令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # 验证令牌
    token_data = jwt_manager.verify_token(credentials.credentials)
    if not token_data:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # 获取用户信息
    auth_service = AuthService(db)
    try:
        current_user = await auth_service.get_current_user(token_data)
        return current_user
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e),
            headers={"WWW-Authenticate": "Bearer"},
        )


async def get_current_active_user(
    current_user: CurrentUserResponse = Depends(get_current_user)
) -> CurrentUserResponse:
    """获取当前活跃用户"""
    if current_user.status != "normal":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="账户已被禁用"
        )
    return current_user


async def get_current_admin(
    current_user: CurrentUserResponse = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Admin:
    """获取当前管理员对象"""
    admin = db.query(Admin).filter(Admin.id == current_user.id).first()
    if not admin:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="管理员不存在"
        )
    return admin


def require_permission(permission: str):
    """权限验证装饰器"""
    def permission_checker(
        current_user: CurrentUserResponse = Depends(get_current_active_user)
    ):
        if not permission_manager.check_permission(current_user.permissions, permission):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"缺少权限: {permission}"
            )
        return current_user

    return permission_checker


def require_permissions(*permissions: str):
    """多权限验证装饰器"""
    def permissions_checker(
        current_user: CurrentUserResponse = Depends(get_current_active_user)
    ):
        for permission in permissions:
            if not permission_manager.check_permission(current_user.permissions, permission):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"缺少权限: {permission}"
                )
        return current_user

    return permissions_checker


def require_super_admin():
    """超级管理员权限验证"""
    return require_permission("super.admin")


def require_store_access(store_id: Optional[int] = None):
    """门店访问权限验证"""
    def store_checker(
        current_user: CurrentUserResponse = Depends(get_current_active_user)
    ):
        # 超级管理员可以访问所有门店
        if "super.admin" in current_user.permissions:
            return current_user

        # 检查门店权限
        if store_id and current_user.store_id != store_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问该门店数据"
            )

        return current_user

    return store_checker


async def get_optional_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: Session = Depends(get_db)
) -> Optional[CurrentUserResponse]:
    """获取可选的当前用户（不强制要求认证）"""
    if not credentials:
        return None

    token_data = jwt_manager.verify_token(credentials.credentials)
    if not token_data:
        return None

    auth_service = AuthService(db)
    try:
        current_user = await auth_service.get_current_user(token_data)
        return current_user if current_user.status == "normal" else None
    except:
        return None


def get_client_ip(request: Request) -> str:
    """获取客户端IP地址"""
    # 检查代理头
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        return forwarded_for.split(",")[0].strip()

    real_ip = request.headers.get("X-Real-IP")
    if real_ip:
        return real_ip

    # 返回直接连接的IP
    return request.client.host if request.client else "unknown"


class PermissionChecker:
    """权限检查器类"""

    def __init__(self, *permissions: str):
        self.permissions = permissions

    def __call__(
        self,
        current_user: CurrentUserResponse = Depends(get_current_active_user)
    ):
        for permission in self.permissions:
            if not permission_manager.check_permission(current_user.permissions, permission):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"缺少权限: {permission}"
                )
        return current_user


class StoreAccessChecker:
    """门店访问检查器类"""

    def __init__(self, store_id_field: str = "store_id"):
        self.store_id_field = store_id_field

    def __call__(
        self,
        request: Request,
        current_user: CurrentUserResponse = Depends(get_current_active_user)
    ):
        # 超级管理员可以访问所有门店
        if "super.admin" in current_user.permissions:
            return current_user

        # 从路径参数或查询参数获取门店ID
        store_id = None
        if hasattr(request, "path_params") and self.store_id_field in request.path_params:
            store_id = request.path_params[self.store_id_field]
        elif self.store_id_field in request.query_params:
            store_id = request.query_params[self.store_id_field]

        # 检查门店权限
        if store_id and int(store_id) != current_user.store_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问该门店数据"
            )

        return current_user


# 常用权限检查器实例
check_jewelry_view = PermissionChecker("jewelry.view")
check_jewelry_create = PermissionChecker("jewelry.create")
check_jewelry_update = PermissionChecker("jewelry.update")
check_jewelry_delete = PermissionChecker("jewelry.delete")

check_store_view = PermissionChecker("store.view")
check_store_create = PermissionChecker("store.create")
check_store_update = PermissionChecker("store.update")
check_store_delete = PermissionChecker("store.delete")

check_member_view = PermissionChecker("member.view")
check_member_create = PermissionChecker("member.create")
check_member_update = PermissionChecker("member.update")
check_member_delete = PermissionChecker("member.delete")

check_admin_view = PermissionChecker("admin.view")
check_admin_create = PermissionChecker("admin.create")
check_admin_update = PermissionChecker("admin.update")
check_admin_delete = PermissionChecker("admin.delete")

check_stock_view = PermissionChecker("stock.view")
check_stock_in = PermissionChecker("stock.in")
check_stock_out = PermissionChecker("stock.out")
check_stock_return = PermissionChecker("stock.return")
check_stock_check = PermissionChecker("stock.check")
check_stock_transfer = PermissionChecker("stock.transfer")
check_stock_recycle = PermissionChecker("stock.recycle")

check_system_dashboard = PermissionChecker("system.dashboard")
check_system_export = PermissionChecker("system.export")
check_system_import = PermissionChecker("system.import")

check_super_admin = PermissionChecker("super.admin")
