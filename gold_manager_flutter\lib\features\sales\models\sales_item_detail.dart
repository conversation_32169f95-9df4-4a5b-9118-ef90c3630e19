import 'package:gold_manager_flutter/core/utils/safe_converter.dart';

/// 销售明细数据模型
class SalesItemDetail {
  final String orderNo;
  final String salesType;
  final String itemBarcode;
  final String itemName;
  final String itemCategory;
  final double salePrice;
  final double costPrice;
  final double profit;
  final double profitRate;
  final String storeName;
  final String operatorName;
  final DateTime saleTime;
  final String? remarks;
  final Map<String, dynamic>? additionalInfo;

  // 兼容性属性（为视图提供别名）
  String get productName => itemName;
  String get productCode => itemBarcode;
  double get quantity => 1.0; // 默认数量为1
  double get totalAmount => salePrice;
  DateTime get salesDate => saleTime;

  /// 获取格式化的门店显示文本
  /// 普通销售：显示门店名称
  /// 调拨数据：显示"源门店->目标门店"格式
  String get formattedStoreName {
    if (salesType == 'transfer') {
      // 调拨数据：从customer字段提取目标门店信息
      if (additionalInfo != null && additionalInfo!.containsKey('target_store_name')) {
        final targetStore = additionalInfo!['target_store_name'];
        return '$storeName->$targetStore';
      } else {
        // 从customer字段解析目标门店（格式："调拨至 门店名"）
        final customerText = additionalInfo?['customer'] ?? '';
        if (customerText.toString().startsWith('调拨至 ')) {
          final targetStore = customerText.toString().substring(4); // 移除"调拨至 "前缀
          return '$storeName->$targetStore';
        }
      }
      return '$storeName->未知门店';
    } else {
      // 普通销售：直接显示门店名称
      return storeName;
    }
  }

  SalesItemDetail({
    required this.orderNo,
    required this.salesType,
    required this.itemBarcode,
    required this.itemName,
    required this.itemCategory,
    required this.salePrice,
    required this.costPrice,
    required this.profit,
    required this.profitRate,
    required this.storeName,
    required this.operatorName,
    required this.saleTime,
    this.remarks,
    this.additionalInfo,
  });

  /// 从JSON创建实例
  factory SalesItemDetail.fromJson(Map<String, dynamic> json) {
    // 处理调拨数据的附加信息
    Map<String, dynamic>? additionalInfo = json['additional_info'];

    // 如果是调拨类型，将customer信息添加到additionalInfo中
    if (SafeConverter.toStringValue(json['sales_type']) == 'transfer') {
      additionalInfo ??= {};
      additionalInfo['customer'] = json['customer'];
    }

    return SalesItemDetail(
      orderNo: SafeConverter.toStringValue(json['order_no']),
      salesType: SafeConverter.toStringValue(json['sales_type']),
      // 支持多种字段名映射
      itemBarcode: SafeConverter.toStringValue(json['barcode'] ?? json['item_barcode']),
      itemName: SafeConverter.toStringValue(json['jewelry_name'] ?? json['item_name']),
      itemCategory: SafeConverter.toStringValue(json['category_name'] ?? json['item_category']),
      salePrice: SafeConverter.toDouble(json['sale_price']),
      costPrice: SafeConverter.toDouble(json['cost_price']),
      profit: SafeConverter.toDouble(json['profit']),
      profitRate: SafeConverter.toDouble(json['profit_rate']),
      storeName: SafeConverter.toStringValue(json['store_name']),
      operatorName: SafeConverter.toStringValue(json['operator_name']),
      saleTime: SafeConverter.toDateTime(json['sale_time']) ?? DateTime.now(),
      remarks: json['remarks'] ?? json['remark'],
      additionalInfo: additionalInfo,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'order_no': orderNo,
      'sales_type': salesType,
      'item_barcode': itemBarcode,
      'item_name': itemName,
      'item_category': itemCategory,
      'sale_price': salePrice,
      'cost_price': costPrice,
      'profit': profit,
      'profit_rate': profitRate,
      'store_name': storeName,
      'operator_name': operatorName,
      'sale_time': saleTime.millisecondsSinceEpoch ~/ 1000,
      'remarks': remarks,
      'additional_info': additionalInfo,
    };
  }

  /// 创建副本
  SalesItemDetail copyWith({
    String? orderNo,
    String? salesType,
    String? itemBarcode,
    String? itemName,
    String? itemCategory,
    double? salePrice,
    double? costPrice,
    double? profit,
    double? profitRate,
    String? storeName,
    String? operatorName,
    DateTime? saleTime,
    String? remarks,
    Map<String, dynamic>? additionalInfo,
  }) {
    return SalesItemDetail(
      orderNo: orderNo ?? this.orderNo,
      salesType: salesType ?? this.salesType,
      itemBarcode: itemBarcode ?? this.itemBarcode,
      itemName: itemName ?? this.itemName,
      itemCategory: itemCategory ?? this.itemCategory,
      salePrice: salePrice ?? this.salePrice,
      costPrice: costPrice ?? this.costPrice,
      profit: profit ?? this.profit,
      profitRate: profitRate ?? this.profitRate,
      storeName: storeName ?? this.storeName,
      operatorName: operatorName ?? this.operatorName,
      saleTime: saleTime ?? this.saleTime,
      remarks: remarks ?? this.remarks,
      additionalInfo: additionalInfo ?? this.additionalInfo,
    );
  }

  @override
  String toString() {
    return 'SalesItemDetail(orderNo: $orderNo, salesType: $salesType, itemName: $itemName, profit: $profit)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SalesItemDetail &&
        other.orderNo == orderNo &&
        other.itemBarcode == itemBarcode;
  }

  @override
  int get hashCode {
    return orderNo.hashCode ^ itemBarcode.hashCode;
  }
}

/// 销售类型枚举
enum SalesType {
  all('全部', 'all'),
  retail('零售', 'retail'),
  wholesale('批发', 'wholesale'),
  transfer('调拨', 'transfer'),
  recycling('回收变现', 'recycling');

  const SalesType(this.displayName, this.value);

  final String displayName;
  final String value;

  /// 从字符串值获取枚举
  static SalesType fromValue(String value) {
    return SalesType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => SalesType.all,
    );
  }

  /// 获取所有销售类型选项
  static List<SalesType> get allTypes => SalesType.values;
}