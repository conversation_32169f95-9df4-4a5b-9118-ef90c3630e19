/// 安全的数据类型转换工具类
/// 提供健壮的类型转换方法，避免运行时类型错误
class SafeConverter {
  /// 安全地将动态类型转换为double
  /// 支持从 int, double, String 转换
  static double toDouble(dynamic value, {double defaultValue = 0.0}) {
    if (value == null) return defaultValue;
    
    if (value is double) return value;
    if (value is int) return value.toDouble();
    
    if (value is String) {
      if (value.isEmpty) return defaultValue;
      
      // 尝试解析字符串
      final parsed = double.tryParse(value);
      if (parsed != null) return parsed;
      
      // 如果包含逗号，尝试替换为点号再解析
      if (value.contains(',')) {
        final normalizedValue = value.replaceAll(',', '.');
        final parsedNormalized = double.tryParse(normalizedValue);
        if (parsedNormalized != null) return parsedNormalized;
      }
      
      return defaultValue;
    }
    
    return defaultValue;
  }

  /// 安全地将动态类型转换为int
  /// 支持从 int, double, String 转换
  static int toInt(dynamic value, {int defaultValue = 0}) {
    if (value == null) return defaultValue;
    
    if (value is int) return value;
    if (value is double) return value.round();
    
    if (value is String) {
      if (value.isEmpty) return defaultValue;
      
      // 尝试解析字符串
      final parsed = int.tryParse(value);
      if (parsed != null) return parsed;
      
      // 尝试先转换为double再转为int
      final doubleValue = toDouble(value);
      return doubleValue.round();
    }
    
    return defaultValue;
  }

  /// 安全地将动态类型转换为String
  static String toStringValue(dynamic value, {String defaultValue = ''}) {
    if (value == null) return defaultValue;
    return value.toString();
  }

  /// 安全地将动态类型转换为bool
  static bool toBool(dynamic value, {bool defaultValue = false}) {
    if (value == null) return defaultValue;
    
    if (value is bool) return value;
    
    if (value is String) {
      final lowerValue = value.toLowerCase();
      if (lowerValue == 'true' || lowerValue == '1' || lowerValue == 'yes') {
        return true;
      }
      if (lowerValue == 'false' || lowerValue == '0' || lowerValue == 'no') {
        return false;
      }
    }
    
    if (value is int) {
      return value != 0;
    }
    
    if (value is double) {
      return value != 0.0;
    }
    
    return defaultValue;
  }

  /// 安全地将动态类型转换为DateTime
  static DateTime? toDateTime(dynamic value) {
    if (value == null) return null;
    
    if (value is DateTime) return value;
    
    if (value is String) {
      if (value.isEmpty) return null;
      
      // 尝试解析ISO格式
      try {
        return DateTime.parse(value);
      } catch (e) {
        // 尝试其他常见格式
        final formats = [
          RegExp(r'^\d{4}-\d{2}-\d{2}$'), // YYYY-MM-DD
          RegExp(r'^\d{4}/\d{2}/\d{2}$'), // YYYY/MM/DD
          RegExp(r'^\d{2}/\d{2}/\d{4}$'), // MM/DD/YYYY
        ];
        
        for (final format in formats) {
          if (format.hasMatch(value)) {
            try {
              return DateTime.parse(value.replaceAll('/', '-'));
            } catch (e) {
              continue;
            }
          }
        }
      }
    }
    
    if (value is int) {
      // 假设是时间戳（毫秒）
      try {
        return DateTime.fromMillisecondsSinceEpoch(value);
      } catch (e) {
        // 可能是秒级时间戳
        try {
          return DateTime.fromMillisecondsSinceEpoch(value * 1000);
        } catch (e) {
          return null;
        }
      }
    }
    
    return null;
  }

  /// 安全地从Map中获取值并转换类型
  static T? getFromMap<T>(Map<String, dynamic>? map, String key, T Function(dynamic) converter) {
    if (map == null || !map.containsKey(key)) return null;
    
    try {
      return converter(map[key]);
    } catch (e) {
      return null;
    }
  }

  /// 安全地从Map中获取double值
  static double getDoubleFromMap(Map<String, dynamic>? map, String key, {double defaultValue = 0.0}) {
    return getFromMap(map, key, (value) => toDouble(value, defaultValue: defaultValue)) ?? defaultValue;
  }

  /// 安全地从Map中获取int值
  static int getIntFromMap(Map<String, dynamic>? map, String key, {int defaultValue = 0}) {
    return getFromMap(map, key, (value) => toInt(value, defaultValue: defaultValue)) ?? defaultValue;
  }

  /// 安全地从Map中获取String值
  static String getStringFromMap(Map<String, dynamic>? map, String key, {String defaultValue = ''}) {
    return getFromMap(map, key, (value) => toStringValue(value, defaultValue: defaultValue)) ?? defaultValue;
  }

  /// 安全地从Map中获取bool值
  static bool getBoolFromMap(Map<String, dynamic>? map, String key, {bool defaultValue = false}) {
    return getFromMap(map, key, (value) => toBool(value, defaultValue: defaultValue)) ?? defaultValue;
  }

  /// 安全地从Map中获取DateTime值
  static DateTime? getDateTimeFromMap(Map<String, dynamic>? map, String key) {
    final value = map?[key];
    return toDateTime(value);
  }

  /// 安全地从Map中获取List值
  static List<T> getListFromMap<T>(
    Map<String, dynamic>? map, 
    String key, 
    T Function(dynamic) converter, {
    List<T>? defaultValue,
  }) {
    if (map == null || !map.containsKey(key)) {
      return defaultValue ?? <T>[];
    }
    
    final value = map[key];
    if (value is! List) {
      return defaultValue ?? <T>[];
    }
    
    final result = <T>[];
    for (final item in value) {
      try {
        result.add(converter(item));
      } catch (e) {
        // 跳过无法转换的项目
        continue;
      }
    }
    
    return result;
  }

  /// 格式化金额显示
  static String formatAmount(double amount, {int decimalPlaces = 2}) {
    return amount.toStringAsFixed(decimalPlaces);
  }

  /// 格式化重量显示
  static String formatWeight(double weight, {int decimalPlaces = 3}) {
    return weight.toStringAsFixed(decimalPlaces);
  }

  /// 验证并清理数字字符串
  static String cleanNumericString(String input) {
    if (input.isEmpty) return '0';
    
    // 移除所有非数字、小数点、负号的字符
    String cleaned = input.replaceAll(RegExp(r'[^\d.-]'), '');
    
    // 确保只有一个小数点
    final parts = cleaned.split('.');
    if (parts.length > 2) {
      cleaned = '${parts[0]}.${parts.sublist(1).join('')}';
    }
    
    // 确保负号只在开头
    if (cleaned.contains('-')) {
      final isNegative = cleaned.startsWith('-');
      cleaned = cleaned.replaceAll('-', '');
      if (isNegative) {
        cleaned = '-$cleaned';
      }
    }
    
    return cleaned.isEmpty ? '0' : cleaned;
  }
}