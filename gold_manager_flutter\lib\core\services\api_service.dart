import 'package:dio/dio.dart' as dio;
import 'package:get/get.dart';

import '../config/app_config.dart';
import '../utils/logger_service.dart';
import 'storage_service.dart';

/// API服务类
/// 处理与后端API的所有网络通信
class ApiService extends GetxService {
  late dio.Dio _dio;

  @override
  void onInit() {
    super.onInit();
    _initDio();
    LoggerService.i('ApiService initialized');
  }

  /// 初始化Dio客户端
  void _initDio() {
    _dio = dio.Dio(
      dio.BaseOptions(
        baseUrl: AppConfig.apiBaseUrl,
        connectTimeout: const Duration(milliseconds: AppConfig.connectTimeout),
        receiveTimeout: const Duration(milliseconds: AppConfig.receiveTimeout),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ),
    );

    // 添加拦截器处理请求、响应和错误
    _dio.interceptors.add(
      dio.InterceptorsWrapper(
        onRequest: (options, handler) {
          // 从存储服务获取token并添加到请求头
          final token = Get.find<StorageService>().getToken();
          if (token != null && token.isNotEmpty) {
            options.headers['Authorization'] = 'Bearer $token';
          }
          return handler.next(options);
        },
        onResponse: (response, handler) {
          // 处理响应
          return handler.next(response);
        },
        onError: (dio.DioException e, handler) {
          // 处理错误
          LoggerService.e('API Error', e);
          return handler.next(e);
        },
      ),
    );
  }

  /// 执行GET请求
  Future<dio.Response> get(
    String path, {
    Map<String, dynamic>? queryParameters,
    dio.Options? options,
  }) async {
    try {
      return await _dio.get(
        path,
        queryParameters: queryParameters,
        options: options,
      );
    } catch (e) {
      LoggerService.e('GET请求错误: $path', e);
      rethrow;
    }
  }

  /// 执行POST请求
  Future<dio.Response> post(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    dio.Options? options,
  }) async {
    try {
      return await _dio.post(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
    } catch (e) {
      LoggerService.e('POST请求错误: $path', e);
      rethrow;
    }
  }

  /// 执行PUT请求
  Future<dio.Response> put(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    dio.Options? options,
  }) async {
    try {
      return await _dio.put(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
    } catch (e) {
      LoggerService.e('PUT请求错误: $path', e);
      rethrow;
    }
  }

  /// 执行DELETE请求
  Future<dio.Response> delete(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    dio.Options? options,
  }) async {
    try {
      return await _dio.delete(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
    } catch (e) {
      LoggerService.e('DELETE请求错误: $path', e);
      rethrow;
    }
  }

  /// 执行PATCH请求
  Future<dio.Response> patch(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    dio.Options? options,
  }) async {
    try {
      return await _dio.patch(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
    } catch (e) {
      LoggerService.e('PATCH请求错误: $path', e);
      rethrow;
    }
  }

  /// 上传文件
  Future<dio.Response> upload(
    String path, {
    required Map<String, dynamic> formData,
    Map<String, dynamic>? queryParameters,
    dio.Options? options,
    dio.ProgressCallback? onSendProgress,
  }) async {
    try {
      options ??= dio.Options(contentType: 'multipart/form-data');

      final dioInstance = dio.Dio(
        dio.BaseOptions(
          baseUrl: _dio.options.baseUrl,
          connectTimeout: _dio.options.connectTimeout,
          receiveTimeout: _dio.options.receiveTimeout,
          headers: _dio.options.headers,
        ),
      );

      // 需要应用相同的拦截器
      dioInstance.interceptors.addAll(_dio.interceptors);

      return await dioInstance.post(
        path,
        data: dio.FormData.fromMap(formData),
        queryParameters: queryParameters,
        options: options,
        onSendProgress: onSendProgress,
      );
    } catch (e) {
      LoggerService.e('上传文件错误: $path', e);
      rethrow;
    }
  }

  /// 创建MultipartFile
  Future<dio.MultipartFile> createMultipartFile(String filePath) async {
    return await dio.MultipartFile.fromFile(filePath);
  }

  /// 处理API响应
  T handleResponse<T>(
    dio.Response response,
    T Function(dynamic data) converter,
  ) {
    // 确保响应状态码为成功
    if (response.statusCode != 200) {
      throw dio.DioException(
        requestOptions: response.requestOptions,
        response: response,
        error: 'HTTP错误: ${response.statusCode}',
      );
    }

    final data = response.data;

    // 处理特定API格式的响应
    if (data is Map<String, dynamic>) {
      if (data.containsKey('code')) {
        if (data['code'] == 0) {
          return converter(data['data']);
        } else {
          throw dio.DioException(
            requestOptions: response.requestOptions,
            response: response,
            error: data['msg'] ?? '未知错误',
          );
        }
      }
    }

    // 如果没有特定格式，直接转换数据
    return converter(data);
  }
}
