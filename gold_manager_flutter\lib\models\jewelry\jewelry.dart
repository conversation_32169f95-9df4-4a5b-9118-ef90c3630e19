import '../common/date_utils.dart';
import '../common/enums.dart';
import '../store/store.dart';
import 'jewelry_category.dart';
import 'jewelry_image.dart';

/// 首饰模型
class Jewelry {
  /// 首饰ID
  final int id;

  /// 条码
  final String barcode;

  /// 名称
  final String name;

  /// 类别ID
  final int categoryId;

  /// 圈口号
  final String? ringSize;

  /// 金重(g)
  final double goldWeight;

  /// 金价(元/g)
  final double goldPrice;

  /// 银重(g)
  final double silverWeight;

  /// 银价(元/g)
  final double silverPrice;

  /// 总重(g) - 对应数据库total_weight字段
  final double totalWeight;

  /// 工费(元) - 对应数据库silver_work_price字段
  final double workPrice;

  /// 零售工费(元) - 对应数据库retail_work_price字段
  final double retailWorkPrice;

  /// 批发工费(元) - 对应数据库wholesale_work_price字段
  final double wholesaleWorkPrice;

  /// 件工费(元) - 对应数据库piece_work_price字段
  final double pieceWorkPrice;

  /// 销售价格(元)
  final double salePrice;

  /// 所属门店ID
  final int storeId;

  /// 状态
  final JewelryStatus status;

  /// 创建时间
  final DateTime createTime;

  /// 关联的门店
  final Store? store;

  /// 关联的分类
  final JewelryCategory? category;

  /// 关联的图片
  final List<JewelryImage>? images;

  /// 构造函数
  const Jewelry({
    required this.id,
    required this.barcode,
    required this.name,
    required this.categoryId,
    this.ringSize,
    required this.goldWeight,
    required this.goldPrice,
    required this.silverWeight,
    required this.silverPrice,
    required this.totalWeight,
    required this.workPrice,
    required this.retailWorkPrice,
    required this.wholesaleWorkPrice,
    required this.pieceWorkPrice,
    required this.salePrice,
    required this.storeId,
    required this.status,
    required this.createTime,
    this.store,
    this.category,
    this.images,
  });

  /// 安全转换为double
  static double _toDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value) ?? 0.0;
    }
    return 0.0;
  }

  /// 从JSON构造
  factory Jewelry.fromJson(Map<String, dynamic> json) {
    // 🔧 修复分类处理逻辑：优先使用完整的category对象，如果没有则从category_name创建简单对象
    JewelryCategory? category;
    if (json['category'] != null) {
      // 如果有完整的category对象，直接使用
      category = JewelryCategory.fromJson(json['category']);
    } else if (json['category_name'] != null && json['category_name'].toString().isNotEmpty) {
      // 如果只有category_name，创建一个简单的JewelryCategory对象
      category = JewelryCategory(
        id: json['category_id'] ?? 0,
        name: json['category_name'] ?? '',
        parentId: 0, // 默认值
      );
    }

    // 🔧 修复门店处理逻辑：优先使用完整的store对象，如果没有则从store_name创建简单对象
    Store? store;
    if (json['store'] != null) {
      // 如果有完整的store对象，直接使用
      store = Store.fromJson(json['store']);
    } else if (json['store_name'] != null && json['store_name'].toString().isNotEmpty) {
      // 如果只有store_name，创建一个简单的Store对象
      store = Store(
        id: json['store_id'] ?? 0,
        name: json['store_name'] ?? '',
      );
    }

    return Jewelry(
      id: json['id'] ?? 0,
      barcode: json['barcode'] ?? '',
      name: json['name'] ?? '',
      categoryId: json['category_id'] ?? 0,
      ringSize: json['ring_size'],
      goldWeight: _toDouble(json['gold_weight']),
      goldPrice: _toDouble(json['gold_price']),
      silverWeight: _toDouble(json['silver_weight']),
      silverPrice: _toDouble(json['silver_price']),
      totalWeight: _toDouble(json['total_weight']),
      workPrice: _toDouble(json['silver_work_price'] ?? json['work_price']),
      retailWorkPrice: _toDouble(json['retail_work_price']),
      wholesaleWorkPrice: _toDouble(json['wholesale_work_price']),
      pieceWorkPrice: _toDouble(json['piece_work_price']),
      salePrice: _toDouble(json['sale_price']),
      storeId: json['store_id'] ?? 0,
      status: JewelryStatus.fromValue(json['status'] ?? 1),
      createTime: DateUtil.fromUnixTimestamp(json['createtime'] ?? json['create_time']) ?? DateTime.now(),
      store: store, // 使用修复后的门店对象
      category: category, // 使用修复后的分类对象
      images: json['images'] != null
          ? (json['images'] as List).map((item) => JewelryImage.fromJson(item)).toList()
          : null,
    );
  }

  /// 转为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'barcode': barcode,
      'name': name,
      'category_id': categoryId,
      'gold_weight': goldWeight,
      'silver_weight': silverWeight,
      'work_price': workPrice,
      'sale_price': salePrice,
      'store_id': storeId,
      'status': status.value,
      'create_time': DateUtil.toUnixTimestamp(createTime),
    };
  }

  /// 获取分类名称
  String get categoryName {
    if (category?.name != null && category!.name.isNotEmpty) {
      return category!.name;
    }
    return '未知分类';
  }

  /// 获取总成本
  double get totalCost {
    // 使用实际的金价和银价计算成本: 金重*金价 + 银重*银价 + 工费
    return goldWeight * goldPrice + silverWeight * silverPrice + workPrice;
  }

  /// 复制并修改属性
  Jewelry copyWith({
    int? id,
    String? barcode,
    String? name,
    int? categoryId,
    String? ringSize,
    double? goldWeight,
    double? goldPrice,
    double? silverWeight,
    double? silverPrice,
    double? totalWeight,
    double? workPrice,
    double? retailWorkPrice,
    double? wholesaleWorkPrice,
    double? pieceWorkPrice,
    double? salePrice,
    int? storeId,
    JewelryStatus? status,
    DateTime? createTime,
    Store? store,
    JewelryCategory? category,
    List<JewelryImage>? images,
  }) {
    return Jewelry(
      id: id ?? this.id,
      barcode: barcode ?? this.barcode,
      name: name ?? this.name,
      categoryId: categoryId ?? this.categoryId,
      ringSize: ringSize ?? this.ringSize,
      goldWeight: goldWeight ?? this.goldWeight,
      goldPrice: goldPrice ?? this.goldPrice,
      silverWeight: silverWeight ?? this.silverWeight,
      silverPrice: silverPrice ?? this.silverPrice,
      totalWeight: totalWeight ?? this.totalWeight,
      workPrice: workPrice ?? this.workPrice,
      retailWorkPrice: retailWorkPrice ?? this.retailWorkPrice,
      wholesaleWorkPrice: wholesaleWorkPrice ?? this.wholesaleWorkPrice,
      pieceWorkPrice: pieceWorkPrice ?? this.pieceWorkPrice,
      salePrice: salePrice ?? this.salePrice,
      storeId: storeId ?? this.storeId,
      status: status ?? this.status,
      createTime: createTime ?? this.createTime,
      store: store ?? this.store,
      category: category ?? this.category,
      images: images ?? this.images,
    );
  }
}