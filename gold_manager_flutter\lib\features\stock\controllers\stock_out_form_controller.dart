import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:uuid/uuid.dart';

import '../../../core/utils/logger.dart';

import '../../../models/stock/stock_out.dart';
import '../../../models/stock/stock_out_item.dart';
import '../../../models/common/document_status.dart';
import '../../../models/common/enums.dart';
import '../../../models/store/store.dart';
import '../../../models/jewelry/jewelry.dart';
import '../../../models/recycling/old_material_item.dart';
import '../../../services/stock_service.dart';
import '../../../services/store_service.dart';
import '../../../services/jewelry_service.dart';
import '../../../services/auth_service.dart';
import '../widgets/old_material_recycling_dialog.dart';
import '../widgets/return_exchange_dialog.dart';
import '../widgets/single_work_fee_dialog.dart';

/// 出库单表单控制器
class StockOutFormController extends GetxController {
  // 服务
  final StockService _stockService = Get.find<StockService>();
  final StoreService _storeService = Get.find<StoreService>();
  final JewelryService _jewelryService = Get.find<JewelryService>();
  final AuthService _authService = Get.find<AuthService>();
  
  // 表单状态
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  final RxBool isLoading = false.obs;
  final RxBool isEditing = false.obs;
  
  // 表单控制器
  final TextEditingController customerController = TextEditingController();
  final TextEditingController remarkController = TextEditingController();
  
  // 数据
  final RxList<Store> storeList = <Store>[].obs;
  final RxList<Jewelry> jewelryList = <Jewelry>[].obs;
  final RxList<StockOutItem> itemList = <StockOutItem>[].obs;
  final Rx<StockOut?> currentStockOut = Rx<StockOut?>(null);
  
  // 选择状态
  final RxInt selectedStoreId = 0.obs;
  final RxString selectedSaleType = 'retail'.obs;
  final RxDouble totalAmount = 0.0.obs;
  final RxDouble totalWeight = 0.0.obs;
  final RxDouble totalGoldWeight = 0.0.obs;
  final RxDouble totalSilverWeight = 0.0.obs;
  
  // 销售类型选项
  final List<Map<String, String>> saleTypeOptions = [
    {'value': 'retail', 'label': '零售'},
    {'value': 'wholesale', 'label': '批发'},
  ];

  // 回调函数
  VoidCallback? onSaveSuccess;
  
  @override
  void onInit() {
    super.onInit();
    LoggerService.d('StockOutFormController 初始化');

    // 🔄 监听销售类型变化，实时更新工费显示
    selectedSaleType.listen((saleType) {
      LoggerService.d('🔄 销售类型变更为: $saleType');
      _updateWorkPricesForSaleType();
    });

    // 检查是否是编辑模式
    final arguments = Get.arguments;
    if (arguments != null && arguments['id'] != null) {
      isEditing.value = true;
      _loadStockOutData(arguments['id']);
    }

    _initializeData();
  }
  
  @override
  void onClose() {
    customerController.dispose();
    remarkController.dispose();
    super.onClose();
  }

  /// 设置编辑模式（用于标签页模式）
  void setEditMode(int stockOutId) {
    LoggerService.d('设置编辑模式，出库单ID: $stockOutId');
    isEditing.value = true;
    _loadStockOutData(stockOutId);
  }

  /// 重置表单到初始状态
  void resetForm() {
    // 清空表单输入
    customerController.clear();
    remarkController.clear();

    // 重置选择状态
    selectedStoreId.value = 0;
    selectedSaleType.value = 'retail';

    // 清空商品明细列表
    itemList.clear();

    // 重置计算结果
    totalAmount.value = 0.0;
    totalWeight.value = 0.0;
    totalGoldWeight.value = 0.0;
    totalSilverWeight.value = 0.0;

    // 重置编辑状态
    isEditing.value = false;
    currentStockOut.value = null;

    LoggerService.d('✅ 出库单表单已重置');
  }
  
  /// 初始化数据
  Future<void> _initializeData() async {
    await fetchStores();
    await fetchJewelryList();
    
    // 如果用户不是管理员，自动选择用户所属门店
    if (_authService.userRole.value != 'admin') {
      selectedStoreId.value = _authService.storeId.value;
    }
  }
  
  /// 获取门店列表
  Future<void> fetchStores() async {
    try {
      // 使用getAllStores确保获取所有门店，不受分页限制
      final stores = await _storeService.getAllStores();
      storeList.value = stores;
    } catch (e) {
      LoggerService.e('获取门店列表失败', e);
      Get.snackbar(
        '错误',
        '获取门店列表失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
  
  /// 获取首饰列表
  Future<void> fetchJewelryList() async {
    try {
      final result = await _jewelryService.getJewelryList({
        'page': 1,
        'page_size': 100,
        'status': 1, // 只获取上架的首饰
      });
      jewelryList.value = result.data;
    } catch (e) {
      LoggerService.e('获取首饰列表失败', e);
    }
  }
  
  /// 加载出库单数据（编辑模式）
  Future<void> _loadStockOutData(int id) async {
    try {
      isLoading.value = true;
      final stockOut = await _stockService.getStockOutById(id);
      currentStockOut.value = stockOut;
      
      // 填充表单数据
      selectedStoreId.value = stockOut.storeId;
      customerController.text = stockOut.customer ?? '';
      selectedSaleType.value = stockOut.saleType ?? 'retail';
      remarkController.text = stockOut.remark ?? '';
      
      // 填充明细数据
      if (stockOut.items != null) {
        itemList.value = stockOut.items!;
        _calculateTotals();
      }
    } catch (e) {
      LoggerService.e('加载出库单数据失败', e);
      Get.snackbar(
        '错误',
        '加载出库单数据失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }
  
  /// 添加商品明细
  void addItem(Jewelry jewelry, double amount) {
    final item = StockOutItem(
      id: 0,
      stockOutId: 0,
      jewelryId: jewelry.id,
      barcode: jewelry.barcode,
      amount: amount,
      jewelry: jewelry,
    );
    
    itemList.add(item);
    _calculateTotals();
  }
  
  /// 移除商品明细
  void removeItem(int index) {
    if (index >= 0 && index < itemList.length) {
      itemList.removeAt(index);
      _calculateTotals();
    }
  }
  
  /// 更新商品明细数量
  void updateItemAmount(int index, double amount) {
    if (index >= 0 && index < itemList.length) {
      final item = itemList[index];
      itemList[index] = StockOutItem(
        id: item.id,
        stockOutId: item.stockOutId,
        jewelryId: item.jewelryId,
        barcode: item.barcode,
        amount: amount,
        jewelry: item.jewelry,
      );
      _calculateTotals();
    }
  }
  
  /// 🔄 计算状态标志，防止循环触发
  bool _isCalculating = false;

  /// 🔄 UI更新回调函数（由View层设置）
  Function(int index, String fieldType, String newValue)? _uiUpdateCallback;

  /// 🔄 设置UI更新回调
  void setUIUpdateCallback(Function(int index, String fieldType, String newValue)? callback) {
    _uiUpdateCallback = callback;
  }

  /// 🔄 更新商品明细字段
  /// 支持正向计算（金价/银价 → 总价格）和反向计算（总价格 → 金价）
  void updateItemField(int index, {
    double? amount,
    double? goldPrice,
    double? silverPrice,
    double? workPrice,
    double? pieceWorkPrice
  }) {
    if (index >= 0 && index < itemList.length && !_isCalculating) {
      _isCalculating = true; // 🔒 设置计算状态，防止循环触发

      try {
        final item = itemList[index];
        final oldJewelry = item.jewelry;

        // 🔄 区分正向计算和反向计算
        if (goldPrice != null || silverPrice != null || workPrice != null || pieceWorkPrice != null) {
          // 🧮 正向计算：用户修改了金价、银价、工费或件工费，需要重新计算商品价格
          LoggerService.d('🧮 正向计算触发 - 用户修改了价格参数');

          final updatedJewelry = oldJewelry?.copyWith(
            goldPrice: goldPrice ?? oldJewelry.goldPrice,
            silverPrice: silverPrice ?? oldJewelry.silverPrice,
            workPrice: workPrice ?? oldJewelry.workPrice,
            pieceWorkPrice: pieceWorkPrice ?? oldJewelry.pieceWorkPrice,
          );

          // 🔑 关键修复：正向计算时，总是重新计算价格，忽略传入的amount参数
          double calculatedAmount;
          if (updatedJewelry != null) {
            calculatedAmount = _calculateItemPrice(updatedJewelry);
            LoggerService.d('🧮 正向计算完成，新价格: ${calculatedAmount.round()}元');
          } else {
            calculatedAmount = item.amount;
            LoggerService.w('⚠️ 无法获取商品信息，保持原价格');
          }

          // 使用新的珠宝对象和计算后的价格创建更新的条目
          itemList[index] = StockOutItem(
            id: item.id,
            stockOutId: item.stockOutId,
            jewelryId: item.jewelryId,
            barcode: item.barcode,
            amount: calculatedAmount,
            jewelry: updatedJewelry,
          );

          // 🔄 通知UI更新价格字段
          _notifyUIUpdate(index, 'amount', calculatedAmount.round().toString());

        } else if (amount != null) {
          // 🔄 反向计算：用户只修改了价格，需要反向计算金价
          LoggerService.d('🔄 反向计算触发 - 用户修改了商品价格: ${amount.round()}元');

          if (oldJewelry != null) {
            final updatedJewelry = _reverseCalculateGoldPrice(oldJewelry, amount);
            LoggerService.d('🔄 反向计算完成，新金价: ${updatedJewelry.goldPrice.round()}元/g');

            itemList[index] = StockOutItem(
              id: item.id,
              stockOutId: item.stockOutId,
              jewelryId: item.jewelryId,
              barcode: item.barcode,
              amount: amount,
              jewelry: updatedJewelry,
            );

            // 🔄 通知UI更新金价字段
            _notifyUIUpdate(index, 'goldPrice', updatedJewelry.goldPrice.round().toString());

          } else {
            // 如果没有珠宝对象，只更新金额
            LoggerService.w('⚠️ 无法获取商品信息，只更新价格');
            itemList[index] = StockOutItem(
              id: item.id,
              stockOutId: item.stockOutId,
              jewelryId: item.jewelryId,
              barcode: item.barcode,
              amount: amount,
              jewelry: item.jewelry,
            );
          }
        }

        // 重新计算总计
        _calculateTotals();

        // 🔄 延迟通知UI更新，确保计算完成后再更新界面
        Future.delayed(const Duration(milliseconds: 100), () {
          itemList.refresh(); // 强制刷新列表，确保UI更新
        });

      } finally {
        _isCalculating = false; // 🔓 释放计算状态
      }
    }
  }

  /// 🔄 通知UI更新指定字段
  void _notifyUIUpdate(int index, String fieldType, String newValue) {
    if (_uiUpdateCallback != null) {
      // 🔄 延迟执行UI更新，避免与当前计算冲突
      Future.delayed(const Duration(milliseconds: 50), () {
        _uiUpdateCallback!(index, fieldType, newValue);
      });
    }
  }
  
  /// 计算总计
  /// 🎯 精确调整：分别处理不同汇总项的计算逻辑
  void _calculateTotals() {
    // 总金额：包含所有商品（正常商品 + 回收商品 + 退换货商品 + 工费项目）
    double totalAmountValue = 0.0;

    // 其他汇总项：只统计正常商品和工费项目，排除回收商品和退换货商品
    double weight = 0.0;
    double goldWeight = 0.0;
    double silverWeight = 0.0;

    for (final item in itemList) {
      // 总金额：包含所有商品（包括回收商品和退换货商品的负数金额，以及工费项目）
      totalAmountValue += item.amount;

      // 其他汇总项：统计正常商品和工费项目，排除回收商品和退换货商品
      if (!_isRecyclingItem(item) && !_isReturnExchangeItem(item) && item.jewelry != null) {
        // 计算总重量（金重+银重）
        weight += (item.jewelry!.goldWeight + item.jewelry!.silverWeight);
        // 分别计算金重和银重总和
        goldWeight += item.jewelry!.goldWeight;
        silverWeight += item.jewelry!.silverWeight;
      }
    }

    totalAmount.value = totalAmountValue;
    totalWeight.value = weight;
    totalGoldWeight.value = goldWeight;
    totalSilverWeight.value = silverWeight;
  }

  /// 检查是否为回收商品
  /// 回收商品的识别标准：jewelryId为0，价格为负数，商品名称包含"回收-"
  bool _isRecyclingItem(StockOutItem item) {
    return item.jewelryId == 0 &&
           item.amount < 0 &&
           (item.jewelry?.name.startsWith('回收-') == true);
  }

  /// 🧮 计算商品价格
  /// 计算公式：总价格 = 金重 × 金价 + 银重 × 银价 + 总重 × 工费 + 件工费
  /// 🔧 修复：直接使用商品当前的工费，而不是根据销售类型重新获取
  double _calculateItemPrice(Jewelry jewelry) {
    final goldCost = jewelry.goldWeight * jewelry.goldPrice;
    final silverCost = jewelry.silverWeight * jewelry.silverPrice;

    // 🔑 使用数据库中的总重量字段，而不是计算值
    final totalWeight = jewelry.totalWeight;

    // 🔧 修复：直接使用商品当前的工费，保持用户编辑的值
    final currentWorkPrice = jewelry.workPrice;
    final workCost = totalWeight * currentWorkPrice;
    final pieceWorkCost = jewelry.pieceWorkPrice;

    final totalPrice = goldCost + silverCost + workCost + pieceWorkCost;

    LoggerService.d('💰 价格计算明细:');
    LoggerService.d('   金重: ${jewelry.goldWeight}g × 金价: ${jewelry.goldPrice}元/g = ${goldCost.round()}元');
    LoggerService.d('   银重: ${jewelry.silverWeight}g × 银价: ${jewelry.silverPrice}元/g = ${silverCost.round()}元');
    LoggerService.d('   总重: ${totalWeight}g × 工费: $currentWorkPrice元/g = ${workCost.round()}元');
    LoggerService.d('   件工费: ${pieceWorkCost.round()}元');
    LoggerService.d('   总价格: ${totalPrice.round()}元');

    return totalPrice;
  }

  /// 🔄 反向计算金价
  /// 当用户手动修改总价格时，根据公式自动调整金价，保持其他参数不变
  /// 公式变形：金价 = (总价格 - 银重 × 银价 - 总重 × 工费 - 件工费) ÷ 金重
  /// 🔧 修复：直接使用商品当前的工费，保持用户编辑的值
  Jewelry _reverseCalculateGoldPrice(Jewelry jewelry, double targetPrice) {
    if (jewelry.goldWeight <= 0) {
      // 如果金重为0，无法反向计算金价，返回原对象
      LoggerService.w('⚠️ 金重为0，无法反向计算金价');
      return jewelry;
    }

    final silverCost = jewelry.silverWeight * jewelry.silverPrice;

    // 🔑 使用数据库中的总重量字段，而不是计算值
    final totalWeight = jewelry.totalWeight;

    // 🔧 修复：直接使用商品当前的工费，保持用户编辑的值
    final currentWorkPrice = jewelry.workPrice;
    final workCost = totalWeight * currentWorkPrice;
    final pieceWorkCost = jewelry.pieceWorkPrice;

    // 计算新的金价
    final newGoldPrice = (targetPrice - silverCost - workCost - pieceWorkCost) / jewelry.goldWeight;

    // 确保金价不为负数
    final adjustedGoldPrice = newGoldPrice > 0 ? newGoldPrice : 0.0;

    LoggerService.d('🔄 反向计算金价:');
    LoggerService.d('   目标价格: ${targetPrice.round()}元');
    LoggerService.d('   银成本: ${silverCost.round()}元');
    LoggerService.d('   工费成本: ${workCost.round()}元 (工费单价: $currentWorkPrice元/g)');
    LoggerService.d('   件工费: ${pieceWorkCost.round()}元');
    LoggerService.d('   计算得出金价: ${adjustedGoldPrice.toStringAsFixed(2)}元/g');

    return jewelry.copyWith(goldPrice: adjustedGoldPrice);
  }

  /// 🔄 根据销售类型更新所有商品的工费显示
  void _updateWorkPricesForSaleType() {
    if (itemList.isEmpty) return;

    LoggerService.d('🔄 开始更新工费显示，当前销售类型: ${selectedSaleType.value}');

    for (int i = 0; i < itemList.length; i++) {
      final item = itemList[i];
      final jewelry = item.jewelry;

      if (jewelry != null) {
        // 根据销售类型获取对应的工费
        final currentWorkPrice = _getWorkPriceForSaleType(jewelry);

        // 更新商品的工费并重新计算价格
        final updatedJewelry = jewelry.copyWith(workPrice: currentWorkPrice);
        final newAmount = _calculateItemPrice(updatedJewelry);

        // 更新商品明细
        itemList[i] = StockOutItem(
          id: item.id,
          stockOutId: item.stockOutId,
          jewelryId: item.jewelryId,
          barcode: item.barcode,
          amount: newAmount,
          jewelry: updatedJewelry,
        );

        LoggerService.d('🔄 第${i + 1}个商品工费更新: ${currentWorkPrice.toStringAsFixed(2)}元/g，新价格: ${newAmount.round()}元');
      }
    }

    // 重新计算总计
    _calculateTotals();
    LoggerService.d('✅ 工费更新完成');
  }

  /// 📊 根据销售类型获取对应的工费
  /// 零售模式：使用 retailWorkPrice
  /// 批发模式：使用 wholesaleWorkPrice
  double _getWorkPriceForSaleType(Jewelry jewelry) {
    switch (selectedSaleType.value) {
      case 'retail':
        LoggerService.d('📊 零售模式，使用零售工费: ${jewelry.retailWorkPrice.toStringAsFixed(2)}元/g');
        return jewelry.retailWorkPrice;
      case 'wholesale':
        LoggerService.d('📊 批发模式，使用批发工费: ${jewelry.wholesaleWorkPrice.toStringAsFixed(2)}元/g');
        return jewelry.wholesaleWorkPrice;
      default:
        LoggerService.w('⚠️ 未知销售类型: ${selectedSaleType.value}，使用零售工费');
        return jewelry.retailWorkPrice;
    }
  }

  /// 🔄 批量更新价格
  /// 支持部分字段更新，空值字段保持原值不变
  /// 使用现有的价格计算公式：总价格 = 金重 × 金价 + 银重 × 银价 + 总重 × 工费 + 件工费
  Future<void> batchUpdatePrices({
    double? goldPrice,
    double? silverPrice,
    double? workPrice,
    double? pieceWorkPrice,
  }) async {
    if (itemList.isEmpty) {
      Get.snackbar(
        '提示',
        '当前没有商品，无法进行批量改价',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      return;
    }

    try {
      // 显示加载对话框
      Get.dialog(
        const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('正在批量更新价格...'),
            ],
          ),
        ),
        barrierDismissible: false,
      );

      LoggerService.d('🔄 开始批量更新价格');
      LoggerService.d('   金价: ${goldPrice?.round() ?? "保持原值"}');
      LoggerService.d('   银价: ${silverPrice?.round() ?? "保持原值"}');
      LoggerService.d('   工费: ${workPrice?.round() ?? "保持原值"}');
      LoggerService.d('   件工费: ${pieceWorkPrice?.round() ?? "保持原值"}');

      int updatedCount = 0;

      // 遍历所有商品明细进行批量更新
      for (int i = 0; i < itemList.length; i++) {
        final item = itemList[i];
        final jewelry = item.jewelry;

        if (jewelry != null) {
          // 创建更新后的珠宝对象，只更新非空的价格字段
          final updatedJewelry = jewelry.copyWith(
            goldPrice: goldPrice ?? jewelry.goldPrice,
            silverPrice: silverPrice ?? jewelry.silverPrice,
            workPrice: workPrice ?? jewelry.workPrice,
            pieceWorkPrice: pieceWorkPrice ?? jewelry.pieceWorkPrice,
          );

          // 重新计算商品价格
          final newAmount = _calculateItemPrice(updatedJewelry);

          // 更新商品明细
          itemList[i] = StockOutItem(
            id: item.id,
            stockOutId: item.stockOutId,
            jewelryId: item.jewelryId,
            barcode: item.barcode,
            amount: newAmount,
            jewelry: updatedJewelry,
          );

          updatedCount++;
          LoggerService.d('✅ 第${i + 1}个商品价格更新完成，新价格: ${newAmount.round()}元');
        }
      }

      // 重新计算总计
      _calculateTotals();

      // 关闭加载对话框
      if (Get.isDialogOpen == true) {
        Get.back();
      }

      // 显示成功提示
      Get.snackbar(
        '批量改价成功',
        '已成功更新 $updatedCount 件商品的价格',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );

      LoggerService.d('✅ 批量改价完成，共更新 $updatedCount 件商品');

    } catch (e) {
      // 关闭加载对话框
      if (Get.isDialogOpen == true) {
        Get.back();
      }

      LoggerService.e('❌ 批量改价失败', e);
      Get.snackbar(
        '批量改价失败',
        '更新价格时发生错误: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        duration: const Duration(seconds: 5),
      );
    }
  }

  /// 提交出库单并收款 - 根据收款方案.md实现简化流程
  ///
  /// 简化的业务流程：
  /// 1. 创建出库单（状态为待审核）
  /// 2. 立即调用收款API（同时完成收款确认和审核通过）
  Future<void> submitWithPayment(Map<String, dynamic> paymentData) async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    if (selectedStoreId.value == 0) {
      Get.snackbar(
        '错误',
        '请选择门店',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    if (itemList.isEmpty) {
      Get.snackbar(
        '错误',
        '请添加商品明细',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    try {
      isLoading.value = true;

      // 第一步：创建出库单
      final stockOut = StockOut(
        id: 0, // 新出库单ID为0，保存时后端会分配ID
        stockOutNo: 'OUT${DateTime.now().year}${DateTime.now().month.toString().padLeft(2, '0')}${const Uuid().v4().substring(0, 6)}',
        storeId: selectedStoreId.value,
        operatorId: _authService.userId.value,
        totalAmount: totalAmount.value,
        status: DocumentStatus.draft,
        createTime: DateTime.now(),
        customer: customerController.text,
        saleType: selectedSaleType.value,
        totalWeight: totalWeight.value,
        remark: remarkController.text,
        items: itemList,
      );

      // 调用API创建出库单
      final stockOutId = await _stockService.createStockOutWithPayment(stockOut, paymentData);

      if (stockOutId != null) {
        // 获取创建成功的出库单详情（包含完整的门店信息）
        try {
          final createdStockOut = await _stockService.getStockOutById(stockOutId);
          // 更新当前出库单数据
          currentStockOut.value = createdStockOut;
          LoggerService.d('✅ 已获取创建成功的出库单详情，包含门店信息: ${createdStockOut.store?.name ?? "未知门店"}');
                } catch (e) {
          LoggerService.w('获取出库单详情失败，但创建收款成功: $e');
        }

        Get.snackbar(
          '成功',
          '出库单创建并收款成功',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
        
        // 调用保存成功回调
        onSaveSuccess?.call();
        
        Get.back(result: true);
      }
    } catch (e) {
      LoggerService.e('提交出库单并收款失败', e);
      Get.snackbar(
        '错误',
        '提交出库单并收款失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// 保存出库单（原有方法，保留用于草稿保存）
  Future<void> saveStockOut() async {
    if (!formKey.currentState!.validate()) {
      return;
    }
    
    if (selectedStoreId.value == 0) {
      Get.snackbar(
        '错误',
        '请选择门店',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }
    
    if (itemList.isEmpty) {
      Get.snackbar(
        '错误',
        '请添加出库商品',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }
    
    try {
      isLoading.value = true;
      
      // 构建出库单数据
      final stockOut = isEditing.value ? 
        currentStockOut.value!.copyWith(
          storeId: selectedStoreId.value,
          customer: customerController.text,
          saleType: selectedSaleType.value,
          totalAmount: totalAmount.value,
          totalWeight: totalWeight.value,
          remark: remarkController.text,
          items: itemList,
        ) : 
        StockOut(
          id: 0, // 新出库单ID为0，保存时后端会分配ID
          stockOutNo: 'OUT${DateTime.now().year}${DateTime.now().month.toString().padLeft(2, '0')}${const Uuid().v4().substring(0, 6)}',
          storeId: selectedStoreId.value,
          operatorId: _authService.userId.value,
          totalAmount: totalAmount.value,
          status: DocumentStatus.draft,
          createTime: DateTime.now(),
          customer: customerController.text,
          saleType: selectedSaleType.value,
          totalWeight: totalWeight.value,
          remark: remarkController.text,
          items: itemList,
        );
      
      // 调用API保存
      bool success;
      if (isEditing.value) {
        success = await _stockService.updateStockOut(stockOut);
      } else {
        success = await _stockService.createStockOut(stockOut);
      }
      
      if (success) {
        Get.snackbar(
          '成功',
          isEditing.value ? '出库单更新成功' : '出库单创建成功',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
        
        // 调用保存成功回调
        onSaveSuccess?.call();
        
        Get.back(result: true);
      }
    } catch (e) {
      LoggerService.e('保存出库单失败', e);
      Get.snackbar(
        '错误',
        '保存出库单失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// 根据条码添加商品到出库单
  Future<void> addItemByBarcode(String barcode) async {
    if (barcode.trim().isEmpty) {
      Get.snackbar(
        '提示',
        '请输入有效的商品条码',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      return;
    }

    // 🔒 前置条件验证：必须先选择门店
    if (selectedStoreId.value == 0) {
      Get.snackbar(
        '提示',
        '请先选择门店，然后再扫描商品条码',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );
      return;
    }

    try {
      isLoading.value = true;
      LoggerService.d('🔍 开始根据条码查询商品: $barcode，门店ID: ${selectedStoreId.value}');

      // 1. 检查是否已经添加过该条码的商品
      final existingIndex = itemList.indexWhere((item) => item.barcode == barcode);
      if (existingIndex != -1) {
        Get.snackbar(
          '提示',
          '该商品已添加到出库单中',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.orange,
          colorText: Colors.white,
        );
        return;
      }

      // 2. 调用API查询商品信息（限制在所选门店范围内）
      final jewelryService = Get.find<JewelryService>();
      final jewelry = await jewelryService.getJewelryByBarcode(
        barcode,
        storeId: selectedStoreId.value,
      );

      if (jewelry == null) {
        Get.snackbar(
          '错误',
          '该商品在当前门店中不存在',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
          duration: const Duration(seconds: 4),
        );
        return;
      }

      // 3. 检查商品状态（只有status=1上架在售的商品才能出库）
      if (jewelry.status != JewelryStatus.onShelf) {
        String statusText = '';
        switch (jewelry.status) {
          case JewelryStatus.offShelf:
            statusText = '已下架';
            break;
          case JewelryStatus.pendingOut:
            statusText = '待出库';
            break;
          default:
            statusText = '状态异常';
        }

        Get.snackbar(
          '错误',
          '商品状态为"$statusText"，无法添加到出库单。只有上架在售的商品才能出库。',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return;
      }

      // 4. 检查店铺权限（员工只能操作自己店铺的商品）
      if (_authService.userRole.value != 'admin') {
        if (jewelry.storeId != _authService.storeId.value) {
          Get.snackbar(
            '权限错误',
            '您只能操作本店铺的商品',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
          return;
        }
      }

      // 5. 根据销售类型更新商品的工费并计算价格
      final currentWorkPrice = _getWorkPriceForSaleType(jewelry);
      final updatedJewelry = jewelry.copyWith(workPrice: currentWorkPrice);
      final calculatedAmount = _calculateItemPrice(updatedJewelry);

      // 6. 创建出库明细项
      final newItem = StockOutItem(
        id: 0,
        stockOutId: 0,
        jewelryId: jewelry.id,
        barcode: jewelry.barcode,
        amount: calculatedAmount, // 使用自动计算的价格
        jewelry: updatedJewelry,
      );

      // 7. 添加到列表并重新计算总计
      itemList.add(newItem);
      _calculateTotals();

      // 8. 显示成功提示
      Get.snackbar(
        '成功',
        '已添加商品：${jewelry.name}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );

      LoggerService.d('✅ 成功添加商品到出库单: ${jewelry.name}');

    } catch (e) {
      LoggerService.e('根据条码添加商品失败', e);

      String errorMessage = '添加商品失败';

      // 🎯 优化错误提示信息，明确区分不同情况
      if (e.toString().contains('该商品在当前门店中不存在')) {
        errorMessage = '该商品在当前门店中不存在，请检查条码是否正确或联系管理员';
      } else if (e.toString().contains('404') || e.toString().contains('商品不存在')) {
        errorMessage = '未找到条码为 $barcode 的商品，请检查条码是否正确';
      } else if (e.toString().contains('403') || e.toString().contains('无权访问')) {
        errorMessage = '无权访问其他门店的商品，请联系管理员';
      } else if (e.toString().contains('400') || e.toString().contains('商品状态不可用')) {
        errorMessage = '商品状态不可用，只有上架在售的商品才能添加到出库单';
      } else if (e.toString().contains('网络')) {
        errorMessage = '网络连接失败，请检查网络设置';
      } else {
        errorMessage = '添加商品失败: ${e.toString()}';
      }

      Get.snackbar(
        '错误',
        errorMessage,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        duration: const Duration(seconds: 5),
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// 验证表单
  String? validateRequired(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName不能为空';
    }
    return null;
  }
  
  /// 验证数字
  String? validateNumber(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName不能为空';
    }

    final number = double.tryParse(value);
    if (number == null || number <= 0) {
      return '$fieldName必须是大于0的数字';
    }

    return null;
  }

  /// 旧料回收功能
  /// 打开旧料回收对话框，收集回收信息并添加到出库单
  Future<void> handleOldMaterialRecycling() async {
    try {
      LoggerService.d('🔄 打开旧料回收对话框');

      // 🔒 前置条件验证：必须先选择门店
      if (selectedStoreId.value == 0) {
        Get.snackbar(
          '提示',
          '请先选择门店，然后再进行旧料回收',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.orange,
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
        );
        return;
      }

      // 显示旧料回收对话框
      final result = await Get.dialog<OldMaterialItem>(
        const OldMaterialRecyclingDialog(),
        barrierDismissible: false,
      );

      if (result != null) {
        LoggerService.d('✅ 用户确认旧料回收: ${result.toString()}');

        // 将旧料回收项转换为Jewelry对象
        final jewelry = result.toJewelry();

        // 创建出库明细项（价格为负数表示回收支出）
        final newItem = StockOutItem(
          id: 0,
          stockOutId: 0,
          jewelryId: 0, // 旧料回收项没有真实的首饰ID
          barcode: result.barcode,
          amount: -result.payableAmount, // 负价格表示回收支出
          jewelry: jewelry,
        );

        // 添加到出库单列表
        itemList.add(newItem);

        // 重新计算总计
        _calculateTotals();

        // 显示成功提示
        Get.snackbar(
          '成功',
          '已添加旧料回收：${result.remark ?? '旧料回收'} (应付: ${result.payableAmount.round()}元)',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.purple,
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
        );

        LoggerService.d('✅ 旧料回收项已添加到出库单');
      } else {
        LoggerService.d('❌ 用户取消了旧料回收');
      }
    } catch (e) {
      LoggerService.e('旧料回收处理失败', e);
      Get.snackbar(
        '错误',
        '旧料回收处理失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 退换货功能
  /// 打开退换货对话框，选择已售商品进行退换货处理
  Future<void> handleReturnExchange() async {
    try {
      LoggerService.d('🔄 打开退换货对话框');

      // 🔒 前置条件验证：必须先选择门店
      if (selectedStoreId.value == 0) {
        Get.snackbar(
          '提示',
          '请先选择门店，然后再进行退换货',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.orange,
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
        );
        return;
      }

      // 显示退换货对话框
      final result = await Get.dialog<List<StockOutItem>>(
        ReturnExchangeDialog(storeId: selectedStoreId.value),
        barrierDismissible: false,
      );

      if (result != null && result.isNotEmpty) {
        // 将退换货商品添加到当前出库单
        _addReturnExchangeItems(result);
      }

    } catch (e) {
      LoggerService.e('❌ 退换货处理失败', e);
      Get.snackbar(
        '错误',
        '退换货处理失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 添加退换货商品到当前出库单
  void _addReturnExchangeItems(List<StockOutItem> returnItems) {
    try {
      for (final returnItem in returnItems) {
        // 创建退换货商品项（负数价格）
        final returnStockOutItem = StockOutItem(
          id: 0,
          stockOutId: 0,
          jewelryId: returnItem.jewelryId,
          barcode: returnItem.barcode,
          amount: -returnItem.amount.abs(), // 确保是负数
          // 🔧 修复：传递完整的价格信息
          goldPrice: returnItem.goldPrice,
          silverPrice: returnItem.silverPrice,
          workPrice: returnItem.workPrice,
          pieceWorkPrice: returnItem.pieceWorkPrice,
          totalWeight: returnItem.totalWeight,
          jewelry: returnItem.jewelry?.copyWith(
            name: '退货-${returnItem.jewelry?.name ?? ''}', // 添加退货前缀
          ),
        );

        // 添加到商品列表
        itemList.add(returnStockOutItem);
      }

      // 重新计算总计
      _calculateTotals();

      // 显示成功提示
      Get.snackbar(
        '成功',
        '已添加 ${returnItems.length} 件退换货商品',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );

      LoggerService.d('✅ 成功添加退换货商品: ${returnItems.length} 件');

    } catch (e) {
      LoggerService.e('❌ 添加退换货商品失败', e);
      Get.snackbar(
        '错误',
        '添加退换货商品失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 检查是否为退换货商品
  /// 退换货商品的识别标准：jewelryId不为0，价格为负数，商品名称包含"退货-"
  bool _isReturnExchangeItem(StockOutItem item) {
    return item.jewelryId != 0 &&
           item.amount < 0 &&
           (item.jewelry?.name.startsWith('退货-') ?? false);
  }

  /// 单收工费功能
  void handleSingleWorkFee() {
    // 🔒 前置条件验证：必须先选择门店
    if (selectedStoreId.value == 0) {
      Get.snackbar(
        '提示',
        '请先选择门店，然后再添加工费项目',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );
      return;
    }

    _showSingleWorkFeeDialog();
  }

  /// 显示单收工费对话框
  void _showSingleWorkFeeDialog() {
    Get.dialog(
      SingleWorkFeeDialog(
        onConfirm: (workFeeData) {
          _addWorkFeeItem(
            barcode: workFeeData['barcode'],
            name: workFeeData['name'],
            chargeType: workFeeData['chargeType'],
            goldWeight: workFeeData['goldWeight'],
            silverWeight: workFeeData['silverWeight'],
            workPrice: workFeeData['workPrice'],
            discount: workFeeData['discount'],
            amount: workFeeData['amount'],
          );
        },
      ),
      barrierDismissible: false,
    );
  }

  /// 添加工费项目到出库单
  void _addWorkFeeItem({
    required String barcode,
    required String name,
    required String chargeType,
    required double goldWeight,
    required double silverWeight,
    required double workPrice,
    required double discount,
    required double amount,
  }) {
    try {
      // 验证必填字段
      if (name.trim().isEmpty) {
        Get.snackbar(
          '提示',
          '请输入商品名称',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.orange,
          colorText: Colors.white,
        );
        return;
      }

      if (amount <= 0) {
        Get.snackbar(
          '提示',
          '工费金额必须大于0',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.orange,
          colorText: Colors.white,
        );
        return;
      }

      // 创建工费项目的Jewelry对象
      final workFeeJewelry = Jewelry(
        id: -1, // 工费项目使用特殊ID
        barcode: barcode,
        name: '工费-$name', // 添加工费前缀
        categoryId: 0,
        storeId: selectedStoreId.value,
        goldWeight: goldWeight,
        silverWeight: silverWeight,
        totalWeight: goldWeight + silverWeight,
        goldPrice: 0.0,
        silverPrice: 0.0,
        workPrice: workPrice,
        pieceWorkPrice: 0.0,
        retailWorkPrice: workPrice,
        wholesaleWorkPrice: workPrice,
        salePrice: amount, // 使用计算后的金额作为销售价格
        status: JewelryStatus.onShelf,
        createTime: DateTime.now(),
      );

      // 创建出库明细项
      final workFeeItem = StockOutItem(
        id: 0,
        stockOutId: 0,
        jewelryId: -1, // 工费项目使用特殊jewelryId
        barcode: barcode,
        amount: amount,
        jewelry: workFeeJewelry,
      );

      // 添加到商品列表
      itemList.add(workFeeItem);

      // 重新计算总计
      _calculateTotals();

      // 显示成功提示
      Get.snackbar(
        '成功',
        '已添加工费项目：$name (${amount.round()}元)',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );

      LoggerService.d('✅ 成功添加工费项目: $name, 金额: ${amount.round()}元');

    } catch (e) {
      LoggerService.e('❌ 添加工费项目失败', e);
      Get.snackbar(
        '错误',
        '添加工费项目失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 检查是否为工费项目
  /// 工费项目的识别标准：jewelryId为-1，商品名称包含"工费-"
  bool isWorkFeeItem(StockOutItem item) {
    return item.jewelryId == -1 &&
           (item.jewelry?.name.startsWith('工费-') == true);
  }
}
