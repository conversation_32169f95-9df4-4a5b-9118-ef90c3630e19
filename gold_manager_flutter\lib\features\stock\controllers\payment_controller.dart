import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 收款控制器
/// 
/// 根据收款方案.md文档实现收款业务逻辑：
/// 1. 实时计算抹零金额和找零金额
/// 2. 验证收款数据的合理性
/// 3. 构建收款数据用于API调用
class PaymentController extends GetxController {
  final double totalAmount; // 应付金额

  PaymentController({required this.totalAmount});

  // 支付方式输入控制器
  final TextEditingController cashController = TextEditingController();
  final TextEditingController cardController = TextEditingController();
  final TextEditingController wechatController = TextEditingController();
  final TextEditingController alipayController = TextEditingController();
  final TextEditingController discountController = TextEditingController();

  // 计算结果
  final RxDouble changeAmount = 0.0.obs; // 找零金额
  final RxDouble totalPayment = 0.0.obs; // 总付款金额
  final RxDouble actualAmount = 0.0.obs; // 实收金额

  // 控制标志
  final RxBool isManualDiscount = false.obs; // 是否手动输入抹零金额
  final RxBool isAutoCalculating = false.obs; // 是否正在自动计算中（防止循环）
  final RxBool isDiscountAutoFilled = false.obs; // 抹零是否为自动填充

  // 保存状态控制
  final RxBool isSaving = false.obs; // 是否正在保存
  final RxBool isSaveDisabled = false.obs; // 保存按钮是否禁用

  @override
  void onInit() {
    super.onInit();
    // 初始化实收金额为应付金额
    actualAmount.value = totalAmount;

    // 监听支付方式输入变化，实时计算
    cashController.addListener(_onPaymentAmountChanged);
    cardController.addListener(_onPaymentAmountChanged);
    wechatController.addListener(_onPaymentAmountChanged);
    alipayController.addListener(_onPaymentAmountChanged);

    // 监听抹零金额输入变化，标记为手动输入
    discountController.addListener(_onDiscountChanged);
  }

  /// 支付金额变化时的处理
  void _onPaymentAmountChanged() {
    if (!isAutoCalculating.value) {
      calculateTotals();
    }
  }

  /// 抹零金额变化时的处理
  void _onDiscountChanged() {
    if (!isAutoCalculating.value) {
      // 标记为手动输入抹零
      isManualDiscount.value = discountController.text.isNotEmpty;
      calculateTotals();
    }
  }

  @override
  void onClose() {
    // 清理资源
    cashController.dispose();
    cardController.dispose();
    wechatController.dispose();
    alipayController.dispose();
    discountController.dispose();
    super.onClose();
  }

  /// 实时计算各项金额
  ///
  /// 优化后的计算逻辑：
  /// 1. 付款不足时：自动计算抹零，找零为0
  /// 2. 付款充足时：抹零为0，自动计算找零
  /// 3. 手动抹零时：重新计算找零金额
  /// 4. 公式：找零金额 = 应付金额 - 实际付款 + 抹零金额
  void calculateTotals() {
    if (isAutoCalculating.value) return; // 防止循环计算

    // 获取各支付方式金额（不包括抹零）
    final cash = _parseAmount(cashController.text);
    final card = _parseAmount(cardController.text);
    final wechat = _parseAmount(wechatController.text);
    final alipay = _parseAmount(alipayController.text);

    // 计算实际付款金额（不包括抹零）
    final actualPayment = cash + card + wechat + alipay;

    // 计算付款差额
    final paymentDifference = actualPayment - totalAmount;

    double discount = 0.0;
    double change = 0.0;

    if (isManualDiscount.value) {
      // 手动输入抹零时，重新计算找零
      discount = _parseAmount(discountController.text);
      // 确保抹零金额不为负数
      if (discount < 0) discount = 0.0;
      // 计算找零：找零 = 实际付款 - (应付金额 - 抹零)
      // 即：找零 = 实际付款 - 应付金额 + 抹零
      change = actualPayment - totalAmount + discount;
      // 找零不能为负数
      if (change < 0) change = 0.0;
    } else {
      // 自动计算模式
      if (paymentDifference < 0) {
        // 付款不足：自动计算抹零，找零为0
        discount = -paymentDifference; // 抹零金额为不足的部分
        change = 0.0;

        // 自动填充抹零金额
        _setDiscountAmount(discount);
      } else {
        // 付款充足：抹零为0，自动计算找零
        discount = 0.0;
        change = paymentDifference;

        // 清空抹零金额
        _setDiscountAmount(0.0);
      }
    }

    // 更新计算结果
    totalPayment.value = actualPayment; // 总付款金额 = 各种支付方式的简单相加
    changeAmount.value = change;
    actualAmount.value = totalAmount - discount;
  }

  /// 设置抹零金额（避免触发监听器）
  void _setDiscountAmount(double amount) {
    isAutoCalculating.value = true;
    if (amount > 0) {
      discountController.text = amount.toStringAsFixed(2);
    } else {
      discountController.clear();
    }
    isAutoCalculating.value = false;
  }

  /// 解析金额字符串为double
  double _parseAmount(String text) {
    if (text.isEmpty) return 0.0;
    return double.tryParse(text) ?? 0.0;
  }

  /// 验证收款数据
  Future<bool> validatePayment() async {
    // 检查是否有支付金额
    final cash = _parseAmount(cashController.text);
    final card = _parseAmount(cardController.text);
    final wechat = _parseAmount(wechatController.text);
    final alipay = _parseAmount(alipayController.text);
    final totalPaid = cash + card + wechat + alipay;

    if (totalPaid <= 0) {
      Get.snackbar(
        '验证失败',
        '请输入支付金额',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red[600],
        colorText: Colors.white,
      );
      return false;
    }

    // 检查抹零金额是否合理（不能为负数）
    final discount = _parseAmount(discountController.text);
    if (discount < 0) {
      Get.snackbar(
        '验证失败',
        '抹零金额不能为负数',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red[600],
        colorText: Colors.white,
      );
      return false;
    }

    // 检查抹零金额是否超过应付金额
    if (discount > totalAmount) {
      Get.snackbar(
        '验证失败',
        '抹零金额不能超过应付金额',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red[600],
        colorText: Colors.white,
      );
      return false;
    }

    // 检查找零金额是否过大（可能输入错误）
    if (changeAmount.value > totalAmount) {
      final confirmed = await Get.dialog<bool>(
        AlertDialog(
          title: const Text('确认收款'),
          content: Text('找零金额较大（¥${changeAmount.value.toStringAsFixed(2)}），请确认是否正确？'),
          actions: [
            TextButton(
              onPressed: () => Get.back(result: false),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () => Get.back(result: true),
              child: const Text('确认'),
            ),
          ],
        ),
      );
      return confirmed == true;
    }

    return true;
  }

  /// 获取收款数据用于API调用
  Map<String, dynamic> getPaymentData() {
    return {
      'payment_status': 1, // 已收款
      'payment_info': {
        'payment_method': _getPaymentMethod(),
        'payment_remark': _getPaymentRemark(),
        'cash_amount': _parseAmount(cashController.text).toStringAsFixed(2),
        'wechat_amount': _parseAmount(wechatController.text).toStringAsFixed(2),
        'alipay_amount': _parseAmount(alipayController.text).toStringAsFixed(2),
        'card_amount': _parseAmount(cardController.text).toStringAsFixed(2),
        'discount_amount': _parseAmount(discountController.text).toStringAsFixed(2),
        'actual_amount': actualAmount.value.toStringAsFixed(2),
      }
    };
  }

  /// 获取支付方式描述
  String _getPaymentMethod() {
    final methods = <String>[];
    
    if (_parseAmount(cashController.text) > 0) methods.add('现金');
    if (_parseAmount(cardController.text) > 0) methods.add('刷卡');
    if (_parseAmount(wechatController.text) > 0) methods.add('微信');
    if (_parseAmount(alipayController.text) > 0) methods.add('支付宝');

    if (methods.isEmpty) return '未知支付方式';
    if (methods.length == 1) return methods.first;
    return '混合支付';
  }

  /// 获取支付备注
  String _getPaymentRemark() {
    final details = <String>[];
    
    final cash = _parseAmount(cashController.text);
    final card = _parseAmount(cardController.text);
    final wechat = _parseAmount(wechatController.text);
    final alipay = _parseAmount(alipayController.text);
    final discount = _parseAmount(discountController.text);

    if (cash > 0) details.add('现金¥${cash.toStringAsFixed(2)}');
    if (card > 0) details.add('刷卡¥${card.toStringAsFixed(2)}');
    if (wechat > 0) details.add('微信¥${wechat.toStringAsFixed(2)}');
    if (alipay > 0) details.add('支付宝¥${alipay.toStringAsFixed(2)}');
    if (discount > 0) details.add('抹零¥${discount.toStringAsFixed(2)}');

    return details.join(', ');
  }

  /// 快速设置现金支付（等于应付金额）
  void setFullCashPayment() {
    _resetManualDiscountFlag();
    cashController.text = totalAmount.toStringAsFixed(2);
    cardController.clear();
    wechatController.clear();
    alipayController.clear();
    discountController.clear();
    calculateTotals();
  }

  /// 快速设置刷卡支付（等于应付金额）
  void setFullCardPayment() {
    _resetManualDiscountFlag();
    cashController.clear();
    cardController.text = totalAmount.toStringAsFixed(2);
    wechatController.clear();
    alipayController.clear();
    discountController.clear();
    calculateTotals();
  }

  /// 快速设置微信支付（等于应付金额）
  void setFullWechatPayment() {
    _resetManualDiscountFlag();
    cashController.clear();
    cardController.clear();
    wechatController.text = totalAmount.toStringAsFixed(2);
    alipayController.clear();
    discountController.clear();
    calculateTotals();
  }

  /// 快速设置支付宝支付（等于应付金额）
  void setFullAlipayPayment() {
    _resetManualDiscountFlag();
    cashController.clear();
    cardController.clear();
    wechatController.clear();
    alipayController.text = totalAmount.toStringAsFixed(2);
    discountController.clear();
    calculateTotals();
  }

  /// 清空所有输入
  void clearAll() {
    _resetManualDiscountFlag();
    cashController.clear();
    cardController.clear();
    wechatController.clear();
    alipayController.clear();
    discountController.clear();
    calculateTotals();
  }

  /// 重置手动抹零标志
  void _resetManualDiscountFlag() {
    isManualDiscount.value = false;
  }
}
