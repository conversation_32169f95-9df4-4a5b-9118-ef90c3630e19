import 'package:intl/intl.dart';
import '../common/enums.dart';
import '../jewelry/jewelry.dart';
import 'metal_separation.dart';
import 'repair_record.dart';

/// 旧料处理记录模型
class RecyclingProcess {
  final int? id;
  final int recyclingItemId; // 回收物品ID
  final RecyclingProcessType processType; // 处理方式
  final String processNo; // 处理单号
  final DateTime processTime; // 处理时间
  final int operatorId; // 操作员ID
  final String operatorName; // 操作员名称
  final int storeId; // 门店ID
  final String storeName; // 门店名称
  final double cost; // 处理成本
  final double income; // 处理收入
  final double profit; // 处理利润
  final int? jewelryId; // 关联新商品ID(维修/再销售时)
  final String? remark; // 备注
  final int status; // 状态: 0=进行中, 1=已完成, 2=已取消
  final DateTime? createTime; // 创建时间
  final DateTime? updateTime; // 更新时间
  final List<String>? imageUrls; // 图片URL列表
  
  // 关联对象
  final Jewelry? jewelry; // 关联的新首饰
  final MetalSeparation? metalSeparation; // 金银分离记录
  final RepairRecord? repairRecord; // 维修记录
  
  const RecyclingProcess({
    this.id,
    required this.recyclingItemId,
    required this.processType,
    required this.processNo,
    required this.processTime,
    required this.operatorId,
    required this.operatorName,
    required this.storeId,
    required this.storeName,
    required this.cost,
    required this.income,
    required this.profit,
    this.jewelryId,
    this.remark,
    required this.status,
    this.createTime,
    this.updateTime,
    this.imageUrls,
    this.jewelry,
    this.metalSeparation,
    this.repairRecord,
  });
  
  /// 从JSON构造
  factory RecyclingProcess.fromJson(Map<String, dynamic> json) {
    List<String>? parseImageUrls;
    if (json['image_urls'] != null) {
      if (json['image_urls'] is List) {
        parseImageUrls = (json['image_urls'] as List).map((item) => item.toString()).toList();
      } else if (json['image_urls'] is String) {
        // 如果是以逗号分隔的字符串
        parseImageUrls = (json['image_urls'] as String).split(',');
      }
    }

    return RecyclingProcess(
      id: json['id'],
      recyclingItemId: json['recycling_item_id'],
      processType: RecyclingProcessType.fromValue(json['process_type'] ?? 'pending'),
      processNo: json['process_no'] ?? '',
      processTime: json['process_time'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['process_time'] * 1000)
          : DateTime.now(),
      operatorId: json['operator_id'] ?? 0,
      operatorName: json['operator_name'] ?? '',
      storeId: json['store_id'] ?? 0,
      storeName: json['store_name'] ?? '',
      cost: (json['cost'] ?? 0.0).toDouble(),
      income: (json['income'] ?? 0.0).toDouble(),
      profit: (json['profit'] ?? 0.0).toDouble(),
      jewelryId: json['jewelry_id'],
      remark: json['remark'],
      status: json['status'] ?? 0,
      createTime: json['createtime'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['createtime'] * 1000)
          : null,
      updateTime: json['updatetime'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['updatetime'] * 1000)
          : null,
      imageUrls: parseImageUrls,
      jewelry: json['jewelry'] != null ? Jewelry.fromJson(json['jewelry']) : null,
      metalSeparation: json['metal_separation'] != null 
          ? MetalSeparation.fromJson(json['metal_separation']) 
          : null,
      repairRecord: json['repair_record'] != null 
          ? RepairRecord.fromJson(json['repair_record']) 
          : null,
    );
  }
  
  /// 转为JSON
  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      'recycling_item_id': recyclingItemId,
      'process_type': processType.value,
      'process_no': processNo,
      'process_time': processTime.millisecondsSinceEpoch ~/ 1000,
      'operator_id': operatorId,
      'operator_name': operatorName,
      'store_id': storeId,
      'store_name': storeName,
      'cost': cost,
      'income': income,
      'profit': profit,
      if (jewelryId != null) 'jewelry_id': jewelryId,
      if (remark != null) 'remark': remark,
      'status': status,
      if (createTime != null) 'createtime': createTime!.millisecondsSinceEpoch ~/ 1000,
      if (updateTime != null) 'updatetime': updateTime!.millisecondsSinceEpoch ~/ 1000,
      if (imageUrls != null && imageUrls!.isNotEmpty) 'image_urls': imageUrls!.join(','),
    };
  }
  
  /// 获取处理方式文本
  String getProcessTypeText() {
    switch (processType) {
      case RecyclingProcessType.sell:
        return '直接卖出';
      case RecyclingProcessType.separate:
        return '金银分离';
      case RecyclingProcessType.repair:
        return '维修再销售';
      case RecyclingProcessType.resell:
        return '直接二次销售';
      default:
        return '待处理';
    }
  }
  
  /// 获取格式化的处理时间
  String get formattedProcessTime {
    return DateFormat('yyyy-MM-dd HH:mm').format(processTime);
  }
  
  /// 获取格式化的创建时间
  String? get formattedCreateTime {
    return createTime != null
        ? DateFormat('yyyy-MM-dd').format(createTime!)
        : null;
  }
  
  /// 获取状态文本
  String get statusText {
    switch (status) {
      case 0:
        return '进行中';
      case 1:
        return '已完成';
      case 2:
        return '已取消';
      default:
        return '未知';
    }
  }
  
  /// 创建一个新实例，但使用部分属性
  RecyclingProcess copyWith({
    int? id,
    int? recyclingItemId,
    RecyclingProcessType? processType,
    String? processNo,
    DateTime? processTime,
    int? operatorId,
    String? operatorName,
    int? storeId,
    String? storeName,
    double? cost,
    double? income,
    double? profit,
    int? jewelryId,
    String? remark,
    int? status,
    DateTime? createTime,
    DateTime? updateTime,
    List<String>? imageUrls,
    Jewelry? jewelry,
    MetalSeparation? metalSeparation,
    RepairRecord? repairRecord,
  }) {
    return RecyclingProcess(
      id: id ?? this.id,
      recyclingItemId: recyclingItemId ?? this.recyclingItemId,
      processType: processType ?? this.processType,
      processNo: processNo ?? this.processNo,
      processTime: processTime ?? this.processTime,
      operatorId: operatorId ?? this.operatorId,
      operatorName: operatorName ?? this.operatorName,
      storeId: storeId ?? this.storeId,
      storeName: storeName ?? this.storeName,
      cost: cost ?? this.cost,
      income: income ?? this.income,
      profit: profit ?? this.profit,
      jewelryId: jewelryId ?? this.jewelryId,
      remark: remark ?? this.remark,
      status: status ?? this.status,
      createTime: createTime ?? this.createTime,
      updateTime: updateTime ?? this.updateTime,
      imageUrls: imageUrls ?? this.imageUrls,
      jewelry: jewelry ?? this.jewelry,
      metalSeparation: metalSeparation ?? this.metalSeparation,
      repairRecord: repairRecord ?? this.repairRecord,
    );
  }
} 