# 新建调拨对话框 - 批量调价功能实现

## 📋 功能概述

新建调拨对话框（`StoreTransferDialog`）是一个严格遵循统一边框调用方式文档规范的组件，集成了完整的批量调价功能。

## 🎯 核心特性

### 1. 严格遵循UI规范
- ✅ **AppBorderStyles.standardBoxDecoration** - 容器边框
- ✅ **AppBorderStyles.borderRadius** - 按钮圆角
- ✅ **AppBorderStyles.compactInputDecoration** - 输入框边框
- ✅ **32px高度标准** - 所有控件统一高度
- ✅ **统一边框调用方式** - 完全遵循文档规范

### 2. 批量调价功能
- ✅ **完全复用** - 100%复用新建出库单的批量调价实现
- ✅ **橙色主题** - Colors.orange[600]背景，价格变更图标
- ✅ **智能计算** - 自动重新计算调拨价格和总额
- ✅ **部分更新** - 只更新用户填写的价格项

### 3. 简化布局设计
- ✅ **单行布局** - 门店选择、扫描输入、批量调价、备注在同一行
- ✅ **紧凑排列** - 最大化利用水平空间
- ✅ **直观操作** - 扫描后直接添加到列表

## 🔧 使用方法

### 1. 基本使用
```dart
import 'package:gold_manager_flutter/features/stock/widgets/store_transfer_dialog.dart';

// 显示新建调拨对话框
Get.dialog(
  const StoreTransferDialog(),
  barrierDismissible: false,
).then((result) {
  if (result == true) {
    // 调拨单操作完成
    print('调拨单创建成功');
  }
});
```

### 2. 依赖注入
```dart
import 'package:gold_manager_flutter/features/stock/bindings/store_transfer_binding.dart';

// 在路由中注册绑定
GetPage(
  name: '/store-transfer',
  page: () => StoreTransferTestView(),
  binding: StoreTransferBinding(),
),
```

### 3. 控制器使用
```dart
// 获取控制器实例
final controller = Get.find<StoreTransferFormController>();

// 扫描商品
await controller.scanBarcode('商品条码');

// 批量调价
await controller.batchUpdatePrices(
  goldPrice: 450.0,
  silverPrice: 8.0,
  workPrice: 50.0,
  pieceWorkPrice: 100.0,
);

// 保存草稿
await controller.saveDraft();

// 提交审核
await controller.submitForAudit();
```

## 🎨 UI组件结构

```
StoreTransferDialog
├── _buildDialogHeader()           # 对话框标题和关闭按钮
├── _buildBasicInfoSection()       # 基本信息区域（单行布局）
│   ├── _buildStoreSelector()      # 门店选择器
│   ├── _buildBarcodeInput()       # 扫描输入框
│   ├── 批量调价按钮               # Colors.orange[600]主题
│   └── _buildRemarkInput()        # 备注输入框
├── _buildTransferItemList()       # 调拨商品列表
│   ├── _buildListHeader()         # 表头
│   └── _buildListItem()           # 商品数据行
├── _buildSummarySection()         # 汇总信息
├── _buildActionButtons()          # 操作按钮
└── 批量调价相关方法
    ├── _batchChangePrice()        # 批量调价入口
    ├── _showBatchPriceChangeDialog() # 显示调价对话框
    ├── _buildPriceInputField()    # 价格输入字段
    └── _executeBatchPriceChange() # 执行批量调价
```

## 📊 批量调价功能详解

### 1. 触发条件
- 调拨商品列表中至少有一件商品
- 用户点击"批量调价"按钮

### 2. 对话框设计
```
┌─────────────────────────────────────────────────────────┐
│  💰 批量调价                              [❌ 关闭]     │
├─────────────────────────────────────────────────────────┤
│ ℹ️ 只填写需要修改的价格项，空白项将保持原值不变        │
├─────────────────────────────────────────────────────────┤
│ 🟡 金价 (元/克):  [输入框 - 可选]                      │
│ ⚪ 银价 (元/克):  [输入框 - 可选]                      │
│ 🔧 工费 (元/克):  [输入框 - 可选]                      │
│ 🛠️ 件工费 (元/件): [输入框 - 可选]                     │
├─────────────────────────────────────────────────────────┤
│                           [取消] [确定]                 │
└─────────────────────────────────────────────────────────┘
```

### 3. 价格计算公式
```dart
调拨价格 = 金重 × 金价 + 银重 × 银价 + 总重 × 工费 + 件工费
```

### 4. 更新逻辑
```dart
// 只更新非空的价格字段
if (goldPrice != null) item.goldPrice = goldPrice;
if (silverPrice != null) item.silverPrice = silverPrice;
if (workPrice != null) item.workPrice = workPrice;
if (pieceWorkPrice != null) item.pieceWorkPrice = pieceWorkPrice;

// 重新计算调拨价格
item.transferPrice = _calculateTransferPrice(item);
```

## 🔍 边框样式规范遵循

### 1. 容器边框
```dart
Container(
  decoration: AppBorderStyles.standardBoxDecoration, // ✅ 统一标准装饰
  child: ...,
)
```

### 2. 输入框边框
```dart
// 扫描输入框
TextFormField(
  decoration: AppBorderStyles.compactInputDecoration.copyWith(...), // ✅ 紧凑型装饰
)

// 备注输入框（32px高度模板）
Container(
  height: 32,
  decoration: AppBorderStyles.standardBoxDecoration, // ✅ 标准装饰
  child: TextField(
    decoration: const InputDecoration(
      border: InputBorder.none,           // ✅ 完整移除内部边框
      enabledBorder: InputBorder.none,
      focusedBorder: InputBorder.none,
      disabledBorder: InputBorder.none,
      contentPadding: EdgeInsets.symmetric(vertical: 8), // ✅ 正确对齐
      isDense: true,                      // ✅ 紧凑模式
    ),
  ),
)
```

### 3. 按钮圆角
```dart
ElevatedButton(
  style: ElevatedButton.styleFrom(
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius), // ✅ 统一圆角
    ),
  ),
)
```

### 4. 表格边框
```dart
// 表头
Container(
  decoration: AppBorderStyles.headerCellDecoration(), // ✅ 表头装饰
)

// 数据行
Container(
  decoration: AppBorderStyles.cellDecoration(isEven: isEven), // ✅ 单元格装饰
)
```

## 🚀 测试验证

使用 `StoreTransferTestView` 页面进行功能测试：

```dart
import 'package:gold_manager_flutter/features/stock/views/store_transfer_test_view.dart';

// 导航到测试页面
Get.to(() => const StoreTransferTestView());
```

## 📝 注意事项

1. **依赖注入**：确保在使用前注册 `StoreTransferBinding`
2. **权限控制**：门店选择会根据用户角色进行过滤
3. **数据验证**：所有输入都会进行严格的业务逻辑验证
4. **错误处理**：完善的异常处理和用户反馈机制
5. **UI一致性**：严格遵循统一边框调用方式文档规范

## 🎉 总结

新建调拨对话框成功实现了：
- ✅ **严格的UI规范遵循**
- ✅ **完整的批量调价功能**
- ✅ **简洁高效的操作流程**
- ✅ **与系统的完美集成**

这个实现为库存调拨管理提供了专业、高效、一致的用户体验。
