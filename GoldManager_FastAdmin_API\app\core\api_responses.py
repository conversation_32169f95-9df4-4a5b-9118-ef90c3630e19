"""
通用API响应配置
定义标准的HTTP响应模式，用于API文档生成
"""

# 通用响应模式
COMMON_RESPONSES = {
    400: {
        "description": "请求参数错误",
        "content": {"application/json": {"example": {"detail": "请求参数错误"}}},
    },
    401: {
        "description": "未授权访问",
        "content": {"application/json": {"example": {"detail": "请先登录"}}},
    },
    403: {
        "description": "权限不足",
        "content": {"application/json": {"example": {"detail": "权限不足"}}},
    },
    404: {
        "description": "资源不存在",
        "content": {"application/json": {"example": {"detail": "资源不存在"}}},
    },
    422: {
        "description": "请求参数验证失败",
        "content": {
            "application/json": {
                "example": {
                    "detail": [
                        {
                            "loc": ["body", "field_name"],
                            "msg": "field required",
                            "type": "value_error.missing",
                        }
                    ]
                }
            }
        },
    },
    429: {
        "description": "请求过于频繁",
        "content": {
            "application/json": {
                "example": {"code": 429, "message": "请求过于频繁，请稍后再试", "data": None}
            }
        },
    },
    500: {
        "description": "服务器内部错误",
        "content": {
            "application/json": {
                "example": {"code": 500, "message": "服务器内部错误", "data": None}
            }
        },
    },
}


# 成功响应模式生成器
def success_response(example_data, description="请求成功"):
    """生成成功响应模式"""
    return {
        200: {
            "description": description,
            "content": {"application/json": {"example": example_data}},
        }
    }


# 创建响应模式生成器
def create_response(description="创建成功", example_data=None):
    """生成创建成功响应模式"""
    if example_data is None:
        example_data = {"id": 1, "message": "创建成功"}

    return {
        201: {
            "description": description,
            "content": {"application/json": {"example": example_data}},
        }
    }


# 更新响应模式生成器
def update_response(description="更新成功", example_data=None):
    """生成更新成功响应模式"""
    if example_data is None:
        example_data = {"message": "更新成功"}

    return {
        200: {
            "description": description,
            "content": {"application/json": {"example": example_data}},
        }
    }


# 删除响应模式生成器
def delete_response(description="删除成功"):
    """生成删除成功响应模式"""
    return {
        200: {
            "description": description,
            "content": {"application/json": {"example": {"message": "删除成功"}}},
        }
    }


# 分页响应模式生成器
def paginated_response(item_example, description="分页数据获取成功"):
    """生成分页响应模式"""
    return {
        200: {
            "description": description,
            "content": {
                "application/json": {
                    "example": {
                        "items": [item_example],
                        "total": 100,
                        "page": 1,
                        "page_size": 20,
                        "total_pages": 5,
                    }
                }
            },
        }
    }


# 合并响应模式的辅助函数
def merge_responses(*response_dicts):
    """合并多个响应字典"""
    merged = {}
    for response_dict in response_dicts:
        merged.update(response_dict)
    return merged


# 商品相关的标准响应
JEWELRY_RESPONSES = {
    **COMMON_RESPONSES,
    **success_response(
        {
            "id": 1,
            "barcode": "JW001",
            "name": "黄金戒指",
            "category_id": 1,
            "category_name": "戒指",
            "store_id": 1,
            "store_name": "总店",
            "ring_size": "17号",
            "gold_weight": "3.50",
            "gold_price": "450.00",
            "total_weight": "3.50",
            "labor_cost": "200.00",
            "total_cost": "1775.00",
            "status": 1,
            "createtime": 1640995200,
            "updatetime": 1640995200,
        }
    ),
}

# 分类相关的标准响应
CATEGORY_RESPONSES = {
    **COMMON_RESPONSES,
    **success_response(
        {
            "id": 1,
            "name": "戒指",
            "pid": 0,
            "status": 1,
            "weigh": 0,
            "createtime": 1640995200,
            "updatetime": 1640995200,
        }
    ),
}

# API标签定义
API_TAGS = {
    "jewelry": "商品管理",
    "jewelry_category": "商品分类管理",
    "stock": "库存管理",
    "store": "门店管理",
    "member": "会员管理",
    "auth": "认证授权",
    "dashboard": "数据统计",
    "admin": "系统管理",
    "recycling": "回收管理",
}
