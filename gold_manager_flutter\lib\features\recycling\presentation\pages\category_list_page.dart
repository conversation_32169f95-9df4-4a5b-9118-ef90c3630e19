import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/utils/responsive_utils.dart';
import '../../../../core/widgets/empty_state.dart';
import '../../../../core/widgets/loading_indicator.dart';
import '../../../../models/recycling/recycling_model.dart';
import '../../controllers/category_controller.dart';
import 'category_form_page.dart';

/// 旧料分类列表页面
class CategoryListPage extends StatelessWidget {
  const CategoryListPage({super.key});

  @override
  Widget build(BuildContext context) {
    // 初始化控制器
    final controller = Get.put(CategoryController());
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('旧料分类管理'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _navigateToAddCategory(context),
            tooltip: '添加分类',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildFilterBar(context, controller),
          Expanded(
            child: Obx(() => _buildCategoryList(context, controller)),
          ),
        ],
      ),
    );
  }
  
  /// 构建筛选栏
  Widget _buildFilterBar(BuildContext context, CategoryController controller) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          // 搜索框
          Expanded(
            flex: 3,
            child: TextField(
              controller: controller.searchController,
              decoration: InputDecoration(
                hintText: '搜索分类名称或描述',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: const EdgeInsets.symmetric(vertical: 12),
                suffixIcon: Obx(() {
                  if (controller.searchQuery.value.isNotEmpty) {
                    return IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: controller.clearSearch,
                    );
                  } else {
                    return const SizedBox.shrink();
                  }
                }),
              ),
              onChanged: controller.setSearchQuery,
            ),
          ),
          const SizedBox(width: 16),
          
          // 显示非活跃分类开关
          Obx(() => Row(
            children: [
              const Text(
                '显示禁用分类',
                style: AppTextStyles.bodySmall,
              ),
              const SizedBox(width: 8),
              Switch(
                value: controller.showInactiveCategories.value,
                onChanged: (_) => controller.toggleShowInactive(),
                activeColor: AppColors.primary,
              ),
            ],
          )),
          
          // 刷新按钮
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: controller.loadCategories,
            tooltip: '刷新',
          ),
        ],
      ),
    );
  }
  
  /// 构建分类列表
  Widget _buildCategoryList(BuildContext context, CategoryController controller) {
    if (controller.isLoading.value) {
      return const Center(child: LoadingIndicator());
    }
    
    if (controller.categories.isEmpty) {
      return EmptyState(
        icon: Icons.category,
        title: '暂无分类数据',
        message: '点击右上角"+"按钮添加分类',
        buttonText: '添加分类',
        onButtonPressed: () => _navigateToAddCategory(context),
      );
    }
    
    // 响应式布局
    final isDesktop = Responsive.isDesktop(context);
    
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Card(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        elevation: 2,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildTableHeader(isDesktop),
              const Divider(),
              Expanded(
                child: ListView.separated(
                  itemCount: controller.categories.length,
                  separatorBuilder: (_, __) => const Divider(),
                  itemBuilder: (context, index) {
                    final category = controller.categories[index];
                    return _buildCategoryItem(
                      context,
                      controller,
                      category,
                      isDesktop,
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  /// 构建表格头部
  Widget _buildTableHeader(bool isDesktop) {
    return Row(
      children: [
        // ID
        if (isDesktop) ...[
          SizedBox(
            width: 60,
            child: Text(
              'ID',
              style: AppTextStyles.subtitle.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(width: 16),
        ],
        
        // 名称
        Expanded(
          flex: 2,
          child: Text(
            '分类名称',
            style: AppTextStyles.subtitle.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        
        // 描述
        if (isDesktop) ...[
          const SizedBox(width: 16),
          Expanded(
            flex: 3,
            child: Text(
              '描述',
              style: AppTextStyles.subtitle.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
        
        // 状态
        const SizedBox(width: 16),
        SizedBox(
          width: 80,
          child: Text(
            '状态',
            style: AppTextStyles.subtitle.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        
        // 操作
        const SizedBox(width: 16),
        SizedBox(
          width: isDesktop ? 150 : 100,
          child: Text(
            '操作',
            style: AppTextStyles.subtitle.copyWith(
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }
  
  /// 构建分类项
  Widget _buildCategoryItem(
    BuildContext context,
    CategoryController controller,
    RecyclingCategory category,
    bool isDesktop,
  ) {
    return InkWell(
      onTap: () => _navigateToEditCategory(context, category),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0),
        child: Row(
          children: [
            // ID
            if (isDesktop) ...[
              SizedBox(
                width: 60,
                child: Text(
                  '${category.id}',
                  style: AppTextStyles.body,
                ),
              ),
              const SizedBox(width: 16),
            ],
            
            // 名称
            Expanded(
              flex: 2,
              child: Text(
                category.name,
                style: AppTextStyles.body.copyWith(
                  fontWeight: FontWeight.bold,
                  color: category.isActive ? AppColors.textPrimary : AppColors.textDisabled,
                ),
              ),
            ),
            
            // 描述
            if (isDesktop) ...[
              const SizedBox(width: 16),
              Expanded(
                flex: 3,
                child: Text(
                  category.description ?? '-',
                  style: AppTextStyles.body.copyWith(
                    color: category.isActive ? AppColors.textSecondary : AppColors.textDisabled,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
            
            // 状态
            const SizedBox(width: 16),
            SizedBox(
              width: 80,
              child: _buildStatusChip(category.isActive),
            ),
            
            // 操作
            const SizedBox(width: 16),
            SizedBox(
              width: isDesktop ? 150 : 100,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // 编辑按钮
                  IconButton(
                    icon: const Icon(Icons.edit, size: 20),
                    onPressed: () => _navigateToEditCategory(context, category),
                    tooltip: '编辑',
                    color: AppColors.primary,
                  ),
                  
                  // 状态切换按钮
                  IconButton(
                    icon: Icon(
                      category.isActive ? Icons.toggle_on : Icons.toggle_off,
                      size: 20,
                    ),
                    onPressed: () => _toggleCategoryStatus(context, controller, category),
                    tooltip: category.isActive ? '禁用' : '启用',
                    color: category.isActive ? AppColors.success : AppColors.textDisabled,
                  ),
                  
                  // 删除按钮
                  IconButton(
                    icon: const Icon(Icons.delete, size: 20),
                    onPressed: () => _confirmDeleteCategory(context, controller, category),
                    tooltip: '删除',
                    color: AppColors.error,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  /// 构建状态标签
  Widget _buildStatusChip(bool isActive) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: isActive ? AppColors.success.withOpacity(0.1) : AppColors.error.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isActive ? AppColors.success.withOpacity(0.5) : AppColors.error.withOpacity(0.5),
        ),
      ),
      child: Text(
        isActive ? '启用' : '禁用',
        style: AppTextStyles.caption.copyWith(
          color: isActive ? AppColors.success : AppColors.error,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
  
  /// 导航到添加分类页面
  void _navigateToAddCategory(BuildContext context) {
    Get.to(() => const CategoryFormPage());
  }
  
  /// 导航到编辑分类页面
  void _navigateToEditCategory(BuildContext context, RecyclingCategory category) {
    Get.to(() => CategoryFormPage(category: category));
  }
  
  /// 切换分类状态
  void _toggleCategoryStatus(
    BuildContext context,
    CategoryController controller,
    RecyclingCategory category,
  ) {
    final newStatus = !category.isActive;
    final statusText = newStatus ? '启用' : '禁用';
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('确认$statusText'),
        content: Text('确定要$statusText分类"${category.name}"吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              controller.updateCategoryStatus(category.id, newStatus);
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
  
  /// 确认删除分类
  void _confirmDeleteCategory(
    BuildContext context,
    CategoryController controller,
    RecyclingCategory category,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除分类"${category.name}"吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              controller.deleteCategory(category.id);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
            ),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }
} 