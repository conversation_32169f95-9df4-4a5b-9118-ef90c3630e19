import 'package:get/get.dart';
import 'package:flutter/material.dart';

import 'base_controller.dart';
import '../models/paginated_response.dart';

/// 通用分页控制器基类
/// 提供标准的分页数据管理功能
abstract class PaginatedController<T> extends BaseController {
  // 数据列表
  final RxList<T> items = <T>[].obs;
  
  // 分页信息
  final RxInt currentPage = 1.obs;
  final RxInt totalPages = 1.obs;
  final RxInt totalRecords = 0.obs;
  final int pageSize = 20;
  
  // 加载状态
  final RxBool hasNextPage = false.obs;
  final RxBool isLoadingMore = false.obs;
  final RxBool isRefreshing = false.obs;
  
  // 搜索和筛选
  final RxString searchQuery = ''.obs;
  final TextEditingController searchController = TextEditingController();
  
  // 下拉刷新控制器
  final refreshController = RefreshController();

  /// 抽象方法：子类必须实现数据获取逻辑
  Future<PaginatedResponse<T>> fetchItems(int page, int pageSize, Map<String, dynamic> filters);

  @override
  void onInit() {
    super.onInit();
    
    // 监听搜索查询变化
    debounce(
      searchQuery,
      (_) => refresh(),
      time: const Duration(milliseconds: 500),
    );
    
    // 初始加载数据
    loadFirstPage();
  }

  /// 获取当前筛选参数
  /// 子类可以重写此方法添加自定义筛选条件
  Map<String, dynamic> get filters => {
    if (searchQuery.value.isNotEmpty) 'search': searchQuery.value,
  };

  /// 加载首页数据
  Future<void> loadFirstPage() async {
    await executeWithLoading(() async {
      currentPage.value = 1;
      items.clear();
      await _loadPage();
    });
  }

  /// 刷新数据
  Future<void> refresh() async {
    if (isRefreshing.value || isLoading.value) return;
    
    try {
      isRefreshing.value = true;
      currentPage.value = 1;
      
      final response = await fetchItems(currentPage.value, pageSize, filters);
      
      items.value = response.items;
      _updatePaginationInfo(response);
      
      refreshController.refreshCompleted();
    } catch (e) {
      handleError(e, 'Refresh Data');
      refreshController.refreshFailed();
    } finally {
      isRefreshing.value = false;
    }
  }

  /// 加载更多数据
  Future<void> loadMore() async {
    if (!hasNextPage.value || isLoadingMore.value || isLoading.value) {
      refreshController.loadComplete();
      return;
    }

    try {
      isLoadingMore.value = true;
      currentPage.value++;
      
      final response = await fetchItems(currentPage.value, pageSize, filters);
      
      items.addAll(response.items);
      _updatePaginationInfo(response);
      
      if (hasNextPage.value) {
        refreshController.loadComplete();
      } else {
        refreshController.loadNoData();
      }
    } catch (e) {
      currentPage.value--; // 回滚页码
      handleError(e, 'Load More Data');
      refreshController.loadFailed();
    } finally {
      isLoadingMore.value = false;
    }
  }

  /// 内部加载页面数据
  Future<void> _loadPage() async {
    final response = await fetchItems(currentPage.value, pageSize, filters);
    
    if (currentPage.value == 1) {
      items.value = response.items;
    } else {
      items.addAll(response.items);
    }
    
    _updatePaginationInfo(response);
  }

  /// 更新分页信息
  void _updatePaginationInfo(PaginatedResponse<T> response) {
    totalPages.value = response.totalPages;
    totalRecords.value = response.total;
    hasNextPage.value = currentPage.value < totalPages.value;
  }

  /// 搜索
  void search(String query) {
    searchController.text = query;
    searchQuery.value = query;
  }

  /// 清除搜索
  void clearSearch() {
    searchController.clear();
    searchQuery.value = '';
  }

  /// 添加单个项目
  void addItem(T item) {
    items.insert(0, item);
    totalRecords.value++;
  }

  /// 更新单个项目
  void updateItem(T oldItem, T newItem) {
    final index = items.indexOf(oldItem);
    if (index != -1) {
      items[index] = newItem;
    }
  }

  /// 删除单个项目
  void removeItem(T item) {
    if (items.remove(item)) {
      totalRecords.value--;
    }
  }

  /// 删除指定索引的项目
  void removeItemAt(int index) {
    if (index >= 0 && index < items.length) {
      items.removeAt(index);
      totalRecords.value--;
    }
  }

  /// 清空所有数据
  void clearItems() {
    items.clear();
    currentPage.value = 1;
    totalPages.value = 1;
    totalRecords.value = 0;
    hasNextPage.value = false;
  }

  /// 检查是否为空
  bool get isEmpty => items.isEmpty;

  /// 检查是否有数据
  bool get hasData => items.isNotEmpty;

  /// 获取总条目数
  int get itemCount => items.length;

  /// 是否正在加载（首次加载或刷新）
  bool get isLoadingData => isLoading.value || isRefreshing.value;

  /// 构建列表项
  Widget buildListItem(BuildContext context, int index) {
    if (index < items.length) {
      return buildItemWidget(context, items[index], index);
    }
    return const SizedBox.shrink();
  }

  /// 构建单个项目组件 - 子类需要实现
  Widget buildItemWidget(BuildContext context, T item, int index);

  /// 构建加载更多指示器
  Widget buildLoadMoreIndicator() {
    return Obx(() {
      if (isLoadingMore.value) {
        return const Padding(
          padding: EdgeInsets.all(16),
          child: Center(
            child: CircularProgressIndicator(),
          ),
        );
      }
      return const SizedBox.shrink();
    });
  }

  /// 构建空状态组件
  Widget buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inbox,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            getEmptyMessage(),
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
          if (searchQuery.value.isNotEmpty) ...[
            const SizedBox(height: 8),
            TextButton(
              onPressed: clearSearch,
              child: const Text('清除搜索条件'),
            ),
          ],
        ],
      ),
    );
  }

  /// 获取空状态提示文本 - 子类可以重写
  String getEmptyMessage() {
    if (searchQuery.value.isNotEmpty) {
      return '未找到相关数据';
    }
    return '暂无数据';
  }

  @override
  void onClose() {
    searchController.dispose();
    refreshController.dispose();
    super.onClose();
  }
}

/// 刷新控制器（简化版）
class RefreshController {
  void refreshCompleted() {}
  void refreshFailed() {}
  void loadComplete() {}
  void loadNoData() {}
  void loadFailed() {}
  void dispose() {}
}