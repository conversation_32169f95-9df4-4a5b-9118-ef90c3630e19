# 旧料回收详情对话框

## 功能概述

本模块实现了旧料回收管理中的"查看"按钮详情页面功能，提供了完整的UI界面和功能实现。

## 主要文件

### RecyclingDetailDialog
- **文件路径**: `recycling_detail_dialog.dart`
- **功能**: 旧料回收详情查看对话框
- **设计参考**: 库存管理中的入库单详情界面

### RecyclingDetailTest
- **文件路径**: `recycling_detail_test.dart`
- **功能**: 测试页面，用于验证详情对话框功能

## 功能特性

### 1. UI设计
- ✅ 参考入库单详情界面的布局结构和样式风格
- ✅ 保持界面一致性和用户体验
- ✅ 响应式设计，支持移动端和桌面端
- ✅ 使用Card组件和统一的主题样式

### 2. 数据展示
- ✅ **基本信息**: 回收单号、客户姓名、客户电话、门店、回收时间、操作员、状态、备注
- ✅ **旧料明细**: 物品名称、分类、重量、单价、状态、回收金额、备注
- ✅ **汇总信息**: 总件数、总金额、总重量

### 3. 交互功能
- ✅ 点击"查看"按钮打开详情对话框
- ✅ 关闭按钮和返回功能
- ✅ 复制信息到剪贴板功能
- ✅ 响应式布局适配不同屏幕尺寸

### 4. 技术实现
- ✅ 正确的路由配置和数据获取
- ✅ 加载状态和错误状态处理
- ✅ 代码结构一致性和可维护性
- ✅ 遵循Flutter最佳实践

## 使用方法

### 1. 在旧料回收管理页面中使用

```dart
// 在操作按钮中调用
void _navigateToRecyclingDetail(int id) {
  final controller = Get.find<RecyclingController>();
  final recyclingOrder = controller.recyclingOrders.firstWhereOrNull((order) => order.id == id);
  
  if (recyclingOrder != null) {
    Get.dialog(
      RecyclingDetailDialog(recyclingOrder: recyclingOrder),
      barrierDismissible: true,
    );
  }
}
```

### 2. 测试功能

```dart
// 导入测试页面
import 'widgets/recycling_detail_test.dart';

// 在路由中添加测试页面
Get.to(() => const RecyclingDetailTest());
```

## 数据模型

### RecyclingOrder
- `id`: 回收单ID
- `orderNo`: 回收单号
- `customerName`: 客户姓名
- `customerPhone`: 客户电话
- `storeName`: 门店名称
- `orderDate`: 回收时间
- `creatorName`: 操作员
- `status`: 状态
- `remark`: 备注
- `items`: 旧料明细列表
- `itemCount`: 总件数
- `totalAmount`: 总金额
- `totalWeight`: 总重量

### RecyclingItem
- `itemName`: 物品名称
- `categoryName`: 分类
- `weight`: 重量
- `price`: 单价
- `amount`: 回收金额
- `status`: 状态
- `remark`: 备注

## 样式规范

### 颜色主题
- 主色调: `AppTheme.primaryColor`
- 状态颜色: 
  - 待处理: `Colors.orange`
  - 已处理: `Colors.blue`
  - 已完成: `Colors.green`
  - 已取消: `Colors.red`

### 布局规范
- 对话框最大宽度: 1200px
- 对话框最大高度: 800px
- 圆角半径: 16px (对话框), 12px (卡片), 8px (小组件)
- 内边距: 20px (主要区域), 16px (卡片), 12px (小组件)

## 注意事项

1. **数据字段映射**: 确保RecyclingItem模型的字段与实际API返回的数据结构匹配
2. **响应式设计**: 在不同屏幕尺寸下测试界面显示效果
3. **错误处理**: 处理数据为空或加载失败的情况
4. **性能优化**: 大量数据时考虑分页或虚拟滚动

## 后续优化

1. 添加打印功能
2. 支持编辑功能（根据权限）
3. 添加图片预览功能
4. 支持导出功能
5. 添加操作日志记录
