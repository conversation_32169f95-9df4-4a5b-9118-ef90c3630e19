"""
库存盘点数据模型
对应FastAdmin数据库中的库存盘点相关表结构

老板，这个模块定义了库存盘点的数据模型：
1. InventoryCheck - 库存盘点主表
2. InventoryCheckItem - 库存盘点明细表

完全匹配FastAdmin数据库结构。
"""

from sqlalchemy import Column, Integer, String, Text, ForeignKey, DateTime
from sqlalchemy.orm import relationship
from ..core.database import Base


class InventoryCheck(Base):
    """库存盘点主表 - 对应 fa_inventory_check"""
    __tablename__ = "fa_inventory_check"
    
    id = Column(Integer, primary_key=True, index=True, comment="盘点单ID")
    check_no = Column(String(50), unique=True, nullable=False, comment="盘点单号")
    store_id = Column(Integer, ForeignKey("fa_store.id"), nullable=False, comment="盘点门店")
    status = Column(Integer, default=0, comment="状态:0=进行中,1=已完成,2=已取消")
    start_time = Column(Integer, comment="开始时间")
    end_time = Column(Integer, comment="结束时间")
    operator_id = Column(Integer, ForeignKey("fa_admin.id"), nullable=False, comment="操作员ID")
    total_count = Column(Integer, default=0, comment="应盘总数")
    checked_count = Column(Integer, default=0, comment="已盘数量")
    difference_count = Column(Integer, default=0, comment="差异数量")
    remark = Column(Text, comment="备注")
    createtime = Column(Integer, comment="创建时间")
    updatetime = Column(Integer, comment="更新时间")
    
    # 关联关系 - 修复外键引用问题
    store = relationship("Store", back_populates="inventory_checks")
    operator = relationship("Admin", foreign_keys=[operator_id], back_populates="inventory_checks")
    items = relationship("InventoryCheckItem", back_populates="check", cascade="all, delete-orphan")


class InventoryCheckItem(Base):
    """库存盘点明细表 - 对应 fa_inventory_check_item"""
    __tablename__ = "fa_inventory_check_item"
    
    id = Column(Integer, primary_key=True, index=True, comment="明细ID")
    check_id = Column(Integer, ForeignKey("fa_inventory_check.id"), nullable=False, comment="盘点单ID")
    jewelry_id = Column(Integer, ForeignKey("fa_jewelry.id"), nullable=False, comment="商品ID")
    barcode = Column(String(50), nullable=False, comment="条码")
    name = Column(String(100), nullable=False, comment="商品名称")
    category_id = Column(Integer, ForeignKey("fa_jewelry_category.id"), nullable=False, comment="分类ID")
    ring_size = Column(String(20), comment="圈口号")
    status = Column(Integer, default=0, comment="状态:0=未盘点,1=已盘点")
    system_stock = Column(Integer, default=1, comment="系统库存")
    actual_stock = Column(Integer, comment="实际库存")
    difference = Column(Integer, comment="差异")
    check_time = Column(Integer, comment="盘点时间")
    check_user_id = Column(Integer, ForeignKey("fa_admin.id"), comment="盘点人员ID")
    remark = Column(String(255), comment="备注")
    createtime = Column(Integer, comment="创建时间")
    
    # 关联关系 - 修复外键引用问题
    check = relationship("InventoryCheck", back_populates="items")
    jewelry = relationship("Jewelry", back_populates="inventory_check_items")
    category = relationship("JewelryCategory", back_populates="inventory_check_items")
    check_user = relationship("Admin", foreign_keys=[check_user_id], back_populates="inventory_check_items")
