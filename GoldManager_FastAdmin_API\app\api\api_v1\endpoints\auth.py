"""
认证API端点
提供用户登录、登出、令牌刷新等功能
"""

from fastapi import APIRouter, Depends, HTTPException, Request, status
from sqlalchemy.orm import Session

from ....core.database import get_db
from ....core.dependencies import (
    get_current_user,
    get_current_active_user,
    get_client_ip
)
from ....services.auth_service import AuthService
from ....schemas.auth import (
    LoginRequest,
    LoginResponse,
    RefreshTokenRequest,
    RefreshTokenResponse,
    ChangePasswordRequest,
    CurrentUserResponse,
    LogoutResponse,
    LoginAttemptResponse
)
from ....schemas.common import SuccessResponse

# 创建路由器
router = APIRouter()


@router.post("/login", response_model=LoginResponse, summary="用户登录")
async def login(
    request: Request,
    login_data: LoginRequest,
    db: Session = Depends(get_db)
):
    """
    用户登录

    - **username**: 用户名
    - **password**: 密码
    - **remember_me**: 是否记住登录状态
    """
    auth_service = AuthService(db)
    client_ip = get_client_ip(request)

    try:
        result = await auth_service.login(login_data, client_ip)
        return result
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e)
        )


@router.post("/logout", response_model=LogoutResponse, summary="用户登出")
async def logout(
    current_user: CurrentUserResponse = Depends(get_current_user)
):
    """
    用户登出

    注销当前用户的登录状态
    """
    # 这里可以添加令牌黑名单逻辑
    return LogoutResponse(message="登出成功")


@router.post("/refresh", response_model=RefreshTokenResponse, summary="刷新令牌")
async def refresh_token(
    refresh_data: RefreshTokenRequest,
    db: Session = Depends(get_db)
):
    """
    刷新访问令牌

    - **refresh_token**: 刷新令牌
    """
    auth_service = AuthService(db)

    try:
        result = await auth_service.refresh_token(refresh_data)
        return result
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e)
        )


@router.get("/me", response_model=CurrentUserResponse, summary="获取当前用户信息")
async def get_current_user_info(
    current_user: CurrentUserResponse = Depends(get_current_active_user)
):
    """
    获取当前登录用户的详细信息

    包括用户基本信息、权限列表、所属门店等
    """
    return current_user


@router.post("/change-password", response_model=SuccessResponse, summary="修改密码")
async def change_password(
    password_data: ChangePasswordRequest,
    current_user: CurrentUserResponse = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    修改当前用户密码

    - **old_password**: 原密码
    - **new_password**: 新密码
    - **confirm_password**: 确认新密码
    """
    auth_service = AuthService(db)

    try:
        result = await auth_service.change_password(current_user.id, password_data)
        return SuccessResponse(message=result["message"])
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/login-attempts/{username}", response_model=LoginAttemptResponse, summary="获取登录尝试信息")
async def get_login_attempts(
    username: str,
    db: Session = Depends(get_db)
):
    """
    获取指定用户名的登录尝试信息

    - **username**: 用户名
    """
    auth_service = AuthService(db)
    result = await auth_service.get_login_attempts(username)
    return result