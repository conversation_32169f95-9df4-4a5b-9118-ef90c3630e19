/// 回收处理相关模型（新版本）
/// 包含处理工单、处理结果、转库存记录等

import '../store/store.dart';
import '../auth/user.dart';
import 'recycling_model.dart';

/// 处理类型枚举
enum ProcessType {
  separation(1, '金银分离'),
  refurbish(2, '翻新加工'),
  melt(3, '直接熔炼');

  final int value;
  final String label;
  const ProcessType(this.value, this.label);

  static ProcessType fromValue(int value) {
    return ProcessType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => ProcessType.separation,
    );
  }
}

/// 处理状态枚举
enum ProcessStatus {
  cancelled(0, '已取消'),
  pending(1, '待处理'),
  processing(2, '处理中'),
  completed(3, '已完成');

  final int value;
  final String label;
  const ProcessStatus(this.value, this.label);

  static ProcessStatus fromValue(int value) {
    return ProcessStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => ProcessStatus.pending,
    );
  }
}

/// 结果类型枚举
enum ResultType {
  pureGold(1, '纯金'),
  pureSilver(2, '纯银'),
  finishedJewelry(3, '成品首饰'),
  bullion(4, '金银锭');

  final int value;
  final String label;
  const ResultType(this.value, this.label);

  static ResultType fromValue(int value) {
    return ResultType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => ResultType.pureGold,
    );
  }
}

/// 库存类型枚举
enum StockType {
  material(1, '原料库存'),
  jewelry(2, '商品库存');

  final int value;
  final String label;
  const StockType(this.value, this.label);

  static StockType fromValue(int value) {
    return StockType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => StockType.material,
    );
  }
}

/// 回收处理工单模型
class RecyclingProcessNew {
  final int id;
  final String processNo;
  final int storeId;
  final int sourceStoreId;
  final int recyclingId;
  final List<int> recyclingItemIds;
  final ProcessType processType;
  final double totalWeight;
  final double estimatedGoldWeight;
  final double estimatedSilverWeight;
  final ProcessStatus status;
  final DateTime? startTime;
  final DateTime? endTime;
  final int operatorId;
  final int? processorId;
  final String? remark;
  final DateTime createTime;
  final DateTime updateTime;
  
  // 关联数据
  final Store? store;
  final Store? sourceStore;
  final RecyclingModel? recycling;
  final String? recyclingNo;
  final String? operatorName;
  final String? processorName;
  final List<ProcessResult>? results;

  RecyclingProcessNew({
    required this.id,
    required this.processNo,
    required this.storeId,
    required this.sourceStoreId,
    required this.recyclingId,
    required this.recyclingItemIds,
    required this.processType,
    required this.totalWeight,
    required this.estimatedGoldWeight,
    required this.estimatedSilverWeight,
    required this.status,
    this.startTime,
    this.endTime,
    required this.operatorId,
    this.processorId,
    this.remark,
    required this.createTime,
    required this.updateTime,
    this.store,
    this.sourceStore,
    this.recycling,
    this.recyclingNo,
    this.operatorName,
    this.processorName,
    this.results,
  });

  factory RecyclingProcessNew.fromJson(Map<String, dynamic> json) {
    List<int> parseItemIds() {
      if (json['recycling_item_ids'] == null) return [];
      if (json['recycling_item_ids'] is List) {
        return List<int>.from(json['recycling_item_ids']);
      }
      if (json['recycling_item_ids'] is String) {
        try {
          // 尝试解析JSON字符串
          final List<dynamic> ids = List<dynamic>.from(
            json['recycling_item_ids'].startsWith('[')
                ? json['recycling_item_ids']
                : '[$json['recycling_item_ids']]'
          );
          return ids.map((e) => int.tryParse(e.toString()) ?? 0).toList();
        } catch (e) {
          // 如果不是JSON，尝试逗号分隔
          return json['recycling_item_ids']
              .split(',')
              .map((e) => int.tryParse(e.trim()) ?? 0)
              .toList();
        }
      }
      return [];
    }

    return RecyclingProcessNew(
      id: json['id'] ?? 0,
      processNo: json['process_no'] ?? '',
      storeId: json['store_id'] ?? 0,
      sourceStoreId: json['source_store_id'] ?? 0,
      recyclingId: json['recycling_id'] ?? 0,
      recyclingItemIds: parseItemIds(),
      processType: ProcessType.fromValue(json['process_type'] ?? 1),
      totalWeight: double.tryParse(json['total_weight']?.toString() ?? '0') ?? 0,
      estimatedGoldWeight: double.tryParse(json['estimated_gold_weight']?.toString() ?? '0') ?? 0,
      estimatedSilverWeight: double.tryParse(json['estimated_silver_weight']?.toString() ?? '0') ?? 0,
      status: ProcessStatus.fromValue(json['status'] ?? 1),
      startTime: json['start_time'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['start_time'] * 1000)
          : null,
      endTime: json['end_time'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['end_time'] * 1000)
          : null,
      operatorId: json['operator_id'] ?? 0,
      processorId: json['processor_id'],
      remark: json['remark'],
      createTime: DateTime.fromMillisecondsSinceEpoch((json['createtime'] ?? 0) * 1000),
      updateTime: DateTime.fromMillisecondsSinceEpoch((json['updatetime'] ?? 0) * 1000),
      store: json['store'] != null ? Store.fromJson(json['store']) : null,
      sourceStore: json['source_store'] != null ? Store.fromJson(json['source_store']) : null,
      recycling: json['recycling'] != null ? RecyclingModel.fromJson(json['recycling']) : null,
      recyclingNo: json['recycling_no'],
      operatorName: json['operator_name'],
      processorName: json['processor_name'],
      results: json['results'] != null
          ? (json['results'] as List).map((e) => ProcessResult.fromJson(e)).toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'store_id': storeId,
      'recycling_id': recyclingId,
      'recycling_item_ids': recyclingItemIds,
      'process_type': processType.value,
      'remark': remark,
    };
  }

  /// 是否可以编辑
  bool get canEdit => status == ProcessStatus.pending;

  /// 是否可以开始处理
  bool get canStart => status == ProcessStatus.pending;

  /// 是否可以完成
  bool get canComplete => status == ProcessStatus.processing;

  /// 是否可以取消
  bool get canCancel => status != ProcessStatus.completed;

  /// 获取状态颜色
  String getStatusColor() {
    switch (status) {
      case ProcessStatus.cancelled:
        return '#909399';
      case ProcessStatus.pending:
        return '#E6A23C';
      case ProcessStatus.processing:
        return '#409EFF';
      case ProcessStatus.completed:
        return '#67C23A';
    }
  }
}

/// 处理结果模型
class ProcessResult {
  final int id;
  final int processId;
  final ResultType resultType;
  final String name;
  final double weight;
  final double? purity;
  final double lossWeight;
  final double lossRate;
  final double processCost;
  final double laborCost;
  final double otherCost;
  final double totalCost;
  final int? targetType;
  final int? targetId;
  final bool isConverted;
  final DateTime? convertTime;
  final DateTime createTime;

  ProcessResult({
    required this.id,
    required this.processId,
    required this.resultType,
    required this.name,
    required this.weight,
    this.purity,
    required this.lossWeight,
    required this.lossRate,
    required this.processCost,
    required this.laborCost,
    required this.otherCost,
    required this.totalCost,
    this.targetType,
    this.targetId,
    required this.isConverted,
    this.convertTime,
    required this.createTime,
  });

  factory ProcessResult.fromJson(Map<String, dynamic> json) {
    return ProcessResult(
      id: json['id'] ?? 0,
      processId: json['process_id'] ?? 0,
      resultType: ResultType.fromValue(json['result_type'] ?? 1),
      name: json['name'] ?? '',
      weight: double.tryParse(json['weight']?.toString() ?? '0') ?? 0,
      purity: json['purity'] != null ? double.tryParse(json['purity'].toString()) : null,
      lossWeight: double.tryParse(json['loss_weight']?.toString() ?? '0') ?? 0,
      lossRate: double.tryParse(json['loss_rate']?.toString() ?? '0') ?? 0,
      processCost: double.tryParse(json['process_cost']?.toString() ?? '0') ?? 0,
      laborCost: double.tryParse(json['labor_cost']?.toString() ?? '0') ?? 0,
      otherCost: double.tryParse(json['other_cost']?.toString() ?? '0') ?? 0,
      totalCost: double.tryParse(json['total_cost']?.toString() ?? '0') ?? 0,
      targetType: json['target_type'],
      targetId: json['target_id'],
      isConverted: json['is_converted'] ?? false,
      convertTime: json['convert_time'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['convert_time'] * 1000)
          : null,
      createTime: DateTime.fromMillisecondsSinceEpoch((json['createtime'] ?? 0) * 1000),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'result_type': resultType.value,
      'name': name,
      'weight': weight,
      'purity': purity,
      'loss_weight': lossWeight,
      'loss_rate': lossRate,
      'process_cost': processCost,
      'labor_cost': laborCost,
      'other_cost': otherCost,
    };
  }
}

/// 回收转库存记录模型
class RecyclingToStock {
  final int id;
  final String convertNo;
  final int storeId;
  final int processResultId;
  final StockType stockType;
  final int? stockInId;
  final int? jewelryId;
  final int? materialId;
  final int quantity;
  final double weight;
  final double unitCost;
  final double totalCost;
  final Map<String, dynamic>? costDetail;
  final int status;
  final int operatorId;
  final String? remark;
  final DateTime createTime;
  
  // 关联数据
  final String? storeName;
  final String? operatorName;
  final ProcessResult? processResult;

  RecyclingToStock({
    required this.id,
    required this.convertNo,
    required this.storeId,
    required this.processResultId,
    required this.stockType,
    this.stockInId,
    this.jewelryId,
    this.materialId,
    required this.quantity,
    required this.weight,
    required this.unitCost,
    required this.totalCost,
    this.costDetail,
    required this.status,
    required this.operatorId,
    this.remark,
    required this.createTime,
    this.storeName,
    this.operatorName,
    this.processResult,
  });

  factory RecyclingToStock.fromJson(Map<String, dynamic> json) {
    Map<String, dynamic>? parseCostDetail(dynamic data) {
      if (data == null) return null;
      if (data is Map<String, dynamic>) return data;
      if (data is String) {
        try {
          return Map<String, dynamic>.from(data);
        } catch (e) {
          return null;
        }
      }
      return null;
    }

    return RecyclingToStock(
      id: json['id'] ?? 0,
      convertNo: json['convert_no'] ?? '',
      storeId: json['store_id'] ?? 0,
      processResultId: json['process_result_id'] ?? 0,
      stockType: StockType.fromValue(json['stock_type'] ?? 1),
      stockInId: json['stock_in_id'],
      jewelryId: json['jewelry_id'],
      materialId: json['material_id'],
      quantity: json['quantity'] ?? 1,
      weight: double.tryParse(json['weight']?.toString() ?? '0') ?? 0,
      unitCost: double.tryParse(json['unit_cost']?.toString() ?? '0') ?? 0,
      totalCost: double.tryParse(json['total_cost']?.toString() ?? '0') ?? 0,
      costDetail: parseCostDetail(json['cost_detail']),
      status: json['status'] ?? 1,
      operatorId: json['operator_id'] ?? 0,
      remark: json['remark'],
      createTime: DateTime.fromMillisecondsSinceEpoch((json['createtime'] ?? 0) * 1000),
      storeName: json['store_name'],
      operatorName: json['operator_name'],
      processResult: json['process_result'] != null
          ? ProcessResult.fromJson(json['process_result'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      'store_id': storeId,
      'process_result_id': processResultId,
      'stock_type': stockType.value,
      'quantity': quantity,
      'remark': remark,
    };
    
    // 商品库存时需要的额外信息
    if (stockType == StockType.jewelry) {
      // 这里需要根据实际需求添加商品信息
    }
    
    // 原料库存时需要的额外信息
    if (stockType == StockType.material) {
      // 这里需要根据实际需求添加原料类型
    }
    
    return data;
  }
}

/// 原料模型
class Material {
  final int id;
  final String materialNo;
  final String name;
  final int type;
  final double? purity;
  final String unit;
  final int storeId;
  final double currentStock;
  final double unitCost;
  final int status;
  final DateTime createTime;
  final DateTime updateTime;
  
  // 关联数据
  final String? storeName;

  Material({
    required this.id,
    required this.materialNo,
    required this.name,
    required this.type,
    this.purity,
    required this.unit,
    required this.storeId,
    required this.currentStock,
    required this.unitCost,
    required this.status,
    required this.createTime,
    required this.updateTime,
    this.storeName,
  });

  factory Material.fromJson(Map<String, dynamic> json) {
    return Material(
      id: json['id'] ?? 0,
      materialNo: json['material_no'] ?? '',
      name: json['name'] ?? '',
      type: json['type'] ?? 1,
      purity: json['purity'] != null ? double.tryParse(json['purity'].toString()) : null,
      unit: json['unit'] ?? '克',
      storeId: json['store_id'] ?? 0,
      currentStock: double.tryParse(json['current_stock']?.toString() ?? '0') ?? 0,
      unitCost: double.tryParse(json['unit_cost']?.toString() ?? '0') ?? 0,
      status: json['status'] ?? 1,
      createTime: DateTime.fromMillisecondsSinceEpoch((json['createtime'] ?? 0) * 1000),
      updateTime: DateTime.fromMillisecondsSinceEpoch((json['updatetime'] ?? 0) * 1000),
      storeName: json['store_name'],
    );
  }

  /// 获取原料类型标签
  String get typeLabel {
    switch (type) {
      case 1:
        return '黄金';
      case 2:
        return '白银';
      case 3:
        return '其他';
      default:
        return '未知';
    }
  }
}