import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/constants/border_styles.dart';
import '../../../services/auth_service.dart';
import '../controllers/store_transfer_form_controller.dart';

/// 库存调拨表单内容组件 - 100%复制新建出库单UI布局
class StoreTransferFormContent extends StatefulWidget {
  final String? tag; // 控制器标签，用于标签页模式

  const StoreTransferFormContent({super.key, this.tag});

  @override
  State<StoreTransferFormContent> createState() =>
      _StoreTransferFormContentState();
}

class _StoreTransferFormContentState extends State<StoreTransferFormContent> {
  // 条码扫描控制器
  final TextEditingController _barcodeController = TextEditingController();
  // TextEditingController管理器，避免重复创建导致输入错误
  final Map<String, TextEditingController> _textControllers = {};

  @override
  void dispose() {
    _barcodeController.dispose();
    // 清理所有TextEditingController
    for (final controller in _textControllers.values) {
      controller.dispose();
    }
    _textControllers.clear();
    super.dispose();
  }

  // 获取控制器实例
  StoreTransferFormController get controller {
    return Get.find<StoreTransferFormController>(tag: widget.tag);
  }

  /// 获取或创建TextEditingController，避免重复创建导致输入错误
  TextEditingController _getOrCreateController(String id, String initialValue) {
    if (!_textControllers.containsKey(id)) {
      _textControllers[id] = TextEditingController(text: initialValue);
    } else {
      final textController = _textControllers[id]!;
      // 只在文本内容确实不同且用户不在编辑时才更新
      if (textController.text != initialValue) {
        // 检查当前值是否可以解析为相同的数字（避免格式差异导致的重复更新）
        final currentValue = double.tryParse(textController.text);
        final newValue = double.tryParse(initialValue);
        // 只有在数值确实不同时才更新
        if (currentValue != newValue) {
          textController.text = initialValue;
        }
      }
    }
    return _textControllers[id]!;
  }

  /// 🔧 智能价格格式化：只在必要时显示小数位，提升用户输入体验
  /// 例如：850.0 -> "850", 850.25 -> "850.25", 850.20 -> "850.2"
  String _formatPriceForInput(double price) {
    if (price == price.roundToDouble()) {
      // 如果是整数，不显示小数点
      return price.round().toString();
    } else {
      // 如果有小数，移除末尾的0
      String formatted = price.toStringAsFixed(2);
      if (formatted.endsWith('0')) {
        formatted = formatted.substring(0, formatted.length - 1);
      }
      return formatted;
    }
  }

  /// 🔧 验证和格式化用户输入的价格
  /// 确保输入符合价格格式要求（最多2位小数）
  String? _validatePriceInput(String input) {
    if (input.isEmpty) return null;

    // 移除多余的空格
    input = input.trim();

    // 检查是否为有效的数字格式
    final double? value = double.tryParse(input);
    if (value == null || value < 0) {
      return null; // 无效输入
    }

    // 检查小数位数不超过2位
    if (input.contains('.')) {
      final parts = input.split('.');
      if (parts.length == 2 && parts[1].length > 2) {
        return null; // 小数位超过2位
      }
    }

    return input; // 有效输入
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: controller.formKey,
      child: Column(
        children: [
          _buildFormHeader(),
          _buildBarcodeSection(),
          Expanded(child: _buildItemList()),
          _buildFormFooter(),
        ],
      ),
    );
  }

  /// 构建表单头部区域 - 100%复制新建出库单的设计
  Widget _buildFormHeader() {
    final authService = Get.find<AuthService>();

    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(bottom: AppBorderStyles.tableBorder),
      ),
      child: Row(
        children: [
          // 🔷 新建库存调拨（图标+标题）
          Icon(Icons.swap_horiz, color: Colors.blue[600], size: 20),
          const SizedBox(width: 8),
          const Text(
            '新建库存调拨',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),

          const SizedBox(width: 24),

          // 👤 操作员信息标签
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(
                AppBorderStyles.largeBorderRadius,
              ),
              border: Border.all(
                color: Colors.blue[200]!,
                width: AppBorderStyles.borderWidth,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.person, size: 14, color: Colors.blue[600]),
                const SizedBox(width: 4),
                Obx(
                  () => Text(
                    '操作员: ${authService.userNickname.value.isNotEmpty ? authService.userNickname.value : authService.userName.value}',
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.blue[700],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(width: 24),

          // 源门店选择
          const Text(
            '源门店:',
            style: TextStyle(fontSize: 13, fontWeight: FontWeight.w500),
          ),
          const SizedBox(width: 8),
          SizedBox(
            width: 150,
            height: 32,
            child: Obx(
              () => Container(
                height: 32,
                decoration: AppBorderStyles.standardBoxDecoration,
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<int>(
                    value: controller.sourceStoreId.value == 0
                        ? null
                        : controller.sourceStoreId.value,
                    hint: Container(
                      alignment: Alignment.center,
                      child: const Text(
                        '请选择源门店',
                        style: TextStyle(fontSize: 13, color: Colors.grey),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    isExpanded: true,
                    items: controller.availableStores.map((store) {
                      return DropdownMenuItem<int>(
                        value: store.id,
                        child: Text(
                          store.name,
                          style: const TextStyle(fontSize: 13),
                          textAlign: TextAlign.center,
                        ),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        controller.sourceStoreId.value = value;
                      }
                    },
                    style: const TextStyle(fontSize: 13, color: Colors.black87),
                    icon: const Icon(Icons.arrow_drop_down, size: 20),
                    iconSize: 20,
                    menuMaxHeight: 300,
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    selectedItemBuilder: (BuildContext context) {
                      return controller.availableStores.map<Widget>((store) {
                        return Container(
                          alignment: Alignment.center,
                          child: Text(
                            store.name,
                            style: const TextStyle(
                              fontSize: 13,
                              color: Colors.black87,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        );
                      }).toList();
                    },
                  ),
                ),
              ),
            ),
          ),

          const SizedBox(width: 24),

          // 目标门店选择
          const Text(
            '目标门店:',
            style: TextStyle(fontSize: 13, fontWeight: FontWeight.w500),
          ),
          const SizedBox(width: 8),
          SizedBox(
            width: 150,
            height: 32,
            child: Obx(
              () => Container(
                height: 32,
                decoration: AppBorderStyles.standardBoxDecoration,
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<int>(
                    value: controller.targetStoreId.value == 0
                        ? null
                        : controller.targetStoreId.value,
                    hint: Container(
                      alignment: Alignment.center,
                      child: const Text(
                        '请选择目标门店',
                        style: TextStyle(fontSize: 13, color: Colors.grey),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    isExpanded: true,
                    items: controller.availableStores.map((store) {
                      return DropdownMenuItem<int>(
                        value: store.id,
                        child: Text(
                          store.name,
                          style: const TextStyle(fontSize: 13),
                          textAlign: TextAlign.center,
                        ),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        controller.targetStoreId.value = value;
                      }
                    },
                    style: const TextStyle(fontSize: 13, color: Colors.black87),
                    icon: const Icon(Icons.arrow_drop_down, size: 20),
                    iconSize: 20,
                    menuMaxHeight: 300,
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    selectedItemBuilder: (BuildContext context) {
                      return controller.availableStores.map<Widget>((store) {
                        return Container(
                          alignment: Alignment.center,
                          child: Text(
                            store.name,
                            style: const TextStyle(
                              fontSize: 13,
                              color: Colors.black87,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        );
                      }).toList();
                    },
                  ),
                ),
              ),
            ),
          ),

          const SizedBox(width: 24),

          // 备注输入
          const Text(
            '备注:',
            style: TextStyle(fontSize: 13, fontWeight: FontWeight.w500),
          ),
          const SizedBox(width: 8),
          SizedBox(
            width: 200,
            height: 32,
            child: Container(
              height: 32,
              decoration: AppBorderStyles.standardBoxDecoration,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                child: TextField(
                  controller: controller.remarkController,
                  decoration: const InputDecoration(
                    hintText: '输入备注信息',
                    hintStyle: TextStyle(fontSize: 13, color: Colors.grey),
                    border: InputBorder.none,
                    enabledBorder: InputBorder.none,
                    focusedBorder: InputBorder.none,
                    errorBorder: InputBorder.none,
                    focusedErrorBorder: InputBorder.none,
                    disabledBorder: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(vertical: 6),
                    isDense: true,
                  ),
                  style: const TextStyle(fontSize: 13),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建条码扫描区域 - 100%复制新建出库单的设计
  Widget _buildBarcodeSection() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(bottom: AppBorderStyles.tableBorder),
      ),
      child: Row(
        children: [
          // 📦 扫描条码标题
          Icon(Icons.qr_code_scanner, color: Colors.blue[600], size: 20),
          const SizedBox(width: 8),
          const Text(
            '扫描条码',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),

          const SizedBox(width: 24),

          // 条码输入框
          SizedBox(
            width: 300,
            height: 32,
            child: TextFormField(
              controller: _barcodeController,
              decoration: AppBorderStyles.compactInputDecoration.copyWith(
                hintText: '请扫描或输入商品条码',
                prefixIcon: const Icon(Icons.qr_code, size: 18),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
              ),
              style: const TextStyle(fontSize: 13),
              onFieldSubmitted: (value) => _handleBarcodeInput(value),
            ),
          ),

          const SizedBox(width: 12),

          // 扫描按钮
          SizedBox(
            height: 32,
            child: ElevatedButton.icon(
              icon: const Icon(Icons.qr_code_scanner, size: 14),
              label: const Text('扫描'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue[600],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 0,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(
                    AppBorderStyles.borderRadius,
                  ),
                ),
                textStyle: const TextStyle(fontSize: 13),
              ),
              onPressed: _scanBarcode,
            ),
          ),

          const SizedBox(width: 12),

          // 批量改价按钮
          SizedBox(
            height: 32,
            child: ElevatedButton.icon(
              icon: const Icon(Icons.price_change, size: 14),
              label: const Text('批量改价'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange[600],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 0,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(
                    AppBorderStyles.borderRadius,
                  ),
                ),
                textStyle: const TextStyle(fontSize: 13),
              ),
              onPressed: _batchChangePrice,
            ),
          ),

          const SizedBox(width: 8),

          // 旧料回收按钮
          SizedBox(
            height: 32,
            child: ElevatedButton.icon(
              icon: const Icon(Icons.recycling, size: 14),
              label: const Text('旧料回收'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.purple[600],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 0,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(
                    AppBorderStyles.borderRadius,
                  ),
                ),
                textStyle: const TextStyle(fontSize: 13),
              ),
              onPressed: _oldMaterialRecycling,
            ),
          ),

          const SizedBox(width: 8),

          // 单收工费按钮
          SizedBox(
            height: 32,
            child: ElevatedButton.icon(
              icon: const Icon(Icons.build, size: 14),
              label: const Text('单收批发工费'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue[600],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 0,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(
                    AppBorderStyles.borderRadius,
                  ),
                ),
                textStyle: const TextStyle(fontSize: 13),
              ),
              onPressed: _singleWorkFee,
            ),
          ),
        ],
      ),
    );
  }

  /// 处理条码输入
  void _handleBarcodeInput(String barcode) {
    if (barcode.trim().isNotEmpty) {
      // 调用控制器的条码处理方法
      controller.scanBarcode(barcode.trim());
      _barcodeController.clear();
    }
  }

  /// 扫描条码
  void _scanBarcode() {
    // 获取当前输入框的内容并处理
    final barcode = _barcodeController.text.trim();
    if (barcode.isNotEmpty) {
      _handleBarcodeInput(barcode);
    } else {
      Get.snackbar(
        '提示',
        '请输入商品条码',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
    }
  }

  /// 批量改价
  void _batchChangePrice() {
    if (controller.transferItems.isEmpty) {
      Get.snackbar(
        '提示',
        '当前没有商品，无法进行批量改价',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      return;
    }

    _showBatchPriceChangeDialog();
  }

  /// 显示批量改价对话框
  void _showBatchPriceChangeDialog() {
    final goldPriceController = TextEditingController();
    final silverPriceController = TextEditingController();
    final workPriceController = TextEditingController();
    final pieceWorkPriceController = TextEditingController();

    Get.dialog(
      AlertDialog(
        title: Row(
          children: [
            Icon(Icons.price_change, color: Colors.orange[600], size: 20),
            const SizedBox(width: 8),
            const Text(
              '批量改价',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
          ],
        ),
        content: SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 提示信息
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(
                    AppBorderStyles.mediumBorderRadius,
                  ),
                  border: Border.all(
                    color: Colors.blue[200]!,
                    width: AppBorderStyles.borderWidth,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info_outline, color: Colors.blue[600], size: 16),
                    const SizedBox(width: 8),
                    const Expanded(
                      child: Text(
                        '只填写需要修改的价格项，空白项将保持原值不变',
                        style: TextStyle(fontSize: 12, color: Colors.black87),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),

              // 金价输入框
              _buildPriceInputField(
                '金价 (元/克)',
                goldPriceController,
                Icons.monetization_on,
                Colors.amber[600]!,
              ),
              const SizedBox(height: 12),

              // 银价输入框
              _buildPriceInputField(
                '银价 (元/克)',
                silverPriceController,
                Icons.circle,
                Colors.grey[600]!,
              ),
              const SizedBox(height: 12),

              // 批发工费输入框
              _buildPriceInputField(
                '批发工费 (元/克)',
                workPriceController,
                Icons.build,
                Colors.blue[600]!,
              ),
              const SizedBox(height: 12),

              // 件工费输入框
              _buildPriceInputField(
                '件工费 (元/件)',
                pieceWorkPriceController,
                Icons.handyman,
                Colors.green[600]!,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('取消')),
          ElevatedButton(
            onPressed: () {
              _executeBatchPriceChange(
                goldPriceController.text,
                silverPriceController.text,
                workPriceController.text,
                pieceWorkPriceController.text,
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange[600],
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(
                  AppBorderStyles.borderRadius,
                ),
              ),
            ),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  /// 构建价格输入字段
  Widget _buildPriceInputField(
    String label,
    TextEditingController controller,
    IconData icon,
    Color iconColor,
  ) {
    return Row(
      children: [
        Icon(icon, color: iconColor, size: 16),
        const SizedBox(width: 8),
        SizedBox(
          width: 80,
          child: Text(
            label,
            style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w500),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Container(
            height: 32,
            decoration: AppBorderStyles.standardBoxDecoration,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              child: TextField(
                controller: controller,
                keyboardType: const TextInputType.numberWithOptions(
                  decimal: true,
                ),
                decoration: const InputDecoration(
                  hintText: '可选',
                  hintStyle: TextStyle(fontSize: 12, color: Colors.grey),
                  border: InputBorder.none,
                  enabledBorder: InputBorder.none,
                  focusedBorder: InputBorder.none,
                  errorBorder: InputBorder.none,
                  focusedErrorBorder: InputBorder.none,
                  contentPadding: EdgeInsets.zero,
                  isDense: true,
                ),
                style: const TextStyle(fontSize: 13),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 执行批量改价
  void _executeBatchPriceChange(
    String goldPriceStr,
    String silverPriceStr,
    String workPriceStr,
    String pieceWorkPriceStr,
  ) {
    // 解析输入的价格
    double? goldPrice = goldPriceStr.isNotEmpty
        ? double.tryParse(goldPriceStr)
        : null;
    double? silverPrice = silverPriceStr.isNotEmpty
        ? double.tryParse(silverPriceStr)
        : null;
    double? workPrice = workPriceStr.isNotEmpty
        ? double.tryParse(workPriceStr)
        : null;
    double? pieceWorkPrice = pieceWorkPriceStr.isNotEmpty
        ? double.tryParse(pieceWorkPriceStr)
        : null;

    // 检查是否至少有一个价格被修改
    if (goldPrice == null &&
        silverPrice == null &&
        workPrice == null &&
        pieceWorkPrice == null) {
      Get.snackbar(
        '提示',
        '请至少填写一个价格项',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      return;
    }

    // 关闭对话框
    Get.back();

    // 显示确认对话框
    Get.dialog(
      AlertDialog(
        title: const Text('确认批量改价'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('即将对当前调拨单中的所有商品进行批量改价：'),
            const SizedBox(height: 8),
            if (goldPrice != null) Text('• 金价：${goldPrice.round()} 元/克'),
            if (silverPrice != null) Text('• 银价：${silverPrice.round()} 元/克'),
            if (workPrice != null) Text('• 批发工费：${workPrice.round()} 元/克'),
            if (pieceWorkPrice != null)
              Text('• 件工费：${pieceWorkPrice.round()} 元/件'),
            const SizedBox(height: 8),
            Text('共 ${controller.transferItems.length} 件商品将被更新'),
            const SizedBox(height: 8),
            const Text(
              '此操作不可撤销，请确认是否继续？',
              style: TextStyle(color: Colors.red, fontWeight: FontWeight.w500),
            ),
          ],
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('取消')),
          ElevatedButton(
            onPressed: () {
              Get.back();
              controller.batchUpdatePrices(
                goldPrice: goldPrice,
                silverPrice: silverPrice,
                workPrice: workPrice,
                pieceWorkPrice: pieceWorkPrice,
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red[600],
              foregroundColor: Colors.white,
            ),
            child: const Text('确认执行'),
          ),
        ],
      ),
    );
  }

  /// 旧料回收功能
  void _oldMaterialRecycling() {
    controller.handleOldMaterialRecycling();
  }

  /// 单收工费功能
  void _singleWorkFee() {
    controller.handleSingleWorkFee();
  }

  /// 构建商品列表区域 - 100%复制新建出库单的表格实现
  Widget _buildItemList() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: AppBorderStyles.elevatedBoxDecoration.copyWith(
        color: Colors.white,
      ),
      child: Column(
        children: [
          // 商品明细标题栏
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: const BoxDecoration(
              color: Colors.white,
              border: Border(bottom: AppBorderStyles.tableBorder),
            ),
            child: Row(
              children: [
                Icon(Icons.inventory_2, color: Colors.blue[600], size: 20),
                const SizedBox(width: 8),
                const Text(
                  '商品明细',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const Spacer(),
                // 商品数量显示
                Obx(() {
                  final itemCount = controller.transferItems.length;
                  return Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.blue[50],
                      borderRadius: BorderRadius.circular(
                        AppBorderStyles.borderRadius,
                      ),
                      border: Border.all(color: Colors.blue[200]!, width: 1),
                    ),
                    child: Text(
                      '共 $itemCount 件商品',
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.blue[700],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  );
                }),
              ],
            ),
          ),
          // 商品列表内容
          Expanded(
            child: Obx(
              () => controller.transferItems.isEmpty
                  ? _buildEmptyItemList()
                  : _buildTransferItemTable(),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建空商品列表
  Widget _buildEmptyItemList() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.inventory_2_outlined, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            '暂无调拨商品',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 8),
          Text('请扫描商品条码添加', style: TextStyle(fontSize: 14, color: Colors.grey)),
        ],
      ),
    );
  }

  /// 构建调拨商品表格 - 完整复制新建出库单的表格实现
  Widget _buildTransferItemTable() {
    return LayoutBuilder(
      builder: (context, constraints) {
        final availableWidth = constraints.maxWidth;

        // 使用比例宽度而非固定宽度，确保充分利用可用空间 - 完全复制出库单的列宽设置
        final indexWidth = availableWidth * 0.04; // 4% - 序号（减小）
        final barcodeWidth = availableWidth * 0.08; // 8% - 条码（增加）
        final nameWidth = availableWidth * 0.12; // 12% - 商品名称（增加）
        final categoryWidth = availableWidth * 0.08; // 8% - 分类（增加）
        final ringSizeWidth = availableWidth * 0.05; // 5% - 圈口号（不变）
        final goldWeightWidth = availableWidth * 0.06; // 6% - 金重（不变）
        final goldPriceWidth = availableWidth * 0.06; // 6% - 金价（不变）
        final silverWeightWidth = availableWidth * 0.06; // 6% - 银重（不变）
        final silverPriceWidth = availableWidth * 0.06; // 6% - 银价（不变）
        final totalWeightWidth = availableWidth * 0.06; // 6% - 总重（不变）
        final workFeeWidth = availableWidth * 0.07; // 7% - 工费（增加）
        final pieceWorkWidth = availableWidth * 0.07; // 7% - 件工费（增加）
        final priceWidth = availableWidth * 0.09; // 9% - 价格（增加）
        final actionWidth = availableWidth * 0.09; // 9% - 操作（从10%减少到9%）
        // 总计：4+8+12+8+5+6+6+6+6+6+7+7+9+9 = 99%（预留1%的弹性空间）

        // 总宽度就是可用宽度
        final totalWidth = availableWidth;

        // 根据可用宽度调整字体大小
        final fontSize = availableWidth < 1200 ? 11.0 : 13.0;

        return Container(
          width: double.infinity,
          height: double.infinity,
          margin: const EdgeInsets.all(4),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.03),
                blurRadius: 3,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Column(
            children: [
              // 表格标题行 - 固定显示，不在滚动区域内
              Container(
                width: totalWidth,
                color: AppBorderStyles.tableHeaderBackground,
                height: 48,
                child: Row(
                  children: [
                    Container(
                      width: indexWidth,
                      decoration: AppBorderStyles.headerCellDecoration(),
                      child: const Center(
                        child: Text(
                          '序号',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ),
                    Container(
                      width: barcodeWidth,
                      decoration: AppBorderStyles.headerCellDecoration(),
                      child: const Center(
                        child: Text(
                          '条码',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ),
                    Container(
                      width: nameWidth,
                      decoration: AppBorderStyles.headerCellDecoration(),
                      child: const Center(
                        child: Text(
                          '商品名称',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ),
                    Container(
                      width: categoryWidth,
                      decoration: AppBorderStyles.headerCellDecoration(),
                      child: const Center(
                        child: Text(
                          '分类',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ),
                    Container(
                      width: ringSizeWidth,
                      decoration: AppBorderStyles.headerCellDecoration(),
                      child: const Center(
                        child: Text(
                          '圈口号',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ),
                    Container(
                      width: goldWeightWidth,
                      decoration: AppBorderStyles.headerCellDecoration(),
                      child: const Center(
                        child: Text(
                          '金重(g)',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ),
                    Container(
                      width: goldPriceWidth,
                      decoration: AppBorderStyles.headerCellDecoration(),
                      child: const Center(
                        child: Text(
                          '金价(元/g)',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ),
                    Container(
                      width: silverWeightWidth,
                      decoration: AppBorderStyles.headerCellDecoration(),
                      child: const Center(
                        child: Text(
                          '银重(g)',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ),
                    Container(
                      width: silverPriceWidth,
                      decoration: AppBorderStyles.headerCellDecoration(),
                      child: const Center(
                        child: Text(
                          '银价(元/g)',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ),
                    Container(
                      width: totalWeightWidth,
                      decoration: AppBorderStyles.headerCellDecoration(),
                      child: const Center(
                        child: Text(
                          '总重(g)',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ),
                    Container(
                      width: workFeeWidth,
                      decoration: AppBorderStyles.headerCellDecoration(),
                      child: const Center(
                        child: Text(
                          '批发工费(元)',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ),
                    Container(
                      width: pieceWorkWidth,
                      decoration: AppBorderStyles.headerCellDecoration(),
                      child: const Center(
                        child: Text(
                          '件工费(元)',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ),
                    Container(
                      width: priceWidth,
                      decoration: AppBorderStyles.headerCellDecoration(),
                      child: const Center(
                        child: Text(
                          '价格(元)',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ),
                    Container(
                      width: actionWidth,
                      decoration: AppBorderStyles.headerCellDecoration(),
                      child: const Center(
                        child: Text(
                          '操作',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // 数据行 - 使用ListView.builder
              Expanded(
                child: ListView.builder(
                  itemCount: controller.transferItems.length,
                  itemBuilder: (context, index) {
                    final item = controller.transferItems[index];
                    final jewelry = item.jewelry;

                    // 🔍 判断商品类型
                    final isRecyclingItem = controller.isRecyclingItem(item);
                    final isWorkFeeItem = controller.isWorkFeeItem(item);
                    const isReturnExchangeItem = false; // 库存调拨暂不支持退换货

                    // 🎨 确定行的装饰样式 - 与出库单保持一致
                    BoxDecoration rowDecoration;
                    if (isRecyclingItem) {
                      rowDecoration = _recyclingCellDecoration();
                    } else if (isReturnExchangeItem) {
                      rowDecoration = _returnExchangeCellDecoration();
                    } else if (isWorkFeeItem) {
                      rowDecoration = _workFeeCellDecoration();
                    } else {
                      rowDecoration = _whiteCellDecoration();
                    }

                    return Container(
                      width: totalWidth,
                      height: 52,
                      decoration: rowDecoration,
                      child: Row(
                        children: [
                          Container(
                            width: indexWidth,
                            height: 52,
                            decoration: rowDecoration,
                            padding: const EdgeInsets.symmetric(horizontal: 4),
                            alignment: Alignment.center,
                            child: Text(
                              '${index + 1}',
                              style: TextStyle(
                                fontWeight: FontWeight.w500,
                                color: isRecyclingItem
                                    ? Colors.purple[700]
                                    : isReturnExchangeItem
                                    ? Colors.red[700]
                                    : Colors.black87,
                              ),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                          Container(
                            width: barcodeWidth,
                            height: 52,
                            decoration: rowDecoration,
                            padding: const EdgeInsets.symmetric(horizontal: 4),
                            alignment: Alignment.center,
                            child: Text(
                              item.barcode,
                              style: TextStyle(
                                fontSize: fontSize,
                                color: isRecyclingItem
                                    ? Colors.purple[700]
                                    : isReturnExchangeItem
                                    ? Colors.red[700]
                                    : isWorkFeeItem
                                    ? Colors.blue[700]
                                    : Colors.black87,
                              ),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                          Container(
                            width: nameWidth,
                            height: 52,
                            decoration: rowDecoration,
                            padding: const EdgeInsets.symmetric(horizontal: 4),
                            alignment: Alignment.center,
                            child: Text(
                              jewelry?.name ?? '未知商品',
                              style: TextStyle(
                                fontSize: fontSize,
                                color: isRecyclingItem
                                    ? Colors.purple[700]
                                    : isReturnExchangeItem
                                    ? Colors.red[700]
                                    : isWorkFeeItem
                                    ? Colors.blue[700]
                                    : Colors.black87,
                                fontWeight:
                                    (isRecyclingItem ||
                                        isReturnExchangeItem ||
                                        isWorkFeeItem)
                                    ? FontWeight.w600
                                    : FontWeight.normal,
                              ),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                          Container(
                            width: categoryWidth,
                            height: 52,
                            decoration: rowDecoration,
                            padding: const EdgeInsets.symmetric(horizontal: 4),
                            alignment: Alignment.center,
                            child: Text(
                              jewelry?.categoryName ?? '未知分类',
                              style: TextStyle(
                                fontSize: fontSize,
                                color: isRecyclingItem
                                    ? Colors.purple[700]
                                    : isReturnExchangeItem
                                    ? Colors.red[700]
                                    : isWorkFeeItem
                                    ? Colors.blue[700]
                                    : Colors.black87,
                              ),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                          Container(
                            width: ringSizeWidth,
                            height: 52,
                            decoration: rowDecoration,
                            padding: const EdgeInsets.symmetric(horizontal: 4),
                            alignment: Alignment.center,
                            child: Text(
                              jewelry?.ringSize ?? '',
                              style: TextStyle(
                                fontSize: fontSize,
                                color: isRecyclingItem
                                    ? Colors.purple[700]
                                    : isReturnExchangeItem
                                    ? Colors.red[700]
                                    : isWorkFeeItem
                                    ? Colors.blue[700]
                                    : Colors.black87,
                              ),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                          Container(
                            width: goldWeightWidth,
                            height: 52,
                            decoration: rowDecoration,
                            padding: const EdgeInsets.symmetric(horizontal: 4),
                            alignment: Alignment.center,
                            child: Text(
                              (jewelry?.goldWeight ?? 0.0).toStringAsFixed(2),
                              style: TextStyle(
                                fontSize: fontSize,
                                color: isRecyclingItem
                                    ? Colors.purple[700]
                                    : isReturnExchangeItem
                                    ? Colors.red[700]
                                    : isWorkFeeItem
                                    ? Colors.blue[700]
                                    : Colors.black87,
                              ),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                          Container(
                            width: goldPriceWidth,
                            height: 52,
                            decoration: rowDecoration,
                            padding: const EdgeInsets.symmetric(horizontal: 4),
                            alignment: Alignment.center,
                            child: _buildEditableCell(
                              width: goldPriceWidth,
                              value: _formatPriceForInput(
                                jewelry?.goldPrice ?? 0.0,
                              ),
                              controllerId: 'goldPrice_$index',
                              onChanged: (value) {
                                final validatedInput = _validatePriceInput(
                                  value,
                                );
                                if (validatedInput != null) {
                                  final price = double.tryParse(validatedInput);
                                  if (price != null) {
                                    controller.updateItemField(
                                      index,
                                      goldPrice: price,
                                    );
                                  }
                                }
                              },
                              isRecyclingItem: isRecyclingItem,
                              isReturnExchangeItem: isReturnExchangeItem,
                              isWorkFeeItem: isWorkFeeItem,
                            ),
                          ),
                          Container(
                            width: silverWeightWidth,
                            height: 52,
                            decoration: rowDecoration,
                            padding: const EdgeInsets.symmetric(horizontal: 4),
                            alignment: Alignment.center,
                            child: Text(
                              (jewelry?.silverWeight ?? 0.0).toStringAsFixed(2),
                              style: TextStyle(
                                fontSize: fontSize,
                                color: isRecyclingItem
                                    ? Colors.purple[700]
                                    : isReturnExchangeItem
                                    ? Colors.red[700]
                                    : isWorkFeeItem
                                    ? Colors.blue[700]
                                    : Colors.black87,
                              ),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                          Container(
                            width: silverPriceWidth,
                            height: 52,
                            decoration: rowDecoration,
                            padding: const EdgeInsets.symmetric(horizontal: 4),
                            alignment: Alignment.center,
                            child: _buildEditableCell(
                              width: silverPriceWidth,
                              value: _formatPriceForInput(
                                jewelry?.silverPrice ?? 0.0,
                              ),
                              controllerId: 'silverPrice_$index',
                              onChanged: (value) {
                                final validatedInput = _validatePriceInput(
                                  value,
                                );
                                if (validatedInput != null) {
                                  final price = double.tryParse(validatedInput);
                                  if (price != null) {
                                    controller.updateItemField(
                                      index,
                                      silverPrice: price,
                                    );
                                  }
                                }
                              },
                              isRecyclingItem: isRecyclingItem,
                              isReturnExchangeItem: isReturnExchangeItem,
                              isWorkFeeItem: isWorkFeeItem,
                            ),
                          ),
                          Container(
                            width: totalWeightWidth,
                            height: 52,
                            decoration: rowDecoration,
                            padding: const EdgeInsets.symmetric(horizontal: 4),
                            alignment: Alignment.center,
                            child: Text(
                              ((jewelry?.goldWeight ?? 0.0) +
                                      (jewelry?.silverWeight ?? 0.0))
                                  .toStringAsFixed(2),
                              style: TextStyle(
                                fontSize: fontSize,
                                color: isRecyclingItem
                                    ? Colors.purple[700]
                                    : isReturnExchangeItem
                                    ? Colors.red[700]
                                    : isWorkFeeItem
                                    ? Colors.blue[700]
                                    : Colors.black87,
                              ),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                          Container(
                            width: workFeeWidth,
                            height: 52,
                            decoration: rowDecoration,
                            padding: const EdgeInsets.symmetric(horizontal: 4),
                            alignment: Alignment.center,
                            child: _buildEditableCell(
                              width: workFeeWidth,
                              value: (jewelry?.wholesaleWorkPrice ?? 0.0)
                                  .round()
                                  .toString(),
                              controllerId: 'workPrice_$index',
                              onChanged: (value) {
                                final price = double.tryParse(value);
                                if (price != null) {
                                  controller.updateItemField(
                                    index,
                                    workPrice: price,
                                  );
                                }
                              },
                              isRecyclingItem: isRecyclingItem,
                              isReturnExchangeItem: isReturnExchangeItem,
                              isWorkFeeItem: isWorkFeeItem,
                            ),
                          ),
                          Container(
                            width: pieceWorkWidth,
                            height: 52,
                            decoration: rowDecoration,
                            padding: const EdgeInsets.symmetric(horizontal: 4),
                            alignment: Alignment.center,
                            child: _buildEditableCell(
                              width: pieceWorkWidth,
                              value: (jewelry?.pieceWorkPrice ?? 0.0)
                                  .round()
                                  .toString(),
                              controllerId: 'pieceWorkPrice_$index',
                              onChanged: (value) {
                                final price = double.tryParse(value);
                                if (price != null) {
                                  controller.updateItemField(
                                    index,
                                    pieceWorkPrice: price,
                                  );
                                }
                              },
                              isRecyclingItem: isRecyclingItem,
                              isReturnExchangeItem: isReturnExchangeItem,
                              isWorkFeeItem: isWorkFeeItem,
                            ),
                          ),
                          Container(
                            width: priceWidth,
                            height: 52,
                            decoration: rowDecoration,
                            padding: const EdgeInsets.symmetric(horizontal: 4),
                            alignment: Alignment.center,
                            child: _buildEditableCell(
                              width: priceWidth,
                              value: item.transferPrice.round().toString(),
                              controllerId: 'amount_$index',
                              onChanged: (value) {
                                final amount = double.tryParse(value);
                                if (amount != null) {
                                  controller.updateItemField(
                                    index,
                                    amount: amount,
                                  );
                                }
                              },
                              isRecyclingItem: isRecyclingItem,
                              isReturnExchangeItem: isReturnExchangeItem,
                              isWorkFeeItem: isWorkFeeItem,
                            ),
                          ),
                          Container(
                            width: actionWidth,
                            height: 52,
                            decoration: rowDecoration,
                            padding: const EdgeInsets.symmetric(horizontal: 4),
                            alignment: Alignment.center,
                            child: Center(
                              child: ElevatedButton.icon(
                                icon: const Icon(
                                  Icons.delete_outline,
                                  size: 16,
                                  color: Colors.white,
                                ),
                                label: const Text('删除'),
                                onPressed: () => _confirmRemoveItem(index),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.red[600],
                                  foregroundColor: Colors.white,
                                  minimumSize: const Size(70, 30),
                                  maximumSize: const Size(70, 30),
                                  padding: EdgeInsets.zero,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 确认删除商品
  void _confirmRemoveItem(int index) {
    Get.dialog(
      AlertDialog(
        title: const Text('确认删除'),
        content: const Text('确定要从调拨单中移除这个商品吗？'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            style: TextButton.styleFrom(foregroundColor: Colors.grey[600]),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              controller.removeTransferItem(index);
              Get.back();
              Get.snackbar(
                '成功',
                '商品已从调拨单中移除',
                snackPosition: SnackPosition.BOTTOM,
                backgroundColor: Colors.green,
                colorText: Colors.white,
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red[600],
              foregroundColor: Colors.white,
            ),
            child: const Text('确定删除'),
          ),
        ],
      ),
    );
  }

  /// 构建可编辑的输入框单元格 - 完全复制出库单的实现
  Widget _buildEditableCell({
    required double width,
    required String value,
    required Function(String) onChanged,
    TextAlign textAlign = TextAlign.center,
    TextInputType keyboardType = TextInputType.number,
    String? controllerId,
    bool isRecyclingItem = false, // 是否为回收商品
    bool isReturnExchangeItem = false, // 是否为退换货商品
    bool isWorkFeeItem = false, // 是否为工费项目
  }) {
    // 🔑 关键修复：使用持久化的TextEditingController，避免重复创建导致输入错误
    final uniqueId =
        controllerId ??
        'cell_${value.hashCode}_${DateTime.now().millisecondsSinceEpoch}';
    final textController = _getOrCreateController(uniqueId, value);

    return Container(
      width: width,
      height: 52,
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      alignment: Alignment.center,
      child: TextFormField(
        controller: textController, // 使用持久化的controller
        textAlign: textAlign,
        keyboardType: keyboardType,
        style: TextStyle(
          fontSize: 13, // 表单标签字体大小
          color: isRecyclingItem
              ? Colors.purple[700]
              : isReturnExchangeItem
              ? Colors.red[700]
              : isWorkFeeItem
              ? Colors.blue[700]
              : Colors.black87,
          fontFamily: keyboardType == TextInputType.number
              ? 'monospace'
              : null, // 数字字段使用等宽字体
          fontWeight: (isRecyclingItem || isReturnExchangeItem || isWorkFeeItem)
              ? FontWeight.w600
              : FontWeight.normal,
        ),
        decoration: InputDecoration(
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 10,
            vertical: 8,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6), // 圆角半径
            borderSide: BorderSide(
              color: isRecyclingItem
                  ? Colors.purple[300]!
                  : isReturnExchangeItem
                  ? Colors.red[300]!
                  : isWorkFeeItem
                  ? Colors.blue[300]!
                  : Colors.grey[300]!,
              width: 1,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: BorderSide(
              color: isRecyclingItem
                  ? Colors.purple[300]!
                  : isReturnExchangeItem
                  ? Colors.red[300]!
                  : isWorkFeeItem
                  ? Colors.blue[300]!
                  : Colors.grey[300]!,
              width: 1,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: BorderSide(
              color: isRecyclingItem
                  ? Colors.purple[600]!
                  : isReturnExchangeItem
                  ? Colors.red[600]!
                  : isWorkFeeItem
                  ? Colors.blue[600]!
                  : const Color(0xFF1E88E5),
              width: 2,
            ),
          ),
          isDense: true,
        ),
        onChanged: onChanged,
        onFieldSubmitted: onChanged,
      ),
    );
  }

  /// 构建表单底部汇总区域 - 100%复制新建出库单设计
  Widget _buildFormFooter() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
      decoration: BoxDecoration(
        color: Colors.white,
        border: const Border(top: AppBorderStyles.tableBorder),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            blurRadius: 3,
            offset: const Offset(0, -1),
          ),
        ],
      ),
      child: Row(
        children: [
          // 💰 汇总信息
          Icon(Icons.calculate, color: Colors.blue[600], size: 20),
          const SizedBox(width: 8),
          const Text(
            '汇总信息',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),

          const SizedBox(width: 24),

          // 总件数
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
              border: Border.all(color: Colors.blue[200]!, width: 1),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.inventory_2, size: 14, color: Colors.blue[600]),
                const SizedBox(width: 4),
                const Text(
                  '总件数: ',
                  style: TextStyle(fontSize: 13, color: Colors.black87),
                ),
                Obx(
                  () => Text(
                    '${controller.totalCount.value}件',
                    style: TextStyle(
                      fontSize: 13,
                      fontWeight: FontWeight.w600,
                      color: Colors.blue[700],
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(width: 16),

          // 总金额
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.green[50],
              borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
              border: Border.all(color: Colors.green[200]!, width: 1),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.attach_money, size: 14, color: Colors.green[600]),
                const SizedBox(width: 4),
                const Text(
                  '调拨总额: ',
                  style: TextStyle(fontSize: 13, color: Colors.black87),
                ),
                Obx(
                  () => Text(
                    '¥${controller.totalAmount.value.round()}',
                    style: TextStyle(
                      fontSize: 13,
                      fontWeight: FontWeight.w600,
                      color: Colors.green[700],
                    ),
                  ),
                ),
              ],
            ),
          ),

          const Spacer(),

          // 操作按钮
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 保存草稿按钮
              SizedBox(
                height: 32,
                child: OutlinedButton(
                  onPressed: () => _saveDraft(),
                  style: OutlinedButton.styleFrom(
                    side: BorderSide(color: Colors.grey[400]!, width: 1),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 0,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(
                        AppBorderStyles.borderRadius,
                      ),
                    ),
                    textStyle: const TextStyle(fontSize: 13),
                  ),
                  child: const Text('保存草稿'),
                ),
              ),

              const SizedBox(width: 16),

              // 提交审核按钮
              SizedBox(
                height: 32,
                child: ElevatedButton(
                  onPressed: () => _submitForReview(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue[600],
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 0,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(
                        AppBorderStyles.borderRadius,
                      ),
                    ),
                    textStyle: const TextStyle(fontSize: 13),
                  ),
                  child: const Text('提交审核'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 保存草稿
  void _saveDraft() {
    controller.saveDraft();
  }

  /// 创建白色背景的单元格装饰 - 与出库单保持一致
  BoxDecoration _whiteCellDecoration() {
    return const BoxDecoration(
      color: Colors.white, // 始终使用白色背景
      border: Border(
        right: AppBorderStyles.tableBorder,
        bottom: AppBorderStyles.tableBorder,
      ),
    );
  }

  /// 创建回收商品的特殊单元格装饰
  /// 使用浅紫色背景来标识回收商品
  BoxDecoration _recyclingCellDecoration() {
    return BoxDecoration(
      color: Colors.purple[50], // 浅紫色背景，与回收按钮颜色呼应
      border: const Border(
        right: AppBorderStyles.tableBorder,
        bottom: AppBorderStyles.tableBorder,
      ),
    );
  }

  /// 创建退换货商品的特殊单元格装饰
  /// 使用浅红色背景来标识退换货商品
  BoxDecoration _returnExchangeCellDecoration() {
    return BoxDecoration(
      color: Colors.red[50], // 浅红色背景，与退换货按钮颜色呼应
      border: const Border(
        right: AppBorderStyles.tableBorder,
        bottom: AppBorderStyles.tableBorder,
      ),
    );
  }

  /// 创建工费项目的特殊单元格装饰
  BoxDecoration _workFeeCellDecoration() {
    return BoxDecoration(
      color: Colors.blue[50], // 浅蓝色背景，与工费按钮颜色呼应
      border: const Border(
        right: AppBorderStyles.tableBorder,
        bottom: AppBorderStyles.tableBorder,
      ),
    );
  }

  /// 提交审核
  void _submitForReview() {
    if (controller.transferItems.isEmpty) {
      Get.snackbar('提示', '请先添加调拨商品');
      return;
    }

    if (controller.sourceStoreId.value == 0) {
      Get.snackbar('提示', '请选择源门店');
      return;
    }

    if (controller.targetStoreId.value == 0) {
      Get.snackbar('提示', '请选择目标门店');
      return;
    }

    if (controller.sourceStoreId.value == controller.targetStoreId.value) {
      Get.snackbar('提示', '源门店和目标门店不能相同');
      return;
    }

    // 调用控制器的提交方法
    controller.submitForAudit();
  }
}
