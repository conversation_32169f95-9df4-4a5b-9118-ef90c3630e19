"""
门店调拨业务服务类

老板，这个模块提供门店调拨的核心业务逻辑，包括：
1. 调拨单的CRUD操作
2. 智能单号生成
3. 审核流程管理
4. 统计分析功能
5. 业务规则验证

完整的门店调拨业务流程管理服务。
"""

import time
import json
from datetime import datetime
from typing import List, Optional, Dict, Any
from decimal import Decimal
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc
from loguru import logger

from app.models.store_transfer import StoreTransfer, StoreTransferItem
from app.models.store import Store
from app.models.admin import Admin
from app.models.jewelry import Jewelry, JewelryCategory
from app.schemas.store_transfer import (
    StoreTransferCreate, StoreTransferUpdate, StoreTransferAuditUpdate,
    StoreTransferResponse, StoreTransferStatistics, TransferStatus,
    StoreTransferItemResponse
)
# from app.schemas.common import PaginatedResponse  # 不再需要


class StoreTransferService:
    """门店调拨管理服务类"""

    def __init__(self, db: Session):
        self.db = db

    def generate_transfer_no(self) -> str:
        """生成调拨单号 - TRANSYYYYMMDD0001格式"""
        try:
            now = datetime.now()
            prefix = f"TRANS{now.year}{now.month:02d}{now.day:02d}"

            # 查询当天已有的调拨单数量
            count = self.db.query(StoreTransfer).filter(
                StoreTransfer.transfer_no.like(f"{prefix}%")
            ).count()

            transfer_no = f"{prefix}{count + 1:04d}"
            logger.info(f"生成调拨单号: {transfer_no}")
            return transfer_no

        except Exception as e:
            logger.error(f"生成调拨单号失败: {str(e)}")
            # 备用方案：使用时间戳
            return f"TRANS{int(time.time())}"

    async def get_transfers(
        self,
        page: int = 1,
        page_size: int = 20,
        status: Optional[int] = None,
        from_store_id: Optional[int] = None,
        to_store_id: Optional[int] = None,
        admin_id: Optional[int] = None,
        keyword: Optional[str] = None,
        start_time: Optional[int] = None,
        end_time: Optional[int] = None
    ) -> tuple[List[StoreTransferResponse], int]:
        """获取调拨单列表"""
        try:
            # 构建查询
            query = self.db.query(StoreTransfer).options(
                joinedload(StoreTransfer.from_store),
                joinedload(StoreTransfer.to_store),
                joinedload(StoreTransfer.admin),
                joinedload(StoreTransfer.auditor),
                joinedload(StoreTransfer.items)
            )

            # 状态筛选
            if status is not None:
                query = query.filter(StoreTransfer.status == status)

            # 源店铺筛选
            if from_store_id:
                query = query.filter(StoreTransfer.from_store_id == from_store_id)

            # 目标店铺筛选
            if to_store_id:
                query = query.filter(StoreTransfer.to_store_id == to_store_id)

            # 操作员筛选
            if admin_id:
                query = query.filter(StoreTransfer.admin_id == admin_id)

            # 时间范围筛选
            if start_time:
                query = query.filter(StoreTransfer.createtime >= start_time)
            if end_time:
                query = query.filter(StoreTransfer.createtime <= end_time)

            # 关键词搜索
            if keyword:
                keyword_filter = or_(
                    StoreTransfer.transfer_no.like(f"%{keyword}%"),
                    StoreTransfer.remark.like(f"%{keyword}%")
                )
                query = query.filter(keyword_filter)

            # 总数统计
            total = query.count()

            # 分页查询
            transfers = query.order_by(desc(StoreTransfer.createtime)).offset(
                (page - 1) * page_size
            ).limit(page_size).all()

            # 转换为响应模型
            transfer_responses = []
            for transfer in transfers:
                # 计算统计信息
                item_count = len(transfer.items)
                total_amount = sum(item.transfer_price for item in transfer.items)

                transfer_response = StoreTransferResponse(
                    id=transfer.id,
                    transfer_no=transfer.transfer_no,
                    from_store_id=transfer.from_store_id,
                    to_store_id=transfer.to_store_id,
                    admin_id=transfer.admin_id,
                    status=transfer.status,
                    audit_id=transfer.audit_id,
                    audit_time=transfer.audit_time,
                    remark=transfer.remark,
                    createtime=transfer.createtime,
                    from_store_name=transfer.from_store.name if transfer.from_store else None,
                    to_store_name=transfer.to_store.name if transfer.to_store else None,
                    admin_name=transfer.admin.nickname if transfer.admin else None,
                    auditor_name=transfer.auditor.nickname if transfer.auditor else None,
                    item_count=item_count,
                    total_amount=total_amount,
                    items=[]  # 列表中不包含明细，详情接口才包含
                )
                transfer_responses.append(transfer_response)

            logger.info(f"获取调拨单列表成功: 共{total}条记录")

            return transfer_responses, total

        except Exception as e:
            logger.error(f"获取调拨单列表失败: {str(e)}")
            raise e

    async def get_transfer_by_id(self, transfer_id: int) -> Optional[StoreTransferResponse]:
        """根据ID获取调拨单详情"""
        try:
            transfer = self.db.query(StoreTransfer).options(
                joinedload(StoreTransfer.from_store),
                joinedload(StoreTransfer.to_store),
                joinedload(StoreTransfer.admin),
                joinedload(StoreTransfer.auditor),
                joinedload(StoreTransfer.items).joinedload(StoreTransferItem.jewelry)
            ).filter(StoreTransfer.id == transfer_id).first()

            if not transfer:
                return None

            # 构建明细响应
            item_responses = []
            for item in transfer.items:
                jewelry = item.jewelry
                category_name = None
                if jewelry and jewelry.category_id:
                    category = self.db.query(JewelryCategory).filter(
                        JewelryCategory.id == jewelry.category_id
                    ).first()
                    category_name = category.name if category else None

                item_response = StoreTransferItemResponse(
                    id=item.id,
                    transfer_id=item.transfer_id,
                    jewelry_id=item.jewelry_id,
                    transfer_price=item.transfer_price,
                    gold_price=item.gold_price,
                    silver_price=item.silver_price,
                    total_weight=item.total_weight,
                    silver_work_price=item.silver_work_price,
                    piece_work_price=item.piece_work_price,
                    original_data=item.original_data,
                    createtime=item.createtime,
                    jewelry_name=jewelry.name if jewelry else None,
                    jewelry_barcode=jewelry.barcode if jewelry else None,
                    category_name=category_name
                )
                item_responses.append(item_response)

            # 计算统计信息
            item_count = len(transfer.items)
            total_amount = sum(item.transfer_price for item in transfer.items)

            transfer_response = StoreTransferResponse(
                id=transfer.id,
                transfer_no=transfer.transfer_no,
                from_store_id=transfer.from_store_id,
                to_store_id=transfer.to_store_id,
                admin_id=transfer.admin_id,
                status=transfer.status,
                audit_id=transfer.audit_id,
                audit_time=transfer.audit_time,
                remark=transfer.remark,
                createtime=transfer.createtime,
                from_store_name=transfer.from_store.name if transfer.from_store else None,
                to_store_name=transfer.to_store.name if transfer.to_store else None,
                admin_name=transfer.admin.nickname if transfer.admin else None,
                auditor_name=transfer.auditor.nickname if transfer.auditor else None,
                item_count=item_count,
                total_amount=total_amount,
                items=item_responses
            )

            logger.info(f"获取调拨单详情成功: ID={transfer_id}")
            return transfer_response

        except Exception as e:
            logger.error(f"获取调拨单详情失败: transfer_id={transfer_id}, error={str(e)}")
            raise e

    async def get_transfer_by_no(self, transfer_no: str) -> Optional[StoreTransferResponse]:
        """根据单号获取调拨单详情"""
        try:
            transfer = self.db.query(StoreTransfer).filter(
                StoreTransfer.transfer_no == transfer_no
            ).first()

            if not transfer:
                return None

            return await self.get_transfer_by_id(transfer.id)

        except Exception as e:
            logger.error(f"根据单号获取调拨单失败: transfer_no={transfer_no}, error={str(e)}")
            raise e

    async def create_transfer(self, transfer_data: StoreTransferCreate, admin_id: int) -> StoreTransferResponse:
        """创建调拨单"""
        try:
            # 验证源店铺和目标店铺不能相同
            if transfer_data.from_store_id == transfer_data.to_store_id:
                raise ValueError("源店铺和目标店铺不能相同")

            # 验证店铺存在
            from_store = self.db.query(Store).filter(Store.id == transfer_data.from_store_id).first()
            if not from_store:
                raise ValueError(f"源店铺不存在: ID={transfer_data.from_store_id}")

            to_store = self.db.query(Store).filter(Store.id == transfer_data.to_store_id).first()
            if not to_store:
                raise ValueError(f"目标店铺不存在: ID={transfer_data.to_store_id}")

            # 验证操作员存在
            admin = self.db.query(Admin).filter(Admin.id == admin_id).first()
            if not admin:
                raise ValueError(f"操作员不存在: ID={admin_id}")

            # 验证商品存在且属于源店铺（仅对正常商品）
            for item_data in transfer_data.items:
                if item_data.jewelry_id > 0:  # 只验证正常商品
                    jewelry = self.db.query(Jewelry).filter(Jewelry.id == item_data.jewelry_id).first()
                    if not jewelry:
                        raise ValueError(f"商品不存在: ID={item_data.jewelry_id}")
                    if jewelry.store_id != transfer_data.from_store_id:
                        raise ValueError(f"商品不属于源店铺: 商品ID={item_data.jewelry_id}")

            # 生成调拨单号
            transfer_no = self.generate_transfer_no()

            # 创建调拨单
            current_time = int(time.time())
            transfer = StoreTransfer(
                transfer_no=transfer_no,
                from_store_id=transfer_data.from_store_id,
                to_store_id=transfer_data.to_store_id,
                admin_id=admin_id,
                status=TransferStatus.PENDING,
                remark=transfer_data.remark,
                createtime=current_time
            )

            self.db.add(transfer)
            self.db.flush()  # 获取调拨单ID

            # 创建调拨明细
            for item_data in transfer_data.items:
                # 根据jewelry_id判断业务类型
                if item_data.jewelry_id > 0:
                    # 正常商品调拨
                    jewelry = self.db.query(Jewelry).filter(Jewelry.id == item_data.jewelry_id).first()
                    original_data = {
                        "jewelry_id": jewelry.id,
                        "barcode": jewelry.barcode,
                        "name": jewelry.name,
                        "category_id": jewelry.category_id,
                        "gold_weight": float(jewelry.gold_weight),
                        "silver_weight": float(jewelry.silver_weight),
                        "total_weight": float(jewelry.total_weight),
                        "gold_cost": float(jewelry.gold_cost),
                        "silver_cost": float(jewelry.silver_cost),
                        "total_cost": float(jewelry.total_cost)
                    }
                elif item_data.jewelry_id == 0:
                    # 旧料回收项目
                    original_data = {
                        "jewelry_id": 0,
                        "barcode": f"旧料回收-{current_time}",
                        "name": "旧料回收",
                        "category_id": 0,
                        "type": "recycling",
                        "transfer_price": float(item_data.transfer_price)
                    }
                elif item_data.jewelry_id == -1:
                    # 单收批发工费项目
                    original_data = {
                        "jewelry_id": -1,
                        "barcode": f"工费-{current_time}",
                        "name": "单收批发工费",
                        "category_id": 0,
                        "type": "work_fee",
                        "transfer_price": float(item_data.transfer_price)
                    }
                else:
                    raise ValueError(f"不支持的jewelry_id: {item_data.jewelry_id}")

                transfer_item = StoreTransferItem(
                    transfer_id=transfer.id,
                    jewelry_id=item_data.jewelry_id,
                    transfer_price=item_data.transfer_price,
                    gold_price=item_data.gold_price,
                    silver_price=item_data.silver_price,
                    total_weight=item_data.total_weight,
                    silver_work_price=item_data.silver_work_price,
                    piece_work_price=item_data.piece_work_price,
                    original_data=json.dumps(original_data, ensure_ascii=False),
                    createtime=current_time
                )
                self.db.add(transfer_item)

            self.db.commit()
            self.db.refresh(transfer)

            logger.info(f"创建调拨单成功: {transfer_no}")

            # 返回详情
            return await self.get_transfer_by_id(transfer.id)

        except Exception as e:
            self.db.rollback()
            logger.error(f"创建调拨单失败: {str(e)}")
            raise e

    async def update_transfer(self, transfer_id: int, transfer_data: StoreTransferUpdate) -> StoreTransferResponse:
        """更新调拨单（仅待审核状态可更新）"""
        try:
            transfer = self.db.query(StoreTransfer).filter(StoreTransfer.id == transfer_id).first()
            if not transfer:
                raise ValueError(f"调拨单不存在: ID={transfer_id}")

            # 只有待审核状态才能更新
            if transfer.status != TransferStatus.PENDING:
                raise ValueError("只有待审核状态的调拨单才能更新")

            # 更新基本信息
            if transfer_data.remark is not None:
                transfer.remark = transfer_data.remark

            # 如果有明细更新
            if transfer_data.items is not None:
                # 删除原有明细
                self.db.query(StoreTransferItem).filter(
                    StoreTransferItem.transfer_id == transfer_id
                ).delete()

                # 创建新明细
                current_time = int(time.time())
                for item_data in transfer_data.items:
                    # 根据jewelry_id判断业务类型
                    if item_data.jewelry_id > 0:
                        # 正常商品调拨
                        jewelry = self.db.query(Jewelry).filter(Jewelry.id == item_data.jewelry_id).first()
                        if not jewelry:
                            raise ValueError(f"商品不存在: ID={item_data.jewelry_id}")
                        
                        original_data = {
                            "jewelry_id": jewelry.id,
                            "barcode": jewelry.barcode,
                            "name": jewelry.name,
                            "category_id": jewelry.category_id,
                            "gold_weight": float(jewelry.gold_weight),
                            "silver_weight": float(jewelry.silver_weight),
                            "total_weight": float(jewelry.total_weight)
                        }
                    elif item_data.jewelry_id == 0:
                        # 旧料回收项目
                        original_data = {
                            "jewelry_id": 0,
                            "barcode": f"旧料回收-{current_time}",
                            "name": "旧料回收",
                            "category_id": 0,
                            "type": "recycling",
                            "transfer_price": float(item_data.transfer_price)
                        }
                    elif item_data.jewelry_id == -1:
                        # 单收批发工费项目
                        original_data = {
                            "jewelry_id": -1,
                            "barcode": f"工费-{current_time}",
                            "name": "单收批发工费",
                            "category_id": 0,
                            "type": "work_fee",
                            "transfer_price": float(item_data.transfer_price)
                        }
                    else:
                        raise ValueError(f"不支持的jewelry_id: {item_data.jewelry_id}")

                    transfer_item = StoreTransferItem(
                        transfer_id=transfer.id,
                        jewelry_id=item_data.jewelry_id,
                        transfer_price=item_data.transfer_price or Decimal('0.00'),
                        gold_price=item_data.gold_price or Decimal('0.00'),
                        silver_price=item_data.silver_price or Decimal('0.00'),
                        total_weight=item_data.total_weight or Decimal('0.00'),
                        silver_work_price=item_data.silver_work_price or Decimal('0.00'),
                        piece_work_price=item_data.piece_work_price or Decimal('0.00'),
                        original_data=json.dumps(original_data, ensure_ascii=False),
                        createtime=current_time
                    )
                    self.db.add(transfer_item)

            self.db.commit()
            self.db.refresh(transfer)

            logger.info(f"更新调拨单成功: ID={transfer_id}")

            return await self.get_transfer_by_id(transfer_id)

        except Exception as e:
            self.db.rollback()
            logger.error(f"更新调拨单失败: transfer_id={transfer_id}, error={str(e)}")
            raise e

    async def delete_transfer(self, transfer_id: int) -> bool:
        """删除调拨单（仅待审核状态可删除）"""
        try:
            transfer = self.db.query(StoreTransfer).filter(StoreTransfer.id == transfer_id).first()
            if not transfer:
                raise ValueError(f"调拨单不存在: ID={transfer_id}")

            # 只有待审核状态才能删除
            if transfer.status != TransferStatus.PENDING:
                raise ValueError("只有待审核状态的调拨单才能删除")

            # 删除调拨单（级联删除明细）
            self.db.delete(transfer)
            self.db.commit()

            logger.info(f"删除调拨单成功: ID={transfer_id}")
            return True

        except Exception as e:
            self.db.rollback()
            logger.error(f"删除调拨单失败: transfer_id={transfer_id}, error={str(e)}")
            raise e

    async def audit_transfer(self, transfer_id: int, audit_data: StoreTransferAuditUpdate, auditor_id: int) -> StoreTransferResponse:
        """审核调拨单"""
        try:
            transfer = self.db.query(StoreTransfer).filter(StoreTransfer.id == transfer_id).first()
            if not transfer:
                raise ValueError(f"调拨单不存在: ID={transfer_id}")

            # 只有待审核状态才能审核
            if transfer.status != TransferStatus.PENDING:
                raise ValueError("只有待审核状态的调拨单才能审核")

            # 验证审核员存在
            auditor = self.db.query(Admin).filter(Admin.id == auditor_id).first()
            if not auditor:
                raise ValueError(f"审核员不存在: ID={auditor_id}")

            # 如果审核通过，需要更新商品所属门店
            if audit_data.status == TransferStatus.APPROVED:
                # 获取调拨明细
                items = self.db.query(StoreTransferItem).filter(
                    StoreTransferItem.transfer_id == transfer_id
                ).all()

                # 更新商品所属门店
                for item in items:
                    jewelry = self.db.query(Jewelry).filter(Jewelry.id == item.jewelry_id).first()
                    if jewelry:
                        jewelry.store_id = transfer.to_store_id
                        jewelry.updatetime = int(time.time())

            # 更新调拨单状态
            transfer.status = audit_data.status
            transfer.audit_id = auditor_id
            transfer.audit_time = int(time.time())
            if audit_data.remark:
                transfer.remark = audit_data.remark

            self.db.commit()
            self.db.refresh(transfer)

            status_text = "通过" if audit_data.status == TransferStatus.APPROVED else "拒绝"
            logger.info(f"审核调拨单成功: ID={transfer_id}, 状态={status_text}")

            return await self.get_transfer_by_id(transfer_id)

        except Exception as e:
            self.db.rollback()
            logger.error(f"审核调拨单失败: transfer_id={transfer_id}, error={str(e)}")
            raise e

    async def get_statistics(
        self,
        start_time: Optional[int] = None,
        end_time: Optional[int] = None,
        from_store_id: Optional[int] = None,
        to_store_id: Optional[int] = None
    ) -> StoreTransferStatistics:
        """获取调拨单统计信息"""
        try:
            # 构建基础查询
            query = self.db.query(StoreTransfer)

            # 时间范围筛选
            if start_time:
                query = query.filter(StoreTransfer.createtime >= start_time)
            if end_time:
                query = query.filter(StoreTransfer.createtime <= end_time)

            # 门店筛选
            if from_store_id:
                query = query.filter(StoreTransfer.from_store_id == from_store_id)
            if to_store_id:
                query = query.filter(StoreTransfer.to_store_id == to_store_id)

            # 基础统计
            total_count = query.count()
            pending_count = query.filter(StoreTransfer.status == TransferStatus.PENDING).count()
            approved_count = query.filter(StoreTransfer.status == TransferStatus.APPROVED).count()
            rejected_count = query.filter(StoreTransfer.status == TransferStatus.REJECTED).count()

            # 计算总金额和总商品数量
            transfers = query.all()
            total_amount = Decimal('0.00')
            total_items = 0

            for transfer in transfers:
                items = self.db.query(StoreTransferItem).filter(
                    StoreTransferItem.transfer_id == transfer.id
                ).all()
                total_items += len(items)
                total_amount += sum(item.transfer_price for item in items)

            # 状态分布
            status_distribution = {
                "待审核": pending_count,
                "已通过": approved_count,
                "已拒绝": rejected_count
            }

            # 源店铺分布
            from_store_stats = self.db.query(
                Store.name,
                func.count(StoreTransfer.id).label('count')
            ).join(
                StoreTransfer, Store.id == StoreTransfer.from_store_id
            ).filter(
                StoreTransfer.id.in_([t.id for t in transfers])
            ).group_by(Store.name).all()

            from_store_distribution = {store_name: count for store_name, count in from_store_stats}

            # 目标店铺分布
            to_store_stats = self.db.query(
                Store.name,
                func.count(StoreTransfer.id).label('count')
            ).join(
                StoreTransfer, Store.id == StoreTransfer.to_store_id
            ).filter(
                StoreTransfer.id.in_([t.id for t in transfers])
            ).group_by(Store.name).all()

            to_store_distribution = {store_name: count for store_name, count in to_store_stats}

            logger.info(f"获取调拨单统计成功: 总数={total_count}")

            return StoreTransferStatistics(
                total_count=total_count,
                pending_count=pending_count,
                approved_count=approved_count,
                rejected_count=rejected_count,
                total_amount=total_amount,
                total_items=total_items,
                status_distribution=status_distribution,
                from_store_distribution=from_store_distribution,
                to_store_distribution=to_store_distribution,
                daily_distribution={},  # 可以后续扩展
                monthly_distribution={}  # 可以后续扩展
            )

        except Exception as e:
            logger.error(f"获取调拨单统计失败: {str(e)}")
            raise e

    async def validate_jewelry_transfer(
        self,
        barcode: str,
        from_store_id: int,
        to_store_id: int
    ) -> Dict[str, Any]:
        """验证商品是否可以调拨"""
        try:
            # 查找商品
            jewelry = self.db.query(Jewelry).filter(Jewelry.barcode == barcode).first()
            if not jewelry:
                return {
                    "is_valid": False,
                    "message": f"商品条码 {barcode} 不存在",
                    "transfer_type": "normal",
                    "suggested_price": 0.0
                }

            # 检查商品是否在源店铺
            if jewelry.store_id != from_store_id:
                return {
                    "is_valid": False,
                    "message": f"商品不在源店铺中",
                    "transfer_type": "normal",
                    "suggested_price": 0.0
                }

            # 检查商品状态（只有上架在售的商品才能调拨）
            if jewelry.status != 1:
                status_text = {0: "下架", 1: "上架在售", 2: "待出库"}.get(jewelry.status, "未知")
                return {
                    "is_valid": False,
                    "message": f"商品状态为 {status_text}，不能调拨",
                    "transfer_type": "normal",
                    "suggested_price": 0.0
                }

            # 检查源店铺和目标店铺是否存在
            from_store = self.db.query(Store).filter(Store.id == from_store_id).first()
            to_store = self.db.query(Store).filter(Store.id == to_store_id).first()

            if not from_store:
                return {
                    "is_valid": False,
                    "message": "源店铺不存在",
                    "transfer_type": "normal",
                    "suggested_price": 0.0
                }

            if not to_store:
                return {
                    "is_valid": False,
                    "message": "目标店铺不存在",
                    "transfer_type": "normal",
                    "suggested_price": 0.0
                }

            # 检查是否是同一个店铺
            if from_store_id == to_store_id:
                return {
                    "is_valid": False,
                    "message": "源店铺和目标店铺不能相同",
                    "transfer_type": "normal",
                    "suggested_price": 0.0
                }

            # 计算建议调拨价格（使用批发工费）
            # 注意：调拨明细表中的silver_work_price字段存储的是批发工费值
            suggested_price = float(
                jewelry.gold_weight * jewelry.gold_price +
                jewelry.silver_weight * jewelry.silver_price +
                jewelry.total_weight * jewelry.wholesale_work_price +
                jewelry.piece_work_price
            )

            logger.info(f"商品验证通过: {barcode}")

            # 获取分类名称
            category_name = None
            if jewelry.category:
                category_name = jewelry.category.name
            else:
                # 如果关联查询失败，手动查询分类信息
                from ..models.jewelry import JewelryCategory
                category = self.db.query(JewelryCategory).filter(JewelryCategory.id == jewelry.category_id).first()
                category_name = category.name if category else None

            return {
                "is_valid": True,
                "message": "商品验证通过，可以调拨",
                "transfer_type": "normal",
                "suggested_price": suggested_price,
                "jewelry": {
                    "id": jewelry.id,
                    "barcode": jewelry.barcode,
                    "name": jewelry.name,
                    "category_id": jewelry.category_id,
                    "category_name": category_name,
                    "ring_size": jewelry.ring_size,
                    "gold_weight": float(jewelry.gold_weight),
                    "silver_weight": float(jewelry.silver_weight),
                    "total_weight": float(jewelry.total_weight),
                    "gold_price": float(jewelry.gold_price),
                    "silver_price": float(jewelry.silver_price),
                    "silver_work_price": float(jewelry.silver_work_price),
                    "wholesale_work_price": float(jewelry.wholesale_work_price),
                    "retail_work_price": float(jewelry.retail_work_price),
                    "piece_work_price": float(jewelry.piece_work_price),
                    "store_id": jewelry.store_id,
                    "status": jewelry.status
                }
            }

        except Exception as e:
            logger.error(f"验证商品调拨失败: barcode={barcode}, error={str(e)}")
            raise e

    async def update_payment(self, transfer_id: int, payment_data, auditor_id: int = None) -> StoreTransferResponse:
        """更新调拨单收款信息 - 根据收款方案.md实现简化流程"""
        try:
            transfer = self.db.query(StoreTransfer).filter(StoreTransfer.id == transfer_id).first()
            if not transfer:
                raise ValueError(f"调拨单不存在: ID={transfer_id}")

            # 根据收款方案.md：简化流程，待审核状态的调拨单也可以直接收款
            # 收款时同时完成审核通过操作
            if transfer.status not in [0, 1]:  # 只有待审核(0)或已通过(1)状态才能收款
                raise ValueError("只有待审核或已通过审核的调拨单才能收款")

            transfer.payment_status = payment_data.payment_status

            if payment_data.payment_status == 1:  # 已收款
                transfer.payment_time = int(time.time())

                # 更新收款信息
                if payment_data.payment_info:
                    transfer.payment_method = payment_data.payment_info.payment_method
                    transfer.payment_remark = payment_data.payment_info.payment_remark
                    transfer.cash_amount = payment_data.payment_info.cash_amount
                    transfer.wechat_amount = payment_data.payment_info.wechat_amount
                    transfer.alipay_amount = payment_data.payment_info.alipay_amount
                    transfer.card_amount = payment_data.payment_info.card_amount
                    transfer.discount_amount = payment_data.payment_info.discount_amount
                    transfer.actual_amount = payment_data.payment_info.actual_amount

                # 🔧 关键修复：收款成功后同时完成审核通过操作
                if transfer.status == 0:  # 如果是待审核状态，直接设为已通过
                    transfer.status = 1  # 设置为已通过
                    transfer.audit_time = int(time.time())
                    # 🔧 修复audit_id字段缺失问题：设置审核员ID
                    if auditor_id:
                        transfer.audit_id = auditor_id

                    # 🔧 执行商品调拨：更新商品所属门店
                    self._execute_jewelry_transfer(transfer)

                print(f"✅ [收款] 调拨单收款成功，状态已更新为已通过")
            else:  # 未收款
                transfer.payment_time = None

            transfer.updatetime = int(time.time())

            self.db.commit()
            self.db.refresh(transfer)

            # 转换为响应模型
            return self._convert_to_response(transfer)

        except Exception as e:
            self.db.rollback()
            logger.error(f"更新调拨单收款信息失败: transfer_id={transfer_id}, error={str(e)}")
            raise e

    def _execute_jewelry_transfer(self, transfer: StoreTransfer):
        """执行商品调拨：更新商品所属门店（仅处理真实商品）"""
        try:
            # 获取调拨明细（只处理真实商品，jewelry_id > 0）
            items = self.db.query(StoreTransferItem).filter(
                StoreTransferItem.transfer_id == transfer.id,
                StoreTransferItem.jewelry_id > 0  # 只处理真实商品
            ).all()

            updated_count = 0
            for item in items:
                # 更新商品所属门店
                jewelry = self.db.query(Jewelry).filter(Jewelry.id == item.jewelry_id).first()
                if jewelry:
                    jewelry.store_id = transfer.to_store_id  # 更新为目标门店
                    jewelry.updatetime = int(time.time())
                    updated_count += 1
                    logger.info(f"商品调拨：{jewelry.barcode} 从门店{transfer.from_store_id}调拨到门店{transfer.to_store_id}")

            print(f"✅ [调拨] 已更新 {updated_count} 个商品的所属门店")

        except Exception as e:
            logger.error(f"执行商品调拨失败: {str(e)}")
            raise e

    def _convert_to_response(self, transfer: StoreTransfer) -> StoreTransferResponse:
        """将数据库模型转换为响应模型"""
        try:
            # 确保关联数据已加载
            if not transfer.from_store:
                transfer = self.db.query(StoreTransfer).options(
                    joinedload(StoreTransfer.from_store),
                    joinedload(StoreTransfer.to_store),
                    joinedload(StoreTransfer.admin),
                    joinedload(StoreTransfer.auditor),
                    joinedload(StoreTransfer.items)
                ).filter(StoreTransfer.id == transfer.id).first()

            # 计算统计信息
            item_count = len(transfer.items) if transfer.items else 0
            total_amount = sum(item.transfer_price for item in transfer.items) if transfer.items else 0

            # 构建调拨明细响应
            items_response = []
            if transfer.items:
                for item in transfer.items:
                    # 根据jewelry_id类型获取相应信息
                    if item.jewelry_id > 0:
                        # 正常商品，获取商品信息
                        jewelry = self.db.query(Jewelry).filter(Jewelry.id == item.jewelry_id).first()
                        if jewelry:
                            barcode = jewelry.barcode
                            jewelry_name = jewelry.name
                            category_name = jewelry.category.name if jewelry.category else ''
                        else:
                            barcode = f"商品ID_{item.jewelry_id}"
                            jewelry_name = "商品不存在"
                            category_name = ""
                    elif item.jewelry_id == 0:
                        # 旧料回收项目
                        barcode = f"旧料回收_{item.id}"
                        jewelry_name = "旧料回收"
                        category_name = "旧料回收"
                    elif item.jewelry_id == -1:
                        # 单收批发工费项目
                        barcode = f"工费_{item.id}"
                        jewelry_name = "单收批发工费"
                        category_name = "工费项目"
                    else:
                        # 其他情况
                        barcode = f"未知类型_{item.jewelry_id}_{item.id}"
                        jewelry_name = "未知类型"
                        category_name = ""
                    
                    item_response = StoreTransferItemResponse(
                        id=item.id,
                        transfer_id=item.transfer_id,
                        jewelry_id=item.jewelry_id,
                        barcode=barcode,
                        jewelry_name=jewelry_name,
                        category_name=category_name,
                        transfer_price=item.transfer_price,
                        gold_price=item.gold_price,
                        silver_price=item.silver_price,
                        total_weight=item.total_weight,
                        silver_work_price=item.silver_work_price,
                        piece_work_price=item.piece_work_price,
                        createtime=item.createtime
                    )
                    items_response.append(item_response)

            # 构建主响应模型
            transfer_response = StoreTransferResponse(
                id=transfer.id,
                transfer_no=transfer.transfer_no,
                from_store_id=transfer.from_store_id,
                to_store_id=transfer.to_store_id,
                admin_id=transfer.admin_id,
                status=transfer.status,
                audit_id=transfer.audit_id,
                audit_time=transfer.audit_time,
                remark=transfer.remark,
                createtime=transfer.createtime,
                updatetime=transfer.updatetime,
                from_store_name=transfer.from_store.name if transfer.from_store else None,
                to_store_name=transfer.to_store.name if transfer.to_store else None,
                admin_name=transfer.admin.nickname if transfer.admin else None,
                auditor_name=transfer.auditor.nickname if transfer.auditor else None,
                item_count=item_count,
                total_amount=total_amount,
                items=items_response
            )

            return transfer_response

        except Exception as e:
            logger.error(f"转换调拨单响应模型失败: transfer_id={transfer.id}, error={str(e)}")
            raise e
