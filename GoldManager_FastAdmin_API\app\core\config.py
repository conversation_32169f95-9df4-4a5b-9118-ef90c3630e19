"""
应用核心配置
"""

import os
from typing import List, Optional
from pydantic_settings import BaseSettings
from pydantic import validator


class Settings(BaseSettings):
    """应用配置类"""

    # 项目基本信息
    PROJECT_NAME: str = "黄金珠宝管理系统 API"
    PROJECT_VERSION: str = "1.0.0"
    PROJECT_DESCRIPTION: str = "基于FastAdmin数据库的现代化API接口"

    # 服务器配置
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    DEBUG: bool = True

    # API配置
    API_V1_STR: str = "/api/v1"

    # 数据库配置
    DATABASE_HOST: str = "localhost"
    DATABASE_PORT: int = 3306
    DATABASE_USER: str = "root"
    DATABASE_PASSWORD: str = ""
    DATABASE_NAME: str = "shgsgueobavnspo"  # FastAdmin数据库名
    DATABASE_CHARSET: str = "utf8mb4"

    # 数据库连接URL
    @validator("DATABASE_URL", pre=True, always=True)
    def assemble_db_connection(cls, v: Optional[str], values: dict) -> str:
        if isinstance(v, str):
            return v
        return f"mysql+pymysql://{values.get('DATABASE_USER')}:{values.get('DATABASE_PASSWORD')}@{values.get('DATABASE_HOST')}:{values.get('DATABASE_PORT')}/{values.get('DATABASE_NAME')}?charset={values.get('DATABASE_CHARSET')}"

    DATABASE_URL: Optional[str] = None

    # 跨域配置
    ALLOWED_HOSTS: List[str] = ["*"]

    # JWT配置
    SECRET_KEY: str = "your-secret-key-here-change-in-production-gold-jewelry-manager-2024"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 8  # 8小时
    REFRESH_TOKEN_EXPIRE_DAYS: int = 30  # 30天

    # 认证配置
    LOGIN_MAX_ATTEMPTS: int = 5  # 最大登录尝试次数
    LOGIN_LOCK_TIME: int = 30  # 锁定时间(分钟)
    PASSWORD_MIN_LENGTH: int = 6  # 最小密码长度

    # Redis配置
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_PASSWORD: str = ""
    REDIS_DB: int = 0

    # 文件上传配置
    UPLOAD_DIR: str = "uploads"
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    ALLOWED_EXTENSIONS: List[str] = ["jpg", "jpeg", "png", "gif", "pdf", "xlsx", "xls"]

    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_DIR: str = "logs"

    # 分页配置
    DEFAULT_PAGE_SIZE: int = 20
    MAX_PAGE_SIZE: int = 100

    # FastAdmin兼容性配置
    FASTADMIN_SALT_PREFIX: str = ""  # FastAdmin密码盐前缀
    FASTADMIN_TIME_FORMAT: str = "timestamp"  # timestamp 或 datetime

    class Config:
        env_file = ".env"
        case_sensitive = True


# 创建全局配置实例
settings = Settings()