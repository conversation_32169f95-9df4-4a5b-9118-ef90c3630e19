import '../common/date_utils.dart';

/// 维修记录模型
class RepairRecord {
  final int id;
  final int processId; // 处理记录ID
  final int recyclingItemId; // 回收物品ID
  final String repairMethod; // 维修方式
  final double materialCost; // 材料成本
  final double laborCost; // 人工成本
  final double otherCost; // 其他成本
  final double totalCost; // 总成本
  final double? newGoldWeight; // 维修后金重
  final double? newSilverWeight; // 维修后银重
  final double? newTotalWeight; // 维修后总重
  final String? remark; // 备注
  final DateTime? createTime;
  
  const RepairRecord({
    required this.id,
    required this.processId,
    required this.recyclingItemId,
    required this.repairMethod,
    this.materialCost = 0.0,
    this.laborCost = 0.0,
    this.otherCost = 0.0,
    this.totalCost = 0.0,
    this.newGoldWeight,
    this.newSilverWeight,
    this.newTotalWeight,
    this.remark,
    this.createTime,
  });
  
  /// 从JSON构造
  factory RepairRecord.fromJson(Map<String, dynamic> json) {
    return RepairRecord(
      id: json['id'],
      processId: json['process_id'],
      recyclingItemId: json['recycling_item_id'],
      repairMethod: json['repair_method'],
      materialCost: (json['material_cost'] ?? 0.0).toDouble(),
      laborCost: (json['labor_cost'] ?? 0.0).toDouble(),
      otherCost: (json['other_cost'] ?? 0.0).toDouble(),
      totalCost: (json['total_cost'] ?? 0.0).toDouble(),
      newGoldWeight: json['new_gold_weight'] != null 
          ? (json['new_gold_weight']).toDouble()
          : null,
      newSilverWeight: json['new_silver_weight'] != null 
          ? (json['new_silver_weight']).toDouble() 
          : null,
      newTotalWeight: json['new_total_weight'] != null 
          ? (json['new_total_weight']).toDouble() 
          : null,
      remark: json['remark'],
      createTime: DateUtil.fromUnixTimestamp(json['createtime']),
    );
  }
  
  /// 转为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'process_id': processId,
      'recycling_item_id': recyclingItemId,
      'repair_method': repairMethod,
      'material_cost': materialCost,
      'labor_cost': laborCost,
      'other_cost': otherCost,
      'total_cost': totalCost,
      'new_gold_weight': newGoldWeight,
      'new_silver_weight': newSilverWeight,
      'new_total_weight': newTotalWeight,
      'remark': remark,
      'createtime': DateUtil.toUnixTimestamp(createTime),
    };
  }
  
  /// 计算新首饰成本价值
  double calculateNewJewelryCost(double goldPrice, double silverPrice) {
    double goldValue = (newGoldWeight ?? 0.0) * goldPrice;
    double silverValue = (newSilverWeight ?? 0.0) * silverPrice;
    return goldValue + silverValue + totalCost;
  }
  
  /// 创建一个新实例，但使用部分属性
  RepairRecord copyWith({
    int? id,
    int? processId,
    int? recyclingItemId,
    String? repairMethod,
    double? materialCost,
    double? laborCost,
    double? otherCost,
    double? totalCost,
    double? newGoldWeight,
    double? newSilverWeight,
    double? newTotalWeight,
    String? remark,
    DateTime? createTime,
  }) {
    return RepairRecord(
      id: id ?? this.id,
      processId: processId ?? this.processId,
      recyclingItemId: recyclingItemId ?? this.recyclingItemId,
      repairMethod: repairMethod ?? this.repairMethod,
      materialCost: materialCost ?? this.materialCost,
      laborCost: laborCost ?? this.laborCost,
      otherCost: otherCost ?? this.otherCost,
      totalCost: totalCost ?? this.totalCost,
      newGoldWeight: newGoldWeight ?? this.newGoldWeight,
      newSilverWeight: newSilverWeight ?? this.newSilverWeight,
      newTotalWeight: newTotalWeight ?? this.newTotalWeight,
      remark: remark ?? this.remark,
      createTime: createTime ?? this.createTime,
    );
  }
} 