import '../common/date_utils.dart';
import '../store/store.dart';

/// 用户模型
class User {
  final int id;
  final String username;
  final String nickname;
  final String? avatar;
  final String? email;
  final String? mobile;
  final int? storeId;
  final List<String>? groups;
  final DateTime? loginTime;
  final DateTime? createTime;
  final int status; // 0=禁用, 1=正常
  
  // 关联对象
  final Store? store;
  
  const User({
    required this.id,
    required this.username,
    required this.nickname,
    this.avatar,
    this.email,
    this.mobile,
    this.storeId,
    this.groups,
    this.loginTime,
    this.createTime,
    this.status = 1,
    this.store,
  });
  
  /// 从JSON构造
  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      username: json['username'],
      nickname: json['nickname'] ?? '',
      avatar: json['avatar'],
      email: json['email'],
      mobile: json['mobile'],
      storeId: json['store_id'],
      groups: json['groups'] != null ? List<String>.from(json['groups']) : null,
      loginTime: DateUtil.fromUnixTimestamp(json['logintime']),
      createTime: DateUtil.fromUnixTimestamp(json['createtime']),
      status: json['status'] ?? 1,
      store: json['store'] != null ? Store.fromJson(json['store']) : null,
    );
  }
  
  /// 转为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'nickname': nickname,
      'avatar': avatar,
      'email': email,
      'mobile': mobile,
      'store_id': storeId,
      'groups': groups,
      'logintime': DateUtil.toUnixTimestamp(loginTime),
      'createtime': DateUtil.toUnixTimestamp(createTime),
      'status': status,
    };
  }
  
  /// 是否为管理员
  bool get isAdmin => groups?.contains('admin') ?? false;
  
  /// 是否禁用
  bool get isDisabled => status == 0;
  
  /// 创建一个新实例，但使用部分属性
  User copyWith({
    int? id,
    String? username,
    String? nickname,
    String? avatar,
    String? email,
    String? mobile,
    int? storeId,
    List<String>? groups,
    DateTime? loginTime,
    DateTime? createTime,
    int? status,
    Store? store,
  }) {
    return User(
      id: id ?? this.id,
      username: username ?? this.username,
      nickname: nickname ?? this.nickname,
      avatar: avatar ?? this.avatar,
      email: email ?? this.email,
      mobile: mobile ?? this.mobile,
      storeId: storeId ?? this.storeId,
      groups: groups ?? this.groups,
      loginTime: loginTime ?? this.loginTime,
      createTime: createTime ?? this.createTime,
      status: status ?? this.status,
      store: store ?? this.store,
    );
  }
} 