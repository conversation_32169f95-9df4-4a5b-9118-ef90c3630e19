import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:get/get.dart';
import '../../../core/theme/app_theme.dart';
import 'metric_card.dart';
import 'chart_card.dart';
import 'chart_theme.dart';
import '../../../widgets/responsive_builder.dart';

/// 销售业绩概览组件
/// 展示销售相关的核心指标和趋势图表
class SalesOverviewWidget extends StatelessWidget {
  /// 销售数据
  final SalesOverviewData data;
  
  /// 是否正在加载
  final bool isLoading;
  
  /// 刷新回调
  final VoidCallback? onRefresh;

  const SalesOverviewWidget({
    super.key,
    required this.data,
    this.isLoading = false,
    this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, sizingInformation) {
        if (sizingInformation.isMobile) {
          return _buildMobileLayout();
        } else if (sizingInformation.isTablet) {
          return _buildTabletLayout();
        } else {
          return _buildDesktopLayout();
        }
      },
    );
  }

  /// 桌面端布局
  Widget _buildDesktopLayout() {
    return Column(
      children: [
        // 核心指标卡片 - 4列布局
        Row(
          children: [
            Expanded(child: _buildTodaySalesCard()),
            const SizedBox(width: 16),
            Expanded(child: _buildMonthSalesCard()),
            const SizedBox(width: 16),
            Expanded(child: _buildOrderCountCard()),
            const SizedBox(width: 16),
            Expanded(child: _buildAvgOrderValueCard()),
          ],
        ),
        const SizedBox(height: 24),
        
        // 图表区域 - 2列布局
        Row(
          children: [
            Expanded(child: _buildSalesTrendChart()),
            const SizedBox(width: 16),
            Expanded(child: _buildTopProductsChart()),
          ],
        ),
      ],
    );
  }

  /// 平板端布局
  Widget _buildTabletLayout() {
    return Column(
      children: [
        // 核心指标卡片 - 2行2列布局
        Row(
          children: [
            Expanded(child: _buildTodaySalesCard()),
            const SizedBox(width: 16),
            Expanded(child: _buildMonthSalesCard()),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(child: _buildOrderCountCard()),
            const SizedBox(width: 16),
            Expanded(child: _buildAvgOrderValueCard()),
          ],
        ),
        const SizedBox(height: 24),
        
        // 图表区域 - 垂直布局
        _buildSalesTrendChart(),
        const SizedBox(height: 16),
        _buildTopProductsChart(),
      ],
    );
  }

  /// 移动端布局
  Widget _buildMobileLayout() {
    return Column(
      children: [
        // 核心指标卡片 - 垂直布局
        _buildTodaySalesCard(),
        const SizedBox(height: 12),
        _buildMonthSalesCard(),
        const SizedBox(height: 12),
        _buildOrderCountCard(),
        const SizedBox(height: 12),
        _buildAvgOrderValueCard(),
        const SizedBox(height: 20),
        
        // 图表区域 - 垂直布局
        _buildSalesTrendChart(),
        const SizedBox(height: 16),
        _buildTopProductsChart(),
      ],
    );
  }

  /// 今日销售额卡片
  Widget _buildTodaySalesCard() {
    return DashboardMetricCard(
      title: '今日销售额',
      value: data.todaySales.toStringAsFixed(0),
      unit: '元',
      icon: Icons.today,
      iconColor: AppTheme.successColor,
      valueColor: AppTheme.successColor,
      trendValue: data.todayTrend,
      trendDescription: '较昨日',
      isLoading: isLoading,
      onTap: () {
        // TODO: 跳转到今日销售详情
      },
    );
  }

  /// 本月销售额卡片
  Widget _buildMonthSalesCard() {
    return DashboardMetricCard(
      title: '本月销售额',
      value: data.monthSales.toStringAsFixed(0),
      unit: '元',
      icon: Icons.calendar_month,
      iconColor: AppTheme.primaryColor,
      valueColor: AppTheme.primaryColor,
      trendValue: data.monthTrend,
      trendDescription: '较上月',
      isLoading: isLoading,
      onTap: () {
        // TODO: 跳转到本月销售详情
      },
    );
  }

  /// 订单数量卡片
  Widget _buildOrderCountCard() {
    return DashboardMetricCard(
      title: '订单数量',
      value: data.orderCount.toString(),
      unit: '单',
      icon: Icons.receipt_long,
      iconColor: AppTheme.infoColor,
      valueColor: AppTheme.infoColor,
      trendValue: data.orderTrend,
      trendDescription: '较昨日',
      isLoading: isLoading,
      onTap: () {
        // TODO: 跳转到订单列表
      },
    );
  }

  /// 平均客单价卡片
  Widget _buildAvgOrderValueCard() {
    return DashboardMetricCard(
      title: '平均客单价',
      value: data.avgOrderValue.toStringAsFixed(0),
      unit: '元',
      icon: Icons.trending_up,
      iconColor: AppTheme.warningColor,
      valueColor: AppTheme.warningColor,
      trendValue: data.avgOrderTrend,
      trendDescription: '较昨日',
      isLoading: isLoading,
      onTap: () {
        // TODO: 跳转到客单价分析
      },
    );
  }

  /// 销售趋势图表
  Widget _buildSalesTrendChart() {
    return DashboardChartCard(
      title: '销售趋势',
      subtitle: '最近7天销售额变化',
      icon: Icons.show_chart,
      height: 300,
      isLoading: isLoading,
      showMoreButton: true,
      onMorePressed: () {
        // TODO: 跳转到详细趋势分析
      },
      chart: isLoading ? const SizedBox.shrink() : _buildLineChart(),
    );
  }

  /// 热销商品图表
  Widget _buildTopProductsChart() {
    return DashboardChartCard(
      title: '热销商品TOP5',
      subtitle: '按销售额排序',
      icon: Icons.star,
      height: 300,
      isLoading: isLoading,
      showMoreButton: true,
      onMorePressed: () {
        // TODO: 跳转到商品销售排行
      },
      chart: isLoading ? const SizedBox.shrink() : _buildBarChart(),
    );
  }

  /// 构建折线图
  Widget _buildLineChart() {
    final spots = data.salesTrendData.asMap().entries.map((entry) {
      return FlSpot(entry.key.toDouble(), entry.value);
    }).toList();

    return LineChart(
      DashboardChartTheme.getDefaultLineChartData(
        spots: spots,
        lineColor: AppTheme.primaryColor,
        showDots: true,
        showGrid: true,
      ),
    );
  }

  /// 构建柱状图
  Widget _buildBarChart() {
    final barGroups = data.topProductsData.asMap().entries.map((entry) {
      return DashboardChartTheme.createBarGroup(
        x: entry.key,
        y: entry.value,
        color: DashboardChartTheme.getChartColor(entry.key),
      );
    }).toList();

    return BarChart(
      DashboardChartTheme.getDefaultBarChartData(
        barGroups: barGroups,
        maxY: data.topProductsData.isNotEmpty 
            ? data.topProductsData.reduce((a, b) => a > b ? a : b) * 1.2 
            : 100,
      ),
    );
  }
}

/// 销售概览数据模型
class SalesOverviewData {
  /// 今日销售额
  final double todaySales;
  
  /// 今日销售趋势(百分比)
  final double todayTrend;
  
  /// 本月销售额
  final double monthSales;
  
  /// 本月销售趋势(百分比)
  final double monthTrend;
  
  /// 订单数量
  final int orderCount;
  
  /// 订单趋势(百分比)
  final double orderTrend;
  
  /// 平均客单价
  final double avgOrderValue;
  
  /// 平均客单价趋势(百分比)
  final double avgOrderTrend;
  
  /// 销售趋势数据(最近7天)
  final List<double> salesTrendData;
  
  /// 热销商品数据(TOP5)
  final List<double> topProductsData;

  const SalesOverviewData({
    required this.todaySales,
    required this.todayTrend,
    required this.monthSales,
    required this.monthTrend,
    required this.orderCount,
    required this.orderTrend,
    required this.avgOrderValue,
    required this.avgOrderTrend,
    required this.salesTrendData,
    required this.topProductsData,
  });

  /// 空数据
  static const SalesOverviewData empty = SalesOverviewData(
    todaySales: 0,
    todayTrend: 0,
    monthSales: 0,
    monthTrend: 0,
    orderCount: 0,
    orderTrend: 0,
    avgOrderValue: 0,
    avgOrderTrend: 0,
    salesTrendData: [],
    topProductsData: [],
  );

  /// 示例数据
  static const SalesOverviewData sample = SalesOverviewData(
    todaySales: 25680,
    todayTrend: 12.5,
    monthSales: 456780,
    monthTrend: 8.3,
    orderCount: 42,
    orderTrend: 15.2,
    avgOrderValue: 611,
    avgOrderTrend: -2.1,
    salesTrendData: [15000, 18000, 22000, 19000, 25000, 23000, 25680],
    topProductsData: [8500, 7200, 6800, 5900, 5200],
  );
}
