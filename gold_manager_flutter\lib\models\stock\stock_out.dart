import '../common/document_status.dart';
import '../store/store.dart';
import 'stock_out_item.dart';

/// 出库单模型
class StockOut {
  /// ID
  final int id;

  /// 出库单号
  final String stockOutNo;

  /// 门店ID
  final int storeId;

  /// 操作员ID
  final int operatorId;

  /// 总金额
  final double totalAmount;

  /// 状态
  final DocumentStatus status;

  /// 创建时间
  final DateTime createTime;

  /// 备注
  final String? remark;

  /// 客户
  final String? customer;

  /// 销售类型
  final String? saleType;

  /// 总重量
  final double? totalWeight;

  /// 收款状态 (0=未收款,1=已收款)
  final int? paymentStatus;

  /// 收款时间
  final DateTime? paymentTime;

  /// 收款方式
  final String? paymentMethod;

  /// 支付备注
  final String? paymentRemark;

  /// 现金金额
  final double? cashAmount;

  /// 微信金额
  final double? wechatAmount;

  /// 支付宝金额
  final double? alipayAmount;

  /// 刷卡金额
  final double? cardAmount;

  /// 抹零金额
  final double? discountAmount;

  /// 实收金额
  final double? actualAmount;

  /// 操作员姓名
  final String? operatorName;

  /// 关联门店
  final Store? store;

  /// 出库明细列表
  final List<StockOutItem>? items;

  /// 构造函数
  const StockOut({
    required this.id,
    required this.stockOutNo,
    required this.storeId,
    required this.operatorId,
    required this.totalAmount,
    required this.status,
    required this.createTime,
    this.remark,
    this.customer,
    this.saleType,
    this.totalWeight,
    this.paymentStatus,
    this.paymentTime,
    this.paymentMethod,
    this.paymentRemark,
    this.cashAmount,
    this.wechatAmount,
    this.alipayAmount,
    this.cardAmount,
    this.discountAmount,
    this.actualAmount,
    this.operatorName,
    this.store,
    this.items,
  });

  /// 从JSON构造
  factory StockOut.fromJson(Map<String, dynamic>? json) {
    // 空值检查
    if (json == null) {
      throw ArgumentError('StockOut.fromJson: json参数不能为null');
    }

    return StockOut(
      id: json['id'] ?? 0,
      stockOutNo: json['order_no'] ?? json['stock_out_no'] ?? '',
      storeId: json['store_id'] ?? 0,
      operatorId: json['operator_id'] ?? 0,
      totalAmount: _parseDouble(json['total_amount']),
      status: DocumentStatus.fromValue(json['status'] ?? 0),
      createTime: DateTime.fromMillisecondsSinceEpoch(
        (json['createtime'] ?? json['create_time'] ?? 0) * 1000,
      ),
      remark: json['remark'],
      customer: json['customer'],
      saleType: json['sale_type'],
      totalWeight: _parseDouble(json['total_weight']),
      paymentStatus: json['payment_status'],
      paymentTime: json['payment_time'] != null
        ? DateTime.fromMillisecondsSinceEpoch(json['payment_time'] * 1000)
        : null,
      paymentMethod: json['payment_method'],
      paymentRemark: json['payment_remark'],
      cashAmount: _parseDouble(json['cash_amount']),
      wechatAmount: _parseDouble(json['wechat_amount']),
      alipayAmount: _parseDouble(json['alipay_amount']),
      cardAmount: _parseDouble(json['card_amount']),
      discountAmount: _parseDouble(json['discount_amount']),
      actualAmount: _parseDouble(json['actual_amount']),
      operatorName: json['operator_name'],
      store: json['store'] != null ? Store.fromJson(json['store']) : 
             (json['store_name'] != null ? Store(id: json['store_id'] ?? 0, name: json['store_name']) : null),
      items: json['items'] != null
        ? (json['items'] as List).map((item) => StockOutItem.fromJson(item)).toList()
        : null,
    );
  }

  /// 安全解析双精度浮点数
  static double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value) ?? 0.0;
    }
    return 0.0;
  }

  /// 转为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'order_no': stockOutNo,
      'store_id': storeId,
      'operator_id': operatorId,
      'total_amount': totalAmount,
      'status': status.value,
      'createtime': createTime.millisecondsSinceEpoch ~/ 1000,
      'remark': remark,
      'customer': customer,
      'sale_type': saleType,
      'total_weight': totalWeight,
      'items': items?.map((item) => item.toJson()).toList(),
    };
  }

  /// 复制并修改属性
  StockOut copyWith({
    int? id,
    String? stockOutNo,
    int? storeId,
    int? operatorId,
    double? totalAmount,
    DocumentStatus? status,
    DateTime? createTime,
    String? remark,
    String? customer,
    String? saleType,
    double? totalWeight,
    int? paymentStatus,
    DateTime? paymentTime,
    String? paymentMethod,
    String? paymentRemark,
    double? cashAmount,
    double? wechatAmount,
    double? alipayAmount,
    double? cardAmount,
    double? discountAmount,
    double? actualAmount,
    String? operatorName,
    Store? store,
    List<StockOutItem>? items,
  }) {
    return StockOut(
      id: id ?? this.id,
      stockOutNo: stockOutNo ?? this.stockOutNo,
      storeId: storeId ?? this.storeId,
      operatorId: operatorId ?? this.operatorId,
      totalAmount: totalAmount ?? this.totalAmount,
      status: status ?? this.status,
      createTime: createTime ?? this.createTime,
      remark: remark ?? this.remark,
      customer: customer ?? this.customer,
      saleType: saleType ?? this.saleType,
      totalWeight: totalWeight ?? this.totalWeight,
      paymentStatus: paymentStatus ?? this.paymentStatus,
      paymentTime: paymentTime ?? this.paymentTime,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      paymentRemark: paymentRemark ?? this.paymentRemark,
      cashAmount: cashAmount ?? this.cashAmount,
      wechatAmount: wechatAmount ?? this.wechatAmount,
      alipayAmount: alipayAmount ?? this.alipayAmount,
      cardAmount: cardAmount ?? this.cardAmount,
      discountAmount: discountAmount ?? this.discountAmount,
      actualAmount: actualAmount ?? this.actualAmount,
      operatorName: operatorName ?? this.operatorName,
      store: store ?? this.store,
      items: items ?? this.items,
    );
  }

  /// 是否可编辑 - 普通逻辑，可被外部权限覆盖
  bool get canEdit => status == DocumentStatus.draft || status == DocumentStatus.pending;

  /// 是否可删除 - 普通逻辑，可被外部权限覆盖
  bool get canDelete => status == DocumentStatus.draft || status == DocumentStatus.pending;

  /// 是否可审核
  bool get canApprove => status == DocumentStatus.pending;

  /// 检查是否可编辑（考虑用户权限）
  bool canEditWithPermission(bool isSuperAdmin) {
    // 超级管理员可以编辑任何状态的出库单（除已取消）
    if (isSuperAdmin) {
      return status != DocumentStatus.cancelled;
    }
    // 普通用户只能编辑草稿和待审核状态
    return canEdit;
  }

  /// 检查是否可删除（考虑用户权限）
  bool canDeleteWithPermission(bool isSuperAdmin) {
    // 根据后端业务规则：只有待审核状态才能删除
    // 不管是否为超级管理员，都遵循这个规则
    return canDelete;
  }


  /// 获取出库件数
  int get itemCount => items?.length ?? 0;
}