/// 销售报表数据模型
class SalesReportData {
  /// 销售总额
  final double totalAmount;
  
  /// 销售数量
  final int totalCount;
  
  /// 平均单价
  final double averagePrice;
  
  /// 销售明细
  final List<SalesReportItem> items;
  
  /// 日期数据（用于图表）
  final List<ChartData> chartData;
  
  /// 分类数据（用于饼图）
  final List<CategoryData> categoryData;
  
  SalesReportData({
    this.totalAmount = 0.0,
    this.totalCount = 0,
    this.averagePrice = 0.0,
    this.items = const [],
    this.chartData = const [],
    this.categoryData = const [],
  });
  
  /// 从JSON创建
  factory SalesReportData.fromJson(Map<String, dynamic> json) {
    return SalesReportData(
      totalAmount: (json['total_amount'] as num?)?.toDouble() ?? 0.0,
      totalCount: (json['total_count'] as num?)?.toInt() ?? 0,
      averagePrice: (json['average_price'] as num?)?.toDouble() ?? 0.0,
      items: (json['items'] as List<dynamic>?)
          ?.map((e) => SalesReportItem.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
      chartData: (json['chart_data'] as List<dynamic>?)
          ?.map((e) => ChartData.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
      categoryData: (json['category_data'] as List<dynamic>?)
          ?.map((e) => CategoryData.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
    );
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'total_amount': totalAmount,
      'total_count': totalCount,
      'average_price': averagePrice,
      'items': items.map((e) => e.toJson()).toList(),
      'chart_data': chartData.map((e) => e.toJson()).toList(),
      'category_data': categoryData.map((e) => e.toJson()).toList(),
    };
  }
}

/// 销售报表明细项
class SalesReportItem {
  /// 日期
  final DateTime date;
  
  /// 订单号
  final String orderNo;
  
  /// 商品名称
  final String itemName;
  
  /// 商品类别
  final String categoryName;
  
  /// 商品数量
  final int quantity;
  
  /// 金重(g)
  final double goldWeight;
  
  /// 银重(g)
  final double silverWeight;
  
  /// 销售金额
  final double amount;
  
  /// 门店名称
  final String storeName;
  
  SalesReportItem({
    required this.date,
    required this.orderNo,
    required this.itemName,
    required this.categoryName,
    required this.quantity,
    required this.goldWeight,
    required this.silverWeight,
    required this.amount,
    required this.storeName,
  });
  
  /// 从JSON创建
  factory SalesReportItem.fromJson(Map<String, dynamic> json) {
    return SalesReportItem(
      date: DateTime.parse(json['date'] as String),
      orderNo: json['order_no'] as String,
      itemName: json['item_name'] as String,
      categoryName: json['category_name'] as String,
      quantity: (json['quantity'] as num).toInt(),
      goldWeight: (json['gold_weight'] as num).toDouble(),
      silverWeight: (json['silver_weight'] as num).toDouble(),
      amount: (json['amount'] as num).toDouble(),
      storeName: json['store_name'] as String,
    );
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'order_no': orderNo,
      'item_name': itemName,
      'category_name': categoryName,
      'quantity': quantity,
      'gold_weight': goldWeight,
      'silver_weight': silverWeight,
      'amount': amount,
      'store_name': storeName,
    };
  }
}

/// 库存报表数据模型
class InventoryReportData {
  /// 库存总数量
  final int totalCount;
  
  /// 库存总价值
  final double totalValue;
  
  /// 总金重(g)
  final double totalGoldWeight;
  
  /// 总银重(g)
  final double totalSilverWeight;
  
  /// 库存明细
  final List<InventoryReportItem> items;
  
  /// 分类数据（用于饼图）
  final List<CategoryData> categoryData;
  
  InventoryReportData({
    this.totalCount = 0,
    this.totalValue = 0.0,
    this.totalGoldWeight = 0.0,
    this.totalSilverWeight = 0.0,
    this.items = const [],
    this.categoryData = const [],
  });
  
  /// 从JSON创建
  factory InventoryReportData.fromJson(Map<String, dynamic> json) {
    return InventoryReportData(
      totalCount: (json['total_count'] as num?)?.toInt() ?? 0,
      totalValue: (json['total_value'] as num?)?.toDouble() ?? 0.0,
      totalGoldWeight: (json['total_gold_weight'] as num?)?.toDouble() ?? 0.0,
      totalSilverWeight: (json['total_silver_weight'] as num?)?.toDouble() ?? 0.0,
      items: (json['items'] as List<dynamic>?)
          ?.map((e) => InventoryReportItem.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
      categoryData: (json['category_data'] as List<dynamic>?)
          ?.map((e) => CategoryData.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
    );
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'total_count': totalCount,
      'total_value': totalValue,
      'total_gold_weight': totalGoldWeight,
      'total_silver_weight': totalSilverWeight,
      'items': items.map((e) => e.toJson()).toList(),
      'category_data': categoryData.map((e) => e.toJson()).toList(),
    };
  }
}

/// 库存报表明细项
class InventoryReportItem {
  /// 商品条码
  final String barcode;
  
  /// 商品名称
  final String name;
  
  /// 商品类别
  final String categoryName;
  
  /// 金重(g)
  final double goldWeight;
  
  /// 银重(g)
  final double silverWeight;
  
  /// 总重量(g)
  final double totalWeight;
  
  /// 成本价
  final double costPrice;
  
  /// 销售价
  final double salePrice;
  
  /// 库存数量
  final int quantity;
  
  /// 库存价值
  final double totalValue;
  
  /// 门店名称
  final String storeName;
  
  InventoryReportItem({
    required this.barcode,
    required this.name,
    required this.categoryName,
    required this.goldWeight,
    required this.silverWeight,
    required this.totalWeight,
    required this.costPrice,
    required this.salePrice,
    required this.quantity,
    required this.totalValue,
    required this.storeName,
  });
  
  /// 从JSON创建
  factory InventoryReportItem.fromJson(Map<String, dynamic> json) {
    return InventoryReportItem(
      barcode: json['barcode'] as String,
      name: json['name'] as String,
      categoryName: json['category_name'] as String,
      goldWeight: (json['gold_weight'] as num).toDouble(),
      silverWeight: (json['silver_weight'] as num).toDouble(),
      totalWeight: (json['total_weight'] as num).toDouble(),
      costPrice: (json['cost_price'] as num).toDouble(),
      salePrice: (json['sale_price'] as num).toDouble(),
      quantity: (json['quantity'] as num).toInt(),
      totalValue: (json['total_value'] as num).toDouble(),
      storeName: json['store_name'] as String,
    );
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'barcode': barcode,
      'name': name,
      'category_name': categoryName,
      'gold_weight': goldWeight,
      'silver_weight': silverWeight,
      'total_weight': totalWeight,
      'cost_price': costPrice,
      'sale_price': salePrice,
      'quantity': quantity,
      'total_value': totalValue,
      'store_name': storeName,
    };
  }
}

/// 回收报表数据模型
class RecyclingReportData {
  /// 回收总笔数
  final int totalCount;
  
  /// 回收总金额
  final double totalAmount;
  
  /// 回收总重量(g)
  final double totalWeight;
  
  /// 回收金重(g)
  final double totalGoldWeight;
  
  /// 回收银重(g)
  final double totalSilverWeight;
  
  /// 回收明细
  final List<RecyclingReportItem> items;
  
  /// 日期数据（用于图表）
  final List<ChartData> chartData;
  
  /// 处理方式数据（用于饼图）
  final List<ProcessTypeData> processTypeData;
  
  RecyclingReportData({
    this.totalCount = 0,
    this.totalAmount = 0.0,
    this.totalWeight = 0.0,
    this.totalGoldWeight = 0.0,
    this.totalSilverWeight = 0.0,
    this.items = const [],
    this.chartData = const [],
    this.processTypeData = const [],
  });
  
  /// 从JSON创建
  factory RecyclingReportData.fromJson(Map<String, dynamic> json) {
    return RecyclingReportData(
      totalCount: (json['total_count'] as num?)?.toInt() ?? 0,
      totalAmount: (json['total_amount'] as num?)?.toDouble() ?? 0.0,
      totalWeight: (json['total_weight'] as num?)?.toDouble() ?? 0.0,
      totalGoldWeight: (json['total_gold_weight'] as num?)?.toDouble() ?? 0.0,
      totalSilverWeight: (json['total_silver_weight'] as num?)?.toDouble() ?? 0.0,
      items: (json['items'] as List<dynamic>?)
          ?.map((e) => RecyclingReportItem.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
      chartData: (json['chart_data'] as List<dynamic>?)
          ?.map((e) => ChartData.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
      processTypeData: (json['process_type_data'] as List<dynamic>?)
          ?.map((e) => ProcessTypeData.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
    );
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'total_count': totalCount,
      'total_amount': totalAmount,
      'total_weight': totalWeight,
      'total_gold_weight': totalGoldWeight,
      'total_silver_weight': totalSilverWeight,
      'items': items.map((e) => e.toJson()).toList(),
      'chart_data': chartData.map((e) => e.toJson()).toList(),
      'process_type_data': processTypeData.map((e) => e.toJson()).toList(),
    };
  }
}

/// 回收报表明细项
class RecyclingReportItem {
  /// 回收单号
  final String orderNo;
  
  /// 回收日期
  final DateTime date;
  
  /// 客户姓名
  final String customerName;
  
  /// 物品名称
  final String itemName;
  
  /// 物品分类
  final String categoryName;
  
  /// 重量(g)
  final double weight;
  
  /// 回收单价
  final double price;
  
  /// 回收金额
  final double amount;
  
  /// 处理方式
  final String processType;
  
  /// 处理状态
  final int processStatus;
  
  /// 处理收益
  final double processResult;
  
  RecyclingReportItem({
    required this.orderNo,
    required this.date,
    required this.customerName,
    required this.itemName,
    required this.categoryName,
    required this.weight,
    required this.price,
    required this.amount,
    required this.processType,
    required this.processStatus,
    required this.processResult,
  });
  
  /// 从JSON创建
  factory RecyclingReportItem.fromJson(Map<String, dynamic> json) {
    return RecyclingReportItem(
      orderNo: json['order_no'] as String,
      date: DateTime.parse(json['date'] as String),
      customerName: json['customer_name'] as String,
      itemName: json['item_name'] as String,
      categoryName: json['category_name'] as String,
      weight: (json['weight'] as num).toDouble(),
      price: (json['price'] as num).toDouble(),
      amount: (json['amount'] as num).toDouble(),
      processType: json['process_type'] as String,
      processStatus: (json['process_status'] as num).toInt(),
      processResult: (json['process_result'] as num).toDouble(),
    );
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'order_no': orderNo,
      'date': date.toIso8601String(),
      'customer_name': customerName,
      'item_name': itemName,
      'category_name': categoryName,
      'weight': weight,
      'price': price,
      'amount': amount,
      'process_type': processType,
      'process_status': processStatus,
      'process_result': processResult,
    };
  }
}

/// 图表数据
class ChartData {
  /// 日期
  final DateTime date;
  
  /// 数值
  final double value;
  
  ChartData({
    required this.date,
    required this.value,
  });
  
  /// 从JSON创建
  factory ChartData.fromJson(Map<String, dynamic> json) {
    return ChartData(
      date: DateTime.parse(json['date'] as String),
      value: (json['value'] as num).toDouble(),
    );
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'value': value,
    };
  }
}

/// 分类数据
class CategoryData {
  /// 分类名称
  final String name;
  
  /// 数值
  final double value;
  
  /// 百分比
  final double percentage;
  
  CategoryData({
    required this.name,
    required this.value,
    required this.percentage,
  });
  
  /// 从JSON创建
  factory CategoryData.fromJson(Map<String, dynamic> json) {
    return CategoryData(
      name: json['name'] as String,
      value: (json['value'] as num).toDouble(),
      percentage: (json['percentage'] as num).toDouble(),
    );
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'value': value,
      'percentage': percentage,
    };
  }
}

/// 处理方式数据
class ProcessTypeData {
  /// 处理方式
  final String type;
  
  /// 数量
  final int count;
  
  /// 百分比
  final double percentage;
  
  ProcessTypeData({
    required this.type,
    required this.count,
    required this.percentage,
  });
  
  /// 从JSON创建
  factory ProcessTypeData.fromJson(Map<String, dynamic> json) {
    return ProcessTypeData(
      type: json['type'] as String,
      count: (json['count'] as num).toInt(),
      percentage: (json['percentage'] as num).toDouble(),
    );
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'count': count,
      'percentage': percentage,
    };
  }
} 