import '../common/date_utils.dart';
import '../common/enums.dart';
import '../jewelry/jewelry.dart';

/// 回收单明细模型
class RecyclingItem {
  final int id;
  final int recyclingId; // 回收单ID
  final String name; // 物品名称
  final String? description; // 物品描述
  final double goldWeight; // 金重(g)
  final double goldPrice; // 金价(元/g)
  final double silverWeight; // 银重(g)
  final double silverPrice; // 银价(元/g)
  final double amount; // 金额
  final String? imageUrl; // 图片URL
  final RecyclingProcessType processType; // 处理方式
  final int processStatus; // 处理状态: 0=未处理,1=处理中,2=已处理,3=已入库
  final DateTime? processTime; // 处理时间
  final double processResult; // 处理收益
  final DateTime? createTime;
  final int? type; // 物品类型：1=黄金首饰,2=银饰,3=金银混合,4=其他
  final double weight; // 物品总重量
  final int? processId; // 关联的处理工单ID

  // 关联对象
  final Jewelry? recycledJewelry; // 关联的新首饰(维修/再销售时)

  const RecyclingItem({
    required this.id,
    required this.recyclingId,
    required this.name,
    this.description,
    required this.goldWeight,
    required this.goldPrice,
    required this.silverWeight,
    required this.silverPrice,
    required this.amount,
    this.imageUrl,
    this.processType = RecyclingProcessType.pending,
    this.processStatus = 0,
    this.processTime,
    this.processResult = 0.0,
    this.createTime,
    this.type = 1,
    this.weight = 0,
    this.processId,
    this.recycledJewelry,
  });

  /// 从JSON构造
  factory RecyclingItem.fromJson(Map<String, dynamic> json) {
    // 安全的数字转换函数
    double parseDouble(dynamic value) {
      if (value == null) return 0.0;
      if (value is double) return value;
      if (value is int) return value.toDouble();
      if (value is String) {
        return double.tryParse(value) ?? 0.0;
      }
      return 0.0;
    }

    return RecyclingItem(
      id: json['id'],
      recyclingId: json['recycling_id'],
      name: json['name'],
      description: json['description'],
      goldWeight: parseDouble(json['gold_weight']),
      goldPrice: parseDouble(json['gold_price']),
      silverWeight: parseDouble(json['silver_weight']),
      silverPrice: parseDouble(json['silver_price']),
      amount: parseDouble(json['amount']),
      imageUrl: json['image_url'],
      processType: RecyclingProcessType.fromValue(
        json['process_type'] ?? 'pending',
      ),
      processStatus: json['process_status'] ?? 0,
      processTime: DateUtil.fromUnixTimestamp(json['process_time']),
      processResult: parseDouble(json['process_result']),
      createTime: DateUtil.fromUnixTimestamp(json['createtime']),
      type: json['type'] ?? 1,
      weight: parseDouble(json['weight']),
      processId: json['process_id'],
      recycledJewelry: json['recycled_jewelry'] != null
          ? Jewelry.fromJson(json['recycled_jewelry'])
          : null,
    );
  }

  /// 转为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'recycling_id': recyclingId,
      'name': name,
      'description': description,
      'gold_weight': goldWeight,
      'gold_price': goldPrice,
      'silver_weight': silverWeight,
      'silver_price': silverPrice,
      'amount': amount,
      'image_url': imageUrl,
      'process_type': processType.value,
      'process_status': processStatus,
      'process_time': DateUtil.toUnixTimestamp(processTime),
      'process_result': processResult,
      'type': type,
      'weight': weight,
      'process_id': processId,
      'createtime': DateUtil.toUnixTimestamp(createTime),
    };
  }

  /// 计算预估价值
  double calculateEstimatedValue() {
    return goldWeight * goldPrice + silverWeight * silverPrice;
  }

  /// 获取处理状态文本
  String getProcessStatusText() {
    switch (processStatus) {
      case 0:
        return '未处理';
      case 1:
        return '处理中';
      case 2:
        return '已处理';
      case 3:
        return '已入库';
      default:
        return '未知';
    }
  }

  /// 获取物品类型名称
  String getTypeName() {
    switch (type) {
      case 1:
        return '黄金首饰';
      case 2:
        return '银饰';
      case 3:
        return '金银混合';
      case 4:
        return '其他';
      default:
        return '未知';
    }
  }

  /// 获取物品名称
  String get itemName => name;

  /// 获取回收时间
  DateTime get recyclingTime => createTime ?? DateTime.now();

  /// 获取物品单价
  double get price => amount / (weight > 0 ? weight : 1);

  /// 是否已处理
  bool get isProcessed => processStatus == 2;

  /// 是否正在处理
  bool get isProcessing => processStatus == 1;

  /// 是否可以处理
  bool get canProcess => processStatus == 0;

  /// 创建一个新实例，但使用部分属性
  RecyclingItem copyWith({
    int? id,
    int? recyclingId,
    String? name,
    String? description,
    double? goldWeight,
    double? goldPrice,
    double? silverWeight,
    double? silverPrice,
    double? amount,
    String? imageUrl,
    RecyclingProcessType? processType,
    int? processStatus,
    DateTime? processTime,
    double? processResult,
    DateTime? createTime,
    int? type,
    double? weight,
    int? processId,
    Jewelry? recycledJewelry,
  }) {
    return RecyclingItem(
      id: id ?? this.id,
      recyclingId: recyclingId ?? this.recyclingId,
      name: name ?? this.name,
      description: description ?? this.description,
      goldWeight: goldWeight ?? this.goldWeight,
      goldPrice: goldPrice ?? this.goldPrice,
      silverWeight: silverWeight ?? this.silverWeight,
      silverPrice: silverPrice ?? this.silverPrice,
      amount: amount ?? this.amount,
      imageUrl: imageUrl ?? this.imageUrl,
      processType: processType ?? this.processType,
      processStatus: processStatus ?? this.processStatus,
      processTime: processTime ?? this.processTime,
      processResult: processResult ?? this.processResult,
      createTime: createTime ?? this.createTime,
      type: type ?? this.type,
      weight: weight ?? this.weight,
      processId: processId ?? this.processId,
      recycledJewelry: recycledJewelry ?? this.recycledJewelry,
    );
  }
}
