#!/bin/bash
# 依赖安装和环境配置脚本
# 黄金珠宝管理系统 - 自动化部署脚本

set -e

echo "🚀 开始安装黄金珠宝管理系统依赖..."

# 确保在正确的目录
cd "$(dirname "$0")/.."

# 检查Python版本
python_version=$(python3 --version 2>&1 | cut -d' ' -f2 | cut -d'.' -f1,2)
required_version="3.8"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "❌ Python版本过低，需要3.8或更高版本，当前版本: $python_version"
    exit 1
fi

echo "✅ Python版本检查通过: $python_version"

# 创建虚拟环境（如果不存在）
if [ ! -d "jewelry_env" ]; then
    echo "📦 创建Python虚拟环境..."
    python3 -m venv jewelry_env
fi

# 激活虚拟环境
echo "🔧 激活虚拟环境..."
source jewelry_env/bin/activate

# 升级pip
echo "⬆️ 升级pip..."
pip install --upgrade pip

# 安装基础依赖
echo "📦 安装项目依赖..."
pip install -r requirements.txt

# 验证关键依赖安装
echo "🔍 验证依赖安装状态..."

# 检查FastAPI
if python -c "import fastapi" 2>/dev/null; then
    echo "✅ FastAPI 安装成功"
else
    echo "❌ FastAPI 安装失败"
    exit 1
fi

# 检查FastAPI-Guard
if python -c "import fastapi_guard" 2>/dev/null; then
    echo "✅ FastAPI-Guard 安装成功"
else
    echo "⚠️ FastAPI-Guard 安装失败，安全防护功能将不可用"
fi

# 检查Prometheus
if python -c "import prometheus_fastapi_instrumentator" 2>/dev/null; then
    echo "✅ Prometheus监控 安装成功"
else
    echo "⚠️ Prometheus监控 安装失败，监控功能将不可用"
fi

# 检查Ruff
if command -v ruff &> /dev/null; then
    echo "✅ Ruff代码质量工具 安装成功"
else
    echo "⚠️ Ruff 安装失败，代码质量检查功能将不可用"
fi

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p logs
mkdir -p reports
mkdir -p ssl

# 设置脚本权限
echo "🔐 设置脚本权限..."
chmod +x scripts/*.sh

# 创建环境配置文件（如果不存在）
if [ ! -f "env_config.txt" ]; then
    echo "⚙️ 创建环境配置文件..."
    cp env_config_example.txt env_config.txt
    echo "🔧 请编辑 env_config.txt 文件设置数据库连接信息"
fi

# 运行代码质量检查
echo "🔍 运行初始代码质量检查..."
if command -v ruff &> /dev/null; then
    ruff check app/ --fix || echo "⚠️ 发现代码质量问题，已自动修复部分问题"
    ruff format app/ || echo "⚠️ 代码格式化完成"
else
    echo "⚠️ Ruff未安装，跳过代码质量检查"
fi

# 生成依赖报告
echo "📋 生成依赖报告..."
pip freeze > reports/installed_packages.txt

echo ""
echo "🎉 依赖安装完成！"
echo ""
echo "📋 安装摘要："
echo "  ✅ Python环境: $(python --version)"
echo "  ✅ 虚拟环境: jewelry_env/"
echo "  ✅ 依赖包数量: $(pip list | wc -l)"
echo "  ✅ 配置文件: env_config.txt"
echo "  ✅ 日志目录: logs/"
echo "  ✅ 报告目录: reports/"
echo ""
echo "🚀 下一步操作："
echo "  1. 编辑 env_config.txt 配置数据库连接"
echo "  2. 运行 'make run' 启动开发服务器"
echo "  3. 访问 http://localhost:8000/docs 查看API文档"
echo "  4. 访问 http://localhost:8000/metrics 查看监控指标"
echo ""
echo "💡 常用命令："
echo "  make help     - 查看所有可用命令"
echo "  make check    - 运行代码质量检查"
echo "  make test     - 运行测试"
echo "  make clean    - 清理临时文件"
echo ""
echo "📚 更多信息请查看 README.md 文件"