import 'package:equatable/equatable.dart';
import '../jewelry/jewelry.dart';
import '../jewelry/jewelry_category.dart';
import '../user/user.dart';

/// 库存盘点明细模型
class InventoryCheckItem extends Equatable {
  /// 明细ID
  final int id;
  
  /// 盘点单ID
  final int checkId;
  
  /// 商品ID
  final int jewelryId;
  
  /// 商品信息
  final Jewelry? jewelry;
  
  /// 条码
  final String barcode;
  
  /// 商品名称
  final String name;
  
  /// 分类ID
  final int categoryId;
  
  /// 分类信息
  final JewelryCategory? category;
  
  /// 圈口号
  final String? ringSize;
  
  /// 状态：0=未盘点，1=已盘点
  final int status;
  
  /// 系统库存
  final int systemStock;
  
  /// 实际库存
  final int? actualStock;
  
  /// 差异
  final int? difference;
  
  /// 盘点时间
  final DateTime? checkTime;
  
  /// 盘点人员ID
  final int? checkUserId;
  
  /// 盘点人员信息
  final User? checkUser;
  
  /// 备注
  final String? remark;
  
  /// 创建时间
  final DateTime createTime;

  const InventoryCheckItem({
    required this.id,
    required this.checkId,
    required this.jewelryId,
    this.jewelry,
    required this.barcode,
    required this.name,
    required this.categoryId,
    this.category,
    this.ringSize,
    required this.status,
    required this.systemStock,
    this.actualStock,
    this.difference,
    this.checkTime,
    this.checkUserId,
    this.checkUser,
    this.remark,
    required this.createTime,
  });

  /// 状态文本
  String get statusText {
    switch (status) {
      case 0:
        return '未盘点';
      case 1:
        return '已盘点';
      default:
        return '未知';
    }
  }

  /// 状态颜色
  String get statusColor {
    switch (status) {
      case 0:
        return 'orange';
      case 1:
        return 'green';
      default:
        return 'grey';
    }
  }

  /// 是否已盘点
  bool get isChecked {
    return status == 1;
  }

  /// 是否有差异
  bool get hasDifference {
    return difference != null && difference != 0;
  }

  /// 差异类型文本
  String get differenceText {
    if (difference == null) return '-';
    if (difference! > 0) return '+${difference!}';
    if (difference! < 0) return '${difference!}';
    return '0';
  }

  /// 差异类型颜色
  String get differenceColor {
    if (difference == null || difference == 0) return 'grey';
    if (difference! > 0) return 'green';
    return 'red';
  }

  /// 分类名称
  String get categoryName {
    return category?.name ?? '未知分类';
  }

  /// 从JSON创建实例
  factory InventoryCheckItem.fromJson(Map<String, dynamic> json) {
    return InventoryCheckItem(
      id: json['id'] ?? 0,
      checkId: json['check_id'] ?? 0,
      jewelryId: json['jewelry_id'] ?? 0,
      jewelry: json['jewelry'] != null ? Jewelry.fromJson(json['jewelry']) : null,
      barcode: json['barcode'] ?? '',
      name: json['name'] ?? '',
      categoryId: json['category_id'] ?? 0,
      category: json['category'] != null ? JewelryCategory.fromJson(json['category']) : null,
      ringSize: json['ring_size'],
      status: json['status'] ?? 0,
      systemStock: json['system_stock'] ?? 1,
      actualStock: json['actual_stock'],
      difference: json['difference'],
      checkTime: json['check_time'] != null 
          ? DateTime.fromMillisecondsSinceEpoch((json['check_time'] as int) * 1000)
          : null,
      checkUserId: json['check_user_id'],
      checkUser: json['check_user'] != null ? User.fromJson(json['check_user']) : null,
      remark: json['remark'],
      createTime: DateTime.fromMillisecondsSinceEpoch((json['createtime'] ?? 0) * 1000),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'check_id': checkId,
      'jewelry_id': jewelryId,
      'jewelry': jewelry?.toJson(),
      'barcode': barcode,
      'name': name,
      'category_id': categoryId,
      'category': category?.toJson(),
      'ring_size': ringSize,
      'status': status,
      'system_stock': systemStock,
      'actual_stock': actualStock,
      'difference': difference,
      'check_time': checkTime?.millisecondsSinceEpoch != null ? checkTime!.millisecondsSinceEpoch ~/ 1000 : null,
      'check_user_id': checkUserId,
      'check_user': checkUser?.toJson(),
      'remark': remark,
      'createtime': createTime.millisecondsSinceEpoch ~/ 1000,
    };
  }

  /// 复制并修改部分属性
  InventoryCheckItem copyWith({
    int? id,
    int? checkId,
    int? jewelryId,
    Jewelry? jewelry,
    String? barcode,
    String? name,
    int? categoryId,
    JewelryCategory? category,
    String? ringSize,
    int? status,
    int? systemStock,
    int? actualStock,
    int? difference,
    DateTime? checkTime,
    int? checkUserId,
    User? checkUser,
    String? remark,
    DateTime? createTime,
  }) {
    return InventoryCheckItem(
      id: id ?? this.id,
      checkId: checkId ?? this.checkId,
      jewelryId: jewelryId ?? this.jewelryId,
      jewelry: jewelry ?? this.jewelry,
      barcode: barcode ?? this.barcode,
      name: name ?? this.name,
      categoryId: categoryId ?? this.categoryId,
      category: category ?? this.category,
      ringSize: ringSize ?? this.ringSize,
      status: status ?? this.status,
      systemStock: systemStock ?? this.systemStock,
      actualStock: actualStock ?? this.actualStock,
      difference: difference ?? this.difference,
      checkTime: checkTime ?? this.checkTime,
      checkUserId: checkUserId ?? this.checkUserId,
      checkUser: checkUser ?? this.checkUser,
      remark: remark ?? this.remark,
      createTime: createTime ?? this.createTime,
    );
  }

  @override
  List<Object?> get props => [
        id,
        checkId,
        jewelryId,
        jewelry,
        barcode,
        name,
        categoryId,
        category,
        ringSize,
        status,
        systemStock,
        actualStock,
        difference,
        checkTime,
        checkUserId,
        checkUser,
        remark,
        createTime,
      ];

  @override
  String toString() {
    return 'InventoryCheckItem{id: $id, barcode: $barcode, name: $name, status: $status}';
  }
}
