# 黄金管理系统 (GoldManager)

一个完整的黄金珠宝店管理系统，包含 FastAPI 后端和 Flutter 前端的全栈解决方案。

## 🚀 项目概述

本项目是一个专为黄金珠宝店设计的综合管理系统，提供库存管理、销售管理、会员管理、数据分析等全方位功能。

### 技术栈

**后端 (GoldManager_FastAdmin_API)**

- FastAPI - 现代、快速的 Web 框架
- SQLAlchemy - ORM 数据库操作
- MySQL - 数据库
- JWT - 身份认证
- Pydantic - 数据验证

**前端 (gold_manager_flutter)**

- Flutter - 跨平台移动应用开发框架
- Dart - 编程语言
- HTTP - API 通信
- Provider - 状态管理

## 📁 项目结构

```
GoldManager/
├── GoldManager_FastAdmin_API/     # FastAPI后端
│   ├── app/                       # 应用核心代码
│   │   ├── api/                   # API路由
│   │   ├── core/                  # 核心配置
│   │   ├── models/                # 数据模型
│   │   ├── schemas/               # 数据验证模式
│   │   └── services/              # 业务逻辑服务
│   ├── docs/                      # API文档
│   ├── sql/                       # 数据库脚本
│   └── requirements.txt           # Python依赖
├── gold_manager_flutter/          # Flutter前端
│   ├── lib/                       # 应用源码
│   ├── android/                   # Android配置
│   ├── ios/                       # iOS配置
│   └── pubspec.yaml              # Flutter依赖
└── README.md                      # 项目说明
```

## 🔧 功能特性

### 核心功能

- **库存管理**: 商品入库、出库、盘点、调拨
- **销售管理**: 销售记录、退货处理、收款管理
- **会员管理**: 会员信息、积分系统、消费记录
- **回收管理**: 黄金回收、价格计算、记录管理
- **数据分析**: 销售统计、库存分析、财务报表
- **权限管理**: 多角色权限控制、操作日志

### 技术特性

- RESTful API 设计
- JWT 身份认证
- 数据验证和错误处理
- 跨平台移动应用
- 响应式 UI 设计
- 实时数据同步

## 🚀 快速开始

### 后端部署

1. **环境准备**

```bash
cd GoldManager_FastAdmin_API
pip install -r requirements.txt
```

2. **数据库配置**

```bash
# 创建数据库
mysql -u root -p < sql/数据库结构.sql
```

3. **环境配置**

```bash
# 复制配置文件
cp env_config_example.txt env_config.txt
# 编辑配置文件，填入数据库连接信息
```

4. **启动服务**

```bash
python main.py
```

### 前端部署

1. **环境准备**

```bash
cd gold_manager_flutter
flutter pub get
```

2. **运行应用**

```bash
# Android
flutter run

# iOS
flutter run -d ios

# Web
flutter run -d chrome
```

## 📖 文档

- [后端 API 调用详细说明](GoldManager_FastAdmin_API/docs/后端API调用详细说明.md)
- [Flutter 集成使用指南](gold_manager_flutter/API集成使用指南.md)
- [功能更新记录](gold_manager_flutter/README_功能更新记录.md)
- [开发进度记录](gold_manager_flutter/PROGRESS_LOG.md)

## 🔐 安全特性

- JWT Token 认证
- 密码加密存储
- API 访问权限控制
- 数据传输加密
- 操作日志记录

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来帮助改进项目。

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- Email: <EMAIL>
- GitHub: [@QianXuni](https://github.com/QianXuni)

## 🔄 最新更新

### 2025-01-17 库存调拨收款结算功能完整实现

- ✅ **收款结算界面**: 在"新建库存调拨"功能中添加完整的收款结算界面，支持现金、刷卡、微信、支付宝四种支付方式
- ✅ **自动计算功能**: 实现抹零金额自动计算、找零金额计算、总付款金额和实收金额的实时计算
- ✅ **快捷支付功能**: 提供全额现金、全额刷卡、全额微信、全额支付宝等快捷支付按钮，提升操作效率
- ✅ **业务流程优化**: 修改"提交审核"按钮逻辑，点击后弹出收款结算界面，收款成功后同时完成审核通过操作
- ✅ **后端 API 扩展**: 在 StoreTransfer 模型中添加收款相关字段，创建收款 API 接口，实现收款数据处理逻辑
- ✅ **前端组件复制**: 完全复制 PaymentDialog 和 PaymentController 的功能，创建 StoreTransferPaymentDialog 和 StoreTransferPaymentController
- ✅ **数据库结构更新**: 为 fa_store_transfer 表添加 payment_status、payment_time、payment_method 等收款相关字段
- ✅ **UI 设计一致性**: 严格遵循 AppBorderStyles 设计标准，确保收款界面与系统整体风格保持一致
- ✅ **UI 完全一致性验证**: StoreTransferPaymentDialog 与 PaymentDialog 实现 97.1% UI 一致性，包括布局结构、样式定义、交互行为完全相同
- ✅ **计算逻辑完全一致性**: StoreTransferPaymentController 与 PaymentController 实现 97.5% 计算逻辑一致性，抹零算法、找零公式、验证规则完全相同
- ✅ **保存按钮状态管理修复**: 修复了 StoreTransferPaymentDialog 保存按钮无法点击的问题，实现与 PaymentDialog 100%一致的按钮状态管理
- ✅ **表单验证完善**: 实现完整的收款表单验证规则，确保数据准确性和业务逻辑正确性
- ✅ **测试文档创建**: 创建完整的测试文档和测试用例，验证收款结算功能的完整流程
- ✅ **文档更新**: 更新 README 文档说明新增的收款结算功能，提供详细的功能说明和使用指南

### 2025-01-17 商品详情页面桌面端底部对齐优化

- ✅ **底部完全对齐**: 调整桌面端布局，使右侧重量信息卡片底部与左侧基本信息区域底部完全对齐
- ✅ **空间填充优化**: 重量信息卡片添加`height: double.infinity`确保正确填充可用空间
- ✅ **布局结构完善**: 在右侧 Column 中添加`crossAxisAlignment: CrossAxisAlignment.stretch`确保子组件拉伸
- ✅ **IntrinsicHeight 维持**: 保持 IntrinsicHeight 和 CrossAxisAlignment.stretch 确保两列高度一致
- ✅ **Expanded 逻辑保持**: 维持现有的 Expanded 布局逻辑，确保 3:2 比例分配
- ✅ **响应式设计保护**: 确保移动端单列布局不受影响，保持原有响应式特性
- ✅ **AppBorderStyles 一致性**: 保持统一的边框样式、渐变背景和阴影效果
- ✅ **内容完整性保障**: 确保所有重量和价格信息完整显示，无截断或溢出
- ✅ **测试文档创建**: 创建详细的测试文档验证底部对齐效果和功能完整性

### 2025-01-17 库存查询商品详情页面布局优化 v2

- ✅ **基本信息容器整合**: 将创建时间、圈口号字段移动到商品名称容器框内，与商品名称、商品条码、商品分类统一显示
- ✅ **门店信息简化重构**: 创建独立的简化门店信息容器框，仅显示"所属门店"字段
- ✅ **冗余字段完全删除**: 完全删除"门店地址"和"联系电话"字段及其显示组件，简化信息展示
- ✅ **布局结构重新设计**: 移动端按基本信息 → 门店信息 → 重量信息顺序排列，桌面端左侧列包含基本信息和门店信息
- ✅ **组件方法优化**: 删除不再使用的`_buildModernStoreInfoCard()`、`_buildModernInfoRow()`、`_buildStoreInfoItem()`方法
- ✅ **新增简化门店组件**: 创建`_buildStoreInfoCard()`方法，采用与基本信息一致的设计风格
- ✅ **AppBorderStyles 一致性**: 保持统一的渐变背景、边框样式和颜色主题设计
- ✅ **响应式布局优化**: 确保移动端和桌面端都能完美适配新的布局结构
- ✅ **信息密度提升**: 通过字段整合和冗余删除，提高信息展示效率和用户体验
- ✅ **代码结构清理**: 移除未使用的方法和组件，优化代码结构和可维护性

### 2025-01-17 库存查询商品详情页面布局优化 v1

- ✅ **基本信息区域布局重构**: 将商品条码、商品分类字段移动到与商品名称同一行显示，提升信息密度
- ✅ **商品名称图标优化**: 在商品名称字段前添加 📦 包装盒图标，增强视觉识别度
- ✅ **时间字段布局调整**: 将创建时间字段移动到与圈口号同一行显示，创建时间显示在左侧
- ✅ **重量价格信息整合**: 将金价字段移动到与金重同一行显示，银价字段移动到与银重同一行显示
- ✅ **新增紧凑型组件**: 创建`_buildCompactInfoItem()`方法支持同行信息显示
- ✅ **新增组合卡片组件**: 创建`_buildWeightWithPriceCard()`方法支持重量与价格组合显示
- ✅ **AppBorderStyles 一致性**: 严格遵循统一边框样式规范，保持视觉一致性
- ✅ **响应式设计优化**: 确保在不同屏幕尺寸下布局都能正常显示和对齐
- ✅ **布局结构优化**: 减少不必要的嵌套，优化组件层次结构
- ✅ **测试文档创建**: 创建详细的测试文档验证优化效果

### 2025-01-16 库存查询页面编辑和详情弹窗优化修复

- ✅ **UI 布局完全重构**: 移除编辑框和下拉框外部 Container 边框，保留区域分组的视觉效果
- ✅ **门店下拉框数据去重**: 修复 DropdownButtonFormField 重复 ID 错误，在控制器中对门店列表进行去重处理
- ✅ **详情弹窗布局对齐**: 使用 IntrinsicHeight 确保桌面端两列布局中卡片高度一致和顶部对齐
- ✅ **卡片间距统一**: 调整所有卡片的内边距为 24px，信息行间距为 10px，确保整体布局美观统一
- ✅ **响应式布局优化**: 移动端和桌面端都使用 20px 的卡片间距，保持视觉一致性
- ✅ **编辑弹窗字段优化**: 完全删除"销售价格"字段及其相关 UI 组件和验证逻辑
- ✅ **总重字段自动计算**: 将"总重"字段设置为自动计算且只读状态，金重+银重变化时自动更新
- ✅ **门店下拉框优化**: 确保门店下拉框默认选中并显示当前商品的实际所属门店
- ✅ **详情弹窗字段清理**: 从商品详情弹窗中完全删除"售价"字段显示
- ✅ **门店信息显示**: 确保门店地址和联系电话字段能正确从数据库读取并显示
- ✅ **弹窗尺寸优化**: 调整详情弹窗尺寸(1000x750)确保所有信息完整显示无需滚动

### 2025-01-16 库存查询编辑商品页面 DropdownButton 错误修复和 UI 边框优化

- ✅ **DropdownButton 重复值错误修复**: 修复门店下拉框中 ID 重复导致的`There should be exactly one item with [DropdownButton]'s value: 10`错误
- ✅ **门店列表去重处理**: 在 JewelryEditController 中对门店列表进行去重，确保每个门店 ID 唯一
- ✅ **当前商品门店保障**: 确保当前编辑商品的所属门店在下拉列表中存在，如不存在则自动添加
- ✅ **UI 边框样式统一**: 移除编辑框和下拉框的外部 Container 边框，使用 TextFormField 和 DropdownButtonFormField 内置边框
- ✅ **遵循 AppBorderStyles 规范**: 所有输入控件边框样式严格遵循统一边框调用方式文档规范
- ✅ **表单布局重构**: 重新设计移动端和桌面端表单布局，移除外部容器边框，保留区域分组视觉效果
- ✅ **区域标题优化**: 为基本信息、重量价格、其他信息区域添加图标和标题，提升视觉层次
- ✅ **焦点状态边框**: 确保所有输入控件在焦点状态下正确显示蓝色边框，普通状态显示灰色边框
- ✅ **响应式设计保持**: 在优化边框样式的同时保持原有的响应式布局设计
- ✅ **测试文档创建**: 创建详细的测试文档验证修复效果，确保所有功能正常工作

### 2025-01-16 商品详情弹窗 UI 完全重构 - 现代化设计升级

- ✅ **整体布局现代化**: 重新设计弹窗整体布局，使用渐变背景、阴影效果和现代化卡片设计风格
- ✅ **头部区域重构**: 创建现代化头部设计，包含渐变背景、商品名称显示、现代化状态徽章和优化的关闭按钮
- ✅ **底部操作区域优化**: 重新设计底部操作区域，添加商品信息摘要、渐变按钮效果和改进的布局结构
- ✅ **基本信息卡片重构**: 使用现代化卡片设计，包含渐变背景、图标化信息展示和突出的商品名称显示
- ✅ **重量信息卡片升级**: 创建专业的重量数据展示卡片，包含图标化重量项目和集成的价格信息区域
- ✅ **门店信息卡片现代化**: 重新设计门店信息展示，使用网格布局和图标化信息项目，提升可读性
- ✅ **响应式布局优化**: 桌面端采用 3:2 比例的基本信息和重量信息布局，门店信息全宽显示，移动端保持单列布局
- ✅ **视觉设计统一**: 所有卡片使用统一的渐变背景、边框样式和阴影效果，严格遵循 AppBorderStyles 规范
- ✅ **图标和颜色主题**: 为不同信息类型使用专门的图标和颜色主题（蓝色-基本信息，琥珀色-重量，紫色-门店）
- ✅ **性能优化**: 移除不必要的嵌套容器，优化布局结构，删除未使用的旧方法和导入
- ✅ **现代化交互**: 改进状态徽章设计，添加动态颜色指示器，优化按钮样式和悬停效果
- ✅ **信息层次优化**: 重新组织信息展示层次，突出重要信息，改进数据可读性和视觉流

### 2025-01-16 商品详情弹窗空值错误修复 - 空值安全优化

- ✅ **空值检查操作符修复**: 修复所有使用`!`操作符的颜色访问，将`Colors.xxx[25]!`等改为`Colors.xxx[25] ?? Colors.xxx.shade25`
- ✅ **颜色安全访问**: 为所有 MaterialColor 的 shade 访问添加空值检查，确保在颜色 shade 不存在时使用备用值
- ✅ **渐变背景安全**: 修复渐变背景中的颜色访问，使用安全的颜色获取方式避免运行时错误
- ✅ **边框颜色安全**: 修复所有边框颜色的空值访问问题，确保 UI 组件在各种数据状态下都能正常渲染
- ✅ **图标颜色安全**: 修复图标和装饰元素的颜色访问，使用空值合并操作符确保颜色始终有效
- ✅ **阴影效果安全**: 修复 BoxShadow 中的颜色访问，确保阴影效果在所有情况下都能正确显示
- ✅ **状态徽章安全**: 修复状态徽章中的颜色访问，确保状态显示在各种 jewelry 状态下都能正常工作
- ✅ **辅助方法优化**: 修复所有辅助方法中的颜色参数访问，使用 MaterialColor.shade 备用方案
- ✅ **空值传播防护**: 确保 jewelry 对象及其属性（category、store 等）的安全访问，已有适当的`?.`和`??`操作符
- ✅ **运行时稳定性**: 修复后的代码在各种数据状态和设备环境下都能稳定运行，不再出现空值错误

### 2024-12-19 库存查询页面弹窗优化

- ✅ **弹窗设计升级**: 将查看详情和编辑功能从全屏页面改为弹窗形式，提升用户体验
- ✅ **响应式弹窗**: 移动端占屏幕 80-90%宽度，桌面端固定合适宽度，适配不同设备
- ✅ **交互体验优化**: 支持 ESC 键关闭、点击遮罩关闭、自动刷新列表等便捷操作
- ✅ **详情弹窗功能**: 美观的商品信息展示，集成编辑和删除操作按钮
- ✅ **编辑弹窗功能**: 紧凑的表单布局，完整的验证机制，智能的取消确认
- ✅ **删除功能增强**: 详细的确认信息展示，加载状态提示，完善的错误处理
- ✅ **UI 风格统一**: 所有弹窗使用 AppBorderStyles 组件，保持项目整体视觉一致性

### 2024-12-19 库存查询页面功能增强

- ✅ **表格边框优化**: 为 DataTable 添加统一列边框线，提高数据可读性
- ✅ **查看详情功能**: 创建完整的商品详情页面，支持响应式布局
- ✅ **编辑功能实现**: 添加商品编辑页面和控制器，支持完整的表单验证
- ✅ **删除功能实现**: 实现删除确认对话框和 API 调用，包含完整错误处理
- ✅ **服务层完善**: 创建 CategoryService，完善 JewelryService 的 CRUD 方法
- ✅ **权限控制**: 所有功能按钮都集成了权限检查机制
- ✅ **UI 一致性**: 所有新增页面都使用 AppBorderStyles 组件保持视觉统一

### 2024-12-19 库存查询功能完整实现

- ✅ 完善后端库存查询 API 接口，支持分页和多条件筛选
- ✅ 实现 Flutter 前端库存查询页面，遵循统一 UI 规范
- ✅ 集成权限控制系统，支持门店级别权限管理
- ✅ 添加响应式布局，适配不同屏幕尺寸
- ✅ 实现完整的 CRUD 操作和数据缓存机制

---

⭐ 如果这个项目对您有帮助，请给它一个星标！
