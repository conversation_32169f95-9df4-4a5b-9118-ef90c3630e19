import 'package:intl/intl.dart';

/// 日期工具类
class DateUtil {
  /// Unix时间戳(秒)转DateTime
  static DateTime? fromUnixTimestamp(int? timestamp) {
    if (timestamp == null) return null;
    return DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
  }
  
  /// DateTime转Unix时间戳(秒)
  static int? toUnixTimestamp(DateTime? dateTime) {
    if (dateTime == null) return null;
    return dateTime.millisecondsSinceEpoch ~/ 1000;
  }
  
  /// 格式化日期时间
  static String formatDateTime(DateTime? dateTime, {String format = 'yyyy-MM-dd HH:mm:ss'}) {
    if (dateTime == null) return '';
    return DateFormat(format).format(dateTime);
  }
  
  /// 格式化日期
  static String formatDate(DateTime? dateTime, {String format = 'yyyy-MM-dd'}) {
    if (dateTime == null) return '';
    return DateFormat(format).format(dateTime);
  }
  
  /// 格式化时间
  static String formatTime(DateTime? dateTime, {String format = 'HH:mm:ss'}) {
    if (dateTime == null) return '';
    return DateFormat(format).format(dateTime);
  }
} 