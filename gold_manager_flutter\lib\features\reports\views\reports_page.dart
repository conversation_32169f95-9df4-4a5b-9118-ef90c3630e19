import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../widgets/loading_widget.dart';
import '../controllers/reports_controller.dart';
import 'sales_report_page.dart';
import 'inventory_report_page.dart';
import 'recycling_report_page.dart';

/// 报表统计主页
class ReportsPage extends StatelessWidget {
  const ReportsPage({super.key});

  @override
  Widget build(BuildContext context) {
    // 获取控制器
    final controller = Get.find<ReportsController>();
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('报表统计'),
        bottom: TabBar(
          controller: controller.tabController,
          tabs: const [
            Tab(text: '销售报表'),
            Tab(text: '库存报表'),
            Tab(text: '回收报表'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.calendar_today),
            tooltip: '选择日期范围',
            onPressed: () => _showDateRangePicker(context, controller),
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            tooltip: '筛选',
            onPressed: () => _showFilterDialog(context, controller),
          ),
          IconButton(
            icon: const Icon(Icons.download),
            tooltip: '导出报表',
            onPressed: () => _showExportDialog(context, controller),
          ),
        ],
      ),
      body: Obx(() {
        return controller.isLoading.value
            ? const LoadingWidget()
            : TabBarView(
                controller: controller.tabController,
                children: const [
                  SalesReportPage(),
                  InventoryReportPage(),
                  RecyclingReportPage(),
                ],
              );
      }),
    );
  }
  
  /// 显示日期范围选择器
  Future<void> _showDateRangePicker(
    BuildContext context,
    ReportsController controller,
  ) async {
    final initialDateRange = controller.dateRange.value;
    final pickedDateRange = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: initialDateRange,
      saveText: '确定',
      cancelText: '取消',
      confirmText: '确定',
      errorFormatText: '日期格式错误',
      errorInvalidText: '日期无效',
      fieldStartHintText: '开始日期',
      fieldEndHintText: '结束日期',
      helpText: '选择日期范围',
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppColors.primary,
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );
    
    if (pickedDateRange != null) {
      await controller.setDateRange(pickedDateRange);
    }
  }
  
  /// 显示筛选对话框
  Future<void> _showFilterDialog(
    BuildContext context,
    ReportsController controller,
  ) async {
    // 获取当前筛选条件
    int currentStoreId = controller.storeFilter.value;
    int currentCategoryId = controller.categoryFilter.value;
    
    // 门店列表
    final storeOptions = [
      {'id': 0, 'name': '全部门店'},
      {'id': 1, 'name': '总店'},
      {'id': 2, 'name': '分店一'},
      {'id': 3, 'name': '分店二'},
    ];
    
    // 分类列表
    final categoryOptions = [
      {'id': 0, 'name': '全部分类'},
      {'id': 1, 'name': '黄金首饰'},
      {'id': 2, 'name': '银饰'},
      {'id': 3, 'name': '金银混合'},
      {'id': 4, 'name': '其他'},
    ];
    
    await showDialog(
      context: context,
      builder: (context) {
        // 临时值
        int tempStoreId = currentStoreId;
        int tempCategoryId = currentCategoryId;
        
        return AlertDialog(
          title: const Text('筛选条件'),
          content: SizedBox(
            width: 400,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('选择门店'),
                const SizedBox(height: 8),
                StatefulBuilder(
                  builder: (context, setState) {
                    return DropdownButtonFormField<int>(
                      value: tempStoreId,
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      items: storeOptions
                          .map((store) => DropdownMenuItem<int>(
                                value: store['id'] as int,
                                child: Text(store['name'] as String),
                              ))
                          .toList(),
                      onChanged: (value) {
                        setState(() {
                          tempStoreId = value!;
                        });
                      },
                    );
                  },
                ),
                const SizedBox(height: 16),
                const Text('选择分类'),
                const SizedBox(height: 8),
                StatefulBuilder(
                  builder: (context, setState) {
                    return DropdownButtonFormField<int>(
                      value: tempCategoryId,
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      items: categoryOptions
                          .map((category) => DropdownMenuItem<int>(
                                value: category['id'] as int,
                                child: Text(category['name'] as String),
                              ))
                          .toList(),
                      onChanged: (value) {
                        setState(() {
                          tempCategoryId = value!;
                        });
                      },
                    );
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () async {
                Navigator.pop(context);
                
                // 应用筛选条件
                if (tempStoreId != currentStoreId) {
                  await controller.setStoreFilter(tempStoreId);
                }
                
                if (tempCategoryId != currentCategoryId) {
                  await controller.setCategoryFilter(tempCategoryId);
                }
              },
              child: const Text('应用'),
            ),
          ],
        );
      },
    );
  }
  
  /// 显示导出对话框
  Future<void> _showExportDialog(
    BuildContext context,
    ReportsController controller,
  ) async {
    final reportTypes = [
      {'type': 'sales', 'name': '销售报表'},
      {'type': 'inventory', 'name': '库存报表'},
      {'type': 'recycling', 'name': '回收报表'},
    ];
    
    String selectedType = reportTypes[controller.tabController.index]['type'] as String;
    
    await showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('导出报表'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('选择报表类型'),
              const SizedBox(height: 8),
              SizedBox(
                width: 300,
                child: StatefulBuilder(
                  builder: (context, setState) {
                    return DropdownButtonFormField<String>(
                      value: selectedType,
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      items: reportTypes
                          .map((report) => DropdownMenuItem<String>(
                                value: report['type'] as String,
                                child: Text(report['name'] as String),
                              ))
                          .toList(),
                      onChanged: (value) {
                        setState(() {
                          selectedType = value!;
                        });
                      },
                    );
                  },
                ),
              ),
              const SizedBox(height: 16),
              Text(
                '导出时间范围: ${DateFormat('yyyy-MM-dd').format(controller.dateRange.value.start)} 至 ${DateFormat('yyyy-MM-dd').format(controller.dateRange.value.end)}',
                style: AppTextStyles.body2,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () async {
                Navigator.pop(context);
                
                // 导出报表
                final success = await controller.exportReport(selectedType);
                
                // 如果导出失败，显示一条消息
                if (!success) {
                  Get.snackbar(
                    '导出失败',
                    '报表导出失败，请稍后重试',
                    snackPosition: SnackPosition.BOTTOM,
                  );
                }
              },
              child: const Text('导出'),
            ),
          ],
        );
      },
    );
  }
} 