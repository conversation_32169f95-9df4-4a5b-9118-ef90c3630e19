/// 系统设置数据模型
class SystemSettings {
  /// 基本配置
  final GeneralSettings generalSettings;

  /// 价格配置
  final PriceSettings priceSettings;

  /// 显示配置
  final DisplaySettings displaySettings;

  /// 数据同步配置
  final SyncSettings syncSettings;

  /// 备份配置
  final BackupSettings backupSettings;

  /// 构造函数
  SystemSettings({
    required this.generalSettings,
    required this.priceSettings,
    required this.displaySettings,
    required this.syncSettings,
    required this.backupSettings,
  });

  /// 从JSON创建
  factory SystemSettings.fromJson(Map<String, dynamic> json) {
    return SystemSettings(
      generalSettings: GeneralSettings.fromJson(json['general_settings'] ?? {}),
      priceSettings: PriceSettings.fromJson(json['price_settings'] ?? {}),
      displaySettings: DisplaySettings.fromJson(json['display_settings'] ?? {}),
      syncSettings: SyncSettings.fromJson(json['sync_settings'] ?? {}),
      backupSettings: BackupSettings.fromJson(json['backup_settings'] ?? {}),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'general_settings': generalSettings.toJson(),
      'price_settings': priceSettings.toJson(),
      'display_settings': displaySettings.toJson(),
      'sync_settings': syncSettings.toJson(),
      'backup_settings': backupSettings.toJson(),
    };
  }

  /// 创建默认设置
  factory SystemSettings.defaultSettings() {
    return SystemSettings(
      generalSettings: GeneralSettings.defaultSettings(),
      priceSettings: PriceSettings.defaultSettings(),
      displaySettings: DisplaySettings.defaultSettings(),
      syncSettings: SyncSettings.defaultSettings(),
      backupSettings: BackupSettings.defaultSettings(),
    );
  }

  /// 深拷贝
  SystemSettings copyWith({
    GeneralSettings? generalSettings,
    PriceSettings? priceSettings,
    DisplaySettings? displaySettings,
    SyncSettings? syncSettings,
    BackupSettings? backupSettings,
  }) {
    return SystemSettings(
      generalSettings: generalSettings ?? this.generalSettings,
      priceSettings: priceSettings ?? this.priceSettings,
      displaySettings: displaySettings ?? this.displaySettings,
      syncSettings: syncSettings ?? this.syncSettings,
      backupSettings: backupSettings ?? this.backupSettings,
    );
  }
}

/// 基本配置
class GeneralSettings {
  /// 默认门店ID
  final int defaultStoreId;

  /// 启用离线模式
  final bool enableOfflineMode;

  /// 默认语言
  final String language;

  /// 默认页面大小
  final int pageSize;

  /// 构造函数
  GeneralSettings({
    required this.defaultStoreId,
    required this.enableOfflineMode,
    required this.language,
    required this.pageSize,
  });

  /// 从JSON创建
  factory GeneralSettings.fromJson(Map<String, dynamic> json) {
    return GeneralSettings(
      defaultStoreId: json['default_store_id'] ?? 0,
      enableOfflineMode: json['enable_offline_mode'] ?? true,
      language: json['language'] ?? 'zh_CN',
      pageSize: json['page_size'] ?? 20,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'default_store_id': defaultStoreId,
      'enable_offline_mode': enableOfflineMode,
      'language': language,
      'page_size': pageSize,
    };
  }

  /// 默认设置
  factory GeneralSettings.defaultSettings() {
    return GeneralSettings(
      defaultStoreId: 1,
      enableOfflineMode: true,
      language: 'zh_CN',
      pageSize: 20,
    );
  }

  /// 深拷贝
  GeneralSettings copyWith({
    int? defaultStoreId,
    bool? enableOfflineMode,
    String? language,
    int? pageSize,
  }) {
    return GeneralSettings(
      defaultStoreId: defaultStoreId ?? this.defaultStoreId,
      enableOfflineMode: enableOfflineMode ?? this.enableOfflineMode,
      language: language ?? this.language,
      pageSize: pageSize ?? this.pageSize,
    );
  }
}

/// 价格配置
class PriceSettings {
  /// 默认金价(元/克)
  final double defaultGoldPrice;

  /// 默认银价(元/克)
  final double defaultSilverPrice;

  /// 工费计算方式 (0: 按比例, 1: 按重量, 2: 固定值)
  final int workingFeeType;

  /// 默认工费比例(%)
  final double defaultWorkingFeeRate;

  /// 默认工费(按克计算)(元/克)
  final double defaultWorkingFeePerGram;

  /// 价格预警
  final bool enablePriceAlert;

  /// 构造函数
  PriceSettings({
    required this.defaultGoldPrice,
    required this.defaultSilverPrice,
    required this.workingFeeType,
    required this.defaultWorkingFeeRate,
    required this.defaultWorkingFeePerGram,
    required this.enablePriceAlert,
  });

  /// 从JSON创建
  factory PriceSettings.fromJson(Map<String, dynamic> json) {
    return PriceSettings(
      defaultGoldPrice: (json['default_gold_price'] as num?)?.toDouble() ?? 380.0,
      defaultSilverPrice: (json['default_silver_price'] as num?)?.toDouble() ?? 6.0,
      workingFeeType: json['working_fee_type'] ?? 0,
      defaultWorkingFeeRate: (json['default_working_fee_rate'] as num?)?.toDouble() ?? 10.0,
      defaultWorkingFeePerGram: (json['default_working_fee_per_gram'] as num?)?.toDouble() ?? 15.0,
      enablePriceAlert: json['enable_price_alert'] ?? true,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'default_gold_price': defaultGoldPrice,
      'default_silver_price': defaultSilverPrice,
      'working_fee_type': workingFeeType,
      'default_working_fee_rate': defaultWorkingFeeRate,
      'default_working_fee_per_gram': defaultWorkingFeePerGram,
      'enable_price_alert': enablePriceAlert,
    };
  }

  /// 默认设置
  factory PriceSettings.defaultSettings() {
    return PriceSettings(
      defaultGoldPrice: 380.0,
      defaultSilverPrice: 6.0,
      workingFeeType: 0,
      defaultWorkingFeeRate: 10.0,
      defaultWorkingFeePerGram: 15.0,
      enablePriceAlert: true,
    );
  }

  /// 深拷贝
  PriceSettings copyWith({
    double? defaultGoldPrice,
    double? defaultSilverPrice,
    int? workingFeeType,
    double? defaultWorkingFeeRate,
    double? defaultWorkingFeePerGram,
    bool? enablePriceAlert,
  }) {
    return PriceSettings(
      defaultGoldPrice: defaultGoldPrice ?? this.defaultGoldPrice,
      defaultSilverPrice: defaultSilverPrice ?? this.defaultSilverPrice,
      workingFeeType: workingFeeType ?? this.workingFeeType,
      defaultWorkingFeeRate: defaultWorkingFeeRate ?? this.defaultWorkingFeeRate,
      defaultWorkingFeePerGram: defaultWorkingFeePerGram ?? this.defaultWorkingFeePerGram,
      enablePriceAlert: enablePriceAlert ?? this.enablePriceAlert,
    );
  }
}

/// 显示配置
class DisplaySettings {
  /// 主题模式 (0: 跟随系统, 1: 深色, 2: 浅色)
  final int themeMode;

  /// 字体大小调整
  final double fontSizeScale;

  /// 表格密度
  final int tableDensity;

  /// 显示库存预警
  final bool showStockAlert;

  /// 启用声音提示
  final bool enableSoundNotification;

  /// 构造函数
  DisplaySettings({
    required this.themeMode,
    required this.fontSizeScale,
    required this.tableDensity,
    required this.showStockAlert,
    required this.enableSoundNotification,
  });

  /// 从JSON创建
  factory DisplaySettings.fromJson(Map<String, dynamic> json) {
    return DisplaySettings(
      themeMode: json['theme_mode'] ?? 0,
      fontSizeScale: (json['font_size_scale'] as num?)?.toDouble() ?? 1.0,
      tableDensity: json['table_density'] ?? 1,
      showStockAlert: json['show_stock_alert'] ?? true,
      enableSoundNotification: json['enable_sound_notification'] ?? true,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'theme_mode': themeMode,
      'font_size_scale': fontSizeScale,
      'table_density': tableDensity,
      'show_stock_alert': showStockAlert,
      'enable_sound_notification': enableSoundNotification,
    };
  }

  /// 默认设置
  factory DisplaySettings.defaultSettings() {
    return DisplaySettings(
      themeMode: 0,
      fontSizeScale: 1.0,
      tableDensity: 1,
      showStockAlert: true,
      enableSoundNotification: true,
    );
  }

  /// 深拷贝
  DisplaySettings copyWith({
    int? themeMode,
    double? fontSizeScale,
    int? tableDensity,
    bool? showStockAlert,
    bool? enableSoundNotification,
  }) {
    return DisplaySettings(
      themeMode: themeMode ?? this.themeMode,
      fontSizeScale: fontSizeScale ?? this.fontSizeScale,
      tableDensity: tableDensity ?? this.tableDensity,
      showStockAlert: showStockAlert ?? this.showStockAlert,
      enableSoundNotification: enableSoundNotification ?? this.enableSoundNotification,
    );
  }
}

/// 数据同步配置
class SyncSettings {
  /// 启用自动同步
  final bool enableAutoSync;

  /// 自动同步间隔(分钟)
  final int syncInterval;

  /// 仅在WiFi下同步
  final bool syncOnlyOnWifi;

  /// 同步进行中通知
  final bool notifyOnSync;

  /// 构造函数
  SyncSettings({
    required this.enableAutoSync,
    required this.syncInterval,
    required this.syncOnlyOnWifi,
    required this.notifyOnSync,
  });

  /// 从JSON创建
  factory SyncSettings.fromJson(Map<String, dynamic> json) {
    return SyncSettings(
      enableAutoSync: json['enable_auto_sync'] ?? true,
      syncInterval: json['sync_interval'] ?? 30,
      syncOnlyOnWifi: json['sync_only_on_wifi'] ?? true,
      notifyOnSync: json['notify_on_sync'] ?? true,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'enable_auto_sync': enableAutoSync,
      'sync_interval': syncInterval,
      'sync_only_on_wifi': syncOnlyOnWifi,
      'notify_on_sync': notifyOnSync,
    };
  }

  /// 默认设置
  factory SyncSettings.defaultSettings() {
    return SyncSettings(
      enableAutoSync: true,
      syncInterval: 30,
      syncOnlyOnWifi: true,
      notifyOnSync: true,
    );
  }

  /// 深拷贝
  SyncSettings copyWith({
    bool? enableAutoSync,
    int? syncInterval,
    bool? syncOnlyOnWifi,
    bool? notifyOnSync,
  }) {
    return SyncSettings(
      enableAutoSync: enableAutoSync ?? this.enableAutoSync,
      syncInterval: syncInterval ?? this.syncInterval,
      syncOnlyOnWifi: syncOnlyOnWifi ?? this.syncOnlyOnWifi,
      notifyOnSync: notifyOnSync ?? this.notifyOnSync,
    );
  }
}

/// 备份配置
class BackupSettings {
  /// 启用自动备份
  final bool enableAutoBackup;

  /// 自动备份间隔(天)
  final int backupInterval;

  /// 保留备份数量
  final int keepBackupCount;

  /// 备份包含图片
  final bool includeImages;

  /// 备份位置路径
  final String backupPath;

  /// 构造函数
  BackupSettings({
    required this.enableAutoBackup,
    required this.backupInterval,
    required this.keepBackupCount,
    required this.includeImages,
    required this.backupPath,
  });

  /// 从JSON创建
  factory BackupSettings.fromJson(Map<String, dynamic> json) {
    return BackupSettings(
      enableAutoBackup: json['enable_auto_backup'] ?? true,
      backupInterval: json['backup_interval'] ?? 7,
      keepBackupCount: json['keep_backup_count'] ?? 5,
      includeImages: json['include_images'] ?? true,
      backupPath: json['backup_path'] ?? '',
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'enable_auto_backup': enableAutoBackup,
      'backup_interval': backupInterval,
      'keep_backup_count': keepBackupCount,
      'include_images': includeImages,
      'backup_path': backupPath,
    };
  }

  /// 默认设置
  factory BackupSettings.defaultSettings() {
    return BackupSettings(
      enableAutoBackup: true,
      backupInterval: 7,
      keepBackupCount: 5,
      includeImages: true,
      backupPath: '',
    );
  }

  /// 深拷贝
  BackupSettings copyWith({
    bool? enableAutoBackup,
    int? backupInterval,
    int? keepBackupCount,
    bool? includeImages,
    String? backupPath,
  }) {
    return BackupSettings(
      enableAutoBackup: enableAutoBackup ?? this.enableAutoBackup,
      backupInterval: backupInterval ?? this.backupInterval,
      keepBackupCount: keepBackupCount ?? this.keepBackupCount,
      includeImages: includeImages ?? this.includeImages,
      backupPath: backupPath ?? this.backupPath,
    );
  }
}

/// 用户模型
class User {
  /// 用户ID
  final int id;
  
  /// 用户名
  final String username;
  
  /// 显示名称
  final String displayName;
  
  /// 角色ID
  final int roleId;
  
  /// 角色名称
  final String roleName;
  
  /// 所属门店ID
  final int storeId;
  
  /// 所属门店名称
  final String storeName;
  
  /// 电子邮件
  final String email;
  
  /// 手机号
  final String phone;
  
  /// 头像URL
  final String avatar;
  
  /// 状态(0:禁用, 1:启用)
  final int status;
  
  /// 创建时间
  final DateTime createdAt;
  
  /// 最后登录时间
  final DateTime? lastLoginAt;
  
  /// 构造函数
  User({
    required this.id,
    required this.username,
    required this.displayName,
    required this.roleId,
    required this.roleName,
    required this.storeId,
    required this.storeName,
    required this.email,
    required this.phone,
    required this.avatar,
    required this.status,
    required this.createdAt,
    this.lastLoginAt,
  });
  
  /// 从JSON创建
  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] as int,
      username: json['username'] as String,
      displayName: json['display_name'] as String,
      roleId: json['role_id'] as int,
      roleName: json['role_name'] as String,
      storeId: json['store_id'] as int,
      storeName: json['store_name'] as String,
      email: json['email'] as String,
      phone: json['phone'] as String,
      avatar: json['avatar'] as String,
      status: json['status'] as int,
      createdAt: DateTime.parse(json['created_at'] as String),
      lastLoginAt: json['last_login_at'] != null 
          ? DateTime.parse(json['last_login_at'] as String) 
          : null,
    );
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'display_name': displayName,
      'role_id': roleId,
      'role_name': roleName,
      'store_id': storeId,
      'store_name': storeName,
      'email': email,
      'phone': phone,
      'avatar': avatar,
      'status': status,
      'created_at': createdAt.toIso8601String(),
      'last_login_at': lastLoginAt?.toIso8601String(),
    };
  }
  
  /// 深拷贝
  User copyWith({
    int? id,
    String? username,
    String? displayName,
    int? roleId,
    String? roleName,
    int? storeId,
    String? storeName,
    String? email,
    String? phone,
    String? avatar,
    int? status,
    DateTime? createdAt,
    DateTime? lastLoginAt,
  }) {
    return User(
      id: id ?? this.id,
      username: username ?? this.username,
      displayName: displayName ?? this.displayName,
      roleId: roleId ?? this.roleId,
      roleName: roleName ?? this.roleName,
      storeId: storeId ?? this.storeId,
      storeName: storeName ?? this.storeName,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      avatar: avatar ?? this.avatar,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
    );
  }
}

/// 角色模型
class Role {
  /// 角色ID
  final int id;
  
  /// 角色名称
  final String name;
  
  /// 角色描述
  final String description;
  
  /// 权限列表
  final List<Permission> permissions;
  
  /// 是否系统内置角色
  final bool isSystemRole;
  
  /// 构造函数
  Role({
    required this.id,
    required this.name,
    required this.description,
    required this.permissions,
    required this.isSystemRole,
  });
  
  /// 从JSON创建
  factory Role.fromJson(Map<String, dynamic> json) {
    return Role(
      id: json['id'] as int,
      name: json['name'] as String,
      description: json['description'] as String,
      permissions: (json['permissions'] as List<dynamic>)
          .map((e) => Permission.fromJson(e as Map<String, dynamic>))
          .toList(),
      isSystemRole: json['is_system_role'] as bool,
    );
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'permissions': permissions.map((e) => e.toJson()).toList(),
      'is_system_role': isSystemRole,
    };
  }
}

/// 权限模型
class Permission {
  /// 权限ID
  final int id;
  
  /// 权限代码
  final String code;
  
  /// 权限名称
  final String name;
  
  /// 权限分组
  final String group;
  
  /// 构造函数
  Permission({
    required this.id,
    required this.code,
    required this.name,
    required this.group,
  });
  
  /// 从JSON创建
  factory Permission.fromJson(Map<String, dynamic> json) {
    return Permission(
      id: json['id'] as int,
      code: json['code'] as String,
      name: json['name'] as String,
      group: json['group'] as String,
    );
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'code': code,
      'name': name,
      'group': group,
    };
  }
}

/// 系统日志模型
class SystemLog {
  /// 日志ID
  final int id;
  
  /// 用户ID
  final int userId;
  
  /// 用户名
  final String username;
  
  /// 操作类型
  final String actionType;
  
  /// 操作内容
  final String content;
  
  /// 操作结果
  final bool result;
  
  /// IP地址
  final String ipAddress;
  
  /// 操作时间
  final DateTime createdAt;
  
  /// 构造函数
  SystemLog({
    required this.id,
    required this.userId,
    required this.username,
    required this.actionType,
    required this.content,
    required this.result,
    required this.ipAddress,
    required this.createdAt,
  });
  
  /// 从JSON创建
  factory SystemLog.fromJson(Map<String, dynamic> json) {
    return SystemLog(
      id: json['id'] as int,
      userId: json['user_id'] as int,
      username: json['username'] as String,
      actionType: json['action_type'] as String,
      content: json['content'] as String,
      result: json['result'] as bool,
      ipAddress: json['ip_address'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'username': username,
      'action_type': actionType,
      'content': content,
      'result': result,
      'ip_address': ipAddress,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

/// 备份记录模型
class BackupRecord {
  /// 备份ID
  final int id;
  
  /// 备份文件名称
  final String fileName;
  
  /// 备份文件路径
  final String filePath;
  
  /// 备份大小(字节)
  final int fileSize;
  
  /// 备份类型(0:手动, 1:自动)
  final int backupType;
  
  /// 备份状态(0:失败, 1:成功)
  final int status;
  
  /// 备份描述
  final String description;
  
  /// 创建用户ID
  final int userId;
  
  /// 创建用户名
  final String username;
  
  /// 创建时间
  final DateTime createdAt;
  
  /// 构造函数
  BackupRecord({
    required this.id,
    required this.fileName,
    required this.filePath,
    required this.fileSize,
    required this.backupType,
    required this.status,
    required this.description,
    required this.userId,
    required this.username,
    required this.createdAt,
  });
  
  /// 从JSON创建
  factory BackupRecord.fromJson(Map<String, dynamic> json) {
    return BackupRecord(
      id: json['id'] as int,
      fileName: json['file_name'] as String,
      filePath: json['file_path'] as String,
      fileSize: json['file_size'] as int,
      backupType: json['backup_type'] as int,
      status: json['status'] as int,
      description: json['description'] as String,
      userId: json['user_id'] as int,
      username: json['username'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'file_name': fileName,
      'file_path': filePath,
      'file_size': fileSize,
      'backup_type': backupType,
      'status': status,
      'description': description,
      'user_id': userId,
      'username': username,
      'created_at': createdAt.toIso8601String(),
    };
  }
} 