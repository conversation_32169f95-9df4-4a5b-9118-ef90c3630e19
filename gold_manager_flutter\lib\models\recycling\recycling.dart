import '../common/date_utils.dart';
import '../common/document_status.dart';
import '../store/store.dart';
import 'recycling_item.dart';

/// 回收单模型
class Recycling {
  final int id;
  final String recyclingNo;
  final String? customer; // 客户姓名
  final String? phone; // 客户电话
  final int storeId;
  final double totalAmount; // 总金额
  final double discountAmount; // 折后金额
  final int operatorId; // 操作员ID
  final String? remark; // 备注
  final DocumentStatus status; // 状态
  final DateTime? createTime;
  final DateTime? updateTime;

  // 关联对象
  final Store? store;
  final List<RecyclingItem>? items;

  const Recycling({
    required this.id,
    required this.recyclingNo,
    this.customer,
    this.phone,
    required this.storeId,
    this.totalAmount = 0.0,
    this.discountAmount = 0.0,
    required this.operatorId,
    this.remark,
    this.status = DocumentStatus.draft,
    this.createTime,
    this.updateTime,
    this.store,
    this.items,
  });

  /// 从JSON构造
  factory Recycling.fromJson(Map<String, dynamic> json) {
    // 安全的数字转换函数
    double parseDouble(dynamic value) {
      if (value == null) return 0.0;
      if (value is double) return value;
      if (value is int) return value.toDouble();
      if (value is String) {
        return double.tryParse(value) ?? 0.0;
      }
      return 0.0;
    }

    return Recycling(
      id: json['id'],
      recyclingNo: json['recycling_no'],
      customer: json['customer'],
      phone: json['phone'],
      storeId: json['store_id'],
      totalAmount: parseDouble(json['total_amount']),
      discountAmount: parseDouble(json['discount_amount']),
      operatorId: json['operator_id'],
      remark: json['remark'],
      status: DocumentStatus.fromValue(json['status'] ?? 0),
      createTime: DateUtil.fromUnixTimestamp(json['createtime']),
      updateTime: DateUtil.fromUnixTimestamp(json['updatetime']),
      // 关联对象
      store: json['store'] != null ? Store.fromJson(json['store']) : null,
      items: json['items'] != null
          ? (json['items'] as List)
                .map((e) => RecyclingItem.fromJson(e))
                .toList()
          : null,
    );
  }

  /// 转为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'recycling_no': recyclingNo,
      'customer': customer,
      'phone': phone,
      'store_id': storeId,
      'total_amount': totalAmount,
      'discount_amount': discountAmount,
      'operator_id': operatorId,
      'remark': remark,
      'status': status.value,
      'createtime': DateUtil.toUnixTimestamp(createTime),
      'updatetime': DateUtil.toUnixTimestamp(updateTime),
    };
  }

  /// 计算折扣率
  double get discountRate {
    if (totalAmount <= 0) return 0;
    return (discountAmount / totalAmount) * 100;
  }

  /// 计算实际金额
  double get actualAmount {
    return totalAmount - discountAmount;
  }

  /// 是否可编辑
  bool get canEdit =>
      status == DocumentStatus.draft || status == DocumentStatus.pending;

  /// 是否可删除
  bool get canDelete =>
      status == DocumentStatus.draft || status == DocumentStatus.pending;

  /// 是否可审核
  bool get canApprove => status == DocumentStatus.pending;

  /// 创建一个新实例，但使用部分属性
  Recycling copyWith({
    int? id,
    String? recyclingNo,
    String? customer,
    String? phone,
    int? storeId,
    double? totalAmount,
    double? discountAmount,
    int? operatorId,
    String? remark,
    DocumentStatus? status,
    DateTime? createTime,
    DateTime? updateTime,
    Store? store,
    List<RecyclingItem>? items,
  }) {
    return Recycling(
      id: id ?? this.id,
      recyclingNo: recyclingNo ?? this.recyclingNo,
      customer: customer ?? this.customer,
      phone: phone ?? this.phone,
      storeId: storeId ?? this.storeId,
      totalAmount: totalAmount ?? this.totalAmount,
      discountAmount: discountAmount ?? this.discountAmount,
      operatorId: operatorId ?? this.operatorId,
      remark: remark ?? this.remark,
      status: status ?? this.status,
      createTime: createTime ?? this.createTime,
      updateTime: updateTime ?? this.updateTime,
      store: store ?? this.store,
      items: items ?? this.items,
    );
  }
}
