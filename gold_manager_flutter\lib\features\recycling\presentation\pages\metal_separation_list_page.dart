import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../models/recycling/recycling_item.dart';
import '../../controllers/metal_separation_controller.dart';
import 'metal_separation_form_page.dart';

/// 金属分离记录列表页面
class MetalSeparationListPage extends StatelessWidget {
  final RecyclingItem recyclingItem;
  final MetalSeparationController controller;

  const MetalSeparationListPage({
    super.key,
    required this.recyclingItem,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('金属分离记录'),
        elevation: 0,
      ),
      body: Column(
        children: [
          _buildItemInfo(context),
          Expanded(
            child: _buildSeparationList(),
          ),
        ],
      ),
      // 移除FloatingActionButton，使用界面内的具体操作按钮
    );
  }

  Widget _buildItemInfo(BuildContext context) {
    // 计算总重量
    final totalWeight = recyclingItem.goldWeight + recyclingItem.silverWeight;

    return Container(
      color: AppColors.primary.withOpacity(0.05),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Card(
        margin: const EdgeInsets.only(bottom: 8),
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          '回收物品信息',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: AppColors.textPrimary,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            _buildInfoChip(
                              label: '金重',
                              value: '${recyclingItem.goldWeight}g',
                              color: AppColors.warning,
                            ),
                            const SizedBox(width: 8),
                            _buildInfoChip(
                              label: '银重',
                              value: '${recyclingItem.silverWeight}g',
                              color: AppColors.info,
                            ),
                            const SizedBox(width: 8),
                            _buildInfoChip(
                              label: '总重',
                              value: '${totalWeight}g',
                              color: AppColors.textSecondary,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        '回收价格: ¥${recyclingItem.amount.toStringAsFixed(2)}',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color: AppColors.primary,
                        ),
                      ),
                      const SizedBox(height: 8),
                      _buildStatusTag(recyclingItem),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoChip({required String label, required String value, required Color color}) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: RichText(
        text: TextSpan(
          children: [
            TextSpan(
              text: label,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.w500,
                fontSize: 12,
              ),
            ),
            TextSpan(
              text: ': $value',
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusTag(RecyclingItem item) {
    Color color;
    String text;

    switch (item.processType.value) {
      case 'pending':
        color = AppColors.warning;
        text = '待处理';
        break;
      case 'separate':
        color = AppColors.info;
        text = '金银分离';
        break;
      case 'sell':
        color = AppColors.success;
        text = '直接卖出';
        break;
      case 'repair':
        color = AppColors.primary;
        text = '维修再销售';
        break;
      case 'resell':
        color = AppColors.secondary;
        text = '直接二次销售';
        break;
      default:
        color = AppColors.textSecondary;
        text = '未知状态';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontWeight: FontWeight.w500,
          fontSize: 14,
        ),
      ),
    );
  }

  Widget _buildSeparationList() {
    return Obx(() {
      if (controller.isLoading.value) {
        return const Center(child: CircularProgressIndicator());
      }

      if (controller.separationRecords.isEmpty) {
        return _buildEmptyState();
      }

      return ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: controller.separationRecords.length,
        itemBuilder: (context, index) {
          final record = controller.separationRecords[index];
          return Card(
            margin: const EdgeInsets.only(bottom: 16),
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: InkWell(
              onTap: () => _editSeparationRecord(context, record.id!),
              borderRadius: BorderRadius.circular(12),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '分离记录 #${record.id}',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: _getStatusColor(record.status).withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            record.statusText,
                            style: TextStyle(
                              color: _getStatusColor(record.status),
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '操作日期: ${record.formattedDate}',
                      style: const TextStyle(
                        color: AppColors.textSecondary,
                        fontSize: 14,
                      ),
                    ),
                    const Divider(),
                    Row(
                      children: [
                        Expanded(
                          child: _buildInfoItem('原始重量', '${record.originalGoldWeight + record.originalSilverWeight}g'),
                        ),
                        Expanded(
                          child: _buildInfoItem('分离后重量', '${record.separatedGoldWeight + record.separatedSilverWeight}g'),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(
                          child: _buildInfoItem('损耗率', '${record.lossRate.toStringAsFixed(2)}%'),
                        ),
                        Expanded(
                          child: _buildInfoItem('分离成本', '¥${record.separationCost.toStringAsFixed(2)}'),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(
                          child: _buildInfoItem('分离后金值', '¥${record.separatedGoldValue.toStringAsFixed(2)}', color: AppColors.success),
                        ),
                        Expanded(
                          child: _buildInfoItem('分离后银值', '¥${record.separatedSilverValue.toStringAsFixed(2)}', color: AppColors.info),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    _buildInfoItem('总价值', '¥${record.totalValue.toStringAsFixed(2)}', isBold: true, color: AppColors.primary),

                    if (record.imageUrls != null && record.imageUrls!.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              '图片',
                              style: TextStyle(
                                fontWeight: FontWeight.w500,
                                color: AppColors.textSecondary,
                                fontSize: 12,
                              ),
                            ),
                            const SizedBox(height: 8),
                            _buildImagePreviews(context, record.imageUrls!),
                          ],
                        ),
                      ),

                    if (record.remark != null && record.remark!.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 8),
                        child: Text(
                          '备注: ${record.remark}',
                          style: const TextStyle(
                            color: AppColors.textSecondary,
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        TextButton.icon(
                          onPressed: () => _editSeparationRecord(context, record.id!),
                          icon: const Icon(Icons.edit, size: 16),
                          label: const Text('编辑'),
                        ),
                        TextButton.icon(
                          onPressed: () => _deleteSeparationRecord(context, record.id!),
                          icon: const Icon(Icons.delete, size: 16, color: AppColors.error),
                          label: const Text('删除', style: TextStyle(color: AppColors.error)),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      );
    });
  }

  Widget _buildImagePreviews(BuildContext context, List<String> images) {
    return SizedBox(
      height: 80,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: images.length,
        itemBuilder: (context, index) {
          return GestureDetector(
            onTap: () => _showImageGallery(context, images, index),
            child: Container(
              width: 80,
              height: 80,
              margin: const EdgeInsets.only(right: 8),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: AppColors.border),
              ),
              clipBehavior: Clip.antiAlias,
              child: CachedNetworkImage(
                imageUrl: images[index],
                fit: BoxFit.cover,
                placeholder: (context, url) => const Center(
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
                errorWidget: (context, url, error) => const Icon(
                  Icons.broken_image,
                  color: AppColors.error,
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  void _showImageGallery(BuildContext context, List<String> images, int initialIndex) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => Scaffold(
          backgroundColor: Colors.black,
          appBar: AppBar(
            backgroundColor: Colors.black,
            foregroundColor: Colors.white,
            title: Text('图片 ${initialIndex + 1}/${images.length}'),
          ),
          body: PhotoViewGallery.builder(
            itemCount: images.length,
            builder: (context, i) {
              return PhotoViewGalleryPageOptions(
                imageProvider: CachedNetworkImageProvider(images[i]),
                minScale: PhotoViewComputedScale.contained,
                maxScale: PhotoViewComputedScale.covered * 2,
              );
            },
            scrollPhysics: const BouncingScrollPhysics(),
            backgroundDecoration: const BoxDecoration(color: Colors.black),
            pageController: PageController(initialPage: initialIndex),
          ),
        ),
      ),
    );
  }

  Color _getStatusColor(int status) {
    switch (status) {
      case 0: return AppColors.warning;
      case 1: return AppColors.success;
      case 2: return AppColors.error;
      default: return AppColors.textSecondary;
    }
  }

  Widget _buildInfoItem(String label, String value, {Color color = AppColors.textPrimary, bool isBold = false}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            color: AppColors.textSecondary,
            fontSize: 12,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: TextStyle(
            color: color,
            fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
            fontSize: 14,
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        margin: const EdgeInsets.all(32),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                height: 100,
                width: 100,
                decoration: BoxDecoration(
                  color: AppColors.info.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.science_outlined,
                  size: 54,
                  color: AppColors.info,
                ),
              ),
              const SizedBox(height: 24),
              const Text(
                '暂无金属分离记录',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                '点击右下角添加按钮创建新的分离记录',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: AppColors.textSecondary,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: () => _navigateToFormPage(Get.context!),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                icon: const Icon(Icons.add),
                label: const Text('添加分离记录'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToFormPage(BuildContext context) async {
    controller.resetForm();

    // 预填充原始金重和银重
    controller.originalGoldWeight.value = recyclingItem.goldWeight;
    controller.originalSilverWeight.value = recyclingItem.silverWeight;

    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => MetalSeparationFormPage(controller: controller),
      ),
    );

    if (result == true) {
      controller.loadSeparationRecords();
    }
  }

  void _editSeparationRecord(BuildContext context, int id) async {
    await controller.loadSeparationDetail(id);

    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => MetalSeparationFormPage(controller: controller),
      ),
    );

    if (result == true) {
      controller.loadSeparationRecords();
    }
  }

  void _deleteSeparationRecord(BuildContext context, int id) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: const Text('删除后无法恢复，确定要删除该分离记录吗？'),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              final success = await controller.deleteSeparationRecord(id);
              if (success) {
                Get.snackbar(
                  '成功',
                  '分离记录已删除',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: AppColors.success.withOpacity(0.8),
                  colorText: Colors.white,
                );
              } else {
                Get.snackbar(
                  '失败',
                  '删除失败，请重试',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: AppColors.error.withOpacity(0.8),
                  colorText: Colors.white,
                );
              }
            },
            child: const Text('删除', style: TextStyle(color: AppColors.error)),
          ),
        ],
      ),
    );
  }
}