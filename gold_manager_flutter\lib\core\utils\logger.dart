import 'package:logger/logger.dart';

/// 日志工具类
class LoggerService {
  static final Logger _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 0,
      errorMethodCount: 8,
      lineLength: 120,
      colors: true,
      printEmojis: true,
      printTime: true,
    ),
  );

  /// 初始化日志服务
  static void init() {
    // 可以在此添加日志初始化配置
  }

  /// 日志级别
  static const String _info = '[INFO]';
  static const String _debug = '[DEBUG]';
  static const String _warning = '[WARN]';
  static const String _error = '[ERROR]';

  /// 记录信息日志
  static void i(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.i('$_info $message', error, stackTrace);
  }

  /// 记录调试日志
  static void d(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.d('$_debug $message', error, stackTrace);
  }

  /// 记录警告日志
  static void w(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.w('$_warning $message', error, stackTrace);
  }

  /// 记录错误日志
  static void e(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.e('$_error $message', error, stackTrace);
  }

  /// 严重错误日志
  static void fatal(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.wtf(message, error, stackTrace);
  }
  
  /// GetX日志适配器
  static void write(String text, {bool isError = false}) {
    if (isError) {
      e(text);
    } else {
      d(text);
    }
  }
  
  // 为了兼容我们新添加的模块中使用的Logger类
  
  /// 调试日志 (兼容版)
  static void debug(String message, [dynamic error, StackTrace? stackTrace]) {
    d(message, error, stackTrace);
  }

  /// 信息日志 (兼容版)
  static void info(String message, [dynamic error, StackTrace? stackTrace]) {
    i(message, error, stackTrace);
  }

  /// 警告日志 (兼容版)
  static void warning(String message, [dynamic error, StackTrace? stackTrace]) {
    w(message, error, stackTrace);
  }

  /// 错误日志 (兼容版)
  static void error(String message, [dynamic error, StackTrace? stackTrace]) {
    e(message, error, stackTrace);
  }
} 