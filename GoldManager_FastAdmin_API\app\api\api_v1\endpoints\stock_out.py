"""
出库管理API路由
提供出库单的CRUD操作、审核、收款和统计功能
"""

from typing import List
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from ....core.database import get_db
from ....core.dependencies import require_permission, require_permissions
from ....schemas.auth import CurrentUserResponse
from ....services.stock_out_service import StockOutService
from ....schemas.stock_out import (
    StockOutCreate, StockOutUpdate, StockOutResponse, StockOutQueryParams,
    StockOutAuditUpdate, StockOutPaymentUpdate, StockOutStatistics
)
from ....schemas.common import PaginatedResponse, StandardResponse

router = APIRouter()


@router.get("/", response_model=PaginatedResponse[StockOutResponse],
           summary="获取出库单列表",
           description="获取出库单列表，支持分页、搜索和筛选")
def get_stock_out_list(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    keyword: str = Query(None, description="关键词搜索(单号、客户、备注)"),
    store_id: int = Query(None, description="门店ID筛选"),
    status: int = Query(None, ge=1, le=4, description="状态筛选:1=待审核,2=已通过,3=未通过,4=已作废"),
    sale_type: str = Query(None, description="销售类型筛选:retail=零售,wholesale=批发"),
    payment_status: int = Query(None, ge=0, le=1, description="收款状态筛选:0=未收款,1=已收款"),
    start_date: str = Query(None, description="开始日期(YYYY-MM-DD)"),
    end_date: str = Query(None, description="结束日期(YYYY-MM-DD)"),
    operator_id: int = Query(None, description="操作员ID筛选"),
    customer: str = Query(None, description="客户筛选"),
    current_user: CurrentUserResponse = Depends(require_permission("stock.view")),
    db: Session = Depends(get_db)
):
    """获取出库单列表"""
    try:
        service = StockOutService(db)
        params = StockOutQueryParams(
            page=page,
            page_size=page_size,
            keyword=keyword,
            store_id=store_id,
            status=status,
            sale_type=sale_type,
            payment_status=payment_status,
            start_date=start_date,
            end_date=end_date,
            operator_id=operator_id,
            customer=customer
        )
        
        stock_outs, total = service.get_stock_out_list(params)
        
        return PaginatedResponse(
            success=True,
            message="获取出库单列表成功",
            data=[service.convert_stock_out_to_dict(stock_out) for stock_out in stock_outs],
            pagination={
                "page": page,
                "page_size": page_size,
                "total": total,
                "pages": (total + page_size - 1) // page_size
            }
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取出库单列表失败: {str(e)}")


@router.get("/{stock_out_id}", response_model=StockOutResponse,
           summary="获取出库单详情",
           description="根据ID获取出库单详情")
def get_stock_out_by_id(
    stock_out_id: int,
    current_user: CurrentUserResponse = Depends(require_permission("stock.view")),
    db: Session = Depends(get_db)
):
    """根据ID获取出库单详情"""
    try:
        service = StockOutService(db)
        stock_out = service.get_stock_out_by_id(stock_out_id)
        
        if not stock_out:
            raise HTTPException(status_code=404, detail="出库单不存在")
        
        stock_out_dict = service.convert_stock_out_to_dict(stock_out)
        return StockOutResponse.model_validate(stock_out_dict)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取出库单详情失败: {str(e)}")


@router.get("/by-no/{order_no}", response_model=StockOutResponse,
           summary="根据单号获取出库单详情",
           description="根据出库单号获取出库单详情")
def get_stock_out_by_no(
    order_no: str,
    current_user: CurrentUserResponse = Depends(require_permission("stock.view")),
    db: Session = Depends(get_db)
):
    """根据单号获取出库单详情"""
    try:
        service = StockOutService(db)
        stock_out = service.get_stock_out_by_no(order_no)
        
        if not stock_out:
            raise HTTPException(status_code=404, detail="出库单不存在")
        
        stock_out_dict = service.convert_stock_out_to_dict(stock_out)
        return StockOutResponse.model_validate(stock_out_dict)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取出库单详情失败: {str(e)}")


@router.post("/", response_model=StockOutResponse,
            summary="创建出库单",
            description="创建新的出库单")
def create_stock_out(
    stock_out_data: StockOutCreate,
    operator_id: int = Query(..., description="操作员ID"),
    current_user: CurrentUserResponse = Depends(require_permission("stock.create")),
    db: Session = Depends(get_db)
):
    """创建出库单"""
    try:
        service = StockOutService(db)
        stock_out = service.create_stock_out(stock_out_data, operator_id)
        
        stock_out_dict = service.convert_stock_out_to_dict(stock_out)
        return StockOutResponse.model_validate(stock_out_dict)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建出库单失败: {str(e)}")


@router.put("/{stock_out_id}", response_model=StockOutResponse,
           summary="更新出库单",
           description="更新出库单信息(只有待审核状态才能修改)")
def update_stock_out(
    stock_out_id: int,
    stock_out_data: StockOutUpdate,
    db: Session = Depends(get_db)
):
    """更新出库单"""
    try:
        service = StockOutService(db)
        stock_out = service.update_stock_out(stock_out_id, stock_out_data)
        
        if not stock_out:
            raise HTTPException(status_code=404, detail="出库单不存在")
        
        stock_out_dict = service.convert_stock_out_to_dict(stock_out)
        return StockOutResponse.model_validate(stock_out_dict)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新出库单失败: {str(e)}")


@router.delete("/{stock_out_id}", response_model=StandardResponse[str],
              summary="删除出库单",
              description="删除出库单(只有待审核状态才能删除)")
def delete_stock_out(
    stock_out_id: int,
    db: Session = Depends(get_db)
):
    """删除出库单"""
    try:
        service = StockOutService(db)
        success = service.delete_stock_out(stock_out_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="出库单不存在")
        
        return StandardResponse(
            success=True,
            message="出库单删除成功",
            data="出库单删除成功",
            code=200
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除出库单失败: {str(e)}")


@router.patch("/{stock_out_id}/audit", response_model=StockOutResponse,
             summary="审核出库单",
             description="审核出库单(通过/拒绝/作废)")
def audit_stock_out(
    stock_out_id: int,
    audit_data: StockOutAuditUpdate,
    auditor_id: int = Query(..., description="审核员ID"),
    db: Session = Depends(get_db)
):
    """审核出库单"""
    try:
        service = StockOutService(db)
        stock_out = service.audit_stock_out(stock_out_id, audit_data, auditor_id)
        
        if not stock_out:
            raise HTTPException(status_code=404, detail="出库单不存在")
        
        stock_out_dict = service.convert_stock_out_to_dict(stock_out)
        return StockOutResponse.model_validate(stock_out_dict)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"审核出库单失败: {str(e)}")


@router.patch("/{stock_out_id}/cancel-audit", response_model=StockOutResponse,
             summary="取消审核",
             description="取消出库单审核，将已审核的出库单重新设置为待审核状态")
def cancel_audit_stock_out(
    stock_out_id: int,
    current_user: CurrentUserResponse = Depends(require_permission("stock.audit")),
    db: Session = Depends(get_db)
):
    """取消审核出库单"""
    try:
        service = StockOutService(db)
        stock_out = service.cancel_audit_stock_out(stock_out_id)
        
        if not stock_out:
            raise HTTPException(status_code=404, detail="出库单不存在")
        
        stock_out_dict = service.convert_stock_out_to_dict(stock_out)
        return StockOutResponse.model_validate(stock_out_dict)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"取消审核失败: {str(e)}")


@router.patch("/{stock_out_id}/payment", response_model=StockOutResponse,
             summary="更新收款信息",
             description="更新出库单收款信息")
def update_payment(
    stock_out_id: int,
    payment_data: StockOutPaymentUpdate,
    db: Session = Depends(get_db)
):
    """更新收款信息"""
    try:
        service = StockOutService(db)
        stock_out = service.update_payment(stock_out_id, payment_data)
        
        if not stock_out:
            raise HTTPException(status_code=404, detail="出库单不存在")
        
        stock_out_dict = service.convert_stock_out_to_dict(stock_out)
        return StockOutResponse.model_validate(stock_out_dict)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新收款信息失败: {str(e)}")


@router.get("/statistics/summary", response_model=StockOutStatistics,
           summary="获取出库单统计信息",
           description="获取出库单的统计信息，包括状态分布、门店分布、销售类型分布等")
def get_stock_out_statistics(
    db: Session = Depends(get_db)
):
    """获取出库单统计信息"""
    try:
        service = StockOutService(db)
        return service.get_stock_out_statistics()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}") 