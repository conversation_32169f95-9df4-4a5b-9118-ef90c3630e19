import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../core/services/storage_service.dart';
import '../../../../core/utils/logger_service.dart';
import '../../../../models/recycling/recycling_model.dart';
import '../../controllers/recycling_controller.dart';
import '../../services/recycling_service.dart';
import 'recycling_detail_dialog.dart';

/// 旧料回收详情数据显示问题诊断页面
class DiagnosisPage extends StatefulWidget {
  const DiagnosisPage({super.key});

  @override
  State<DiagnosisPage> createState() => _DiagnosisPageState();
}

class _DiagnosisPageState extends State<DiagnosisPage> {
  final List<String> _logs = [];
  bool _isRunning = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('旧料回收详情诊断'),
        backgroundColor: Colors.purple[600],
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          // 控制面板
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.grey[100],
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        icon: const Icon(Icons.play_arrow),
                        label: const Text('开始完整诊断'),
                        onPressed: _isRunning ? null : _runFullDiagnosis,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton.icon(
                        icon: const Icon(Icons.clear),
                        label: const Text('清空日志'),
                        onPressed: _clearLogs,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        icon: const Icon(Icons.security),
                        label: const Text('检查认证'),
                        onPressed: _isRunning ? null : _checkAuth,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton.icon(
                        icon: const Icon(Icons.api),
                        label: const Text('测试API'),
                        onPressed: _isRunning ? null : _testApi,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.purple,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          // 日志显示区域
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '诊断日志 (${_logs.length}条)',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Expanded(
                    child: Container(
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey[300]!),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: ListView.builder(
                        itemCount: _logs.length,
                        itemBuilder: (context, index) {
                          final log = _logs[index];
                          Color textColor = Colors.black;
                          if (log.contains('❌') || log.contains('ERROR')) {
                            textColor = Colors.red;
                          } else if (log.contains('⚠️') || log.contains('WARN')) {
                            textColor = Colors.orange;
                          } else if (log.contains('✅') || log.contains('SUCCESS')) {
                            textColor = Colors.green;
                          } else if (log.contains('🔍') || log.contains('INFO')) {
                            textColor = Colors.blue;
                          }
                          
                          return Padding(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 2,
                            ),
                            child: Text(
                              log,
                              style: TextStyle(
                                fontSize: 12,
                                fontFamily: 'monospace',
                                color: textColor,
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 添加日志
  void _addLog(String message) {
    setState(() {
      final timestamp = DateTime.now().toString().substring(11, 19);
      _logs.add('[$timestamp] $message');
    });
    
    // 自动滚动到底部
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        // 这里可以添加自动滚动逻辑
      }
    });
  }

  /// 清空日志
  void _clearLogs() {
    setState(() {
      _logs.clear();
    });
  }

  /// 运行完整诊断
  void _runFullDiagnosis() async {
    setState(() {
      _isRunning = true;
    });

    _addLog('🚀 开始完整诊断流程');
    
    try {
      // 1. 检查认证状态
      await _checkAuth();
      
      // 2. 测试API调用
      await _testApi();
      
      // 3. 测试数据解析
      await _testDataParsing();
      
      // 4. 测试完整流程
      await _testFullFlow();
      
      _addLog('✅ 完整诊断流程结束');
      
    } catch (e) {
      _addLog('❌ 诊断过程中发生异常: $e');
    } finally {
      setState(() {
        _isRunning = false;
      });
    }
  }

  /// 检查认证状态
  Future<void> _checkAuth() async {
    _addLog('🔍 检查认证状态...');
    
    try {
      final storageService = Get.find<StorageService>();
      final token = storageService.getToken();
      final userId = storageService.getUserId();
      final userName = storageService.getUserName();
      final storeId = storageService.getStoreId();
      
      if (token != null && token.isNotEmpty) {
        _addLog('✅ JWT Token存在: ${token.substring(0, 20)}...');
        _addLog('👤 用户信息: $userName (ID: $userId)');
        _addLog('🏪 门店信息: ID $storeId');
      } else {
        _addLog('❌ JWT Token不存在或为空');
      }
    } catch (e) {
      _addLog('❌ 检查认证状态失败: $e');
    }
  }

  /// 测试API调用
  Future<void> _testApi() async {
    _addLog('🔍 测试API调用...');
    
    try {
      final recyclingService = RecyclingService();
      
      _addLog('🚀 调用getRecyclingOrderDetail(1)');
      final order = await recyclingService.getRecyclingOrderDetail(1);
      
      _addLog('✅ API调用成功');
      _addLog('📦 回收单号: ${order.orderNo}');
      _addLog('👤 客户姓名: ${order.customerName}');
      _addLog('💰 总金额: ${order.totalAmount}');
      _addLog('📦 明细数量: ${order.items.length}');
      
      if (order.items.isNotEmpty) {
        for (int i = 0; i < order.items.length; i++) {
          final item = order.items[i];
          _addLog('   明细${i + 1}: ${item.itemName} - ${item.amount}元');
        }
      } else {
        _addLog('⚠️ 明细列表为空');
      }
      
    } catch (e) {
      _addLog('❌ API调用失败: $e');
    }
  }

  /// 测试数据解析
  Future<void> _testDataParsing() async {
    _addLog('🔍 测试数据解析...');
    
    // 模拟API返回的数据
    final mockApiData = {
      'id': 1,
      'recycle_no': 'REC202506190002',
      'customer_name': '测试客户',
      'phone': '13812345678',
      'total_amount': 470.0,
      'gold_weight': 10.5,
      'silver_weight': 0.0,
      'item_count': 1,
      'status': 1,
      'createtime': 1734590649,
      'store_id': 1,
      'store_name': '测试门店',
      'operator_name': 'Admin',
      'items': [
        {
          'id': 1,
          'recycling_id': 1,
          'name': '黄金项链',
          'category_id': 1,
          'category_name': '黄金首饰',
          'gold_weight': 10.5,
          'gold_price': 470.0,
          'gold_amount': 4935.0,
          'silver_weight': 2.0,
          'silver_price': 8.0,
          'silver_amount': 16.0,
          'total_amount': 470.0,
          'discount_rate': 95.0,
          'discount_amount': 23.5,
          'remark': '成色不错',
        }
      ]
    };
    
    try {
      _addLog('📊 开始解析模拟数据...');
      final order = RecyclingOrder.fromJson(mockApiData);
      
      _addLog('✅ 数据解析成功');
      _addLog('📦 解析结果: ${order.orderNo}, 明细数量: ${order.items.length}');
      
    } catch (e) {
      _addLog('❌ 数据解析失败: $e');
    }
  }

  /// 测试完整流程
  Future<void> _testFullFlow() async {
    _addLog('🔍 测试完整流程...');
    
    try {
      final controller = Get.find<RecyclingController>();
      
      _addLog('🚀 调用Controller.loadRecyclingOrderDetail(1)');
      await controller.loadRecyclingOrderDetail(1);
      
      final order = controller.currentOrder.value;
      if (order != null) {
        _addLog('✅ Controller调用成功');
        _addLog('📦 获取到回收单: ${order.orderNo}');
        _addLog('📦 明细数量: ${order.items.length}');
        
        // 显示详情对话框
        Get.dialog(
          RecyclingDetailDialog(recyclingOrder: order),
          barrierDismissible: true,
        );
        
      } else {
        _addLog('❌ Controller返回的order为null');
      }
      
    } catch (e) {
      _addLog('❌ 完整流程测试失败: $e');
    }
  }
}
