import 'package:flutter/material.dart';
import 'package:get/get.dart';


import '../../../core/constants/border_styles.dart';
import '../../../services/auth_service.dart';
import '../../../models/stock/stock_out_item.dart';

import '../controllers/stock_out_form_controller.dart';
import '../widgets/payment_dialog.dart';

/// 出库单表单页面 - 重新设计的UI界面
class StockOutFormView extends StatefulWidget {
  final String? tag; // 控制器标签，用于标签页模式

  const StockOutFormView({super.key, this.tag});

  @override
  State<StockOutFormView> createState() => _StockOutFormViewState();
}

class _StockOutFormViewState extends State<StockOutFormView> {
  // 横向滚动控制器
  final ScrollController _horizontalScrollController = ScrollController();
  // 条码扫描控制器
  final TextEditingController _barcodeController = TextEditingController();
  // 🔑 关键修复：TextEditingController管理器，避免重复创建导致输入错误
  final Map<String, TextEditingController> _textControllers = {};

  @override
  void initState() {
    super.initState();

    // 🔄 设置UI更新回调
    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.setUIUpdateCallback(_handleUIUpdate);
    });
  }

  /// 🔄 处理UI更新回调
  void _handleUIUpdate(int index, String fieldType, String newValue) {
    final controllerId = '${fieldType}_$index';
    _forceUpdateController(controllerId, newValue);
  }

  @override
  void dispose() {
    _horizontalScrollController.dispose();
    _barcodeController.dispose();
    // 🔑 清理所有TextEditingController
    for (final controller in _textControllers.values) {
      controller.dispose();
    }
    _textControllers.clear();
    super.dispose();
  }

  // 获取控制器实例
  StockOutFormController get controller {
    if (widget.tag != null) {
      // 使用标签获取特定的控制器实例
      return Get.find<StockOutFormController>(tag: widget.tag);
    } else {
      // 获取默认控制器
      return Get.find<StockOutFormController>();
    }
  }

  /// 🔑 关键修复：获取或创建TextEditingController，避免重复创建导致输入错误
  /// 🔧 优化：改进用户输入体验，避免在用户输入时强制更新
  TextEditingController _getOrCreateController(String id, String initialValue) {
    if (!_textControllers.containsKey(id)) {
      _textControllers[id] = TextEditingController(text: initialValue);
    } else {
      final textController = _textControllers[id]!;

      // 🔧 改进更新逻辑：只在文本内容确实不同且用户不在编辑时才更新
      // 使用更精确的判断条件：检查文本是否相等，避免不必要的更新
      if (textController.text != initialValue) {
        // 检查当前值是否可以解析为相同的数字（避免格式差异导致的重复更新）
        final currentValue = double.tryParse(textController.text);
        final newValue = double.tryParse(initialValue);

        // 只有在数值确实不同时才更新
        if (currentValue != newValue) {
          textController.text = initialValue;
        }
      }
    }
    return _textControllers[id]!;
  }

  /// 🔄 强制更新指定控制器的值（用于计算结果同步）
  void _forceUpdateController(String id, String newValue) {
    if (_textControllers.containsKey(id)) {
      final controller = _textControllers[id]!;

      // 🔄 延迟更新，确保在下一个事件循环中执行，避免与当前输入事件冲突
      Future.microtask(() {
        if (controller.text != newValue) {
          controller.text = newValue;
          // 将光标移到末尾
          controller.selection = TextSelection.fromPosition(
            TextPosition(offset: newValue.length),
          );
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Form(
        key: controller.formKey,
        child: Column(
          children: [
            _buildFormHeader(),
            _buildBarcodeSection(),
            Expanded(
              child: _buildItemList(),
            ),
            _buildFormFooter(),
          ],
        ),
      ),
    );
  }

  /// 构建表单头部区域 - 参考入库管理页面的单行布局
  Widget _buildFormHeader() {
    final authService = Get.find<AuthService>();

    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: AppBorderStyles.tableBorder,
        ),
      ),
      child: Row(
        children: [
          // 🔷 出库单表单（图标+标题）- 根据编辑模式显示不同内容
          Obx(() {
            final isEditing = controller.isEditing.value;
            return Row(
              children: [
                Icon(
                  isEditing ? Icons.edit : Icons.logout, 
                  color: isEditing ? Colors.orange[600] : Colors.green[600], 
                  size: 20
                ),
                const SizedBox(width: 8),
                Text(
                  isEditing ? '编辑出库单' : '新建出库单',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ],
            );
          }),

          const SizedBox(width: 24),

          // 👤 操作员信息标签
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(AppBorderStyles.largeBorderRadius),
              border: Border.all(color: Colors.blue[200]!, width: AppBorderStyles.borderWidth),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.person, size: 14, color: Colors.blue[600]),
                const SizedBox(width: 4),
                Obx(() => Text(
                  '操作员: ${authService.userNickname.value.isNotEmpty ? authService.userNickname.value : authService.userName.value}',
                  style: TextStyle(
                    fontSize: 13,
                    color: Colors.blue[700],
                    fontWeight: FontWeight.w500,
                  ),
                )),
              ],
            ),
          ),

          const SizedBox(width: 24),

          // 门店选择
          const Text('门店:', style: TextStyle(fontSize: 13, fontWeight: FontWeight.w500)),
          const SizedBox(width: 8),
          SizedBox(
            width: 150,
            height: 32,
            child: Obx(() => Container(
              height: 32,
              decoration: AppBorderStyles.standardBoxDecoration,
              child: DropdownButtonHideUnderline(
                child: DropdownButton<int>(
                  value: controller.selectedStoreId.value == 0 ? null : controller.selectedStoreId.value,
                  hint: Container(
                    alignment: Alignment.center,
                    child: const Text(
                      '请选择门店',
                      style: TextStyle(fontSize: 13, color: Colors.grey),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  isExpanded: true,
                  items: controller.storeList.map((store) {
                    return DropdownMenuItem<int>(
                      value: store.id,
                      child: Text(
                        store.name,
                        style: const TextStyle(fontSize: 13),
                        textAlign: TextAlign.center,
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      controller.selectedStoreId.value = value;
                    }
                  },
                  style: const TextStyle(fontSize: 13, color: Colors.black87),
                  icon: const Icon(Icons.arrow_drop_down, size: 20),
                  iconSize: 20,
                  menuMaxHeight: 300,
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  selectedItemBuilder: (BuildContext context) {
                    return controller.storeList.map<Widget>((store) {
                      return Container(
                        alignment: Alignment.center,
                        child: Text(
                          store.name,
                          style: const TextStyle(fontSize: 13, color: Colors.black87),
                          textAlign: TextAlign.center,
                        ),
                      );
                    }).toList();
                  },
                ),
              ),
            )),
          ),

          const SizedBox(width: 24),

          // 销售类型选择
          const Text('销售类型:', style: TextStyle(fontSize: 13, fontWeight: FontWeight.w500)),
          const SizedBox(width: 8),
          SizedBox(
            width: 120,
            height: 32,
            child: Obx(() => Container(
              height: 32,
              decoration: AppBorderStyles.standardBoxDecoration,
              child: DropdownButtonHideUnderline(
                child: DropdownButton<String>(
                  value: controller.selectedSaleType.value,
                  isExpanded: true,
                  items: controller.saleTypeOptions.map((option) {
                    return DropdownMenuItem<String>(
                      value: option['value'],
                      child: Text(
                        option['label']!,
                        style: const TextStyle(fontSize: 13),
                        textAlign: TextAlign.center,
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      controller.selectedSaleType.value = value;
                    }
                  },
                  style: const TextStyle(fontSize: 13, color: Colors.black87),
                  icon: const Icon(Icons.arrow_drop_down, size: 20),
                  iconSize: 20,
                  menuMaxHeight: 300,
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  selectedItemBuilder: (BuildContext context) {
                    return controller.saleTypeOptions.map<Widget>((option) {
                      return Container(
                        alignment: Alignment.center,
                        child: Text(
                          option['label']!,
                          style: const TextStyle(fontSize: 13, color: Colors.black87),
                          textAlign: TextAlign.center,
                        ),
                      );
                    }).toList();
                  },
                ),
              ),
            )),
          ),

          const SizedBox(width: 24),

          // 备注输入
          const Text('备注:', style: TextStyle(fontSize: 13, fontWeight: FontWeight.w500)),
          const SizedBox(width: 8),
          SizedBox(
            width: 200,
            height: 32,
            child: Container(
              height: 32,
              decoration: AppBorderStyles.standardBoxDecoration,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                child: TextField(
                  controller: controller.remarkController,
                  decoration: const InputDecoration(
                    hintText: '输入备注信息',
                    hintStyle: TextStyle(fontSize: 13, color: Colors.grey),
                    border: InputBorder.none,
                    enabledBorder: InputBorder.none,
                    focusedBorder: InputBorder.none,
                    errorBorder: InputBorder.none,
                    focusedErrorBorder: InputBorder.none,
                    disabledBorder: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(vertical: 6),
                    isDense: true,
                  ),
                  style: const TextStyle(fontSize: 13),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建条码扫描区域 - 包含功能按钮的重构版本
  Widget _buildBarcodeSection() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: AppBorderStyles.tableBorder,
        ),
      ),
      child: Row(
        children: [
          // 📦 扫描条码标题
          Icon(Icons.qr_code_scanner, color: Colors.green[600], size: 20),
          const SizedBox(width: 8),
          const Text(
            '扫描条码',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),

          const SizedBox(width: 24),

          // 条码输入框 - 缩小宽度为功能按钮腾出空间
          SizedBox(
            width: 300, // 固定宽度，不再使用Expanded
            height: 32, // 调整为32px高度
            child: TextFormField(
              controller: _barcodeController,
              decoration: AppBorderStyles.compactInputDecoration.copyWith(
                hintText: '请扫描或输入商品条码',
                prefixIcon: const Icon(Icons.qr_code, size: 18),
                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              style: const TextStyle(fontSize: 13),
              onFieldSubmitted: (value) => _handleBarcodeInput(value),
            ),
          ),

          const SizedBox(width: 12),

          // 扫描按钮
          SizedBox(
            height: 32,
            child: ElevatedButton.icon(
              icon: const Icon(Icons.qr_code_scanner, size: 14),
              label: const Text('扫描'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green[600],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 0),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
                ),
                textStyle: const TextStyle(fontSize: 13),
              ),
              onPressed: _scanBarcode,
            ),
          ),

          const SizedBox(width: 12),

          // 批量改价按钮
          SizedBox(
            height: 32,
            child: ElevatedButton.icon(
              icon: const Icon(Icons.price_change, size: 14),
              label: const Text('批量改价'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange[600],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 0),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
                ),
                textStyle: const TextStyle(fontSize: 13),
              ),
              onPressed: _batchChangePrice,
            ),
          ),

          const SizedBox(width: 8),

          // 旧料回收按钮
          SizedBox(
            height: 32,
            child: ElevatedButton.icon(
              icon: const Icon(Icons.recycling, size: 14),
              label: const Text('旧料回收'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.purple[600],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 0),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
                ),
                textStyle: const TextStyle(fontSize: 13),
              ),
              onPressed: _oldMaterialRecycling,
            ),
          ),

          const SizedBox(width: 8),

          // 退换货按钮
          SizedBox(
            height: 32,
            child: ElevatedButton.icon(
              icon: const Icon(Icons.keyboard_return, size: 14),
              label: const Text('退换货'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red[600],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 0),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
                ),
                textStyle: const TextStyle(fontSize: 13),
              ),
              onPressed: _returnExchange,
            ),
          ),

          const SizedBox(width: 8),

          // 单收工费按钮
          SizedBox(
            height: 32,
            child: ElevatedButton.icon(
              icon: const Icon(Icons.build, size: 14),
              label: const Text('单收工费'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue[600],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 0),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
                ),
                textStyle: const TextStyle(fontSize: 13),
              ),
              onPressed: _singleWorkFee,
            ),
          ),
        ],
      ),
    );
  }

  /// 处理条码输入
  void _handleBarcodeInput(String barcode) {
    if (barcode.trim().isNotEmpty) {
      // 调用控制器的条码处理方法
      controller.addItemByBarcode(barcode.trim());
      _barcodeController.clear();
    }
  }

  /// 扫描条码
  void _scanBarcode() {
    // 获取当前输入框的内容并处理
    final barcode = _barcodeController.text.trim();
    if (barcode.isNotEmpty) {
      _handleBarcodeInput(barcode);
    } else {
      Get.snackbar(
        '提示',
        '请先输入条码或使用扫描设备',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
    }
  }

  /// 构建商品明细列表 - 参考入库管理页面的表格设计
  Widget _buildItemList() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: AppBorderStyles.elevatedBoxDecoration.copyWith(
        color: Colors.white,
      ),
      child: Column(
        children: [
          // 商品明细标题栏
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: const BoxDecoration(
              color: Colors.white,
              border: Border(
                bottom: AppBorderStyles.tableBorder,
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.inventory_2, color: Colors.green[600], size: 20),
                const SizedBox(width: 8),
                const Text(
                  '商品明细',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const Spacer(),
                // 商品数量显示（排除回收商品和退换货商品）
                Obx(() {
                  // 🎯 计算正常商品数量，排除回收商品和退换货商品
                  final normalItemCount = controller.itemList.where((item) =>
                    !_isRecyclingItem(item) && !_isReturnExchangeItem(item)).length;
                  return Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.green[50],
                      borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
                      border: Border.all(color: Colors.green[200]!, width: 1),
                    ),
                    child: Text(
                      '共 $normalItemCount 件商品',
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.green[700],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  );
                }),
              ],
            ),
          ),
          // 商品列表内容
          Expanded(
            child: Obx(() => controller.itemList.isEmpty
                ? _buildEmptyItemList()
                : _buildStockOutItemTable()),
          ),
        ],
      ),
    );
  }

  /// 构建空商品列表
  Widget _buildEmptyItemList() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inventory_2_outlined,
            size: 64,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text(
            '暂无商品明细',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 8),
          Text(
            '请扫描条码或使用功能按钮添加商品',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  /// 检查是否为回收商品
  /// 回收商品的特征：条码以"9"开头且长度为9位，jewelryId为0，价格为负数
  bool _isRecyclingItem(StockOutItem item) {
    return item.barcode != null &&
           item.barcode!.startsWith('9') &&
           item.barcode!.length == 9 &&
           item.jewelryId == 0 &&
           item.amount < 0;
  }

  /// 检查是否为退换货商品
  /// 退换货商品的特征：jewelryId不为0，价格为负数，商品名称包含"退货-"
  bool _isReturnExchangeItem(StockOutItem item) {
    return item.jewelryId != 0 &&
           item.amount < 0 &&
           (item.jewelry?.name.startsWith('退货-') ?? false);
  }

  /// 检查是否为工费项目
  /// 工费项目的特征：jewelryId为-1，商品名称包含"工费-"
  bool _isWorkFeeItem(StockOutItem item) {
    return item.jewelryId == -1 &&
           (item.jewelry?.name.startsWith('工费-') == true);
  }

  /// 🔧 智能价格格式化：只在必要时显示小数位，提升用户输入体验
  /// 例如：850.0 -> "850", 850.25 -> "850.25", 850.20 -> "850.2"
  String _formatPriceForInput(double price) {
    if (price == price.roundToDouble()) {
      // 如果是整数，不显示小数点
      return price.round().toString();
    } else {
      // 如果有小数，移除末尾的0
      
      String formatted = price.toStringAsFixed(2);
      if (formatted.endsWith('0')) {
        formatted = formatted.substring(0, formatted.length - 1);
      }
      return formatted;
    }
  }

  /// 🔧 验证和格式化用户输入的价格
  /// 确保输入符合价格格式要求（最多2位小数）
  String? _validatePriceInput(String input) {
    if (input.isEmpty) return null;

    // 移除多余的空格
    input = input.trim();

    // 检查是否为有效的数字格式
    final double? value = double.tryParse(input);
    if (value == null || value < 0) {
      return null; // 无效输入
    }

    // 检查小数位数不超过2位
    if (input.contains('.')) {
      final parts = input.split('.');
      if (parts.length == 2 && parts[1].length > 2) {
        return null; // 小数位超过2位
      }
    }

    return input; // 有效输入
  }

  /// 构建出库商品表格 - 参考入库管理页面的表格实现
  Widget _buildStockOutItemTable() {
    return LayoutBuilder(
      builder: (context, constraints) {
        final availableWidth = constraints.maxWidth;

        // 使用比例宽度而非固定宽度，确保充分利用可用空间
        final indexWidth = availableWidth * 0.04;      // 4% - 序号（减小）
        final barcodeWidth = availableWidth * 0.08;    // 8% - 条码（增加）
        final nameWidth = availableWidth * 0.12;       // 12% - 商品名称（增加）
        final categoryWidth = availableWidth * 0.08;   // 8% - 分类（增加）
        final ringSizeWidth = availableWidth * 0.05;   // 5% - 圈口号（不变）
        final goldWeightWidth = availableWidth * 0.06; // 6% - 金重（不变）
        final goldPriceWidth = availableWidth * 0.06;  // 6% - 金价（不变）
        final silverWeightWidth = availableWidth * 0.06; // 6% - 银重（不变）
        final silverPriceWidth = availableWidth * 0.06; // 6% - 银价（不变）
        final totalWeightWidth = availableWidth * 0.06; // 6% - 总重（不变）
        final workFeeWidth = availableWidth * 0.07;    // 7% - 工费（增加）
        final pieceWorkWidth = availableWidth * 0.07;  // 7% - 件工费（增加）
        final priceWidth = availableWidth * 0.09;      // 9% - 价格（增加）
        final actionWidth = availableWidth * 0.09;     // 9% - 操作（从10%减少到9%）
        // 总计：4+8+12+8+5+6+6+6+6+6+7+7+9+9 = 99%（预留1%的弹性空间）

        // 总宽度就是可用宽度
        final totalWidth = availableWidth;

        // 根据可用宽度调整字体大小
        final fontSize = availableWidth < 1200 ? 11.0 : 13.0;
        final headingFontSize = availableWidth < 1200 ? 12.0 : 14.0;

        return Container(
          width: double.infinity,
          height: double.infinity,
          margin: const EdgeInsets.all(4),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.03),
                blurRadius: 3,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Column(
            children: [
              // 表格标题行 - 固定显示，不在滚动区域内
              Container(
                width: totalWidth,
                color: AppBorderStyles.tableHeaderBackground,
                height: 48,
                child: Row(
                  children: [
                    Container(
                      width: indexWidth, 
                      decoration: AppBorderStyles.headerCellDecoration(),
                      child: const Center(child: Text('序号', style: TextStyle(fontWeight: FontWeight.w600, fontSize: 14, color: Colors.black87)))
                    ),
                    Container(
                      width: barcodeWidth, 
                      decoration: AppBorderStyles.headerCellDecoration(),
                      child: const Center(child: Text('条码', style: TextStyle(fontWeight: FontWeight.w600, fontSize: 14, color: Colors.black87)))
                    ),
                    Container(
                      width: nameWidth, 
                      decoration: AppBorderStyles.headerCellDecoration(),
                      child: const Center(child: Text('商品名称', style: TextStyle(fontWeight: FontWeight.w600, fontSize: 14, color: Colors.black87)))
                    ),
                    Container(
                      width: categoryWidth, 
                      decoration: AppBorderStyles.headerCellDecoration(),
                      child: const Center(child: Text('分类', style: TextStyle(fontWeight: FontWeight.w600, fontSize: 14, color: Colors.black87)))
                    ),
                    Container(
                      width: ringSizeWidth, 
                      decoration: AppBorderStyles.headerCellDecoration(),
                      child: const Center(child: Text('圈口号', style: TextStyle(fontWeight: FontWeight.w600, fontSize: 14, color: Colors.black87)))
                    ),
                    Container(
                      width: goldWeightWidth, 
                      decoration: AppBorderStyles.headerCellDecoration(),
                      child: const Center(child: Text('金重(g)', style: TextStyle(fontWeight: FontWeight.w600, fontSize: 14, color: Colors.black87)))
                    ),
                    Container(
                      width: goldPriceWidth, 
                      decoration: AppBorderStyles.headerCellDecoration(),
                      child: const Center(child: Text('金价(元/g)', style: TextStyle(fontWeight: FontWeight.w600, fontSize: 14, color: Colors.black87)))
                    ),
                    Container(
                      width: silverWeightWidth, 
                      decoration: AppBorderStyles.headerCellDecoration(),
                      child: const Center(child: Text('银重(g)', style: TextStyle(fontWeight: FontWeight.w600, fontSize: 14, color: Colors.black87)))
                    ),
                    Container(
                      width: silverPriceWidth, 
                      decoration: AppBorderStyles.headerCellDecoration(),
                      child: const Center(child: Text('银价(元/g)', style: TextStyle(fontWeight: FontWeight.w600, fontSize: 14, color: Colors.black87)))
                    ),
                    Container(
                      width: totalWeightWidth, 
                      decoration: AppBorderStyles.headerCellDecoration(),
                      child: const Center(child: Text('总重(g)', style: TextStyle(fontWeight: FontWeight.w600, fontSize: 14, color: Colors.black87)))
                    ),
                    Container(
                      width: workFeeWidth, 
                      decoration: AppBorderStyles.headerCellDecoration(),
                      child: const Center(child: Text('工费(元)', style: TextStyle(fontWeight: FontWeight.w600, fontSize: 14, color: Colors.black87)))
                    ),
                    Container(
                      width: pieceWorkWidth, 
                      decoration: AppBorderStyles.headerCellDecoration(),
                      child: const Center(child: Text('件工费(元)', style: TextStyle(fontWeight: FontWeight.w600, fontSize: 14, color: Colors.black87)))
                    ),
                    Container(
                      width: priceWidth, 
                      decoration: AppBorderStyles.headerCellDecoration(),
                      child: const Center(child: Text('价格(元)', style: TextStyle(fontWeight: FontWeight.w600, fontSize: 14, color: Colors.black87)))
                    ),
                    Container(
                      width: actionWidth, 
                      decoration: AppBorderStyles.headerCellDecoration(),
                      child: const Center(child: Text('操作', style: TextStyle(fontWeight: FontWeight.w600, fontSize: 14, color: Colors.black87)))
                    ),
                  ],
                ),
              ),
              
              // 数据行 - 使用ListView.builder
              Expanded(
                child: ListView.builder(
                  itemCount: controller.itemList.length,
                  itemBuilder: (context, index) {
                    final item = controller.itemList[index];
                    final jewelry = item.jewelry;
                    final isRecyclingItem = _isRecyclingItem(item);
                    final isReturnExchangeItem = _isReturnExchangeItem(item);
                    final isWorkFeeItem = _isWorkFeeItem(item);

                    // 🎨 确定行的装饰样式
                    BoxDecoration rowDecoration;
                    if (isRecyclingItem) {
                      rowDecoration = _recyclingCellDecoration();
                    } else if (isReturnExchangeItem) {
                      rowDecoration = _returnExchangeCellDecoration();
                    } else if (isWorkFeeItem) {
                      rowDecoration = _workFeeCellDecoration();
                    } else {
                      rowDecoration = _whiteCellDecoration();
                    }

                    return Container(
                      width: totalWidth,
                      height: 52,
                      decoration: rowDecoration,
                      child: Row(
                        children: [
                          Container(
                            width: indexWidth,
                            height: 52,
                            decoration: rowDecoration,
                            padding: const EdgeInsets.symmetric(horizontal: 4),
                            alignment: Alignment.center,
                            child: Text('${index + 1}',
                                style: TextStyle(
                                  fontWeight: FontWeight.w500,
                                  color: isRecyclingItem ? Colors.purple[700] :
                                         isReturnExchangeItem ? Colors.red[700] : Colors.black87
                                ),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1)
                          ),
                          Container(
                            width: barcodeWidth,
                            height: 52,
                            decoration: rowDecoration,
                            padding: const EdgeInsets.symmetric(horizontal: 4),
                            alignment: Alignment.center,
                            child: Text(item.barcode ?? '',
                                style: TextStyle(
                                  fontSize: fontSize,
                                  color: isRecyclingItem ? Colors.purple[700] :
                                         isReturnExchangeItem ? Colors.red[700] :
                                         isWorkFeeItem ? Colors.blue[700] : Colors.black87
                                ),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1)
                          ),
                          Container(
                            width: nameWidth,
                            height: 52,
                            decoration: rowDecoration,
                            padding: const EdgeInsets.symmetric(horizontal: 4),
                            alignment: Alignment.center,
                            child: Text(jewelry?.name ?? '未知商品',
                                style: TextStyle(
                                  fontSize: fontSize,
                                  color: isRecyclingItem ? Colors.purple[700] :
                                         isReturnExchangeItem ? Colors.red[700] :
                                         isWorkFeeItem ? Colors.blue[700] : Colors.black87,
                                  fontWeight: (isRecyclingItem || isReturnExchangeItem || isWorkFeeItem) ? FontWeight.w600 : FontWeight.normal
                                ),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1)
                          ),
                          Container(
                            width: categoryWidth,
                            height: 52,
                            decoration: rowDecoration,
                            padding: const EdgeInsets.symmetric(horizontal: 4),
                            alignment: Alignment.center,
                            child: Text(jewelry?.categoryName ?? '未知分类',
                                style: TextStyle(
                                  fontSize: fontSize,
                                  color: isRecyclingItem ? Colors.purple[700] :
                                         isReturnExchangeItem ? Colors.red[700] :
                                         isWorkFeeItem ? Colors.blue[700] : Colors.black87
                                ),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1)
                          ),
                          Container(
                            width: ringSizeWidth,
                            height: 52,
                            decoration: rowDecoration,
                            padding: const EdgeInsets.symmetric(horizontal: 4),
                            alignment: Alignment.center,
                            child: Text('',
                                style: TextStyle(
                                  fontSize: fontSize,
                                  color: isRecyclingItem ? Colors.purple[700] :
                                         isReturnExchangeItem ? Colors.red[700] :
                                         isWorkFeeItem ? Colors.blue[700] : Colors.black87
                                ),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1)
                          ),
                          Container(
                            width: goldWeightWidth,
                            height: 52,
                            decoration: rowDecoration,
                            padding: const EdgeInsets.symmetric(horizontal: 4),
                            alignment: Alignment.center,
                            child: Text((jewelry?.goldWeight ?? 0.0).toStringAsFixed(2),
                                style: TextStyle(
                                  fontSize: fontSize,
                                  color: isRecyclingItem ? Colors.purple[700] :
                                         isReturnExchangeItem ? Colors.red[700] :
                                         isWorkFeeItem ? Colors.blue[700] : Colors.black87
                                ),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1)
                          ),
                          Container(
                            width: goldPriceWidth,
                            height: 52,
                            decoration: rowDecoration,
                            padding: const EdgeInsets.symmetric(horizontal: 4),
                            alignment: Alignment.center,
                            child: Obx(() => _buildEditableCell(
                              width: goldPriceWidth,
                              // 🔧 修复：始终使用StockOutItem自身的价格信息，而不是jewelry对象的价格
                              value: _formatPriceForInput(controller.itemList[index].goldPrice),
                              controllerId: 'goldPrice_$index',
                              onChanged: (value) {
                                // 🔧 改进输入验证：使用智能验证函数
                                final validatedInput = _validatePriceInput(value);
                                if (validatedInput != null) {
                                  final price = double.tryParse(validatedInput);
                                  if (price != null) {
                                    controller.updateItemField(index, goldPrice: price);
                                  }
                                }
                              },
                              isRecyclingItem: isRecyclingItem,
                              isReturnExchangeItem: isReturnExchangeItem,
                              isWorkFeeItem: isWorkFeeItem,
                            )),
                          ),
                          Container(
                            width: silverWeightWidth,
                            height: 52,
                            decoration: rowDecoration,
                            padding: const EdgeInsets.symmetric(horizontal: 4),
                            alignment: Alignment.center,
                            child: Text((jewelry?.silverWeight ?? 0.0).toStringAsFixed(2),
                                style: TextStyle(
                                  fontSize: fontSize,
                                  color: isRecyclingItem ? Colors.purple[700] :
                                         isReturnExchangeItem ? Colors.red[700] :
                                         isWorkFeeItem ? Colors.blue[700] : Colors.black87
                                ),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1)
                          ),
                          Container(
                            width: silverPriceWidth,
                            height: 52,
                            decoration: rowDecoration,
                            padding: const EdgeInsets.symmetric(horizontal: 4),
                            alignment: Alignment.center,
                            child: Obx(() => _buildEditableCell(
                              width: silverPriceWidth,
                              // 🔧 修复：始终使用StockOutItem自身的价格信息，而不是jewelry对象的价格
                              value: _formatPriceForInput(controller.itemList[index].silverPrice),
                              controllerId: 'silverPrice_$index',
                              onChanged: (value) {
                                // 🔧 改进输入验证：使用智能验证函数
                                final validatedInput = _validatePriceInput(value);
                                if (validatedInput != null) {
                                  final price = double.tryParse(validatedInput);
                                  if (price != null) {
                                    controller.updateItemField(index, silverPrice: price);
                                  }
                                }
                              },
                              isRecyclingItem: isRecyclingItem,
                              isReturnExchangeItem: isReturnExchangeItem,
                              isWorkFeeItem: isWorkFeeItem,
                            )),
                          ),
                          Container(
                            width: totalWeightWidth,
                            height: 52,
                            decoration: rowDecoration,
                            padding: const EdgeInsets.symmetric(horizontal: 4),
                            alignment: Alignment.center,
                            child: Text(
                                // 🔧 修复：显示StockOutItem中的总重信息，如果没有则计算
                                isReturnExchangeItem ?
                                controller.itemList[index].totalWeight.toStringAsFixed(2) :
                                ((jewelry?.goldWeight ?? 0.0) + (jewelry?.silverWeight ?? 0.0)).toStringAsFixed(2),
                                style: TextStyle(
                                  fontSize: fontSize,
                                  color: isRecyclingItem ? Colors.purple[700] :
                                         isReturnExchangeItem ? Colors.red[700] :
                                         isWorkFeeItem ? Colors.blue[700] : Colors.black87
                                ),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1)
                          ),
                          Container(
                            width: workFeeWidth,
                            height: 52,
                            decoration: rowDecoration,
                            padding: const EdgeInsets.symmetric(horizontal: 4),
                            alignment: Alignment.center,
                            child: Obx(() => _buildEditableCell(
                              width: workFeeWidth,
                              // 🔧 修复：始终使用StockOutItem自身的价格信息，而不是jewelry对象的价格
                              value: controller.itemList[index].workPrice.round().toString(),
                              controllerId: 'workPrice_$index',
                              onChanged: (value) {
                                final price = double.tryParse(value);
                                if (price != null) {
                                  // 🔧 添加调试日志
                                  debugPrint('🔧 工费修改: 索引=$index, 新工费=$price');
                                  controller.updateItemField(index, workPrice: price);
                                }
                              },
                              isRecyclingItem: isRecyclingItem,
                              isReturnExchangeItem: isReturnExchangeItem,
                              isWorkFeeItem: isWorkFeeItem,
                            )),
                          ),
                          Container(
                            width: pieceWorkWidth,
                            height: 52,
                            decoration: rowDecoration,
                            padding: const EdgeInsets.symmetric(horizontal: 4),
                            alignment: Alignment.center,
                            child: Obx(() => _buildEditableCell(
                              width: pieceWorkWidth,
                              // 🔧 修复：始终使用StockOutItem自身的价格信息，而不是jewelry对象的价格
                              value: controller.itemList[index].pieceWorkPrice.round().toString(),
                              controllerId: 'pieceWorkPrice_$index',
                              onChanged: (value) {
                                final price = double.tryParse(value);
                                if (price != null) {
                                  controller.updateItemField(index, pieceWorkPrice: price);
                                }
                              },
                              isRecyclingItem: isRecyclingItem,
                              isReturnExchangeItem: isReturnExchangeItem,
                              isWorkFeeItem: isWorkFeeItem,
                            )),
                          ),
                          Container(
                            width: priceWidth,
                            height: 52,
                            decoration: rowDecoration,
                            padding: const EdgeInsets.symmetric(horizontal: 4),
                            alignment: Alignment.center,
                            child: Obx(() => _buildEditableCell(
                              width: priceWidth,
                              value: controller.itemList[index].amount.round().toString(),
                              controllerId: 'amount_$index',
                              onChanged: (value) {
                                final amount = double.tryParse(value);
                                if (amount != null) {
                                  controller.updateItemField(index, amount: amount);
                                }
                              },
                              isRecyclingItem: isRecyclingItem,
                              isReturnExchangeItem: isReturnExchangeItem,
                              isWorkFeeItem: isWorkFeeItem,
                            )),
                          ),
                          Container(
                            width: actionWidth,
                            height: 52,
                            decoration: rowDecoration,
                            padding: const EdgeInsets.symmetric(horizontal: 4),
                            alignment: Alignment.center,
                            child: Center(
                              child: ElevatedButton.icon(
                                icon: const Icon(Icons.delete_outline, size: 16, color: Colors.white),
                                label: const Text('删除'),
                                onPressed: () => _confirmRemoveItem(index),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.red[600],
                                  foregroundColor: Colors.white,
                                  minimumSize: const Size(70, 30),
                                  maximumSize: const Size(70, 30),
                                  padding: EdgeInsets.zero,
                                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 批量改价功能
  void _batchChangePrice() {
    if (controller.itemList.isEmpty) {
      Get.snackbar(
        '提示',
        '当前没有商品，无法进行批量改价',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      return;
    }

    _showBatchPriceChangeDialog();
  }

  /// 显示批量改价对话框
  void _showBatchPriceChangeDialog() {
    final goldPriceController = TextEditingController();
    final silverPriceController = TextEditingController();
    final workPriceController = TextEditingController();
    final pieceWorkPriceController = TextEditingController();

    Get.dialog(
      AlertDialog(
        title: Row(
          children: [
            Icon(Icons.price_change, color: Colors.orange[600], size: 20),
            const SizedBox(width: 8),
            const Text('批量改价', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
          ],
        ),
        content: SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 提示信息
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(AppBorderStyles.mediumBorderRadius),
                  border: Border.all(color: Colors.blue[200]!, width: AppBorderStyles.borderWidth),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info_outline, color: Colors.blue[600], size: 16),
                    const SizedBox(width: 8),
                    const Expanded(
                      child: Text(
                        '只填写需要修改的价格项，空白项将保持原值不变',
                        style: TextStyle(fontSize: 12, color: Colors.black87),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),

              // 金价输入框
              _buildPriceInputField('金价 (元/克)', goldPriceController, Icons.monetization_on, Colors.amber[600]!),
              const SizedBox(height: 12),

              // 银价输入框
              _buildPriceInputField('银价 (元/克)', silverPriceController, Icons.circle, Colors.grey[600]!),
              const SizedBox(height: 12),

              // 工费输入框
              _buildPriceInputField('工费 (元/克)', workPriceController, Icons.build, Colors.blue[600]!),
              const SizedBox(height: 12),

              // 件工费输入框
              _buildPriceInputField('件工费 (元/件)', pieceWorkPriceController, Icons.handyman, Colors.green[600]!),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              _executeBatchPriceChange(
                goldPriceController.text,
                silverPriceController.text,
                workPriceController.text,
                pieceWorkPriceController.text,
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange[600],
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
              ),
            ),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  /// 构建价格输入字段
  Widget _buildPriceInputField(String label, TextEditingController controller, IconData icon, Color iconColor) {
    return Row(
      children: [
        Icon(icon, color: iconColor, size: 16),
        const SizedBox(width: 8),
        SizedBox(
          width: 80,
          child: Text(
            label,
            style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w500),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Container(
            height: 32,
            decoration: AppBorderStyles.standardBoxDecoration,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              child: TextField(
                controller: controller,
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                decoration: const InputDecoration(
                  hintText: '可选',
                  hintStyle: TextStyle(fontSize: 12, color: Colors.grey),
                  border: InputBorder.none,
                  enabledBorder: InputBorder.none,
                  focusedBorder: InputBorder.none,
                  errorBorder: InputBorder.none,
                  focusedErrorBorder: InputBorder.none,
                  contentPadding: EdgeInsets.zero,
                  isDense: true,
                ),
                style: const TextStyle(fontSize: 13),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 执行批量改价
  void _executeBatchPriceChange(String goldPriceStr, String silverPriceStr, String workPriceStr, String pieceWorkPriceStr) {
    // 解析输入的价格
    double? goldPrice = goldPriceStr.isNotEmpty ? double.tryParse(goldPriceStr) : null;
    double? silverPrice = silverPriceStr.isNotEmpty ? double.tryParse(silverPriceStr) : null;
    double? workPrice = workPriceStr.isNotEmpty ? double.tryParse(workPriceStr) : null;
    double? pieceWorkPrice = pieceWorkPriceStr.isNotEmpty ? double.tryParse(pieceWorkPriceStr) : null;

    // 检查是否至少有一个价格被修改
    if (goldPrice == null && silverPrice == null && workPrice == null && pieceWorkPrice == null) {
      Get.snackbar(
        '提示',
        '请至少填写一个价格项',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      return;
    }

    // 关闭对话框
    Get.back();

    // 显示确认对话框
    Get.dialog(
      AlertDialog(
        title: const Text('确认批量改价'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('即将对当前表格中的所有商品进行批量改价：'),
            const SizedBox(height: 8),
            if (goldPrice != null) Text('• 金价：${goldPrice.round()} 元/克'),
            if (silverPrice != null) Text('• 银价：${silverPrice.round()} 元/克'),
            if (workPrice != null) Text('• 工费：${workPrice.round()} 元/克'),
            if (pieceWorkPrice != null) Text('• 件工费：${pieceWorkPrice.round()} 元/件'),
            const SizedBox(height: 8),
            Text('共 ${controller.itemList.length} 件商品将被更新'),
            const SizedBox(height: 8),
            const Text('此操作不可撤销，请确认是否继续？', style: TextStyle(color: Colors.red, fontWeight: FontWeight.w500)),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              controller.batchUpdatePrices(
                goldPrice: goldPrice,
                silverPrice: silverPrice,
                workPrice: workPrice,
                pieceWorkPrice: pieceWorkPrice,
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red[600],
              foregroundColor: Colors.white,
            ),
            child: const Text('确认执行'),
          ),
        ],
      ),
    );
  }

  /// 旧料回收功能
  void _oldMaterialRecycling() {
    controller.handleOldMaterialRecycling();
  }

  /// 退换货功能
  void _returnExchange() {
    controller.handleReturnExchange();
  }

  /// 单收工费功能
  void _singleWorkFee() {
    controller.handleSingleWorkFee();
  }

  /// 确认删除商品
  void _confirmRemoveItem(int index) {
    Get.dialog(
      AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppBorderStyles.largeBorderRadius),
        ),
        title: Row(
          children: [
            Icon(Icons.warning_amber_rounded, color: Colors.red[600]),
            const SizedBox(width: 8),
            const Text('确认删除'),
          ],
        ),
        content: SizedBox(
          width: 300,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red[200]!),
                ),
                child: Row(
                  children: [
                    Icon(Icons.delete_forever, color: Colors.red[600], size: 20),
                    const SizedBox(width: 8),
                    const Expanded(
                      child: Text(
                        '确定要删除这件商品吗？删除后将从出库单中移除。',
                        style: TextStyle(fontSize: 13, fontWeight: FontWeight.w500),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            style: TextButton.styleFrom(
              foregroundColor: Colors.grey[600],
            ),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              controller.removeItem(index);
              Get.back();
              Get.snackbar(
                '成功',
                '商品已从出库单中移除',
                snackPosition: SnackPosition.BOTTOM,
                backgroundColor: Colors.green,
                colorText: Colors.white,
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red[600],
              foregroundColor: Colors.white,
            ),
            child: const Text('确定删除'),
          ),
        ],
      ),
    );
  }

  /// 构建表单底部汇总区域 - 参考入库管理页面设计
  Widget _buildFormFooter() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
      decoration: BoxDecoration(
        color: Colors.white,
        border: const Border(
          top: AppBorderStyles.tableBorder,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            blurRadius: 3,
            offset: const Offset(0, -1),
          ),
        ],
      ),
      child: Row(
        children: [
          // 💰 汇总信息
          Icon(Icons.calculate, color: Colors.green[600], size: 20),
          const SizedBox(width: 8),
          const Text(
            '汇总信息',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),

          const SizedBox(width: 24),

          // 总件数（排除回收商品）
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
              border: Border.all(color: Colors.blue[200]!, width: 1),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.inventory_2, size: 14, color: Colors.blue[600]),
                const SizedBox(width: 4),
                const Text('总件数: ', style: TextStyle(fontSize: 13, color: Colors.black87)),
                Obx(() {
                  // 🎯 计算正常商品数量，排除回收商品和退换货商品
                  final normalItemCount = controller.itemList.where((item) =>
                    !_isRecyclingItem(item) && !_isReturnExchangeItem(item)).length;
                  return Text(
                    '$normalItemCount件',
                    style: TextStyle(
                      fontSize: 13,
                      fontWeight: FontWeight.w600,
                      color: Colors.blue[700],
                    ),
                  );
                }),
              ],
            ),
          ),

          const SizedBox(width: 16),

          // 金重
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.amber[50],
              borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
              border: Border.all(color: Colors.amber[200]!, width: 1),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.star, size: 14, color: Colors.amber[600]),
                const SizedBox(width: 4),
                const Text('金重: ', style: TextStyle(fontSize: 13, color: Colors.black87)),
                Obx(() => Text(
                  '${controller.totalGoldWeight.value.toStringAsFixed(2)}g',
                  style: TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w600,
                    color: Colors.amber[700],
                  ),
                )),
              ],
            ),
          ),

          const SizedBox(width: 16),

          // 银重
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
              border: Border.all(color: Colors.grey[300]!, width: 1),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.circle, size: 14, color: Colors.grey[600]),
                const SizedBox(width: 4),
                const Text('银重: ', style: TextStyle(fontSize: 13, color: Colors.black87)),
                Obx(() => Text(
                  '${controller.totalSilverWeight.value.toStringAsFixed(2)}g',
                  style: TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey[700],
                  ),
                )),
              ],
            ),
          ),

          const SizedBox(width: 16),

          // 总重量
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.orange[50],
              borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
              border: Border.all(color: Colors.orange[200]!, width: 1),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.scale, size: 14, color: Colors.orange[600]),
                const SizedBox(width: 4),
                const Text('总重量: ', style: TextStyle(fontSize: 13, color: Colors.black87)),
                Obx(() => Text(
                  '${controller.totalWeight.value.toStringAsFixed(2)}g',
                  style: TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w600,
                    color: Colors.orange[700],
                  ),
                )),
              ],
            ),
          ),

          const SizedBox(width: 16),

          // 总金额
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.green[50],
              borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
              border: Border.all(color: Colors.green[200]!, width: 1),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.attach_money, size: 14, color: Colors.green[600]),
                const SizedBox(width: 4),
                const Text('总金额: ', style: TextStyle(fontSize: 13, color: Colors.black87)),
                Obx(() => Text(
                  '¥${controller.totalAmount.value.round()}',
                  style: TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w600,
                    color: Colors.green[700],
                  ),
                )),
              ],
            ),
          ),

          const Spacer(),

          // 操作按钮
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 保存草稿按钮
              SizedBox(
                height: 32,
                child: OutlinedButton(
                  onPressed: () => _saveDraft(),
                  style: OutlinedButton.styleFrom(
                    side: BorderSide(color: Colors.grey[400]!, width: 1),
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
                    ),
                    textStyle: const TextStyle(fontSize: 13),
                  ),
                  child: const Text('保存草稿'),
                ),
              ),

              const SizedBox(width: 16),

              // 提交审核按钮
              SizedBox(
                height: 32,
                child: ElevatedButton(
                  onPressed: () => _submitForReview(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue[600],
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
                    ),
                    textStyle: const TextStyle(fontSize: 13),
                  ),
                  child: const Text('提交审核'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 保存草稿
  void _saveDraft() {
    // TODO: 实现保存草稿功能
    Get.snackbar('功能提示', '保存草稿功能开发中...');
  }

  /// 提交审核 - 改为直接弹出收款对话框
  void _submitForReview() {
    if (controller.itemList.isEmpty) {
      Get.snackbar('提示', '请先添加商品明细');
      return;
    }

    if (controller.selectedStoreId.value == 0) {
      Get.snackbar('提示', '请选择门店');
      return;
    }

    // 构建真实的出库单数据
    final stockOutData = {
      'order_no': controller.currentStockOut.value?.stockOutNo ?? 'CK${DateTime.now().year}${DateTime.now().month.toString().padLeft(2, '0')}${DateTime.now().day.toString().padLeft(2, '0')}_${DateTime.now().millisecondsSinceEpoch.toString().substring(8)}',
      'customer': controller.customerController.text.isNotEmpty ? controller.customerController.text : '客户',
      'sale_type': controller.selectedSaleType.value,
      'store_id': controller.selectedStoreId.value,
      'store_name': controller.currentStockOut.value?.store?.name ?? '未知门店',
      'store_address': controller.currentStockOut.value?.store?.address ?? '',
      'store_phone': controller.currentStockOut.value?.store?.phone ?? '',
      'remark': controller.remarkController.text,
    };

    // 构建真实的商品明细数据
    final itemsData = controller.itemList.map((item) {
      final jewelry = item.jewelry;
      return {
        'barcode': item.barcode,
        'name': jewelry?.name ?? '未知商品',
        'category': jewelry?.category?.name ?? '',
        'ring_size': jewelry?.ringSize ?? '',
        'gold_weight': (jewelry?.goldWeight ?? 0.0).toStringAsFixed(2),
        'silver_weight': (jewelry?.silverWeight ?? 0.0).toStringAsFixed(2),
        'total_weight': (jewelry?.totalWeight ?? 0.0).toStringAsFixed(2),
        'gold_price': (jewelry?.goldPrice ?? 0.0).toStringAsFixed(2),
        'silver_price': (jewelry?.silverPrice ?? 0.0).toStringAsFixed(2),
        'work_price': (jewelry?.workPrice ?? 0.0).toStringAsFixed(2),
        'piece_work_price': (jewelry?.pieceWorkPrice ?? 0.0).toStringAsFixed(2),
        'total_amount': item.amount.toStringAsFixed(2),
        'jewelry_id': item.jewelryId,
      };
    }).toList();

    // 根据收款方案.md：点击"提交审核"按钮直接弹出收款界面
    Get.dialog(
      PaymentDialog(
        totalAmount: controller.totalAmount.value,
        stockOutData: stockOutData,
        itemsData: itemsData,
        onPaymentConfirmed: (paymentData) {
          // 收款确认后，调用控制器的收款提交方法
          controller.submitWithPayment(paymentData);
        },
      ),
      barrierDismissible: false, // 防止点击外部关闭
    );
  }

  /// 创建白色背景的单元格装饰
  BoxDecoration _whiteCellDecoration() {
    return const BoxDecoration(
      color: Colors.white, // 始终使用白色背景
      border: Border(
        right: AppBorderStyles.tableBorder,
        bottom: AppBorderStyles.tableBorder,
      ),
    );
  }

  /// 创建回收商品的特殊单元格装饰
  /// 使用浅紫色背景来标识回收商品
  BoxDecoration _recyclingCellDecoration() {
    return BoxDecoration(
      color: Colors.purple[50], // 浅紫色背景，与回收按钮颜色呼应
      border: const Border(
        right: AppBorderStyles.tableBorder,
        bottom: AppBorderStyles.tableBorder,
      ),
    );
  }

  /// 创建退换货商品的特殊单元格装饰
  /// 使用浅红色背景来标识退换货商品
  BoxDecoration _returnExchangeCellDecoration() {
    return BoxDecoration(
      color: Colors.red[50], // 浅红色背景，与退换货按钮颜色呼应
      border: const Border(
        right: AppBorderStyles.tableBorder,
        bottom: AppBorderStyles.tableBorder,
      ),
    );
  }

  /// 构建可编辑的输入框单元格
  Widget _buildEditableCell({
    required double width,
    required String value,
    required Function(String) onChanged,
    TextAlign textAlign = TextAlign.center,
    TextInputType keyboardType = TextInputType.number,
    String? controllerId,
    bool isRecyclingItem = false, // 是否为回收商品
    bool isReturnExchangeItem = false, // 是否为退换货商品
    bool isWorkFeeItem = false, // 是否为工费项目
  }) {
    // 🔑 关键修复：使用持久化的TextEditingController，避免重复创建导致输入错误
    final uniqueId = controllerId ?? 'cell_${value.hashCode}_${DateTime.now().millisecondsSinceEpoch}';
    final textController = _getOrCreateController(uniqueId, value);

    return Container(
      width: width,
      height: 52,
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      alignment: Alignment.center,
      child: TextFormField(
        controller: textController, // 使用持久化的controller
        textAlign: textAlign,
        keyboardType: keyboardType,
        style: TextStyle(
          fontSize: 13, // 表单标签字体大小
          color: isRecyclingItem ? Colors.purple[700] :
                 isReturnExchangeItem ? Colors.red[700] :
                 isWorkFeeItem ? Colors.blue[700] : Colors.black87,
          fontFamily: keyboardType == TextInputType.number ? 'monospace' : null, // 数字字段使用等宽字体
          fontWeight: (isRecyclingItem || isReturnExchangeItem || isWorkFeeItem) ? FontWeight.w600 : FontWeight.normal,
        ),
        decoration: InputDecoration(
          contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6), // 圆角半径
            borderSide: BorderSide(
              color: isRecyclingItem ? Colors.purple[300]! :
                     isReturnExchangeItem ? Colors.red[300]! :
                     isWorkFeeItem ? Colors.blue[300]! : Colors.grey[300]!,
              width: 1
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: BorderSide(
              color: isRecyclingItem ? Colors.purple[300]! :
                     isReturnExchangeItem ? Colors.red[300]! :
                     isWorkFeeItem ? Colors.blue[300]! : Colors.grey[300]!,
              width: 1
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: BorderSide(
              color: isRecyclingItem ? Colors.purple[600]! :
                     isReturnExchangeItem ? Colors.red[600]! :
                     isWorkFeeItem ? Colors.blue[600]! : const Color(0xFF1E88E5),
              width: 2
            ),
          ),
          isDense: true,
        ),
        onChanged: onChanged,
        onFieldSubmitted: onChanged,
      ),
    );
  }

  /// 创建工费项目的特殊单元格装饰
  BoxDecoration _workFeeCellDecoration() {
    return BoxDecoration(
      color: Colors.blue[50], // 浅蓝色背景，与工费按钮颜色呼应
      border: const Border(
        right: AppBorderStyles.tableBorder,
        bottom: AppBorderStyles.tableBorder,
      ),
    );
  }


}
