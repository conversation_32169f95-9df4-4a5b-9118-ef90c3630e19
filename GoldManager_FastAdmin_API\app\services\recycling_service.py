"""
回收管理业务服务类

老板，这个服务类提供回收管理的核心业务逻辑：
1. 回收单的CRUD操作
2. 回收价格计算
3. 统计分析功能
4. 智能单号生成
5. 批量操作功能

完整的回收管理业务逻辑实现。
"""

import time
from datetime import datetime
from typing import List, Optional, Dict, Any, Tuple
from decimal import Decimal
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc

from ..models.recycling import Recycling, RecyclingItem
from ..models.jewelry import JewelryCategory
from ..models.store import Store
from ..models.admin import Admin
from ..models.member import Member
from ..schemas.recycling import (
    RecyclingCreate, RecyclingUpdate, RecyclingResponse,
    RecyclingDetailResponse, RecyclingItemResponse,
    RecyclingStatistics, RecyclingQueryParams,
    RecyclingStatusUpdate, RecyclingBatchUpdate,
    RecyclingPriceCalculation, RecyclingPriceResult
)


class RecyclingService:
    """回收管理服务类"""

    def __init__(self, db: Session):
        self.db = db

    def generate_recycle_no(self) -> str:
        """生成回收单号"""
        now = datetime.now()
        prefix = f"REC{now.year}{now.month:02d}{now.day:02d}"

        # 查询当天已有的回收单数量
        today_start = int(datetime(now.year, now.month, now.day).timestamp())
        today_end = today_start + 86400  # 24小时

        count = self.db.query(Recycling).filter(
            and_(
                Recycling.createtime >= today_start,
                Recycling.createtime < today_end
            )
        ).count()

        return f"{prefix}{count + 1:04d}"

    def calculate_price(self, calculation: RecyclingPriceCalculation) -> RecyclingPriceResult:
        """计算回收价格"""
        # 计算金价金额
        gold_amount = calculation.gold_weight * calculation.gold_price

        # 计算银价金额
        silver_amount = calculation.silver_weight * calculation.silver_price

        # 计算总金额
        total_amount = gold_amount + silver_amount

        # 计算折扣金额
        discount_value = total_amount * (Decimal('100.00') - calculation.discount_rate) / Decimal('100.00')

        # 计算折后金额
        discount_amount = total_amount - discount_value

        return RecyclingPriceResult(
            gold_amount=gold_amount,
            silver_amount=silver_amount,
            total_amount=total_amount,
            discount_amount=discount_amount,
            discount_value=discount_value
        )

    def create_recycling(self, recycling_data: RecyclingCreate, operator_id: int) -> RecyclingResponse:
        """创建回收单"""
        # 验证门店是否存在
        store = self.db.query(Store).filter(Store.id == recycling_data.store_id).first()
        if not store:
            raise ValueError(f"门店ID {recycling_data.store_id} 不存在")

        # 验证操作员是否存在
        operator = self.db.query(Admin).filter(Admin.id == operator_id).first()
        if not operator:
            raise ValueError(f"操作员ID {operator_id} 不存在")

        # 验证会员是否存在（如果提供了会员ID）
        if recycling_data.member_id:
            member = self.db.query(Member).filter(Member.id == recycling_data.member_id).first()
            if not member:
                raise ValueError(f"会员ID {recycling_data.member_id} 不存在")

        # 验证分类是否存在
        category_ids = [item.category_id for item in recycling_data.items]
        existing_categories = self.db.query(JewelryCategory).filter(JewelryCategory.id.in_(category_ids)).all()
        existing_category_ids = {c.id for c in existing_categories}

        for item in recycling_data.items:
            if item.category_id not in existing_category_ids:
                raise ValueError(f"分类ID {item.category_id} 不存在")

        # 计算总金额
        total_gold_weight = Decimal('0.00')
        total_silver_weight = Decimal('0.00')
        total_gold_amount = Decimal('0.00')
        total_silver_amount = Decimal('0.00')
        total_amount = Decimal('0.00')
        total_discount_amount = Decimal('0.00')

        for item_data in recycling_data.items:
            # 计算单项金额
            price_calc = RecyclingPriceCalculation(
                gold_weight=item_data.gold_weight or Decimal('0.00'),
                gold_price=item_data.gold_price or Decimal('0.00'),
                silver_weight=item_data.silver_weight or Decimal('0.00'),
                silver_price=item_data.silver_price or Decimal('0.00'),
                discount_rate=item_data.discount_rate or Decimal('100.00')
            )
            price_result = self.calculate_price(price_calc)

            # 累加总计
            total_gold_weight += item_data.gold_weight or Decimal('0.00')
            total_silver_weight += item_data.silver_weight or Decimal('0.00')
            total_gold_amount += price_result.gold_amount
            total_silver_amount += price_result.silver_amount
            total_amount += price_result.total_amount
            total_discount_amount += price_result.discount_amount

        # 创建回收单
        current_time = int(time.time())
        recycle_no = self.generate_recycle_no()

        recycling = Recycling(
            recycle_no=recycle_no,
            store_id=recycling_data.store_id,
            member_id=recycling_data.member_id,
            customer_name=recycling_data.customer_name,
            phone=recycling_data.phone,
            gold_weight=total_gold_weight,
            gold_price=Decimal('0.00'),  # 平均金价在明细中计算
            gold_amount=total_gold_amount,
            silver_weight=total_silver_weight,
            silver_price=Decimal('0.00'),  # 平均银价在明细中计算
            silver_amount=total_silver_amount,
            price=recycling_data.price or Decimal('0.00'),
            total_amount=total_amount,
            discount_amount=total_discount_amount,
            operator_id=operator_id,
            remark=recycling_data.remark,
            createtime=current_time,
            updatetime=current_time
        )

        self.db.add(recycling)
        self.db.flush()  # 获取ID

        # 创建回收明细
        for item_data in recycling_data.items:
            # 重新计算单项金额
            price_calc = RecyclingPriceCalculation(
                gold_weight=item_data.gold_weight or Decimal('0.00'),
                gold_price=item_data.gold_price or Decimal('0.00'),
                silver_weight=item_data.silver_weight or Decimal('0.00'),
                silver_price=item_data.silver_price or Decimal('0.00'),
                discount_rate=item_data.discount_rate or Decimal('100.00')
            )
            price_result = self.calculate_price(price_calc)

            recycling_item = RecyclingItem(
                recycling_id=recycling.id,
                name=item_data.name,
                category_id=item_data.category_id,
                gold_weight=item_data.gold_weight or Decimal('0.00'),
                gold_price=item_data.gold_price or Decimal('0.00'),
                gold_amount=price_result.gold_amount,
                silver_weight=item_data.silver_weight or Decimal('0.00'),
                silver_price=item_data.silver_price or Decimal('0.00'),
                silver_amount=price_result.silver_amount,
                total_amount=price_result.total_amount,
                discount_rate=item_data.discount_rate or Decimal('100.00'),
                discount_amount=price_result.discount_amount,
                remark=item_data.remark,
                createtime=current_time
            )
            self.db.add(recycling_item)

        self.db.commit()
        return self._convert_to_response(recycling)

    def get_recycling_list(
        self,
        page: int = 1,
        page_size: int = 20,
        params: Optional[RecyclingQueryParams] = None
    ) -> Tuple[List[RecyclingResponse], int]:
        """获取回收单列表"""
        query = self.db.query(Recycling).options(
            joinedload(Recycling.store),
            joinedload(Recycling.member),
            joinedload(Recycling.operator),
            joinedload(Recycling.items)  # 加载items关联数据以计算件数
        )

        # 应用筛选条件
        if params:
            if params.keyword:
                query = query.filter(
                    or_(
                        Recycling.recycle_no.like(f"%{params.keyword}%"),
                        Recycling.customer_name.like(f"%{params.keyword}%"),
                        Recycling.remark.like(f"%{params.keyword}%")
                    )
                )

            if params.store_id is not None:
                query = query.filter(Recycling.store_id == params.store_id)

            if params.status is not None:
                query = query.filter(Recycling.status == params.status)

            if params.operator_id is not None:
                query = query.filter(Recycling.operator_id == params.operator_id)

            if params.member_id is not None:
                query = query.filter(Recycling.member_id == params.member_id)

            if params.start_date:
                start_timestamp = int(datetime.strptime(params.start_date, "%Y-%m-%d").timestamp())
                query = query.filter(Recycling.createtime >= start_timestamp)

            if params.end_date:
                end_timestamp = int(datetime.strptime(params.end_date, "%Y-%m-%d").timestamp()) + 86400
                query = query.filter(Recycling.createtime < end_timestamp)

            if params.min_amount is not None:
                query = query.filter(Recycling.discount_amount >= params.min_amount)

            if params.max_amount is not None:
                query = query.filter(Recycling.discount_amount <= params.max_amount)

        # 获取总数
        total = query.count()

        # 分页查询
        recyclings = query.order_by(desc(Recycling.createtime)).offset((page - 1) * page_size).limit(page_size).all()

        # 转换为响应模型
        recycling_responses = [self._convert_to_response(recycling) for recycling in recyclings]

        return recycling_responses, total

    def get_recycling_by_id(self, recycling_id: int) -> Optional[RecyclingDetailResponse]:
        """根据ID获取回收单详情"""
        recycling = self.db.query(Recycling).options(
            joinedload(Recycling.store),
            joinedload(Recycling.member),
            joinedload(Recycling.operator),
            joinedload(Recycling.items).joinedload(RecyclingItem.category)
        ).filter(Recycling.id == recycling_id).first()

        if not recycling:
            return None

        return self._convert_to_detail_response(recycling)

    def get_recycling_by_no(self, recycle_no: str) -> Optional[RecyclingDetailResponse]:
        """根据单号获取回收单详情"""
        recycling = self.db.query(Recycling).options(
            joinedload(Recycling.store),
            joinedload(Recycling.member),
            joinedload(Recycling.operator),
            joinedload(Recycling.items).joinedload(RecyclingItem.category)
        ).filter(Recycling.recycle_no == recycle_no).first()

        if not recycling:
            return None

        return self._convert_to_detail_response(recycling)

    def update_recycling(self, recycling_id: int, update_data: RecyclingUpdate) -> Optional[RecyclingResponse]:
        """更新回收单"""
        recycling = self.db.query(Recycling).filter(Recycling.id == recycling_id).first()
        if not recycling:
            return None

        # 只有正常状态才能修改
        if recycling.status != 1:
            raise ValueError("只有正常状态的回收单才能修改")

        # 验证会员是否存在（如果提供了会员ID）
        if update_data.member_id is not None:
            if update_data.member_id > 0:
                member = self.db.query(Member).filter(Member.id == update_data.member_id).first()
                if not member:
                    raise ValueError(f"会员ID {update_data.member_id} 不存在")
            recycling.member_id = update_data.member_id if update_data.member_id > 0 else None

        # 更新字段
        if update_data.customer_name is not None:
            recycling.customer_name = update_data.customer_name

        if update_data.phone is not None:
            recycling.phone = update_data.phone

        if update_data.price is not None:
            recycling.price = update_data.price

        if update_data.remark is not None:
            recycling.remark = update_data.remark

        recycling.updatetime = int(time.time())

        self.db.commit()
        return self._convert_to_response(recycling)

    def delete_recycling(self, recycling_id: int) -> bool:
        """删除回收单"""
        recycling = self.db.query(Recycling).filter(Recycling.id == recycling_id).first()
        if not recycling:
            return False

        # 只有正常状态才能删除
        if recycling.status != 1:
            raise ValueError("只有正常状态的回收单才能删除")

        self.db.delete(recycling)
        self.db.commit()
        return True

    def update_recycling_status(self, recycling_id: int, status_data: RecyclingStatusUpdate) -> Optional[RecyclingResponse]:
        """更新回收单状态"""
        recycling = self.db.query(Recycling).filter(Recycling.id == recycling_id).first()
        if not recycling:
            return None

        recycling.status = status_data.status
        if status_data.remark:
            recycling.remark = status_data.remark
        recycling.updatetime = int(time.time())

        self.db.commit()
        return self._convert_to_response(recycling)

    def batch_update_status(self, batch_data: RecyclingBatchUpdate) -> bool:
        """批量更新回收单状态"""
        recyclings = self.db.query(Recycling).filter(Recycling.id.in_(batch_data.recycling_ids)).all()

        if len(recyclings) != len(batch_data.recycling_ids):
            raise ValueError("部分回收单不存在")

        current_time = int(time.time())

        for recycling in recyclings:
            recycling.status = batch_data.status
            if batch_data.remark:
                recycling.remark = batch_data.remark
            recycling.updatetime = current_time

        self.db.commit()
        return True

    def get_statistics(self) -> RecyclingStatistics:
        """获取回收统计信息"""
        # 基础统计
        total_recyclings = self.db.query(Recycling).count()
        normal_count = self.db.query(Recycling).filter(Recycling.status == 1).count()
        cancelled_count = self.db.query(Recycling).filter(Recycling.status == 0).count()

        # 物品统计
        total_items = self.db.query(RecyclingItem).count()

        # 重量和金额统计
        weight_amount_stats = self.db.query(
            func.sum(Recycling.gold_weight).label('total_gold_weight'),
            func.sum(Recycling.silver_weight).label('total_silver_weight'),
            func.sum(Recycling.total_amount).label('total_amount'),
            func.sum(Recycling.discount_amount).label('total_discount_amount'),
            func.avg(Recycling.discount_amount).label('average_amount'),
            func.max(Recycling.discount_amount).label('max_amount'),
            func.min(Recycling.discount_amount).label('min_amount')
        ).filter(Recycling.status == 1).first()

        total_gold_weight = weight_amount_stats.total_gold_weight or Decimal('0.000')
        total_silver_weight = weight_amount_stats.total_silver_weight or Decimal('0.000')
        total_amount = weight_amount_stats.total_amount or Decimal('0.00')
        total_discount_amount = weight_amount_stats.total_discount_amount or Decimal('0.00')
        average_amount = weight_amount_stats.average_amount or Decimal('0.00')
        max_amount = weight_amount_stats.max_amount or Decimal('0.00')
        min_amount = weight_amount_stats.min_amount or Decimal('0.00')

        # 状态分布
        status_distribution = {
            "正常": normal_count,
            "作废": cancelled_count
        }

        # 门店分布
        store_stats = self.db.query(
            Store.name,
            func.count(Recycling.id).label('count'),
            func.sum(Recycling.discount_amount).label('amount')
        ).join(Recycling).filter(Recycling.status == 1).group_by(Store.id, Store.name).all()

        store_distribution = [
            {
                "store_name": stat.name,
                "count": stat.count,
                "amount": float(stat.amount or 0)
            }
            for stat in store_stats
        ]

        # 分类分布
        category_stats = self.db.query(
            JewelryCategory.name,
            func.count(RecyclingItem.id).label('count'),
            func.sum(RecyclingItem.discount_amount).label('amount')
        ).join(RecyclingItem).join(Recycling).filter(Recycling.status == 1).group_by(
            JewelryCategory.id, JewelryCategory.name
        ).all()

        category_distribution = [
            {
                "category_name": stat.name,
                "count": stat.count,
                "amount": float(stat.amount or 0)
            }
            for stat in category_stats
        ]

        return RecyclingStatistics(
            total_recyclings=total_recyclings,
            normal_count=normal_count,
            cancelled_count=cancelled_count,
            total_items=total_items,
            total_gold_weight=total_gold_weight,
            total_silver_weight=total_silver_weight,
            total_amount=total_amount,
            total_discount_amount=total_discount_amount,
            status_distribution=status_distribution,
            store_distribution=store_distribution,
            category_distribution=category_distribution,
            average_amount=average_amount,
            max_amount=max_amount,
            min_amount=min_amount
        )

    def _convert_to_response(self, recycling: Recycling) -> RecyclingResponse:
        """转换为响应模型"""
        # 状态描述
        status_map = {0: "作废", 1: "正常"}
        status_text = status_map.get(recycling.status, "未知")

        # 计算物品件数
        item_count = len(recycling.items) if recycling.items else 0

        return RecyclingResponse(
            id=recycling.id,
            recycle_no=recycling.recycle_no,
            store_id=recycling.store_id,
            member_id=recycling.member_id,
            customer_name=recycling.customer_name,
            phone=recycling.phone,
            gold_weight=recycling.gold_weight,
            gold_price=recycling.gold_price,
            gold_amount=recycling.gold_amount,
            silver_weight=recycling.silver_weight,
            silver_price=recycling.silver_price,
            silver_amount=recycling.silver_amount,
            price=recycling.price,
            total_amount=recycling.total_amount,
            discount_amount=recycling.discount_amount,
            operator_id=recycling.operator_id,
            status=recycling.status,
            remark=recycling.remark,
            createtime=recycling.createtime,
            updatetime=recycling.updatetime,
            item_count=item_count,
            store_name=recycling.store.name if recycling.store else None,
            member_name=recycling.member.name if recycling.member else None,
            operator_name=recycling.operator.nickname if recycling.operator else None,
            status_text=status_text
        )

    def _convert_to_detail_response(self, recycling: Recycling) -> RecyclingDetailResponse:
        """转换为详情响应模型"""
        # 基础信息
        base_response = self._convert_to_response(recycling)

        # 转换明细
        items = []
        for item in recycling.items:
            item_response = RecyclingItemResponse(
                id=item.id,
                recycling_id=item.recycling_id,
                name=item.name,
                category_id=item.category_id,
                gold_weight=item.gold_weight,
                gold_price=item.gold_price,
                gold_amount=item.gold_amount,
                silver_weight=item.silver_weight,
                silver_price=item.silver_price,
                silver_amount=item.silver_amount,
                total_amount=item.total_amount,
                discount_rate=item.discount_rate,
                discount_amount=item.discount_amount,
                remark=item.remark,
                createtime=item.createtime,
                category_name=item.category.name if item.category else None
            )
            items.append(item_response)

        return RecyclingDetailResponse(
            **base_response.model_dump(),
            items=items
        )