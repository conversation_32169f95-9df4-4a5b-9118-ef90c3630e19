import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/utils/logger.dart';
import '../../../core/routes/app_pages.dart';
import '../../../core/constants/app_permissions.dart';
import '../../../models/stock/stock_in.dart';
import '../../../models/common/document_status.dart';
import '../../../models/store/store.dart';
import '../../../models/stock/batch_import_result.dart';
import '../../../services/stock_service.dart';
import '../../../services/store_service.dart';
import '../../../services/auth_service.dart';
import '../../../services/batch_import_service.dart';
import '../../dashboard/controllers/dashboard_controller.dart';
import '../views/stock_in_detail_dialog.dart';
import 'stock_tab_controller.dart';
import 'stock_in_form_controller.dart';
import '../../../widgets/loading_state.dart';

/// 入库管理控制器
class StockInController extends GetxController {
  // 服务
  final StockService _stockService = Get.find<StockService>();
  final StoreService _storeService = Get.find<StoreService>();
  final AuthService _authService = Get.find<AuthService>();
  final BatchImportService _batchImportService = Get.find<BatchImportService>();

  // 状态变量
  final RxBool isLoading = false.obs;
  final RxBool isSearching = false.obs;
  final RxList<StockIn> stockInList = <StockIn>[].obs;
  final RxList<Store> storeList = <Store>[].obs;

  // 筛选条件
  final TextEditingController searchController = TextEditingController();
  final Rx<DateTime?> startDate = Rx<DateTime?>(null);
  final Rx<DateTime?> endDate = Rx<DateTime?>(null);
  final RxInt selectedStoreId = 0.obs;
  final Rx<DocumentStatus?> selectedStatus = Rx<DocumentStatus?>(null);

  // 分页
  final RxInt currentPage = 1.obs;
  final RxInt totalPages = 1.obs;
  final int pageSize = 20;

  /// 检查是否有取消审核权限（管理员权限）
  bool get canCancelApprove => _authService.hasPermission(AppPermissions.stockAudit) ||
                               _authService.hasPermission(AppPermissions.superAdmin) ||
                               _authService.userRole.value == 'admin';

  @override
  void onInit() {
    super.onInit();
    LoggerService.d('StockInController 初始化');
    _updateDashboardNavigation();

    // 延迟初始化，确保权限已加载
    Future.delayed(const Duration(milliseconds: 100), () {
      if (_authService.hasPermission('store.view')) {
        fetchStores();
      } else {
        LoggerService.w('用户没有store.view权限，跳过获取门店列表');
      }

      if (_authService.hasPermission('stock.view')) {
        fetchStockInList();
      } else {
        LoggerService.w('用户没有stock.view权限，跳过获取入库单列表');
      }
    });
  }

  /// 更新仪表盘导航状态
  void _updateDashboardNavigation() {
    try {
      final dashboardController = Get.find<DashboardController>();
      dashboardController.updateSelectedIndex('/stock/in');
    } catch (e) {
      LoggerService.w('无法找到DashboardController: $e');
    }
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }

  /// 获取门店列表
  Future<void> fetchStores() async {
    try {
      isLoading.value = true;
      // 使用getAllStores确保获取所有门店，不受分页限制
      final stores = await _storeService.getAllStores();
      storeList.value = stores;

      // 如果用户不是管理员，自动选择用户所属门店
      if (_authService.userRole.value != 'admin' && !_authService.hasPermission('super.admin')) {
        selectedStoreId.value = _authService.storeId.value;
      }
    } catch (e) {
      LoggerService.e('获取门店列表失败', e);
      Get.snackbar(
        '错误',
        '获取门店列表失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// 获取入库单列表
  Future<void> fetchStockInList() async {
    try {
      isLoading.value = true;

      final Map<String, dynamic> params = {
        'page': currentPage.value,
        'page_size': pageSize,
      };

      // 添加筛选条件
      if (searchController.text.isNotEmpty) {
        params['keyword'] = searchController.text;
      }

      // 🔒 门店级别数据过滤逻辑
      // 后端会根据当前用户的门店信息自动过滤数据
      // 前端只需要在管理员账号时才允许选择门店
      final currentUserStoreId = _authService.storeId.value;
      if (currentUserStoreId > 0) {
        // 普通员工账号：不传递store_id参数，让后端自动过滤
        LoggerService.d('当前用户属于门店 $currentUserStoreId，后端将自动过滤数据');
      } else {
        // 管理员账号：可以选择特定门店查看
        if (selectedStoreId.value > 0) {
          params['store_id'] = selectedStoreId.value;
          LoggerService.d('管理员账号选择查看门店 ${selectedStoreId.value} 的数据');
        } else {
          LoggerService.d('管理员账号查看所有门店数据');
        }
      }

      if (selectedStatus.value != null) {
        params['status'] = selectedStatus.value!.value;
      }

      if (startDate.value != null) {
        params['start_date'] = '${startDate.value!.year}-${startDate.value!.month.toString().padLeft(2, '0')}-${startDate.value!.day.toString().padLeft(2, '0')}';
      }

      if (endDate.value != null) {
        params['end_date'] = '${endDate.value!.year}-${endDate.value!.month.toString().padLeft(2, '0')}-${endDate.value!.day.toString().padLeft(2, '0')}';
      }

      final result = await _stockService.getStockInList(params);

      stockInList.value = result.data;
      totalPages.value = result.lastPage;
    } catch (e) {
      LoggerService.e('获取入库单列表失败', e);
      Get.snackbar(
        '错误',
        '获取入库单列表失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// 搜索
  void search() {
    currentPage.value = 1;
    fetchStockInList();
  }

  /// 重置筛选条件
  void resetFilters() {
    searchController.clear();
    startDate.value = null;
    endDate.value = null;
    selectedStoreId.value = 0;
    selectedStatus.value = null;
    currentPage.value = 1;
    fetchStockInList();
  }

  /// 分页
  void goToPage(int page) {
    if (page < 1 || page > totalPages.value) return;
    currentPage.value = page;
    fetchStockInList();
  }

  /// 创建新的入库单 - 使用标签页方式打开
  void createNewStockIn() {
    try {
      // 尝试获取标签页控制器
      final stockTabController = Get.find<StockTabController>();

      // 使用标签页方式打开新建入库单表单
      stockTabController.openStockInForm();

    } catch (e) {
      LoggerService.e('无法找到StockTabController，使用传统页面跳转方式', e);

      // 如果没有找到标签页控制器，使用原来的路由方式作为备用
      Get.toNamed(Routes.STOCK_IN_FORM)?.then((value) {
        if (value == true) {
          fetchStockInList();
        }
      });
    }
  }

  /// 查看入库单详情
  void viewStockInDetail(int id) async {
    bool isLoadingDialogOpen = false;

    try {
      LoggerService.d('🔍 开始查看入库单详情: ID=$id');

      // 显示加载指示器
      Get.dialog(
        const Center(
          child: Card(
            child: Padding(
              padding: EdgeInsets.all(20),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('正在加载详情...'),
                ],
              ),
            ),
          ),
        ),
        barrierDismissible: false,
      );
      isLoadingDialogOpen = true;

      // 调用API获取详情
      final stockInDetail = await _stockService.getStockInById(id);
      LoggerService.d('✅ 入库单详情加载成功');

      // 安全关闭加载指示器
      if (isLoadingDialogOpen && Get.isDialogOpen == true) {
        Get.back();
        isLoadingDialogOpen = false;
      }

      // 显示详情对话框
      Get.dialog(
        StockInDetailDialog(stockIn: stockInDetail),
        barrierDismissible: true,
      );

    } catch (e) {
      LoggerService.e('❌ 获取入库单详情失败', e);

      // 安全关闭加载指示器
      if (isLoadingDialogOpen && Get.isDialogOpen == true) {
        Get.back();
        isLoadingDialogOpen = false;
      }

      Get.snackbar(
        '错误',
        '获取入库单详情失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 编辑入库单
  Future<void> editStockIn(int id) async {
    try {
      // 先显示全屏加载动画，防止UI卡顿，带超时处理
      Get.dialog(
        Container(
          color: Colors.transparent, // 透明背景
          child: LoadingState(
            text: '正在加载入库单数据...',
            backgroundColor: Colors.black54, // 对话框需要暗色背景
            showCard: true, // 显示白色卡片
            timeoutSeconds: 30, // 30秒超时
            onTimeout: () {
              LoggerService.e('❌ 编辑入库单加载超时: ID=$id');
              if (Get.isDialogOpen == true) {
                Get.back(); // 关闭加载对话框
              }
              Get.snackbar(
                '加载超时',
                '入库单数据加载超时，请检查网络连接或稍后重试',
                snackPosition: SnackPosition.BOTTOM,
                backgroundColor: Colors.orange,
                colorText: Colors.white,
              );
            },
          ),
        ),
        barrierDismissible: false,
        barrierColor: Colors.transparent, // 对话框自己处理背景色
      );
      
      // 预加载入库单数据（先获取基本信息），减轻后续页面加载压力
      try {
        LoggerService.d('🔄 预加载入库单基本信息: ID=$id');
        final stockIn = await _stockService.getStockInById(id);
        
        // 验证编辑权限
        if (!stockIn.canEdit) {
          // 关闭加载动画
          if (Get.isDialogOpen == true) {
            Get.back();
          }
          
          Get.snackbar(
            '提示',
            '该入库单已审核，无法编辑',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.orange,
            colorText: Colors.white,
          );
          return;
        }
        
        LoggerService.d('✅ 入库单基本信息加载成功，准备打开编辑页面');
      } catch (e) {
        LoggerService.e('❌ 预加载入库单信息失败', e);
        // 预加载失败不影响后续流程，仍然尝试打开编辑页面
      }
      
      // 预加载完成后，使用标签页控制器打开编辑页面
      final stockTabController = Get.find<StockTabController>();
      
      // 先预创建编辑表单控制器实例，便于后续数据加载
      final editFormTabId = 'stock_in_edit_$id';
      
      // 重要：先检查是否已存在此控制器，如果存在则移除它
      if (Get.isRegistered<StockInFormController>(tag: editFormTabId)) {
        Get.delete<StockInFormController>(tag: editFormTabId);
        LoggerService.d('🗑️ 移除已存在的控制器实例: $editFormTabId');
      }
      
      // 创建新的控制器实例并明确注册
      final editController = StockInFormController();
      Get.put<StockInFormController>(editController, tag: editFormTabId, permanent: true);
      
      // 设置编辑模式标志
      editController.isEditing.value = true;
      LoggerService.d('✅ 已创建新的表单控制器实例: $editFormTabId');
      
      // 关闭加载对话框，然后才执行页面跳转
      // 确保加载动画至少显示500毫秒，避免闪烁
      await Future.delayed(const Duration(milliseconds: 500));
      if (Get.isDialogOpen == true) {
        Get.back();
      }
      
      // 打开编辑入库单标签页（带加载动画）
      stockTabController.openStockInEditForm(id);
      
    } catch (e) {
      // 关闭可能存在的加载对话框
      if (Get.isDialogOpen == true) {
        Get.back();
      }
      
      LoggerService.e('无法找到StockTabController，使用传统页面跳转方式', e);
      
      // 如果没有找到标签页控制器，使用原来的方式
      // 也显示加载动画
      Get.dialog(
        const Center(
          child: CircularProgressIndicator(),
        ),
        barrierDismissible: false,
      );
      
      // 延迟关闭加载动画，再执行页面跳转
      await Future.delayed(const Duration(milliseconds: 300));
      Get.back();
      
      // 使用传统导航
      Get.toNamed(Routes.STOCK_IN_FORM, arguments: {'id': id})?.then((value) {
        if (value == true) {
          fetchStockInList();
        }
      });
    }
  }

  /// 删除入库单
  Future<void> deleteStockIn(int id) async {
    // 获取入库单信息用于确认对话框
    final stockIn = stockInList.firstWhereOrNull((item) => item.id == id);
    final stockInNo = stockIn?.stockInNo ?? 'ID: $id';

    final confirmed = await Get.dialog<bool>(
      AlertDialog(
        title: Row(
          children: [
            Icon(Icons.warning_amber_rounded, color: Colors.red[600]),
            const SizedBox(width: 8),
            const Text('确认删除'),
          ],
        ),
        content: SizedBox(
          width: 350,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red[200]!),
                ),
                child: Row(
                  children: [
                    Icon(Icons.delete_forever, color: Colors.red[600], size: 20),
                    const SizedBox(width: 8),
                    const Expanded(
                      child: Text(
                        '此操作将永久删除入库单及其所有明细数据，无法恢复！',
                        style: TextStyle(fontSize: 13, fontWeight: FontWeight.w500),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              RichText(
                text: TextSpan(
                  style: const TextStyle(color: Colors.black87, fontSize: 14),
                  children: [
                    const TextSpan(text: '确定要删除入库单 '),
                    TextSpan(
                      text: stockInNo,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.blue,
                      ),
                    ),
                    const TextSpan(text: ' 吗？'),
                  ],
                ),
              ),
              if (stockIn != null) ...[
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('门店: ${stockIn.store?.name ?? '未知门店'}'),
                      Text('金额: ¥${stockIn.totalAmount.toStringAsFixed(2)}'),
                      Text('状态: ${getStatusText(stockIn.status)}'),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red[600],
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
            ),
            onPressed: () => Get.back(result: true),
            child: const Text('确认删除'),
          ),
        ],
      ),
    ) ?? false;

    if (!confirmed) return;

    try {
      isLoading.value = true;

      LoggerService.d('🗑️ 开始删除入库单: ID=$id, 单号=$stockInNo');

      await _stockService.deleteStockIn(id);

      LoggerService.d('✅ 入库单删除成功');

      Get.snackbar(
        '删除成功',
        '入库单 $stockInNo 已成功删除',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green[600],
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
        icon: const Icon(Icons.check_circle, color: Colors.white),
      );

      // 刷新列表
      fetchStockInList();

    } catch (e) {
      LoggerService.e('删除入库单失败', e);

      String errorMessage = '删除失败';
      if (e.toString().contains('404')) {
        errorMessage = '入库单不存在或已被删除';
      } else if (e.toString().contains('403')) {
        errorMessage = '权限不足，无法删除此入库单';
      } else if (e.toString().contains('400')) {
        errorMessage = '入库单状态不允许删除';
      } else {
        errorMessage = '删除失败: ${e.toString()}';
      }

      Get.snackbar(
        '删除失败',
        errorMessage,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red[600],
        colorText: Colors.white,
        duration: const Duration(seconds: 4),
        icon: const Icon(Icons.error, color: Colors.white),
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// 审核入库单
  Future<void> approveStockIn(int id, bool isApproved, String? reason) async {
    // 获取入库单信息用于反馈
    final stockIn = stockInList.firstWhereOrNull((item) => item.id == id);
    final stockInNo = stockIn?.stockInNo ?? 'ID: $id';
    final actionText = isApproved ? '通过' : '拒绝';

    LoggerService.d('🎯 [控制器审核] 开始审核流程');
    LoggerService.d('   入库单ID: $id');
    LoggerService.d('   入库单号: $stockInNo');
    LoggerService.d('   审核动作: $actionText');
    LoggerService.d('   审核备注: ${reason ?? "无"}');

    try {
      isLoading.value = true;

      // 获取当前用户ID作为审核员ID
      final auditorId = _authService.userId.value;
      LoggerService.d('👤 [控制器审核] 获取审核员信息');
      LoggerService.d('   当前用户ID: $auditorId');
      LoggerService.d('   当前用户名: ${_authService.userName.value}');

      LoggerService.d('📡 [控制器审核] 调用API服务...');
      await _stockService.approveStockIn(id, isApproved, reason, auditorId);

      LoggerService.d('✅ [控制器审核] API调用成功');

      Get.snackbar(
        '审核$actionText',
        '入库单 $stockInNo 已$actionText审核',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: isApproved ? Colors.green[600] : Colors.orange[600],
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
        icon: Icon(
          isApproved ? Icons.check_circle : Icons.cancel,
          color: Colors.white,
        ),
      );

      LoggerService.d('🔄 [控制器审核] 刷新入库单列表...');
      // 刷新列表
      fetchStockInList();

    } catch (e) {
      LoggerService.e('❌ [控制器审核] 审核失败', e);

      String errorMessage = '审核失败';
      if (e.toString().contains('404')) {
        errorMessage = '入库单不存在或已被删除';
      } else if (e.toString().contains('403')) {
        errorMessage = '权限不足，无法审核此入库单';
      } else if (e.toString().contains('401')) {
        errorMessage = '身份验证失败，请重新登录';
      } else if (e.toString().contains('400')) {
        errorMessage = '入库单状态不允许审核';
      } else if (e.toString().contains('无法获取当前用户信息')) {
        errorMessage = '无法获取当前用户信息，请重新登录';
      } else {
        errorMessage = '审核失败: ${e.toString()}';
      }

      LoggerService.e('💬 [控制器审核] 显示错误消息: $errorMessage');

      Get.snackbar(
        '审核失败',
        errorMessage,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red[600],
        colorText: Colors.white,
        duration: const Duration(seconds: 4),
        icon: const Icon(Icons.error, color: Colors.white),
      );
    } finally {
      isLoading.value = false;
      LoggerService.d('🏁 [控制器审核] 审核流程结束');
    }
  }

  /// 取消审核入库单
  Future<void> cancelApproveStockIn(int id, String reason) async {
    try {
      isLoading.value = true;
      LoggerService.d('🔄 [控制器取消审核] 开始取消审核入库单: $id');

      // 获取入库单信息用于反馈
      final stockIn = stockInList.firstWhereOrNull((item) => item.id == id);
      final stockInNo = stockIn?.stockInNo ?? 'ID: $id';

      LoggerService.d('📋 [控制器取消审核] 入库单信息:');
      LoggerService.d('   入库单号: $stockInNo');
      LoggerService.d('   当前状态: ${stockIn?.status}');
      LoggerService.d('   取消原因: $reason');

      // 用户身份验证
      final auditorId = _authService.userId.value;
      LoggerService.d('👤 [控制器取消审核] 审核员信息:');
      LoggerService.d('   当前用户ID: $auditorId');
      LoggerService.d('   当前用户名: ${_authService.userName.value}');

      LoggerService.d('📡 [控制器取消审核] 调用API服务...');
      await _stockService.cancelApproveStockIn(id, reason, auditorId);

      LoggerService.d('✅ [控制器取消审核] API调用成功');

      Get.snackbar(
        '取消审核成功',
        '入库单 $stockInNo 已取消审核',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange[600],
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
        icon: const Icon(Icons.cancel, color: Colors.white),
      );

      LoggerService.d('🔄 [控制器取消审核] 刷新入库单列表...');
      // 刷新列表
      fetchStockInList();

    } catch (e) {
      LoggerService.e('❌ [控制器取消审核] 取消审核失败', e);

      String errorMessage = '取消审核失败';
      if (e.toString().contains('404')) {
        errorMessage = '入库单不存在或已被删除';
      } else if (e.toString().contains('403')) {
        errorMessage = '权限不足，无法取消审核此入库单';
      } else if (e.toString().contains('401')) {
        errorMessage = '身份验证失败，请重新登录';
      } else if (e.toString().contains('400')) {
        errorMessage = '入库单状态不允许取消审核';
      } else {
        errorMessage = '取消审核失败: ${e.toString()}';
      }

      Get.snackbar(
        '取消审核失败',
        errorMessage,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red[600],
        colorText: Colors.white,
        duration: const Duration(seconds: 4),
        icon: const Icon(Icons.error, color: Colors.white),
      );
    } finally {
      isLoading.value = false;
      LoggerService.d('🏁 [控制器取消审核] 取消审核流程结束');
    }
  }

  /// 获取状态显示文本
  String getStatusText(DocumentStatus status) {
    switch (status) {
      case DocumentStatus.draft:
        return '草稿';
      case DocumentStatus.pending:
        return '待审核';
      case DocumentStatus.approved:
        return '已通过';
      case DocumentStatus.rejected:
        return '已拒绝';
      default:
        return '未知';
    }
  }

  /// 获取状态显示颜色
  Color getStatusColor(DocumentStatus status) {
    switch (status) {
      case DocumentStatus.draft:
        return Colors.grey;
      case DocumentStatus.pending:
        return Colors.orange;
      case DocumentStatus.approved:
        return Colors.green;
      case DocumentStatus.rejected:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  /// 显示批量导入对话框
  Future<void> showBatchImportDialog() async {
    try {
      // 选择文件
      final file = await _batchImportService.pickFile();
      if (file == null) {
        return;
      }

      // 显示加载对话框
      Get.dialog(
        const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('正在解析文件...'),
            ],
          ),
        ),
        barrierDismissible: false,
      );

      // 解析文件
      BatchImportPreviewData? previewData;
      final fileName = file.path.toLowerCase();

      if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls')) {
        previewData = await _batchImportService.parseExcelFile(file);
      } else if (fileName.endsWith('.csv')) {
        previewData = await _batchImportService.parseCsvFile(file);
      }

      // 关闭加载对话框
      Get.back();

      if (previewData == null) {
        Get.snackbar(
          '错误',
          '文件解析失败，请检查文件格式',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return;
      }

      // 显示预览对话框
      _showImportPreviewDialog(previewData);

    } catch (e) {
      // 关闭可能存在的加载对话框
      if (Get.isDialogOpen == true) {
        Get.back();
      }

      LoggerService.e('批量导入失败', e);
      Get.snackbar(
        '错误',
        '批量导入失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 显示导入预览对话框
  void _showImportPreviewDialog(BatchImportPreviewData previewData) {
    Get.dialog(
      AlertDialog(
        title: const Text('导入预览'),
        content: SizedBox(
          width: Get.width * 0.8,
          height: Get.height * 0.6,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 统计信息
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text('总行数: ${previewData.totalRowCount}'),
                    ),
                    Expanded(
                      child: Text(
                        '有效: ${previewData.validRowCount}',
                        style: TextStyle(color: Colors.green[600]),
                      ),
                    ),
                    Expanded(
                      child: Text(
                        '无效: ${previewData.invalidRowCount}',
                        style: TextStyle(color: Colors.red[600]),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              // 数据预览表格
              Expanded(
                child: SingleChildScrollView(
                  child: DataTable(
                    columnSpacing: 12,
                    columns: previewData.headers
                        .map((header) => DataColumn(label: Text(header)))
                        .toList(),
                    rows: previewData.rows.take(10).map((row) {
                      return DataRow(
                        cells: row
                            .map((cell) => DataCell(Text(cell)))
                            .toList(),
                      );
                    }).toList(),
                  ),
                ),
              ),
              if (previewData.rows.length > 10)
                const Padding(
                  padding: EdgeInsets.only(top: 8),
                  child: Text(
                    '注：仅显示前10行数据',
                    style: TextStyle(color: Colors.grey),
                  ),
                ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: previewData.validRowCount > 0
                ? () => _executeImport(previewData)
                : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green[600],
              foregroundColor: Colors.white,
            ),
            child: const Text('确认导入'),
          ),
        ],
      ),
    );
  }

  /// 执行导入
  Future<void> _executeImport(BatchImportPreviewData previewData) async {
    Get.back(); // 关闭预览对话框

    // 显示进度对话框
    Get.dialog(
      AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            const Text('正在导入数据...'),
            const SizedBox(height: 8),
            Text('共 ${previewData.validRowCount} 条有效数据'),
          ],
        ),
      ),
      barrierDismissible: false,
    );

    try {
      // 执行导入
      final result = await _batchImportService.executeImport(
        previewData,
        (importData) async {
          // 这里调用实际的导入API
          // TODO: 实现批量创建入库单的API调用
          LoggerService.d('导入数据: ${importData.length} 条');
        },
      );

      // 关闭进度对话框
      Get.back();

      // 显示结果对话框
      _showImportResultDialog(result);

      // 刷新列表
      fetchStockInList();

    } catch (e) {
      // 关闭进度对话框
      Get.back();

      LoggerService.e('执行导入失败', e);
      Get.snackbar(
        '错误',
        '导入失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 显示导入结果对话框
  void _showImportResultDialog(BatchImportResult result) {
    Get.dialog(
      AlertDialog(
        title: Row(
          children: [
            Icon(
              result.isAllSuccess ? Icons.check_circle : Icons.warning,
              color: result.isAllSuccess ? Colors.green : Colors.orange,
            ),
            const SizedBox(width: 8),
            const Text('导入完成'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('总数量: ${result.totalCount}'),
            Text(
              '成功: ${result.successCount}',
              style: TextStyle(color: Colors.green[600]),
            ),
            Text(
              '失败: ${result.failureCount}',
              style: TextStyle(color: Colors.red[600]),
            ),
            Text('耗时: ${result.durationSeconds} 秒'),
            if (result.errors.isNotEmpty) ...[
              const SizedBox(height: 16),
              const Text('错误详情:', style: TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              SizedBox(
                height: 100,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: result.errors.map((error) {
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 4),
                        child: Text(
                          '第${error.rowIndex}行: ${error.errorMessage}',
                          style: const TextStyle(fontSize: 12),
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
}