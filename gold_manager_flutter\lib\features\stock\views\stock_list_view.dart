import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/theme/app_theme.dart';
import '../../dashboard/controllers/dashboard_controller.dart';
import '../controllers/stock_tab_controller.dart';

/// 库存列表页面
class StockListView extends GetView<DashboardController> {
  /// 构造函数
  const StockListView({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppTheme.backgroundColor,
      child: Column(
        children: [
          // 页面标题区域
          _buildHeaderSection(),
          // 主要内容区域
          Expanded(
            child: _buildBody(context),
          ),
        ],
      ),
    );
  }

  /// 构建页面头部区域
  Widget _buildHeaderSection() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      color: Colors.white,
      child: Row(
        children: [
          const Text(
            '库存管理',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const Spacer(),
          // 快捷操作按钮
          ElevatedButton.icon(
            icon: const Icon(Icons.dashboard, size: 16),
            label: const Text('快捷操作'),
            onPressed: _navigateToMenu,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建页面主体
  Widget _buildBody(BuildContext context) {
    // 这里可以使用 Obx 监听控制器状态，目前返回一个固定的布局
    return Column(
      children: [
        _buildStockSummary(context),
        Expanded(
          child: _buildStockOptions(context),
        ),
      ],
    );
  }

  /// 构建库存概要信息卡片
  Widget _buildStockSummary(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '库存概览',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16.0),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildSummaryItem(
                  context,
                  title: '总库存',
                  value: '452件',
                  icon: Icons.inventory,
                  color: Colors.blue,
                ),
                _buildSummaryItem(
                  context,
                  title: '总价值',
                  value: '¥1,289,500',
                  icon: Icons.monetization_on,
                  color: Colors.green,
                ),
                _buildSummaryItem(
                  context,
                  title: '预警',
                  value: '3件',
                  icon: Icons.warning,
                  color: Colors.orange,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建库存选项网格
  Widget _buildStockOptions(BuildContext context) {
    final options = [
      {
        'title': '入库管理',
        'icon': Icons.add_box,
        'color': Colors.green,
        'route': '/stock/in',
      },
      {
        'title': '出库管理',
        'icon': Icons.indeterminate_check_box,
        'color': Colors.red,
        'route': '/stock/out',
      },
      {
        'title': '库存查询',
        'icon': Icons.search,
        'color': Colors.blue,
        'route': '/stock/query',
      },
      {
        'title': '库存盘点',
        'icon': Icons.content_paste_search,
        'color': Colors.purple,
        'route': '/stock/inventory',
      },
      {
        'title': '库存调拨',
        'icon': Icons.swap_horiz,
        'color': Colors.orange,
        'route': '/stock/transfer',
      },
      {
        'title': '库存报表',
        'icon': Icons.bar_chart,
        'color': Colors.teal,
        'route': '/stock/report',
      },
    ];

    // 根据屏幕宽度动态计算列数
    final screenWidth = MediaQuery.of(context).size.width;
    int crossAxisCount;
    double childAspectRatio;

    if (screenWidth > 1200) {
      // 大屏幕：4列，更扁平的按钮
      crossAxisCount = 4;
      childAspectRatio = 2.5;
    } else if (screenWidth > 800) {
      // 中等屏幕：3列
      crossAxisCount = 3;
      childAspectRatio = 2.2;
    } else {
      // 小屏幕：2列
      crossAxisCount = 2;
      childAspectRatio = 2.0;
    }

    return GridView.builder(
      padding: const EdgeInsets.all(16.0),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: 12.0, // 使用UI规范中的12px间距
        mainAxisSpacing: 12.0, // 使用UI规范中的12px间距
        childAspectRatio: childAspectRatio, // 动态调整宽高比
      ),
      itemCount: options.length,
      itemBuilder: (context, index) {
        final option = options[index];
        return _buildOptionCard(
          context,
          title: option['title'] as String,
          icon: option['icon'] as IconData,
          color: option['color'] as Color,
          route: option['route'] as String,
        );
      },
    );
  }

  /// 构建概要项目
  Widget _buildSummaryItem(
    BuildContext context, {
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 8.0),
        Text(
          value,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4.0),
        Text(
          title,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  /// 构建选项卡片
  Widget _buildOptionCard(
    BuildContext context, {
    required String title,
    required IconData icon,
    required Color color,
    required String route,
  }) {
    return Card(
      elevation: 1.0, // 进一步减少阴影，使按钮更轻量
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0), // 使用UI规范中的12px圆角
      ),
      child: InkWell(
        onTap: () => _handleOptionTap(route),
        borderRadius: BorderRadius.circular(12.0),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 8.0), // 使用UI规范中的8px内边距
          child: Row( // 水平布局，图标和文字并排
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: color,
                size: 16, // 使用UI规范中的16px图标大小
              ),
              const SizedBox(width: 8.0), // 使用UI规范中的8px间距
              Flexible( // 使用Flexible避免文字溢出
                child: Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14, // 使用UI规范中的次要文本大小14px
                    fontWeight: FontWeight.w500, // 保持适中的字重
                    color: Color(0xFF212121), // 使用UI规范中的主要文本色
                  ),
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis, // 防止文字溢出
                  maxLines: 1, // 限制为单行显示
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 处理选项点击事件
  void _handleOptionTap(String route) {
    // 尝试获取库存标签页控制器
    try {
      final stockTabController = Get.find<StockTabController>();
      stockTabController.openTabByRoute(route);
    } catch (e) {
      // 如果没有找到标签页控制器，使用原来的方式
      switch (route) {
        case '/stock/in':
          controller.changeStockSubPage(1); // 入库管理
          break;
        case '/stock/out':
          controller.changeStockSubPage(2); // 出库管理
          break;
        case '/stock/query':
          controller.changeStockSubPage(3); // 库存查询
          break;
        case '/stock/inventory':
          Get.snackbar('提示', '库存盘点功能正在开发中');
          break;
        case '/stock/transfer':
          Get.snackbar('提示', '库存调拨功能正在开发中');
          break;
        case '/stock/report':
          Get.snackbar('提示', '库存报表功能正在开发中');
          break;
        default:
          Get.snackbar('提示', '功能正在开发中');
      }
    }
  }

  /// 导航到库存菜单
  void _navigateToMenu() {
    // 显示库存操作菜单
    showModalBottomSheet(
      context: Get.context!,
      builder: (context) => ListView(
        shrinkWrap: true,
        children: [
          ListTile(
            leading: const Icon(Icons.add_box, color: Colors.green),
            title: const Text('新建入库单'),
            onTap: () {
              Navigator.pop(context);
              Get.toNamed('/stock/in/form');
            },
          ),
          ListTile(
            leading: const Icon(Icons.indeterminate_check_box, color: Colors.red),
            title: const Text('新建出库单'),
            onTap: () {
              Navigator.pop(context);
              Get.toNamed('/stock/out/form');
            },
          ),
          ListTile(
            leading: const Icon(Icons.swap_horiz, color: Colors.orange),
            title: const Text('新建调拨单'),
            onTap: () {
              Navigator.pop(context);
              Get.toNamed('/stock/transfer/form');
            },
          ),
          ListTile(
            leading: const Icon(Icons.content_paste_search, color: Colors.purple),
            title: const Text('新建盘点单'),
            onTap: () {
              Navigator.pop(context);
              Get.toNamed('/stock/inventory/form');
            },
          ),
        ],
      ),
    );
  }
}