# 黄金珠宝管理系统 - 后端API调用详细说明

## 📋 目录
- [系统概述](#系统概述)
- [认证机制](#认证机制)
- [API基础信息](#api基础信息)
- [通用响应格式](#通用响应格式)
- [API接口详细说明](#api接口详细说明)
  - [1. 系统信息](#1-系统信息)
  - [2. 认证管理](#2-认证管理)
  - [3. 仪表板统计](#3-仪表板统计)
  - [4. 商品管理](#4-商品管理)
  - [5. 门店管理](#5-门店管理)
  - [6. 管理员管理](#6-管理员管理)
  - [7. 会员管理](#7-会员管理)
  - [8. 库存管理](#8-库存管理)
  - [9. 数据导入导出](#9-数据导入导出)
- [错误处理](#错误处理)
- [调用示例](#调用示例)

## 系统概述

黄金珠宝管理系统API是基于FastAPI框架开发的现代化RESTful API，提供完整的珠宝店铺管理功能。

### 🔧 技术特性
- 基于FastAdmin数据库结构
- RESTful API设计
- JWT认证机制
- 自动数据验证和序列化
- 完整的错误处理机制
- 详细的操作日志记录

### 📋 主要功能模块
- **商品管理**: 商品信息的增删改查、分类管理
- **库存管理**: 入库、出库、退货、盘点管理
- **门店管理**: 多门店支持、调拨管理
- **会员管理**: 会员信息管理、积分系统
- **回收管理**: 旧料回收、金银分离处理

## 认证机制

### JWT Bearer Token认证
系统采用JWT Bearer Token认证方式，所有需要认证的接口都需要在请求头中包含认证信息。

```http
Authorization: Bearer <your_jwt_token>
```

### 获取Token
通过登录接口获取访问令牌：

```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "username": "your_username",
  "password": "your_password",
  "remember_me": false
}
```

## API基础信息

- **基础URL**: `http://localhost:8000`
- **API版本**: `v1`
- **API前缀**: `/api/v1`
- **内容类型**: `application/json`
- **字符编码**: `UTF-8`

## 通用响应格式

### 成功响应
```json
{
  "success": true,
  "message": "操作成功",
  "data": {},
  "code": 200
}
```

### 分页响应
```json
{
  "success": true,
  "message": "获取数据成功",
  "data": [],
  "pagination": {
    "page": 1,
    "page_size": 20,
    "total": 100,
    "pages": 5
  },
  "code": 200
}
```

### 错误响应
```json
{
  "detail": "错误详情信息"
}
```

## API接口详细说明

## 1. 系统信息

### 1.1 系统欢迎页面
获取系统基本信息和可用服务。

**接口地址**: `GET /`

**请求参数**: 无

**响应示例**:
```json
{
  "message": "欢迎使用黄金珠宝管理系统 API",
  "project": "黄金珠宝管理系统 API",
  "version": "1.0.0",
  "docs": "/docs",
  "redoc": "/redoc"
}
```

### 1.2 健康检查
检查API服务是否正常运行。

**接口地址**: `GET /health`

**请求参数**: 无

**响应示例**:
```json
{
  "status": "healthy",
  "project": "黄金珠宝管理系统 API",
  "version": "1.0.0"
}
```

## 2. 认证管理

### 2.1 用户登录
用户登录获取访问令牌。

**接口地址**: `POST /api/v1/auth/login`

**请求参数**:
```json
{
  "username": "string",      // 用户名 (必填)
  "password": "string",      // 密码 (必填)
  "remember_me": false       // 是否记住登录状态 (可选)
}
```

**响应示例**:
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "expires_in": 3600,
  "user": {
    "id": 1,
    "username": "admin",
    "nickname": "管理员",
    "email": "<EMAIL>",
    "store_id": 1,
    "permissions": ["admin.view", "admin.create"]
  }
}
```

### 2.2 用户登出
注销当前用户的登录状态。

**接口地址**: `POST /api/v1/auth/logout`

**认证**: 需要Bearer Token

**请求参数**: 无

**响应示例**:
```json
{
  "message": "登出成功"
}
```

### 2.3 刷新令牌
使用刷新令牌获取新的访问令牌。

**接口地址**: `POST /api/v1/auth/refresh`

**请求参数**:
```json
{
  "refresh_token": "string"  // 刷新令牌 (必填)
}
```

**响应示例**:
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "expires_in": 3600
}
```

### 2.4 获取当前用户信息
获取当前登录用户的详细信息。

**接口地址**: `GET /api/v1/auth/me`

**认证**: 需要Bearer Token

**请求参数**: 无

**响应示例**:
```json
{
  "id": 1,
  "username": "admin",
  "nickname": "管理员",
  "email": "<EMAIL>",
  "mobile": "13800138000",
  "store_id": 1,
  "store_name": "旗舰店",
  "permissions": ["admin.view", "admin.create", "jewelry.view"],
  "roles": ["超级管理员"],
  "status": "normal",
  "last_login_time": 1640995200,
  "last_login_ip": "*************"
}
```

### 2.5 修改密码
修改当前用户密码。

**接口地址**: `POST /api/v1/auth/change-password`

**认证**: 需要Bearer Token

**请求参数**:
```json
{
  "old_password": "string",      // 原密码 (必填)
  "new_password": "string",      // 新密码 (必填)
  "confirm_password": "string"   // 确认新密码 (必填)
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "密码修改成功",
  "code": 200
}
```

### 2.6 获取登录尝试信息
获取指定用户名的登录尝试信息。

**接口地址**: `GET /api/v1/auth/login-attempts/{username}`

**路径参数**:
- `username`: 用户名

**请求参数**: 无

**响应示例**:
```json
{
  "username": "admin",
  "total_attempts": 5,
  "failed_attempts": 2,
  "last_attempt_time": 1640995200,
  "last_attempt_ip": "*************",
  "is_locked": false,
  "lock_until": null
}
```

## 3. 仪表板统计

### 3.1 获取仪表板概览
获取仪表板概览数据，包含商品总数、门店总数、会员总数等关键指标。

**接口地址**: `GET /api/v1/dashboard/overview`

**认证**: 需要Bearer Token

**请求参数**: 无

**响应示例**:
```json
{
  "total_jewelry": 1250,
  "total_stores": 5,
  "total_members": 3200,
  "today_sales": "125000.00",
  "month_sales": "2850000.00",
  "inventory_value": "15600000.00",
  "pending_orders": 23,
  "low_stock_alerts": 8
}
```

### 3.2 获取销售统计
获取销售统计数据，包含销售总额、订单数量、平均客单价等。

**接口地址**: `GET /api/v1/dashboard/sales-statistics`

**认证**: 需要Bearer Token

**查询参数**:
- `start_date`: 开始日期 (YYYY-MM-DD) (可选)
- `end_date`: 结束日期 (YYYY-MM-DD) (可选)
- `store_id`: 门店ID (可选)

**响应示例**:
```json
{
  "total_sales": "2850000.00",
  "total_orders": 1250,
  "average_order_value": "2280.00",
  "growth_rate": 15.6,
  "top_products": [
    {
      "product_id": 1,
      "product_name": "黄金项链",
      "sales_amount": "125000.00",
      "sales_count": 25
    }
  ],
  "daily_trend": [
    {
      "date": "2024-01-01",
      "amount": "85000.00",
      "count": 35
    }
  ]
}
```

### 3.3 获取库存统计
获取库存统计数据，包含库存总值、商品总数、低库存预警等。

**接口地址**: `GET /api/v1/dashboard/inventory-statistics`

**认证**: 需要Bearer Token

**查询参数**:
- `store_id`: 门店ID (可选)

**响应示例**:
```json
{
  "total_value": "15600000.00",
  "total_count": 1250,
  "low_stock_count": 8,
  "in_stock_count": 1180,
  "out_stock_count": 62,
  "turnover_rate": 2.5,
  "category_distribution": [
    {
      "category_name": "黄金首饰",
      "count": 650,
      "value": "8500000.00"
    }
  ]
}
```

### 3.4 获取会员统计
获取会员统计数据，包含会员总数、新增会员、活跃会员等。

**接口地址**: `GET /api/v1/dashboard/member-statistics`

**认证**: 需要Bearer Token

**查询参数**:
- `start_date`: 开始日期 (YYYY-MM-DD) (可选)
- `end_date`: 结束日期 (YYYY-MM-DD) (可选)

**响应示例**:
```json
{
  "total_members": 3200,
  "new_members": 85,
  "active_members": 1250,
  "level_distribution": [
    {
      "level": 1,
      "level_name": "普通会员",
      "count": 2100
    },
    {
      "level": 2,
      "level_name": "银卡会员",
      "count": 650
    }
  ],
  "top_consumers": [
    {
      "member_id": 1,
      "member_name": "张三",
      "total_consumption": "125000.00"
    }
  ]
}
```

### 3.5 获取财务统计
获取财务统计数据，包含收入支出统计、利润分析等。

**接口地址**: `GET /api/v1/dashboard/financial-statistics`

**认证**: 需要Bearer Token

**查询参数**:
- `start_date`: 开始日期 (YYYY-MM-DD) (可选)
- `end_date`: 结束日期 (YYYY-MM-DD) (可选)
- `store_id`: 门店ID (可选)

**响应示例**:
```json
{
  "total_revenue": "2850000.00",
  "total_cost": "1950000.00",
  "gross_profit": "900000.00",
  "profit_margin": 31.58,
  "recycling_revenue": "450000.00",
  "monthly_trend": [
    {
      "month": "2024-01",
      "revenue": "850000.00",
      "cost": "580000.00",
      "profit": "270000.00"
    }
  ]
}
```

### 3.6 获取最近活动
获取最近活动记录，包含最近的入库、出库、销售记录等。

**接口地址**: `GET /api/v1/dashboard/recent-activities`

**认证**: 需要Bearer Token

**查询参数**:
- `limit`: 返回数量限制 (1-100, 默认20)

**响应示例**:
```json
{
  "activities": [
    {
      "id": 1,
      "type": "stock_in",
      "title": "商品入库",
      "description": "入库单 RK202401010001 已审核通过",
      "operator": "张三",
      "time": 1640995200,
      "amount": "125000.00"
    },
    {
      "id": 2,
      "type": "member_register",
      "title": "会员注册",
      "description": "新会员 李四 注册成功",
      "operator": "系统",
      "time": 1640995100
    }
  ]
}
```

### 3.7 获取销售趋势
获取销售趋势数据，支持按日、周、月、年统计。

**接口地址**: `GET /api/v1/dashboard/sales-trend`

**认证**: 需要Bearer Token

**查询参数**:
- `period`: 统计周期 (day/week/month/year, 默认month)
- `days`: 统计天数 (1-365, 默认30)
- `store_id`: 门店ID (可选)

**响应示例**:
```json
{
  "period": "month",
  "data": [
    {
      "date": "2024-01",
      "amount": "850000.00",
      "count": 285
    },
    {
      "date": "2024-02",
      "amount": "920000.00",
      "count": 312
    }
  ],
  "total_amount": "2850000.00",
  "total_count": 1250,
  "growth_rate": 15.6
}
```

## 4. 商品管理

### 4.1 获取商品分类列表
获取商品分类列表。

**接口地址**: `GET /api/v1/jewelry/categories`

**认证**: 需要Bearer Token

**查询参数**:
- `status`: 状态筛选 (0=禁用, 1=正常) (可选)

**响应示例**:
```json
[
  {
    "id": 1,
    "name": "黄金首饰",
    "status": 1,
    "sort": 1,
    "createtime": 1640995200,
    "updatetime": 1640995200
  },
  {
    "id": 2,
    "name": "银饰",
    "status": 1,
    "sort": 2,
    "createtime": 1640995200,
    "updatetime": 1640995200
  }
]
```

### 4.2 获取单个商品分类
获取单个商品分类详情。

**接口地址**: `GET /api/v1/jewelry/categories/{category_id}`

**认证**: 需要Bearer Token

**路径参数**:
- `category_id`: 分类ID

**响应示例**:
```json
{
  "id": 1,
  "name": "黄金首饰",
  "status": 1,
  "sort": 1,
  "createtime": 1640995200,
  "updatetime": 1640995200
}
```

### 4.3 获取商品列表
获取商品列表，支持多种筛选条件。

**接口地址**: `GET /api/v1/jewelry`

**认证**: 需要Bearer Token

**查询参数**:
- `page`: 页码 (默认1)
- `page_size`: 每页数量 (1-100, 默认20)
- `category_id`: 分类ID (可选)
- `store_id`: 门店ID (可选)
- `status`: 状态 (0=下架, 1=上架, 2=待出库) (可选)
- `keyword`: 搜索关键词(商品名称或条码) (可选)

**响应示例**:
```json
{
  "items": [
    {
      "id": 1,
      "barcode": "JW202401010001",
      "name": "黄金项链",
      "category_id": 1,
      "category_name": "黄金首饰",
      "store_id": 1,
      "store_name": "旗舰店",
      "ring_size": null,
      "gold_weight": "15.50",
      "silver_weight": "0.00",
      "total_weight": "15.50",
      "gold_price": "450.00",
      "silver_price": "0.00",
      "cost": "6975.00",
      "price": "8500.00",
      "status": 1,
      "createtime": 1640995200,
      "updatetime": 1640995200
    }
  ],
  "total": 1250,
  "page": 1,
  "page_size": 20,
  "total_pages": 63
}
```

### 4.4 创建商品
创建新商品。

**接口地址**: `POST /api/v1/jewelry`

**认证**: 需要Bearer Token

**请求参数**:
```json
{
  "barcode": "JW202401010001",        // 商品条码 (必填)
  "name": "黄金项链",                  // 商品名称 (必填)
  "category_id": 1,                   // 分类ID (必填)
  "store_id": 1,                      // 门店ID (必填)
  "ring_size": "16",                  // 圈口 (可选)
  "gold_weight": "15.50",             // 金重(g) (可选)
  "silver_weight": "0.00",            // 银重(g) (可选)
  "total_weight": "15.50",            // 总重(g) (可选)
  "gold_price": "450.00",             // 金价(元/g) (可选)
  "silver_price": "0.00",             // 银价(元/g) (可选)
  "cost": "6975.00",                  // 成本 (可选)
  "price": "8500.00",                 // 售价 (可选)
  "status": 1                         // 状态 (可选, 默认1)
}
```

**响应示例**:
```json
{
  "id": 1,
  "barcode": "JW202401010001",
  "name": "黄金项链",
  "category_id": 1,
  "category_name": "黄金首饰",
  "store_id": 1,
  "store_name": "旗舰店",
  "ring_size": "16",
  "gold_weight": "15.50",
  "silver_weight": "0.00",
  "total_weight": "15.50",
  "gold_price": "450.00",
  "silver_price": "0.00",
  "cost": "6975.00",
  "price": "8500.00",
  "status": 1,
  "createtime": 1640995200,
  "updatetime": 1640995200
}
```

### 4.5 获取单个商品详情
根据商品ID获取商品详情。

**接口地址**: `GET /api/v1/jewelry/{jewelry_id}`

**认证**: 需要Bearer Token

**路径参数**:
- `jewelry_id`: 商品ID

**响应示例**:
```json
{
  "id": 1,
  "barcode": "JW202401010001",
  "name": "黄金项链",
  "category_id": 1,
  "category_name": "黄金首饰",
  "store_id": 1,
  "store_name": "旗舰店",
  "ring_size": "16",
  "gold_weight": "15.50",
  "silver_weight": "0.00",
  "total_weight": "15.50",
  "gold_price": "450.00",
  "silver_price": "0.00",
  "cost": "6975.00",
  "price": "8500.00",
  "status": 1,
  "createtime": 1640995200,
  "updatetime": 1640995200
}
```

### 4.6 更新商品信息
更新商品信息。

**接口地址**: `PUT /api/v1/jewelry/{jewelry_id}`

**认证**: 需要Bearer Token

**路径参数**:
- `jewelry_id`: 商品ID

**请求参数**:
```json
{
  "name": "黄金项链(更新)",            // 商品名称 (可选)
  "category_id": 1,                   // 分类ID (可选)
  "store_id": 1,                      // 门店ID (可选)
  "ring_size": "18",                  // 圈口 (可选)
  "gold_weight": "16.00",             // 金重(g) (可选)
  "price": "9000.00",                 // 售价 (可选)
  "status": 1                         // 状态 (可选)
}
```

**响应示例**:
```json
{
  "id": 1,
  "barcode": "JW202401010001",
  "name": "黄金项链(更新)",
  "category_id": 1,
  "category_name": "黄金首饰",
  "store_id": 1,
  "store_name": "旗舰店",
  "ring_size": "18",
  "gold_weight": "16.00",
  "silver_weight": "0.00",
  "total_weight": "16.00",
  "gold_price": "450.00",
  "silver_price": "0.00",
  "cost": "7200.00",
  "price": "9000.00",
  "status": 1,
  "createtime": 1640995200,
  "updatetime": 1640995300
}
```

### 4.7 删除商品
删除商品。

**接口地址**: `DELETE /api/v1/jewelry/{jewelry_id}`

**认证**: 需要Bearer Token

**路径参数**:
- `jewelry_id`: 商品ID

**响应示例**:
```json
{
  "success": true,
  "message": "商品删除成功",
  "code": 200
}
```

### 4.8 根据条码获取商品
根据商品条码获取商品信息，支持门店级别的库存查询。

**接口地址**: `GET /api/v1/jewelry/barcode/{barcode}`

**认证**: 需要Bearer Token

**路径参数**:
- `barcode`: 商品条码

**查询参数**:
- `store_id` (可选): 门店ID，用于限制查询范围
  - 如果指定store_id，则只查询该门店的商品
  - 如果不指定store_id，则查询全系统商品（管理员权限）
  - 员工账户会自动使用其分配的门店ID

**权限控制**:
- 员工账户只能查询自己分配门店的商品
- 管理员账户可以查询任意门店或全系统商品
- 只有状态为1（上架在售）的商品才能被查询到

**响应示例**:
```json
{
  "id": 1,
  "barcode": "JW202401010001",
  "name": "黄金项链",
  "category_id": 1,
  "category_name": "黄金首饰",
  "store_id": 1,
  "store_name": "旗舰店",
  "ring_size": "16",
  "gold_weight": "15.50",
  "silver_weight": "0.00",
  "total_weight": "15.50",
  "gold_price": "450.00",
  "silver_price": "0.00",
  "cost": "6975.00",
  "price": "8500.00",
  "status": 1,
  "createtime": 1640995200,
  "updatetime": 1640995200
}
```

**错误响应示例**:
```json
// 商品在当前门店中不存在
{
  "detail": "该商品在当前门店中不存在"
}

// 商品状态不可用
{
  "detail": "商品状态不可用，只有上架在售的商品才能操作"
}

// 权限不足
{
  "detail": "无权访问其他门店的商品"
}

// 商品不存在
{
  "detail": "商品不存在"
}
```

**使用示例**:
```bash
# 查询指定门店的商品
curl -X GET "http://localhost:8000/api/v1/jewelry/barcode/JW202401010001?store_id=1" \
  -H "Authorization: Bearer your_token_here"

# 查询全系统商品（管理员权限）
curl -X GET "http://localhost:8000/api/v1/jewelry/barcode/JW202401010001" \
  -H "Authorization: Bearer your_token_here"
```

### 4.9 更新商品状态
更新商品状态。

**接口地址**: `PATCH /api/v1/jewelry/{jewelry_id}/status`

**认证**: 需要Bearer Token

**路径参数**:
- `jewelry_id`: 商品ID

**查询参数**:
- `status`: 状态 (0=下架, 1=上架, 2=待出库)

**响应示例**:
```json
{
  "id": 1,
  "barcode": "JW202401010001",
  "name": "黄金项链",
  "status": 0,
  "updatetime": 1640995400
}
```

### 4.10 计算商品成本
计算商品成本明细。

**接口地址**: `GET /api/v1/jewelry/{jewelry_id}/cost-calculation`

**认证**: 需要Bearer Token

**路径参数**:
- `jewelry_id`: 商品ID

**响应示例**:
```json
{
  "jewelry_id": 1,
  "gold_cost": "6975.00",
  "silver_cost": "0.00",
  "labor_cost": "500.00",
  "other_cost": "100.00",
  "total_cost": "7575.00",
  "profit_margin": 18.82,
  "calculation_details": {
    "gold_weight": "15.50",
    "gold_price": "450.00",
    "silver_weight": "0.00",
    "silver_price": "0.00",
    "labor_rate": "32.26"
  }
}
```

## 5. 门店管理

### 5.1 获取门店列表
获取门店列表，支持筛选和搜索。

**接口地址**: `GET /api/v1/store`

**认证**: 需要Bearer Token

**查询参数**:
- `page`: 页码 (默认1)
- `page_size`: 每页数量 (1-500, 默认20)
- `status`: 状态筛选 (0=关闭, 1=正常) (可选)
- `keyword`: 搜索关键词(门店名称、地址、电话) (可选)

**权限说明**:
- 管理员可以看到所有门店
- 非管理员只能看到自己所属的门店

**响应示例**:
```json
{
  "items": [
    {
      "id": 1,
      "name": "旗舰店",
      "address": "北京市朝阳区xxx街道xxx号",
      "phone": "010-12345678",
      "status": 1,
      "createtime": 1640995200,
      "updatetime": 1640995200,
      "jewelry_count": 250,
      "admin_count": 5
    },
    {
      "id": 2,
      "name": "分店A",
      "address": "北京市海淀区xxx街道xxx号",
      "phone": "010-87654321",
      "status": 1,
      "createtime": 1640995200,
      "updatetime": 1640995200,
      "jewelry_count": 180,
      "admin_count": 3
    }
  ],
  "total": 5,
  "page": 1,
  "page_size": 20,
  "total_pages": 1
}
```

### 5.2 创建门店
创建新门店。

**接口地址**: `POST /api/v1/store`

**认证**: 需要Bearer Token

**请求参数**:
```json
{
  "name": "新门店",                    // 门店名称 (必填)
  "address": "门店地址",               // 门店地址 (可选)
  "phone": "联系电话",                 // 联系电话 (可选)
  "status": 1                         // 状态 (0=关闭, 1=正常, 默认1)
}
```

**响应示例**:
```json
{
  "id": 3,
  "name": "新门店",
  "address": "门店地址",
  "phone": "联系电话",
  "status": 1,
  "createtime": 1640995500,
  "updatetime": 1640995500,
  "jewelry_count": 0,
  "admin_count": 0
}
```

### 5.3 获取单个门店详情
获取单个门店的详细信息。

**接口地址**: `GET /api/v1/store/{store_id}`

**认证**: 需要Bearer Token

**路径参数**:
- `store_id`: 门店ID

**响应示例**:
```json
{
  "id": 1,
  "name": "旗舰店",
  "address": "北京市朝阳区xxx街道xxx号",
  "phone": "010-12345678",
  "status": 1,
  "createtime": 1640995200,
  "updatetime": 1640995200,
  "jewelry_count": 250,
  "admin_count": 5
}
```

### 5.4 更新门店信息
更新门店信息。

**接口地址**: `PUT /api/v1/store/{store_id}`

**认证**: 需要Bearer Token

**路径参数**:
- `store_id`: 门店ID

**请求参数**:
```json
{
  "name": "更新后的门店名称",           // 门店名称 (可选)
  "address": "更新后的门店地址",       // 门店地址 (可选)
  "phone": "更新后的联系电话",         // 联系电话 (可选)
  "status": 1                         // 状态 (可选)
}
```

**响应示例**:
```json
{
  "id": 1,
  "name": "更新后的门店名称",
  "address": "更新后的门店地址",
  "phone": "更新后的联系电话",
  "status": 1,
  "createtime": 1640995200,
  "updatetime": 1640995600,
  "jewelry_count": 250,
  "admin_count": 5
}
```

### 5.5 删除门店
删除门店。

**接口地址**: `DELETE /api/v1/store/{store_id}`

**认证**: 需要Bearer Token

**路径参数**:
- `store_id`: 门店ID

**响应示例**:
```json
{
  "success": true,
  "message": "门店删除成功",
  "code": 200
}
```

### 5.6 更新门店状态
更新门店状态。

**接口地址**: `PATCH /api/v1/store/{store_id}/status`

**认证**: 需要Bearer Token

**路径参数**:
- `store_id`: 门店ID

**查询参数**:
- `status`: 状态 (0=关闭, 1=正常)

**响应示例**:
```json
{
  "id": 1,
  "name": "旗舰店",
  "status": 0,
  "updatetime": 1640995700
}
```

## 6. 管理员管理

### 6.1 获取管理员列表
获取管理员列表，支持多种筛选条件。

**接口地址**: `GET /api/v1/admin`

**认证**: 需要Bearer Token

**查询参数**:
- `page`: 页码 (默认1)
- `page_size`: 每页数量 (1-100, 默认20)
- `status`: 状态筛选 (normal=正常, hidden=禁用) (可选)
- `store_id`: 门店筛选 (可选)
- `keyword`: 搜索关键词(用户名、昵称、邮箱、手机号) (可选)

**响应示例**:
```json
{
  "items": [
    {
      "id": 1,
      "username": "admin",
      "nickname": "超级管理员",
      "email": "<EMAIL>",
      "mobile": "13800138000",
      "avatar": "/uploads/avatar/admin.jpg",
      "store_id": null,
      "store_name": null,
      "status": "normal",
      "logintime": 1640995200,
      "loginip": "*************",
      "createtime": 1640995200,
      "updatetime": 1640995200,
      "roles": ["超级管理员"],
      "permissions": ["*"]
    }
  ],
  "total": 10,
  "page": 1,
  "page_size": 20,
  "total_pages": 1
}
```

### 6.2 创建管理员
创建新管理员。

**接口地址**: `POST /api/v1/admin`

**认证**: 需要Bearer Token

**请求参数**:
```json
{
  "username": "newadmin",               // 用户名 (必填)
  "password": "password123",            // 密码 (必填)
  "nickname": "新管理员",               // 昵称 (必填)
  "email": "<EMAIL>",      // 邮箱 (可选)
  "mobile": "13900139000",              // 手机号 (可选)
  "store_id": 1,                        // 所属门店ID (可选)
  "status": "normal",                   // 状态 (normal/hidden, 默认normal)
  "roles": [2]                          // 角色ID列表 (可选)
}
```

**响应示例**:
```json
{
  "id": 11,
  "username": "newadmin",
  "nickname": "新管理员",
  "email": "<EMAIL>",
  "mobile": "13900139000",
  "avatar": null,
  "store_id": 1,
  "store_name": "旗舰店",
  "status": "normal",
  "logintime": null,
  "loginip": null,
  "createtime": 1640995800,
  "updatetime": 1640995800,
  "roles": ["店长"],
  "permissions": ["jewelry.view", "jewelry.create"]
}
```

### 6.3 获取单个管理员详情
获取单个管理员的详细信息。

**接口地址**: `GET /api/v1/admin/{admin_id}`

**认证**: 需要Bearer Token

**路径参数**:
- `admin_id`: 管理员ID

**响应示例**:
```json
{
  "id": 1,
  "username": "admin",
  "nickname": "超级管理员",
  "email": "<EMAIL>",
  "mobile": "13800138000",
  "avatar": "/uploads/avatar/admin.jpg",
  "store_id": null,
  "store_name": null,
  "status": "normal",
  "logintime": 1640995200,
  "loginip": "*************",
  "createtime": 1640995200,
  "updatetime": 1640995200,
  "roles": ["超级管理员"],
  "permissions": ["*"]
}
```

## 7. 会员管理

### 7.1 获取会员列表
获取会员列表，支持多种筛选条件。

**接口地址**: `GET /api/v1/member`

**认证**: 需要Bearer Token

**查询参数**:
- `page`: 页码 (默认1)
- `page_size`: 每页数量 (1-100, 默认20)
- `status`: 状态筛选 (0=禁用, 1=正常) (可选)
- `level`: 等级筛选 (1-5) (可选)
- `keyword`: 搜索关键词(会员卡号、姓名、电话) (可选)

**响应示例**:
```json
{
  "items": [
    {
      "id": 1,
      "card_no": "VIP202401010001",
      "name": "张三",
      "mobile": "13800138000",
      "email": "<EMAIL>",
      "gender": 1,
      "birthday": "1990-01-01",
      "level": 2,
      "level_name": "银卡会员",
      "points": 1250,
      "total_consumption": "25000.00",
      "status": 1,
      "createtime": 1640995200,
      "updatetime": 1640995200
    }
  ],
  "total": 3200,
  "page": 1,
  "page_size": 20,
  "total_pages": 160
}
```

## 8. 库存管理

### 8.1 入库管理

#### 8.1.1 获取入库单列表
获取入库单列表，支持多种筛选条件。

**接口地址**: `GET /api/v1/stock-in`

**认证**: 需要Bearer Token

**查询参数**:
- `page`: 页码 (默认1)
- `page_size`: 每页数量 (1-100, 默认20)
- `store_id`: 门店ID筛选 (可选)
- `status`: 状态筛选 (1=待审核, 2=已通过, 3=未通过, 4=已作废) (可选)
- `start_date`: 开始日期 (YYYY-MM-DD) (可选)
- `end_date`: 结束日期 (YYYY-MM-DD) (可选)
- `keyword`: 搜索关键词(入库单号、操作员) (可选)

**响应示例**:
```json
{
  "success": true,
  "message": "获取入库单列表成功",
  "data": [
    {
      "id": 1,
      "order_no": "RK202401010001",
      "store_id": 1,
      "store_name": "旗舰店",
      "operator_id": 1,
      "operator_name": "张三",
      "total_weight": "125.50",
      "total_amount": "125000.00",
      "status": 2,
      "status_text": "已通过",
      "audit_user_id": 2,
      "auditor_name": "李四",
      "audit_time": 1640995300,
      "audit_remark": "审核通过",
      "remark": "批量入库",
      "createtime": 1640995200,
      "updatetime": 1640995300,
      "item_count": 25
    }
  ],
  "pagination": {
    "page": 1,
    "page_size": 20,
    "total": 150,
    "pages": 8
  }
}
```

#### 8.1.2 创建入库单
创建新的入库单。

**接口地址**: `POST /api/v1/stock-in`

**认证**: 需要Bearer Token

**请求参数**:
```json
{
  "store_id": 1,                        // 入库门店ID (必填)
  "supplier": "供应商A",                // 供应商 (可选)
  "remark": "批量入库",                 // 备注 (可选)
  "items": [                            // 入库商品明细 (必填)
    {
      "barcode": "JW202401010001",      // 商品条码 (必填)
      "name": "黄金项链",               // 商品名称 (必填)
      "category_id": 1,                 // 分类ID (可选)
      "ring_size": "16",                // 圈口 (可选)
      "gold_weight": "15.50",           // 金重(g) (可选)
      "silver_weight": "0.00",          // 银重(g) (可选)
      "total_weight": "15.50",          // 总重(g) (可选)
      "gold_price": "450.00",           // 金价(元/g) (可选)
      "silver_price": "0.00",           // 银价(元/g) (可选)
      "cost": "6975.00",                // 成本 (可选)
      "price": "8500.00"                // 售价 (可选)
    }
  ]
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "入库单创建成功",
  "data": {
    "id": 1,
    "order_no": "RK202401010001",
    "store_id": 1,
    "store_name": "旗舰店",
    "operator_id": 1,
    "operator_name": "张三",
    "supplier": "供应商A",
    "total_weight": "15.50",
    "total_amount": "8500.00",
    "status": 1,
    "status_text": "待审核",
    "remark": "批量入库",
    "createtime": 1640995200,
    "updatetime": 1640995200,
    "items": [
      {
        "id": 1,
        "barcode": "JW202401010001",
        "name": "黄金项链",
        "category_id": 1,
        "ring_size": "16",
        "gold_weight": "15.50",
        "silver_weight": "0.00",
        "total_weight": "15.50",
        "gold_price": "450.00",
        "silver_price": "0.00",
        "cost": "6975.00",
        "price": "8500.00"
      }
    ]
  }
}
```

#### 8.1.3 获取入库单详情
根据ID获取入库单详细信息。

**接口地址**: `GET /api/v1/stock-in/{stock_in_id}`

**认证**: 需要Bearer Token

**路径参数**:
- `stock_in_id`: 入库单ID

**响应示例**:
```json
{
  "success": true,
  "message": "获取入库单详情成功",
  "data": {
    "id": 1,
    "order_no": "RK202401010001",
    "store_id": 1,
    "store_name": "旗舰店",
    "operator_id": 1,
    "operator_name": "张三",
    "supplier": "供应商A",
    "total_weight": "15.50",
    "total_amount": "8500.00",
    "status": 2,
    "status_text": "已通过",
    "audit_user_id": 2,
    "auditor_name": "李四",
    "audit_time": 1640995300,
    "audit_remark": "审核通过",
    "remark": "批量入库",
    "createtime": 1640995200,
    "updatetime": 1640995300,
    "items": [
      {
        "id": 1,
        "barcode": "JW202401010001",
        "name": "黄金项链",
        "category_id": 1,
        "category_name": "黄金首饰",
        "ring_size": "16",
        "gold_weight": "15.50",
        "silver_weight": "0.00",
        "total_weight": "15.50",
        "gold_price": "450.00",
        "silver_price": "0.00",
        "cost": "6975.00",
        "price": "8500.00",
        "jewelry_id": 1
      }
    ]
  }
}
```

#### 8.1.4 审核入库单
审核入库单，通过或拒绝。

**接口地址**: `PATCH /api/v1/stock-in/{stock_in_id}/audit`

**认证**: 需要Bearer Token

**路径参数**:
- `stock_in_id`: 入库单ID

**请求参数**:
```json
{
  "status": 2,                          // 审核状态 (2=已通过, 3=未通过, 4=已作废)
  "audit_remark": "审核通过"            // 审核备注 (可选)
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "入库单审核成功",
  "data": {
    "id": 1,
    "order_no": "RK202401010001",
    "status": 2,
    "status_text": "已通过",
    "audit_user_id": 2,
    "auditor_name": "李四",
    "audit_time": 1640995300,
    "audit_remark": "审核通过"
  }
}
```

### 8.2 出库管理

#### 8.2.1 获取出库单列表
获取出库单列表，支持多种筛选条件。

**接口地址**: `GET /api/v1/stock-out`

**认证**: 需要Bearer Token

**查询参数**:
- `page`: 页码 (默认1)
- `page_size`: 每页数量 (1-100, 默认20)
- `store_id`: 门店ID筛选 (可选)
- `status`: 状态筛选 (1=待审核, 2=已通过, 3=未通过, 4=已作废) (可选)
- `sale_type`: 销售类型筛选 (可选)
- `start_date`: 开始日期 (YYYY-MM-DD) (可选)
- `end_date`: 结束日期 (YYYY-MM-DD) (可选)
- `keyword`: 搜索关键词(出库单号、客户、操作员) (可选)

**响应示例**:
```json
{
  "success": true,
  "message": "获取出库单列表成功",
  "data": [
    {
      "id": 1,
      "order_no": "CK202401010001",
      "store_id": 1,
      "store_name": "旗舰店",
      "customer": "张三",
      "sale_type": "零售",
      "total_weight": "15.50",
      "total_amount": "8500.00",
      "actual_amount": "8500.00",
      "status": 2,
      "status_text": "已通过",
      "payment_status": 1,
      "payment_status_text": "已收款",
      "operator_id": 1,
      "operator_name": "李四",
      "audit_user_id": 2,
      "auditor_name": "王五",
      "createtime": 1640995200,
      "updatetime": 1640995300,
      "item_count": 1
    }
  ],
  "pagination": {
    "page": 1,
    "page_size": 20,
    "total": 85,
    "pages": 5
  }
}
```

### 8.3 退货管理

#### 8.3.1 获取退货单列表
获取退货单列表，支持多种筛选条件。

**接口地址**: `GET /api/v1/stock-return`

**认证**: 需要Bearer Token

**查询参数**:
- `page`: 页码 (默认1)
- `page_size`: 每页数量 (1-100, 默认20)
- `store_id`: 门店ID筛选 (可选)
- `status`: 状态筛选 (1=待审核, 2=已通过, 3=未通过, 4=已作废) (可选)
- `start_date`: 开始日期 (YYYY-MM-DD) (可选)
- `end_date`: 结束日期 (YYYY-MM-DD) (可选)
- `keyword`: 搜索关键词(退货单号、客户、操作员) (可选)

**响应示例**:
```json
{
  "success": true,
  "message": "获取退货单列表成功",
  "data": [
    {
      "id": 1,
      "order_no": "TH202401010001",
      "store_id": 1,
      "store_name": "旗舰店",
      "customer": "张三",
      "total_amount": "8500.00",
      "discount_amount": "100.00",
      "actual_amount": "8400.00",
      "status": 2,
      "status_text": "已通过",
      "operator_id": 1,
      "operator_name": "李四",
      "audit_user_id": 2,
      "auditor_name": "王五",
      "createtime": 1640995200,
      "updatetime": 1640995300,
      "item_count": 1
    }
  ],
  "pagination": {
    "page": 1,
    "page_size": 20,
    "total": 25,
    "pages": 2
  }
}
```

### 8.4 库存盘点

#### 8.4.1 获取盘点单列表
获取库存盘点单列表。

**接口地址**: `GET /api/v1/inventory-check`

**认证**: 需要Bearer Token

**查询参数**:
- `page`: 页码 (默认1)
- `page_size`: 每页数量 (1-100, 默认20)
- `store_id`: 门店ID筛选 (可选)
- `status`: 状态筛选 (0=进行中, 1=已完成, 2=已取消) (可选)
- `start_date`: 开始日期 (YYYY-MM-DD) (可选)
- `end_date`: 结束日期 (YYYY-MM-DD) (可选)
- `keyword`: 搜索关键词(盘点单号、操作员) (可选)

**响应示例**:
```json
{
  "success": true,
  "message": "获取盘点单列表成功",
  "data": [
    {
      "id": 1,
      "check_no": "PD202401010001",
      "store_id": 1,
      "store_name": "旗舰店",
      "title": "月度盘点",
      "total_count": 250,
      "checked_count": 245,
      "profit_count": 2,
      "loss_count": 3,
      "status": 1,
      "status_text": "已完成",
      "operator_id": 1,
      "operator_name": "张三",
      "start_time": 1640995200,
      "end_time": 1640998800,
      "createtime": 1640995200,
      "updatetime": 1640998800
    }
  ],
  "pagination": {
    "page": 1,
    "page_size": 20,
    "total": 12,
    "pages": 1
  }
}
```

### 8.5 回收管理

#### 8.5.1 获取回收单列表
获取回收单列表，支持多种筛选条件。

**接口地址**: `GET /api/v1/recycling`

**认证**: 需要Bearer Token

**查询参数**:
- `page`: 页码 (默认1)
- `page_size`: 每页数量 (1-100, 默认20)
- `store_id`: 门店ID筛选 (可选)
- `status`: 状态筛选 (1=待审核, 2=已通过, 3=未通过, 4=已作废) (可选)
- `start_date`: 开始日期 (YYYY-MM-DD) (可选)
- `end_date`: 结束日期 (YYYY-MM-DD) (可选)
- `keyword`: 搜索关键词(回收单号、会员、操作员) (可选)

**响应示例**:
```json
{
  "success": true,
  "message": "获取回收单列表成功",
  "data": [
    {
      "id": 1,
      "order_no": "HS202401010001",
      "store_id": 1,
      "store_name": "旗舰店",
      "member_id": 1,
      "member_name": "张三",
      "total_weight": "25.50",
      "total_amount": "11250.00",
      "actual_amount": "11000.00",
      "status": 2,
      "status_text": "已通过",
      "operator_id": 1,
      "operator_name": "李四",
      "audit_user_id": 2,
      "auditor_name": "王五",
      "createtime": 1640995200,
      "updatetime": 1640995300,
      "item_count": 3
    }
  ],
  "pagination": {
    "page": 1,
    "page_size": 20,
    "total": 45,
    "pages": 3
  }
}
```

### 8.6 门店调拨

#### 8.6.1 获取调拨单列表
获取门店间商品调拨单列表。

**接口地址**: `GET /api/v1/store-transfer`

**认证**: 需要Bearer Token

**查询参数**:
- `page`: 页码 (默认1)
- `page_size`: 每页数量 (1-100, 默认20)
- `from_store_id`: 源门店ID筛选 (可选)
- `to_store_id`: 目标门店ID筛选 (可选)
- `status`: 状态筛选 (0=待审核, 1=已通过, 2=已拒绝) (可选)
- `start_date`: 开始日期 (YYYY-MM-DD) (可选)
- `end_date`: 结束日期 (YYYY-MM-DD) (可选)
- `keyword`: 搜索关键词(调拨单号、操作员) (可选)

**响应示例**:
```json
{
  "success": true,
  "message": "获取调拨单列表成功",
  "data": [
    {
      "id": 1,
      "transfer_no": "DB202401010001",
      "from_store_id": 1,
      "from_store_name": "旗舰店",
      "to_store_id": 2,
      "to_store_name": "分店A",
      "total_amount": "25000.00",
      "item_count": 5,
      "status": 1,
      "status_text": "已通过",
      "admin_id": 1,
      "admin_name": "张三",
      "audit_id": 2,
      "auditor_name": "李四",
      "audit_time": 1640995300,
      "createtime": 1640995200,
      "updatetime": 1640995300
    }
  ],
  "pagination": {
    "page": 1,
    "page_size": 20,
    "total": 15,
    "pages": 1
  }
}
```

## 9. 数据导入导出

### 9.1 数据导出

#### 9.1.1 导出商品数据
导出商品数据到Excel文件。

**接口地址**: `POST /api/v1/export/jewelry`

**认证**: 需要Bearer Token

**请求参数**:
```json
{
  "format": "excel",                    // 导出格式 (excel/csv)
  "store_id": 1,                        // 门店ID筛选 (可选)
  "category_id": 1,                     // 分类ID筛选 (可选)
  "status": 1,                          // 状态筛选 (可选)
  "start_date": "2024-01-01",           // 开始日期 (可选)
  "end_date": "2024-01-31",             // 结束日期 (可选)
  "fields": [                           // 导出字段 (可选)
    "barcode", "name", "category_name",
    "store_name", "gold_weight", "price"
  ]
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "数据导出成功",
  "data": {
    "download_url": "/api/v1/export/download/jewelry_20240101_20240131.xlsx",
    "file_name": "jewelry_20240101_20240131.xlsx",
    "file_size": "2.5MB",
    "record_count": 1250,
    "export_time": 1640995500
  }
}
```

#### 9.1.2 导出入库数据
导出入库单数据到Excel文件。

**接口地址**: `POST /api/v1/export/stock-in`

**认证**: 需要Bearer Token

**请求参数**:
```json
{
  "format": "excel",                    // 导出格式 (excel/csv)
  "store_id": 1,                        // 门店ID筛选 (可选)
  "status": 2,                          // 状态筛选 (可选)
  "start_date": "2024-01-01",           // 开始日期 (可选)
  "end_date": "2024-01-31",             // 结束日期 (可选)
  "include_items": true                 // 是否包含明细 (可选)
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "入库数据导出成功",
  "data": {
    "download_url": "/api/v1/export/download/stock_in_20240101_20240131.xlsx",
    "file_name": "stock_in_20240101_20240131.xlsx",
    "file_size": "1.8MB",
    "record_count": 150,
    "export_time": 1640995500
  }
}
```

### 9.2 数据导入

#### 9.2.1 下载导入模板
下载数据导入模板文件。

**接口地址**: `GET /api/v1/export/template`

**认证**: 需要Bearer Token

**查询参数**:
- `template_type`: 模板类型 (jewelry/member/stock_in) (必填)
- `include_sample_data`: 是否包含示例数据 (默认true)

**响应示例**:
```json
{
  "success": true,
  "message": "模板生成成功",
  "download_url": "/api/v1/export/template/jewelry_template.xlsx",
  "file_name": "jewelry_template.xlsx"
}
```

#### 9.2.2 批量导入商品
批量导入商品数据。

**接口地址**: `POST /api/v1/export/import/jewelry`

**认证**: 需要Bearer Token

**请求参数**:
- Content-Type: `multipart/form-data`
- `file`: Excel文件 (必填)
- `store_id`: 目标门店ID (必填)
- `overwrite`: 是否覆盖已存在的数据 (可选, 默认false)

**响应示例**:
```json
{
  "success": true,
  "message": "商品导入成功",
  "data": {
    "total_rows": 100,
    "success_count": 95,
    "failed_count": 5,
    "failed_rows": [
      {
        "row": 6,
        "error": "商品条码已存在"
      },
      {
        "row": 15,
        "error": "分类不存在"
      }
    ],
    "import_time": 1640995600
  }
}
```

## 错误处理

### HTTP状态码说明

| 状态码 | 说明 | 示例场景 |
|--------|------|----------|
| 200 | 请求成功 | 正常的API调用 |
| 400 | 请求参数错误 | 参数格式不正确、必填参数缺失 |
| 401 | 未授权 | Token无效、Token过期 |
| 403 | 权限不足 | 用户没有访问该资源的权限 |
| 404 | 资源不存在 | 请求的数据不存在 |
| 422 | 数据验证失败 | 数据格式正确但不符合业务规则 |
| 500 | 服务器内部错误 | 系统异常、数据库连接失败 |

### 错误响应格式

#### 400 错误示例
```json
{
  "detail": "请求参数错误: 商品名称不能为空"
}
```

#### 401 错误示例
```json
{
  "detail": "Token已过期，请重新登录"
}
```

#### 403 错误示例
```json
{
  "detail": "权限不足，无法访问该资源"
}
```

#### 404 错误示例
```json
{
  "detail": "商品不存在"
}
```

#### 422 错误示例
```json
{
  "detail": [
    {
      "loc": ["body", "price"],
      "msg": "价格必须大于0",
      "type": "value_error"
    }
  ]
}
```

#### 500 错误示例
```json
{
  "code": 500,
  "message": "服务器内部错误",
  "data": null
}
```

## 调用示例

### JavaScript/Axios 示例

#### 1. 用户登录
```javascript
import axios from 'axios';

const API_BASE_URL = 'http://localhost:8000';

// 创建axios实例
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 用户登录
async function login(username, password) {
  try {
    const response = await apiClient.post('/api/v1/auth/login', {
      username: username,
      password: password,
      remember_me: false
    });

    const { access_token, user } = response.data;

    // 保存token到localStorage
    localStorage.setItem('access_token', access_token);

    // 设置默认认证头
    apiClient.defaults.headers.common['Authorization'] = `Bearer ${access_token}`;

    console.log('登录成功:', user);
    return response.data;
  } catch (error) {
    console.error('登录失败:', error.response?.data?.detail || error.message);
    throw error;
  }
}

// 使用示例
login('admin', 'password123');
```

#### 2. 获取商品列表
```javascript
// 获取商品列表
async function getJewelryList(params = {}) {
  try {
    const response = await apiClient.get('/api/v1/jewelry', {
      params: {
        page: params.page || 1,
        page_size: params.page_size || 20,
        category_id: params.category_id,
        store_id: params.store_id,
        status: params.status,
        keyword: params.keyword
      }
    });

    console.log('商品列表:', response.data);
    return response.data;
  } catch (error) {
    console.error('获取商品列表失败:', error.response?.data?.detail || error.message);
    throw error;
  }
}

// 使用示例
getJewelryList({
  page: 1,
  page_size: 20,
  category_id: 1,
  status: 1,
  keyword: '黄金'
});
```

#### 3. 创建入库单
```javascript
// 创建入库单
async function createStockIn(stockInData) {
  try {
    const response = await apiClient.post('/api/v1/stock-in', stockInData);

    console.log('入库单创建成功:', response.data);
    return response.data;
  } catch (error) {
    console.error('创建入库单失败:', error.response?.data?.detail || error.message);
    throw error;
  }
}

// 使用示例
createStockIn({
  store_id: 1,
  supplier: '供应商A',
  remark: '批量入库',
  items: [
    {
      barcode: 'JW202401010001',
      name: '黄金项链',
      category_id: 1,
      ring_size: '16',
      gold_weight: '15.50',
      silver_weight: '0.00',
      total_weight: '15.50',
      gold_price: '450.00',
      silver_price: '0.00',
      cost: '6975.00',
      price: '8500.00'
    }
  ]
});
```

### Python/Requests 示例

#### 1. 用户登录
```python
import requests
import json

API_BASE_URL = 'http://localhost:8000'

class JewelryAPI:
    def __init__(self, base_url=API_BASE_URL):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json'
        })

    def login(self, username, password):
        """用户登录"""
        url = f"{self.base_url}/api/v1/auth/login"
        data = {
            "username": username,
            "password": password,
            "remember_me": False
        }

        try:
            response = self.session.post(url, json=data)
            response.raise_for_status()

            result = response.json()
            access_token = result['access_token']

            # 设置认证头
            self.session.headers.update({
                'Authorization': f'Bearer {access_token}'
            })

            print(f"登录成功: {result['user']['nickname']}")
            return result
        except requests.exceptions.RequestException as e:
            print(f"登录失败: {e}")
            raise

    def get_jewelry_list(self, **params):
        """获取商品列表"""
        url = f"{self.base_url}/api/v1/jewelry"

        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()

            result = response.json()
            print(f"获取到 {len(result['items'])} 个商品")
            return result
        except requests.exceptions.RequestException as e:
            print(f"获取商品列表失败: {e}")
            raise

    def create_stock_in(self, stock_in_data):
        """创建入库单"""
        url = f"{self.base_url}/api/v1/stock-in"

        try:
            response = self.session.post(url, json=stock_in_data)
            response.raise_for_status()

            result = response.json()
            print(f"入库单创建成功: {result['data']['order_no']}")
            return result
        except requests.exceptions.RequestException as e:
            print(f"创建入库单失败: {e}")
            raise

# 使用示例
api = JewelryAPI()

# 登录
api.login('admin', 'password123')

# 获取商品列表
jewelry_list = api.get_jewelry_list(
    page=1,
    page_size=20,
    category_id=1,
    status=1
)

# 创建入库单
stock_in_data = {
    "store_id": 1,
    "supplier": "供应商A",
    "remark": "批量入库",
    "items": [
        {
            "barcode": "JW202401010001",
            "name": "黄金项链",
            "category_id": 1,
            "ring_size": "16",
            "gold_weight": "15.50",
            "silver_weight": "0.00",
            "total_weight": "15.50",
            "gold_price": "450.00",
            "silver_price": "0.00",
            "cost": "6975.00",
            "price": "8500.00"
        }
    ]
}

api.create_stock_in(stock_in_data)
```

### cURL 示例

#### 1. 用户登录
```bash
# 用户登录
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "password123",
    "remember_me": false
  }'
```

#### 2. 获取商品列表
```bash
# 获取商品列表 (需要先登录获取token)
curl -X GET "http://localhost:8000/api/v1/jewelry?page=1&page_size=20&category_id=1" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json"
```

#### 3. 创建商品
```bash
# 创建商品
curl -X POST "http://localhost:8000/api/v1/jewelry" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "barcode": "JW202401010001",
    "name": "黄金项链",
    "category_id": 1,
    "store_id": 1,
    "ring_size": "16",
    "gold_weight": "15.50",
    "silver_weight": "0.00",
    "total_weight": "15.50",
    "gold_price": "450.00",
    "silver_price": "0.00",
    "cost": "6975.00",
    "price": "8500.00",
    "status": 1
  }'
```

### Flutter/Dart 示例

#### 1. API客户端封装
```dart
import 'dart:convert';
import 'package:http/http.dart' as http;

class JewelryApiClient {
  static const String baseUrl = 'http://localhost:8000';
  String? _accessToken;

  // 设置认证token
  void setAccessToken(String token) {
    _accessToken = token;
  }

  // 获取请求头
  Map<String, String> get _headers {
    final headers = {
      'Content-Type': 'application/json',
    };

    if (_accessToken != null) {
      headers['Authorization'] = 'Bearer $_accessToken';
    }

    return headers;
  }

  // 用户登录
  Future<Map<String, dynamic>> login(String username, String password) async {
    final url = Uri.parse('$baseUrl/api/v1/auth/login');
    final body = json.encode({
      'username': username,
      'password': password,
      'remember_me': false,
    });

    try {
      final response = await http.post(url, headers: _headers, body: body);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        setAccessToken(data['access_token']);
        return data;
      } else {
        throw Exception('登录失败: ${response.body}');
      }
    } catch (e) {
      throw Exception('网络错误: $e');
    }
  }

  // 获取商品列表
  Future<Map<String, dynamic>> getJewelryList({
    int page = 1,
    int pageSize = 20,
    int? categoryId,
    int? storeId,
    int? status,
    String? keyword,
  }) async {
    final queryParams = <String, String>{
      'page': page.toString(),
      'page_size': pageSize.toString(),
    };

    if (categoryId != null) queryParams['category_id'] = categoryId.toString();
    if (storeId != null) queryParams['store_id'] = storeId.toString();
    if (status != null) queryParams['status'] = status.toString();
    if (keyword != null) queryParams['keyword'] = keyword;

    final url = Uri.parse('$baseUrl/api/v1/jewelry').replace(queryParameters: queryParams);

    try {
      final response = await http.get(url, headers: _headers);

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        throw Exception('获取商品列表失败: ${response.body}');
      }
    } catch (e) {
      throw Exception('网络错误: $e');
    }
  }
}

// 使用示例
void main() async {
  final apiClient = JewelryApiClient();

  try {
    // 登录
    final loginResult = await apiClient.login('admin', 'password123');
    print('登录成功: ${loginResult['user']['nickname']}');

    // 获取商品列表
    final jewelryList = await apiClient.getJewelryList(
      page: 1,
      pageSize: 20,
      categoryId: 1,
      status: 1,
    );
    print('获取到 ${jewelryList['items'].length} 个商品');

  } catch (e) {
    print('错误: $e');
  }
}
```

## 总结

本文档详细介绍了黄金珠宝管理系统后端API的所有接口调用方式，包括：

1. **认证机制**: JWT Bearer Token认证
2. **核心模块**: 商品管理、库存管理、门店管理、会员管理等
3. **数据操作**: 增删改查、审核、统计等完整业务流程
4. **错误处理**: 完整的HTTP状态码和错误响应格式
5. **调用示例**: JavaScript、Python、cURL、Flutter等多种语言示例

### 重要提醒

1. **认证**: 除了登录接口外，所有接口都需要在请求头中包含有效的JWT Token
2. **权限**: 不同用户角色具有不同的API访问权限，请确保用户具有相应权限
3. **数据格式**: 所有请求和响应都使用JSON格式，注意Content-Type设置
4. **错误处理**: 请根据HTTP状态码和错误响应进行适当的错误处理
5. **分页**: 列表接口支持分页，注意设置合适的page和page_size参数

如有疑问或需要更多技术支持，请联系开发团队。
