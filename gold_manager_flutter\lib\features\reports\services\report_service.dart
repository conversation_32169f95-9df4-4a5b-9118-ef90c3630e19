import 'package:dio/dio.dart';
import 'package:get/get.dart';

import '../../../core/services/api_service.dart';
import '../models/report_model.dart';

/// 报表服务类
class ReportService {
  final ApiService _apiService = Get.find<ApiService>();
  
  /// 获取销售报表数据
  Future<SalesReportData> getSalesReport(Map<String, dynamic> params) async {
    try {
      final response = await _apiService.get('/reports/sales', queryParameters: params);
      return SalesReportData.fromJson(response.data);
    } on DioException catch (e) {
      print('获取销售报表失败: $e');
      rethrow;
    } catch (e) {
      print('解析销售报表数据失败: $e');
      rethrow;
    }
  }
  
  /// 获取库存报表数据
  Future<InventoryReportData> getInventoryReport(Map<String, dynamic> params) async {
    try {
      final response = await _apiService.get('/reports/inventory', queryParameters: params);
      return InventoryReportData.fromJson(response.data);
    } on DioException catch (e) {
      print('获取库存报表失败: $e');
      rethrow;
    } catch (e) {
      print('解析库存报表数据失败: $e');
      rethrow;
    }
  }
  
  /// 获取回收报表数据
  Future<RecyclingReportData> getRecyclingReport(Map<String, dynamic> params) async {
    try {
      final response = await _apiService.get('/reports/recycling', queryParameters: params);
      return RecyclingReportData.fromJson(response.data);
    } on DioException catch (e) {
      print('获取回收报表失败: $e');
      rethrow;
    } catch (e) {
      print('解析回收报表数据失败: $e');
      rethrow;
    }
  }
  
  /// 导出报表
  Future<bool> exportReport(Map<String, dynamic> params) async {
    try {
      final response = await _apiService.post('/reports/export', data: params);
      
      // 检查响应状态
      if (response.statusCode == 200) {
        // 获取下载链接
        final String downloadUrl = response.data['download_url'];
        
        // 触发文件下载
        await _downloadFile(downloadUrl);
        
        return true;
      } else {
        return false;
      }
    } on DioException catch (e) {
      print('导出报表失败: $e');
      return false;
    } catch (e) {
      print('导出报表时发生错误: $e');
      return false;
    }
  }
  
  /// 下载文件
  Future<void> _downloadFile(String url) async {
    try {
      // 创建Dio实例
      Dio dio = Dio();
      
      // 设置文件名
      String fileName = url.split('/').last;
      
      // 获取存储路径
      String savePath = await _getFileSavePath(fileName);
      
      // 开始下载
      await dio.download(
        url,
        savePath,
        onReceiveProgress: (received, total) {
          if (total != -1) {
            print('下载进度: ${(received / total * 100).toStringAsFixed(0)}%');
          }
        },
      );
      
      print('文件已保存到: $savePath');
    } catch (e) {
      print('下载文件失败: $e');
      rethrow;
    }
  }
  
  /// 获取文件保存路径
  Future<String> _getFileSavePath(String fileName) async {
    // 这里需要根据平台获取适当的保存路径
    // 在实际实现中，我们应该使用path_provider包获取平台相关的路径
    // 简化起见，这里只返回一个示例路径
    return '/downloads/$fileName';
  }
} 