"""
退货管理API路由

老板，这个模块提供退货管理的API接口，包括：
1. 退货单的CRUD操作
2. 审核流程管理
3. 统计分析功能
4. 多条件查询筛选

完整的退货业务API接口。
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.services.stock_return_service import StockReturnService
from app.schemas.stock_return import (
    StockReturnCreate, StockReturnUpdate, StockReturnAuditUpdate,
    StockReturnResponse, StockReturnListResponse, StockReturnStatistics
)

router = APIRouter()


@router.get("/", response_model=StockReturnListResponse, summary="获取退货单列表")
async def get_stock_return_list(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    store_id: Optional[int] = Query(None, description="门店ID"),
    status: Optional[int] = Query(None, ge=1, le=4, description="状态:1=待审核,2=已通过,3=未通过,4=已作废"),
    operator_id: Optional[int] = Query(None, description="操作员ID"),
    customer: Optional[str] = Query(None, description="客户名称"),
    keyword: Optional[str] = Query(None, description="关键词搜索(单号/客户/备注)"),
    start_time: Optional[int] = Query(None, description="开始时间(时间戳)"),
    end_time: Optional[int] = Query(None, description="结束时间(时间戳)"),
    db: Session = Depends(get_db)
):
    """
    获取退货单列表

    支持多条件筛选和分页查询：
    - 按门店筛选
    - 按状态筛选
    - 按操作员筛选
    - 按客户名称筛选
    - 关键词搜索
    - 时间范围筛选
    """
    service = StockReturnService(db)
    result = service.get_stock_return_list(
        page=page,
        page_size=page_size,
        store_id=store_id,
        status=status,
        operator_id=operator_id,
        customer=customer,
        keyword=keyword,
        start_time=start_time,
        end_time=end_time
    )

    return StockReturnListResponse(
        success=True,
        message="查询成功",
        data=result["items"],
        pagination={
            "total": result["total"],
            "page": result["page"],
            "page_size": result["page_size"],
            "pages": result["pages"]
        }
    )


@router.get("/{stock_return_id}", response_model=StockReturnResponse, summary="根据ID获取退货单详情")
async def get_stock_return_by_id(
    stock_return_id: int,
    db: Session = Depends(get_db)
):
    """根据ID获取退货单详情，包含完整的退货商品明细信息"""
    service = StockReturnService(db)
    stock_return = service.get_stock_return_by_id(stock_return_id)

    if not stock_return:
        raise HTTPException(status_code=404, detail="退货单不存在")

    return stock_return


@router.get("/by-no/{order_no}", response_model=StockReturnResponse, summary="根据单号获取退货单详情")
async def get_stock_return_by_order_no(
    order_no: str,
    db: Session = Depends(get_db)
):
    """根据退货单号获取详情，包含完整的退货商品明细信息"""
    service = StockReturnService(db)
    stock_return = service.get_stock_return_by_order_no(order_no)

    if not stock_return:
        raise HTTPException(status_code=404, detail="退货单不存在")

    return stock_return


@router.post("/", response_model=StockReturnResponse, summary="创建退货单")
async def create_stock_return(
    stock_return_data: StockReturnCreate,
    operator_id: int = Query(..., description="操作员ID"),
    db: Session = Depends(get_db)
):
    """
    创建新的退货单

    功能特点：
    - 自动生成退货单号(RETYYYYMMDD0001格式)
    - 验证门店和操作员信息
    - 自动计算成本和金额
    - 支持多商品退货
    """
    try:
        service = StockReturnService(db)
        return service.create_stock_return(stock_return_data, operator_id)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/{stock_return_id}", response_model=StockReturnResponse, summary="更新退货单")
async def update_stock_return(
    stock_return_id: int,
    stock_return_data: StockReturnUpdate,
    db: Session = Depends(get_db)
):
    """
    更新退货单信息

    注意：只有待审核状态的退货单才能修改
    """
    try:
        service = StockReturnService(db)
        stock_return = service.update_stock_return(stock_return_id, stock_return_data)

        if not stock_return:
            raise HTTPException(status_code=404, detail="退货单不存在")

        return stock_return
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/{stock_return_id}", summary="删除退货单")
async def delete_stock_return(
    stock_return_id: int,
    db: Session = Depends(get_db)
):
    """
    删除退货单

    注意：只有待审核状态的退货单才能删除
    """
    try:
        service = StockReturnService(db)
        success = service.delete_stock_return(stock_return_id)

        if not success:
            raise HTTPException(status_code=404, detail="退货单不存在")

        return {"message": "退货单删除成功"}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.patch("/{stock_return_id}/audit", response_model=StockReturnResponse, summary="审核退货单")
async def audit_stock_return(
    stock_return_id: int,
    audit_data: StockReturnAuditUpdate,
    auditor_id: int = Query(..., description="审核员ID"),
    db: Session = Depends(get_db)
):
    """
    审核退货单

    审核状态：
    - 2: 已通过
    - 3: 未通过
    - 4: 已作废

    注意：只有待审核状态的退货单才能审核
    """
    try:
        service = StockReturnService(db)
        stock_return = service.audit_stock_return(stock_return_id, audit_data, auditor_id)

        if not stock_return:
            raise HTTPException(status_code=404, detail="退货单不存在")

        return stock_return
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/statistics/summary", response_model=StockReturnStatistics, summary="获取退货单统计信息")
async def get_stock_return_statistics(
    db: Session = Depends(get_db)
):
    """
    获取退货单统计信息

    包含：
    - 各状态退货单数量统计
    - 金额统计(应收/优惠/实际退款)
    - 状态分布
    - 门店分布
    """
    service = StockReturnService(db)
    return service.get_statistics()
