# 金包银首饰管理系统 - 数据模型

本目录包含金包银首饰管理系统的所有数据模型定义。这些模型遵循不可变(Immutable)原则设计，提供了类型安全的数据操作方式。

## 模型组织结构

数据模型按照业务领域分组：

```
models/
├── auth/              # 认证相关模型
│   └── user.dart      # 用户模型
├── common/            # 通用模型组件
│   ├── date_utils.dart # 日期工具
│   └── enums.dart     # 枚举定义
├── jewelry/           # 首饰相关模型
│   ├── jewelry.dart   # 首饰模型
│   ├── jewelry_category.dart # 首饰分类
│   └── jewelry_image.dart # 首饰图片
├── sales/             # 销售相关模型
│   ├── sale.dart      # 销售单
│   └── sale_item.dart # 销售明细
├── stock/             # 库存相关模型
│   ├── stock_in.dart  # 入库单
│   ├── stock_in_item.dart # 入库明细
│   ├── stock_out.dart # 出库单
│   └── stock_out_item.dart # 出库明细
├── store/             # 门店相关模型
│   └── store.dart     # 门店模型
├── recycling/         # 回收相关模型(待实现)
└── models.dart        # 模型导出索引
```

## 模型设计特点

1. **不可变性**：所有模型类都使用`final`字段，确保数据一旦创建就不能被修改。
2. **复制与更新**：每个模型提供`copyWith()`方法，用于创建新实例并更新部分属性。
3. **序列化支持**：所有模型都支持JSON序列化和反序列化。
4. **类型安全**：使用强类型定义，避免运行时类型错误。
5. **空安全**：充分利用Dart空安全特性，明确区分可空和非空字段。
6. **枚举替代常量**：使用枚举代替数字常量，提高代码可读性。

## 使用示例

### 导入模型

```dart
// 导入单个模型
import 'package:gold_manager_flutter/models/jewelry/jewelry.dart';

// 或使用索引文件导入所有模型
import 'package:gold_manager_flutter/models/models.dart';
```

### 创建模型实例

```dart
// 创建首饰实例
final jewelry = Jewelry(
  id: 1,
  barcode: 'J001',
  name: '黄金项链',
  categoryId: 1,
  goldWeight: 10.5,
  goldPrice: 380.0,
);

// 使用copyWith创建新实例
final updatedJewelry = jewelry.copyWith(
  name: '黄金项链(新款)',
  goldWeight: 12.0,
);
```

### JSON转换

```dart
// 从JSON创建实例
final Map<String, dynamic> json = getJsonFromApi();
final jewelry = Jewelry.fromJson(json);

// 转换为JSON
final Map<String, dynamic> jsonData = jewelry.toJson();
```

### 枚举使用

```dart
// 使用枚举代替数字常量
if (jewelry.status == JewelryStatus.onShelf) {
  // 处理上架商品
}

// 从API获取的数值转换为枚举
final status = JewelryStatus.fromValue(apiStatus);
```

## 数据模型验证

每个数据模型提供了必要的验证逻辑，确保数据的完整性和一致性。您可以在创建模型时进行验证，或者使用测试代码验证模型行为。

```dart
// 运行测试验证模型
flutter test test/models_test.dart
```

## 后续开发计划

1. 完善回收相关模型
2. 添加更多业务逻辑方法
3. 考虑使用代码生成简化JSON序列化 