import 'package:get/get.dart';
import '../core/utils/logger.dart';
import '../models/store/store.dart';
import '../core/services/api_client.dart';
import '../core/config/app_config.dart';

/// 门店服务
/// 处理门店相关的业务逻辑
class StoreService extends GetxService {
  final ApiClient _apiClient = Get.find<ApiClient>();

  /// 初始化服务
  Future<StoreService> init() async {
    LoggerService.d('StoreService 初始化');
    return this;
  }

  /// 获取门店列表
  Future<List<Store>> getStores({
    int page = 1,
    int pageSize = 20,
    int? status,
    String? keyword,
  }) async {
    try {
      // 尝试真实API调用
      try {
        final response = await _apiClient.get(
          '${AppConfig.apiEndpoint['store']}',
          queryParameters: {
            'page': page,
            'page_size': pageSize,
            if (status != null) 'status': status,
            if (keyword != null && keyword.isNotEmpty) 'keyword': keyword,
          },
        );

        if (response.statusCode == 200) {
          final responseData = response.data;
          // 后端直接返回分页结构: {items: [...], total: ..., page: ...}
          if (responseData != null && responseData['items'] != null) {
            final List<dynamic> storeList = responseData['items'];
            return storeList.map((json) => _mapStoreFromApi(json)).toList();
          } else {
            LoggerService.w('门店API返回数据格式异常: $responseData');
            return [];
          }
        } else {
          throw Exception('API调用失败: ${response.statusMessage}');
        }
      } catch (e) {
        LoggerService.e('API调用失败: $e');
        rethrow;
      }
    } catch (e) {
      LoggerService.e('获取门店列表失败', e);
      rethrow;
    }
  }

  /// 获取所有门店（用于下拉框等场景，不受分页限制）
  Future<List<Store>> getAllStores({
    int? status,
    String? keyword,
  }) async {
    try {
      List<Store> allStores = [];
      int currentPage = 1;
      const int maxPageSize = 100; // 后端限制的最大页面大小

      while (true) {
        // 获取当前页的门店数据
        final stores = await getStores(
          page: currentPage,
          pageSize: maxPageSize,
          status: status,
          keyword: keyword,
        );

        // 如果没有数据，说明已经获取完所有门店
        if (stores.isEmpty) {
          break;
        }

        allStores.addAll(stores);

        // 如果返回的数据少于页面大小，说明这是最后一页
        if (stores.length < maxPageSize) {
          break;
        }

        currentPage++;

        // 安全检查：避免无限循环（最多获取10页，即1000个门店）
        if (currentPage > 10) {
          LoggerService.w('获取门店数据超过10页，可能存在问题');
          break;
        }
      }

      LoggerService.d('成功获取所有门店，共${allStores.length}个');
      return allStores;
    } catch (e) {
      LoggerService.e('获取所有门店失败', e);
      rethrow;
    }
  }

  /// 数据映射方法：将API返回的JSON数据映射为Store对象
  Store _mapStoreFromApi(Map<String, dynamic> json) {
    return Store(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      code: json['code'] ?? json['id']?.toString() ?? '', // 如果没有code，使用id作为code
      address: json['address'] ?? '',
      phone: json['phone'] ?? '',
      manager: json['manager'] ?? '', // 后端可能没有这个字段，保持兼容
    );
  }

  /// 获取门店详情
  Future<Store> getStoreById(int id) async {
    try {
      // 尝试真实API调用
      try {
        final response = await _apiClient.get('${AppConfig.apiEndpoint['store']}/$id');

        if (response.statusCode == 200) {
          final data = response.data;
          return _mapStoreFromApi(data);
        } else {
          throw Exception('API调用失败: ${response.statusMessage}');
        }
      } catch (e) {
        LoggerService.w('API调用失败，使用模拟数据: $e');

        // API调用失败时使用模拟数据
        await Future.delayed(const Duration(milliseconds: 300));

        return Store(
          id: id,
          name: '门店$id',
          code: 'S$id',
          address: '测试地址$id',
          phone: '123-${id.toString().padLeft(8, '0')}',
          manager: '经理$id',
        );
      }
    } catch (e) {
      LoggerService.e('获取门店详情失败', e);
      rethrow;
    }
  }

  /// 创建门店
  Future<bool> createStore(Store store) async {
    try {
      // 尝试真实API调用
      try {
        final response = await _apiClient.post(
          '${AppConfig.apiEndpoint['store']}',
          data: {
            'name': store.name,
            'address': store.address,
            'phone': store.phone,
            'status': 1, // 默认正常状态
          },
        );

        if (response.statusCode == 200) {
          return true;
        } else {
          throw Exception('API调用失败: ${response.statusMessage}');
        }
      } catch (e) {
        LoggerService.w('API调用失败，使用模拟处理: $e');

        // API调用失败时使用模拟处理
        await Future.delayed(const Duration(milliseconds: 800));
        return true;
      }
    } catch (e) {
      LoggerService.e('创建门店失败', e);
      rethrow;
    }
  }

  /// 更新门店
  Future<bool> updateStore(Store store) async {
    try {
      // 尝试真实API调用
      try {
        final response = await _apiClient.put(
          '${AppConfig.apiEndpoint['store']}/${store.id}',
          data: {
            'name': store.name,
            'address': store.address,
            'phone': store.phone,
          },
        );

        if (response.statusCode == 200) {
          return true;
        } else {
          throw Exception('API调用失败: ${response.statusMessage}');
        }
      } catch (e) {
        LoggerService.w('API调用失败，使用模拟处理: $e');

        // API调用失败时使用模拟处理
        await Future.delayed(const Duration(milliseconds: 800));
        return true;
      }
    } catch (e) {
      LoggerService.e('更新门店失败', e);
      rethrow;
    }
  }

  /// 删除门店
  Future<bool> deleteStore(int id) async {
    try {
      // 尝试真实API调用
      try {
        final response = await _apiClient.delete('${AppConfig.apiEndpoint['store']}/$id');

        if (response.statusCode == 200) {
          return true;
        } else {
          throw Exception('API调用失败: ${response.statusMessage}');
        }
      } catch (e) {
        LoggerService.w('API调用失败，使用模拟处理: $e');

        // API调用失败时使用模拟处理
        await Future.delayed(const Duration(milliseconds: 500));
        return true;
      }
    } catch (e) {
      LoggerService.e('删除门店失败', e);
      rethrow;
    }
  }
}