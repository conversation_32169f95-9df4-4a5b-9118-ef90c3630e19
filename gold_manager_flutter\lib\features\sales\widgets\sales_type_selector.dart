import 'package:flutter/material.dart';

import '../../../core/constants/border_styles.dart';
import '../models/sales_item_detail.dart';

/// 销售类型选择器组件
class SalesTypeSelector extends StatelessWidget {
  final SalesType selectedType;
  final Function(SalesType) onTypeChanged;

  const SalesTypeSelector({
    super.key,
    required this.selectedType,
    required this.onTypeChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: SalesType.allTypes.map((type) {
        final isSelected = selectedType == type;
        return _buildTypeButton(
          type: type,
          isSelected: isSelected,
          onTap: () => onTypeChanged(type),
        );
      }).toList(),
    );
  }

  /// 构建类型按钮
  Widget _buildTypeButton({
    required SalesType type,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: isSelected ? _getTypeColor(type) : Colors.transparent,
              borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
              border: Border.all(
                color: _getTypeColor(type),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  _getTypeIcon(type),
                  size: 16,
                  color: isSelected ? Colors.white : _getTypeColor(type),
                ),
                const SizedBox(width: 6),
                Text(
                  type.displayName,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: isSelected ? Colors.white : _getTypeColor(type),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 获取类型对应的颜色
  Color _getTypeColor(SalesType type) {
    switch (type) {
      case SalesType.all:
        return Colors.grey[600]!; // 灰色表示全部
      case SalesType.retail:
        return const Color(0xFF1E88E5); // 主蓝色
      case SalesType.wholesale:
        return Colors.green[600]!;
      case SalesType.transfer:
        return Colors.orange[600]!;
      case SalesType.recycling:
        return Colors.purple[600]!;
    }
  }

  /// 获取类型对应的图标
  IconData _getTypeIcon(SalesType type) {
    switch (type) {
      case SalesType.all:
        return Icons.dashboard; // 仪表盘图标表示全部
      case SalesType.retail:
        return Icons.shopping_cart;
      case SalesType.wholesale:
        return Icons.inventory;
      case SalesType.transfer:
        return Icons.swap_horiz;
      case SalesType.recycling:
        return Icons.recycling;
    }
  }
}