import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/theme/app_theme.dart';
import '../../../widgets/responsive_builder.dart';
import '../controllers/dashboard_data_controller.dart';
import '../widgets/sales_overview_widget.dart';
import '../widgets/inventory_overview_widget.dart';
import '../widgets/recycling_overview_widget.dart';
import '../widgets/financial_overview_widget.dart';
import '../widgets/metric_card.dart';

/// 仪表盘概览视图
/// 展示系统的核心业务指标和数据概览
class DashboardOverviewView extends StatelessWidget {
  const DashboardOverviewView({super.key});

  @override
  Widget build(BuildContext context) {
    // 获取或创建数据控制器
    final dataController = Get.put(DashboardDataController());

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: RefreshIndicator(
        onRefresh: dataController.refreshData,
        child: ResponsiveBuilder(
          builder: (context, sizingInformation) {
            return SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              padding: EdgeInsets.all(sizingInformation.isMobile ? 12 : 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 页面标题
                  _buildPageHeader(sizingInformation),
                  const SizedBox(height: 24),
                  
                  // 核心指标概览
                  _buildCoreMetrics(dataController, sizingInformation),
                  const SizedBox(height: 32),
                  
                  // 业务模块概览
                  _buildBusinessOverview(dataController, sizingInformation),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  /// 构建页面标题
  Widget _buildPageHeader(SizingInformation sizingInformation) {
    return Row(
      children: [
        Icon(
          Icons.dashboard,
          size: sizingInformation.isMobile ? 24 : 28,
          color: AppTheme.primaryColor,
        ),
        const SizedBox(width: 12),
        Text(
          '仪表盘概览',
          style: TextStyle(
            fontSize: sizingInformation.isMobile ? 20 : 24,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const Spacer(),
        
        // 刷新按钮
        IconButton(
          onPressed: () {
            final controller = Get.find<DashboardDataController>();
            controller.refreshData();
          },
          icon: const Icon(Icons.refresh),
          tooltip: '刷新数据',
        ),
      ],
    );
  }

  /// 构建核心指标
  Widget _buildCoreMetrics(DashboardDataController controller, SizingInformation sizingInformation) {
    return Obx(() {
      final overview = controller.overviewData.value;
      final isLoading = controller.isLoading.value;
      
      if (sizingInformation.isMobile) {
        return _buildMobileCoreMetrics(overview, isLoading);
      } else if (sizingInformation.isTablet) {
        return _buildTabletCoreMetrics(overview, isLoading);
      } else {
        return _buildDesktopCoreMetrics(overview, isLoading);
      }
    });
  }

  /// 桌面端核心指标布局
  Widget _buildDesktopCoreMetrics(overview, bool isLoading) {
    return Row(
      children: [
        Expanded(child: _buildTotalProductsCard(overview, isLoading)),
        const SizedBox(width: 16),
        Expanded(child: _buildTodaySalesCard(overview, isLoading)),
        const SizedBox(width: 16),
        Expanded(child: _buildMonthSalesCard(overview, isLoading)),
        const SizedBox(width: 16),
        Expanded(child: _buildInventoryValueCard(overview, isLoading)),
      ],
    );
  }

  /// 平板端核心指标布局
  Widget _buildTabletCoreMetrics(overview, bool isLoading) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(child: _buildTotalProductsCard(overview, isLoading)),
            const SizedBox(width: 16),
            Expanded(child: _buildTodaySalesCard(overview, isLoading)),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(child: _buildMonthSalesCard(overview, isLoading)),
            const SizedBox(width: 16),
            Expanded(child: _buildInventoryValueCard(overview, isLoading)),
          ],
        ),
      ],
    );
  }

  /// 移动端核心指标布局
  Widget _buildMobileCoreMetrics(overview, bool isLoading) {
    return Column(
      children: [
        _buildTotalProductsCard(overview, isLoading),
        const SizedBox(height: 12),
        _buildTodaySalesCard(overview, isLoading),
        const SizedBox(height: 12),
        _buildMonthSalesCard(overview, isLoading),
        const SizedBox(height: 12),
        _buildInventoryValueCard(overview, isLoading),
      ],
    );
  }

  /// 商品总数卡片
  Widget _buildTotalProductsCard(overview, bool isLoading) {
    return DashboardMetricCard(
      title: '商品总数',
      value: overview.totalProducts.toString(),
      unit: '件',
      icon: Icons.inventory,
      iconColor: AppTheme.primaryColor,
      valueColor: AppTheme.primaryColor,
      subtitle: '${overview.totalStores}个门店',
      isLoading: isLoading,
    );
  }

  /// 今日销售额卡片
  Widget _buildTodaySalesCard(overview, bool isLoading) {
    return DashboardMetricCard(
      title: '今日销售额',
      value: overview.todaySales.toStringAsFixed(0),
      unit: '元',
      icon: Icons.today,
      iconColor: AppTheme.successColor,
      valueColor: AppTheme.successColor,
      trendValue: 12.5,
      trendDescription: '较昨日',
      isLoading: isLoading,
    );
  }

  /// 本月销售额卡片
  Widget _buildMonthSalesCard(overview, bool isLoading) {
    return DashboardMetricCard(
      title: '本月销售额',
      value: overview.monthSales.toStringAsFixed(0),
      unit: '元',
      icon: Icons.calendar_month,
      iconColor: AppTheme.infoColor,
      valueColor: AppTheme.infoColor,
      trendValue: 8.3,
      trendDescription: '较上月',
      isLoading: isLoading,
    );
  }

  /// 库存总值卡片
  Widget _buildInventoryValueCard(overview, bool isLoading) {
    return DashboardMetricCard(
      title: '库存总值',
      value: overview.inventoryValue.toStringAsFixed(0),
      unit: '元',
      icon: Icons.account_balance_wallet,
      iconColor: AppTheme.warningColor,
      valueColor: AppTheme.warningColor,
      subtitle: '${overview.pendingOrders}个待处理订单',
      isLoading: isLoading,
    );
  }

  /// 构建业务模块概览
  Widget _buildBusinessOverview(DashboardDataController controller, SizingInformation sizingInformation) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 模块标题
        Text(
          '业务概览',
          style: TextStyle(
            fontSize: sizingInformation.isMobile ? 18 : 20,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),
        
        // 业务模块
        if (sizingInformation.isDesktop) ...[
          // 桌面端：2x2网格布局
          Row(
            children: [
              Expanded(child: _buildSalesOverviewCard(controller)),
              const SizedBox(width: 16),
              Expanded(child: _buildInventoryOverviewCard(controller)),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(child: _buildRecyclingOverviewCard(controller)),
              const SizedBox(width: 16),
              Expanded(child: _buildFinancialOverviewCard(controller)),
            ],
          ),
        ] else ...[
          // 平板端和移动端：垂直布局
          _buildSalesOverviewCard(controller),
          const SizedBox(height: 16),
          _buildInventoryOverviewCard(controller),
          const SizedBox(height: 16),
          _buildRecyclingOverviewCard(controller),
          const SizedBox(height: 16),
          _buildFinancialOverviewCard(controller),
        ],
      ],
    );
  }

  /// 销售概览卡片
  Widget _buildSalesOverviewCard(DashboardDataController controller) {
    return Obx(() => SalesOverviewWidget(
      data: controller.salesData.value,
      isLoading: controller.isLoading.value,
      onRefresh: controller.refreshData,
    ));
  }

  /// 库存概览卡片
  Widget _buildInventoryOverviewCard(DashboardDataController controller) {
    return Obx(() => InventoryOverviewWidget(
      data: controller.inventoryData.value,
      isLoading: controller.isLoading.value,
      onRefresh: controller.refreshData,
    ));
  }

  /// 回收概览卡片
  Widget _buildRecyclingOverviewCard(DashboardDataController controller) {
    return Obx(() => RecyclingOverviewWidget(
      data: controller.recyclingData.value,
      isLoading: controller.isLoading.value,
      onRefresh: controller.refreshData,
    ));
  }

  /// 财务概览卡片
  Widget _buildFinancialOverviewCard(DashboardDataController controller) {
    return Obx(() => FinancialOverviewWidget(
      data: controller.financialData.value,
      isLoading: controller.isLoading.value,
      onRefresh: controller.refreshData,
    ));
  }
}
