import 'dart:io';
import 'package:get/get.dart';
import '../../../models/recycling/recycling_process.dart';
import '../../../models/recycling/recycling_item.dart';
import '../../../models/common/enums.dart';
import '../services/recycling_process_service.dart';
import '../services/image_upload_service.dart';

/// 旧料处理控制器
class RecyclingProcessController extends GetxController {
  final RecyclingProcessService _processService = Get.find<RecyclingProcessService>();
  final ImageUploadService _imageUploadService = Get.find<ImageUploadService>();
  
  // 当前回收物品
  final RecyclingItem recyclingItem;
  
  // 状态变量
  final RxBool isLoading = false.obs;
  final RxBool isSubmitting = false.obs;
  final RxBool isUploadingImage = false.obs;
  final RxList<RecyclingProcess> processList = <RecyclingProcess>[].obs;
  final Rx<RecyclingProcess?> currentProcess = Rx<RecyclingProcess?>(null);
  
  // 处理表单数据
  final Rx<RecyclingProcessType> selectedProcessType = RecyclingProcessType.pending.obs;
  final RxDouble cost = 0.0.obs;
  final RxDouble income = 0.0.obs;
  final RxDouble profit = 0.0.obs;
  final RxString remark = ''.obs;
  final RxList<String> imageUrls = <String>[].obs;
  final RxList<File> tempImages = <File>[].obs;
  
  RecyclingProcessController({required this.recyclingItem});
  
  @override
  void onInit() {
    super.onInit();
    loadProcessList();
  }
  
  @override
  void onClose() {
    // 清理临时图片
    tempImages.clear();
    super.onClose();
  }
  
  /// 加载处理记录列表
  Future<void> loadProcessList() async {
    isLoading.value = true;
    try {
      final list = await _processService.getProcessList(recyclingItem.id);
      processList.assignAll(list);
    } finally {
      isLoading.value = false;
    }
  }
  
  /// 加载处理记录详情
  Future<void> loadProcessDetail(int id) async {
    isLoading.value = true;
    try {
      final process = await _processService.getProcessDetail(id);
      if (process != null) {
        currentProcess.value = process;
        
        // 更新表单数据
        selectedProcessType.value = process.processType;
        cost.value = process.cost;
        income.value = process.income;
        profit.value = process.profit;
        remark.value = process.remark ?? '';
        
        // 加载图片
        if (process.imageUrls != null && process.imageUrls!.isNotEmpty) {
          imageUrls.assignAll(process.imageUrls!);
        } else {
          imageUrls.clear();
        }
        
        tempImages.clear();
      }
    } finally {
      isLoading.value = false;
    }
  }
  
  /// 重置表单
  void resetForm() {
    currentProcess.value = null;
    selectedProcessType.value = RecyclingProcessType.pending;
    cost.value = 0.0;
    income.value = 0.0;
    profit.value = 0.0;
    remark.value = '';
    imageUrls.clear();
    tempImages.clear();
    
    // 根据默认处理类型更新成本和收益预估
    updateEstimates();
  }
  
  /// 更改处理类型
  void changeProcessType(RecyclingProcessType type) {
    selectedProcessType.value = type;
    updateEstimates();
  }
  
  /// 更新成本和收益预估
  void updateEstimates() {
    if (selectedProcessType.value == RecyclingProcessType.pending) {
      cost.value = 0.0;
      income.value = 0.0;
      profit.value = 0.0;
      return;
    }
    
    cost.value = _processService.calculateProcessCost(selectedProcessType.value, recyclingItem);
    income.value = _processService.calculateEstimatedIncome(selectedProcessType.value, recyclingItem);
    profit.value = income.value - cost.value;
  }
  
  /// 从相册选择图片
  Future<void> pickImage() async {
    final image = await _imageUploadService.pickImageFromGallery();
    if (image != null) {
      tempImages.add(image);
    }
  }
  
  /// 拍照
  Future<void> takePhoto() async {
    final image = await _imageUploadService.takePhoto();
    if (image != null) {
      tempImages.add(image);
    }
  }
  
  /// 删除临时图片
  void removeTempImage(int index) {
    if (index >= 0 && index < tempImages.length) {
      tempImages.removeAt(index);
    }
  }
  
  /// 删除已上传图片
  Future<void> removeUploadedImage(int index) async {
    if (index >= 0 && index < imageUrls.length) {
      final url = imageUrls[index];
      final success = await _imageUploadService.deleteImage(url);
      if (success) {
        imageUrls.removeAt(index);
      }
    }
  }
  
  /// 上传所有临时图片
  Future<List<String>> uploadTempImages() async {
    if (tempImages.isEmpty) return [];
    
    isUploadingImage.value = true;
    try {
      final urls = await _imageUploadService.uploadImages(tempImages.toList(), recyclingItemId: recyclingItem.id);
      imageUrls.addAll(urls);
      tempImages.clear();
      return urls;
    } finally {
      isUploadingImage.value = false;
    }
  }
  
  /// 提交处理记录
  Future<bool> submitProcess() async {
    if (selectedProcessType.value == RecyclingProcessType.pending) {
      Get.snackbar('错误', '请选择处理方式', snackPosition: SnackPosition.BOTTOM);
      return false;
    }
    
    isSubmitting.value = true;
    try {
      // 先上传图片
      if (tempImages.isNotEmpty) {
        await uploadTempImages();
      }
      
      // 创建处理记录
      final process = RecyclingProcess(
        id: currentProcess.value?.id,
        recyclingItemId: recyclingItem.id,
        processType: selectedProcessType.value,
        processNo: currentProcess.value?.processNo ?? _processService.generateProcessNo(),
        processTime: DateTime.now(),
        operatorId: 1, // 实际应用中应该获取当前用户ID
        operatorName: 'Admin', // 实际应用中应该获取当前用户名
        storeId: 1, // 实际应用中应该获取当前门店ID
        storeName: '总店', // 实际应用中应该获取当前门店名称
        cost: cost.value,
        income: income.value,
        profit: profit.value,
        remark: remark.value.isEmpty ? null : remark.value,
        status: 0, // 默认进行中
        imageUrls: imageUrls.isEmpty ? null : imageUrls.toList(),
      );
      
      bool success;
      if (currentProcess.value?.id != null) {
        success = await _processService.updateProcess(process);
      } else {
        success = await _processService.addProcess(process);
      }
      
      if (success) {
        await loadProcessList();
        resetForm();
      }
      
      return success;
    } finally {
      isSubmitting.value = false;
    }
  }
  
  /// 删除处理记录
  Future<bool> deleteProcess(int id) async {
    isSubmitting.value = true;
    try {
      final success = await _processService.deleteProcess(id);
      if (success) {
        await loadProcessList();
        if (currentProcess.value?.id == id) {
          resetForm();
        }
      }
      return success;
    } finally {
      isSubmitting.value = false;
    }
  }
  
  /// 更新处理记录状态
  Future<bool> updateProcessStatus(int id, int status) async {
    isSubmitting.value = true;
    try {
      final success = await _processService.updateProcessStatus(id, status);
      if (success) {
        await loadProcessList();
        if (currentProcess.value?.id == id) {
          await loadProcessDetail(id);
        }
      }
      return success;
    } finally {
      isSubmitting.value = false;
    }
  }
  
  /// 获取处理方式说明
  String getProcessTypeDescription() {
    return _processService.getProcessTypeDescription(selectedProcessType.value);
  }
} 