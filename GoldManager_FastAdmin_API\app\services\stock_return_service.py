"""
退货管理业务服务类

老板，这个模块提供退货管理的核心业务逻辑，包括：
1. 退货单的CRUD操作
2. 智能单号生成
3. 审核流程管理
4. 统计分析功能
5. 业务规则验证

完整的退货业务流程管理服务。
"""

import time
from datetime import datetime
from typing import List, Optional, Dict, Any
from decimal import Decimal
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc

from app.models.stock_return import StockReturn, StockReturnItem
from app.models.store import Store
from app.models.admin import Admin
from app.models.jewelry import Jewelry, JewelryCategory
from app.schemas.stock_return import (
    StockReturnCreate, StockReturnUpdate, StockReturnAuditUpdate,
    StockReturnResponse, StockReturnStatistics, StockReturnStatus
)


class StockReturnService:
    """退货管理服务类"""

    def __init__(self, db: Session):
        self.db = db

    def generate_order_no(self) -> str:
        """生成退货单号 - RETYYYYMMDD0001格式"""
        now = datetime.now()
        prefix = f"RET{now.year}{now.month:02d}{now.day:02d}"

        # 查询当天已有的退货单数量
        count = self.db.query(StockReturn).filter(
            StockReturn.order_no.like(f"{prefix}%")
        ).count()

        return f"{prefix}{count + 1:04d}"

    def create_stock_return(self, stock_return_data: StockReturnCreate, operator_id: int) -> StockReturnResponse:
        """创建退货单"""
        # 验证门店是否存在
        store = self.db.query(Store).filter(Store.id == stock_return_data.store_id).first()
        if not store:
            raise ValueError(f"门店ID {stock_return_data.store_id} 不存在")

        # 验证操作员是否存在
        operator = self.db.query(Admin).filter(Admin.id == operator_id).first()
        if not operator:
            raise ValueError(f"操作员ID {operator_id} 不存在")

        # 生成退货单号
        order_no = self.generate_order_no()
        current_time = int(time.time())

        # 计算总金额
        total_amount = sum(item.amount for item in stock_return_data.items)
        actual_amount = total_amount - stock_return_data.discount_amount

        # 创建退货单
        db_stock_return = StockReturn(
            order_no=order_no,
            store_id=stock_return_data.store_id,
            customer=stock_return_data.customer,
            total_amount=total_amount,
            discount_amount=stock_return_data.discount_amount,
            actual_amount=actual_amount,
            remark=stock_return_data.remark,
            operator_id=operator_id,
            status=StockReturnStatus.PENDING,
            createtime=current_time,
            updatetime=current_time
        )

        self.db.add(db_stock_return)
        self.db.flush()  # 获取ID

        # 创建退货明细
        for item_data in stock_return_data.items:
            # 验证商品是否存在
            if item_data.jewelry_id:
                jewelry = self.db.query(Jewelry).filter(Jewelry.id == item_data.jewelry_id).first()
                if not jewelry:
                    raise ValueError(f"商品ID {item_data.jewelry_id} 不存在")

            db_item = StockReturnItem(
                stock_return_id=db_stock_return.id,
                jewelry_id=item_data.jewelry_id,
                barcode=item_data.barcode,
                name=item_data.name,
                category_id=item_data.category_id,
                ring_size=item_data.ring_size,
                gold_weight=item_data.gold_weight,
                silver_weight=item_data.silver_weight,
                total_weight=item_data.total_weight,
                gold_price=item_data.gold_price,
                silver_price=item_data.silver_price,
                silver_work_price=item_data.silver_work_price,
                piece_work_price=item_data.piece_work_price,
                amount=item_data.amount,
                source_order_no=item_data.source_order_no,
                createtime=current_time,
                updatetime=current_time
            )
            self.db.add(db_item)

        self.db.commit()
        self.db.refresh(db_stock_return)

        return self._convert_to_response(db_stock_return)

    def get_stock_return_list(
        self,
        page: int = 1,
        page_size: int = 20,
        store_id: Optional[int] = None,
        status: Optional[int] = None,
        operator_id: Optional[int] = None,
        customer: Optional[str] = None,
        keyword: Optional[str] = None,
        start_time: Optional[int] = None,
        end_time: Optional[int] = None
    ) -> Dict[str, Any]:
        """获取退货单列表"""
        query = self.db.query(StockReturn).options(
            joinedload(StockReturn.store),
            joinedload(StockReturn.operator),
            joinedload(StockReturn.auditor),
            joinedload(StockReturn.items)
        )

        # 应用筛选条件
        if store_id:
            query = query.filter(StockReturn.store_id == store_id)

        if status is not None:
            query = query.filter(StockReturn.status == status)

        if operator_id:
            query = query.filter(StockReturn.operator_id == operator_id)

        if customer:
            query = query.filter(StockReturn.customer.like(f"%{customer}%"))

        if keyword:
            query = query.filter(
                or_(
                    StockReturn.order_no.like(f"%{keyword}%"),
                    StockReturn.customer.like(f"%{keyword}%"),
                    StockReturn.remark.like(f"%{keyword}%")
                )
            )

        if start_time:
            query = query.filter(StockReturn.createtime >= start_time)

        if end_time:
            query = query.filter(StockReturn.createtime <= end_time)

        # 按创建时间倒序排列
        query = query.order_by(desc(StockReturn.createtime))

        # 分页
        total = query.count()
        items = query.offset((page - 1) * page_size).limit(page_size).all()

        return {
            "items": [self._convert_to_response(item) for item in items],
            "total": total,
            "page": page,
            "page_size": page_size,
            "pages": (total + page_size - 1) // page_size
        }

    def get_stock_return_by_id(self, stock_return_id: int) -> Optional[StockReturnResponse]:
        """根据ID获取退货单详情"""
        stock_return = self.db.query(StockReturn).options(
            joinedload(StockReturn.store),
            joinedload(StockReturn.operator),
            joinedload(StockReturn.auditor),
            joinedload(StockReturn.items).joinedload(StockReturnItem.jewelry),
            joinedload(StockReturn.items).joinedload(StockReturnItem.category)
        ).filter(StockReturn.id == stock_return_id).first()

        if not stock_return:
            return None

        return self._convert_to_response(stock_return)

    def get_stock_return_by_order_no(self, order_no: str) -> Optional[StockReturnResponse]:
        """根据单号获取退货单详情"""
        stock_return = self.db.query(StockReturn).options(
            joinedload(StockReturn.store),
            joinedload(StockReturn.operator),
            joinedload(StockReturn.auditor),
            joinedload(StockReturn.items).joinedload(StockReturnItem.jewelry),
            joinedload(StockReturn.items).joinedload(StockReturnItem.category)
        ).filter(StockReturn.order_no == order_no).first()

        if not stock_return:
            return None

        return self._convert_to_response(stock_return)

    def _convert_to_response(self, stock_return: StockReturn) -> StockReturnResponse:
        """转换为响应模型"""
        # 转换退货明细
        items = []
        for item in stock_return.items:
            item_dict = {
                "id": item.id,
                "stock_return_id": item.stock_return_id,
                "jewelry_id": item.jewelry_id,
                "barcode": item.barcode or "",
                "name": item.name or "未知商品",
                "category_id": item.category_id,
                "ring_size": item.ring_size,
                "gold_weight": item.gold_weight,
                "silver_weight": item.silver_weight,
                "total_weight": item.total_weight,
                "gold_price": item.gold_price,
                "silver_price": item.silver_price,
                "silver_work_price": item.silver_work_price,
                "piece_work_price": item.piece_work_price,
                "amount": item.amount,
                "source_order_no": item.source_order_no,
                "createtime": item.createtime,
                "updatetime": item.updatetime,
                "jewelry_name": item.jewelry.name if item.jewelry else None,
                "category_name": item.category.name if item.category else None
            }
            items.append(item_dict)

        # 构建响应数据
        response_data = {
            "id": stock_return.id,
            "order_no": stock_return.order_no,
            "store_id": stock_return.store_id,
            "customer": stock_return.customer,
            "total_amount": stock_return.total_amount,
            "discount_amount": stock_return.discount_amount,
            "actual_amount": stock_return.actual_amount,
            "remark": stock_return.remark,
            "operator_id": stock_return.operator_id,
            "status": stock_return.status,
            "audit_user_id": stock_return.audit_user_id,
            "audit_time": stock_return.audit_time,
            "audit_remark": stock_return.audit_remark,
            "createtime": stock_return.createtime,
            "updatetime": stock_return.updatetime,
            "store_name": stock_return.store.name if stock_return.store else None,
            "operator_name": stock_return.operator.username if stock_return.operator else None,
            "auditor_name": stock_return.auditor.username if stock_return.auditor else None,
            "items": items
        }

        return StockReturnResponse(**response_data)

    def update_stock_return(self, stock_return_id: int, stock_return_data: StockReturnUpdate) -> Optional[StockReturnResponse]:
        """更新退货单"""
        stock_return = self.db.query(StockReturn).filter(StockReturn.id == stock_return_id).first()
        if not stock_return:
            return None

        # 只有待审核状态才能修改
        if stock_return.status != StockReturnStatus.PENDING:
            raise ValueError("只有待审核状态的退货单才能修改")

        # 更新基本信息
        if stock_return_data.store_id is not None:
            # 验证门店是否存在
            store = self.db.query(Store).filter(Store.id == stock_return_data.store_id).first()
            if not store:
                raise ValueError(f"门店ID {stock_return_data.store_id} 不存在")
            stock_return.store_id = stock_return_data.store_id

        if stock_return_data.customer is not None:
            stock_return.customer = stock_return_data.customer

        if stock_return_data.total_amount is not None:
            stock_return.total_amount = stock_return_data.total_amount

        if stock_return_data.discount_amount is not None:
            stock_return.discount_amount = stock_return_data.discount_amount

        if stock_return_data.actual_amount is not None:
            stock_return.actual_amount = stock_return_data.actual_amount

        if stock_return_data.remark is not None:
            stock_return.remark = stock_return_data.remark

        # 更新时间
        stock_return.updatetime = int(time.time())

        # 如果有明细更新，先删除原有明细再添加新明细
        if stock_return_data.items is not None:
            # 删除原有明细
            self.db.query(StockReturnItem).filter(
                StockReturnItem.stock_return_id == stock_return_id
            ).delete()

            # 添加新明细
            current_time = int(time.time())
            for item_data in stock_return_data.items:
                # 验证商品是否存在
                if item_data.jewelry_id:
                    jewelry = self.db.query(Jewelry).filter(Jewelry.id == item_data.jewelry_id).first()
                    if not jewelry:
                        raise ValueError(f"商品ID {item_data.jewelry_id} 不存在")

                db_item = StockReturnItem(
                    stock_return_id=stock_return.id,
                    jewelry_id=item_data.jewelry_id,
                    barcode=item_data.barcode or "",
                    name=item_data.name or "",
                    category_id=item_data.category_id,
                    ring_size=item_data.ring_size,
                    gold_weight=item_data.gold_weight,
                    silver_weight=item_data.silver_weight,
                    total_weight=item_data.total_weight,
                    gold_price=item_data.gold_price,
                    silver_price=item_data.silver_price,
                    silver_work_price=item_data.silver_work_price,
                    piece_work_price=item_data.piece_work_price,
                    amount=item_data.amount if hasattr(item_data, 'amount') else Decimal('0.00'),
                    source_order_no=item_data.source_order_no if hasattr(item_data, 'source_order_no') else None,
                    createtime=current_time,
                    updatetime=current_time
                )
                self.db.add(db_item)

        self.db.commit()
        self.db.refresh(stock_return)

        return self.get_stock_return_by_id(stock_return_id)

    def delete_stock_return(self, stock_return_id: int) -> bool:
        """删除退货单"""
        stock_return = self.db.query(StockReturn).filter(StockReturn.id == stock_return_id).first()
        if not stock_return:
            return False

        # 只有待审核状态才能删除
        if stock_return.status != StockReturnStatus.PENDING:
            raise ValueError("只有待审核状态的退货单才能删除")

        # 删除明细（级联删除）
        self.db.delete(stock_return)
        self.db.commit()

        return True

    def audit_stock_return(self, stock_return_id: int, audit_data: StockReturnAuditUpdate, auditor_id: int) -> Optional[StockReturnResponse]:
        """审核退货单"""
        stock_return = self.db.query(StockReturn).filter(StockReturn.id == stock_return_id).first()
        if not stock_return:
            return None

        # 只有待审核状态才能审核
        if stock_return.status != StockReturnStatus.PENDING:
            raise ValueError("只有待审核状态的退货单才能审核")

        # 验证审核员是否存在
        auditor = self.db.query(Admin).filter(Admin.id == auditor_id).first()
        if not auditor:
            raise ValueError(f"审核员ID {auditor_id} 不存在")

        # 更新审核信息
        stock_return.status = audit_data.status
        stock_return.audit_user_id = auditor_id
        stock_return.audit_time = int(time.time())
        stock_return.audit_remark = audit_data.audit_remark
        stock_return.updatetime = int(time.time())

        self.db.commit()
        self.db.refresh(stock_return)

        return self.get_stock_return_by_id(stock_return_id)

    def get_statistics(self) -> StockReturnStatistics:
        """获取退货单统计信息"""
        # 基础统计
        total_count = self.db.query(StockReturn).count()
        pending_count = self.db.query(StockReturn).filter(StockReturn.status == StockReturnStatus.PENDING).count()
        approved_count = self.db.query(StockReturn).filter(StockReturn.status == StockReturnStatus.APPROVED).count()
        rejected_count = self.db.query(StockReturn).filter(StockReturn.status == StockReturnStatus.REJECTED).count()
        cancelled_count = self.db.query(StockReturn).filter(StockReturn.status == StockReturnStatus.CANCELLED).count()

        # 金额统计
        amount_stats = self.db.query(
            func.sum(StockReturn.total_amount).label('total_amount'),
            func.sum(StockReturn.discount_amount).label('total_discount'),
            func.sum(StockReturn.actual_amount).label('total_actual')
        ).first()

        total_amount = amount_stats.total_amount or Decimal('0.00')
        total_discount = amount_stats.total_discount or Decimal('0.00')
        total_actual = amount_stats.total_actual or Decimal('0.00')

        # 状态分布
        status_stats = self.db.query(
            StockReturn.status,
            func.count(StockReturn.id).label('count')
        ).group_by(StockReturn.status).all()

        status_distribution = {}
        for stat in status_stats:
            status_name = StockReturnStatus.get_status_name(stat.status)
            status_distribution[status_name] = stat.count

        # 门店分布
        store_stats = self.db.query(
            Store.name,
            func.count(StockReturn.id).label('count')
        ).join(StockReturn, Store.id == StockReturn.store_id).group_by(Store.name).all()

        store_distribution = {}
        for stat in store_stats:
            store_distribution[stat.name] = stat.count

        return StockReturnStatistics(
            total_count=total_count,
            pending_count=pending_count,
            approved_count=approved_count,
            rejected_count=rejected_count,
            cancelled_count=cancelled_count,
            total_amount=total_amount,
            total_discount=total_discount,
            total_actual=total_actual,
            status_distribution=status_distribution,
            store_distribution=store_distribution
        )
