# 金包银首饰管理系统 Flutter 版

基于原有 FastAdmin 框架的金包银首饰管理系统(jbycrm)转写的 Flutter 全平台客户端应用，支持 Windows、macOS、iOS、Android 等平台。

## 项目概述

金包银首饰管理系统是一套专业的首饰店管理系统，主要功能包括：

- 首饰库存管理
- 销售管理
- 门店管理
- 旧料回收管理
- 盘点管理
- 统计分析
- 权限管理

## 技术栈

- Flutter SDK: 3.10.0+
- Dart: 3.0.0+
- 状态管理: GetX
- 数据库: SQLite (本地) / MySQL (服务端)
- API: RESTful API
- 网络请求: Dio
- 本地存储: Shared Preferences, Hive
- UI 库: Flutter Material Design

## 开发环境设置

1. 安装 Flutter SDK

   ```bash
   # 请参考Flutter官方文档安装SDK
   # https://flutter.dev/docs/get-started/install
   ```

2. 配置 IDE (推荐 VS Code 或 Android Studio)

3. 克隆项目仓库

   ```bash
   git clone https://your-repository-url/gold_manager_flutter.git
   cd gold_manager_flutter
   ```

4. 安装依赖

   ```bash
   flutter pub get
   ```

5. 配置环境

   - 修改`lib/core/config/app_config.dart`中的 API 端点配置

6. 运行项目

   ```bash
   # 运行Windows应用
   flutter run -d windows

   # 运行Android应用
   flutter run -d android

   # 运行iOS应用（需要在macOS上）
   flutter run -d ios

   # 运行Web应用
   flutter run -d chrome
   ```

## 项目结构

项目采用基于功能的结构组织，主要目录如下：

```
lib/
├── app/                # 应用核心
├── core/               # 核心功能和工具
│   ├── config/         # 配置
│   ├── routes/         # 路由定义
│   ├── services/       # 核心服务
│   ├── theme/          # 主题定义
│   ├── translations/   # 国际化
│   └── utils/          # 工具类
├── features/           # 功能模块
│   ├── auth/           # 认证模块
│   ├── dashboard/      # 仪表盘
│   ├── jewelry/        # 首饰管理
│   ├── stock/          # 库存管理
│   ├── sales/          # 销售管理
│   ├── recycling/      # 旧料回收
│   └── statistics/     # 统计分析
├── models/             # 数据模型
├── widgets/            # 公共组件
└── main.dart           # 入口文件
```

## API 文档

系统使用的 API 端点如下：

| 功能         | 方法 | 端点              | 描述         |
| ------------ | ---- | ----------------- | ------------ |
| 登录         | POST | `/auth/login`     | 用户登录     |
| 获取首饰列表 | GET  | `/jewelry/index`  | 获取首饰列表 |
| 首饰详情     | GET  | `/jewelry/detail` | 获取首饰详情 |
| ...          | ...  | ...               | ...          |

具体 API 文档请参考后端服务文档。

## 构建发布

```bash
# 构建Windows应用
flutter build windows --release

# 构建Android应用
flutter build apk --release

# 构建iOS应用
flutter build ios --release

# 构建Web应用
flutter build web --release
```

## 开发路线图

- [x] 项目初始化
- [x] 基础架构搭建
- [ ] 认证模块
- [ ] 仪表盘
- [ ] 首饰管理模块
- [ ] 库存管理模块
- [ ] 销售管理模块
- [ ] 旧料回收模块
- [ ] 统计分析模块
- [ ] 系统设置
- [ ] 多平台适配
- [ ] 离线模式
- [ ] 性能优化
- [ ] 测试与发布

## 贡献指南

欢迎贡献代码、报告问题或提供改进建议。请确保提交的代码遵循项目的代码规范。

## 许可协议

[在此添加许可证信息]

## 金包银首饰管理系统（Flutter 版）

### 项目概述

本项目是一个金包银首饰管理系统，实现首饰店铺的库存管理、销售管理、旧料回收、成本分析等功能。使用 Flutter 框架开发，支持 Windows、macOS、Android、iOS 等多平台部署。

### 功能模块

- **用户认证管理**：用户登录、权限控制
- **首饰管理**：商品信息管理、分类管理
- **库存管理**：入库、出库、库存查询、盘点管理
- **销售管理**：销售单管理、退货管理
- **旧料回收**：回收单管理、旧料处理、金属分离
- **报表统计**：销售报表、库存报表、成本分析
- **系统设置**：参数设置、数据备份

### 技术架构

- **框架**：Flutter 3.32.0
- **状态管理**：GetX
- **存储**：SharedPreferences, Hive, SQLite
- **网络**：Dio, HTTP
- **UI**：Material Design

### 旧料回收模块

旧料回收模块是专为首饰店设计的旧料回收管理功能，包括：

1. **回收单管理**

   - 回收单创建、编辑和删除
   - 回收单状态流转（待处理、已处理、已完成、已取消）
   - 回收单详情查看

2. **回收物品管理**
   - 支持多种物品类型（黄金首饰、银饰、金银混合、其他）
   - 物品重量和金额计算
   - 物品图片上传和管理
3. **金属分离**

   - 记录金属分离过程
   - 分离后的金属重量和价值计算
   - 分离损耗记录和分析

4. **数据统计**
   - 回收量统计和分析
   - 金属分离效率分析
   - 利润分析

### 开发进度

目前已完成：

- 回收单列表页面
- 回收单详情页面
- 回收单表单页面
- 回收单模型单元测试
- 回收单列表页面测试
- **新建出库单退换货功能**：完整的退换货对话框，支持选择已售商品进行退换货处理
- **旧料回收界面"件数"列功能**：在数据表格中添加件数显示，支持 API 对接和响应式布局

#### 最新完成功能：退换货管理

**功能描述**：

- 在新建出库单页面提供退换货功能
- 支持查询已收款的出库单和商品明细
- 可选择已售商品进行退换货，自动以负数价格添加到当前出库单
- 严格遵循 UI 设计规范，提供良好的用户体验

**技术实现**：

- 创建了`ReturnExchangeDialog`组件，遵循统一边框调用方式标准
- 在`StockOutFormController`中添加了`handleReturnExchange()`方法
- 实现了店铺级数据过滤和权限控制
- 支持实时搜索和多选操作

**文件位置**：

- 对话框组件：`lib/features/stock/widgets/return_exchange_dialog.dart`
- 控制器方法：`lib/features/stock/controllers/stock_out_form_controller.dart`
- 测试文档：`test_return_exchange.md`

#### 最新完成功能：旧料回收界面"件数"列

**功能描述**：

- 在旧料回收界面的数据表格中添加"件数"列，显示每个回收单的物品件数
- 支持桌面端表格视图和移动端卡片视图的响应式显示
- 集成真实 API 调用，支持后端数据对接和模拟数据后备

**技术实现**：

- 更新了`RecyclingOrder`数据模型，添加`itemCount`字段
- 调整了表格列宽分配，保持与出库管理界面的视觉一致性
- 实现了真实 API 调用，支持错误处理和模拟数据降级
- 优化了字段映射，兼容后端 API 的响应格式

**文件位置**：

- 数据模型：`lib/models/recycling/recycling_model.dart`
- 界面组件：`lib/features/recycling/presentation/recycling_list_page.dart`
- 服务层：`lib/features/recycling/services/recycling_service.dart`

正在开发：

- 金属分离功能
- 数据统计功能
- 更多测试用例

### 安装和运行

```bash
# 克隆项目
git clone https://github.com/yourusername/gold_manager_flutter.git

# 进入项目目录
cd gold_manager_flutter

# 安装依赖
flutter pub get

# 运行项目
flutter run
```

### 测试

```bash
# 运行所有测试
flutter test

# 运行特定测试
flutter test test/recycling/recycling_model_test.dart
```
