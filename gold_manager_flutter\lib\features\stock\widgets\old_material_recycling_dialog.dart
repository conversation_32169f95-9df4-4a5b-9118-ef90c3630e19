import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

import '../../../core/constants/border_styles.dart';
import '../../../core/utils/barcode_generator.dart';
import '../../../core/utils/logger.dart';
import '../../../models/common/enums.dart';
import '../../../models/jewelry/jewelry_category.dart';
import '../../../models/recycling/old_material_item.dart';
import '../../../services/jewelry_service.dart';

/// 旧料回收对话框控制器
class OldMaterialRecyclingDialogController extends GetxController {
  final JewelryService _jewelryService = Get.find<JewelryService>();
  
  // 表单控制器
  final TextEditingController barcodeController = TextEditingController();
  final TextEditingController goldWeightController = TextEditingController();
  final TextEditingController goldPriceController = TextEditingController();
  final TextEditingController silverWeightController = TextEditingController();
  final TextEditingController silverPriceController = TextEditingController();
  final TextEditingController discountController = TextEditingController();
  final TextEditingController remarkController = TextEditingController();
  
  // 响应式数据
  final RxList<JewelryCategory> categoryList = <JewelryCategory>[].obs;
  final Rx<OldMaterialRecyclingType> selectedRecyclingType = OldMaterialRecyclingType.byWeight.obs;
  final Rx<MetalPurity> selectedPurity = MetalPurity.goldSilver.obs;
  final RxInt selectedCategoryId = 0.obs;
  final RxDouble totalWeight = 0.0.obs;
  final RxDouble payableAmount = 0.0.obs;
  final RxBool isLoading = false.obs;
  
  @override
  void onInit() {
    super.onInit();
    _initializeData();
    _setupCalculationListeners();
  }
  
  @override
  void onClose() {
    barcodeController.dispose();
    goldWeightController.dispose();
    goldPriceController.dispose();
    silverWeightController.dispose();
    silverPriceController.dispose();
    discountController.dispose();
    remarkController.dispose();
    super.onClose();
  }
  
  /// 初始化数据
  Future<void> _initializeData() async {
    try {
      isLoading.value = true;
      
      // 生成旧料条码
      final barcode = await BarcodeGenerator.generateOldMaterialBarcode();
      barcodeController.text = barcode;
      
      // 设置默认折扣
      discountController.text = '100';
      
      // 获取分类列表
      await _fetchCategories();
      
    } catch (e) {
      LoggerService.e('初始化旧料回收对话框失败', e);
      Get.snackbar('错误', '初始化失败: ${e.toString()}');
    } finally {
      isLoading.value = false;
    }
  }
  
  /// 获取分类列表
  Future<void> _fetchCategories() async {
    try {
      final result = await _jewelryService.getCategories();
      categoryList.value = result;
      
      // 默认选择第一个分类
      if (categoryList.isNotEmpty) {
        selectedCategoryId.value = categoryList.first.id;
      }
    } catch (e) {
      LoggerService.e('获取分类列表失败', e);
    }
  }
  
  /// 设置计算监听器
  void _setupCalculationListeners() {
    // 监听重量和价格变化，实时计算
    goldWeightController.addListener(_calculateTotals);
    goldPriceController.addListener(_calculateTotals);
    silverWeightController.addListener(_calculateTotals);
    silverPriceController.addListener(_calculateTotals);
    discountController.addListener(_calculateTotals);
  }
  
  /// 计算总重和应付金额
  void _calculateTotals() {
    try {
      final goldWeight = double.tryParse(goldWeightController.text) ?? 0.0;
      final goldPrice = double.tryParse(goldPriceController.text) ?? 0.0;
      final silverWeight = double.tryParse(silverWeightController.text) ?? 0.0;
      final silverPrice = double.tryParse(silverPriceController.text) ?? 0.0;
      final discount = double.tryParse(discountController.text) ?? 100.0;
      
      // 计算总重
      totalWeight.value = goldWeight + silverWeight;
      
      // 计算应付金额
      final goldAmount = goldWeight * goldPrice;
      final silverAmount = silverWeight * silverPrice;
      final totalAmount = goldAmount + silverAmount;
      payableAmount.value = totalAmount * (discount / 100);
      
    } catch (e) {
      LoggerService.w('计算总重和应付金额失败: $e');
    }
  }
  
  /// 验证表单
  bool _validateForm() {
    if (barcodeController.text.trim().isEmpty) {
      Get.snackbar('验证失败', '请输入旧料条码');
      return false;
    }
    
    if (selectedCategoryId.value == 0) {
      Get.snackbar('验证失败', '请选择分类');
      return false;
    }
    
    final goldWeight = double.tryParse(goldWeightController.text) ?? 0.0;
    final silverWeight = double.tryParse(silverWeightController.text) ?? 0.0;
    
    if (goldWeight <= 0 && silverWeight <= 0) {
      Get.snackbar('验证失败', '金重和银重不能同时为0');
      return false;
    }
    
    if (goldWeight > 0 && (double.tryParse(goldPriceController.text) ?? 0.0) <= 0) {
      Get.snackbar('验证失败', '金重大于0时，回收金价必须大于0');
      return false;
    }
    
    if (silverWeight > 0 && (double.tryParse(silverPriceController.text) ?? 0.0) <= 0) {
      Get.snackbar('验证失败', '银重大于0时，回收银价必须大于0');
      return false;
    }
    
    final discount = double.tryParse(discountController.text) ?? 0.0;
    if (discount <= 0 || discount > 100) {
      Get.snackbar('验证失败', '折扣必须在0-100之间');
      return false;
    }
    
    return true;
  }
  
  /// 创建旧料回收项
  OldMaterialItem createOldMaterialItem() {
    if (!_validateForm()) {
      throw Exception('表单验证失败');
    }
    
    final selectedCategory = categoryList.firstWhereOrNull(
      (category) => category.id == selectedCategoryId.value,
    );
    
    return OldMaterialItem(
      barcode: barcodeController.text.trim(),
      recyclingType: selectedRecyclingType.value,
      purity: selectedPurity.value,
      categoryId: selectedCategoryId.value,
      categoryName: selectedCategory?.name,
      goldWeight: double.tryParse(goldWeightController.text) ?? 0.0,
      goldPrice: double.tryParse(goldPriceController.text) ?? 0.0,
      silverWeight: double.tryParse(silverWeightController.text) ?? 0.0,
      silverPrice: double.tryParse(silverPriceController.text) ?? 0.0,
      discount: double.tryParse(discountController.text) ?? 100.0,
      remark: remarkController.text.trim(),
      category: selectedCategory,
    );
  }
}

/// 旧料回收对话框
class OldMaterialRecyclingDialog extends StatelessWidget {
  const OldMaterialRecyclingDialog({super.key});
  
  @override
  Widget build(BuildContext context) {
    final controller = Get.put(OldMaterialRecyclingDialogController());
    
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppBorderStyles.mediumBorderRadius),
      ),
      child: Container(
        width: 600,
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(AppBorderStyles.mediumBorderRadius),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Obx(() {
          if (controller.isLoading.value) {
            return const SizedBox(
              height: 200,
              child: Center(child: CircularProgressIndicator()),
            );
          }
          
          return Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题
              Row(
                children: [
                  Icon(Icons.recycling, color: Colors.purple[600], size: 24),
                  const SizedBox(width: 8),
                  const Text(
                    '旧料回收',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Get.back(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              
              const SizedBox(height: 24),
              
              // 表单内容
              _buildFormContent(controller),
              
              const SizedBox(height: 24),
              
              // 底部按钮
              _buildBottomButtons(controller),
            ],
          );
        }),
      ),
    );
  }
  
  Widget _buildFormContent(OldMaterialRecyclingDialogController controller) {
    return Column(
      children: [
        // 旧料信息区域
        _buildOldMaterialInfoSection(controller),
        
        const SizedBox(height: 16),
        
        // 回收信息区域
        _buildRecyclingInfoSection(controller),
      ],
    );
  }
  
  Widget _buildOldMaterialInfoSection(OldMaterialRecyclingDialogController controller) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: AppBorderStyles.standardBoxDecoration,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '旧料信息',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 16),

          Row(
            children: [
              // 旧料条码
              Expanded(
                flex: 2,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('旧料条码', style: TextStyle(fontSize: 14)),
                    const SizedBox(height: 4),
                    Container(
                      height: 32,
                      decoration: AppBorderStyles.standardBoxDecoration,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                        child: TextField(
                          controller: controller.barcodeController,
                          decoration: const InputDecoration(
                            border: InputBorder.none,
                            enabledBorder: InputBorder.none,
                            focusedBorder: InputBorder.none,
                            disabledBorder: InputBorder.none,
                            contentPadding: EdgeInsets.symmetric(vertical: 8),
                            isDense: true,
                          ),
                          style: const TextStyle(fontSize: 13),
                          readOnly: true, // 自动生成，不允许编辑
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(width: 16),

              // 方式
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('方式', style: TextStyle(fontSize: 14)),
                    const SizedBox(height: 4),
                    Container(
                      height: 32,
                      decoration: AppBorderStyles.standardBoxDecoration,
                      child: Obx(() => DropdownButtonHideUnderline(
                        child: DropdownButton<OldMaterialRecyclingType>(
                          value: controller.selectedRecyclingType.value,
                          onChanged: (value) {
                            if (value != null) {
                              controller.selectedRecyclingType.value = value;
                            }
                          },
                          items: OldMaterialRecyclingType.values.map((type) {
                            return DropdownMenuItem(
                              value: type,
                              child: Center(
                                child: Text(
                                  type.label,
                                  style: const TextStyle(fontSize: 13, color: Colors.black),
                                ),
                              ),
                            );
                          }).toList(),
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                          style: const TextStyle(fontSize: 13, color: Colors.black),
                          isExpanded: true,
                        ),
                      )),
                    ),
                  ],
                ),
              ),

              const SizedBox(width: 16),

              // 成色
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('成色', style: TextStyle(fontSize: 14)),
                    const SizedBox(height: 4),
                    Container(
                      height: 32,
                      decoration: AppBorderStyles.standardBoxDecoration,
                      child: Obx(() => DropdownButtonHideUnderline(
                        child: DropdownButton<MetalPurity>(
                          value: controller.selectedPurity.value,
                          onChanged: (value) {
                            if (value != null) {
                              controller.selectedPurity.value = value;
                            }
                          },
                          items: MetalPurity.values.map((purity) {
                            return DropdownMenuItem(
                              value: purity,
                              child: Center(
                                child: Text(
                                  purity.label,
                                  style: const TextStyle(fontSize: 13, color: Colors.black),
                                ),
                              ),
                            );
                          }).toList(),
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                          style: const TextStyle(fontSize: 13, color: Colors.black),
                          isExpanded: true,
                        ),
                      )),
                    ),
                  ],
                ),
              ),

              const SizedBox(width: 16),

              // 分类
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('分类', style: TextStyle(fontSize: 14)),
                    const SizedBox(height: 4),
                    Container(
                      height: 32,
                      decoration: AppBorderStyles.standardBoxDecoration,
                      child: Obx(() => DropdownButtonHideUnderline(
                        child: DropdownButton<int>(
                          value: controller.selectedCategoryId.value == 0 ? null : controller.selectedCategoryId.value,
                          onChanged: (value) {
                            if (value != null) {
                              controller.selectedCategoryId.value = value;
                            }
                          },
                          items: controller.categoryList.map((category) {
                            return DropdownMenuItem(
                              value: category.id,
                              child: Center(
                                child: Text(
                                  category.name,
                                  style: const TextStyle(fontSize: 13, color: Colors.black),
                                ),
                              ),
                            );
                          }).toList(),
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                          style: const TextStyle(fontSize: 13, color: Colors.black),
                          isExpanded: true,
                          hint: const Center(
                            child: Text(
                              '请选择',
                              style: TextStyle(fontSize: 13, color: Colors.grey),
                            ),
                          ),
                        ),
                      )),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRecyclingInfoSection(OldMaterialRecyclingDialogController controller) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: AppBorderStyles.standardBoxDecoration,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '回收信息',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 16),

          // 第一行：金重、回收金价、银重、回收银价
          Row(
            children: [
              _buildNumberInputField('金重', '克', controller.goldWeightController),
              const SizedBox(width: 12),
              _buildNumberInputField('回收金价', '元/克', controller.goldPriceController),
              const SizedBox(width: 12),
              _buildNumberInputField('银重', '克', controller.silverWeightController),
              const SizedBox(width: 12),
              _buildNumberInputField('回收银价', '元/克', controller.silverPriceController),
            ],
          ),

          const SizedBox(height: 12),

          // 第二行：总重、折扣、应付金额
          Row(
            children: [
              // 总重（只读显示）
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('总重', style: TextStyle(fontSize: 14)),
                    const SizedBox(height: 4),
                    Container(
                      height: 32,
                      decoration: AppBorderStyles.standardBoxDecoration.copyWith(
                        color: Colors.grey[100],
                      ),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        child: Row(
                          children: [
                            Expanded(
                              child: Obx(() => Text(
                                controller.totalWeight.value.toStringAsFixed(2),
                                style: const TextStyle(fontSize: 13),
                              )),
                            ),
                            const Text('克', style: TextStyle(fontSize: 12, color: Colors.grey)),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(width: 12),

              _buildNumberInputField('折扣', '%', controller.discountController),

              const SizedBox(width: 12),

              // 应付金额（只读显示）
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('应付金额', style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600, color: Colors.red)),
                    const SizedBox(height: 4),
                    Container(
                      height: 32,
                      decoration: AppBorderStyles.standardBoxDecoration.copyWith(
                        color: Colors.red[50],
                        border: Border.all(color: Colors.red[200]!, width: 1),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        child: Row(
                          children: [
                            Expanded(
                              child: Obx(() => Text(
                                controller.payableAmount.value.round().toString(),
                                style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w600, color: Colors.red),
                              )),
                            ),
                            const Text('元', style: TextStyle(fontSize: 12, color: Colors.red)),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(width: 12),

              // 占位，保持布局对齐
              const Expanded(child: SizedBox()),
            ],
          ),

          const SizedBox(height: 12),

          // 备注
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('备注', style: TextStyle(fontSize: 14)),
              const SizedBox(height: 4),
              Container(
                height: 32,
                decoration: AppBorderStyles.standardBoxDecoration,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                  child: TextField(
                    controller: controller.remarkController,
                    decoration: const InputDecoration(
                      hintText: '请输入备注信息',
                      border: InputBorder.none,
                      enabledBorder: InputBorder.none,
                      focusedBorder: InputBorder.none,
                      disabledBorder: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(vertical: 8),
                      isDense: true,
                    ),
                    style: const TextStyle(fontSize: 13),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildNumberInputField(String label, String unit, TextEditingController controller) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(label, style: const TextStyle(fontSize: 14)),
          const SizedBox(height: 4),
          Container(
            height: 32,
            decoration: AppBorderStyles.standardBoxDecoration,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
              child: Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: controller,
                      decoration: const InputDecoration(
                        border: InputBorder.none,
                        enabledBorder: InputBorder.none,
                        focusedBorder: InputBorder.none,
                        disabledBorder: InputBorder.none,
                        contentPadding: EdgeInsets.symmetric(vertical: 8),
                        isDense: true,
                      ),
                      style: const TextStyle(fontSize: 13),
                      keyboardType: const TextInputType.numberWithOptions(decimal: true),
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                      ],
                    ),
                  ),
                  Text(unit, style: const TextStyle(fontSize: 12, color: Colors.grey)),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomButtons(OldMaterialRecyclingDialogController controller) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        TextButton(
          onPressed: () => Get.back(),
          child: const Text('取消'),
        ),
        const SizedBox(width: 16),
        ElevatedButton(
          onPressed: () {
            try {
              final oldMaterialItem = controller.createOldMaterialItem();
              Get.back(result: oldMaterialItem);
            } catch (e) {
              LoggerService.e('创建旧料回收项失败', e);
            }
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue[600],
            foregroundColor: Colors.white,
          ),
          child: const Text('确定'),
        ),
      ],
    );
  }
}
