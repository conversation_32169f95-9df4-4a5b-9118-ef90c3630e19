/// 回收处理服务
/// 处理回收物品的金银分离、翻新加工、熔炼等业务

import 'package:get/get.dart';
import '../core/services/api_service.dart';
import '../core/utils/logger.dart';
import '../models/common/page_data.dart';
import '../models/recycling/recycling_process_new.dart';

class RecyclingProcessService extends GetxService {
  final ApiService _apiService = Get.find<ApiService>();

  /// 初始化服务
  Future<RecyclingProcessService> init() async {
    LoggerService.info('RecyclingProcessService 初始化');
    return this;
  }

  /// 创建处理工单
  Future<RecyclingProcessNew> createProcess({
    required int storeId,
    required int recyclingId,
    required List<int> recyclingItemIds,
    required int processType,
    String? remark,
  }) async {
    try {
      final data = {
        'store_id': storeId,
        'recycling_id': recyclingId,
        'recycling_item_ids': recyclingItemIds,
        'process_type': processType,
        'remark': remark,
      };

      final response = await _apiService.post('/recycling/process', data: data);
      return RecyclingProcessNew.fromJson(response.data);
    } catch (e) {
      LoggerService.error('创建处理工单失败', e);
      rethrow;
    }
  }

  /// 获取处理工单列表
  Future<PageData<RecyclingProcessNew>> getProcessList({
    int page = 1,
    int pageSize = 20,
    String? keyword,
    int? storeId,
    int? status,
    int? processType,
    String? startDate,
    String? endDate,
  }) async {
    try {
      final params = {
        'page': page,
        'page_size': pageSize,
        if (keyword != null) 'keyword': keyword,
        if (storeId != null) 'store_id': storeId,
        if (status != null) 'status': status,
        if (processType != null) 'process_type': processType,
        if (startDate != null) 'start_date': startDate,
        if (endDate != null) 'end_date': endDate,
      };

      final response = await _apiService.get('/recycling/process', queryParameters: params);
      
      final List<dynamic> dataList = response.data['data'] ?? [];
      final pagination = response.data['pagination'] ?? {};

      return PageData<RecyclingProcessNew>(
        data: dataList.map((json) => RecyclingProcessNew.fromJson(json)).toList(),
        currentPage: pagination['page'] ?? 1,
        lastPage: pagination['pages'] ?? 1,
        total: pagination['total'] ?? 0,
      );
    } catch (e) {
      LoggerService.error('获取处理工单列表失败', e);
      rethrow;
    }
  }

  /// 获取处理工单详情
  Future<RecyclingProcessNew> getProcessDetail(int processId) async {
    try {
      final response = await _apiService.get('/recycling/process/$processId');
      return RecyclingProcessNew.fromJson(response.data);
    } catch (e) {
      LoggerService.error('获取处理工单详情失败', e);
      rethrow;
    }
  }

  /// 更新处理工单
  Future<RecyclingProcessNew> updateProcess(int processId, {
    int? storeId,
    int? processorId,
    String? remark,
  }) async {
    try {
      final data = <String, dynamic>{};
      if (storeId != null) data['store_id'] = storeId;
      if (processorId != null) data['processor_id'] = processorId;
      if (remark != null) data['remark'] = remark;

      final response = await _apiService.put('/recycling/process/$processId', data: data);
      return RecyclingProcessNew.fromJson(response.data);
    } catch (e) {
      LoggerService.error('更新处理工单失败', e);
      rethrow;
    }
  }

  /// 开始处理
  Future<RecyclingProcessNew> startProcess(int processId) async {
    try {
      final response = await _apiService.post('/recycling/process/$processId/start');
      return RecyclingProcessNew.fromJson(response.data);
    } catch (e) {
      LoggerService.error('开始处理失败', e);
      rethrow;
    }
  }

  /// 完成处理
  Future<RecyclingProcessNew> completeProcess(
    int processId, {
    required List<ProcessResult> results,
    int? processorId,
  }) async {
    try {
      final data = {
        'results': results.map((r) => r.toJson()).toList(),
        if (processorId != null) 'processor_id': processorId,
      };

      final response = await _apiService.post('/recycling/process/$processId/complete', data: data);
      return RecyclingProcessNew.fromJson(response.data);
    } catch (e) {
      LoggerService.error('完成处理失败', e);
      rethrow;
    }
  }

  /// 取消处理工单
  Future<bool> cancelProcess(int processId) async {
    try {
      await _apiService.delete('/recycling/process/$processId');
      return true;
    } catch (e) {
      LoggerService.error('取消处理工单失败', e);
      rethrow;
    }
  }

  /// 转为库存
  Future<RecyclingToStock> convertToStock({
    required int storeId,
    required int processResultId,
    required int stockType,
    int quantity = 1,
    String? remark,
    Map<String, dynamic>? jewelryInfo,
    int? materialType,
  }) async {
    try {
      final data = {
        'store_id': storeId,
        'process_result_id': processResultId,
        'stock_type': stockType,
        'quantity': quantity,
        'remark': remark,
        if (jewelryInfo != null) 'jewelry_info': jewelryInfo,
        if (materialType != null) 'material_type': materialType,
      };

      final response = await _apiService.post('/recycling/convert-to-stock', data: data);
      return RecyclingToStock.fromJson(response.data);
    } catch (e) {
      LoggerService.error('转库存失败', e);
      rethrow;
    }
  }

  /// 获取可处理的回收明细
  Future<List<Map<String, dynamic>>> getAvailableRecyclingItems(int recyclingId) async {
    try {
      // 获取回收单详情，包含明细
      final response = await _apiService.get('/recycling/$recyclingId');
      final items = response.data['items'] as List<dynamic>? ?? [];
      
      // 筛选未处理的明细
      return items
          .where((item) => item['process_status'] == 0)
          .map((item) => Map<String, dynamic>.from(item))
          .toList();
    } catch (e) {
      LoggerService.error('获取可处理的回收明细失败', e);
      rethrow;
    }
  }

  /// 批量更新明细处理状态
  Future<bool> updateItemsProcessStatus(List<int> itemIds, int status) async {
    try {
      // 这个接口可能需要后端支持，暂时作为占位
      final data = {
        'item_ids': itemIds,
        'process_status': status,
      };
      
      await _apiService.put('/recycling/items/batch-update-status', data: data);
      return true;
    } catch (e) {
      LoggerService.error('批量更新明细处理状态失败', e);
      // 如果后端没有这个接口，可以忽略错误
      return false;
    }
  }
}