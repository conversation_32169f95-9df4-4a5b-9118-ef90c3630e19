/// 批量导入结果
class BatchImportResult {
  /// 总数量
  final int totalCount;
  
  /// 成功数量
  final int successCount;
  
  /// 失败数量
  final int failureCount;
  
  /// 成功的数据
  final List<Map<String, dynamic>> successData;
  
  /// 失败的数据和错误信息
  final List<BatchImportError> errors;
  
  /// 导入开始时间
  final DateTime startTime;
  
  /// 导入结束时间
  final DateTime endTime;

  const BatchImportResult({
    required this.totalCount,
    required this.successCount,
    required this.failureCount,
    required this.successData,
    required this.errors,
    required this.startTime,
    required this.endTime,
  });

  /// 是否全部成功
  bool get isAllSuccess => failureCount == 0;

  /// 成功率
  double get successRate => totalCount > 0 ? successCount / totalCount : 0.0;

  /// 耗时（秒）
  int get durationSeconds => endTime.difference(startTime).inSeconds;
}

/// 批量导入错误信息
class BatchImportError {
  /// 行号（从1开始）
  final int rowIndex;
  
  /// 原始数据
  final Map<String, dynamic> rowData;
  
  /// 错误信息
  final String errorMessage;
  
  /// 错误类型
  final BatchImportErrorType errorType;

  const BatchImportError({
    required this.rowIndex,
    required this.rowData,
    required this.errorMessage,
    required this.errorType,
  });
}

/// 批量导入错误类型
enum BatchImportErrorType {
  /// 数据格式错误
  formatError,
  
  /// 必填字段缺失
  missingRequired,
  
  /// 数据验证失败
  validationError,
  
  /// 业务规则错误
  businessError,
  
  /// 系统错误
  systemError,
}

extension BatchImportErrorTypeExtension on BatchImportErrorType {
  String get label {
    switch (this) {
      case BatchImportErrorType.formatError:
        return '格式错误';
      case BatchImportErrorType.missingRequired:
        return '必填字段缺失';
      case BatchImportErrorType.validationError:
        return '数据验证失败';
      case BatchImportErrorType.businessError:
        return '业务规则错误';
      case BatchImportErrorType.systemError:
        return '系统错误';
    }
  }
}

/// 批量导入预览数据
class BatchImportPreviewData {
  /// 表头
  final List<String> headers;

  /// 数据行
  final List<List<String>> rows;

  /// 字段映射（Excel列 -> 系统字段）
  final Map<String, String> fieldMapping;

  /// 验证结果
  final List<BatchImportValidationResult> validationResults;

  /// 重复条码检测结果
  final DuplicateBarcodeResult duplicateBarcodeResult;

  const BatchImportPreviewData({
    required this.headers,
    required this.rows,
    required this.fieldMapping,
    required this.validationResults,
    required this.duplicateBarcodeResult,
  });

  /// 有效数据行数
  int get validRowCount => validationResults.where((r) => r.isValid).length;

  /// 无效数据行数
  int get invalidRowCount => validationResults.where((r) => !r.isValid).length;

  /// 总行数
  int get totalRowCount => rows.length;

  /// 重复条码行数
  int get duplicateRowCount => duplicateBarcodeResult.duplicateRows.length;

  /// 唯一条码行数
  int get uniqueRowCount => totalRowCount - duplicateRowCount;
}

/// 批量导入验证结果
class BatchImportValidationResult {
  /// 行号（从1开始）
  final int rowIndex;
  
  /// 是否有效
  final bool isValid;
  
  /// 错误信息列表
  final List<String> errors;
  
  /// 警告信息列表
  final List<String> warnings;

  const BatchImportValidationResult({
    required this.rowIndex,
    required this.isValid,
    required this.errors,
    required this.warnings,
  });

  /// 是否有警告
  bool get hasWarnings => warnings.isNotEmpty;
}

/// 重复条码检测结果
class DuplicateBarcodeResult {
  /// 重复的条码列表
  final List<String> duplicateBarcodes;

  /// 重复条码对应的行号列表（从1开始）
  final List<int> duplicateRows;

  /// 条码到行号的映射（包含所有出现的行号）
  final Map<String, List<int>> barcodeToRows;

  /// 唯一条码的行号列表（每个条码只保留第一次出现的行）
  final List<int> uniqueRows;

  const DuplicateBarcodeResult({
    required this.duplicateBarcodes,
    required this.duplicateRows,
    required this.barcodeToRows,
    required this.uniqueRows,
  });

  /// 是否有重复条码
  bool get hasDuplicates => duplicateBarcodes.isNotEmpty;

  /// 重复条码数量
  int get duplicateCount => duplicateBarcodes.length;

  /// 检查指定行是否为重复条码行
  bool isRowDuplicate(int rowIndex) => duplicateRows.contains(rowIndex);

  /// 检查指定行是否为唯一条码行（第一次出现）
  bool isRowUnique(int rowIndex) => uniqueRows.contains(rowIndex);
}

/// 支持的文件类型
enum SupportedFileType {
  excel,
  csv,
}

extension SupportedFileTypeExtension on SupportedFileType {
  String get label {
    switch (this) {
      case SupportedFileType.excel:
        return 'Excel文件';
      case SupportedFileType.csv:
        return 'CSV文件';
    }
  }

  List<String> get extensions {
    switch (this) {
      case SupportedFileType.excel:
        return ['xlsx', 'xls'];
      case SupportedFileType.csv:
        return ['csv'];
    }
  }

  String get mimeType {
    switch (this) {
      case SupportedFileType.excel:
        return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      case SupportedFileType.csv:
        return 'text/csv';
    }
  }
}

/// 入库单导入字段映射
class StockInImportFieldMapping {
  static const Map<String, String> defaultMapping = {
    // 商品基本信息
    '商品名称': 'name',
    '名称': 'name',
    '产品名称': 'name',
    '条码': 'barcode',
    '条码号': 'barcode',
    '商品条码': 'barcode',
    '类别': 'category',
    '分类': 'category',
    '商品类别': 'category',
    '圈口': 'ringSize',
    '圈口号': 'ringSize',

    // 重量相关
    '金重': 'goldWeight',
    '金重(g)': 'goldWeight',
    '金重(克)': 'goldWeight',
    '银重': 'silverWeight',
    '银重(g)': 'silverWeight',
    '银重(克)': 'silverWeight',
    '总重': 'totalWeight',
    '总重(g)': 'totalWeight',

    // 价格相关
    '金价': 'goldPrice',
    '金价(元/g)': 'goldPrice',
    '银价': 'silverPrice',
    '银价(元/g)': 'silverPrice',

    // 工费相关
    '工费': 'laborCost',
    '加工费': 'laborCost',
    '工费(元)': 'laborCost',
    '工费方式': 'workType',
    '计费方式': 'workType',
    '工费类型': 'workType',
    '计算方式': 'workType',
    '电铸费': 'platingCost',
    '电铸费(元)': 'platingCost',
    '批发工费': 'wholesaleWorkPrice',
    '零售工费': 'retailWorkPrice',
    '件工费': 'pieceWorkPrice',

    // 其他费用
    '其他费用': 'otherCost',
    '杂费': 'otherCost',
    '其他': 'otherCost',

    // 总金额
    '总金额': 'amount',
    '金额': 'amount',
    '总价': 'amount',
    '价格': 'amount',
    '成本': 'amount',
    '总成本': 'amount',
  };

  static const Map<String, String> requiredFields = {
    'name': '商品名称',
    'category': '类别',
  };

  static const Map<String, String> optionalFields = {
    'barcode': '条码',
    'ringSize': '圈口号',
    'goldWeight': '金重(g)',
    'silverWeight': '银重(g)',
    'goldPrice': '金价(元/g)',
    'silverPrice': '银价(元/g)',
    'laborCost': '工费',
    'workType': '工费方式',
    'platingCost': '电铸费',
    'wholesaleWorkPrice': '批发工费',
    'retailWorkPrice': '零售工费',
    'pieceWorkPrice': '件工费',
    'otherCost': '其他费用',
    'amount': '总金额',
  };

  static const Map<String, String> fieldDescriptions = {
    'name': '商品的名称或型号（必填）',
    'barcode': '商品条码，可为空时自动生成',
    'category': '商品类别，如：戒指、项链、手镯等（必填）',
    'ringSize': '首饰圈口号，如：16、18等',
    'goldWeight': '商品的金重量，单位：克',
    'silverWeight': '商品的银重量，单位：克',
    'goldPrice': '金的单价，单位：元/克',
    'silverPrice': '银的单价，单位：元/克',
    'laborCost': '加工费用，可为0',
    'workType': '工费计算方式，如：按克、按件、固定等',
    'platingCost': '电铸费用，可为0',
    'wholesaleWorkPrice': '批发工费，可为0',
    'retailWorkPrice': '零售工费，可为0',
    'pieceWorkPrice': '件工费，可为0',
    'otherCost': '其他费用，可为0',
    'amount': '商品总金额或成本',
  };

  /// 获取所有字段（必填+可选）
  static Map<String, String> get allFields {
    return {...requiredFields, ...optionalFields};
  }
}
