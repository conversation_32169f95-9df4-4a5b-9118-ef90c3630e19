import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:math' as math;

import '../../../services/auth_service.dart';
import '../../../models/stock/stock_in_item.dart';
import '../../../models/jewelry/jewelry_category.dart';
import '../controllers/stock_in_form_controller.dart';
import '../controllers/stock_tab_controller.dart';
import '../../../core/constants/border_styles.dart';
import '../../../services/batch_import_service.dart';
import '../../../models/stock/batch_import_result.dart';
import '../../../core/utils/logger.dart';
import '../../../widgets/loading_state.dart';

/// 入库单表单页面
class StockInFormView extends StatefulWidget {
  final String? tag; // 控制器标签，用于编辑模式

  const StockInFormView({super.key, this.tag});

  @override
  State<StockInFormView> createState() => _StockInFormViewState();
}

class _StockInFormViewState extends State<StockInFormView> {
  // 横向滚动控制器
  final ScrollController _horizontalScrollController = ScrollController();
  
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _horizontalScrollController.dispose();
    super.dispose();
  }

  // 获取控制器实例
  StockInFormController get controller {
    if (widget.tag != null) {
      // 使用标签获取特定的控制器实例
      return Get.find<StockInFormController>(tag: widget.tag);
    } else {
      // 获取默认控制器
      return Get.find<StockInFormController>();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(() {
        // 不再使用全屏加载状态，改为在表单内部显示加载指示器
        // if (controller.isLoading.value) {
        //   return const LoadingState(text: '加载中...');
        // }
        
        return Form(
          key: controller.formKey,
          child: Column(
            children: [
              _buildFormHeader(),
              Expanded(
                child: _buildItemList(),
              ),
              _buildFormFooter(),
            ],
          ),
        );
      }),
    );
  }

  /// 构建表单头部区域 - 直接复用新建入库单的UI代码
  Widget _buildFormHeader() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: AppBorderStyles.tableBorder,
        ),
      ),
      child: Row(
        children: [
          // 根据模式显示不同的标题
          if (controller.isEditing.value) ...[
            // 编辑模式：显示编辑入库单标题
            Icon(
              Icons.edit,
              color: Colors.blue[600],
              size: 20
            ),
            const SizedBox(width: 8),
            const Text(
              '编辑入库单',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            const SizedBox(width: 24),
          ] else ...[
            // 新建模式：显示新建入库单标题
            Icon(
              Icons.add_box,
              color: Colors.blue[600],
              size: 20
            ),
            const SizedBox(width: 8),
            const Text(
              '新建入库单',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            const SizedBox(width: 24),
          ],

          // 操作员信息标签 - 完全复用新建入库单的样式
          Obx(() {
            final authService = Get.find<AuthService>();
            final operatorName = authService.userNickname.value.isNotEmpty
                ? authService.userNickname.value
                : authService.userName.value;
            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: Colors.blue[200]!, width: 1),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.person, size: 14, color: Colors.blue[600]),
                  const SizedBox(width: 4),
                  Text(
                    '操作员: $operatorName',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.blue[700],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            );
          }),
          const SizedBox(width: 24),

          // 门店标签和选择器 - 完全复用新建入库单的样式
          const Text(
            '门店:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(width: 8),
          SizedBox(
            width: 150,
            child: _buildCompactStoreSelector(),
          ),
          const SizedBox(width: 24),

          // 备注标签和输入框 - 完全复用新建入库单的样式
          const Text(
            '备注:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(width: 8),
          SizedBox(
            width: 200,
            child: _buildCompactRemarkField(),
          ),
        ],
      ),
    );
  }

  /// 构建紧凑型门店选择器（单行布局）
  Widget _buildCompactStoreSelector() {
    return Obx(() {
      // 检查用户权限
      final authService = Get.find<AuthService>();
      final isAdmin = authService.userRole.value == 'admin' || authService.hasPermission('super.admin');
      final currentUserStoreId = authService.storeId.value;
      final currentStoreName = authService.storeName.value;

      if (!isAdmin && currentUserStoreId > 0 && !controller.isEditing.value) {
        // 普通员工新建模式：显示当前用户所属门店，不可选择
        return Container(
          height: 32, // 紧凑高度
          decoration: BoxDecoration(
            border: Border.all(color: AppBorderStyles.borderColor),
            borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
            color: Colors.grey[50],
          ),
          child: Row(
            children: [
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  currentStoreName,
                  style: const TextStyle(
                    fontSize: 13,
                    color: Colors.black87,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const Icon(Icons.lock, size: 14, color: Colors.grey),
              const SizedBox(width: 8),
            ],
          ),
        );
      } else {
        // 管理员或编辑模式：可以选择门店
        if (controller.isLoading.value) {
          return Container(
            height: 32,
            decoration: BoxDecoration(
              border: Border.all(color: AppBorderStyles.borderColor),
              borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
            ),
            child: const Center(
              child: SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            ),
          );
        }

        return Container(
          height: 32,
          decoration: AppBorderStyles.standardBoxDecoration,
          child: DropdownButtonHideUnderline(
            child: DropdownButton<int>(
              value: controller.selectedStoreId.value == 0 ? null : controller.selectedStoreId.value,
              hint: const Padding(
                padding: EdgeInsets.symmetric(horizontal: 8),
                child: Text(
                  '请选择',
                  style: TextStyle(fontSize: 13, color: Colors.grey),
                ),
              ),
              selectedItemBuilder: (BuildContext context) {
                return controller.storeList.map<Widget>((store) {
                  return Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    child: Align(
                      alignment: Alignment.center,
                      child: Text(
                        store.name,
                        style: const TextStyle(fontSize: 13, color: Colors.black87),
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.center,
                      ),
                    ),
                  );
                }).toList();
              },
              isExpanded: true,
              items: controller.storeList.map((store) => DropdownMenuItem<int>(
                value: store.id,
                child: Text(store.name, style: const TextStyle(fontSize: 13), textAlign: TextAlign.center),
              )).toList(),
              onChanged: controller.isEditing.value
                  ? null // 编辑模式时禁用门店选择
                  : (value) {
                      controller.selectedStoreId.value = value ?? 0;
                    },
              style: const TextStyle(fontSize: 13, color: Colors.black87),
              dropdownColor: Colors.white,
              icon: const Padding(
                padding: EdgeInsets.only(right: 8),
                child: Icon(Icons.arrow_drop_down, color: Colors.grey, size: 18),
              ),
            ),
          ),
        );
      }
    });
  }
  
  /// 构建紧凑型备注输入框（单行布局）
  Widget _buildCompactRemarkField() {
    return Container(
      height: 32, // 紧凑高度
      decoration: AppBorderStyles.standardBoxDecoration,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: TextField(
          controller: controller.remarkController,
          decoration: const InputDecoration(
            hintText: '可选',
            border: InputBorder.none,
            enabledBorder: InputBorder.none,
            focusedBorder: InputBorder.none,
            disabledBorder: InputBorder.none,
            errorBorder: InputBorder.none,
            focusedErrorBorder: InputBorder.none,
            contentPadding: EdgeInsets.zero,
            hintStyle: TextStyle(fontSize: 13, color: Colors.grey),
            isDense: true,
          ),
          style: const TextStyle(fontSize: 13),
        ),
      ),
    );
  }

  /// 构建商品列表 - 直接复用新建入库单的实现
  Widget _buildItemList() {
    return Container(
      color: Colors.grey[50],
      child: Column(
        children: [
          // 商品列表区域
          Expanded(
            child: _buildItemListSection(),
          ),
          // 不再在这里添加底部汇总区域，避免重复显示
        ],
      ),
    );
  }

  /// 构建商品列表区域 - 直接复用新建入库单的代码
  Widget _buildItemListSection() {
    return Container(
      color: Colors.white,
      // 确保容器不会限制内部滚动视图的宽度
      width: double.infinity,
      child: Column(
        children: [
          // 商品列表头部
          Container(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                const Text(
                  '商品明细',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const Spacer(),
                // 移除编辑模式下不显示批量导入和添加新行按钮的条件判断
                // 批量导入按钮
                ElevatedButton.icon(
                  icon: const Icon(Icons.upload_file, size: 16),
                  label: const Text('批量导入'),
                  onPressed: () => _showBatchImportDialog(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange[600],
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                // 添加新行按钮
                ElevatedButton.icon(
                  icon: const Icon(Icons.add, size: 16),
                  label: const Text('添加新行'),
                  onPressed: () => _addNewStockInItem(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green[600],
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ],
            ),
          ),
          const Divider(height: 1),
          // 商品列表内容
          Expanded(
            child: Obx(() => controller.itemList.isEmpty
                ? _buildEmptyItemList()
                : _buildStockInItemTable()),
          ),
        ],
      ),
    );
  }

  /// 构建空商品列表 - 直接复用新建入库单的代码
  Widget _buildEmptyItemList() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inventory_2_outlined,
            size: 64,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text(
            '暂无商品',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.grey,
            ),
          ),
          SizedBox(height: 8),
          Text(
            '点击"添加新行"按钮录入商品信息',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建商品表格 - 完整的18列实现
  Widget _buildStockInItemTable() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // 获取可用宽度
        final availableWidth = constraints.maxWidth;

        // 设置每列的固定宽度
        const indexWidth = 60.0;      // 序号
        const barcodeWidth = 120.0;   // 条码
        const nameWidth = 150.0;      // 商品名称
        const categoryWidth = 100.0;   // 分类 - 从90px增加到120px
        const ringSizeWidth = 80.0;   // 圈口号
        const goldWeightWidth = 90.0; // 金重
        const goldPriceWidth = 90.0;  // 金价
        const silverWeightWidth = 90.0; // 银重
        const silverPriceWidth = 90.0; // 银价
        const totalWeightWidth = 90.0; // 总重
        const workTypeWidth = 90.0;   // 工费方式
        const workPriceWidth = 90.0;  // 工费
        const platingCostWidth = 90.0; // 电铸费
        const wholesaleWorkWidth = 90.0; // 批发工费
        const retailWorkWidth = 90.0; // 零售工费
        const pieceWorkWidth = 90.0;  // 件工费
        const totalCostWidth = 100.0;  // 总成本
        const actionWidth = 70.0;     // 操作

        // 根据可用宽度调整字体大小
        final fontSize = availableWidth < 1200 ? 11.0 : 13.0;
        final headingFontSize = availableWidth < 1200 ? 12.0 : 14.0;

        // 计算所有列实际需要的总宽度
        const totalColumnsWidth = indexWidth + barcodeWidth + nameWidth + categoryWidth + 
                                 ringSizeWidth + goldWeightWidth + goldPriceWidth + 
                                 silverWeightWidth + silverPriceWidth + totalWeightWidth + 
                                 workTypeWidth + workPriceWidth + platingCostWidth + 
                                 wholesaleWorkWidth + retailWorkWidth + pieceWorkWidth + 
                                 totalCostWidth + actionWidth;

        return Container(
          width: double.infinity,
          height: double.infinity,
          margin: const EdgeInsets.all(4), // 从8减小到4，减少边距
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8), // 从12减小到8，减少圆角
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.03), // 降低阴影不透明度
                blurRadius: 3, // 从4减小到3，减少阴影
                offset: const Offset(0, 1), // 从(0,2)减小到(0,1)，减少阴影偏移
              ),
            ],
          ),
          child: Column(
            children: [
              // 表格占用整个可用空间
              Expanded(
                child: Stack(
                  children: [
                    // 横向滚动条
                    Scrollbar(
                      controller: _horizontalScrollController,
                      thumbVisibility: true,
                      trackVisibility: true,
                      thickness: 6.0, // 从8减小到6，使滚动条更加细致
                      radius: const Radius.circular(4.0), // 减小圆角
                      child: SingleChildScrollView(
                        controller: _horizontalScrollController,
                        scrollDirection: Axis.horizontal,
                        child: SizedBox(
                          width: totalColumnsWidth,
                          // 通过固定高度强制显示垂直滚动条
                          height: constraints.maxHeight - 10, // 从50减小到10，减少底部空白区域
                          child: Stack(
                            children: [
                              // 垂直滚动视图
                              SingleChildScrollView(
                                scrollDirection: Axis.vertical,
                                child: DataTable(
                                  columnSpacing: 0,
                                  horizontalMargin: 0,
                                  headingRowHeight: 48,
                                  dataRowMinHeight: 52,
                                  dataRowMaxHeight: 52,
                                  headingTextStyle: TextStyle(
                                    fontWeight: FontWeight.w600,
                                    color: Colors.black87,
                                    fontSize: headingFontSize,
                                  ),
                                  dataTextStyle: TextStyle(
                                    fontSize: fontSize,
                                    color: Colors.black87,
                                  ),
                                  headingRowColor: WidgetStateProperty.all(AppBorderStyles.tableHeaderBackground),
                                  border: AppBorderStyles.tableStandardBorder,
                                  columns: [
                                    DataColumn(
                                      label: Container(
                                        width: indexWidth,
                                        padding: const EdgeInsets.symmetric(horizontal: 4),
                                        child: const Text('序号', textAlign: TextAlign.center),
                                      ),
                                    ),
                                    DataColumn(
                                      label: Container(
                                        width: barcodeWidth,
                                        padding: const EdgeInsets.symmetric(horizontal: 4),
                                        child: const Text('条码', textAlign: TextAlign.center),
                                      ),
                                    ),
                                    DataColumn(
                                      label: Container(
                                        width: nameWidth,
                                        padding: const EdgeInsets.symmetric(horizontal: 4),
                                        child: const Text('商品名称', textAlign: TextAlign.center),
                                      ),
                                    ),
                                    DataColumn(
                                      label: Container(
                                        width: categoryWidth,
                                        padding: const EdgeInsets.symmetric(horizontal: 4),
                                        child: const Text('分类', textAlign: TextAlign.center),
                                      ),
                                    ),
                                    DataColumn(
                                      label: Container(
                                        width: ringSizeWidth,
                                        padding: const EdgeInsets.symmetric(horizontal: 4),
                                        child: const Text('圈口号', textAlign: TextAlign.center),
                                      ),
                                    ),
                                    DataColumn(
                                      label: Container(
                                        width: goldWeightWidth,
                                        padding: const EdgeInsets.symmetric(horizontal: 4),
                                        child: const Text('金重(g)', textAlign: TextAlign.center),
                                      ),
                                    ),
                                    DataColumn(
                                      label: Container(
                                        width: goldPriceWidth,
                                        padding: const EdgeInsets.symmetric(horizontal: 4),
                                        child: const Text('金价(元/g)', textAlign: TextAlign.center),
                                      ),
                                    ),
                                    DataColumn(
                                      label: Container(
                                        width: silverWeightWidth,
                                        padding: const EdgeInsets.symmetric(horizontal: 4),
                                        child: const Text('银重(g)', textAlign: TextAlign.center),
                                      ),
                                    ),
                                    DataColumn(
                                      label: Container(
                                        width: silverPriceWidth,
                                        padding: const EdgeInsets.symmetric(horizontal: 4),
                                        child: const Text('银价(元/g)', textAlign: TextAlign.center),
                                      ),
                                    ),
                                    DataColumn(
                                      label: Container(
                                        width: totalWeightWidth,
                                        padding: const EdgeInsets.symmetric(horizontal: 4),
                                        child: const Text('总重(g)', textAlign: TextAlign.center),
                                      ),
                                    ),
                                    DataColumn(
                                      label: Container(
                                        width: workTypeWidth,
                                        padding: const EdgeInsets.symmetric(horizontal: 4),
                                        child: const Text('工费方式', textAlign: TextAlign.center),
                                      ),
                                    ),
                                    DataColumn(
                                      label: Container(
                                        width: workPriceWidth,
                                        padding: const EdgeInsets.symmetric(horizontal: 4),
                                        child: const Text('工费(元)', textAlign: TextAlign.center),
                                      ),
                                    ),
                                    DataColumn(
                                      label: Container(
                                        width: platingCostWidth,
                                        padding: const EdgeInsets.symmetric(horizontal: 4),
                                        child: const Text('电铸费(元)', textAlign: TextAlign.center),
                                      ),
                                    ),
                                    DataColumn(
                                      label: Container(
                                        width: wholesaleWorkWidth,
                                        padding: const EdgeInsets.symmetric(horizontal: 4),
                                        child: const Text('批发工费(元)', textAlign: TextAlign.center),
                                      ),
                                    ),
                                    DataColumn(
                                      label: Container(
                                        width: retailWorkWidth,
                                        padding: const EdgeInsets.symmetric(horizontal: 4),
                                        child: const Text('零售工费(元)', textAlign: TextAlign.center),
                                      ),
                                    ),
                                    DataColumn(
                                      label: Container(
                                        width: pieceWorkWidth,
                                        padding: const EdgeInsets.symmetric(horizontal: 4),
                                        child: const Text('件工费(元)', textAlign: TextAlign.center),
                                      ),
                                    ),
                                    DataColumn(
                                      label: Container(
                                        width: totalCostWidth,
                                        padding: const EdgeInsets.symmetric(horizontal: 4),
                                        child: const Text('总成本(元)', textAlign: TextAlign.center),
                                      ),
                                    ),
                                    DataColumn(
                                      label: Container(
                                        width: actionWidth,
                                        padding: const EdgeInsets.symmetric(horizontal: 4),
                                        child: const Text('操作', textAlign: TextAlign.center),
                                      ),
                                    ),
                                  ],
                                  rows: controller.itemList.asMap().entries.map((entry) {
                                    final index = entry.key;
                                    final item = entry.value;

                                    return DataRow(
                                      color: WidgetStateProperty.all(Colors.white),
                                      cells: [
                                        // 1. 序号
                                        DataCell(
                                          Container(
                                            width: indexWidth,
                                            height: 52,
                                            padding: const EdgeInsets.symmetric(horizontal: 4),
                                            alignment: Alignment.center,
                                            child: Text(
                                              '${index + 1}',
                                              style: const TextStyle(fontWeight: FontWeight.w500),
                                              overflow: TextOverflow.ellipsis,
                                              maxLines: 1,
                                            ),
                                          ),
                                        ),
                                        // 2. 条码 - 可编辑
                                        DataCell(
                                          _buildEditableCell(
                                            width: barcodeWidth,
                                            value: item.barcode,
                                            onChanged: (value) {
                                              controller.updateItemField(index, barcode: value);
                                            },
                                          ),
                                        ),
                                        // 3. 商品名称 - 可编辑
                                        DataCell(
                                          _buildEditableCell(
                                            width: nameWidth,
                                            value: item.name,
                                            onChanged: (value) {
                                              controller.updateItemField(index, name: value);
                                            },
                                          ),
                                        ),
                                        // 4. 分类 - 下拉框
                                        DataCell(
                                          Container(
                                            width: categoryWidth,
                                            height: 52,
                                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                                            alignment: Alignment.center,
                                            child: Obx(() => DropdownButtonFormField<int?>(
                                              value: item.categoryId == 0 ? null : item.categoryId,
                                              style: const TextStyle(
                                                fontSize: 13,
                                                color: Colors.black87,
                                              ),
                                              decoration: InputDecoration(
                                                contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
                                                border: OutlineInputBorder(
                                                  borderRadius: BorderRadius.circular(6),
                                                  borderSide: BorderSide(color: Colors.grey[300]!, width: 1),
                                                ),
                                                enabledBorder: OutlineInputBorder(
                                                  borderRadius: BorderRadius.circular(6),
                                                  borderSide: BorderSide(color: Colors.grey[300]!, width: 1),
                                                ),
                                                focusedBorder: OutlineInputBorder(
                                                  borderRadius: BorderRadius.circular(6),
                                                  borderSide: const BorderSide(color: Color(0xFF1E88E5), width: 2),
                                                ),
                                                isDense: true,
                                              ),
                                              dropdownColor: Colors.white,
                                              icon: const Icon(Icons.arrow_drop_down, size: 20),
                                              isExpanded: true,
                                              items: [
                                                const DropdownMenuItem<int?>(
                                                  value: null,
                                                  child: Center(child: Text('请选择', style: TextStyle(fontSize: 13, color: Colors.black87))),
                                                ),
                                                ...controller.categoryList.map((category) {
                                                  return DropdownMenuItem<int?>(
                                                    value: category.id,
                                                    child: Center(
                                                      child: Text(
                                                        category.name,
                                                        style: const TextStyle(fontSize: 13, color: Colors.black87),
                                                        overflow: TextOverflow.ellipsis,
                                                        textAlign: TextAlign.center,
                                                      ),
                                                    ),
                                                  );
                                                }),
                                              ],
                                              onChanged: (value) {
                                                controller.updateItemField(index, categoryId: value ?? 0);
                                              },
                                            )),
                                          ),
                                        ),
                                        // 5. 圈口号 - 可编辑
                                        DataCell(
                                          _buildEditableCell(
                                            width: ringSizeWidth,
                                            value: item.ringSize ?? '',
                                            onChanged: (value) {
                                              controller.updateItemField(index, ringSize: value.isEmpty ? null : value);
                                            },
                                          ),
                                        ),
                                        // 6. 金重(g) - 可编辑
                                        DataCell(
                                          _buildEditableCell(
                                            width: goldWeightWidth,
                                            value: item.goldWeight?.toStringAsFixed(2) ?? '0.00',
                                            keyboardType: TextInputType.number,
                                            onChanged: (value) {
                                              final weight = double.tryParse(value);
                                              controller.updateItemField(index, goldWeight: weight);
                                            },
                                          ),
                                        ),
                                        // 7. 金价(元/g) - 可编辑
                                        DataCell(
                                          _buildEditableCell(
                                            width: goldPriceWidth,
                                            value: item.goldPrice?.toStringAsFixed(2) ?? '0.00',
                                            keyboardType: TextInputType.number,
                                            onChanged: (value) {
                                              final price = double.tryParse(value);
                                              controller.updateItemField(index, goldPrice: price);
                                            },
                                          ),
                                        ),
                                        // 8. 银重(g) - 可编辑
                                        DataCell(
                                          _buildEditableCell(
                                            width: silverWeightWidth,
                                            value: item.silverWeight?.toStringAsFixed(2) ?? '0.00',
                                            keyboardType: TextInputType.number,
                                            onChanged: (value) {
                                              final weight = double.tryParse(value);
                                              controller.updateItemField(index, silverWeight: weight);
                                            },
                                          ),
                                        ),
                                        // 9. 银价(元/g) - 可编辑
                                        DataCell(
                                          _buildEditableCell(
                                            width: silverPriceWidth,
                                            value: item.silverPrice?.toStringAsFixed(2) ?? '0.00',
                                            keyboardType: TextInputType.number,
                                            onChanged: (value) {
                                              final price = double.tryParse(value);
                                              controller.updateItemField(index, silverPrice: price);
                                            },
                                          ),
                                        ),
                                        // 10. 总重(g) - 不可编辑（自动计算）
                                        DataCell(
                                          Container(
                                            width: totalWeightWidth,
                                            height: 52,
                                            padding: const EdgeInsets.symmetric(horizontal: 4),
                                            alignment: Alignment.center,
                                            child: Text(
                                              item.totalWeight?.toStringAsFixed(2) ?? '0.00',
                                              style: const TextStyle(fontSize: 12),
                                              textAlign: TextAlign.center,
                                            ),
                                          ),
                                        ),
                                        // 11. 工费方式 - 可编辑下拉框
                                        DataCell(
                                          _buildWorkTypeDropdownCell(
                                            width: workTypeWidth,
                                            value: item.silverWorkType ?? 0,
                                            onChanged: (value) {
                                              controller.updateItemField(index, silverWorkType: value);
                                            },
                                          ),
                                        ),
                                        // 12. 工费(元) - 可编辑
                                        DataCell(
                                          _buildEditableCell(
                                            width: workPriceWidth,
                                            value: item.silverWorkPrice?.toStringAsFixed(2) ?? '0.00',
                                            keyboardType: TextInputType.number,
                                            onChanged: (value) {
                                              final price = double.tryParse(value);
                                              controller.updateItemField(index, silverWorkPrice: price);
                                            },
                                          ),
                                        ),
                                        // 13. 电铸费(元) - 可编辑
                                        DataCell(
                                          _buildEditableCell(
                                            width: platingCostWidth,
                                            value: item.platingCost?.toStringAsFixed(2) ?? '0.00',
                                            keyboardType: TextInputType.number,
                                            onChanged: (value) {
                                              final cost = double.tryParse(value);
                                              controller.updateItemField(index, platingCost: cost);
                                            },
                                          ),
                                        ),
                                        // 14. 批发工费(元) - 可编辑
                                        DataCell(
                                          _buildEditableCell(
                                            width: wholesaleWorkWidth,
                                            value: item.wholesaleWorkPrice?.toStringAsFixed(2) ?? '0.00',
                                            keyboardType: TextInputType.number,
                                            onChanged: (value) {
                                              final price = double.tryParse(value);
                                              controller.updateItemField(index, wholesaleWorkPrice: price);
                                            },
                                          ),
                                        ),
                                        // 15. 零售工费(元) - 可编辑
                                        DataCell(
                                          _buildEditableCell(
                                            width: retailWorkWidth,
                                            value: item.retailWorkPrice?.toStringAsFixed(2) ?? '0.00',
                                            keyboardType: TextInputType.number,
                                            onChanged: (value) {
                                              final price = double.tryParse(value);
                                              controller.updateItemField(index, retailWorkPrice: price);
                                            },
                                          ),
                                        ),
                                        // 16. 件工费(元) - 可编辑
                                        DataCell(
                                          _buildEditableCell(
                                            width: pieceWorkWidth,
                                            value: item.pieceWorkPrice?.toStringAsFixed(2) ?? '0.00',
                                            keyboardType: TextInputType.number,
                                            onChanged: (value) {
                                              final price = double.tryParse(value);
                                              controller.updateItemField(index, pieceWorkPrice: price);
                                            },
                                          ),
                                        ),
                                        // 17. 总成本(元)
                                        DataCell(
                                          Container(
                                            width: totalCostWidth,
                                            height: 52,
                                            padding: const EdgeInsets.symmetric(horizontal: 4),
                                            alignment: Alignment.center,
                                            child: Text(
                                              '¥${item.totalCost?.toStringAsFixed(2) ?? '0.00'}',
                                              style: TextStyle(
                                                fontWeight: FontWeight.w600,
                                                color: Colors.green[700],
                                              ),
                                              textAlign: TextAlign.center,
                                              overflow: TextOverflow.ellipsis,
                                              maxLines: 1,
                                            ),
                                          ),
                                        ),
                                        // 18. 操作
                                        DataCell(
                                          Container(
                                            width: actionWidth,
                                            height: 52,
                                            padding: const EdgeInsets.symmetric(horizontal: 4),
                                            alignment: Alignment.center,
                                            child: IconButton(
                                              icon: const Icon(Icons.delete, color: Colors.red, size: 16),
                                              tooltip: '移除',
                                              onPressed: () => controller.removeItem(index),
                                            ),
                                          ),
                                        ),
                                      ],
                                    );
                                  }).toList(),
                                ),
                              ),
                              // 添加垂直滚动条
                              Positioned(
                                right: 0,
                                top: 0,
                                bottom: 0,
                                child: Container(
                                  width: 6, // 从8减小到6，更加细致
                                  decoration: BoxDecoration(
                                    color: Colors.grey.withValues(alpha: 0.2), // 降低不透明度，使其更轻量
                                    borderRadius: BorderRadius.circular(3), // 对应减小的宽度
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 构建表单汇总区域 - 直接复用新建入库单的代码
  Widget _buildFormFooter() {
    return _buildFormSummarySection();
  }

  /// 构建表单汇总区域 - 直接复用新建入库单的代码
  Widget _buildFormSummarySection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0), // 减少垂直内边距
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(color: Colors.grey[200]!, width: 1),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03), // 降低阴影不透明度
            blurRadius: 3, // 减小阴影模糊半径
            offset: const Offset(0, -1), // 减小阴影偏移
          ),
        ],
      ),
      child: Obx(() => Row(
        children: [
          // 左侧汇总信息
          const Text(
            '商品件数: ',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          Text(
            '${controller.itemList.length}',
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.blue,
            ),
          ),
          const SizedBox(width: 24),
          const Text(
            '总金额: ',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          Text(
            '¥${controller.totalAmount.value.toStringAsFixed(2)}',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.green,
            ),
          ),
          // 弹性空间，将按钮推到右侧
          const Spacer(),
          // 右侧按钮组
          OutlinedButton.icon(
            icon: const Icon(Icons.close, size: 16),
            label: const Text('取消'),
            onPressed: () => _handleCancel(),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
          // 编辑模式下不显示保存草稿按钮
          if (!controller.isEditing.value) ...[
            const SizedBox(width: 16),
            ElevatedButton(
              onPressed: controller.saveDraftManually,
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
              child: const Text('保存草稿'),
            ),
            const SizedBox(width: 16),
          ] else ...[
            const SizedBox(width: 16),
          ],
          ElevatedButton(
            onPressed: controller.isEditing.value
                ? controller.saveStockIn  // 编辑模式：直接保存更新
                : controller.submitForApproval,  // 新建模式：提交审核
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue[600],
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
            child: Text(controller.isEditing.value ? '更新' : '提交审核'),
          ),
        ],
      )),
    );
  }

  /// 构建可编辑的输入框单元格
  Widget _buildEditableCell({
    required double width,
    required String value,
    required Function(String) onChanged,
    TextAlign textAlign = TextAlign.center,
    TextInputType keyboardType = TextInputType.text,
  }) {
    return Container(
      width: width,
      height: 52,
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      alignment: Alignment.center,
      child: TextFormField(
        initialValue: value,
        textAlign: textAlign,
        keyboardType: keyboardType,
        style: TextStyle(
          fontSize: 13, // 按照UI规范：表单标签字体大小
          color: Colors.black87,
          fontFamily: keyboardType == TextInputType.number ? 'monospace' : null, // 数字字段使用等宽字体
        ),
        decoration: InputDecoration(
          contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6), // 按照UI规范：圆角半径
            borderSide: BorderSide(color: Colors.grey[300]!, width: 1),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: BorderSide(color: Colors.grey[300]!, width: 1),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: const BorderSide(color: Color(0xFF1E88E5), width: 2), // 按照UI规范：主色调蓝色
          ),
          isDense: true,
        ),
        onChanged: onChanged,
        onFieldSubmitted: onChanged,
      ),
    );
  }

  /// 构建工费方式下拉框单元格
  Widget _buildWorkTypeDropdownCell({
    required double width,
    required int value,
    required Function(int) onChanged,
  }) {
    return Container(
      width: width,
      height: 52,
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      alignment: Alignment.center,
      child: DropdownButtonFormField<int>(
        value: value,
        style: const TextStyle(
          fontSize: 13, // 按照UI规范：表单标签字体大小
          color: Colors.black87,
        ),
        decoration: InputDecoration(
          contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6), // 按照UI规范：圆角半径
            borderSide: BorderSide(color: Colors.grey[300]!, width: 1),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: BorderSide(color: Colors.grey[300]!, width: 1),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(6),
            borderSide: const BorderSide(color: Color(0xFF1E88E5), width: 2), // 按照UI规范：主色调蓝色
          ),
          isDense: true,
        ),
        dropdownColor: Colors.white, // 按照UI规范：下拉菜单背景色
        icon: const Icon(Icons.arrow_drop_down, size: 20), // 按照UI规范：下拉图标
        items: const [
          DropdownMenuItem(
            value: 0,
            child: Center(child: Text('按克', style: TextStyle(fontSize: 13, color: Colors.black87))),
          ),
          DropdownMenuItem(
            value: 1,
            child: Center(child: Text('按件', style: TextStyle(fontSize: 13, color: Colors.black87))),
          ),
        ],
        onChanged: (newValue) {
          if (newValue != null) {
            onChanged(newValue);
          }
        },
      ),
    );
  }

  /// 处理取消按钮点击事件
  void _handleCancel() {
    if (controller.isEditing.value) {
      // 编辑模式：关闭编辑标签页
      _closeEditTab();
    } else {
      // 新建模式：返回上一页
      Get.back();
    }
  }

  /// 关闭编辑标签页
  void _closeEditTab() {
    try {
      // 获取库存标签页控制器
      final stockTabController = Get.find<StockTabController>();
      final tabManager = stockTabController.tabManager;

      // 查找并关闭编辑标签页
      String? tabIdToClose;
      
      // 先查看是否有tag参数
      if (widget.tag != null && widget.tag!.startsWith('stock_in_edit_')) {
        tabIdToClose = widget.tag;
      } else {
        // 否则查找所有以stock_in_edit_开头的标签页
        final editTabIndex = tabManager.tabs.indexWhere(
          (tab) => tab.id.startsWith('stock_in_edit_'),
        );
        if (editTabIndex != -1) {
          tabIdToClose = tabManager.tabs[editTabIndex].id;
        }
      }
      
      if (tabIdToClose != null) {
        final tabIndex = tabManager.tabs.indexWhere((tab) => tab.id == tabIdToClose);
        if (tabIndex != -1) {
          tabManager.closeTab(tabIndex);
          return;
        }
      }
      
      // 如果找不到标签页，直接返回
      Get.back();
    } catch (e) {
      // 如果找不到控制器，直接返回
      Get.back();
    }
  }

  /// 显示添加商品对话框
  void _showAddItemDialog() {
    final TextEditingController searchController = TextEditingController();

    Get.dialog(
      AlertDialog(
        title: const Text('添加商品'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: searchController,
                    decoration: InputDecoration(
                      labelText: '搜索商品',
                      hintText: '输入商品名称或条码',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      prefixIcon: const Icon(Icons.search),
                    ),
                    onSubmitted: (value) {
                      if (value.isNotEmpty) {
                        controller.searchJewelry(value);
                      }
                    },
                  ),
                ),
                const SizedBox(width: 8),
                IconButton(
                  icon: const Icon(Icons.qr_code_scanner),
                  tooltip: '扫描条码',
                  onPressed: controller.scanBarcode,
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Text(
              '商品搜索功能正在开发中...',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          // 暂时禁用确定按钮，等商品搜索功能完成后启用
          const ElevatedButton(
            onPressed: null,
            child: Text('确定'),
          ),
        ],
      ),
    );
  }

  /// 显示批量导入对话框
  Future<void> _showBatchImportDialog() async {
    try {
      // 使用BatchImportService直接实现批量导入功能
      final batchImportService = Get.find<BatchImportService>();
      
      // 选择文件
      final file = await batchImportService.pickFile();
      if (file == null) {
        return;
      }

      // 显示加载对话框
      Get.dialog(
        const LoadingState(text: '加载中...', timeoutSeconds: 30),
        barrierDismissible: false,
      );

      // 解析文件
      BatchImportPreviewData? previewData;
      final fileName = file.path.toLowerCase();

      if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls')) {
        previewData = await batchImportService.parseExcelFile(file);
      } else if (fileName.endsWith('.csv')) {
        previewData = await batchImportService.parseCsvFile(file);
      }

      // 关闭加载对话框
      Get.back();

      if (previewData == null) {
        Get.snackbar(
          '错误',
          '文件解析失败，请检查文件格式',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return;
      }

      // 显示预览对话框
      _showImportPreviewDialog(previewData, batchImportService);

    } catch (e) {
      // 关闭可能存在的加载对话框
      if (Get.isDialogOpen == true) {
        Get.back();
      }

      LoggerService.e('批量导入失败', e);
      Get.snackbar(
        '错误',
        '批量导入失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
  
  /// 显示导入预览对话框
  void _showImportPreviewDialog(BatchImportPreviewData previewData, BatchImportService batchImportService) {
    // 计算表格实际需要的宽度
    final tableWidth = _calculateTableWidth(previewData.headers);
    final dialogWidth = (tableWidth + 60).clamp(600.0, Get.width * 0.9); // 最小600px，最大90%屏幕宽度

    Get.dialog(
      AlertDialog(
        title: const Text('导入预览', style: TextStyle(fontSize: 15, fontWeight: FontWeight.w600)),
        titlePadding: const EdgeInsets.fromLTRB(16, 12, 16, 4), // 进一步减少内边距
        contentPadding: const EdgeInsets.fromLTRB(16, 4, 16, 4), // 极小内边距
        actionsPadding: const EdgeInsets.fromLTRB(16, 4, 16, 12), // 极小内边距
        insetPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 40), // 减少对话框外边距
        content: SizedBox(
          width: dialogWidth, // 根据内容动态调整宽度
          height: Get.height * 0.75, // 增加高度利用率
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min, // 使用最小尺寸
            children: [
              // 统计信息（极致紧凑布局）
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6), // 进一步减少内边距
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(4), // 更小圆角
                ),
                child: IntrinsicHeight(
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          '总行数: ${previewData.totalRowCount}',
                          style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      VerticalDivider(width: 1, color: Colors.grey[300]),
                      Expanded(
                        child: Text(
                          '有效: ${previewData.validRowCount}',
                          style: TextStyle(fontSize: 12, fontWeight: FontWeight.w500, color: Colors.green[600]),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      VerticalDivider(width: 1, color: Colors.grey[300]),
                      Expanded(
                        child: Text(
                          '无效: ${previewData.invalidRowCount}',
                          style: TextStyle(fontSize: 12, fontWeight: FontWeight.w500, color: Colors.red[600]),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      if (previewData.duplicateRowCount > 0) ...[
                        VerticalDivider(width: 1, color: Colors.grey[300]),
                        Expanded(
                          child: Text(
                            '重复: ${previewData.duplicateRowCount}',
                            style: TextStyle(fontSize: 12, fontWeight: FontWeight.w500, color: Colors.orange[600]),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 8), // 进一步减少间距
              // 错误详情（如果有无效数据，极致紧凑布局）
              if (previewData.invalidRowCount > 0) ...[
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6), // 进一步减少内边距
                  decoration: BoxDecoration(
                    color: Colors.red[50],
                    borderRadius: BorderRadius.circular(4), // 更小圆角
                    border: Border.all(color: Colors.red[200]!, width: 0.5), // 更细边框
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.error_outline, color: Colors.red[600], size: 14), // 更小图标
                          const SizedBox(width: 4),
                          Text(
                            '错误详情',
                            style: TextStyle(
                              fontSize: 11, // 更小字体
                              fontWeight: FontWeight.w600,
                              color: Colors.red[600],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4), // 更小间距
                      ConstrainedBox(
                        constraints: const BoxConstraints(maxHeight: 60), // 进一步减少高度
                        child: SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: previewData.validationResults
                                .where((result) => !result.isValid)
                                .take(5) // 只显示前5个错误，避免占用过多空间
                                .map((result) => Padding(
                                      padding: const EdgeInsets.only(bottom: 1), // 极小行间距
                                      child: Text(
                                        '第${result.rowIndex}行: ${result.errors.join(', ')}',
                                        style: TextStyle(
                                          fontSize: 10, // 更小字体
                                          color: Colors.red[700],
                                        ),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ))
                                .toList(),
                          ),
                        ),
                      ),
                      if (previewData.validationResults.where((result) => !result.isValid).length > 5)
                        Text(
                          '... 还有${previewData.validationResults.where((result) => !result.isValid).length - 5}个错误',
                          style: TextStyle(fontSize: 9, color: Colors.red[500]),
                        ),
                    ],
                  ),
                ),
                const SizedBox(height: 6), // 进一步减少间距
              ],
              // 重复条码详情（如果有重复条码）
              if (previewData.duplicateRowCount > 0) ...[
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.orange[50],
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(color: Colors.orange[200]!, width: 0.5),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.warning_outlined, color: Colors.orange[600], size: 14),
                          const SizedBox(width: 4),
                          Text(
                            '重复条码详情',
                            style: TextStyle(
                              fontSize: 11,
                              fontWeight: FontWeight.w600,
                              color: Colors.orange[600],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      ConstrainedBox(
                        constraints: const BoxConstraints(maxHeight: 60),
                        child: SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: previewData.duplicateBarcodeResult.duplicateBarcodes
                                .take(5) // 只显示前5个重复条码
                                .map((barcode) {
                                  final rows = previewData.duplicateBarcodeResult.barcodeToRows[barcode] ?? [];
                                  return Padding(
                                    padding: const EdgeInsets.only(bottom: 1),
                                    child: Text(
                                      '条码 "$barcode" 在第${rows.join(', ')}行重复',
                                      style: TextStyle(
                                        fontSize: 10,
                                        color: Colors.orange[700],
                                      ),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  );
                                })
                                .toList(),
                          ),
                        ),
                      ),
                      if (previewData.duplicateBarcodeResult.duplicateBarcodes.length > 5)
                        Text(
                          '... 还有${previewData.duplicateBarcodeResult.duplicateBarcodes.length - 5}个重复条码',
                          style: TextStyle(fontSize: 9, color: Colors.orange[500]),
                        ),
                      const SizedBox(height: 2),
                      Text(
                        '注：导入时将自动跳过重复条码，只保留每个条码的第一次出现',
                        style: TextStyle(fontSize: 9, color: Colors.orange[600], fontStyle: FontStyle.italic),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 6),
              ],
              // 数据预览表格（极致紧凑布局，消除空白）
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      '数据预览（前20行）',
                      style: TextStyle(
                        fontSize: 11, // 更小字体
                        fontWeight: FontWeight.w600,
                        color: Colors.grey[700],
                      ),
                    ),
                    const SizedBox(height: 4), // 更小间距
                    Expanded(
                      child: Container(
                        width: double.infinity, // 确保容器占满宽度
                        decoration: BoxDecoration(
                          border: Border.all(color: AppBorderStyles.borderColor, width: 0.5),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(4),
                          child: SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: ConstrainedBox(
                              constraints: BoxConstraints(
                                minWidth: dialogWidth - 32, // 确保表格至少占满对话框宽度
                              ),
                              child: SingleChildScrollView(
                                child: DataTable(
                                  columnSpacing: 6, // 进一步减少列间距
                                  horizontalMargin: 4, // 进一步减少水平边距
                                  headingRowHeight: 28, // 进一步减少表头行高
                                  dataRowMinHeight: 24, // 进一步减少数据行高
                                  dataRowMaxHeight: 24,
                                  headingRowColor: WidgetStateProperty.all(AppBorderStyles.tableHeaderBackground),
                                  border: TableBorder.all(color: AppBorderStyles.borderColor, width: 0.5), // 使用统一边框样式
                                  columns: previewData.headers
                                      .map((header) => DataColumn(
                                            label: Text(
                                              header,
                                              style: const TextStyle(
                                                fontSize: 10, // 更小字体
                                                fontWeight: FontWeight.w600,
                                              ),
                                            ),
                                          ))
                                      .toList(),
                                  rows: previewData.rows
                                      .take(20) // 增加显示行数到20行
                                      .toList()
                                      .asMap()
                                      .entries
                                      .map((entry) {
                                        final index = entry.key;
                                        final row = entry.value;
                                        final validation = previewData.validationResults[index];

                                        return DataRow(
                                          color: WidgetStateProperty.all(
                                            // 检查是否为重复条码行
                                            previewData.duplicateBarcodeResult.isRowDuplicate(index + 1)
                                                ? Colors.orange[100] // 重复条码行使用橙色背景
                                                : validation.isValid
                                                    ? (index.isEven ? AppBorderStyles.tableEvenRowBackground : AppBorderStyles.tableOddRowBackground) // 使用统一表格行背景色
                                                    : Colors.red[50], // 无效数据行使用红色背景
                                          ),
                                          cells: row
                                              .map((cell) => DataCell(
                                                    Text(
                                                      cell,
                                                      style: TextStyle(
                                                        fontSize: 9, // 更小字体
                                                        color: validation.isValid
                                                            ? Colors.black87
                                                            : Colors.red[700],
                                                      ),
                                                    ),
                                                  ))
                                              .toList(),
                                        );
                                      })
                                      .toList(),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    if (previewData.rows.length > 20) ...[
                      Padding(
                        padding: const EdgeInsets.only(top: 2),
                        child: Text(
                          '注：仅显示前20行数据，共${previewData.rows.length}行',
                          style: const TextStyle(fontSize: 9, color: Colors.grey),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: [
          // 紧凑的按钮布局
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextButton(
                onPressed: () => Get.back(),
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8), // 减少按钮内边距
                ),
                child: const Text('取消', style: TextStyle(fontSize: 13)),
              ),
              const SizedBox(width: 8),
              ElevatedButton(
                onPressed: previewData.uniqueRowCount > 0
                    ? () => _executeImport(previewData, batchImportService)
                    : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green[600],
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8), // 减少按钮内边距
                ),
                child: Text(
                  '确认导入 (${previewData.uniqueRowCount}条)',
                  style: const TextStyle(fontSize: 13),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
  
  /// 计算表格宽度
  double _calculateTableWidth(List<String> headers) {
    // 根据表头数量和内容估算宽度
    double totalWidth = 0;

    for (String header in headers) {
      // 根据表头内容估算列宽
      switch (header) {
        case '条码':
          totalWidth += 120;
          break;
        case '商品名称':
          totalWidth += 140;
          break;
        case '分类':
        case '类别':
          totalWidth += 80;
          break;
        case '圈口号':
          totalWidth += 70;
          break;
        case '金重(g)':
        case '银重(g)':
        case '总重(g)':
          totalWidth += 80;
          break;
        case '金价(元/g)':
        case '银价(元/g)':
          totalWidth += 90;
          break;
        case '工费(元)':
        case '工费':
          totalWidth += 80;
          break;
        case '工费方式':
          totalWidth += 80;
          break;
        case '电铸费(元)':
        case '批发工费(元)':
        case '零售工费(元)':
        case '件工费(元)':
          totalWidth += 90;
          break;
        default:
          // 默认列宽
          totalWidth += math.max(60, header.length * 8 + 20);
      }
    }

    // 添加列间距和边距
    totalWidth += (headers.length - 1) * 8; // 列间距
    totalWidth += 16; // 水平边距

    return totalWidth;
  }
  
  /// 执行导入
  Future<void> _executeImport(BatchImportPreviewData previewData, BatchImportService batchImportService) async {
    Get.back(); // 关闭预览对话框

    // 显示进度对话框
    Get.dialog(
      const LoadingState(text: '加载中...', timeoutSeconds: 30),
      barrierDismissible: false,
    );

    try {
      // 执行导入
      final result = await batchImportService.executeImport(
        previewData,
        (importData) async {
          // 将导入的数据转换为StockInItem并添加到当前表单
          for (final data in importData) {
            // 调试日志：打印导入的原始数据
            LoggerService.d('导入数据转换', '原始数据: $data');

            // 根据分类名称查找分类ID
            final categoryName = data['category']?.toString() ?? '默认分类';
            final category = controller.categoryList.firstWhere(
              (cat) => cat.name == categoryName,
              orElse: () => controller.categoryList.isNotEmpty ? controller.categoryList.first : const JewelryCategory(id: 1, name: '默认分类', parentId: 0),
            );

            // 创建新的商品项
            final item = StockInItem(
              id: 0,
              stockInId: controller.currentStockIn.value?.id ?? 0,
              jewelryId: 0, // 新添加的行没有关联的首饰ID
              barcode: data['barcode']?.toString() ?? 'IMPORT_${DateTime.now().millisecondsSinceEpoch}',
              name: data['name']?.toString() ?? '导入商品',
              categoryId: category.id,
              categoryName: category.name,
              ringSize: data['ringSize']?.toString() ?? '',
              goldWeight: (data['goldWeight'] as double?) ?? 0.0,
              goldPrice: (data['goldPrice'] as double?) ?? 0.0,
              silverWeight: (data['silverWeight'] as double?) ?? 0.0,
              silverPrice: (data['silverPrice'] as double?) ?? 0.0,
              silverWorkType: (data['workType'] as int?) ?? 0,
              silverWorkPrice: (data['laborCost'] as double?) ?? 0.0,
              platingCost: (data['platingCost'] as double?) ?? 0.0,
              wholesaleWorkPrice: (data['wholesaleWorkPrice'] as double?) ?? 0.0,
              retailWorkPrice: (data['retailWorkPrice'] as double?) ?? 0.0,
              pieceWorkPrice: (data['pieceWorkPrice'] as double?) ?? 0.0,
            );

            // 调试日志：打印转换后的StockInItem
            LoggerService.d('导入数据转换', '转换后商品: ${item.name}, 金重: ${item.goldWeight}, 银重: ${item.silverWeight}, 工费: ${item.silverWorkPrice}, 工费方式: ${item.silverWorkType}');

            // 添加到控制器的商品列表中
            controller.itemList.add(item);
          }
          
          // 手动计算总金额
          double total = 0;
          for (var item in controller.itemList) {
            total += item.amount;
          }
          controller.totalAmount.value = total;
        },
      );

      // 关闭进度对话框
      Get.back();

      // 显示结果
      final duplicateCount = previewData.duplicateRowCount;
      final message = duplicateCount > 0
          ? '成功导入 ${result.successCount} 条数据，跳过重复条码 $duplicateCount 条，失败 ${result.failureCount} 条'
          : '成功导入 ${result.successCount} 条数据，失败 ${result.failureCount} 条';

      Get.snackbar(
        '导入完成',
        message,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: result.isAllSuccess && duplicateCount == 0 ? Colors.green : Colors.orange,
        colorText: Colors.white,
        duration: const Duration(seconds: 4), // 延长显示时间，让用户看清楚详细信息
      );

    } catch (e) {
      // 关闭进度对话框
      Get.back();

      LoggerService.e('执行导入失败', e);
      Get.snackbar(
        '错误',
        '导入失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 添加新行
  void _addNewStockInItem() async {
    try {
      // 生成新条码
      final barcode = await _generateBarcode();
      
      // 先找到商品分类，避免空值错误
      final defaultCategory = controller.categoryList.isNotEmpty ? controller.categoryList.first : null;
      
      // 创建新的商品项
      final newItem = StockInItem(
        id: 0,
        stockInId: controller.currentStockIn.value?.id ?? 0,
        jewelryId: 0, // 新添加的行没有关联的首饰ID
        barcode: barcode,
        name: '',
        categoryId: defaultCategory?.id ?? 0,
        categoryName: defaultCategory?.name,
        ringSize: '',
        // 其他可选参数可以省略，使用默认值null
      );
      
      // 添加到控制器的商品列表中
      controller.itemList.add(newItem);
      
      // 手动计算总计，因为_calculateTotalAmount是私有方法
      double total = 0;
      for (var item in controller.itemList) {
        total += item.amount;
      }
      controller.totalAmount.value = total;
    } catch (e) {
      Get.snackbar(
        '错误',
        '添加新行失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
  
  /// 生成条码
  Future<String> _generateBarcode() async {
    final now = DateTime.now();

    // 生成日期部分：YYMMDD
    final year = now.year % 100; // 取年份后两位
    final month = now.month;
    final day = now.day;
    final datePrefix = '${year.toString().padLeft(2, '0')}${month.toString().padLeft(2, '0')}${day.toString().padLeft(2, '0')}';

    // 生成4位随机序列号
    final sequenceStr = (1000 + now.millisecond % 9000).toString();
    
    return '$datePrefix$sequenceStr';
  }
}