import 'package:intl/intl.dart';

/// 金银分离记录模型
class MetalSeparation {
  final int? id;
  final int processId; // 处理记录ID
  final int recyclingItemId; // 回收物品ID
  final double originalGoldWeight; // 原金重
  final double originalSilverWeight; // 原银重
  final double separatedGoldWeight; // 分离后金重
  final double separatedSilverWeight; // 分离后银重
  final double lossRate; // 损耗率(%)
  final double goldPrice; // 金价(克价)
  final double silverPrice; // 银价(克价)
  final double separationCost; // 分离成本
  final String? remark; // 备注
  final DateTime? createTime;
  final int operatorId;
  final String operatorName;
  final DateTime operationDate;
  final double goldWeight;
  final double silverWeight;
  final double otherWeight;
  final double purityRate;
  final String description;
  final int status; // 0: 进行中, 1: 已完成, 2: 已取消
  final List<String>? imageUrls; // 图片URL列表

  const MetalSeparation({
    this.id,
    required this.processId,
    required this.recyclingItemId,
    required this.originalGoldWeight,
    required this.originalSilverWeight,
    required this.separatedGoldWeight,
    required this.separatedSilverWeight,
    required this.lossRate,
    required this.goldPrice,
    required this.silverPrice,
    required this.separationCost,
    this.remark,
    this.createTime,
    required this.operatorId,
    required this.operatorName,
    required this.operationDate,
    required this.goldWeight,
    required this.silverWeight,
    required this.otherWeight,
    required this.purityRate,
    this.description = '',
    required this.status,
    this.imageUrls,
  });

  /// 从JSON构造
  factory MetalSeparation.fromJson(Map<String, dynamic> json) {
    // 安全的数字转换函数
    double parseDouble(dynamic value) {
      if (value == null) return 0.0;
      if (value is double) return value;
      if (value is int) return value.toDouble();
      if (value is String) {
        return double.tryParse(value) ?? 0.0;
      }
      return 0.0;
    }

    List<String>? parseImageUrls;
    if (json['image_urls'] != null) {
      if (json['image_urls'] is List) {
        parseImageUrls = (json['image_urls'] as List)
            .map((item) => item.toString())
            .toList();
      } else if (json['image_urls'] is String) {
        // 如果是以逗号分隔的字符串
        parseImageUrls = (json['image_urls'] as String).split(',');
      }
    }

    return MetalSeparation(
      id: json['id'],
      processId: json['process_id'],
      recyclingItemId: json['recycling_item_id'],
      originalGoldWeight: parseDouble(json['original_gold_weight']),
      originalSilverWeight: parseDouble(json['original_silver_weight']),
      separatedGoldWeight: parseDouble(json['separated_gold_weight']),
      separatedSilverWeight: parseDouble(json['separated_silver_weight']),
      lossRate: parseDouble(json['loss_rate']),
      goldPrice: parseDouble(json['gold_price']),
      silverPrice: parseDouble(json['silver_price']),
      separationCost: parseDouble(json['separation_cost']),
      remark: json['remark'],
      createTime: json['createtime'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['createtime'] * 1000)
          : null,
      operatorId: json['operator_id'],
      operatorName: json['operator_name'],
      operationDate: DateTime.fromMillisecondsSinceEpoch(
        json['operation_date'] * 1000,
      ),
      goldWeight: parseDouble(json['gold_weight']),
      silverWeight: parseDouble(json['silver_weight']),
      otherWeight: parseDouble(json['other_weight']),
      purityRate: parseDouble(json['purity_rate']),
      description: json['description'] ?? '',
      status: json['status'],
      imageUrls: parseImageUrls,
    );
  }

  /// 转为JSON
  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      'process_id': processId,
      'recycling_item_id': recyclingItemId,
      'original_gold_weight': originalGoldWeight,
      'original_silver_weight': originalSilverWeight,
      'separated_gold_weight': separatedGoldWeight,
      'separated_silver_weight': separatedSilverWeight,
      'loss_rate': lossRate,
      'gold_price': goldPrice,
      'silver_price': silverPrice,
      'separation_cost': separationCost,
      if (remark != null) 'remark': remark,
      if (createTime != null)
        'createtime': createTime!.millisecondsSinceEpoch ~/ 1000,
      'operator_id': operatorId,
      'operator_name': operatorName,
      'operation_date': operationDate.millisecondsSinceEpoch ~/ 1000,
      'gold_weight': goldWeight,
      'silver_weight': silverWeight,
      'other_weight': otherWeight,
      'purity_rate': purityRate,
      'description': description,
      'status': status,
      if (imageUrls != null && imageUrls!.isNotEmpty)
        'image_urls': imageUrls!.join(','),
    };
  }

  /// 计算金重损耗
  double get goldWeightLoss {
    return originalGoldWeight - separatedGoldWeight;
  }

  /// 计算银重损耗
  double get silverWeightLoss {
    return originalSilverWeight - separatedSilverWeight;
  }

  /// 计算分离后金值
  double get separatedGoldValue {
    return separatedGoldWeight * goldPrice;
  }

  /// 计算分离后银值
  double get separatedSilverValue {
    return separatedSilverWeight * silverPrice;
  }

  /// 计算分离后总价值
  double get totalValue {
    return separatedGoldValue + separatedSilverValue;
  }

  /// 计算分离利润
  double get profit {
    final originalValue =
        originalGoldWeight * goldPrice + originalSilverWeight * silverPrice;
    return totalValue - originalValue - separationCost;
  }

  /// 创建一个新实例，但使用部分属性
  MetalSeparation copyWith({
    int? id,
    int? processId,
    int? recyclingItemId,
    double? originalGoldWeight,
    double? originalSilverWeight,
    double? separatedGoldWeight,
    double? separatedSilverWeight,
    double? lossRate,
    double? goldPrice,
    double? silverPrice,
    double? separationCost,
    String? remark,
    DateTime? createTime,
    int? operatorId,
    String? operatorName,
    DateTime? operationDate,
    double? goldWeight,
    double? silverWeight,
    double? otherWeight,
    double? purityRate,
    String? description,
    int? status,
    List<String>? imageUrls,
  }) {
    return MetalSeparation(
      id: id ?? this.id,
      processId: processId ?? this.processId,
      recyclingItemId: recyclingItemId ?? this.recyclingItemId,
      originalGoldWeight: originalGoldWeight ?? this.originalGoldWeight,
      originalSilverWeight: originalSilverWeight ?? this.originalSilverWeight,
      separatedGoldWeight: separatedGoldWeight ?? this.separatedGoldWeight,
      separatedSilverWeight:
          separatedSilverWeight ?? this.separatedSilverWeight,
      lossRate: lossRate ?? this.lossRate,
      goldPrice: goldPrice ?? this.goldPrice,
      silverPrice: silverPrice ?? this.silverPrice,
      separationCost: separationCost ?? this.separationCost,
      remark: remark ?? this.remark,
      createTime: createTime ?? this.createTime,
      operatorId: operatorId ?? this.operatorId,
      operatorName: operatorName ?? this.operatorName,
      operationDate: operationDate ?? this.operationDate,
      goldWeight: goldWeight ?? this.goldWeight,
      silverWeight: silverWeight ?? this.silverWeight,
      otherWeight: otherWeight ?? this.otherWeight,
      purityRate: purityRate ?? this.purityRate,
      description: description ?? this.description,
      status: status ?? this.status,
      imageUrls: imageUrls ?? this.imageUrls,
    );
  }

  /// 获取状态文本
  String get statusText {
    switch (status) {
      case 0:
        return '进行中';
      case 1:
        return '已完成';
      case 2:
        return '已取消';
      default:
        return '未知状态';
    }
  }

  /// 获取格式化的日期
  String get formattedDate {
    return DateFormat('yyyy-MM-dd').format(operationDate);
  }

  /// 获取总重量
  double get totalWeight {
    return goldWeight + silverWeight + otherWeight;
  }
}
