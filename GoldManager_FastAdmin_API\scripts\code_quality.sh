#!/bin/bash
# 代码质量检查和格式化脚本
# 黄金珠宝管理系统 - 代码质量工具

set -e

echo "🔍 开始代码质量检查..."

# 确保在正确的目录
cd "$(dirname "$0")/.."

# 检查是否安装了ruff
if ! command -v ruff &> /dev/null; then
    echo "❌ Ruff 未安装，请先安装: pip install ruff"
    exit 1
fi

echo "📋 当前工作目录: $(pwd)"

# 1. 代码格式化
echo "🎨 正在格式化代码..."
ruff format app/ --diff
if [ $? -eq 0 ]; then
    echo "✅ 代码格式化检查通过"
    ruff format app/
    echo "✅ 代码格式化完成"
else
    echo "❌ 代码格式化检查失败"
    exit 1
fi

# 2. 代码静态检查
echo "🔍 正在进行代码静态检查..."
ruff check app/ --fix
if [ $? -eq 0 ]; then
    echo "✅ 代码静态检查通过"
else
    echo "❌ 代码静态检查发现问题，已自动修复可修复的问题"
    echo "📋 请检查剩余问题并手动修复"
fi

# 3. 生成检查报告
echo "📊 生成代码质量报告..."
ruff check app/ --output-format=github > reports/ruff_report.txt 2>&1 || true
ruff check app/ --statistics > reports/ruff_stats.txt 2>&1 || true

# 4. 检查特定文件类型
echo "🔍 检查Python文件质量..."
python_files=$(find app/ -name "*.py" -type f | wc -l)
echo "📁 发现 $python_files 个Python文件"

# 5. 复杂度检查
echo "🧮 检查代码复杂度..."
ruff check app/ --select=C90 || true

# 6. 安全性检查
echo "🔒 检查潜在安全问题..."
ruff check app/ --select=S || true

echo "✅ 代码质量检查完成！"
echo "📋 详细报告已保存到 reports/ 目录"
echo "💡 建议在提交代码前运行此脚本"