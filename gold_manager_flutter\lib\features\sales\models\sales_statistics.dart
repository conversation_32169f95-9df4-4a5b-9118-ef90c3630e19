import '../../../core/utils/safe_converter.dart';

/// 销售统计数据模型
class SalesStatistics {
  final int totalItems;
  final double totalSalesAmount;
  final double totalCostAmount;
  final double totalProfit;
  final double profitRate;
  final Map<String, int> salesTypeCount;
  final Map<String, double> salesTypeAmount;

  // 兼容性属性（为视图提供别名）
  double get todayAmount => totalSalesAmount; // 假设当前统计就是今日数据
  double get monthAmount => totalSalesAmount; // 假设当前统计就是月度数据
  int get totalOrders => totalItems; // 使用总项目数作为订单数

  SalesStatistics({
    required this.totalItems,
    required this.totalSalesAmount,
    required this.totalCostAmount,
    required this.totalProfit,
    required this.profitRate,
    required this.salesTypeCount,
    required this.salesTypeAmount,
  });

  /// 从JSON创建实例
  factory SalesStatistics.fromJson(Map<String, dynamic> json) {
    // 🔧 修复：使用后端API实际返回的字段名
    return SalesStatistics(
      totalItems: json['total_count'] ?? 0,  // 修复字段名映射
      totalSalesAmount: SafeConverter.toDouble(json['total_sales']),  // 修复字段名映射
      totalCostAmount: SafeConverter.toDouble(json['total_cost']),  // 修复字段名映射
      totalProfit: SafeConverter.toDouble(json['total_profit']),
      profitRate: SafeConverter.toDouble(json['average_profit_rate']),  // 修复字段名映射
      salesTypeCount: _extractSalesTypeCount(json['by_type'] ?? []),  // 从by_type数组中提取
      salesTypeAmount: _extractSalesTypeAmount(json['by_type'] ?? []),  // 从by_type数组中提取
    );
  }

  /// 从by_type数组中提取销售类型件数统计
  static Map<String, int> _extractSalesTypeCount(List<dynamic> byType) {
    Map<String, int> result = {};
    for (var item in byType) {
      if (item is Map<String, dynamic>) {
        String typeName = item['type_name'] ?? '';
        int count = item['total_count'] ?? 0;
        result[typeName] = count;
      }
    }
    return result;
  }

  /// 从by_type数组中提取销售类型金额统计
  static Map<String, double> _extractSalesTypeAmount(List<dynamic> byType) {
    Map<String, double> result = {};
    for (var item in byType) {
      if (item is Map<String, dynamic>) {
        String typeName = item['type_name'] ?? '';
        double amount = SafeConverter.toDouble(item['total_sales']);
        result[typeName] = amount;
      }
    }
    return result;
  }


  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'total_items': totalItems,
      'total_sales_amount': totalSalesAmount,
      'total_cost_amount': totalCostAmount,
      'total_profit': totalProfit,
      'profit_rate': profitRate,
      'sales_type_count': salesTypeCount,
      'sales_type_amount': salesTypeAmount,
    };
  }

  /// 创建空的统计数据
  factory SalesStatistics.empty() {
    return SalesStatistics(
      totalItems: 0,
      totalSalesAmount: 0.0,
      totalCostAmount: 0.0,
      totalProfit: 0.0,
      profitRate: 0.0,
      salesTypeCount: {},
      salesTypeAmount: {},
    );
  }

  /// 创建副本
  SalesStatistics copyWith({
    int? totalItems,
    double? totalSalesAmount,
    double? totalCostAmount,
    double? totalProfit,
    double? profitRate,
    Map<String, int>? salesTypeCount,
    Map<String, double>? salesTypeAmount,
  }) {
    return SalesStatistics(
      totalItems: totalItems ?? this.totalItems,
      totalSalesAmount: totalSalesAmount ?? this.totalSalesAmount,
      totalCostAmount: totalCostAmount ?? this.totalCostAmount,
      totalProfit: totalProfit ?? this.totalProfit,
      profitRate: profitRate ?? this.profitRate,
      salesTypeCount: salesTypeCount ?? this.salesTypeCount,
      salesTypeAmount: salesTypeAmount ?? this.salesTypeAmount,
    );
  }

  /// 格式化销售金额显示
  String get formattedSalesAmount {
    return '¥${totalSalesAmount.toStringAsFixed(2)}';
  }

  /// 格式化利润显示
  String get formattedProfit {
    return '¥${totalProfit.toStringAsFixed(2)}';
  }

  /// 格式化利润率显示
  String get formattedProfitRate {
    return '${profitRate.toStringAsFixed(1)}%';
  }

  @override
  String toString() {
    return 'SalesStatistics(totalItems: $totalItems, totalSalesAmount: $totalSalesAmount, totalProfit: $totalProfit, profitRate: $profitRate)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SalesStatistics &&
        other.totalItems == totalItems &&
        other.totalSalesAmount == totalSalesAmount &&
        other.totalProfit == totalProfit;
  }

  @override
  int get hashCode {
    return totalItems.hashCode ^
        totalSalesAmount.hashCode ^
        totalProfit.hashCode;
  }
}