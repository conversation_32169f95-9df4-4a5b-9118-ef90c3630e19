@echo off
echo 正在启动Flutter调试...
cd /D "C:\WorkSpace\GoldManager\gold_manager_flutter"

echo 当前目录: %CD%

echo 方法1: 尝试标准调试模式
flutter run -d windows

if %ERRORLEVEL% NEQ 0 (
    echo 标准模式失败，尝试其他方法...
    
    echo 方法2: 尝试profile模式
    flutter run -d windows --profile
    
    if %ERRORLEVEL% NEQ 0 (
        echo Profile模式失败，启动已编译的exe...
        
        echo 方法3: 直接运行exe文件
        if exist "build\windows\x64\runner\Debug\gold_manager_flutter.exe" (
            echo 找到Debug版本，正在启动...
            start "" "build\windows\x64\runner\Debug\gold_manager_flutter.exe"
        ) else (
            echo 未找到Debug版本，尝试编译...
            flutter build windows --debug
            if exist "build\windows\x64\runner\Debug\gold_manager_flutter.exe" (
                start "" "build\windows\x64\runner\Debug\gold_manager_flutter.exe"
            )
        )
    )
)

pause