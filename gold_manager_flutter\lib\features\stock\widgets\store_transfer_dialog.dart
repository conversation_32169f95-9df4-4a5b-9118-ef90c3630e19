import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gold_manager_flutter/core/constants/border_styles.dart';
import '../controllers/store_transfer_form_controller.dart';

/// 新建调拨对话框组件
/// 严格遵循统一边框调用方式文档的规范
class StoreTransferDialog extends StatefulWidget {
  const StoreTransferDialog({super.key});

  @override
  State<StoreTransferDialog> createState() => _StoreTransferDialogState();
}

class _StoreTransferDialogState extends State<StoreTransferDialog> {
  late StoreTransferFormController controller;

  @override
  void initState() {
    super.initState();
    // 创建控制器实例
    controller = Get.put(StoreTransferFormController());
  }

  @override
  void dispose() {
    // 清理控制器
    Get.delete<StoreTransferFormController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(16),
        child: Form(
          key: controller.formKey,
          child: Column(
            children: [
              _buildDialogHeader(),
              const SizedBox(height: 16),
              _buildBasicInfoSection(),
              const SizedBox(height: 16),
              Expanded(
                child: _buildTransferItemList(),
              ),
              const SizedBox(height: 16),
              _buildSummarySection(),
              const SizedBox(height: 16),
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建对话框标题和关闭按钮
  Widget _buildDialogHeader() {
    return Row(
      children: [
        Icon(Icons.swap_horiz, color: Colors.blue[600], size: 20),
        const SizedBox(width: 8),
        const Text(
          '新建库存调拨',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const Spacer(),
        IconButton(
          onPressed: () => Get.back(),
          icon: const Icon(Icons.close),
          style: IconButton.styleFrom(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建基本信息区域（单行布局）
  /// 严格遵循AppBorderStyles.standardBoxDecoration规范
  Widget _buildBasicInfoSection() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: AppBorderStyles.standardBoxDecoration,
      child: Row(
        children: [
          // 源门店选择
          Expanded(
            flex: 2,
            child: _buildStoreSelector('源门店', true),
          ),
          const SizedBox(width: 8),
          // 目标门店选择
          Expanded(
            flex: 2,
            child: _buildStoreSelector('目标门店', false),
          ),
          const SizedBox(width: 8),
          // 扫描输入框
          Expanded(
            flex: 3,
            child: _buildBarcodeInput(),
          ),
          const SizedBox(width: 8),
          // 批量调价按钮
          SizedBox(
            height: 32,
            child: ElevatedButton.icon(
              icon: const Icon(Icons.price_change, size: 14),
              label: const Text('批量调价'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange[600],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 0),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
                ),
                textStyle: const TextStyle(fontSize: 13),
              ),
              onPressed: _batchChangePrice,
            ),
          ),
          const SizedBox(width: 8),
          // 备注输入框
          Expanded(
            flex: 2,
            child: _buildRemarkInput(),
          ),
        ],
      ),
    );
  }

  /// 构建门店选择器
  /// 严格遵循AppBorderStyles.standardBoxDecoration规范
  Widget _buildStoreSelector(String label, bool isSource) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '$label:',
          style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 4),
        Container(
          height: 32,
          decoration: AppBorderStyles.standardBoxDecoration,
          child: Obx(() => DropdownButtonHideUnderline(
            child: DropdownButton<int>(
              value: isSource 
                ? (controller.sourceStoreId.value == 0 ? null : controller.sourceStoreId.value)
                : (controller.targetStoreId.value == 0 ? null : controller.targetStoreId.value),
              hint: Container(
                alignment: Alignment.center,
                child: Text(
                  '请选择$label',
                  style: const TextStyle(fontSize: 13, color: Colors.grey),
                  textAlign: TextAlign.center,
                ),
              ),
              isExpanded: true,
              items: controller.availableStores.map((store) {
                return DropdownMenuItem<int>(
                  value: store.id,
                  child: Text(
                    store.name,
                    style: const TextStyle(fontSize: 13),
                    textAlign: TextAlign.center,
                  ),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  if (isSource) {
                    controller.sourceStoreId.value = value;
                  } else {
                    controller.targetStoreId.value = value;
                  }
                }
              },
              style: const TextStyle(fontSize: 13, color: Colors.black87),
              icon: const Icon(Icons.arrow_drop_down, size: 20),
              iconSize: 20,
              menuMaxHeight: 300,
              padding: const EdgeInsets.symmetric(horizontal: 8),
              selectedItemBuilder: (BuildContext context) {
                return controller.availableStores.map<Widget>((store) {
                  return Container(
                    alignment: Alignment.center,
                    child: Text(
                      store.name,
                      style: const TextStyle(fontSize: 13, color: Colors.black87),
                      textAlign: TextAlign.center,
                    ),
                  );
                }).toList();
              },
            ),
          )),
        ),
      ],
    );
  }

  /// 构建扫描输入框
  /// 严格遵循AppBorderStyles.compactInputDecoration规范
  Widget _buildBarcodeInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '商品条码:',
          style: TextStyle(fontSize: 13, fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 4),
        SizedBox(
          height: 32,
          child: TextFormField(
            controller: controller.barcodeController,
            decoration: AppBorderStyles.compactInputDecoration.copyWith(
              hintText: '请扫描或输入商品条码',
              prefixIcon: const Icon(Icons.qr_code, size: 18),
              contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
            style: const TextStyle(fontSize: 13),
            onFieldSubmitted: (value) => controller.scanBarcode(value),
          ),
        ),
      ],
    );
  }

  /// 构建备注输入框
  /// 严格遵循统一边框调用方式文档中的32px高度输入框模板
  Widget _buildRemarkInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '备注:',
          style: TextStyle(fontSize: 13, fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 4),
        Container(
          height: 32,
          decoration: AppBorderStyles.standardBoxDecoration,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
            child: TextField(
              controller: controller.remarkController,
              decoration: const InputDecoration(
                hintText: '输入备注信息',
                hintStyle: TextStyle(fontSize: 13, color: Colors.grey),
                // 完整移除所有边框
                border: InputBorder.none,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                disabledBorder: InputBorder.none,
                // 正确的文字对齐配置
                contentPadding: EdgeInsets.symmetric(vertical: 8),
                isDense: true,
              ),
              style: const TextStyle(fontSize: 13),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建调拨商品列表
  Widget _buildTransferItemList() {
    return Container(
      decoration: AppBorderStyles.standardBoxDecoration,
      child: Column(
        children: [
          // 列表标题
          Container(
            padding: const EdgeInsets.all(12),
            decoration: const BoxDecoration(
              color: AppBorderStyles.tableHeaderBackground,
              border: Border(
                bottom: AppBorderStyles.tableBorder,
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.inventory_2, color: Colors.blue[600], size: 16),
                const SizedBox(width: 8),
                const Text(
                  '调拨商品列表',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          ),
          // 表头
          _buildListHeader(),
          // 商品列表
          Expanded(
            child: Obx(() => controller.transferItems.isEmpty
              ? const Center(
                  child: Text(
                    '暂无调拨商品\n请扫描商品条码添加',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                )
              : ListView.builder(
                  itemCount: controller.transferItems.length,
                  itemBuilder: (context, index) => _buildListItem(index),
                ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建列表表头
  Widget _buildListHeader() {
    return Container(
      height: 40,
      decoration: AppBorderStyles.headerCellDecoration(),
      child: const Row(
        children: [
          Expanded(flex: 2, child: Center(child: Text('条码', style: TextStyle(fontSize: 13, fontWeight: FontWeight.w600)))),
          Expanded(flex: 3, child: Center(child: Text('商品名称', style: TextStyle(fontSize: 13, fontWeight: FontWeight.w600)))),
          Expanded(flex: 2, child: Center(child: Text('当前门店', style: TextStyle(fontSize: 13, fontWeight: FontWeight.w600)))),
          Expanded(flex: 2, child: Center(child: Text('调拨价格', style: TextStyle(fontSize: 13, fontWeight: FontWeight.w600)))),
          Expanded(flex: 1, child: Center(child: Text('类型', style: TextStyle(fontSize: 13, fontWeight: FontWeight.w600)))),
          Expanded(flex: 1, child: Center(child: Text('操作', style: TextStyle(fontSize: 13, fontWeight: FontWeight.w600)))),
        ],
      ),
    );
  }

  /// 构建列表项
  Widget _buildListItem(int index) {
    final item = controller.transferItems[index];
    final isEven = index % 2 == 0;
    
    return Container(
      height: 38,
      decoration: AppBorderStyles.cellDecoration(isEven: isEven),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Center(
              child: Text(
                item.barcode,
                style: const TextStyle(fontSize: 13),
                textAlign: TextAlign.center,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Center(
              child: Text(
                item.jewelry?.name ?? '',
                style: const TextStyle(fontSize: 13),
                textAlign: TextAlign.center,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Center(
              child: Text(
                item.jewelry?.store?.name ?? '',
                style: const TextStyle(fontSize: 13),
                textAlign: TextAlign.center,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Center(
              child: Text(
                '¥${item.transferPrice.round()}',
                style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w500),
                textAlign: TextAlign.center,
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Center(
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: item.transferTypeColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
                  border: Border.all(color: item.transferTypeColor, width: 1),
                ),
                child: Text(
                  item.transferTypeText,
                  style: TextStyle(
                    fontSize: 12,
                    color: item.transferTypeColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Center(
              child: IconButton(
                onPressed: () => _confirmRemoveItem(index),
                icon: const Icon(Icons.delete, size: 16),
                style: IconButton.styleFrom(
                  foregroundColor: Colors.red,
                  minimumSize: const Size(24, 24),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建汇总信息
  Widget _buildSummarySection() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: AppBorderStyles.standardBoxDecoration,
      child: Row(
        children: [
          Icon(Icons.calculate, color: Colors.green[600], size: 16),
          const SizedBox(width: 8),
          const Text(
            '汇总信息',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(width: 24),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
              border: Border.all(color: Colors.blue[200]!, width: 1),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.inventory_2, size: 14, color: Colors.blue[600]),
                const SizedBox(width: 4),
                const Text('商品总数: ', style: TextStyle(fontSize: 13, color: Colors.black87)),
                Obx(() => Text(
                  '${controller.totalCount.value}件',
                  style: TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w600,
                    color: Colors.blue[700],
                  ),
                )),
              ],
            ),
          ),
          const SizedBox(width: 16),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.green[50],
              borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
              border: Border.all(color: Colors.green[200]!, width: 1),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.attach_money, size: 14, color: Colors.green[600]),
                const SizedBox(width: 4),
                const Text('调拨总额: ', style: TextStyle(fontSize: 13, color: Colors.black87)),
                Obx(() => Text(
                  '¥${controller.totalAmount.value.round()}',
                  style: TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w600,
                    color: Colors.green[700],
                  ),
                )),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        // 取消按钮
        SizedBox(
          height: 32,
          child: TextButton(
            onPressed: () => Get.back(),
            style: TextButton.styleFrom(
              foregroundColor: Colors.grey[600],
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
              ),
            ),
            child: const Text('取消'),
          ),
        ),
        const SizedBox(width: 16),
        // 保存草稿按钮
        SizedBox(
          height: 32,
          child: OutlinedButton(
            onPressed: () => controller.saveDraft(),
            style: OutlinedButton.styleFrom(
              side: BorderSide(color: Colors.grey[400]!, width: 1),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
              ),
              textStyle: const TextStyle(fontSize: 13),
            ),
            child: const Text('保存草稿'),
          ),
        ),
        const SizedBox(width: 16),
        // 提交审核按钮
        SizedBox(
          height: 32,
          child: ElevatedButton(
            onPressed: () => controller.submitForAudit(),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue[600],
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
              ),
              textStyle: const TextStyle(fontSize: 13),
            ),
            child: const Text('提交审核'),
          ),
        ),
      ],
    );
  }

  /// 批量调价功能
  void _batchChangePrice() {
    if (controller.transferItems.isEmpty) {
      Get.snackbar(
        '提示',
        '当前没有商品，无法进行批量调价',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      return;
    }
    _showBatchPriceChangeDialog();
  }

  /// 显示批量调价对话框
  /// 完全复用新建出库单的批量调价实现
  void _showBatchPriceChangeDialog() {
    final goldPriceController = TextEditingController();
    final silverPriceController = TextEditingController();
    final workPriceController = TextEditingController();
    final pieceWorkPriceController = TextEditingController();

    Get.dialog(
      AlertDialog(
        title: Row(
          children: [
            Icon(Icons.price_change, color: Colors.orange[600], size: 20),
            const SizedBox(width: 8),
            const Text('批量调价', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
          ],
        ),
        content: SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 提示信息
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(AppBorderStyles.mediumBorderRadius),
                  border: Border.all(color: Colors.blue[200]!, width: AppBorderStyles.borderWidth),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info_outline, color: Colors.blue[600], size: 16),
                    const SizedBox(width: 8),
                    const Expanded(
                      child: Text(
                        '只填写需要修改的价格项，空白项将保持原值不变',
                        style: TextStyle(fontSize: 12, color: Colors.black87),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),

              // 价格输入字段
              _buildPriceInputField('金价 (元/克)', goldPriceController, Icons.monetization_on, Colors.amber[600]!),
              const SizedBox(height: 12),
              _buildPriceInputField('银价 (元/克)', silverPriceController, Icons.circle, Colors.grey[600]!),
              const SizedBox(height: 12),
              _buildPriceInputField('批发工费 (元/克)', workPriceController, Icons.build, Colors.blue[600]!),
              const SizedBox(height: 12),
              _buildPriceInputField('件工费 (元/件)', pieceWorkPriceController, Icons.handyman, Colors.green[600]!),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              _executeBatchPriceChange(
                goldPriceController.text,
                silverPriceController.text,
                workPriceController.text,
                pieceWorkPriceController.text,
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange[600],
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
              ),
            ),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  /// 构建价格输入字段
  /// 严格遵循AppBorderStyles.standardBoxDecoration规范
  Widget _buildPriceInputField(String label, TextEditingController controller, IconData icon, Color iconColor) {
    return Row(
      children: [
        Icon(icon, color: iconColor, size: 16),
        const SizedBox(width: 8),
        SizedBox(
          width: 80,
          child: Text(
            label,
            style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w500),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Container(
            height: 32,
            decoration: AppBorderStyles.standardBoxDecoration,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              child: TextField(
                controller: controller,
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                decoration: const InputDecoration(
                  hintText: '可选',
                  hintStyle: TextStyle(fontSize: 12, color: Colors.grey),
                  border: InputBorder.none,
                  enabledBorder: InputBorder.none,
                  focusedBorder: InputBorder.none,
                  errorBorder: InputBorder.none,
                  focusedErrorBorder: InputBorder.none,
                  contentPadding: EdgeInsets.zero,
                  isDense: true,
                ),
                style: const TextStyle(fontSize: 13),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 执行批量调价
  void _executeBatchPriceChange(String goldPriceStr, String silverPriceStr, String workPriceStr, String pieceWorkPriceStr) {
    // 解析输入的价格
    double? goldPrice = goldPriceStr.isNotEmpty ? double.tryParse(goldPriceStr) : null;
    double? silverPrice = silverPriceStr.isNotEmpty ? double.tryParse(silverPriceStr) : null;
    double? workPrice = workPriceStr.isNotEmpty ? double.tryParse(workPriceStr) : null;
    double? pieceWorkPrice = pieceWorkPriceStr.isNotEmpty ? double.tryParse(pieceWorkPriceStr) : null;

    // 检查是否至少有一个价格被修改
    if (goldPrice == null && silverPrice == null && workPrice == null && pieceWorkPrice == null) {
      Get.snackbar(
        '提示',
        '请至少填写一个价格项',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      return;
    }

    Get.back(); // 关闭输入对话框

    // 显示确认对话框
    Get.dialog(
      AlertDialog(
        title: const Text('确认批量调价'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('即将对当前调拨单中的所有商品进行批量调价：'),
            const SizedBox(height: 8),
            if (goldPrice != null) Text('• 金价：${goldPrice.round()} 元/克'),
            if (silverPrice != null) Text('• 银价：${silverPrice.round()} 元/克'),
            if (workPrice != null) Text('• 批发工费：${workPrice.round()} 元/克'),
            if (pieceWorkPrice != null) Text('• 件工费：${pieceWorkPrice.round()} 元/件'),
            const SizedBox(height: 8),
            Text('共 ${controller.transferItems.length} 件商品将被更新'),
            const SizedBox(height: 8),
            const Text('此操作不可撤销，请确认是否继续？', style: TextStyle(color: Colors.red, fontWeight: FontWeight.w500)),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              controller.batchUpdatePrices(
                goldPrice: goldPrice,
                silverPrice: silverPrice,
                workPrice: workPrice,
                pieceWorkPrice: pieceWorkPrice,
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red[600],
              foregroundColor: Colors.white,
            ),
            child: const Text('确认执行'),
          ),
        ],
      ),
    );
  }

  /// 确认删除商品
  void _confirmRemoveItem(int index) {
    Get.dialog(
      AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppBorderStyles.largeBorderRadius),
        ),
        title: Row(
          children: [
            Icon(Icons.warning_amber_rounded, color: Colors.red[600]),
            const SizedBox(width: 8),
            const Text('确认删除'),
          ],
        ),
        content: SizedBox(
          width: 300,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red[50],
                  borderRadius: BorderRadius.circular(AppBorderStyles.mediumBorderRadius),
                  border: Border.all(color: Colors.red[200]!),
                ),
                child: Row(
                  children: [
                    Icon(Icons.delete_forever, color: Colors.red[600], size: 20),
                    const SizedBox(width: 8),
                    const Expanded(
                      child: Text(
                        '确定要删除这件商品吗？删除后将从调拨单中移除。',
                        style: TextStyle(fontSize: 13, fontWeight: FontWeight.w500),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            style: TextButton.styleFrom(
              foregroundColor: Colors.grey[600],
            ),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              controller.removeTransferItem(index);
              Get.back();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red[600],
              foregroundColor: Colors.white,
            ),
            child: const Text('确定删除'),
          ),
        ],
      ),
    );
  }
}
