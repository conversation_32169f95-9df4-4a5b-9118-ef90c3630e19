import 'package:get/get.dart';

/// 用户模型
class User {
  final int id;
  final String username;
  final String name;
  final String? nickname;
  final String role;
  final int storeId;
  final RxList<String> permissions;
  
  const User({
    required this.id,
    required this.username,
    required this.name,
    this.nickname,
    required this.role,
    required this.storeId,
    required this.permissions,
  });
  
  /// 从JSON构造
  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      username: json['username'],
      name: json['name'],
      nickname: json['nickname'],
      role: json['role'],
      storeId: json['store_id'],
      permissions: RxList<String>.from(json['permissions'] ?? []),
    );
  }
  
  /// 转为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'name': name,
      'nickname': nickname,
      'role': role,
      'store_id': storeId,
      'permissions': permissions.toList(),
    };
  }
} 