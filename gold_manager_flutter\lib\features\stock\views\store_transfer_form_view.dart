import 'package:flutter/material.dart';
import '../widgets/store_transfer_form_content.dart';

/// 新建库存调拨表单视图（标签页版本）
/// 参考新建出库单的标签页实现模式
class StoreTransferFormView extends StatelessWidget {
  /// 标签页标识，用于支持多标签页模式
  final String? tag;

  const StoreTransferFormView({
    super.key,
    this.tag,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: StoreTransferFormContent(tag: tag),
    );
  }
}
