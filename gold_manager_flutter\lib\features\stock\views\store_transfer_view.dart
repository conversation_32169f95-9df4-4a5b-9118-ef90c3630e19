import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:gold_manager_flutter/core/constants/border_styles.dart';
import 'package:gold_manager_flutter/core/theme/app_theme.dart';
import 'package:gold_manager_flutter/services/auth_service.dart';
import 'package:gold_manager_flutter/widgets/empty_state.dart';
import 'package:gold_manager_flutter/widgets/loading_state.dart';
import 'package:gold_manager_flutter/widgets/responsive_builder.dart';
import '../controllers/store_transfer_controller.dart';
import '../controllers/stock_tab_controller.dart';
import '../widgets/store_transfer_dialog.dart';
import 'package:gold_manager_flutter/core/utils/logger.dart';

/// 库存调拨管理页面
/// 完全参考出库管理页面的UI设计标准
class StoreTransferView extends GetView<StoreTransferController> {
  const StoreTransferView({super.key});

  @override
  Widget build(BuildContext context) {
    return Material(
      color: AppTheme.backgroundColor,
      child: Column(
        children: [
          _buildFilterSection(),
          const Divider(height: 1),
          Expanded(
            child: Obx(() {
              if (controller.isLoading.value) {
                return const LoadingState(text: '加载中...', timeoutSeconds: 30);
              }

              if (controller.transferList.isEmpty) {
                return EmptyState(
                  icon: Icons.swap_horiz,
                  title: '暂无调拨单',
                  message: '点击右上角按钮创建新的调拨单',
                  buttonText: '新建调拨',
                  onButtonPressed: _showNewTransferDialog,
                );
              }

              return _buildTransferList();
            }),
          ),
          const Divider(height: 1),
          _buildBottomSection(), // 新的底部区域，包含统计信息和分页
        ],
      ),
    );
  }

  /// 构建筛选区域 - 完全复用出库管理页面的单行布局结构
  Widget _buildFilterSection() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(bottom: AppBorderStyles.tableBorder),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center, // 确保所有控件垂直居中对齐
        children: [
          // 图标和标题 - 复用出库管理页面的样式
          Icon(Icons.swap_horiz, color: Colors.blue[600], size: 20),
          const SizedBox(width: 8),
          const Text(
            '库存调拨',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(width: 24),

          // 操作员信息标签 - 复用出库管理页面的样式
          Obx(() {
            final authService = Get.find<AuthService>();
            final operatorName = authService.userNickname.value.isNotEmpty
                ? authService.userNickname.value
                : authService.userName.value;
            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(
                  AppBorderStyles.largeBorderRadius,
                ), // 使用统一的大圆角
                border: Border.all(
                  color: Colors.blue[200]!,
                  width: AppBorderStyles.borderWidth,
                ), // 使用统一边框宽度
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.person, size: 14, color: Colors.blue[600]),
                  const SizedBox(width: 4),
                  Text(
                    '操作员: $operatorName',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.blue[700],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            );
          }),
          const SizedBox(width: 24),

          // 源门店标签和选择器
          const Text(
            '源门店:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(width: 8),
          SizedBox(
            width: 130, // 从120px减小到90px
            child: _buildCompactStoreSelector(true),
          ),
          const SizedBox(width: 16), // 从24px减小到16px
          // 目标门店标签和选择器
          const Text(
            '目标门店:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(width: 8),
          SizedBox(
            width: 130, // 从120px减小到90px
            child: _buildCompactStoreSelector(false),
          ),
          const SizedBox(width: 16), // 从24px减小到16px
          // 状态标签和选择器
          const Text(
            '状态:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(width: 8),
          SizedBox(
            width: 90, // 从100px减小到80px
            child: _buildCompactStatusSelector(),
          ),
          const SizedBox(width: 16), // 从24px减小到16px
          // 日期范围标签和选择器
          const Text(
            '日期范围:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(width: 8),
          SizedBox(
            width: 220, // 从220px减小到160px
            child: _buildCompactDateRangeSelector(),
          ),
          const SizedBox(width: 16), // 从24px减小到16px
          // 搜索框 - 紧凑型设计
          const Text(
            '搜索:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(width: 8),
          SizedBox(
            width: 140, // 从200px减小到100px
            child: _buildCompactSearchField(),
          ),
          const SizedBox(width: 12), // 从24px减小到12px
          // 操作按钮组
          _buildResetButton(),
          const SizedBox(width: 6), // 从8px减小到6px
          _buildSearchButton(),
          const SizedBox(width: 6), // 从8px减小到6px
          _buildNewTransferButton(),
        ],
      ),
    );
  }

  /// 构建紧凑型搜索字段（单行布局）
  Widget _buildCompactSearchField() {
    return Container(
      height: 32, // 紧凑高度
      decoration: AppBorderStyles.standardBoxDecoration,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
        child: TextField(
          decoration: const InputDecoration(
            hintText: '调拨单号、备注等',
            border: InputBorder.none,
            enabledBorder: InputBorder.none,
            focusedBorder: InputBorder.none,
            disabledBorder: InputBorder.none,
            errorBorder: InputBorder.none,
            focusedErrorBorder: InputBorder.none,
            contentPadding: EdgeInsets.symmetric(vertical: 8),
            hintStyle: TextStyle(fontSize: 13, color: Colors.grey),
            isDense: true,
          ),
          style: const TextStyle(fontSize: 13),
          onChanged: (value) => controller.searchKeyword.value = value,
          onSubmitted: (_) => controller.searchTransfers(),
        ),
      ),
    );
  }

  /// 构建紧凑型门店选择器（单行布局）
  Widget _buildCompactStoreSelector(bool isSource) {
    return Obx(() {
      // 检查用户权限
      final authService = Get.find<AuthService>();
      final isAdmin =
          authService.userRole.value == 'admin' ||
          authService.hasPermission('super.admin');
      final currentUserStoreId = authService.storeId.value;
      final currentStoreName = authService.storeName.value;

      if (!isAdmin && currentUserStoreId > 0 && isSource) {
        // 普通员工：源门店显示当前用户所属门店，不可选择
        return Container(
          height: 32, // 紧凑高度
          decoration: BoxDecoration(
            border: Border.all(color: AppBorderStyles.borderColor),
            borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
            color: Colors.grey[50],
          ),
          child: Row(
            children: [
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  currentStoreName,
                  style: const TextStyle(fontSize: 13, color: Colors.black87),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const Icon(Icons.lock, size: 14, color: Colors.grey),
              const SizedBox(width: 8),
            ],
          ),
        );
      } else {
        // 管理员：可以选择门店
        return Container(
          height: 32, // 紧凑高度
          decoration: AppBorderStyles.standardBoxDecoration,
          child: DropdownButtonHideUnderline(
            child: DropdownButton<int>(
              value: isSource
                  ? (controller.selectedFromStoreId.value == 0
                        ? null
                        : controller.selectedFromStoreId.value)
                  : (controller.selectedToStoreId.value == 0
                        ? null
                        : controller.selectedToStoreId.value),
              hint: Text(
                isSource ? '选择源门店' : '选择目标门店',
                style: const TextStyle(fontSize: 13, color: Colors.grey),
              ),
              isExpanded: true,
              items: [
                const DropdownMenuItem<int>(
                  value: null,
                  child: Text('全部门店', style: TextStyle(fontSize: 13)),
                ),
                ...controller.storeList.map(
                  (store) => DropdownMenuItem<int>(
                    value: store['id'],
                    child: Text(
                      store['name'],
                      style: const TextStyle(fontSize: 13),
                    ),
                  ),
                ),
              ],
              onChanged: (value) {
                if (isSource) {
                  controller.selectedFromStoreId.value = value ?? 0;
                } else {
                  controller.selectedToStoreId.value = value ?? 0;
                }
                controller.searchTransfers();
              },
              style: const TextStyle(fontSize: 13, color: Colors.black87),
              icon: const Icon(Icons.arrow_drop_down, size: 20),
              iconSize: 20,
              menuMaxHeight: 300,
              padding: const EdgeInsets.symmetric(horizontal: 8),
            ),
          ),
        );
      }
    });
  }

  /// 构建紧凑型状态选择器（单行布局）
  Widget _buildCompactStatusSelector() {
    return Obx(
      () => Container(
        height: 32, // 紧凑高度
        decoration: AppBorderStyles.standardBoxDecoration,
        child: DropdownButtonHideUnderline(
          child: DropdownButton<int>(
            value: controller.selectedStatus.value == -1
                ? null
                : controller.selectedStatus.value,
            hint: const Text(
              '选择状态',
              style: TextStyle(fontSize: 13, color: Colors.grey),
            ),
            isExpanded: true,
            items: [
              const DropdownMenuItem<int>(
                value: null,
                child: Text('全部状态', style: TextStyle(fontSize: 13)),
              ),
              ...controller.statusOptions.map(
                (option) => DropdownMenuItem<int>(
                  value: option['value'],
                  child: Text(
                    option['label'],
                    style: const TextStyle(fontSize: 13),
                  ),
                ),
              ),
            ],
            onChanged: (value) {
              controller.selectedStatus.value = value ?? -1;
              controller.searchTransfers();
            },
            style: const TextStyle(fontSize: 13, color: Colors.black87),
            icon: const Icon(Icons.arrow_drop_down, size: 20),
            iconSize: 20,
            menuMaxHeight: 300,
            padding: const EdgeInsets.symmetric(horizontal: 8),
          ),
        ),
      ),
    );
  }

  /// 构建紧凑型日期范围选择器（单行布局）
  Widget _buildCompactDateRangeSelector() {
    return Obx(
      () => Container(
        height: 32, // 紧凑高度，与其他控件保持一致
        decoration: AppBorderStyles.standardBoxDecoration,
        child: InkWell(
          onTap: _showDateRangePicker,
          borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
            child: Row(
              children: [
                const Icon(Icons.date_range, size: 16, color: Colors.grey),
                const SizedBox(width: 6),
                Expanded(
                  child: Text(
                    controller.dateRangeDisplayText,
                    style: TextStyle(
                      fontSize: 13,
                      color:
                          (controller.startDate.value != null ||
                              controller.endDate.value != null)
                          ? Colors.black87
                          : Colors.grey,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const Icon(Icons.arrow_drop_down, size: 20, color: Colors.grey),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建重置按钮 - 紧凑型设计
  Widget _buildResetButton() {
    return SizedBox(
      height: 32, // 强制高度为32px，与其他控件保持一致
      child: OutlinedButton.icon(
        icon: const Icon(Icons.refresh, size: 14),
        label: const Text('重置'),
        style: OutlinedButton.styleFrom(
          foregroundColor: Colors.grey[600],
          padding: const EdgeInsets.symmetric(
            vertical: 0,
            horizontal: 12,
          ), // 移除垂直内边距，由SizedBox控制高度
          minimumSize: const Size(0, 32), // 固定高度32px
          maximumSize: const Size(double.infinity, 32), // 限制最大高度
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
          ),
          side: const BorderSide(color: AppBorderStyles.borderColor),
          textStyle: const TextStyle(fontSize: 13), // 紧凑字体
        ),
        onPressed: controller.resetFilters,
      ),
    );
  }

  /// 构建搜索按钮 - 紧凑型设计
  Widget _buildSearchButton() {
    return SizedBox(
      height: 32, // 强制高度为32px，与其他控件保持一致
      child: ElevatedButton.icon(
        icon: const Icon(Icons.search, size: 14),
        label: const Text('搜索'),
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF1E88E5), // 使用UI规范主色调
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(
            vertical: 0,
            horizontal: 12,
          ), // 移除垂直内边距，由SizedBox控制高度
          minimumSize: const Size(0, 32), // 固定高度32px
          maximumSize: const Size(double.infinity, 32), // 限制最大高度
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
          ),
          textStyle: const TextStyle(fontSize: 13), // 紧凑字体
        ),
        onPressed: controller.searchTransfers,
      ),
    );
  }

  /// 构建新建调拨按钮 - 紧凑型设计
  Widget _buildNewTransferButton() {
    return SizedBox(
      height: 32, // 强制高度为32px，与其他控件保持一致
      child: ElevatedButton.icon(
        icon: const Icon(Icons.add_box, size: 14),
        label: const Text('新建调拨'),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.green[600], // 绿色背景
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(
            vertical: 0,
            horizontal: 12,
          ), // 移除垂直内边距，由SizedBox控制高度
          minimumSize: const Size(0, 32), // 固定高度32px
          maximumSize: const Size(double.infinity, 32), // 限制最大高度
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
          ),
          textStyle: const TextStyle(fontSize: 13), // 紧凑字体
        ),
        onPressed: _showNewTransferDialog,
      ),
    );
  }

  /// 构建底部区域（统计信息 + 分页）
  Widget _buildBottomSection() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 16),
      color: Colors.white,
      child: Row(
        children: [
          // 左侧：统计信息
          Expanded(child: _buildStatisticsInfo()),
          // 右侧：分页组件
          _buildPaginationControls(),
        ],
      ),
    );
  }

  /// 构建统计信息（紧凑版本）
  Widget _buildStatisticsInfo() {
    return Obx(() {
      return Wrap(
        spacing: 16,
        runSpacing: 8,
        children: [
          _buildCompactStatItem(
            '总调拨单',
            '${controller.totalTransferCount.value}',
            Colors.blue,
          ),
          _buildCompactStatItem(
            '待审核',
            '${controller.pendingAuditCount.value}',
            Colors.orange,
          ),
          _buildCompactStatItem(
            '已通过',
            '${controller.approvedCount.value}',
            Colors.green,
          ),
          _buildCompactStatItem(
            '已拒绝',
            '${controller.rejectedCount.value}',
            Colors.red,
          ),
          _buildCompactStatItem(
            '总金额',
            '¥${controller.totalTransferAmount.value.toStringAsFixed(2)}',
            Colors.purple,
          ),
          _buildCompactStatItem(
            '总商品数',
            '${controller.totalCount.value}',
            Colors.teal,
          ),
        ],
      );
    });
  }

  /// 构建紧凑统计项
  Widget _buildCompactStatItem(
    String label,
    String value,
    MaterialColor color,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color[50],
        borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
        border: Border.all(color: color[200]!, width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            '$label: ',
            style: const TextStyle(fontSize: 12, color: Colors.black87),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: color[700],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建分页控件（紧凑版本）
  Widget _buildPaginationControls() {
    return Obx(
      () => Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          IconButton(
            icon: const Icon(Icons.first_page, size: 20),
            tooltip: '第一页',
            onPressed: controller.currentPage.value > 1
                ? () => controller.goToPage(1)
                : null,
          ),
          IconButton(
            icon: const Icon(Icons.chevron_left, size: 20),
            tooltip: '上一页',
            onPressed: controller.currentPage.value > 1
                ? () => controller.previousPage()
                : null,
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
              border: Border.all(color: Colors.grey[300]!, width: 1),
            ),
            child: Text(
              '${controller.currentPage.value} / ${controller.totalPages.value}',
              style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.chevron_right, size: 20),
            tooltip: '下一页',
            onPressed:
                controller.currentPage.value < controller.totalPages.value
                ? () => controller.nextPage()
                : null,
          ),
          IconButton(
            icon: const Icon(Icons.last_page, size: 20),
            tooltip: '最后一页',
            onPressed:
                controller.currentPage.value < controller.totalPages.value
                ? () => controller.goToPage(controller.totalPages.value)
                : null,
          ),
        ],
      ),
    );
  }

  /// 构建调拨单列表
  Widget _buildTransferList() {
    return ScreenTypeLayout(
      mobile: _buildListView(),
      tablet: _buildDataTable(),
      desktop: _buildDataTable(),
    );
  }

  /// 构建列表视图 (用于移动设备)
  Widget _buildListView() {
    return ListView.separated(
      padding: const EdgeInsets.all(16),
      itemCount: controller.transferList.length,
      separatorBuilder: (context, index) => const Divider(height: 16),
      itemBuilder: (context, index) {
        final transfer = controller.transferList[index];

        return Card(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(
              AppBorderStyles.largeBorderRadius,
            ), // 使用统一的大圆角
          ),
          elevation: 2,
          child: InkWell(
            borderRadius: BorderRadius.circular(
              AppBorderStyles.largeBorderRadius,
            ), // 使用统一的大圆角
            onTap: () => _viewTransferDetail(transfer),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        transfer['transferNo'] ?? '',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      _buildStatusBadge(transfer),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      const Icon(Icons.store, size: 16, color: Colors.grey),
                      const SizedBox(width: 4),
                      Text(
                        '${transfer['fromStoreName'] ?? ''} → ${transfer['toStoreName'] ?? ''}',
                        style: const TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      const Icon(
                        Icons.access_time,
                        size: 16,
                        color: Colors.grey,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        _formatDate(DateTime.now()),
                        style: const TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '商品数: ${transfer['itemCount'] ?? 0}',
                        style: const TextStyle(
                          fontWeight: FontWeight.w500,
                          color: Colors.blue,
                        ),
                      ),
                      Text(
                        '金额: ¥${(transfer['totalAmount'] ?? 0).round()}',
                        style: const TextStyle(
                          fontWeight: FontWeight.w500,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: _buildActionButtons(transfer),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// 格式化日期
  String _formatDate(DateTime date) {
    return DateFormat('yyyy-MM-dd HH:mm').format(date);
  }

  /// 格式化创建时间（处理时间戳和字符串格式）
  String _formatCreateTime(dynamic createTime) {
    try {
      DateTime dateTime;

      if (createTime == null) {
        return '-';
      } else if (createTime is int) {
        // Unix时间戳（秒）
        dateTime = DateTime.fromMillisecondsSinceEpoch(createTime * 1000);
      } else if (createTime is String) {
        // ISO字符串格式
        dateTime = DateTime.parse(createTime);
      } else {
        return '-';
      }

      return _formatDate(dateTime);
    } catch (e) {
      print('⚠️ 时间格式化失败: $createTime, 错误: $e');
      return '-';
    }
  }

  /// 构建数据表格 (用于平板和桌面设备) - 完全复用出库管理页面的样式
  Widget _buildDataTable() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // 获取可用宽度
        final availableWidth = constraints.maxWidth;

        // 优化列宽分配：添加商品数列
        final transferNoWidth = availableWidth * 0.16; // 16% - 调拨单号
        final fromStoreWidth = availableWidth * 0.11; // 11% - 源门店
        final toStoreWidth = availableWidth * 0.11; // 11% - 目标门店
        final itemCountWidth = availableWidth * 0.09; // 9% - 商品数
        final amountWidth = availableWidth * 0.12; // 12% - 金额
        final statusWidth = availableWidth * 0.09; // 9% - 状态
        final timeWidth = availableWidth * 0.14; // 14% - 创建时间
        final actionWidth = availableWidth * 0.18; // 18% - 操作

        // 根据可用宽度调整字体大小
        final fontSize = availableWidth < 800 ? 12.0 : 14.0;
        final headingFontSize = availableWidth < 800 ? 13.0 : 15.0;

        return Container(
          margin: const EdgeInsets.all(4), // 与出库管理页面保持一致的边距
          decoration: AppBorderStyles.elevatedBoxDecoration.copyWith(
            color: Colors.white, // 明确设置背景色为白色
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(
              AppBorderStyles.borderRadius,
            ), // 使用标准圆角
            child: SingleChildScrollView(
              scrollDirection: Axis.vertical,
              child: SizedBox(
                width: availableWidth,
                child: DataTable(
                  columnSpacing: 0, // 移除列间距，让列宽完全由我们控制
                  horizontalMargin: 0, // 移除水平边距
                  headingRowHeight: 44, // 与出库管理页面保持一致的表头高度
                  dataRowMinHeight: 48, // 与出库管理页面保持一致的数据行高度
                  dataRowMaxHeight: 48,
                  headingTextStyle: TextStyle(
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                    fontSize: headingFontSize,
                  ),
                  dataTextStyle: TextStyle(
                    fontSize: fontSize,
                    color: Colors.black87,
                  ),
                  headingRowColor: WidgetStateProperty.all(
                    AppBorderStyles.tableHeaderBackground,
                  ), // 使用统一的表头背景色
                  border: AppBorderStyles.tableStandardBorder, // 使用统一的表格边框
                  columns: [
                    DataColumn(
                      label: Container(
                        width: transferNoWidth,
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: const Text('调拨单号', textAlign: TextAlign.center),
                      ),
                    ),
                    DataColumn(
                      label: Container(
                        width: fromStoreWidth,
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: const Text('源门店', textAlign: TextAlign.center),
                      ),
                    ),
                    DataColumn(
                      label: Container(
                        width: toStoreWidth,
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: const Text('目标门店', textAlign: TextAlign.center),
                      ),
                    ),
                    DataColumn(
                      label: Container(
                        width: itemCountWidth,
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: const Text('商品数', textAlign: TextAlign.center),
                      ),
                    ),
                    DataColumn(
                      label: Container(
                        width: amountWidth,
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: const Text('调拨金额', textAlign: TextAlign.center),
                      ),
                    ),
                    DataColumn(
                      label: Container(
                        width: statusWidth,
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: const Text('状态', textAlign: TextAlign.center),
                      ),
                    ),
                    DataColumn(
                      label: Container(
                        width: timeWidth,
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: const Text('创建时间', textAlign: TextAlign.center),
                      ),
                    ),
                    DataColumn(
                      label: Container(
                        width: actionWidth,
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: const Text('操作', textAlign: TextAlign.center),
                      ),
                    ),
                  ],
                  rows: controller.transferList.map((transfer) {
                    return DataRow(
                      cells: [
                        DataCell(
                          Container(
                            width: transferNoWidth,
                            height: 48, // 与出库管理页面保持一致的固定高度
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                            alignment: Alignment.center, // 垂直和水平都居中
                            child: Text(
                              transfer['transferNo'] ?? '',
                              style: const TextStyle(
                                fontWeight: FontWeight.w500,
                                color: AppTheme.primaryColor,
                              ),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                          onTap: () => _viewTransferDetail(transfer),
                        ),
                        DataCell(
                          Container(
                            width: fromStoreWidth,
                            height: 48, // 与出库管理页面保持一致的高度
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                            alignment: Alignment.center,
                            child: Text(
                              transfer['fromStoreName'] ?? '',
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                        ),
                        DataCell(
                          Container(
                            width: toStoreWidth,
                            height: 48, // 与出库管理页面保持一致的高度
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                            alignment: Alignment.center,
                            child: Text(
                              transfer['toStoreName'] ?? '',
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                        ),
                        DataCell(
                          Container(
                            width: itemCountWidth,
                            height: 48, // 与出库管理页面保持一致的高度
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                            alignment: Alignment.center,
                            child: Text(
                              '${transfer['itemCount'] ?? 0}',
                              style: const TextStyle(
                                fontWeight: FontWeight.w500,
                                color: Colors.blue,
                              ),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                        ),
                        DataCell(
                          Container(
                            width: amountWidth,
                            height: 48, // 与出库管理页面保持一致的高度
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                            alignment: Alignment.center, // 垂直和水平都居中
                            child: Text(
                              '¥${(transfer['totalAmount'] ?? 0).round()}',
                              style: const TextStyle(
                                fontWeight: FontWeight.w500,
                                color: Colors.green,
                              ),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                        ),
                        DataCell(
                          Container(
                            width: statusWidth,
                            height: 48, // 与出库管理页面保持一致的高度
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                            alignment: Alignment.center, // 垂直和水平都居中
                            child: _buildStatusBadge(transfer),
                          ),
                        ),
                        DataCell(
                          Container(
                            width: timeWidth,
                            height: 48, // 与出库管理页面保持一致的高度
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                            alignment: Alignment.center,
                            child: Text(
                              _formatCreateTime(transfer['createTime']),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                        ),
                        DataCell(
                          Container(
                            width: actionWidth,
                            height: 48, // 与出库管理页面保持一致的高度
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                            alignment: Alignment.center,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              mainAxisSize: MainAxisSize.min,
                              children: _buildActionButtons(
                                transfer,
                                availableWidth < 800,
                              ),
                            ),
                          ),
                        ),
                      ],
                    );
                  }).toList(),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// 构建状态徽章 - 完全复用出库管理页面的样式
  Widget _buildStatusBadge(Map<String, dynamic> transfer) {
    final statusText = transfer['statusText'] ?? '草稿';
    final status = transfer['status'] ?? 0;

    Color statusColor;
    switch (status) {
      case 0:
        statusColor = Colors.grey;
        break;
      case 1:
        statusColor = Colors.orange;
        break;
      case 2:
        statusColor = Colors.green;
        break;
      case 3:
        statusColor = Colors.red;
        break;
      default:
        statusColor = Colors.grey;
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 5,
        vertical: 2,
      ), // 与出库管理页面保持一致的内边距
      decoration: BoxDecoration(
        color: statusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(
          AppBorderStyles.borderRadius,
        ), // 使用统一的标准圆角
        border: Border.all(color: statusColor, width: 1),
      ),
      child: Text(
        statusText,
        style: TextStyle(
          color: statusColor,
          fontSize: 11, // 与出库管理页面保持一致的字体大小
          fontWeight: FontWeight.w500,
        ),
        overflow: TextOverflow.ellipsis, // 防止文字溢出
        maxLines: 1, // 确保单行显示
      ),
    );
  }

  /// 构建操作按钮 - 完全复用出库管理页面的样式和逻辑
  List<Widget> _buildActionButtons(
    Map<String, dynamic> transfer, [
    bool isSmallScreen = false,
  ]) {
    final List<Widget> buttons = [];
    final double iconSize = isSmallScreen ? 18 : 20;
    final double buttonSize = isSmallScreen ? 32 : 40;

    // 查看按钮
    buttons.add(
      SizedBox(
        width: buttonSize,
        height: buttonSize,
        child: IconButton(
          icon: Icon(Icons.visibility, color: Colors.blue, size: iconSize),
          tooltip: '查看',
          padding: EdgeInsets.zero,
          onPressed: () => _viewTransferDetail(transfer),
        ),
      ),
    );

    // 编辑按钮 (仅草稿和待审核可编辑)
    final status = transfer['status'] ?? 0;
    if (status == 0 || status == 1) {
      buttons.add(
        SizedBox(
          width: buttonSize,
          height: buttonSize,
          child: IconButton(
            icon: Icon(Icons.edit, color: Colors.orange, size: iconSize),
            tooltip: '编辑',
            padding: EdgeInsets.zero,
            onPressed: () => _editTransfer(transfer),
          ),
        ),
      );
    }

    // 审核按钮 (仅待审核可审核且需要管理员权限)
    if (status == 1) {
      buttons.add(
        SizedBox(
          width: buttonSize,
          height: buttonSize,
          child: IconButton(
            icon: Icon(Icons.check_circle, color: Colors.green, size: iconSize),
            tooltip: '审核',
            padding: EdgeInsets.zero,
            onPressed: () => _auditTransfer(transfer),
          ),
        ),
      );
    }

    // 删除按钮 (仅草稿和待审核可删除)
    if (status == 0 || status == 1) {
      buttons.add(
        SizedBox(
          width: buttonSize,
          height: buttonSize,
          child: IconButton(
            icon: Icon(Icons.delete, color: Colors.red, size: iconSize),
            tooltip: '删除',
            padding: EdgeInsets.zero,
            onPressed: () => _deleteTransfer(transfer),
          ),
        ),
      );
    }

    return buttons;
  }

  /// 打开新建调拨标签页
  void _showNewTransferDialog() {
    try {
      // 获取标签页控制器
      final stockTabController = Get.find<StockTabController>();

      // 打开新建库存调拨标签页
      stockTabController.openStoreTransferForm();

      LoggerService.d('✅ 成功打开新建库存调拨标签页');
    } catch (e) {
      LoggerService.e('❌ 打开新建库存调拨标签页失败', e);

      // 如果标签页控制器不可用，回退到对话框模式
      Get.dialog(const StoreTransferDialog(), barrierDismissible: false).then((
        result,
      ) {
        if (result == true) {
          // 刷新列表
          controller.refreshData();
        }
      });
    }
  }

  /// 显示日期范围选择器
  Future<void> _showDateRangePicker() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: Get.context!,
      firstDate: DateTime(2020, 1, 1),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      initialDateRange:
          (controller.startDate.value != null &&
              controller.endDate.value != null)
          ? DateTimeRange(
              start: controller.startDate.value!,
              end: controller.endDate.value!,
            )
          : null,
      locale: const Locale('zh', 'CN'),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: const Color(0xFF1E88E5), // 使用UI规范主色调
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Colors.black87,
            ),
            dialogTheme: const DialogThemeData(backgroundColor: Colors.white),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      controller.updateDateRange(picked.start, picked.end);
    }
  }

  /// 查看调拨单详情
  void _viewTransferDetail(transfer) {
    // TODO: 实现查看详情功能
    Get.snackbar('提示', '查看详情功能开发中...');
  }

  /// 编辑调拨单
  void _editTransfer(transfer) {
    // TODO: 实现编辑功能
    Get.snackbar('提示', '编辑功能开发中...');
  }

  /// 审核调拨单
  void _auditTransfer(transfer) {
    final transferId = transfer['id'] ?? 0;
    final transferNo = transfer['transfer_no'] ?? '';

    Get.dialog(
      AlertDialog(
        title: const Text('审核调拨单'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('调拨单号：$transferNo'),
            const SizedBox(height: 16),
            const Text('请选择审核结果：'),
          ],
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('取消')),
          TextButton(
            onPressed: () {
              Get.back();
              _performAudit(transferId, false, '审核拒绝');
            },
            child: const Text('拒绝', style: TextStyle(color: Colors.red)),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              _performAudit(transferId, true, '审核通过');
            },
            child: const Text('通过'),
          ),
        ],
      ),
    );
  }

  /// 执行审核操作
  void _performAudit(int transferId, bool approved, String remark) {
    controller.auditTransfer(
      transferId: transferId,
      approved: approved,
      auditRemark: remark,
    );
  }

  /// 删除调拨单
  void _deleteTransfer(transfer) {
    Get.dialog(
      AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(
            AppBorderStyles.largeBorderRadius,
          ), // 使用统一的大圆角
        ),
        title: const Text('确认删除'),
        content: Text('确定要删除调拨单 ${transfer['transferNo'] ?? ''} 吗？'),
        actions: [
          TextButton(
            style: TextButton.styleFrom(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(
                  AppBorderStyles.borderRadius,
                ), // 使用统一的圆角
              ),
            ),
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red[600],
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(
                  AppBorderStyles.borderRadius,
                ), // 使用统一的圆角
              ),
            ),
            onPressed: () {
              Get.back();
              controller.deleteTransfer(transfer['id'] ?? 0);
            },
            child: const Text('确定删除'),
          ),
        ],
      ),
    );
  }
}
