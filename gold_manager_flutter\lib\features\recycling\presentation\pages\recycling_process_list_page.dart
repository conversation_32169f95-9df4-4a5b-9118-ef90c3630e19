import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../models/common/enums.dart';
import '../../../../models/recycling/recycling_process.dart';
import '../../controllers/recycling_process_controller.dart';
import 'recycling_process_form_page.dart';

/// 旧料处理列表页面
class RecyclingProcessListPage extends StatelessWidget {
  final RecyclingProcessController controller;

  const RecyclingProcessListPage({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('旧料处理记录'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: '刷新',
            onPressed: controller.loadProcessList,
          ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }

        if (controller.processList.isEmpty) {
          return _buildEmptyState();
        }

        return RefreshIndicator(
          onRefresh: controller.loadProcessList,
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: controller.processList.length,
            itemBuilder: (context, index) {
              final process = controller.processList[index];
              return _buildProcessCard(context, process);
            },
          ),
        );
      }),
      // 移除FloatingActionButton，使用界面内的具体操作按钮
    );
  }

  // 空状态提示
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.engineering_outlined,
            size: 80,
            color: AppColors.textSecondary,
          ),
          const SizedBox(height: 16),
          const Text(
            '暂无处理记录',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            '点击下方按钮添加处理记录',
            style: TextStyle(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _addNewProcess,
            icon: const Icon(Icons.add),
            label: const Text('添加处理记录'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 处理记录卡片
  Widget _buildProcessCard(BuildContext context, RecyclingProcess process) {
    // 根据处理类型设置颜色
    final typeColor = _getColorForProcessType(process.processType);

    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () => _editProcess(process),
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 卡片顶部
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: typeColor.withOpacity(0.1),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Row(
                      children: [
                        CircleAvatar(
                          backgroundColor: typeColor,
                          radius: 16,
                          child: Icon(
                            _getIconForProcessType(process.processType),
                            color: Colors.white,
                            size: 16,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                process.processType.label,
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: typeColor,
                                ),
                              ),
                              Text(
                                process.processNo,
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: AppColors.textSecondary,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  _buildStatusChip(process.status),
                ],
              ),
            ),

            // 卡片内容
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: _buildInfoItem('处理时间', process.formattedProcessTime),
                      ),
                      Expanded(
                        child: _buildInfoItem('操作员', process.operatorName),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: _buildInfoItem('处理成本', '¥${process.cost.toStringAsFixed(2)}'),
                      ),
                      Expanded(
                        child: _buildInfoItem('处理收入', '¥${process.income.toStringAsFixed(2)}'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),

                  // 利润信息
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: AppColors.backgroundLight,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          '处理利润',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          '¥${process.profit.toStringAsFixed(2)}',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: process.profit >= 0
                                ? AppColors.success
                                : AppColors.error,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // 备注
                  if (process.remark != null && process.remark!.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(top: 12),
                      child: Text(
                        '备注: ${process.remark}',
                        style: const TextStyle(
                          fontSize: 13,
                          color: AppColors.textSecondary,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ),

                  // 图片预览
                  if (process.imageUrls != null && process.imageUrls!.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(top: 12),
                      child: _buildImagePreview(process.imageUrls!),
                    ),
                ],
              ),
            ),

            // 卡片底部操作按钮
            Padding(
              padding: const EdgeInsets.fromLTRB(8, 0, 8, 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  // 状态操作按钮
                  _buildStatusActionButton(process),
                  const SizedBox(width: 8),
                  // 编辑按钮
                  TextButton.icon(
                    onPressed: () => _editProcess(process),
                    icon: const Icon(Icons.edit, size: 18),
                    label: const Text('编辑'),
                  ),
                  const SizedBox(width: 8),
                  // 删除按钮
                  TextButton.icon(
                    onPressed: () => _confirmDelete(process),
                    icon: const Icon(Icons.delete, size: 18, color: AppColors.error),
                    label: const Text('删除', style: TextStyle(color: AppColors.error)),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 状态标签
  Widget _buildStatusChip(int status) {
    Color backgroundColor;
    String statusText;

    switch (status) {
      case 0:
        backgroundColor = AppColors.warning;
        statusText = '进行中';
        break;
      case 1:
        backgroundColor = AppColors.success;
        statusText = '已完成';
        break;
      case 2:
        backgroundColor = AppColors.error;
        statusText = '已取消';
        break;
      default:
        backgroundColor = AppColors.textSecondary;
        statusText = '未知';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor.withOpacity(0.2),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: backgroundColor.withOpacity(0.5)),
      ),
      child: Text(
        statusText,
        style: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: backgroundColor,
        ),
      ),
    );
  }

  // 信息项
  Widget _buildInfoItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  // 图片预览
  Widget _buildImagePreview(List<String> imageUrls) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '处理图片',
          style: TextStyle(
            fontSize: 12,
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 80,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: imageUrls.length,
            itemBuilder: (context, index) {
              return Padding(
                padding: const EdgeInsets.only(right: 8),
                child: GestureDetector(
                  onTap: () => _showFullScreenImage(context, imageUrls, index),
                  child: Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: AppColors.border),
                      image: DecorationImage(
                        image: NetworkImage(imageUrls[index]),
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  // 状态操作按钮
  Widget _buildStatusActionButton(RecyclingProcess process) {
    if (process.status == 1 || process.status == 2) {
      return const SizedBox.shrink();
    }

    return TextButton.icon(
      onPressed: () => _confirmUpdateStatus(process, 1),
      icon: const Icon(Icons.check_circle, size: 18, color: AppColors.success),
      label: const Text('标记完成', style: TextStyle(color: AppColors.success)),
    );
  }

  // 根据处理类型获取颜色
  Color _getColorForProcessType(RecyclingProcessType type) {
    switch (type.value) {
      case 'sell':
        return AppColors.success;
      case 'separate':
        return AppColors.primary;
      case 'repair':
        return AppColors.warning;
      case 'resell':
        return AppColors.info;
      default:
        return AppColors.textSecondary;
    }
  }

  // 根据处理类型获取图标
  IconData _getIconForProcessType(RecyclingProcessType type) {
    switch (type.value) {
      case 'sell':
        return Icons.attach_money;
      case 'separate':
        return Icons.science;
      case 'repair':
        return Icons.build;
      case 'resell':
        return Icons.shopping_bag;
      default:
        return Icons.help_outline;
    }
  }

  // 添加新处理记录
  void _addNewProcess() {
    controller.resetForm();
    Get.to(
      () => RecyclingProcessFormPage(controller: controller),
      transition: Transition.rightToLeft,
    )?.then((result) {
      if (result == true) {
        controller.loadProcessList();
      }
    });
  }

  // 编辑处理记录
  void _editProcess(RecyclingProcess process) async {
    await controller.loadProcessDetail(process.id!);
    Get.to(
      () => RecyclingProcessFormPage(controller: controller),
      transition: Transition.rightToLeft,
    )?.then((result) {
      if (result == true) {
        controller.loadProcessList();
      }
    });
  }

  // 确认删除处理记录
  void _confirmDelete(RecyclingProcess process) {
    Get.dialog(
      AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除处理单号为 ${process.processNo} 的处理记录吗？此操作不可逆。'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () async {
              Get.back();
              final success = await controller.deleteProcess(process.id!);
              if (success) {
                Get.snackbar(
                  '成功',
                  '处理记录已删除',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: AppColors.success.withOpacity(0.8),
                  colorText: Colors.white,
                );
              } else {
                Get.snackbar(
                  '失败',
                  '删除失败，请重试',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: AppColors.error.withOpacity(0.8),
                  colorText: Colors.white,
                );
              }
            },
            child: const Text('删除', style: TextStyle(color: AppColors.error)),
          ),
        ],
      ),
    );
  }

  // 确认更新状态
  void _confirmUpdateStatus(RecyclingProcess process, int status) {
    final statusText = status == 1 ? '完成' : '取消';

    Get.dialog(
      AlertDialog(
        title: Text('确认$statusText'),
        content: Text('确定要将处理单号为 ${process.processNo} 的处理记录标记为$statusText吗？'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () async {
              Get.back();
              final success = await controller.updateProcessStatus(process.id!, status);
              if (success) {
                Get.snackbar(
                  '成功',
                  '处理记录已标记为$statusText',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: AppColors.success.withOpacity(0.8),
                  colorText: Colors.white,
                );
              } else {
                Get.snackbar(
                  '失败',
                  '操作失败，请重试',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: AppColors.error.withOpacity(0.8),
                  colorText: Colors.white,
                );
              }
            },
            child: Text('确认', style: TextStyle(color: status == 1 ? AppColors.success : AppColors.error)),
          ),
        ],
      ),
    );
  }

  // 显示全屏图片
  void _showFullScreenImage(BuildContext context, List<String> imageUrls, int initialIndex) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => Scaffold(
          appBar: AppBar(
            title: Text('图片预览 ${initialIndex + 1}/${imageUrls.length}'),
            backgroundColor: Colors.black,
            iconTheme: const IconThemeData(color: Colors.white),
          ),
          body: PageView.builder(
            itemCount: imageUrls.length,
            controller: PageController(initialPage: initialIndex),
            onPageChanged: (index) {
              // 更新AppBar标题
              (context as Element).markNeedsBuild();
            },
            itemBuilder: (context, index) {
              return GestureDetector(
                onTap: () => Navigator.pop(context),
                child: Center(
                  child: InteractiveViewer(
                    minScale: 0.5,
                    maxScale: 3.0,
                    child: Image.network(
                      imageUrls[index],
                      fit: BoxFit.contain,
                    ),
                  ),
                ),
              );
            },
          ),
          backgroundColor: Colors.black,
        ),
      ),
    );
  }
}