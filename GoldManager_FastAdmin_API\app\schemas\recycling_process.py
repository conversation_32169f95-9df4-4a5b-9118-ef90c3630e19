"""
回收处理相关的Pydantic模型
包含处理工单、处理结果、转库存等数据验证模型
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
from decimal import Decimal
from pydantic import BaseModel, Field, validator


# ========== 处理工单相关 ==========

class RecyclingProcessBase(BaseModel):
    """处理工单基础模型"""
    store_id: int = Field(..., description="处理门店ID")
    recycling_id: int = Field(..., description="关联回收单ID")
    recycling_item_ids: List[int] = Field(..., description="关联回收明细IDs")
    process_type: int = Field(1, description="处理类型：1=金银分离，2=翻新加工，3=直接熔炼")
    remark: Optional[str] = Field(None, description="备注")


class RecyclingProcessCreate(RecyclingProcessBase):
    """创建处理工单请求模型"""
    pass


class RecyclingProcessUpdate(BaseModel):
    """更新处理工单请求模型"""
    store_id: Optional[int] = Field(None, description="处理门店ID")
    processor_id: Optional[int] = Field(None, description="处理人员ID")
    remark: Optional[str] = Field(None, description="备注")


class RecyclingProcessInDB(RecyclingProcessBase):
    """数据库中的处理工单模型"""
    id: int
    process_no: str
    source_store_id: int
    total_weight: Decimal
    estimated_gold_weight: Decimal
    estimated_silver_weight: Decimal
    status: int
    start_time: Optional[int]
    end_time: Optional[int]
    operator_id: int
    processor_id: Optional[int]
    createtime: int
    updatetime: int

    class Config:
        orm_mode = True


class RecyclingProcessResponse(RecyclingProcessInDB):
    """处理工单响应模型"""
    store_name: Optional[str] = None
    source_store_name: Optional[str] = None
    recycling_no: Optional[str] = None
    operator_name: Optional[str] = None
    processor_name: Optional[str] = None
    results: Optional[List["RecyclingProcessResultResponse"]] = []


# ========== 处理结果相关 ==========

class RecyclingProcessResultBase(BaseModel):
    """处理结果基础模型"""
    result_type: int = Field(..., description="结果类型：1=纯金，2=纯银，3=成品首饰，4=金银锭")
    name: str = Field(..., description="产品名称")
    weight: Decimal = Field(..., description="重量（克）")
    purity: Optional[Decimal] = Field(None, description="纯度")
    loss_weight: Decimal = Field(0, description="损耗重量（克）")
    loss_rate: Decimal = Field(0, description="损耗率（%）")
    process_cost: Decimal = Field(0, description="加工成本")
    labor_cost: Decimal = Field(0, description="人工成本")
    other_cost: Decimal = Field(0, description="其他成本")


class RecyclingProcessResultCreate(RecyclingProcessResultBase):
    """创建处理结果请求模型"""
    pass


class RecyclingProcessResultInDB(RecyclingProcessResultBase):
    """数据库中的处理结果模型"""
    id: int
    process_id: int
    total_cost: Decimal
    target_type: Optional[int]
    target_id: Optional[int]
    is_converted: bool
    convert_time: Optional[int]
    createtime: int

    class Config:
        orm_mode = True


class RecyclingProcessResultResponse(RecyclingProcessResultInDB):
    """处理结果响应模型"""
    pass


# ========== 完成处理相关 ==========

class ProcessCompleteRequest(BaseModel):
    """完成处理请求模型"""
    results: List[RecyclingProcessResultCreate] = Field(..., description="处理结果列表")
    processor_id: Optional[int] = Field(None, description="处理人员ID")


# ========== 转库存相关 ==========

class RecyclingToStockBase(BaseModel):
    """转库存基础模型"""
    store_id: int = Field(..., description="入库门店ID")
    process_result_id: int = Field(..., description="处理结果ID")
    stock_type: int = Field(..., description="库存类型：1=原料库存，2=商品库存")
    quantity: int = Field(1, description="数量")
    remark: Optional[str] = Field(None, description="备注")


class RecyclingToStockCreate(RecyclingToStockBase):
    """创建转库存请求模型"""
    # 商品库存时需要的额外信息
    jewelry_info: Optional[Dict[str, Any]] = Field(None, description="商品信息（商品库存时）")
    # 原料库存时需要的额外信息
    material_type: Optional[int] = Field(None, description="原料类型（原料库存时）")


class RecyclingToStockInDB(RecyclingToStockBase):
    """数据库中的转库存模型"""
    id: int
    convert_no: str
    stock_in_id: Optional[int]
    jewelry_id: Optional[int]
    material_id: Optional[int]
    weight: Decimal
    unit_cost: Decimal
    total_cost: Decimal
    cost_detail: Optional[str]
    status: int
    operator_id: int
    createtime: int

    class Config:
        orm_mode = True


class RecyclingToStockResponse(RecyclingToStockInDB):
    """转库存响应模型"""
    store_name: Optional[str] = None
    operator_name: Optional[str] = None
    process_result: Optional[RecyclingProcessResultResponse] = None


# ========== 原料相关 ==========

class MaterialBase(BaseModel):
    """原料基础模型"""
    name: str = Field(..., description="原料名称")
    type: int = Field(..., description="原料类型：1=黄金，2=白银，3=其他")
    purity: Optional[Decimal] = Field(None, description="纯度")
    unit: str = Field("克", description="单位")
    store_id: int = Field(..., description="所属门店ID")


class MaterialCreate(MaterialBase):
    """创建原料请求模型"""
    pass


class MaterialUpdate(BaseModel):
    """更新原料请求模型"""
    name: Optional[str] = None
    type: Optional[int] = None
    purity: Optional[Decimal] = None
    unit: Optional[str] = None
    status: Optional[int] = None


class MaterialInDB(MaterialBase):
    """数据库中的原料模型"""
    id: int
    material_no: str
    current_stock: Decimal
    unit_cost: Decimal
    status: int
    createtime: int
    updatetime: int

    class Config:
        orm_mode = True


class MaterialResponse(MaterialInDB):
    """原料响应模型"""
    store_name: Optional[str] = None


# ========== 查询参数 ==========

class RecyclingProcessQueryParams(BaseModel):
    """处理工单查询参数"""
    page: int = Field(1, gt=0, description="页码")
    page_size: int = Field(20, gt=0, le=100, description="每页数量")
    keyword: Optional[str] = Field(None, description="关键词搜索")
    store_id: Optional[int] = Field(None, description="门店ID")
    status: Optional[int] = Field(None, description="状态")
    process_type: Optional[int] = Field(None, description="处理类型")
    start_date: Optional[str] = Field(None, description="开始日期")
    end_date: Optional[str] = Field(None, description="结束日期")


# 避免循环导入
RecyclingProcessResponse.update_forward_refs()
RecyclingToStockResponse.update_forward_refs()