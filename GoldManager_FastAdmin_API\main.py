#!/usr/bin/env python3
"""
黄金珠宝管理系统 API
基于FastAdmin数据库的现代化API接口

作者: AI Assistant
创建时间: 2024
"""

import uvicorn
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, HTMLResponse
from fastapi.openapi.docs import get_swagger_ui_html
from fastapi.openapi.utils import get_openapi
from loguru import logger
import sys
import os

# 添加app目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), "app"))

from app.core.config import settings
from app.core.database import engine, Base
from app.api.api_v1.api import api_router
from app.core.security_middleware import SecurityHeadersMiddleware, RateLimitMiddleware


# 配置日志
logger.add("logs/api.log", rotation="1 day", retention="30 days")

# 创建FastAPI应用实例 - 添加中文支持
app = FastAPI(
    title=settings.PROJECT_NAME,
    version=settings.PROJECT_VERSION,
    description="""
    ## 🏆 黄金珠宝管理系统 API

    ### 📋 功能说明
    - **商品管理**: 商品信息的增删改查、分类管理
    - **库存管理**: 入库、出库、退货、盘点管理
    - **门店管理**: 多门店支持、调拨管理
    - **会员管理**: 会员信息管理、积分系统
    - **回收管理**: 旧料回收、金银分离处理

    ### 🔧 技术特性
    - 基于FastAdmin数据库结构
    - RESTful API设计
    - 自动数据验证和序列化
    - 完整的错误处理机制
    - 详细的操作日志记录

    ### 📖 使用说明
    1. 点击下方的API分组查看具体接口
    2. 点击"试用"按钮可以直接测试接口
    3. 所有接口都有详细的参数说明和返回示例
    """,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    docs_url=None,  # 禁用默认docs，使用自定义
    redoc_url="/redoc",
    servers=[
        {
            "url": f"http://localhost:{settings.PORT}",
            "description": "开发环境服务器"
        }
    ]
)

# 自定义中文Swagger UI
@app.get("/docs", include_in_schema=False)
async def custom_swagger_ui_html():
    html_content = f"""
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>{settings.PROJECT_NAME} - API文档</title>
        <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5.9.0/swagger-ui.css" />
        <style>
            html {{
                box-sizing: border-box;
                overflow: -moz-scrollbars-vertical;
                overflow-y: scroll;
            }}
            *, *:before, *:after {{
                box-sizing: inherit;
            }}
            body {{
                margin:0;
                background: #fafafa;
                font-family: "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
            }}
        </style>
    </head>
    <body>
        <div id="swagger-ui"></div>

        <script src="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5.9.0/swagger-ui-bundle.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5.9.0/swagger-ui-standalone-preset.js"></script>

        <script>
        window.onload = function() {{

            // 自定义中文翻译
            const ChineseTranslations = {{
                'Parameters': '参数',
                'Execute': '执行',
                'Try it out': '试用',
                'Cancel': '取消',
                'Response body': '响应内容',
                'Response headers': '响应头',
                'Responses': '响应',
                'Request body': '请求内容',
                'Example Value': '示例值',
                'Schema': '数据结构',
                'Model': '数据模型',
                'Request URL': '请求地址',
                'Server response': '服务器响应',
                'Code': '状态码',
                'Details': '详情',
                'Headers': '请求头',
                'No parameters': '无参数',
                'Response Schema': '响应数据结构',
                'Request Schema': '请求数据结构',
                'Clear': '清除',
                'Download': '下载',
                'Curl': 'Curl命令',
                'Request samples': '请求示例',
                'Response samples': '响应示例',
                'Media type': '数据类型',
                'Controls Accept header': '控制Accept请求头',
                'Successful Response': '请求成功',
                'Error Response': '错误响应',
                'Description': '说明',
                'Links': '链接',
                'No links': '无链接',
                'application/json': 'JSON格式',
                'String': '字符串',
                'Integer': '整数',
                'Boolean': '布尔值',
                'Array': '数组',
                'Object': '对象',
                'required': '必填',
                'optional': '可选',
                'GET': '查询',
                'POST': '创建',
                'PUT': '更新',
                'DELETE': '删除',
                'PATCH': '修改'
            }};

            // 初始化Swagger UI
            const ui = SwaggerUIBundle({{
                url: "{app.openapi_url}",
                dom_id: '#swagger-ui',
                deepLinking: true,
                presets: [
                    SwaggerUIBundle.presets.apis,
                    SwaggerUIStandalonePreset
                ],
                plugins: [
                    SwaggerUIBundle.plugins.DownloadUrl
                ],
                layout: "StandaloneLayout",
                defaultModelsExpandDepth: 1,
                defaultModelExpandDepth: 1,
                displayOperationId: false,
                displayRequestDuration: true,
                docExpansion: "list",
                filter: true,
                showExtensions: true,
                showCommonExtensions: true,
                tryItOutEnabled: true,
                supportedSubmitMethods: ["get", "post", "put", "delete", "patch"],
                operationsSorter: "alpha",
                tagsSorter: "alpha"
            }});

            // 翻译函数
            function translateInterface() {{
                // 延迟执行，确保DOM已渲染
                setTimeout(() => {{
                    // 翻译所有文本内容
                    const elements = document.querySelectorAll('button, span, label, th, td, .parameter__name, .opblock-summary-description, .tab-header, .response-col_description, .parameter__type, .parameter-item-wrap, .opblock-section-header');

                    elements.forEach(element => {{
                        if (element.textContent && element.children.length === 0) {{
                            const text = element.textContent.trim();
                            if (ChineseTranslations[text]) {{
                                element.textContent = ChineseTranslations[text];
                            }}
                        }}
                    }});

                    // 翻译placeholder
                    const inputs = document.querySelectorAll('input[placeholder], textarea[placeholder]');
                    inputs.forEach(input => {{
                        const placeholder = input.placeholder;
                        if (ChineseTranslations[placeholder]) {{
                            input.placeholder = ChineseTranslations[placeholder];
                        }}
                    }});

                }}, 500);
            }}

            // 初始翻译
            translateInterface();

            // 监听DOM变化
            const observer = new MutationObserver((mutations) => {{
                let shouldTranslate = false;
                mutations.forEach((mutation) => {{
                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {{
                        shouldTranslate = true;
                    }}
                }});

                if (shouldTranslate) {{
                    translateInterface();
                }}
            }});

            observer.observe(document.body, {{
                childList: true,
                subtree: true
            }});

        }};
        </script>
    </body>
    </html>
    """

    return HTMLResponse(content=html_content)

# 自定义OpenAPI schema
def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema

    openapi_schema = get_openapi(
        title=settings.PROJECT_NAME,
        version=settings.PROJECT_VERSION,
        description=app.description,
        routes=app.routes,
    )

    # 自定义响应描述
    openapi_schema["info"]["x-logo"] = {
        "url": "https://fastapi.tiangolo.com/img/logo-margin/logo-teal.png"
    }

    # 添加中文标签描述
    openapi_schema["tags"] = [
        {
            "name": "系统信息",
            "description": "系统基本信息和健康检查接口"
        },
        {
            "name": "商品管理",
            "description": "商品信息的增删改查、分类管理、成本计算等功能"
        },
        {
            "name": "门店管理",
            "description": "门店信息管理、多门店支持"
        },
        {
            "name": "会员管理",
            "description": "会员信息管理、积分系统"
        },
        {
            "name": "库存管理",
            "description": "入库、出库、退货、盘点等库存相关操作"
        },
        {
            "name": "认证",
            "description": "用户登录、权限验证相关接口"
        },
        {
            "name": "仪表板",
            "description": "数据统计和报表相关接口"
        },
        {
            "name": "管理员",
            "description": "管理员账户管理相关接口"
        }
    ]

    # 添加通用响应模式的中文描述
    openapi_schema["components"] = openapi_schema.get("components", {})
    openapi_schema["components"]["responses"] = {
        "200": {
            "description": "请求成功",
            "content": {
                "application/json": {
                    "schema": {"type": "object"}
                }
            }
        },
        "400": {
            "description": "请求参数错误",
            "content": {
                "application/json": {
                    "schema": {
                        "type": "object",
                        "properties": {
                            "detail": {"type": "string", "description": "错误详情"}
                        }
                    }
                }
            }
        },
        "404": {
            "description": "资源不存在",
            "content": {
                "application/json": {
                    "schema": {
                        "type": "object",
                        "properties": {
                            "detail": {"type": "string", "description": "错误详情"}
                        }
                    }
                }
            }
        },
        "500": {
            "description": "服务器内部错误",
            "content": {
                "application/json": {
                    "schema": {
                        "type": "object",
                        "properties": {
                            "code": {"type": "integer", "description": "错误代码"},
                            "message": {"type": "string", "description": "错误信息"},
                            "data": {"type": "object", "description": "错误数据"}
                        }
                    }
                }
            }
        }
    }

    app.openapi_schema = openapi_schema
    return app.openapi_schema

app.openapi = custom_openapi

# 配置CORS中间件 - 安全配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",  # React开发环境
        "http://localhost:8080",  # Vue开发环境
        "https://localhost:3000", # React HTTPS
        "https://localhost:8080", # Vue HTTPS
        "http://127.0.0.1:3000",  # 本地开发
        "https://127.0.0.1:3000", # 本地HTTPS
        "http://localhost:8000",  # API自身
        "https://localhost:8000", # API HTTPS
        # 生产环境域名请在此添加
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
    allow_headers=[
        "Authorization",
        "Content-Type",
        "Accept",
        "Origin",
        "User-Agent",
        "DNT",
        "Cache-Control",
        "X-Mx-ReqToken",
        "Keep-Alive",
        "X-Requested-With",
        "If-Modified-Since",
        "X-CSRF-Token"
    ],
    expose_headers=[
        "Content-Length",
        "Content-Range",
        "X-Content-Range"
    ]
)
# 添加安全中间件
app.add_middleware(SecurityHeadersMiddleware)
app.add_middleware(RateLimitMiddleware, calls=100, period=60)  # 每分钟100次请求



@app.middleware("http")
async def log_requests(request: Request, call_next):
    """记录所有请求的中间件"""
    start_time = logger.bind(request_id=id(request)).info(
        f"收到请求: {request.method} {request.url}"
    )

    response = await call_next(request)

    logger.bind(request_id=id(request)).info(
        f"请求完成: {request.method} {request.url} - 状态码: {response.status_code}"
    )

    return response


@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """全局异常处理器"""
    logger.error(f"全局异常: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "code": 500,
            "message": "服务器内部错误",
            "data": None
        }
    )


@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    logger.info("🚀 启动黄金珠宝管理系统 API")
    logger.info(f"📖 项目名称: {settings.PROJECT_NAME}")
    logger.info(f"🔢 版本号: {settings.PROJECT_VERSION}")
    logger.info(f"🔗 数据库: {settings.DATABASE_URL}")
    logger.info(f"📚 API文档: http://localhost:{settings.PORT}/docs")


@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    logger.info("⏹️ 关闭黄金珠宝管理系统 API")


# 根路径
@app.get("/", tags=["系统信息"], summary="系统欢迎页面", description="显示系统基本信息和可用服务")
async def root():
    """根路径欢迎信息"""
    return {
        "message": "欢迎使用黄金珠宝管理系统 API",
        "project": settings.PROJECT_NAME,
        "version": settings.PROJECT_VERSION,
        "docs": "/docs",
        "redoc": "/redoc"
    }


# 健康检查
@app.get("/health", tags=["系统信息"], summary="健康检查", description="检查API服务是否正常运行")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "project": settings.PROJECT_NAME,
        "version": settings.PROJECT_VERSION
    }


# 包含API路由
app.include_router(api_router, prefix=settings.API_V1_STR)


if __name__ == "__main__":
    # 创建日志目录
    os.makedirs("logs", exist_ok=True)

    # 检查是否有SSL证书
    ssl_keyfile = os.path.join(os.path.dirname(__file__), "ssl", "server.key")
    ssl_certfile = os.path.join(os.path.dirname(__file__), "ssl", "server.crt")
    use_ssl = os.path.exists(ssl_keyfile) and os.path.exists(ssl_certfile)

    logger.info(f"🔍 检查SSL证书: {ssl_keyfile} - {'存在' if os.path.exists(ssl_keyfile) else '不存在'}")
    logger.info(f"🔍 检查SSL证书: {ssl_certfile} - {'存在' if os.path.exists(ssl_certfile) else '不存在'}")

    if use_ssl:
        logger.info("🔒 启用HTTPS模式")
        # 启动HTTPS服务
        uvicorn.run(
            "main:app",
            host=settings.HOST,
            port=settings.PORT,
            reload=settings.DEBUG,
            log_level="info",
            ssl_keyfile=ssl_keyfile,
            ssl_certfile=ssl_certfile
        )
    else:
        logger.info("⚠️  HTTP模式启动 (生产环境建议使用HTTPS)")
        # 启动HTTP服务
        uvicorn.run(
            "main:app",
            host=settings.HOST,
            port=settings.PORT,
            reload=settings.DEBUG,
            log_level="info"
        )