import 'package:flutter/material.dart';

/// 定义屏幕尺寸信息
class SizingInformation {
  final Size screenSize;
  final Size localSize;
  final DeviceScreenType deviceScreenType;
  
  /// 是否是移动设备屏幕
  bool get isMobile => deviceScreenType == DeviceScreenType.mobile;
  
  /// 是否是平板设备屏幕
  bool get isTablet => deviceScreenType == DeviceScreenType.tablet;
  
  /// 是否是桌面设备屏幕
  bool get isDesktop => deviceScreenType == DeviceScreenType.desktop;

  SizingInformation({
    required this.screenSize,
    required this.localSize,
    required this.deviceScreenType,
  });

  @override
  String toString() {
    return 'DeviceType: $deviceScreenType, ScreenSize: $screenSize, LocalSize: $localSize';
  }
}

/// 设备屏幕类型枚举
enum DeviceScreenType {
  mobile,
  tablet,
  desktop,
}

/// 根据屏幕宽度确定设备类型
DeviceScreenType getDeviceType(Size size) {
  double deviceWidth = size.width;

  if (deviceWidth < 600) {
    return DeviceScreenType.mobile;
  }

  if (deviceWidth < 1000) {
    return DeviceScreenType.tablet;
  }

  return DeviceScreenType.desktop;
}

/// 响应式构建器
/// 根据不同屏幕尺寸提供适当的布局信息
class ResponsiveBuilder extends StatelessWidget {
  final Widget Function(
    BuildContext context,
    SizingInformation sizingInformation,
  ) builder;

  const ResponsiveBuilder({
    super.key,
    required this.builder,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(builder: (context, constraints) {
      var mediaQuery = MediaQuery.of(context);
      var sizingInformation = SizingInformation(
        deviceScreenType: getDeviceType(mediaQuery.size),
        screenSize: mediaQuery.size,
        localSize: Size(constraints.maxWidth, constraints.maxHeight),
      );
      return builder(context, sizingInformation);
    });
  }
}

/// 屏幕尺寸类型
enum ScreenType {
  /// 移动设备
  mobile,
  
  /// 平板设备
  tablet,
  
  /// 桌面设备
  desktop,
}

/// 响应式布局构建器
/// 根据屏幕尺寸返回不同的布局
class ScreenTypeLayout extends StatelessWidget {
  /// 移动设备布局
  final Widget mobile;
  
  /// 平板设备布局
  final Widget? tablet;
  
  /// 桌面设备布局
  final Widget? desktop;
  
  /// 移动设备断点
  final double mobileBreakpoint;
  
  /// 平板设备断点
  final double tabletBreakpoint;

  /// 构造函数
  const ScreenTypeLayout({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
    this.mobileBreakpoint = 600,
    this.tabletBreakpoint = 1000,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final maxWidth = constraints.maxWidth;
        
        // 根据屏幕宽度决定使用哪个布局
        if (maxWidth >= tabletBreakpoint) {
          // 桌面布局
          return desktop ?? tablet ?? mobile;
        } else if (maxWidth >= mobileBreakpoint) {
          // 平板布局
          return tablet ?? mobile;
        } else {
          // 移动设备布局
          return mobile;
        }
      },
    );
  }
}

/// 获取当前屏幕类型
ScreenType getScreenType(BuildContext context, {
  double mobileBreakpoint = 600,
  double tabletBreakpoint = 1000,
}) {
  final width = MediaQuery.of(context).size.width;
  
  if (width >= tabletBreakpoint) {
    return ScreenType.desktop;
  } else if (width >= mobileBreakpoint) {
    return ScreenType.tablet;
  } else {
    return ScreenType.mobile;
  }
}

/// 判断是否为移动设备
bool isMobile(BuildContext context) => 
    getScreenType(context) == ScreenType.mobile;

/// 判断是否为平板设备
bool isTablet(BuildContext context) => 
    getScreenType(context) == ScreenType.tablet;

/// 判断是否为桌面设备
bool isDesktop(BuildContext context) => 
    getScreenType(context) == ScreenType.desktop; 