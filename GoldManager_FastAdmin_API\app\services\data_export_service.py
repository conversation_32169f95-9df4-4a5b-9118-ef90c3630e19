"""
数据导入导出服务类
"""

import os
import pandas as pd
from typing import List, Dict, Any, Optional, Union
from datetime import datetime, date
from sqlalchemy.orm import Session
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment
from openpyxl.utils.dataframe import dataframe_to_rows
import aiofiles
import json

from app.models.jewelry import Jewelry, JewelryCategory
from app.models.store import Store
from app.models.member import Member
from app.models.admin import Admin
from app.models.stock_in import StockIn, StockInItem
from app.models.stock_out import StockOut, StockOutItem
from app.models.stock_return import StockReturn, StockReturnItem
from app.models.inventory_check import InventoryCheck, InventoryCheckItem
from app.models.recycling import Recycling, RecyclingItem
from app.models.store_transfer import StoreTransfer, StoreTransferItem

from app.schemas.data_export import (
    ExportRequest, ExportResponse, ImportRequest, ImportResponse,
    TemplateRequest, TemplateResponse, ExportType, ImportType, ExportFormat,
    ImportValidationError
)


class DataExportService:
    """数据导入导出服务类"""

    def __init__(self, db: Session):
        self.db = db
        self.export_dir = "exports"
        self.template_dir = "templates"

        # 确保导出目录存在
        os.makedirs(self.export_dir, exist_ok=True)
        os.makedirs(self.template_dir, exist_ok=True)

    async def export_data(self, request: ExportRequest, operator_id: int) -> ExportResponse:
        """导出数据"""
        try:
            # 根据导出类型获取数据
            data = await self._get_export_data(request)

            if not data:
                return ExportResponse(
                    success=False,
                    message="没有找到符合条件的数据"
                )

            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_name = f"{request.export_type.value}_{timestamp}.{request.export_format.value}"
            file_path = os.path.join(self.export_dir, file_name)

            # 根据格式导出文件
            if request.export_format == ExportFormat.EXCEL:
                await self._export_to_excel(data, file_path, request.export_type)
            elif request.export_format == ExportFormat.CSV:
                await self._export_to_csv(data, file_path)
            else:
                return ExportResponse(
                    success=False,
                    message=f"不支持的导出格式: {request.export_format}"
                )

            # 获取文件大小
            file_size = os.path.getsize(file_path)

            # 记录导出历史
            await self._save_export_history(
                export_type=request.export_type.value,
                export_format=request.export_format.value,
                file_name=file_name,
                file_size=file_size,
                record_count=len(data),
                operator_id=operator_id
            )

            return ExportResponse(
                success=True,
                message="数据导出成功",
                download_url=f"/api/v1/export/download/{file_name}",
                file_name=file_name,
                file_size=file_size,
                record_count=len(data)
            )

        except Exception as e:
            return ExportResponse(
                success=False,
                message=f"导出失败: {str(e)}"
            )

    async def _get_export_data(self, request: ExportRequest) -> List[Dict[str, Any]]:
        """根据导出类型获取数据"""
        query_filters = self._build_query_filters(request)

        if request.export_type == ExportType.JEWELRY:
            return await self._get_jewelry_data(query_filters)
        elif request.export_type == ExportType.STORE:
            return await self._get_store_data(query_filters)
        elif request.export_type == ExportType.MEMBER:
            return await self._get_member_data(query_filters)
        elif request.export_type == ExportType.ADMIN:
            return await self._get_admin_data(query_filters)
        elif request.export_type == ExportType.STOCK_IN:
            return await self._get_stock_in_data(query_filters)
        elif request.export_type == ExportType.STOCK_OUT:
            return await self._get_stock_out_data(query_filters)
        elif request.export_type == ExportType.STOCK_RETURN:
            return await self._get_stock_return_data(query_filters)
        elif request.export_type == ExportType.INVENTORY_CHECK:
            return await self._get_inventory_check_data(query_filters)
        elif request.export_type == ExportType.RECYCLING:
            return await self._get_recycling_data(query_filters)
        elif request.export_type == ExportType.STORE_TRANSFER:
            return await self._get_store_transfer_data(query_filters)
        else:
            return []

    def _build_query_filters(self, request: ExportRequest) -> Dict[str, Any]:
        """构建查询过滤条件"""
        filters = {}

        if request.store_id:
            filters['store_id'] = request.store_id
        if request.category_id:
            filters['category_id'] = request.category_id
        if request.status is not None:
            filters['status'] = request.status
        if request.start_date:
            filters['start_date'] = request.start_date
        if request.end_date:
            filters['end_date'] = request.end_date
        if request.keyword:
            filters['keyword'] = request.keyword
        if request.operator_id:
            filters['operator_id'] = request.operator_id

        return filters

    async def _get_jewelry_data(self, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """获取商品数据"""
        query = self.db.query(Jewelry).join(JewelryCategory).join(Store)

        # 应用过滤条件
        if 'store_id' in filters:
            query = query.filter(Jewelry.store_id == filters['store_id'])
        if 'category_id' in filters:
            query = query.filter(Jewelry.category_id == filters['category_id'])
        if 'status' in filters:
            query = query.filter(Jewelry.status == filters['status'])
        if 'keyword' in filters:
            keyword = f"%{filters['keyword']}%"
            query = query.filter(
                (Jewelry.name.like(keyword)) |
                (Jewelry.barcode.like(keyword))
            )

        jewelry_list = query.all()

        data = []
        for jewelry in jewelry_list:
            data.append({
                'ID': jewelry.id,
                '条码': jewelry.barcode,
                '商品名称': jewelry.name,
                '分类': jewelry.category.name if jewelry.category else '',
                '门店': jewelry.store.name if jewelry.store else '',
                '圈口号': jewelry.ring_size or '',
                '金重(克)': float(jewelry.gold_weight),
                '金进价(元/克)': float(jewelry.gold_price),
                '金成本(元)': float(jewelry.gold_cost),
                '银重(克)': float(jewelry.silver_weight),
                '总重(克)': float(jewelry.total_weight),
                '银进价(元/克)': float(jewelry.silver_price),
                '银成本(元)': float(jewelry.silver_cost),
                '银工费方式': '按克' if jewelry.silver_work_type == 0 else '按件',
                '银工费': float(jewelry.silver_work_price),
                '电铸费(元)': float(jewelry.plating_cost),
                '总成本(元)': float(jewelry.total_cost),
                '批发工费(元)': float(jewelry.wholesale_work_price),
                '零售工费(元)': float(jewelry.retail_work_price),
                '件工费(元)': float(jewelry.piece_work_price),
                '状态': '上架' if jewelry.status == 1 else '下架' if jewelry.status == 0 else '待出库',
                '创建时间': datetime.fromtimestamp(jewelry.createtime).strftime('%Y-%m-%d %H:%M:%S') if jewelry.createtime else '',
                '更新时间': datetime.fromtimestamp(jewelry.updatetime).strftime('%Y-%m-%d %H:%M:%S') if jewelry.updatetime else ''
            })

        return data

    async def _get_store_data(self, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """获取门店数据"""
        query = self.db.query(Store)

        # 应用过滤条件
        if 'status' in filters:
            query = query.filter(Store.status == filters['status'])
        if 'keyword' in filters:
            keyword = f"%{filters['keyword']}%"
            query = query.filter(Store.name.like(keyword))

        store_list = query.all()

        data = []
        for store in store_list:
            data.append({
                'ID': store.id,
                '门店名称': store.name,
                '门店地址': store.address or '',
                '联系电话': store.phone or '',
                '状态': '正常' if store.status == 1 else '关闭',
                '创建时间': datetime.fromtimestamp(store.createtime).strftime('%Y-%m-%d %H:%M:%S') if store.createtime else '',
                '更新时间': datetime.fromtimestamp(store.updatetime).strftime('%Y-%m-%d %H:%M:%S') if store.updatetime else ''
            })

        return data

    async def _get_member_data(self, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """获取会员数据"""
        query = self.db.query(Member)

        # 应用过滤条件
        if 'status' in filters:
            query = query.filter(Member.status == filters['status'])
        if 'keyword' in filters:
            keyword = f"%{filters['keyword']}%"
            query = query.filter(
                (Member.name.like(keyword)) |
                (Member.card_no.like(keyword)) |
                (Member.phone.like(keyword))
            )

        member_list = query.all()

        data = []
        for member in member_list:
            # 计算年龄
            age = ''
            if member.birthday:
                today = date.today()
                age = today.year - member.birthday.year - ((today.month, today.day) < (member.birthday.month, member.birthday.day))

            # 会员等级
            level_names = {1: '普通会员', 2: '银卡会员', 3: '金卡会员', 4: '白金会员', 5: '钻石会员'}

            data.append({
                'ID': member.id,
                '会员卡号': member.card_no,
                '会员姓名': member.name,
                '联系电话': member.phone or '',
                '生日': member.birthday.strftime('%Y-%m-%d') if member.birthday else '',
                '年龄': age,
                '积分': member.points,
                '会员等级': level_names.get(member.level, '普通会员'),
                '状态': '正常' if member.status == 1 else '禁用',
                '创建时间': datetime.fromtimestamp(member.createtime).strftime('%Y-%m-%d %H:%M:%S') if member.createtime else '',
                '更新时间': datetime.fromtimestamp(member.updatetime).strftime('%Y-%m-%d %H:%M:%S') if member.updatetime else ''
            })

        return data

    async def _get_admin_data(self, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """获取管理员数据"""
        query = self.db.query(Admin).join(Store, Admin.store_id == Store.id, isouter=True)

        # 应用过滤条件
        if 'status' in filters:
            query = query.filter(Admin.status == filters['status'])
        if 'store_id' in filters:
            query = query.filter(Admin.store_id == filters['store_id'])
        if 'keyword' in filters:
            keyword = f"%{filters['keyword']}%"
            query = query.filter(
                (Admin.username.like(keyword)) |
                (Admin.nickname.like(keyword)) |
                (Admin.email.like(keyword))
            )

        admin_list = query.all()

        data = []
        for admin in admin_list:
            data.append({
                'ID': admin.id,
                '用户名': admin.username,
                '昵称': admin.nickname or '',
                '邮箱': admin.email or '',
                '手机号': admin.mobile or '',
                '所属门店': admin.store.name if admin.store else '',
                '状态': admin.status,
                '登录失败次数': admin.loginfailure,
                '最后登录时间': datetime.fromtimestamp(admin.logintime).strftime('%Y-%m-%d %H:%M:%S') if admin.logintime else '',
                '最后登录IP': admin.loginip or '',
                '创建时间': datetime.fromtimestamp(admin.createtime).strftime('%Y-%m-%d %H:%M:%S') if admin.createtime else '',
                '更新时间': datetime.fromtimestamp(admin.updatetime).strftime('%Y-%m-%d %H:%M:%S') if admin.updatetime else ''
            })

        return data

    async def _get_stock_in_data(self, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """获取入库数据"""
        query = self.db.query(StockIn).join(Store).join(Admin, StockIn.operator_id == Admin.id)

        # 应用过滤条件
        if 'store_id' in filters:
            query = query.filter(StockIn.store_id == filters['store_id'])
        if 'status' in filters:
            query = query.filter(StockIn.status == filters['status'])
        if 'operator_id' in filters:
            query = query.filter(StockIn.operator_id == filters['operator_id'])
        if 'start_date' in filters:
            start_timestamp = int(datetime.combine(filters['start_date'], datetime.min.time()).timestamp())
            query = query.filter(StockIn.createtime >= start_timestamp)
        if 'end_date' in filters:
            end_timestamp = int(datetime.combine(filters['end_date'], datetime.max.time()).timestamp())
            query = query.filter(StockIn.createtime <= end_timestamp)
        if 'keyword' in filters:
            keyword = f"%{filters['keyword']}%"
            query = query.filter(
                (StockIn.order_no.like(keyword)) |
                (StockIn.supplier.like(keyword))
            )

        stock_in_list = query.all()

        data = []
        for stock_in in stock_in_list:
            # 获取审核人信息
            auditor_name = ''
            if stock_in.audit_user_id:
                auditor = self.db.query(Admin).filter(Admin.id == stock_in.audit_user_id).first()
                if auditor:
                    auditor_name = auditor.nickname or auditor.username

            data.append({
                'ID': stock_in.id,
                '入库单号': stock_in.order_no,
                '入库门店': stock_in.store.name,
                '供应商': stock_in.supplier or '',
                '总金额(元)': float(stock_in.total_amount),
                '备注': stock_in.remark or '',
                '操作员': stock_in.operator.nickname or stock_in.operator.username,
                '审核人': auditor_name,
                '审核时间': datetime.fromtimestamp(stock_in.audit_time).strftime('%Y-%m-%d %H:%M:%S') if stock_in.audit_time else '',
                '审核备注': stock_in.audit_note or '',
                '状态': '草稿' if stock_in.status == 0 else '已入库',
                '创建时间': datetime.fromtimestamp(stock_in.createtime).strftime('%Y-%m-%d %H:%M:%S') if stock_in.createtime else '',
                '更新时间': datetime.fromtimestamp(stock_in.updatetime).strftime('%Y-%m-%d %H:%M:%S') if stock_in.updatetime else ''
            })

        return data

    async def _get_stock_out_data(self, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """获取出库数据"""
        query = self.db.query(StockOut).join(Store).join(Admin, StockOut.operator_id == Admin.id)

        # 应用过滤条件
        if 'store_id' in filters:
            query = query.filter(StockOut.store_id == filters['store_id'])
        if 'status' in filters:
            query = query.filter(StockOut.status == filters['status'])
        if 'operator_id' in filters:
            query = query.filter(StockOut.operator_id == filters['operator_id'])
        if 'start_date' in filters:
            start_timestamp = int(datetime.combine(filters['start_date'], datetime.min.time()).timestamp())
            query = query.filter(StockOut.createtime >= start_timestamp)
        if 'end_date' in filters:
            end_timestamp = int(datetime.combine(filters['end_date'], datetime.max.time()).timestamp())
            query = query.filter(StockOut.createtime <= end_timestamp)
        if 'keyword' in filters:
            keyword = f"%{filters['keyword']}%"
            query = query.filter(
                (StockOut.order_no.like(keyword)) |
                (StockOut.customer.like(keyword))
            )

        stock_out_list = query.all()

        data = []
        for stock_out in stock_out_list:
            # 获取审核人信息
            auditor_name = ''
            if stock_out.audit_user_id:
                auditor = self.db.query(Admin).filter(Admin.id == stock_out.audit_user_id).first()
                if auditor:
                    auditor_name = auditor.nickname or auditor.username

            # 状态映射
            status_map = {1: '待审核', 2: '已通过', 3: '未通过', 4: '已作废'}

            data.append({
                'ID': stock_out.id,
                '出库单号': stock_out.order_no,
                '出库门店': stock_out.store.name,
                '客户': stock_out.customer or '',
                '总重量(克)': float(stock_out.total_weight),
                '销售类型': '零售' if stock_out.sale_type == 'retail' else '批发',
                '总金额(元)': float(stock_out.total_amount),
                '操作员': stock_out.operator.nickname or stock_out.operator.username,
                '备注': stock_out.remark or '',
                '状态': status_map.get(stock_out.status, '未知'),
                '审核人': auditor_name,
                '审核时间': datetime.fromtimestamp(stock_out.audit_time).strftime('%Y-%m-%d %H:%M:%S') if stock_out.audit_time else '',
                '审核备注': stock_out.audit_remark or '',
                '收款状态': '已收款' if stock_out.payment_status == 1 else '未收款',
                '收款时间': datetime.fromtimestamp(stock_out.payment_time).strftime('%Y-%m-%d %H:%M:%S') if stock_out.payment_time else '',
                '现金金额(元)': float(stock_out.cash_amount),
                '微信金额(元)': float(stock_out.wechat_amount),
                '支付宝金额(元)': float(stock_out.alipay_amount),
                '刷卡金额(元)': float(stock_out.card_amount),
                '抹零金额(元)': float(stock_out.discount_amount),
                '实收金额(元)': float(stock_out.actual_amount),
                '创建时间': datetime.fromtimestamp(stock_out.createtime).strftime('%Y-%m-%d %H:%M:%S') if stock_out.createtime else '',
                '更新时间': datetime.fromtimestamp(stock_out.updatetime).strftime('%Y-%m-%d %H:%M:%S') if stock_out.updatetime else ''
            })

        return data

    async def _get_stock_return_data(self, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """获取退货数据"""
        query = self.db.query(StockReturn).join(Store).join(Admin, StockReturn.operator_id == Admin.id)

        # 应用过滤条件
        if 'store_id' in filters:
            query = query.filter(StockReturn.store_id == filters['store_id'])
        if 'status' in filters:
            query = query.filter(StockReturn.status == filters['status'])
        if 'operator_id' in filters:
            query = query.filter(StockReturn.operator_id == filters['operator_id'])
        if 'start_date' in filters:
            start_timestamp = int(datetime.combine(filters['start_date'], datetime.min.time()).timestamp())
            query = query.filter(StockReturn.createtime >= start_timestamp)
        if 'end_date' in filters:
            end_timestamp = int(datetime.combine(filters['end_date'], datetime.max.time()).timestamp())
            query = query.filter(StockReturn.createtime <= end_timestamp)
        if 'keyword' in filters:
            keyword = f"%{filters['keyword']}%"
            query = query.filter(
                (StockReturn.order_no.like(keyword)) |
                (StockReturn.customer.like(keyword))
            )

        stock_return_list = query.all()

        data = []
        for stock_return in stock_return_list:
            # 获取审核人信息
            auditor_name = ''
            if stock_return.audit_user_id:
                auditor = self.db.query(Admin).filter(Admin.id == stock_return.audit_user_id).first()
                if auditor:
                    auditor_name = auditor.nickname or auditor.username

            # 状态映射
            status_map = {1: '待审核', 2: '已通过', 3: '未通过', 4: '已作废'}

            data.append({
                'ID': stock_return.id,
                '退货单号': stock_return.order_no,
                '退货门店': stock_return.store.name,
                '客户': stock_return.customer or '',
                '应收金额(元)': float(stock_return.total_amount),
                '优惠金额(元)': float(stock_return.discount_amount),
                '实际退款(元)': float(stock_return.actual_amount),
                '备注': stock_return.remark or '',
                '操作员': stock_return.operator.nickname or stock_return.operator.username,
                '状态': status_map.get(stock_return.status, '未知'),
                '审核人': auditor_name,
                '审核时间': datetime.fromtimestamp(stock_return.audit_time).strftime('%Y-%m-%d %H:%M:%S') if stock_return.audit_time else '',
                '审核备注': stock_return.audit_remark or '',
                '创建时间': datetime.fromtimestamp(stock_return.createtime).strftime('%Y-%m-%d %H:%M:%S') if stock_return.createtime else '',
                '更新时间': datetime.fromtimestamp(stock_return.updatetime).strftime('%Y-%m-%d %H:%M:%S') if stock_return.updatetime else ''
            })

        return data

    async def _get_inventory_check_data(self, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """获取库存盘点数据"""
        query = self.db.query(InventoryCheck).join(Store).join(Admin, InventoryCheck.operator_id == Admin.id)

        # 应用过滤条件
        if 'store_id' in filters:
            query = query.filter(InventoryCheck.store_id == filters['store_id'])
        if 'status' in filters:
            query = query.filter(InventoryCheck.status == filters['status'])
        if 'operator_id' in filters:
            query = query.filter(InventoryCheck.operator_id == filters['operator_id'])
        if 'start_date' in filters:
            start_timestamp = int(datetime.combine(filters['start_date'], datetime.min.time()).timestamp())
            query = query.filter(InventoryCheck.createtime >= start_timestamp)
        if 'end_date' in filters:
            end_timestamp = int(datetime.combine(filters['end_date'], datetime.max.time()).timestamp())
            query = query.filter(InventoryCheck.createtime <= end_timestamp)
        if 'keyword' in filters:
            keyword = f"%{filters['keyword']}%"
            query = query.filter(InventoryCheck.check_no.like(keyword))

        inventory_check_list = query.all()

        data = []
        for inventory_check in inventory_check_list:
            # 状态映射
            status_map = {0: '进行中', 1: '已完成', 2: '已取消'}

            # 计算进度
            progress = 0
            if inventory_check.total_count > 0:
                progress = round((inventory_check.checked_count / inventory_check.total_count) * 100, 2)

            data.append({
                'ID': inventory_check.id,
                '盘点单号': inventory_check.check_no,
                '盘点门店': inventory_check.store.name,
                '状态': status_map.get(inventory_check.status, '未知'),
                '开始时间': datetime.fromtimestamp(inventory_check.start_time).strftime('%Y-%m-%d %H:%M:%S') if inventory_check.start_time else '',
                '结束时间': datetime.fromtimestamp(inventory_check.end_time).strftime('%Y-%m-%d %H:%M:%S') if inventory_check.end_time else '',
                '操作员': inventory_check.operator.nickname or inventory_check.operator.username,
                '应盘总数': inventory_check.total_count,
                '已盘数量': inventory_check.checked_count,
                '差异数量': inventory_check.difference_count,
                '盘点进度(%)': progress,
                '备注': inventory_check.remark or '',
                '创建时间': datetime.fromtimestamp(inventory_check.createtime).strftime('%Y-%m-%d %H:%M:%S') if inventory_check.createtime else '',
                '更新时间': datetime.fromtimestamp(inventory_check.updatetime).strftime('%Y-%m-%d %H:%M:%S') if inventory_check.updatetime else ''
            })

        return data

    async def _get_recycling_data(self, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """获取回收数据"""
        query = self.db.query(Recycling).join(Store).join(Admin, Recycling.operator_id == Admin.id)

        # 应用过滤条件
        if 'store_id' in filters:
            query = query.filter(Recycling.store_id == filters['store_id'])
        if 'status' in filters:
            query = query.filter(Recycling.status == filters['status'])
        if 'operator_id' in filters:
            query = query.filter(Recycling.operator_id == filters['operator_id'])
        if 'start_date' in filters:
            start_timestamp = int(datetime.combine(filters['start_date'], datetime.min.time()).timestamp())
            query = query.filter(Recycling.createtime >= start_timestamp)
        if 'end_date' in filters:
            end_timestamp = int(datetime.combine(filters['end_date'], datetime.max.time()).timestamp())
            query = query.filter(Recycling.createtime <= end_timestamp)
        if 'keyword' in filters:
            keyword = f"%{filters['keyword']}%"
            query = query.filter(
                (Recycling.recycle_no.like(keyword)) |
                (Recycling.customer_name.like(keyword))
            )

        recycling_list = query.all()

        data = []
        for recycling in recycling_list:
            # 获取会员信息
            member_name = ''
            if recycling.member_id:
                member = self.db.query(Member).filter(Member.id == recycling.member_id).first()
                if member:
                    member_name = member.name

            data.append({
                'ID': recycling.id,
                '回收单号': recycling.recycle_no,
                '门店': recycling.store.name,
                '会员': member_name,
                '客户姓名': recycling.customer_name or '',
                '联系电话': recycling.phone or '',
                '金重(克)': float(recycling.gold_weight),
                '金价(元/克)': float(recycling.gold_price),
                '金价金额(元)': float(recycling.gold_amount),
                '银重(克)': float(recycling.silver_weight),
                '银价(元/克)': float(recycling.silver_price),
                '银价金额(元)': float(recycling.silver_amount),
                '回收价格(元)': float(recycling.price),
                '总金额(元)': float(recycling.total_amount),
                '折后总金额(元)': float(recycling.discount_amount),
                '操作员': recycling.operator.nickname or recycling.operator.username,
                '状态': '正常' if recycling.status == 1 else '作废',
                '备注': recycling.remark or '',
                '创建时间': datetime.fromtimestamp(recycling.createtime).strftime('%Y-%m-%d %H:%M:%S') if recycling.createtime else '',
                '更新时间': datetime.fromtimestamp(recycling.updatetime).strftime('%Y-%m-%d %H:%M:%S') if recycling.updatetime else ''
            })

        return data

    async def _get_store_transfer_data(self, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """获取门店调拨数据"""
        query = self.db.query(StoreTransfer).join(Store, StoreTransfer.from_store_id == Store.id).join(Admin, StoreTransfer.admin_id == Admin.id)

        # 应用过滤条件
        if 'store_id' in filters:
            query = query.filter(
                (StoreTransfer.from_store_id == filters['store_id']) |
                (StoreTransfer.to_store_id == filters['store_id'])
            )
        if 'status' in filters:
            query = query.filter(StoreTransfer.status == filters['status'])
        if 'operator_id' in filters:
            query = query.filter(StoreTransfer.admin_id == filters['operator_id'])
        if 'start_date' in filters:
            start_timestamp = int(datetime.combine(filters['start_date'], datetime.min.time()).timestamp())
            query = query.filter(StoreTransfer.createtime >= start_timestamp)
        if 'end_date' in filters:
            end_timestamp = int(datetime.combine(filters['end_date'], datetime.max.time()).timestamp())
            query = query.filter(StoreTransfer.createtime <= end_timestamp)
        if 'keyword' in filters:
            keyword = f"%{filters['keyword']}%"
            query = query.filter(StoreTransfer.transfer_no.like(keyword))

        store_transfer_list = query.all()

        data = []
        for store_transfer in store_transfer_list:
            # 获取目标门店信息
            to_store = self.db.query(Store).filter(Store.id == store_transfer.to_store_id).first()
            to_store_name = to_store.name if to_store else ''

            # 获取审核人信息
            auditor_name = ''
            if store_transfer.audit_id:
                auditor = self.db.query(Admin).filter(Admin.id == store_transfer.audit_id).first()
                if auditor:
                    auditor_name = auditor.nickname or auditor.username

            # 状态映射
            status_map = {0: '待审核', 1: '已通过', 2: '已拒绝'}

            data.append({
                'ID': store_transfer.id,
                '调拨单号': store_transfer.transfer_no,
                '源店铺': store_transfer.from_store.name,
                '目标店铺': to_store_name,
                '操作员': store_transfer.admin.nickname or store_transfer.admin.username,
                '状态': status_map.get(store_transfer.status, '未知'),
                '审核员': auditor_name,
                '审核时间': datetime.fromtimestamp(store_transfer.audit_time).strftime('%Y-%m-%d %H:%M:%S') if store_transfer.audit_time else '',
                '备注': store_transfer.remark or '',
                '创建时间': datetime.fromtimestamp(store_transfer.createtime).strftime('%Y-%m-%d %H:%M:%S') if store_transfer.createtime else ''
            })

        return data

    async def _export_to_excel(self, data: List[Dict[str, Any]], file_path: str, export_type: ExportType):
        """导出到Excel文件"""
        # 创建工作簿
        wb = Workbook()
        ws = wb.active

        # 设置工作表名称
        type_names = {
            ExportType.JEWELRY: "商品数据",
            ExportType.STORE: "门店数据",
            ExportType.MEMBER: "会员数据",
            ExportType.ADMIN: "管理员数据",
            ExportType.STOCK_IN: "入库数据",
            ExportType.STOCK_OUT: "出库数据",
            ExportType.STOCK_RETURN: "退货数据",
            ExportType.INVENTORY_CHECK: "盘点数据",
            ExportType.RECYCLING: "回收数据",
            ExportType.STORE_TRANSFER: "调拨数据"
        }
        ws.title = type_names.get(export_type, "数据导出")

        if not data:
            # 如果没有数据，只写入表头
            ws.append(["暂无数据"])
            wb.save(file_path)
            return

        # 写入表头
        headers = list(data[0].keys())
        ws.append(headers)

        # 设置表头样式
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        header_alignment = Alignment(horizontal="center", vertical="center")

        for col_num, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col_num)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment

        # 写入数据
        for row_data in data:
            ws.append(list(row_data.values()))

        # 自动调整列宽
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)  # 最大宽度限制为50
            ws.column_dimensions[column_letter].width = adjusted_width

        # 保存文件
        wb.save(file_path)

    async def _export_to_csv(self, data: List[Dict[str, Any]], file_path: str):
        """导出到CSV文件"""
        if not data:
            # 如果没有数据，创建空文件
            async with aiofiles.open(file_path, 'w', encoding='utf-8-sig') as f:
                await f.write("暂无数据\n")
            return

        # 使用pandas导出CSV
        df = pd.DataFrame(data)
        df.to_csv(file_path, index=False, encoding='utf-8-sig')

    async def _save_export_history(self, export_type: str, export_format: str, file_name: str,
                                 file_size: int, record_count: int, operator_id: int):
        """保存导出历史记录"""
        # 这里可以保存到数据库，暂时跳过
        # 可以创建一个export_history表来记录导出历史
        pass

    async def generate_template(self, request: TemplateRequest) -> TemplateResponse:
        """生成导入模板"""
        try:
            # 根据模板类型生成对应的模板
            template_data = self._get_template_data(request.template_type, request.include_sample_data)

            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_name = f"{request.template_type.value}_template_{timestamp}.xlsx"
            file_path = os.path.join(self.template_dir, file_name)

            # 创建Excel模板
            await self._create_excel_template(template_data, file_path, request.template_type)

            return TemplateResponse(
                success=True,
                message="模板生成成功",
                download_url=f"/api/v1/export/template/{file_name}",
                file_name=file_name
            )

        except Exception as e:
            return TemplateResponse(
                success=False,
                message=f"模板生成失败: {str(e)}",
                download_url="",
                file_name=""
            )

    def _get_template_data(self, template_type: ImportType, include_sample: bool) -> Dict[str, Any]:
        """获取模板数据结构"""
        templates = {
            ImportType.JEWELRY: {
                "headers": [
                    "条码", "商品名称", "分类ID", "圈口号", "金重(克)", "金进价(元/克)",
                    "银重(克)", "总重(克)", "银进价(元/克)", "银工费方式", "银工费",
                    "电铸费(元)", "批发工费(元)", "零售工费(元)", "件工费(元)", "门店ID"
                ],
                "sample_data": [
                    ["JW001", "黄金戒指", "1", "15", "5.2", "450", "0", "5.2", "0", "按克", "50", "0", "100", "150", "80", "1"],
                    ["JW002", "银手镯", "2", "", "0", "0", "25.8", "25.8", "8", "按克", "12", "0", "50", "80", "60", "1"]
                ] if include_sample else []
            },
            ImportType.MEMBER: {
                "headers": [
                    "会员卡号", "会员姓名", "联系电话", "生日", "积分", "会员等级"
                ],
                "sample_data": [
                    ["VIP001", "张三", "13800138000", "1990-01-01", "1000", "2"],
                    ["VIP002", "李四", "13900139000", "1985-05-15", "2500", "3"]
                ] if include_sample else []
            },
            ImportType.STOCK_IN: {
                "headers": [
                    "门店ID", "供应商", "备注", "商品条码", "商品名称", "分类ID",
                    "圈口号", "金重(克)", "金进价(元/克)", "银重(克)", "总重(克)",
                    "银进价(元/克)", "银工费方式", "银工费", "电铸费(元)",
                    "批发工费(元)", "零售工费(元)", "件工费(元)"
                ],
                "sample_data": [
                    ["1", "黄金供应商", "批量入库", "JW001", "黄金戒指", "1", "15", "5.2", "450", "0", "5.2", "0", "按克", "50", "0", "100", "150", "80"],
                    ["1", "银饰供应商", "新款入库", "JW002", "银手镯", "2", "", "0", "0", "25.8", "25.8", "8", "按克", "12", "0", "50", "80", "60"]
                ] if include_sample else []
            }
        }

        return templates.get(template_type, {"headers": [], "sample_data": []})

    async def _create_excel_template(self, template_data: Dict[str, Any], file_path: str, template_type: ImportType):
        """创建Excel模板文件"""
        wb = Workbook()
        ws = wb.active

        # 设置工作表名称
        type_names = {
            ImportType.JEWELRY: "商品导入模板",
            ImportType.MEMBER: "会员导入模板",
            ImportType.STOCK_IN: "入库导入模板"
        }
        ws.title = type_names.get(template_type, "导入模板")

        # 写入表头
        headers = template_data.get("headers", [])
        if headers:
            ws.append(headers)

            # 设置表头样式
            header_font = Font(bold=True, color="FFFFFF")
            header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            header_alignment = Alignment(horizontal="center", vertical="center")

            for col_num in range(1, len(headers) + 1):
                cell = ws.cell(row=1, column=col_num)
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = header_alignment

        # 写入示例数据
        sample_data = template_data.get("sample_data", [])
        for row_data in sample_data:
            ws.append(row_data)

        # 自动调整列宽
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 30)  # 模板列宽限制为30
            ws.column_dimensions[column_letter].width = adjusted_width

        # 保存文件
        wb.save(file_path)

    async def import_data(self, request: ImportRequest, file_path: str) -> ImportResponse:
        """导入数据"""
        try:
            # 读取Excel文件
            df = pd.read_excel(file_path)

            if df.empty:
                return ImportResponse(
                    success=False,
                    message="文件为空或格式错误",
                    total_rows=0,
                    success_count=0,
                    error_count=0,
                    skip_count=0
                )

            total_rows = len(df)
            success_count = 0
            error_count = 0
            skip_count = 0
            errors = []

            # 根据导入类型处理数据
            if request.import_type == ImportType.JEWELRY:
                success_count, error_count, skip_count, errors = await self._import_jewelry_data(
                    df, request, errors
                )
            elif request.import_type == ImportType.MEMBER:
                success_count, error_count, skip_count, errors = await self._import_member_data(
                    df, request, errors
                )
            elif request.import_type == ImportType.STOCK_IN:
                success_count, error_count, skip_count, errors = await self._import_stock_in_data(
                    df, request, errors
                )
            else:
                return ImportResponse(
                    success=False,
                    message=f"不支持的导入类型: {request.import_type}",
                    total_rows=total_rows,
                    success_count=0,
                    error_count=total_rows,
                    skip_count=0,
                    errors=errors
                )

            # 如果只是验证，不提交事务
            if request.validate_only:
                self.db.rollback()
                message = "数据验证完成"
            else:
                self.db.commit()
                message = "数据导入完成"

            return ImportResponse(
                success=True,
                message=message,
                total_rows=total_rows,
                success_count=success_count,
                error_count=error_count,
                skip_count=skip_count,
                errors=errors
            )

        except Exception as e:
            self.db.rollback()
            return ImportResponse(
                success=False,
                message=f"导入失败: {str(e)}",
                total_rows=0,
                success_count=0,
                error_count=0,
                skip_count=0,
                errors=[]
            )

    async def _import_jewelry_data(self, df: pd.DataFrame, request: ImportRequest, errors: List) -> tuple:
        """导入商品数据"""
        success_count = 0
        error_count = 0
        skip_count = 0

        required_columns = ["条码", "商品名称", "分类ID", "门店ID"]

        # 检查必需列
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            errors.append({
                "row": 0,
                "column": "表头",
                "value": "",
                "error": f"缺少必需列: {', '.join(missing_columns)}"
            })
            return success_count, len(df), skip_count, errors

        for index, row in df.iterrows():
            try:
                row_num = index + 2  # Excel行号从2开始（第1行是表头）

                # 验证必需字段
                if pd.isna(row["条码"]) or str(row["条码"]).strip() == "":
                    errors.append({
                        "row": row_num,
                        "column": "条码",
                        "value": row["条码"],
                        "error": "条码不能为空"
                    })
                    error_count += 1
                    continue

                if pd.isna(row["商品名称"]) or str(row["商品名称"]).strip() == "":
                    errors.append({
                        "row": row_num,
                        "column": "商品名称",
                        "value": row["商品名称"],
                        "error": "商品名称不能为空"
                    })
                    error_count += 1
                    continue

                # 检查条码是否已存在
                barcode = str(row["条码"]).strip()
                existing_jewelry = self.db.query(Jewelry).filter(Jewelry.barcode == barcode).first()

                if existing_jewelry:
                    if request.skip_duplicates:
                        skip_count += 1
                        continue
                    elif request.update_existing:
                        # 更新现有商品
                        self._update_jewelry_from_row(existing_jewelry, row)
                        success_count += 1
                        continue
                    else:
                        errors.append({
                            "row": row_num,
                            "column": "条码",
                            "value": barcode,
                            "error": "条码已存在"
                        })
                        error_count += 1
                        continue

                # 创建新商品
                jewelry = self._create_jewelry_from_row(row, request.store_id)
                self.db.add(jewelry)
                success_count += 1

            except Exception as e:
                errors.append({
                    "row": row_num,
                    "column": "数据处理",
                    "value": "",
                    "error": str(e)
                })
                error_count += 1

        return success_count, error_count, skip_count, errors

    def _create_jewelry_from_row(self, row: pd.Series, default_store_id: int) -> Jewelry:
        """从行数据创建商品对象"""
        from datetime import datetime
        import time

        # 处理数值字段，确保不是NaN
        def safe_float(value, default=0.0):
            try:
                return float(value) if not pd.isna(value) else default
            except:
                return default

        def safe_int(value, default=0):
            try:
                return int(value) if not pd.isna(value) else default
            except:
                return default

        def safe_str(value, default=""):
            try:
                return str(value).strip() if not pd.isna(value) else default
            except:
                return default

        # 银工费方式处理
        silver_work_type = 0  # 默认按克
        if "银工费方式" in row and not pd.isna(row["银工费方式"]):
            if str(row["银工费方式"]).strip() == "按件":
                silver_work_type = 1

        current_time = int(time.time())

        jewelry = Jewelry(
            barcode=safe_str(row["条码"]),
            name=safe_str(row["商品名称"]),
            category_id=safe_int(row["分类ID"], 1),
            ring_size=safe_str(row.get("圈口号", "")),
            gold_weight=safe_float(row.get("金重(克)", 0)),
            gold_price=safe_float(row.get("金进价(元/克)", 0)),
            silver_weight=safe_float(row.get("银重(克)", 0)),
            total_weight=safe_float(row.get("总重(克)", 0)),
            silver_price=safe_float(row.get("银进价(元/克)", 0)),
            silver_work_type=silver_work_type,
            silver_work_price=safe_float(row.get("银工费", 0)),
            plating_cost=safe_float(row.get("电铸费(元)", 0)),
            wholesale_work_price=safe_float(row.get("批发工费(元)", 0)),
            retail_work_price=safe_float(row.get("零售工费(元)", 0)),
            piece_work_price=safe_float(row.get("件工费(元)", 0)),
            store_id=safe_int(row.get("门店ID", default_store_id)),
            status=1,  # 默认上架
            createtime=current_time,
            updatetime=current_time
        )

        # 计算成本
        jewelry.gold_cost = jewelry.gold_weight * jewelry.gold_price
        jewelry.silver_cost = jewelry.silver_weight * jewelry.silver_price
        jewelry.total_cost = jewelry.gold_cost + jewelry.silver_cost + jewelry.plating_cost

        return jewelry

    def _update_jewelry_from_row(self, jewelry: Jewelry, row: pd.Series):
        """从行数据更新商品对象"""
        from datetime import datetime
        import time

        # 处理数值字段，确保不是NaN
        def safe_float(value, default=None):
            try:
                return float(value) if not pd.isna(value) else default
            except:
                return default

        def safe_int(value, default=None):
            try:
                return int(value) if not pd.isna(value) else default
            except:
                return default

        def safe_str(value, default=None):
            try:
                return str(value).strip() if not pd.isna(value) else default
            except:
                return default

        # 更新字段（只更新非空值）
        if "商品名称" in row and not pd.isna(row["商品名称"]):
            jewelry.name = safe_str(row["商品名称"])

        if "分类ID" in row and not pd.isna(row["分类ID"]):
            jewelry.category_id = safe_int(row["分类ID"])

        if "圈口号" in row:
            jewelry.ring_size = safe_str(row["圈口号"], "")

        # 更新重量和价格
        for field_map in [
            ("金重(克)", "gold_weight"),
            ("金进价(元/克)", "gold_price"),
            ("银重(克)", "silver_weight"),
            ("总重(克)", "total_weight"),
            ("银进价(元/克)", "silver_price"),
            ("银工费", "silver_work_price"),
            ("电铸费(元)", "plating_cost"),
            ("批发工费(元)", "wholesale_work_price"),
            ("零售工费(元)", "retail_work_price"),
            ("件工费(元)", "piece_work_price")
        ]:
            excel_col, model_field = field_map
            if excel_col in row and not pd.isna(row[excel_col]):
                value = safe_float(row[excel_col])
                if value is not None:
                    setattr(jewelry, model_field, value)

        # 银工费方式处理
        if "银工费方式" in row and not pd.isna(row["银工费方式"]):
            if str(row["银工费方式"]).strip() == "按件":
                jewelry.silver_work_type = 1
            else:
                jewelry.silver_work_type = 0

        # 重新计算成本
        jewelry.gold_cost = jewelry.gold_weight * jewelry.gold_price
        jewelry.silver_cost = jewelry.silver_weight * jewelry.silver_price
        jewelry.total_cost = jewelry.gold_cost + jewelry.silver_cost + jewelry.plating_cost

        jewelry.updatetime = int(time.time())

    async def _import_member_data(self, df: pd.DataFrame, request: ImportRequest, errors: List) -> tuple:
        """导入会员数据"""
        success_count = 0
        error_count = 0
        skip_count = 0

        required_columns = ["会员卡号", "会员姓名"]

        # 检查必需列
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            errors.append({
                "row": 0,
                "column": "表头",
                "value": "",
                "error": f"缺少必需列: {', '.join(missing_columns)}"
            })
            return success_count, len(df), skip_count, errors

        for index, row in df.iterrows():
            try:
                row_num = index + 2  # Excel行号从2开始（第1行是表头）

                # 验证必需字段
                if pd.isna(row["会员卡号"]) or str(row["会员卡号"]).strip() == "":
                    errors.append({
                        "row": row_num,
                        "column": "会员卡号",
                        "value": row["会员卡号"],
                        "error": "会员卡号不能为空"
                    })
                    error_count += 1
                    continue

                if pd.isna(row["会员姓名"]) or str(row["会员姓名"]).strip() == "":
                    errors.append({
                        "row": row_num,
                        "column": "会员姓名",
                        "value": row["会员姓名"],
                        "error": "会员姓名不能为空"
                    })
                    error_count += 1
                    continue

                # 检查会员卡号是否已存在
                card_no = str(row["会员卡号"]).strip()
                existing_member = self.db.query(Member).filter(Member.card_no == card_no).first()

                if existing_member:
                    if request.skip_duplicates:
                        skip_count += 1
                        continue
                    elif request.update_existing:
                        # 更新现有会员
                        self._update_member_from_row(existing_member, row)
                        success_count += 1
                        continue
                    else:
                        errors.append({
                            "row": row_num,
                            "column": "会员卡号",
                            "value": card_no,
                            "error": "会员卡号已存在"
                        })
                        error_count += 1
                        continue

                # 创建新会员
                member = self._create_member_from_row(row)
                self.db.add(member)
                success_count += 1

            except Exception as e:
                errors.append({
                    "row": row_num,
                    "column": "数据处理",
                    "value": "",
                    "error": str(e)
                })
                error_count += 1

        return success_count, error_count, skip_count, errors

    def _create_member_from_row(self, row: pd.Series) -> Member:
        """从行数据创建会员对象"""
        from datetime import datetime
        import time

        # 处理数值字段，确保不是NaN
        def safe_int(value, default=0):
            try:
                return int(value) if not pd.isna(value) else default
            except:
                return default

        def safe_str(value, default=""):
            try:
                return str(value).strip() if not pd.isna(value) else default
            except:
                return default

        def safe_date(value):
            try:
                if pd.isna(value):
                    return None
                if isinstance(value, str):
                    return datetime.strptime(value, '%Y-%m-%d').date()
                return value
            except:
                return None

        current_time = int(time.time())

        member = Member(
            card_no=safe_str(row["会员卡号"]),
            name=safe_str(row["会员姓名"]),
            phone=safe_str(row.get("联系电话", "")),
            birthday=safe_date(row.get("生日")),
            points=safe_int(row.get("积分", 0)),
            level=safe_int(row.get("会员等级", 1)),
            status=1,  # 默认正常
            createtime=current_time,
            updatetime=current_time
        )

        return member

    def _update_member_from_row(self, member: Member, row: pd.Series):
        """从行数据更新会员对象"""
        from datetime import datetime
        import time

        # 处理数值字段，确保不是NaN
        def safe_int(value, default=None):
            try:
                return int(value) if not pd.isna(value) else default
            except:
                return default

        def safe_str(value, default=None):
            try:
                return str(value).strip() if not pd.isna(value) else default
            except:
                return default

        def safe_date(value):
            try:
                if pd.isna(value):
                    return None
                if isinstance(value, str):
                    return datetime.strptime(value, '%Y-%m-%d').date()
                return value
            except:
                return None

        # 更新字段（只更新非空值）
        if "会员姓名" in row and not pd.isna(row["会员姓名"]):
            member.name = safe_str(row["会员姓名"])

        if "联系电话" in row:
            member.phone = safe_str(row["联系电话"], "")

        if "生日" in row:
            birthday = safe_date(row["生日"])
            if birthday:
                member.birthday = birthday

        if "积分" in row and not pd.isna(row["积分"]):
            points = safe_int(row["积分"])
            if points is not None:
                member.points = points

        if "会员等级" in row and not pd.isna(row["会员等级"]):
            level = safe_int(row["会员等级"])
            if level is not None:
                member.level = level

        member.updatetime = int(time.time())

    async def _import_stock_in_data(self, df: pd.DataFrame, request: ImportRequest, errors: List) -> tuple:
        """导入入库数据"""
        success_count = 0
        error_count = 0
        skip_count = 0

        # 入库导入比较复杂，这里简化处理
        # 实际项目中可能需要更复杂的逻辑

        return success_count, error_count, skip_count, errors

    async def get_export_history(self, page: int, page_size: int, export_type: str, operator_id: int):
        """获取导出历史记录"""
        # 这里可以实现导出历史记录的查询
        # 暂时返回空结果
        return {
            "success": True,
            "message": "获取成功",
            "data": [],
            "total": 0
        }

    async def delete_export_history(self, history_id: int, operator_id: int):
        """删除导出历史记录"""
        # 这里可以实现删除导出历史记录的逻辑
        return {
            "success": True,
            "message": "删除成功"
        }

    async def get_export_statistics(self, operator_id: int):
        """获取导出统计信息"""
        # 这里可以实现导出统计信息的查询
        return {
            "success": True,
            "message": "获取成功",
            "data": {
                "total_exports": 0,
                "today_exports": 0,
                "this_month_exports": 0,
                "popular_types": []
            }
        }