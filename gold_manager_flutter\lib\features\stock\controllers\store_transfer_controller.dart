import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gold_manager_flutter/features/stock/services/store_transfer_service.dart';
import 'package:gold_manager_flutter/services/auth_service.dart';
import 'package:gold_manager_flutter/services/store_service.dart';

/// 库存调拨控制器
class StoreTransferController extends GetxController {
  final StoreTransferService _transferService =
      Get.find<StoreTransferService>();
  final AuthService _authService = Get.find<AuthService>();
  final StoreService _storeService = Get.find<StoreService>();

  // 表单相关
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  final TextEditingController remarkController = TextEditingController();

  // 响应式变量
  final RxList<Map<String, dynamic>> transferList =
      <Map<String, dynamic>>[].obs;
  final RxList<Map<String, dynamic>> storeList = <Map<String, dynamic>>[].obs;
  final RxBool isLoading = false.obs;
  final RxInt currentPage = 1.obs;
  final RxInt totalPages = 1.obs;
  final RxInt totalCount = 0.obs;

  // 筛选条件
  final RxString searchKeyword = ''.obs;
  final RxInt selectedFromStoreId = 0.obs;
  final RxInt selectedToStoreId = 0.obs;
  final RxInt selectedStatus = (-1).obs; // -1表示全部
  final Rx<DateTime?> startDate = Rx<DateTime?>(null);
  final Rx<DateTime?> endDate = Rx<DateTime?>(null);

  // 统计信息
  final RxInt totalTransferCount = 0.obs;
  final RxInt pendingAuditCount = 0.obs;
  final RxInt approvedCount = 0.obs;
  final RxInt rejectedCount = 0.obs;
  final RxDouble totalTransferAmount = 0.0.obs;

  @override
  void onInit() {
    super.onInit();
    _initializeDefaultDateRange();
    _initializeData();
  }

  @override
  void onClose() {
    remarkController.dispose();
    super.onClose();
  }

  /// 初始化默认日期范围（最近30天）
  void _initializeDefaultDateRange() {
    final now = DateTime.now();
    // 🔧 修复：设置结束日期为今天的23:59:59，确保包含今天的所有数据
    endDate.value = DateTime(now.year, now.month, now.day, 23, 59, 59);
    // 设置开始日期为30天前的00:00:00
    startDate.value = DateTime(
      now.year,
      now.month,
      now.day,
    ).subtract(const Duration(days: 30));

    print(
      '📅 初始化默认日期范围: ${_formatDateForDisplay(startDate.value!)} 至 ${_formatDateForDisplay(endDate.value!)}',
    );
  }

  /// 初始化数据
  Future<void> _initializeData() async {
    await loadStoreList();
    await loadTransferList();
    await loadStatistics();
  }

  /// 加载门店列表
  Future<void> loadStoreList() async {
    try {
      print('📋 加载门店列表');

      // 调用真实的门店API获取所有门店
      final stores = await _storeService.getAllStores(status: 1); // 只获取正常状态的门店

      // 转换为下拉框需要的格式
      storeList.value = stores
          .map((store) => {'id': store.id, 'name': store.name})
          .toList();

      print('✅ 门店列表加载成功: ${storeList.length}个门店');
    } catch (e) {
      print('❌ 加载门店列表失败: $e');

      // API调用失败时使用备用数据
      storeList.value = [
        {'id': 1, 'name': '总店'},
        {'id': 2, 'name': '分店1'},
        {'id': 3, 'name': '分店2'},
      ];

      Get.snackbar(
        '警告',
        '门店数据加载失败，使用默认数据: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 加载调拨单列表
  Future<void> loadTransferList({bool showLoading = true}) async {
    try {
      if (showLoading) isLoading.value = true;

      print('📋 加载调拨单列表: page=${currentPage.value}');

      final response = await _transferService.getTransferList(
        page: currentPage.value,
        pageSize: 20,
        keyword: searchKeyword.value.isNotEmpty ? searchKeyword.value : null,
        fromStoreId: selectedFromStoreId.value > 0
            ? selectedFromStoreId.value
            : null,
        toStoreId: selectedToStoreId.value > 0 ? selectedToStoreId.value : null,
        status: selectedStatus.value >= 0 ? selectedStatus.value : null,
        startDate: startDate.value,
        endDate: endDate.value,
      );

      // 处理分页响应数据，并进行字段名映射
      final rawItems = List<Map<String, dynamic>>.from(response['items'] ?? []);
      transferList.value = rawItems
          .map((item) => _mapTransferFields(item))
          .toList();
      totalCount.value = response['total'] ?? 0;
      totalPages.value = response['total_pages'] ?? 1;

      print('✅ 调拨单列表加载成功: ${transferList.length}条，总计${totalCount.value}条');
    } catch (e) {
      print('❌ 加载调拨单列表失败: $e');
      Get.snackbar(
        '错误',
        '加载调拨单列表失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      if (showLoading) isLoading.value = false;
    }
  }

  /// 加载统计信息
  Future<void> loadStatistics() async {
    try {
      print('📊 加载调拨统计信息');

      final statistics = await _transferService.getTransferStatistics();

      totalTransferCount.value = statistics['total_count'] ?? 0;
      pendingAuditCount.value = statistics['pending_count'] ?? 0;
      approvedCount.value = statistics['approved_count'] ?? 0;
      rejectedCount.value = statistics['rejected_count'] ?? 0;
      totalTransferAmount.value = _parseDouble(statistics['total_amount']);

      print('✅ 调拨统计信息加载成功');
    } catch (e) {
      print('❌ 加载调拨统计信息失败: $e');
    }
  }

  /// 映射调拨单字段名（从API的下划线命名转换为前端的驼峰命名）
  Map<String, dynamic> _mapTransferFields(Map<String, dynamic> apiData) {
    return {
      'id': apiData['id'],
      'transferNo': apiData['transfer_no'] ?? apiData['transferNo'],
      'fromStoreId': apiData['from_store_id'] ?? apiData['fromStoreId'],
      'toStoreId': apiData['to_store_id'] ?? apiData['toStoreId'],
      'fromStoreName': apiData['from_store_name'] ?? apiData['fromStoreName'],
      'toStoreName': apiData['to_store_name'] ?? apiData['toStoreName'],
      'operatorId': apiData['admin_id'] ?? apiData['operatorId'],
      'operatorName': apiData['admin_name'] ?? apiData['operatorName'],
      'totalAmount': _parseDouble(
        apiData['total_amount'] ?? apiData['totalAmount'],
      ),
      'status': apiData['status'],
      'statusText': apiData['status_text'] ?? _getStatusText(apiData['status']),
      'createTime': apiData['createtime'] ?? apiData['create_time'],
      'auditTime': apiData['audit_time'],
      'auditUserId': apiData['audit_id'],
      'auditUserName': apiData['auditor_name'],
      'remark': apiData['remark'],
      'auditRemark': apiData['audit_remark'],
      'itemCount': apiData['item_count'] ?? 0,
    };
  }

  /// 根据状态值获取状态文本
  String _getStatusText(int? status) {
    switch (status) {
      case 0:
        return '待审核';
      case 1:
        return '已通过';
      case 2:
        return '已拒绝';
      default:
        return '未知';
    }
  }

  /// 安全解析double类型数据，支持字符串和数字类型
  double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      try {
        return double.parse(value);
      } catch (e) {
        print('⚠️ 解析double失败: $value, 错误: $e');
        return 0.0;
      }
    }
    return 0.0;
  }

  /// 格式化日期用于显示（YYYY-MM-DD格式）
  String _formatDateForDisplay(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  /// 更新日期范围并触发搜索
  void updateDateRange(DateTime? start, DateTime? end) {
    startDate.value = start;
    // 🔧 修复：确保结束日期包含完整的一天（设置为23:59:59）
    if (end != null) {
      endDate.value = DateTime(end.year, end.month, end.day, 23, 59, 59);
    } else {
      endDate.value = end;
    }
    currentPage.value = 1; // 重置到第一页
    searchTransfers();
    print(
      '📅 日期范围已更新: ${start != null ? _formatDateForDisplay(start) : '无'} 至 ${endDate.value != null ? _formatDateForDisplay(endDate.value!) : '无'}',
    );
  }

  /// 获取日期范围显示文本
  String get dateRangeDisplayText {
    if (startDate.value != null && endDate.value != null) {
      return '${_formatDateForDisplay(startDate.value!)} 至 ${_formatDateForDisplay(endDate.value!)}';
    } else if (startDate.value != null) {
      return '从 ${_formatDateForDisplay(startDate.value!)}';
    } else if (endDate.value != null) {
      return '到 ${_formatDateForDisplay(endDate.value!)}';
    } else {
      return '选择日期范围';
    }
  }

  /// 搜索调拨单
  Future<void> searchTransfers() async {
    currentPage.value = 1;
    await loadTransferList();
    await loadStatistics();
  }

  /// 重置筛选条件
  void resetFilters() {
    searchKeyword.value = '';
    selectedFromStoreId.value = 0;
    selectedToStoreId.value = 0;
    selectedStatus.value = -1;
    // 重置为默认日期范围（最近30天）
    _initializeDefaultDateRange();
    currentPage.value = 1;
    searchTransfers();
  }

  /// 刷新数据
  Future<void> refreshData() async {
    currentPage.value = 1;
    await loadTransferList();
    await loadStatistics();
  }

  /// 上一页
  void previousPage() {
    print('🔙 点击上一页: 当前页=${currentPage.value}, 总页数=${totalPages.value}');
    if (currentPage.value > 1) {
      currentPage.value--;
      print('📄 跳转到第${currentPage.value}页');
      loadTransferList();
    } else {
      print('⚠️ 已经是第一页，无法上一页');
    }
  }

  /// 下一页
  void nextPage() {
    print('🔜 点击下一页: 当前页=${currentPage.value}, 总页数=${totalPages.value}');
    if (currentPage.value < totalPages.value) {
      currentPage.value++;
      print('📄 跳转到第${currentPage.value}页');
      loadTransferList();
    } else {
      print('⚠️ 已经是最后一页，无法下一页');
    }
  }

  /// 跳转到指定页
  void goToPage(int page) {
    print(
      '🎯 跳转到指定页: 目标页=$page, 当前页=${currentPage.value}, 总页数=${totalPages.value}',
    );
    if (page >= 1 && page <= totalPages.value) {
      currentPage.value = page;
      print('📄 跳转到第${currentPage.value}页');
      loadTransferList();
    } else {
      print('⚠️ 页码超出范围: $page (有效范围: 1-${totalPages.value})');
    }
  }

  /// 审核调拨单
  Future<void> auditTransfer({
    required int transferId,
    required bool approved,
    String? auditRemark,
  }) async {
    try {
      isLoading.value = true;
      print('📋 审核调拨单: ID=$transferId, approved=$approved');

      final success = await _transferService.auditTransfer(
        transferId: transferId,
        approved: approved,
        auditRemark: auditRemark,
        auditorId: _authService.userId.value,
      );

      if (success) {
        Get.snackbar(
          '成功',
          approved ? '调拨单审核通过' : '调拨单审核拒绝',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: approved ? Colors.green : Colors.orange,
          colorText: Colors.white,
        );

        // 刷新列表和统计
        await refreshData();
      }
    } catch (e) {
      print('❌ 审核调拨单失败: $e');
      Get.snackbar(
        '错误',
        '审核调拨单失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// 删除调拨单
  Future<void> deleteTransfer(int id) async {
    try {
      isLoading.value = true;

      final success = await _transferService.deleteTransfer(id);

      if (success) {
        Get.snackbar(
          '成功',
          '调拨单删除成功',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
        await refreshData();
      }
    } catch (e) {
      print('❌ 删除调拨单失败: $e');
      Get.snackbar(
        '错误',
        '删除调拨单失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// 获取状态选项
  List<Map<String, dynamic>> get statusOptions => [
    {'value': -1, 'label': '全部状态'},
    {'value': 0, 'label': '草稿'},
    {'value': 1, 'label': '待审核'},
    {'value': 2, 'label': '已通过'},
    {'value': 3, 'label': '已拒绝'},
  ];

  /// 获取用户可见的门店列表
  List<Map<String, dynamic>> get availableStores {
    return storeList;
  }
}
