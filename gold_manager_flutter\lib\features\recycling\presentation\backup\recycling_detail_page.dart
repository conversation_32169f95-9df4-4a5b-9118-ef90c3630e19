import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../models/common/enums.dart';
import '../../../models/recycling/recycling_item.dart';
import '../../../models/recycling/recycling_process.dart';
import '../../../models/recycling/recycling_model.dart' hide RecyclingItem;
import '../../../widgets/loading_widget.dart';
import '../controllers/recycling_controller.dart';
import 'recycling_form_page.dart';
import 'pages/process_page.dart';
import '../models/recycling_item.dart' as process_item;

/// 回收单详情页面
class RecyclingDetailPage extends StatefulWidget {
  final int id;
  
  const RecyclingDetailPage({
    super.key,
    required this.id,
  });

  @override
  State<RecyclingDetailPage> createState() => _RecyclingDetailPageState();
}

class _RecyclingDetailPageState extends State<RecyclingDetailPage> {
  late final RecyclingController _controller;
  
  @override
  void initState() {
    super.initState();
    _controller = Get.find<RecyclingController>();
    _loadData();
  }
  
  Future<void> _loadData() async {
    await _controller.loadRecyclingOrderDetail(widget.id);
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('回收单详情'),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () {
              // 打开编辑页面
              Get.to(() => RecyclingFormPage(id: widget.id));
            },
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuItemSelected,
            itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
              const PopupMenuItem<String>(
                value: 'process',
                child: Text('标记为已处理'),
              ),
              const PopupMenuItem<String>(
                value: 'complete',
                child: Text('标记为已完成'),
              ),
              const PopupMenuItem<String>(
                value: 'cancel',
                child: Text('取消回收单'),
              ),
              const PopupMenuItem<String>(
                value: 'delete',
                child: Text('删除'),
              ),
            ],
          ),
        ],
      ),
      body: Obx(() {
        if (_controller.isDetailLoading.value) {
          return const LoadingWidget();
        }
        
        final order = _controller.currentOrder.value;
        if (order == null) {
          return const Center(
            child: Text('未找到回收单信息'),
          );
        }
        
        return _buildDetailContent(order);
      }),
    );
  }
  
  Widget _buildDetailContent(RecyclingOrder order) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildOrderHeader(order),
          const SizedBox(height: 24),
          _buildCustomerInfo(order),
          const SizedBox(height: 24),
          _buildOrderItems(order),
          const SizedBox(height: 24),
          _buildOrderSummary(order),
        ],
      ),
    );
  }
  
  Widget _buildOrderHeader(RecyclingOrder order) {
    // 根据状态返回对应的颜色
    Color getStatusColor(int status) {
      switch (status) {
        case 0:
          return AppColors.warning;
        case 1:
          return AppColors.info;
        case 2:
          return AppColors.success;
        case 3:
          return AppColors.error;
        default:
          return Colors.grey;
      }
    }
    
    return Card(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '回收单号: ${order.orderNo}',
                  style: AppTextStyles.title,
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: getStatusColor(order.status).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: getStatusColor(order.status),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    order.statusText,
                    style: TextStyle(
                      color: getStatusColor(order.status),
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                const Icon(
                  Icons.calendar_today,
                  size: 16,
                  color: AppColors.textSecondary,
                ),
                const SizedBox(width: 8),
                Text(
                  '回收日期: ${order.formattedDate}',
                  style: AppTextStyles.bodySmall,
                ),
                const SizedBox(width: 16),
                const Icon(
                  Icons.person,
                  size: 16,
                  color: AppColors.textSecondary,
                ),
                const SizedBox(width: 8),
                Text(
                  '操作员: ${order.creatorName}',
                  style: AppTextStyles.bodySmall,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildCustomerInfo(RecyclingOrder order) {
    return Card(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '客户信息',
              style: AppTextStyles.subtitle,
            ),
            const SizedBox(height: 16),
            _buildInfoRow('客户名称', order.customerName),
            _buildInfoRow('联系电话', order.customerPhone),
            if (order.remark.isNotEmpty)
              _buildInfoRow('备注信息', order.remark),
          ],
        ),
      ),
    );
  }
  
  Widget _buildOrderItems(RecyclingOrder order) {
    return Card(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '回收物品',
                  style: AppTextStyles.subtitle,
                ),
                Text(
                  '共${order.items.length}件',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...order.items.map((item) => _buildItemCard(item)).toList(),
          ],
        ),
      ),
    );
  }
  
  Widget _buildItemCard(RecyclingItem item) {
    // 根据状态返回对应的颜色
    Color getStatusColor(int status) {
      switch (status) {
        case 0:
          return AppColors.warning;
        case 1:
          return AppColors.info;
        case 2:
          return AppColors.success;
        case 3:
          return AppColors.error;
        default:
          return Colors.grey;
      }
    }
    
    // 临时解决方案: 从RecyclingModel.dart中的RecyclingItem获取相应的属性
    final int itemStatus = item is RecyclingOrderItem ? item.status : 0;
    final String itemStatusText = itemStatus == 0 ? '待处理' : 
                                 itemStatus == 1 ? '已加工' : 
                                 itemStatus == 2 ? '已丢弃' : 
                                 itemStatus == 3 ? '已售出' : '未知状态';
    
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      elevation: 0,
      color: Colors.grey.shade50,
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 物品图片
                if (item.photoUrl != null)
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.asset(
                      item.photoUrl!,
                      width: 80,
                      height: 80,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          width: 80,
                          height: 80,
                          color: Colors.grey.shade200,
                          child: const Icon(
                            Icons.image_not_supported,
                            color: AppColors.textSecondary,
                          ),
                        );
                      },
                    ),
                  )
                else
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: Colors.grey.shade200,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.image,
                      color: AppColors.textSecondary,
                    ),
                  ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            item.itemName,
                            style: AppTextStyles.subtitle,
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: getStatusColor(item.status).withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: getStatusColor(item.status),
                                width: 1,
                              ),
                            ),
                            child: Text(
                              itemStatusText,
                              style: TextStyle(
                                color: getStatusColor(item.status),
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '分类: ${item.categoryName}',
                        style: AppTextStyles.bodySmall,
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Text(
                            '重量: ${item.weight}g',
                            style: AppTextStyles.bodySmall,
                          ),
                          const SizedBox(width: 16),
                          Text(
                            '单价: ¥${item.price.toStringAsFixed(2)}/g',
                            style: AppTextStyles.bodySmall,
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '金额: ¥${item.amount.toStringAsFixed(2)}',
                        style: AppTextStyles.subtitle.copyWith(
                          color: AppColors.primary,
                        ),
                      ),
                      if (item.remark != null && item.remark!.isNotEmpty) ...[
                        const SizedBox(height: 4),
                        Text(
                          '备注: ${item.remark}',
                          style: AppTextStyles.caption,
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                if (item.status == 0) // 只有待处理状态的物品才能进行金属分离
                  OutlinedButton.icon(
                    icon: const Icon(Icons.science, size: 18),
                    label: const Text('金属分离'),
                    onPressed: () => _handleMetalSeparation(item),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: AppColors.primary,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                    ),
                  ),
                const SizedBox(width: 8),
                OutlinedButton.icon(
                  icon: const Icon(Icons.settings, size: 18),
                  label: const Text('旧料处理'),
                  onPressed: () => _handleItemProcess(item),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppColors.secondary,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                OutlinedButton.icon(
                  icon: const Icon(Icons.history, size: 18),
                  label: const Text('处理记录'),
                  onPressed: () => _viewItemHistory(item),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppColors.info,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildOrderSummary(RecyclingOrder order) {
    return Card(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '回收汇总',
              style: AppTextStyles.subtitle,
            ),
            const SizedBox(height: 16),
            _buildInfoRow('总重量', '${order.totalWeight}g'),
            _buildInfoRow('总金额', '¥${order.totalAmount.toStringAsFixed(2)}'),
            _buildInfoRow('创建时间', order.formattedCreatedAt),
          ],
        ),
      ),
    );
  }
  
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTextStyles.body,
            ),
          ),
        ],
      ),
    );
  }
  
  void _handleMenuItemSelected(String value) async {
    final order = _controller.currentOrder.value;
    if (order == null) return;
    
    switch (value) {
      case 'process':
        await _controller.updateRecyclingOrderStatus(order.id, 1);
        break;
      case 'complete':
        await _controller.updateRecyclingOrderStatus(order.id, 2);
        break;
      case 'cancel':
        final confirm = await Get.dialog<bool>(
          AlertDialog(
            title: const Text('确认取消'),
            content: const Text('确定要取消此回收单吗？'),
            actions: [
              TextButton(
                onPressed: () => Get.back(result: false),
                child: const Text('取消'),
              ),
              ElevatedButton(
                onPressed: () => Get.back(result: true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.error,
                  foregroundColor: Colors.white,
                ),
                child: const Text('确定'),
              ),
            ],
          ),
        );
        
        if (confirm == true) {
          await _controller.updateRecyclingOrderStatus(order.id, 3);
        }
        break;
      case 'delete':
        final confirm = await Get.dialog<bool>(
          AlertDialog(
            title: const Text('确认删除'),
            content: const Text('删除后无法恢复，确定要删除此回收单吗？'),
            actions: [
              TextButton(
                onPressed: () => Get.back(result: false),
                child: const Text('取消'),
              ),
              ElevatedButton(
                onPressed: () => Get.back(result: true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.error,
                  foregroundColor: Colors.white,
                ),
                child: const Text('确定'),
              ),
            ],
          ),
        );
        
        if (confirm == true) {
          final success = await _controller.deleteRecyclingOrder(order.id);
          if (success) {
            Get.back(); // 返回列表页
          }
        }
        break;
    }
  }
  
  /// 处理金属分离操作
  void _handleMetalSeparation(RecyclingItem item) {
    Get.dialog(
      AlertDialog(
        title: const Text('金属分离操作'),
        content: const Text('确定要对该物品进行金属分离操作吗？'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              // 打开金属分离表单页面
              Get.snackbar(
                '功能开发中',
                '金属分离功能正在开发中，敬请期待',
                snackPosition: SnackPosition.BOTTOM,
              );
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
  
  /// 查看物品处理记录
  void _viewItemHistory(RecyclingItem item) {
    Get.snackbar(
      '功能开发中',
      '处理记录查看功能正在开发中，敬请期待',
      snackPosition: SnackPosition.BOTTOM,
    );
  }
  
  /// 打开旧料处理页面
  void _handleItemProcess(RecyclingItem item) {
    // 将RecyclingItem(recycling_model.dart)转换为RecyclingItem(recycling_item.dart)
    final processItem = process_item.RecyclingItem(
      id: item.id,
      recyclingId: item.recyclingId,
      name: item.name,
      description: item.description,
      goldWeight: item.goldWeight,
      goldPrice: item.goldPrice,
      silverWeight: item.silverWeight,
      silverPrice: item.silverPrice,
      amount: item.amount,
      imageUrl: item.imageUrl,
      processType: process_item.RecyclingProcessType.pending,
      weight: item.weight,
      type: item.type ?? 1,
      createTime: item.createTime,
    );
    
    // 导航到旧料处理页面
    Get.to(() => ProcessPage(recyclingItem: processItem));
  }
} 