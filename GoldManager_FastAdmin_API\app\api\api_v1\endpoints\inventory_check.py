"""
库存盘点API路由

老板，这个模块提供库存盘点的API接口，包括：
1. 盘点单的CRUD操作
2. 盘点流程管理
3. 商品盘点操作
4. 统计分析功能
5. 多条件查询筛选

完整的库存盘点业务API接口。
"""

from typing import List, Optional, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.dependencies import require_permission
from app.schemas.auth import CurrentUserResponse
from app.services.inventory_check_service import InventoryCheckService
from app.schemas.inventory_check import (
    InventoryCheckCreate, InventoryCheckUpdate, InventoryCheckResponse,
    InventoryCheckDetailResponse, InventoryCheckStatistics,
    InventoryCheckQueryParams, InventoryCheckStatusUpdate,
    InventoryCheckItemCheck, InventoryCheckBatchCheck
)
from app.schemas.common import PaginatedResponse, StandardResponse

router = APIRouter()


def get_inventory_check_service(db: Session = Depends(get_db)) -> InventoryCheckService:
    """获取库存盘点服务实例"""
    return InventoryCheckService(db)


@router.post("", response_model=StandardResponse[InventoryCheckResponse], summary="创建盘点单")
async def create_inventory_check(
    check_data: InventoryCheckCreate = Body(..., description="盘点单创建数据"),
    current_user: CurrentUserResponse = Depends(require_permission("inventory.create")),
    service: InventoryCheckService = Depends(get_inventory_check_service)
) -> Any:
    """
    创建新的库存盘点单

    **功能说明:**
    - 自动生成盘点单号(格式: CHKYYYYMMDD0001)
    - 验证门店和商品是否存在
    - 自动计算应盘总数
    - 初始状态为进行中(0)

    **业务规则:**
    - 盘点商品明细不能为空
    - 所有商品必须存在于系统中
    - 门店必须存在且状态正常
    - 操作员必须存在且状态正常

    **状态说明:**
    - 0: 进行中
    - 1: 已完成
    - 2: 已取消
    """
    try:
        # 权限控制：非管理员用户只能为自己的门店创建盘点单
        if current_user.store_id and check_data.store_id != current_user.store_id:
            raise HTTPException(status_code=403, detail="无权为其他门店创建盘点单")

        result = service.create_inventory_check(check_data, current_user.id)
        return StandardResponse(
            success=True,
            code=200,
            message="盘点单创建成功",
            data=result
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建盘点单失败: {str(e)}")


@router.get("", response_model=PaginatedResponse[InventoryCheckResponse], summary="获取盘点单列表")
async def get_inventory_check_list(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    keyword: Optional[str] = Query(None, description="关键词搜索(单号、备注)"),
    store_id: Optional[int] = Query(None, description="门店ID筛选"),
    status: Optional[int] = Query(None, ge=0, le=2, description="状态筛选(0=进行中,1=已完成,2=已取消)"),
    operator_id: Optional[int] = Query(None, description="操作员ID筛选"),
    start_date: Optional[str] = Query(None, regex=r'^\d{4}-\d{2}-\d{2}$', description="开始日期(YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, regex=r'^\d{4}-\d{2}-\d{2}$', description="结束日期(YYYY-MM-DD)"),
    current_user: CurrentUserResponse = Depends(require_permission("inventory.view")),
    service: InventoryCheckService = Depends(get_inventory_check_service)
) -> Any:
    """
    获取库存盘点单列表

    **查询功能:**
    - 支持分页查询
    - 支持关键词搜索(盘点单号、备注)
    - 支持多条件筛选(门店、状态、操作员、日期范围)
    - 按创建时间倒序排列

    **返回信息:**
    - 盘点单基本信息
    - 门店名称
    - 操作员姓名
    - 盘点进度
    - 状态描述
    """
    try:
        # 权限控制：非管理员用户只能查询自己门店的盘点单
        if current_user.store_id and store_id and store_id != current_user.store_id:
            raise HTTPException(status_code=403, detail="无权查询其他门店的盘点单")

        # 如果用户有分配门店且未指定store_id，自动使用用户门店
        if current_user.store_id and not store_id:
            store_id = current_user.store_id

        # 构建查询参数
        params = InventoryCheckQueryParams(
            keyword=keyword,
            store_id=store_id,
            status=status,
            operator_id=operator_id,
            start_date=start_date,
            end_date=end_date
        )

        checks, total = service.get_inventory_check_list(page, page_size, params)

        # 计算总页数
        total_pages = (total + page_size - 1) // page_size if total > 0 else 1

        # 构建分页信息
        from app.schemas.common import PaginationInfo
        pagination_info = PaginationInfo(
            page=page,
            page_size=page_size,
            total=total,
            pages=total_pages
        )

        return PaginatedResponse(
            success=True,
            code=200,
            message="获取盘点单列表成功",
            data=checks,
            pagination=pagination_info
        )
    except HTTPException:
        raise
    except Exception as e:
        import traceback
        print(f"❌ 获取盘点单列表失败: {e}")
        print(f"❌ 详细错误: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"获取盘点单列表失败: {str(e)}")


@router.get("/{check_id}", response_model=StandardResponse[InventoryCheckDetailResponse], summary="获取盘点单详情")
async def get_inventory_check_detail(
    check_id: int = Path(..., description="盘点单ID"),
    current_user: CurrentUserResponse = Depends(require_permission("inventory.view")),
    service: InventoryCheckService = Depends(get_inventory_check_service)
) -> Any:
    """
    根据ID获取库存盘点单详情

    **返回信息:**
    - 盘点单完整信息
    - 盘点明细列表
    - 关联的门店、操作员信息
    - 每个明细的盘点状态和差异
    """
    try:
        result = service.get_inventory_check_by_id(check_id)
        if not result:
            raise HTTPException(status_code=404, detail="盘点单不存在")

        # 权限控制：非管理员用户只能查询自己门店的盘点单
        if current_user.store_id and result.store_id != current_user.store_id:
            raise HTTPException(status_code=403, detail="无权查询其他门店的盘点单")

        return StandardResponse(
            success=True,
            code=200,
            message="获取盘点单详情成功",
            data=result
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取盘点单详情失败: {str(e)}")


@router.get("/by-no/{check_no}", response_model=StandardResponse[InventoryCheckDetailResponse], summary="根据单号获取盘点单详情")
async def get_inventory_check_by_no(
    check_no: str = Path(..., description="盘点单号"),
    current_user: CurrentUserResponse = Depends(require_permission("inventory.view")),
    service: InventoryCheckService = Depends(get_inventory_check_service)
) -> Any:
    """
    根据盘点单号获取详情

    **使用场景:**
    - 扫码查询盘点单
    - 快速定位盘点单
    - 移动端查询
    """
    try:
        result = service.get_inventory_check_by_no(check_no)
        if not result:
            raise HTTPException(status_code=404, detail="盘点单不存在")

        # 权限控制：非管理员用户只能查询自己门店的盘点单
        if current_user.store_id and result.store_id != current_user.store_id:
            raise HTTPException(status_code=403, detail="无权查询其他门店的盘点单")

        return StandardResponse(
            success=True,
            code=200,
            message="获取盘点单详情成功",
            data=result
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取盘点单详情失败: {str(e)}")


@router.put("/{check_id}", response_model=StandardResponse[InventoryCheckResponse], summary="更新盘点单")
async def update_inventory_check(
    check_id: int = Path(..., description="盘点单ID"),
    update_data: InventoryCheckUpdate = Body(..., description="更新数据"),
    current_user: CurrentUserResponse = Depends(require_permission("inventory.update")),
    service: InventoryCheckService = Depends(get_inventory_check_service)
) -> Any:
    """
    更新库存盘点单信息

    **业务规则:**
    - 只有进行中状态的盘点单才能修改
    - 可修改备注信息

    **注意事项:**
    - 已完成或已取消的盘点单不能修改
    """
    try:
        # 先获取盘点单信息进行权限检查
        existing_check = service.get_inventory_check_by_id(check_id)
        if not existing_check:
            raise HTTPException(status_code=404, detail="盘点单不存在")

        # 权限控制：非管理员用户只能更新自己门店的盘点单
        if current_user.store_id and existing_check.store_id != current_user.store_id:
            raise HTTPException(status_code=403, detail="无权更新其他门店的盘点单")

        result = service.update_inventory_check(check_id, update_data)

        return StandardResponse(
            success=True,
            code=200,
            message="盘点单更新成功",
            data=result
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新盘点单失败: {str(e)}")


@router.delete("/{check_id}", response_model=StandardResponse[bool], summary="删除盘点单")
async def delete_inventory_check(
    check_id: int = Path(..., description="盘点单ID"),
    current_user: CurrentUserResponse = Depends(require_permission("inventory.delete")),
    service: InventoryCheckService = Depends(get_inventory_check_service)
) -> Any:
    """
    删除库存盘点单

    **业务规则:**
    - 只有进行中状态的盘点单才能删除
    - 删除盘点单会同时删除所有明细

    **注意事项:**
    - 已完成或已取消的盘点单不能删除
    - 删除操作不可恢复
    """
    try:
        # 先获取盘点单信息进行权限检查
        existing_check = service.get_inventory_check_by_id(check_id)
        if not existing_check:
            raise HTTPException(status_code=404, detail="盘点单不存在")

        # 权限控制：非管理员用户只能删除自己门店的盘点单
        if current_user.store_id and existing_check.store_id != current_user.store_id:
            raise HTTPException(status_code=403, detail="无权删除其他门店的盘点单")

        result = service.delete_inventory_check(check_id)

        return StandardResponse(
            success=True,
            code=200,
            message="盘点单删除成功",
            data=True
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除盘点单失败: {str(e)}")


@router.patch("/{check_id}/status", response_model=StandardResponse[InventoryCheckResponse], summary="更新盘点单状态")
async def update_check_status(
    check_id: int = Path(..., description="盘点单ID"),
    status_data: InventoryCheckStatusUpdate = Body(..., description="状态更新数据"),
    current_user: CurrentUserResponse = Depends(require_permission("inventory.update")),
    service: InventoryCheckService = Depends(get_inventory_check_service)
) -> Any:
    """
    更新库存盘点单状态

    **状态流转:**
    - 进行中(0) → 已完成(1)
    - 进行中(0) → 已取消(2)

    **业务规则:**
    - 只有进行中状态才能变更为其他状态
    - 完成盘点时自动设置结束时间
    - 取消盘点时自动设置结束时间

    **使用场景:**
    - 完成盘点工作
    - 取消盘点任务
    """
    try:
        # 先获取盘点单信息进行权限检查
        existing_check = service.get_inventory_check_by_id(check_id)
        if not existing_check:
            raise HTTPException(status_code=404, detail="盘点单不存在")

        # 权限控制：非管理员用户只能更新自己门店的盘点单状态
        if current_user.store_id and existing_check.store_id != current_user.store_id:
            raise HTTPException(status_code=403, detail="无权更新其他门店的盘点单状态")

        result = service.update_check_status(check_id, status_data)

        status_text = {1: "完成", 2: "取消"}.get(status_data.status, "更新")

        return StandardResponse(
            success=True,
            code=200,
            message=f"盘点单{status_text}成功",
            data=result
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新盘点单状态失败: {str(e)}")


@router.get("/{check_id}/items", response_model=PaginatedResponse[dict], summary="获取盘点明细列表")
async def get_inventory_check_items(
    check_id: int = Path(..., description="盘点单ID"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(100, ge=1, description="每页数量"),
    status: Optional[int] = Query(None, ge=0, le=1, description="盘点状态筛选(0=未盘点,1=已盘点)"),
    current_user: CurrentUserResponse = Depends(require_permission("inventory.view")),
    service: InventoryCheckService = Depends(get_inventory_check_service)
) -> Any:
    """
    获取指定盘点单的商品明细列表

    **功能说明:**
    - 分页获取盘点明细
    - 支持按盘点状态筛选
    - 返回商品基本信息和盘点状态

    **返回信息:**
    - 商品基本信息(条码、名称、分类等)
    - 系统库存数量
    - 实际库存数量
    - 盘点状态和差异
    - 盘点时间和人员
    """
    try:
        # 先获取盘点单信息进行权限检查
        existing_check = service.get_inventory_check_by_id(check_id)
        if not existing_check:
            raise HTTPException(status_code=404, detail="盘点单不存在")

        # 权限控制：非管理员用户只能查询自己门店的盘点单明细
        if current_user.store_id and existing_check.store_id != current_user.store_id:
            raise HTTPException(status_code=403, detail="无权查询其他门店的盘点明细")

        items, total = service.get_inventory_check_items(check_id, page, page_size, status)

        # 计算总页数
        total_pages = (total + page_size - 1) // page_size if total > 0 else 1

        # 构建分页信息
        from app.schemas.common import PaginationInfo
        pagination_info = PaginationInfo(
            page=page,
            page_size=page_size,
            total=total,
            pages=total_pages
        )

        return PaginatedResponse(
            success=True,
            code=200,
            message="获取盘点明细列表成功",
            data=items,
            pagination=pagination_info
        )
    except HTTPException:
        raise
    except Exception as e:
        import traceback
        print(f"❌ 获取盘点明细列表失败: {e}")
        print(f"❌ 详细错误: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"获取盘点明细列表失败: {str(e)}")


@router.post("/{check_id}/scan", response_model=StandardResponse[dict], summary="扫码盘点商品")
async def scan_check_item(
    check_id: int = Path(..., description="盘点单ID"),
    scan_data: dict = Body(..., description="扫码数据"),
    current_user: CurrentUserResponse = Depends(require_permission("inventory.check")),
    service: InventoryCheckService = Depends(get_inventory_check_service)
) -> Any:
    """
    扫码盘点商品

    **功能说明:**
    - 通过条码快速盘点商品
    - 自动验证商品库存状态
    - 自动设置实际库存为1（表示盘点到该商品）
    - 验证商品是否在库（status=1，上架在售状态）

    **请求数据:**
    ```json
    {
        "barcode": "JW202401010001",
        "remark": "扫码盘点"
    }
    ```

    **业务规则:**
    - 只有进行中状态的盘点单才能扫码盘点
    - 商品必须在当前盘点单中
    - 商品必须是上架在售状态(status=1)
    - 自动将实际库存设置为1
    """
    try:
        # 先获取盘点单信息进行权限检查
        existing_check = service.get_inventory_check_by_id(check_id)
        if not existing_check:
            raise HTTPException(status_code=404, detail="盘点单不存在")

        # 权限控制：非管理员用户只能盘点自己门店的商品
        if current_user.store_id and existing_check.store_id != current_user.store_id:
            raise HTTPException(status_code=403, detail="无权盘点其他门店的商品")

        # 提取条码
        barcode = scan_data.get("barcode")
        if not barcode:
            raise HTTPException(status_code=400, detail="条码不能为空")

        remark = scan_data.get("remark", "扫码盘点")

        result = service.scan_check_item(check_id, barcode, remark, current_user.id)

        return StandardResponse(
            success=True,
            code=200,
            message="扫码盘点成功",
            data=result
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"扫码盘点失败: {str(e)}")


@router.patch("/{check_id}/items/{item_id}/check", response_model=StandardResponse[bool], summary="盘点单个商品")
async def check_item(
    check_id: int = Path(..., description="盘点单ID"),
    item_id: int = Path(..., description="盘点明细ID"),
    check_data: InventoryCheckItemCheck = Body(..., description="盘点数据"),
    current_user: CurrentUserResponse = Depends(require_permission("inventory.check")),
    service: InventoryCheckService = Depends(get_inventory_check_service)
) -> Any:
    """
    盘点单个商品

    **功能说明:**
    - 记录实际库存数量
    - 自动计算差异
    - 更新盘点状态
    - 记录盘点时间和人员

    **业务规则:**
    - 只有进行中状态的盘点单才能盘点
    - 盘点员必须存在且状态正常
    - 自动更新盘点单的统计信息

    **盘点流程:**
    1. 扫描商品条码
    2. 输入实际库存
    3. 添加盘点备注
    4. 提交盘点结果
    """
    try:
        # 先获取盘点单信息进行权限检查
        existing_check = service.get_inventory_check_by_id(check_id)
        if not existing_check:
            raise HTTPException(status_code=404, detail="盘点单不存在")

        # 权限控制：非管理员用户只能盘点自己门店的商品
        if current_user.store_id and existing_check.store_id != current_user.store_id:
            raise HTTPException(status_code=403, detail="无权盘点其他门店的商品")

        result = service.check_item(check_id, item_id, check_data, current_user.id)

        return StandardResponse(
            success=True,
            code=200,
            message="商品盘点成功",
            data=result
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"商品盘点失败: {str(e)}")


@router.patch("/{check_id}/batch-check", response_model=StandardResponse[bool], summary="批量盘点商品")
async def batch_check_items(
    check_id: int = Path(..., description="盘点单ID"),
    batch_data: InventoryCheckBatchCheck = Body(..., description="批量盘点数据"),
    current_user: CurrentUserResponse = Depends(require_permission("inventory.check")),
    service: InventoryCheckService = Depends(get_inventory_check_service)
) -> Any:
    """
    批量盘点商品

    **功能说明:**
    - 一次性盘点多个商品
    - 提高盘点效率
    - 自动更新统计信息

    **数据格式:**
    ```json
    {
        "items": [
            {"item_id": 1, "actual_stock": 1, "remark": "正常"},
            {"item_id": 2, "actual_stock": 0, "remark": "缺失"}
        ]
    }
    ```

    **使用场景:**
    - 移动端批量提交
    - 导入盘点结果
    - 快速盘点模式
    """
    try:
        # 先获取盘点单信息进行权限检查
        existing_check = service.get_inventory_check_by_id(check_id)
        if not existing_check:
            raise HTTPException(status_code=404, detail="盘点单不存在")

        # 权限控制：非管理员用户只能批量盘点自己门店的商品
        if current_user.store_id and existing_check.store_id != current_user.store_id:
            raise HTTPException(status_code=403, detail="无权批量盘点其他门店的商品")

        result = service.batch_check_items(check_id, batch_data, current_user.id)

        return StandardResponse(
            success=True,
            code=200,
            message="批量盘点成功",
            data=result
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量盘点失败: {str(e)}")


@router.get("/statistics/summary", response_model=StandardResponse[InventoryCheckStatistics], summary="获取盘点统计")
async def get_inventory_check_statistics(
    store_id: Optional[int] = Query(None, description="门店ID筛选"),
    current_user: CurrentUserResponse = Depends(require_permission("inventory.view")),
    service: InventoryCheckService = Depends(get_inventory_check_service)
) -> Any:
    """
    获取库存盘点统计信息

    **统计内容:**
    - 盘点单数量统计(总数、进行中、已完成、已取消)
    - 商品盘点统计(总数、已盘点、有差异)
    - 状态分布统计
    - 门店分布统计
    - 盘点进度统计
    - 完成率统计

    **使用场景:**
    - 管理仪表板
    - 盘点进度监控
    - 业务分析报告
    """
    try:
        # 权限控制：非管理员用户只能查询自己门店的统计
        if current_user.store_id and store_id and store_id != current_user.store_id:
            raise HTTPException(status_code=403, detail="无权查询其他门店的统计信息")

        # 如果用户有分配门店且未指定store_id，自动使用用户门店
        if current_user.store_id and not store_id:
            store_id = current_user.store_id

        result = service.get_statistics(store_id)

        return StandardResponse(
            success=True,
            code=200,
            message="获取盘点统计成功",
            data=result
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取盘点统计失败: {str(e)}")
