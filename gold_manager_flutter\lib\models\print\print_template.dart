import 'package:pdf/pdf.dart';

/// 打印模板配置
class PrintTemplate {
  /// 模板ID
  final String id;
  
  /// 模板名称
  final String name;
  
  /// 模板描述
  final String? description;
  
  /// 纸张格式
  final PdfPageFormat pageFormat;
  
  /// 页边距
  final PrintMargins margins;
  
  /// 字体配置
  final PrintFontConfig fontConfig;
  
  /// 公司信息配置
  final CompanyInfoConfig companyInfo;
  
  /// 表格配置
  final TableConfig tableConfig;
  
  /// 是否显示页眉
  final bool showHeader;
  
  /// 是否显示页脚
  final bool showFooter;
  
  /// 是否显示汇总信息
  final bool showSummary;
  
  /// 创建时间
  final DateTime createTime;
  
  /// 更新时间
  final DateTime updateTime;

  const PrintTemplate({
    required this.id,
    required this.name,
    this.description,
    required this.pageFormat,
    required this.margins,
    required this.fontConfig,
    required this.companyInfo,
    required this.tableConfig,
    this.showHeader = true,
    this.showFooter = true,
    this.showSummary = true,
    required this.createTime,
    required this.updateTime,
  });

  /// 从JSON构造
  factory PrintTemplate.fromJson(Map<String, dynamic> json) {
    return PrintTemplate(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      pageFormat: _parsePageFormat(json['page_format']),
      margins: PrintMargins.fromJson(json['margins']),
      fontConfig: PrintFontConfig.fromJson(json['font_config']),
      companyInfo: CompanyInfoConfig.fromJson(json['company_info']),
      tableConfig: TableConfig.fromJson(json['table_config']),
      showHeader: json['show_header'] ?? true,
      showFooter: json['show_footer'] ?? true,
      showSummary: json['show_summary'] ?? true,
      createTime: DateTime.fromMillisecondsSinceEpoch(json['create_time']),
      updateTime: DateTime.fromMillisecondsSinceEpoch(json['update_time']),
    );
  }

  /// 转为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'page_format': _pageFormatToString(pageFormat),
      'margins': margins.toJson(),
      'font_config': fontConfig.toJson(),
      'company_info': companyInfo.toJson(),
      'table_config': tableConfig.toJson(),
      'show_header': showHeader,
      'show_footer': showFooter,
      'show_summary': showSummary,
      'create_time': createTime.millisecondsSinceEpoch,
      'update_time': updateTime.millisecondsSinceEpoch,
    };
  }

  /// 解析页面格式
  static PdfPageFormat _parsePageFormat(String format) {
    switch (format.toLowerCase()) {
      case 'a4':
        return PdfPageFormat.a4;
      case 'a5':
        return PdfPageFormat.a5;
      case 'letter':
        return PdfPageFormat.letter;
      default:
        return PdfPageFormat.a4;
    }
  }

  /// 页面格式转字符串
  static String _pageFormatToString(PdfPageFormat format) {
    if (format == PdfPageFormat.a4) return 'a4';
    if (format == PdfPageFormat.a5) return 'a5';
    if (format == PdfPageFormat.letter) return 'letter';
    return 'a4';
  }

  /// 复制并修改属性
  PrintTemplate copyWith({
    String? id,
    String? name,
    String? description,
    PdfPageFormat? pageFormat,
    PrintMargins? margins,
    PrintFontConfig? fontConfig,
    CompanyInfoConfig? companyInfo,
    TableConfig? tableConfig,
    bool? showHeader,
    bool? showFooter,
    bool? showSummary,
    DateTime? createTime,
    DateTime? updateTime,
  }) {
    return PrintTemplate(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      pageFormat: pageFormat ?? this.pageFormat,
      margins: margins ?? this.margins,
      fontConfig: fontConfig ?? this.fontConfig,
      companyInfo: companyInfo ?? this.companyInfo,
      tableConfig: tableConfig ?? this.tableConfig,
      showHeader: showHeader ?? this.showHeader,
      showFooter: showFooter ?? this.showFooter,
      showSummary: showSummary ?? this.showSummary,
      createTime: createTime ?? this.createTime,
      updateTime: updateTime ?? this.updateTime,
    );
  }
}

/// 页边距配置
class PrintMargins {
  final double top;
  final double bottom;
  final double left;
  final double right;

  const PrintMargins({
    required this.top,
    required this.bottom,
    required this.left,
    required this.right,
  });

  factory PrintMargins.fromJson(Map<String, dynamic> json) {
    return PrintMargins(
      top: (json['top'] ?? 32.0).toDouble(),
      bottom: (json['bottom'] ?? 32.0).toDouble(),
      left: (json['left'] ?? 32.0).toDouble(),
      right: (json['right'] ?? 32.0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'top': top,
      'bottom': bottom,
      'left': left,
      'right': right,
    };
  }
}

/// 字体配置
class PrintFontConfig {
  final double titleSize;
  final double headerSize;
  final double bodySize;
  final double footerSize;
  final String fontFamily;

  const PrintFontConfig({
    this.titleSize = 24.0,
    this.headerSize = 14.0,
    this.bodySize = 12.0,
    this.footerSize = 10.0,
    this.fontFamily = 'default',
  });

  factory PrintFontConfig.fromJson(Map<String, dynamic> json) {
    return PrintFontConfig(
      titleSize: (json['title_size'] ?? 24.0).toDouble(),
      headerSize: (json['header_size'] ?? 14.0).toDouble(),
      bodySize: (json['body_size'] ?? 12.0).toDouble(),
      footerSize: (json['footer_size'] ?? 10.0).toDouble(),
      fontFamily: json['font_family'] ?? 'default',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title_size': titleSize,
      'header_size': headerSize,
      'body_size': bodySize,
      'footer_size': footerSize,
      'font_family': fontFamily,
    };
  }
}

/// 公司信息配置
class CompanyInfoConfig {
  final String companyName;
  final String? address;
  final String? phone;
  final String? email;
  final bool showLogo;
  final String? logoPath;

  const CompanyInfoConfig({
    required this.companyName,
    this.address,
    this.phone,
    this.email,
    this.showLogo = false,
    this.logoPath,
  });

  factory CompanyInfoConfig.fromJson(Map<String, dynamic> json) {
    return CompanyInfoConfig(
      companyName: json['company_name'] ?? '金包银首饰管理系统',
      address: json['address'],
      phone: json['phone'],
      email: json['email'],
      showLogo: json['show_logo'] ?? false,
      logoPath: json['logo_path'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'company_name': companyName,
      'address': address,
      'phone': phone,
      'email': email,
      'show_logo': showLogo,
      'logo_path': logoPath,
    };
  }
}

/// 表格配置
class TableConfig {
  final bool showBorder;
  final double borderWidth;
  final bool showHeader;
  final List<String> visibleColumns;
  final Map<String, double> columnWidths;

  const TableConfig({
    this.showBorder = true,
    this.borderWidth = 0.5,
    this.showHeader = true,
    required this.visibleColumns,
    required this.columnWidths,
  });

  factory TableConfig.fromJson(Map<String, dynamic> json) {
    return TableConfig(
      showBorder: json['show_border'] ?? true,
      borderWidth: (json['border_width'] ?? 0.5).toDouble(),
      showHeader: json['show_header'] ?? true,
      visibleColumns: List<String>.from(json['visible_columns'] ?? []),
      columnWidths: Map<String, double>.from(
        (json['column_widths'] ?? {}).map((k, v) => MapEntry(k, v.toDouble())),
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'show_border': showBorder,
      'border_width': borderWidth,
      'show_header': showHeader,
      'visible_columns': visibleColumns,
      'column_widths': columnWidths,
    };
  }
}

/// 默认模板
class DefaultPrintTemplates {
  /// 标准A4模板
  static PrintTemplate get standardA4 => PrintTemplate(
    id: 'standard_a4',
    name: '标准A4模板',
    description: '适用于A4纸张的标准打印模板',
    pageFormat: PdfPageFormat.a4,
    margins: const PrintMargins(top: 32, bottom: 32, left: 32, right: 32),
    fontConfig: const PrintFontConfig(),
    companyInfo: const CompanyInfoConfig(companyName: '金包银首饰管理系统'),
    tableConfig: const TableConfig(
      visibleColumns: ['序号', '条码', '商品名称', '分类', '圈口', '金重', '银重', '总重', '总成本'],
      columnWidths: {
        '序号': 30,
        '条码': 80,
        '商品名称': 100,
        '分类': 50,
        '圈口': 40,
        '金重': 40,
        '银重': 40,
        '总重': 50,
        '总成本': 60,
      },
    ),
    createTime: DateTime.now(),
    updateTime: DateTime.now(),
  );

  /// 紧凑A5模板
  static PrintTemplate get compactA5 => PrintTemplate(
    id: 'compact_a5',
    name: '紧凑A5模板',
    description: '适用于A5纸张的紧凑打印模板',
    pageFormat: PdfPageFormat.a5,
    margins: const PrintMargins(top: 20, bottom: 20, left: 20, right: 20),
    fontConfig: const PrintFontConfig(
      titleSize: 18,
      headerSize: 12,
      bodySize: 10,
      footerSize: 8,
    ),
    companyInfo: const CompanyInfoConfig(companyName: '金包银首饰管理系统'),
    tableConfig: const TableConfig(
      visibleColumns: ['序号', '商品名称', '金重', '银重', '总成本'],
      columnWidths: {
        '序号': 25,
        '商品名称': 80,
        '金重': 35,
        '银重': 35,
        '总成本': 50,
      },
    ),
    createTime: DateTime.now(),
    updateTime: DateTime.now(),
  );
}
