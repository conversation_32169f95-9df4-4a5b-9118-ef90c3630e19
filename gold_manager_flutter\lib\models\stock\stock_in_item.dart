import '../jewelry/jewelry.dart';

/// 入库单商品项模型
class StockInItem {
  /// ID
  final int id;

  /// 入库单ID
  final int stockInId;

  /// 首饰ID
  final int jewelryId;

  /// 商品条码
  final String barcode;

  /// 商品名称
  final String name;

  /// 分类ID
  final int categoryId;

  /// 分类名称
  final String? categoryName;

  /// 圈口号
  final String? ringSize;

  /// 金重(克)
  final double? goldWeight;

  /// 金进价(克价)
  final double? goldPrice;

  /// 金成本
  final double? goldCost;

  /// 银重(克)
  final double? silverWeight;

  /// 总重
  final double? totalWeight;

  /// 银进价(克价)
  final double? silverPrice;

  /// 银成本
  final double? silverCost;

  /// 银工费方式:0=按克,1=按件
  final int? silverWorkType;

  /// 银工费
  final double? silverWorkPrice;

  /// 银工费成本
  final double? silverWorkCost;

  /// 电铸费
  final double? platingCost;

  /// 进货总成本
  final double? totalCost;

  /// 批发工费
  final double? wholesaleWorkPrice;

  /// 零售工费
  final double? retailWorkPrice;

  /// 件工费
  final double? pieceWorkPrice;

  /// 总价
  final double? totalPrice;

  /// 备注
  final String? remark;

  /// 创建时间
  final DateTime? createTime;

  /// 关联首饰
  final Jewelry? jewelry;

  /// 金额（向后兼容性，等同于totalPrice）
  double get amount => totalPrice ?? totalCost ?? 0.0;

  /// 构造函数
  const StockInItem({
    required this.id,
    required this.stockInId,
    required this.jewelryId,
    required this.barcode,
    required this.name,
    required this.categoryId,
    this.categoryName,
    this.ringSize,
    this.goldWeight,
    this.goldPrice,
    this.goldCost,
    this.silverWeight,
    this.totalWeight,
    this.silverPrice,
    this.silverCost,
    this.silverWorkType,
    this.silverWorkPrice,
    this.silverWorkCost,
    this.platingCost,
    this.totalCost,
    this.wholesaleWorkPrice,
    this.retailWorkPrice,
    this.pieceWorkPrice,
    this.totalPrice,
    this.remark,
    this.createTime,
    this.jewelry,
  });
  
  /// 从JSON构造
  factory StockInItem.fromJson(Map<String, dynamic> json) {
    return StockInItem(
      id: json['id'] ?? 0,
      stockInId: json['stock_in_id'] ?? 0,
      jewelryId: json['jewelry_id'] ?? 0,
      barcode: json['barcode'] ?? '',
      name: json['name'] ?? '',
      categoryId: json['category_id'] ?? 0,
      categoryName: json['category_name'],
      ringSize: json['ring_size'],
      goldWeight: _parseDouble(json['gold_weight']),
      goldPrice: _parseDouble(json['gold_price']),
      goldCost: _parseDouble(json['gold_cost']),
      silverWeight: _parseDouble(json['silver_weight']),
      totalWeight: _parseDouble(json['total_weight']),
      silverPrice: _parseDouble(json['silver_price']),
      silverCost: _parseDouble(json['silver_cost']),
      silverWorkType: json['silver_work_type'],
      silverWorkPrice: _parseDouble(json['silver_work_price']),
      silverWorkCost: _parseDouble(json['silver_work_cost']),
      platingCost: _parseDouble(json['plating_cost']),
      totalCost: _parseDouble(json['total_cost']),
      wholesaleWorkPrice: _parseDouble(json['wholesale_work_price']),
      retailWorkPrice: _parseDouble(json['retail_work_price']),
      pieceWorkPrice: _parseDouble(json['piece_work_price']),
      totalPrice: _parseDouble(json['total_cost']), // 使用total_cost作为总价
      remark: json['remark'],
      createTime: json['createtime'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['createtime'] * 1000)
          : null,
      jewelry: json['jewelry'] != null ? Jewelry.fromJson(json['jewelry']) : null,
    );
  }

  /// 解析双精度浮点数
  static double? _parseDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value);
    return null;
  }
  
  /// 转为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'stock_in_id': stockInId,
      'jewelry_id': jewelryId,
      'barcode': barcode,
      'name': name,
      'category_id': categoryId,
      'ring_size': ringSize,
      'gold_weight': goldWeight,
      'gold_price': goldPrice,
      'gold_cost': goldCost,
      'silver_weight': silverWeight,
      'total_weight': totalWeight,
      'silver_price': silverPrice,
      'silver_cost': silverCost,
      'silver_work_type': silverWorkType,
      'silver_work_price': silverWorkPrice,
      'silver_work_cost': silverWorkCost,
      'plating_cost': platingCost,
      'total_cost': totalCost,
      'wholesale_work_price': wholesaleWorkPrice,
      'retail_work_price': retailWorkPrice,
      'piece_work_price': pieceWorkPrice,
      'remark': remark,
    };
  }

  /// 复制并修改属性
  StockInItem copyWith({
    int? id,
    int? stockInId,
    int? jewelryId,
    String? barcode,
    String? name,
    int? categoryId,
    String? categoryName,
    String? ringSize,
    double? goldWeight,
    double? goldPrice,
    double? goldCost,
    double? silverWeight,
    double? totalWeight,
    double? silverPrice,
    double? silverCost,
    int? silverWorkType,
    double? silverWorkPrice,
    double? silverWorkCost,
    double? platingCost,
    double? totalCost,
    double? wholesaleWorkPrice,
    double? retailWorkPrice,
    double? pieceWorkPrice,
    double? totalPrice,
    String? remark,
    DateTime? createTime,
    Jewelry? jewelry,
  }) {
    return StockInItem(
      id: id ?? this.id,
      stockInId: stockInId ?? this.stockInId,
      jewelryId: jewelryId ?? this.jewelryId,
      barcode: barcode ?? this.barcode,
      name: name ?? this.name,
      categoryId: categoryId ?? this.categoryId,
      categoryName: categoryName ?? this.categoryName,
      ringSize: ringSize ?? this.ringSize,
      goldWeight: goldWeight ?? this.goldWeight,
      goldPrice: goldPrice ?? this.goldPrice,
      goldCost: goldCost ?? this.goldCost,
      silverWeight: silverWeight ?? this.silverWeight,
      totalWeight: totalWeight ?? this.totalWeight,
      silverPrice: silverPrice ?? this.silverPrice,
      silverCost: silverCost ?? this.silverCost,
      silverWorkType: silverWorkType ?? this.silverWorkType,
      silverWorkPrice: silverWorkPrice ?? this.silverWorkPrice,
      silverWorkCost: silverWorkCost ?? this.silverWorkCost,
      platingCost: platingCost ?? this.platingCost,
      totalCost: totalCost ?? this.totalCost,
      wholesaleWorkPrice: wholesaleWorkPrice ?? this.wholesaleWorkPrice,
      retailWorkPrice: retailWorkPrice ?? this.retailWorkPrice,
      pieceWorkPrice: pieceWorkPrice ?? this.pieceWorkPrice,
      totalPrice: totalPrice ?? this.totalPrice,
      remark: remark ?? this.remark,
      createTime: createTime ?? this.createTime,
      jewelry: jewelry ?? this.jewelry,
    );
  }
} 