"""
管理员管理相关的数据验证模型
定义管理员的创建、更新、响应等数据结构
"""

from typing import Optional, List
from pydantic import BaseModel, Field, validator, EmailStr
from datetime import datetime
import re


class AdminBase(BaseModel):
    """管理员基础模型"""
    username: str = Field(..., min_length=3, max_length=20, description="用户名")
    nickname: str = Field(..., min_length=1, max_length=50, description="昵称")
    email: Optional[str] = Field(None, max_length=100, description="电子邮箱")
    mobile: Optional[str] = Field(None, max_length=11, description="手机号码")
    status: str = Field("normal", description="状态:normal=正常,hidden=禁用")
    store_id: Optional[int] = Field(None, description="所属门店ID")


class AdminCreate(AdminBase):
    """创建管理员的请求模型"""
    password: str = Field(..., min_length=6, max_length=32, description="密码")
    
    @validator('username')
    def validate_username(cls, v):
        if not v or not v.strip():
            raise ValueError('用户名不能为空')
        username = v.strip()
        # 用户名格式验证：只允许字母、数字和下划线
        if not re.match(r'^[a-zA-Z0-9_]+$', username):
            raise ValueError('用户名只能包含字母、数字和下划线')
        return username
    
    @validator('nickname')
    def validate_nickname(cls, v):
        if not v or not v.strip():
            raise ValueError('昵称不能为空')
        return v.strip()
    
    @validator('email')
    def validate_email(cls, v):
        if v and len(v.strip()) > 0:
            email = v.strip()
            # 简单的邮箱格式验证
            if not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email):
                raise ValueError('邮箱格式不正确')
            return email
        return v
    
    @validator('mobile')
    def validate_mobile(cls, v):
        if v and len(v.strip()) > 0:
            mobile = v.strip()
            # 手机号验证：11位数字，以1开头
            if not re.match(r'^1[3-9]\d{9}$', mobile):
                raise ValueError('手机号格式不正确')
            return mobile
        return v
    
    @validator('status')
    def validate_status(cls, v):
        if v not in ['normal', 'hidden']:
            raise ValueError('状态只能是normal或hidden')
        return v
    
    @validator('password')
    def validate_password(cls, v):
        if not v or len(v.strip()) < 6:
            raise ValueError('密码长度至少6位')
        return v.strip()


class AdminUpdate(BaseModel):
    """更新管理员的请求模型"""
    username: Optional[str] = Field(None, min_length=3, max_length=20, description="用户名")
    nickname: Optional[str] = Field(None, min_length=1, max_length=50, description="昵称")
    email: Optional[str] = Field(None, max_length=100, description="电子邮箱")
    mobile: Optional[str] = Field(None, max_length=11, description="手机号码")
    status: Optional[str] = Field(None, description="状态:normal=正常,hidden=禁用")
    store_id: Optional[int] = Field(None, description="所属门店ID")
    password: Optional[str] = Field(None, min_length=6, max_length=32, description="新密码(可选)")
    
    @validator('username')
    def validate_username(cls, v):
        if v is not None and (not v or not v.strip()):
            raise ValueError('用户名不能为空')
        if v:
            username = v.strip()
            if not re.match(r'^[a-zA-Z0-9_]+$', username):
                raise ValueError('用户名只能包含字母、数字和下划线')
            return username
        return v
    
    @validator('nickname')
    def validate_nickname(cls, v):
        if v is not None and (not v or not v.strip()):
            raise ValueError('昵称不能为空')
        return v.strip() if v else v
    
    @validator('email')
    def validate_email(cls, v):
        if v and len(v.strip()) > 0:
            email = v.strip()
            if not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email):
                raise ValueError('邮箱格式不正确')
            return email
        return v
    
    @validator('mobile')
    def validate_mobile(cls, v):
        if v and len(v.strip()) > 0:
            mobile = v.strip()
            if not re.match(r'^1[3-9]\d{9}$', mobile):
                raise ValueError('手机号格式不正确')
            return mobile
        return v
    
    @validator('status')
    def validate_status(cls, v):
        if v is not None and v not in ['normal', 'hidden']:
            raise ValueError('状态只能是normal或hidden')
        return v
    
    @validator('password')
    def validate_password(cls, v):
        if v is not None and len(v.strip()) < 6:
            raise ValueError('密码长度至少6位')
        return v.strip() if v else v


class AdminResponse(AdminBase):
    """管理员响应模型"""
    id: int = Field(..., description="管理员ID")
    avatar: Optional[str] = Field(None, description="头像")
    loginfailure: int = Field(0, description="失败次数")
    logintime: Optional[int] = Field(None, description="登录时间戳")
    loginip: Optional[str] = Field(None, description="登录IP")
    createtime: Optional[int] = Field(None, description="创建时间戳")
    updatetime: Optional[int] = Field(None, description="更新时间戳")
    
    # 扩展信息
    store_name: Optional[str] = Field(None, description="所属门店名称")
    last_login_time: Optional[str] = Field(None, description="最后登录时间(格式化)")
    status_name: Optional[str] = Field(None, description="状态名称")
    
    class Config:
        from_attributes = True


class AdminListResponse(BaseModel):
    """管理员列表响应模型"""
    items: List[AdminResponse] = Field(..., description="管理员列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页")
    page_size: int = Field(..., description="每页数量")
    total_pages: int = Field(..., description="总页数")


class AdminStatistics(BaseModel):
    """管理员统计信息模型"""
    total_admins: int = Field(0, description="管理员总数")
    normal_admins: int = Field(0, description="正常管理员数")
    hidden_admins: int = Field(0, description="禁用管理员数")
    store_distribution: dict = Field({}, description="门店分布")
    recent_logins: int = Field(0, description="近7天登录人数")


class AdminPasswordUpdate(BaseModel):
    """管理员密码更新模型"""
    old_password: str = Field(..., description="原密码")
    new_password: str = Field(..., min_length=6, max_length=32, description="新密码")
    
    @validator('new_password')
    def validate_new_password(cls, v):
        if not v or len(v.strip()) < 6:
            raise ValueError('新密码长度至少6位')
        return v.strip()


class AdminLoginInfo(BaseModel):
    """管理员登录信息模型"""
    admin_id: int = Field(..., description="管理员ID")
    login_ip: str = Field(..., description="登录IP")
    user_agent: Optional[str] = Field(None, description="用户代理")
    success: bool = Field(True, description="是否成功") 