import 'package:get/get.dart';

import '../core/services/api_service.dart';
import '../core/utils/logger.dart';
import '../models/stock/stock_out.dart';

/// 出库单服务
class StockOutService extends GetxService {
  final ApiService _apiService = Get.find<ApiService>();

  /// 获取出库单详情
  Future<StockOut> getStockOutDetail(int id) async {
    try {
      LoggerService.d('获取出库单详情: ID=$id');
      final response = await _apiService.get('/api/v1/stock-out/$id');
      
      // 后端直接返回出库单数据，不是包装在{code: 0, data: ...}格式中
      if (response.statusCode == 200 && response.data != null) {
        LoggerService.d('出库单详情获取成功');
        return StockOut.fromJson(response.data);
      } else {
        throw Exception('获取出库单详情失败');
      }
    } catch (e) {
      LoggerService.e('获取出库单详情失败', e);
      rethrow;
    }
  }

  /// 获取出库单列表
  Future<Map<String, dynamic>> getStockOutList({
    int page = 1,
    int pageSize = 20,
    int? storeId,
    String? status,
    String? keyword,
    String? startDate,
    String? endDate,
  }) async {
    try {
      final params = <String, dynamic>{
        'page': page,
        'page_size': pageSize,
      };

      if (storeId != null && storeId > 0) {
        params['store_id'] = storeId;
      }
      if (status != null && status.isNotEmpty) {
        params['status'] = status;
      }
      if (keyword != null && keyword.isNotEmpty) {
        params['keyword'] = keyword;
      }
      if (startDate != null && startDate.isNotEmpty) {
        params['start_date'] = startDate;
      }
      if (endDate != null && endDate.isNotEmpty) {
        params['end_date'] = endDate;
      }

      LoggerService.d('获取出库单列表, 参数: $params');
      final response = await _apiService.get('/api/v1/stock-out', queryParameters: params);

      if (response.data['success'] == true) {
        return {
          'list': (response.data['data'] as List)
              .map((item) => StockOut.fromJson(item))
              .toList(),
          'total': response.data['pagination']['total'] ?? 0,
          'page': response.data['pagination']['page'] ?? page,
          'page_size': response.data['pagination']['page_size'] ?? pageSize,
        };
      } else {
        throw Exception(response.data['message'] ?? '获取出库单列表失败');
      }
    } catch (e) {
      LoggerService.e('获取出库单列表失败', e);
      rethrow;
    }
  }

  /// 创建出库单
  Future<bool> createStockOut(Map<String, dynamic> data) async {
    try {
      LoggerService.d('创建出库单: $data');
      final response = await _apiService.post('/api/v1/stock-out', data: data);
      
      if (response.statusCode == 200 && response.data != null) {
        LoggerService.i('出库单创建成功');
        return true;
      } else {
        throw Exception('创建出库单失败');
      }
    } catch (e) {
      LoggerService.e('创建出库单失败', e);
      rethrow;
    }
  }

  /// 更新出库单
  Future<bool> updateStockOut(int id, Map<String, dynamic> data) async {
    try {
      LoggerService.d('更新出库单: ID=$id');
      final response = await _apiService.put('/api/v1/stock-out/$id', data: data);
      
      if (response.statusCode == 200 && response.data != null) {
        LoggerService.i('出库单更新成功');
        return true;
      } else {
        throw Exception('更新出库单失败');
      }
    } catch (e) {
      LoggerService.e('更新出库单失败', e);
      rethrow;
    }
  }

  /// 删除出库单
  Future<bool> deleteStockOut(int id) async {
    try {
      LoggerService.d('删除出库单: ID=$id');
      final response = await _apiService.delete('/api/v1/stock-out/$id');
      
      if (response.data['success'] == true) {
        LoggerService.i('出库单删除成功');
        return true;
      } else {
        throw Exception(response.data['message'] ?? '删除出库单失败');
      }
    } catch (e) {
      LoggerService.e('删除出库单失败', e);
      rethrow;
    }
  }

  /// 审核出库单
  Future<bool> approveStockOut(int id, bool approve, String? reason) async {
    try {
      LoggerService.d('审核出库单: ID=$id, 通过=$approve');
      final response = await _apiService.patch(
        '/api/v1/stock-out/$id/audit',
        data: {
          'status': approve ? 2 : 3,  // 2=已通过, 3=未通过
          'audit_reason': reason,
        },
      );
      
      if (response.statusCode == 200 && response.data != null) {
        LoggerService.i('出库单审核成功');
        return true;
      } else {
        throw Exception('审核出库单失败');
      }
    } catch (e) {
      LoggerService.e('审核出库单失败', e);
      rethrow;
    }
  }
}