import 'package:flutter/material.dart';

/// 自定义应用栏
class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  /// 标题
  final String title;
  
  /// 操作按钮列表
  final List<Widget>? actions;
  
  /// 是否显示返回按钮
  final bool showBackButton;
  
  /// 返回按钮回调
  final VoidCallback? onBackPressed;
  
  /// 应用栏高度
  final double height;
  
  /// 构造函数
  const CustomAppBar({
    super.key,
    required this.title,
    this.actions,
    this.showBackButton = true,
    this.onBackPressed,
    this.height = kToolbarHeight,
  });
  
  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Text(title),
      actions: actions,
      automaticallyImplyLeading: showBackButton,
      leading: showBackButton
          ? IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
            )
          : null,
      elevation: 1,
    );
  }
  
  @override
  Size get preferredSize => Size.fromHeight(height);
} 