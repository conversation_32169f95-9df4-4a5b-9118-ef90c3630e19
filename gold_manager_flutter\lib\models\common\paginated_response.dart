/// 分页响应数据模型
class PaginatedResponse<T> {
  /// 数据列表
  final List<T> data;
  
  /// 当前页码
  final int currentPage;
  
  /// 最后一页页码
  final int lastPage;
  
  /// 每页数据量
  final int perPage;
  
  /// 总数据量
  final int total;
  
  /// 当前页数据量
  final int count;
  
  /// 是否有下一页
  bool get hasNextPage => currentPage < lastPage;
  
  /// 是否有上一页
  bool get hasPreviousPage => currentPage > 1;

  const PaginatedResponse({
    required this.data,
    required this.currentPage,
    required this.lastPage,
    this.perPage = 20,
    this.total = 0,
    int? count,
  }) : count = count ?? data.length;

  /// 从JSON创建对象
  factory PaginatedResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Map<String, dynamic>) fromJsonT,
  ) {
    final dataList = json['data'] as List? ?? [];
    return PaginatedResponse<T>(
      data: dataList.map((item) => fromJsonT(item as Map<String, dynamic>)).toList(),
      currentPage: json['current_page'] ?? json['page'] ?? 1,
      lastPage: json['last_page'] ?? json['total_pages'] ?? 1,
      perPage: json['per_page'] ?? json['page_size'] ?? 20,
      total: json['total'] ?? json['total_count'] ?? 0,
      count: json['count'] ?? dataList.length,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson(Map<String, dynamic> Function(T) toJsonT) {
    return {
      'data': data.map((item) => toJsonT(item)).toList(),
      'current_page': currentPage,
      'last_page': lastPage,
      'per_page': perPage,
      'total': total,
      'count': count,
    };
  }

  /// 复制并修改部分属性
  PaginatedResponse<T> copyWith({
    List<T>? data,
    int? currentPage,
    int? lastPage,
    int? perPage,
    int? total,
    int? count,
  }) {
    return PaginatedResponse<T>(
      data: data ?? this.data,
      currentPage: currentPage ?? this.currentPage,
      lastPage: lastPage ?? this.lastPage,
      perPage: perPage ?? this.perPage,
      total: total ?? this.total,
      count: count ?? this.count,
    );
  }

  @override
  String toString() {
    return 'PaginatedResponse<$T>('
        'data: ${data.length} items, '
        'currentPage: $currentPage, '
        'lastPage: $lastPage, '
        'total: $total'
        ')';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PaginatedResponse<T> &&
        other.data == data &&
        other.currentPage == currentPage &&
        other.lastPage == lastPage &&
        other.perPage == perPage &&
        other.total == total &&
        other.count == count;
  }

  @override
  int get hashCode {
    return Object.hash(
      data,
      currentPage,
      lastPage,
      perPage,
      total,
      count,
    );
  }
}
