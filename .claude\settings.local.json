{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["Bash(rg:*)", "Bash(find:*)", "Bash(grep:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./scripts/install_dependencies.sh:*)", "<PERSON><PERSON>(dos2unix:*)", "Bash(./jewelry_env/Scripts/pip.exe install fastapi-guard==3.0.1 prometheus-client==0.19.0 prometheus-fastapi-instrumentator==6.1.0 ruff==0.1.9)", "Bash(./jewelry_env/Scripts/ruff.exe check app/ --statistics)", "Bash(./jewelry_env/Scripts/ruff.exe format app/)", "Bash(./jewelry_env/Scripts/python.exe main.py)", "Bash(./jewelry_env/Scripts/pip.exe list)", "Bash(./jewelry_env/Scripts/python.exe -c \"import fastapi_guard; print(dir(fastapi_guard))\")", "Bash(./jewelry_env/Scripts/python.exe -c \"import guard; print(dir(guard))\")", "Bash(./jewelry_env/Scripts/python.exe -c \"from guard import SecurityConfig; print(SecurityConfig.__doc__); print(SecurityConfig.__init__.__doc__)\")", "Bash(./jewelry_env/Scripts/python.exe -c \"from guard import SecurityConfig; import inspect; print(inspect.signature(SecurityConfig.__init__))\")", "Bash(./jewelry_env/Scripts/python.exe -c \"from guard import SecurityConfig; config = SecurityConfig(); print(config.model_dump())\")", "<PERSON><PERSON>(curl:*)", "Bash(ls:*)", "Bash(ss:*)", "Bash(export ENVIRONMENT=development)", "<PERSON><PERSON>(mv:*)", "Bash(claude mcp add:*)", "<PERSON><PERSON>(claude mcp)", "<PERSON><PERSON>(claude mcp:*)", "Bash(npx:*)", "Bash(node:*)", "Bash(where claude)", "<PERSON><PERSON>(claude auth status)", "Bash(claude --version)", "Bash(\"jewelry_env\\Scripts\\activate\")", "Bash(python main.py)", "<PERSON><PERSON>(flutter run:*)", "Bash(pip install fastapi-guard==3.0.1)", "Ba<PERSON>(flutter:*)", "Bash(jewelry_envScriptsactivate)", "Bash(.jewelry_envScriptsactivate.bat)", "Bash(where flutter)", "Ba<PERSON>(C:flutterbinflutter.bat doctor)", "Bash(cmd /c:*)", "Bash(del /s /q build)", "Bash(rm:*)", "Bash(powershell -Command \"& {flutter pub get}\")", "Bash(powershell.exe -Command \"flutter pub get; flutter run -d windows\")", "Ba<PERSON>(start_flutter.bat)", "Bash(cmd.exe:*)", "Bash(C:/flutter/bin/flutter.bat doctor)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(powershell:*)", "Bash(del /F /Q \"C:\\flutter\\bin\\cache\\lockfile\")", "Bash(.gold_manager_flutter.exe)", "Bash(./gold_manager_flutter.exe)", "Bash(C:flutterbinflutter.bat run -d windows)", "Bash(python -c \"from app.services.sales_service import SalesService; print(''SalesService import success'')\")", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(dir /a .claude)", "Bash(npm:*)", "<PERSON><PERSON>(context7-mcp)", "<PERSON><PERSON>(cat:*)", "Bash(where context7-mcp)", "Bash(C:Users12191AppDataRoamingnpmcontext7-mcp.cmd --version)", "Bash(\"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\context7-mcp.cmd\" --version)", "Bash(./start_flutter.bat)", "Bash(git reset:*)", "Bash(copy:*)", "Bash(git restore:*)", "<PERSON><PERSON>(timeout:*)"], "deny": []}}