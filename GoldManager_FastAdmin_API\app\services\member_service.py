"""
会员管理业务逻辑服务
提供会员相关的数据操作和业务逻辑
"""

import time
import math
from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc
from loguru import logger
from datetime import date, datetime

from ..models.member import Member
from ..schemas.member import (
    MemberCreate, 
    MemberUpdate, 
    MemberResponse, 
    MemberListResponse,
    MemberStatistics,
    MemberPointsUpdate
)


class MemberService:
    """会员管理服务类"""
    
    # 会员等级名称映射
    LEVEL_NAMES = {
        1: "普通会员",
        2: "银卡会员", 
        3: "金卡会员",
        4: "白金会员",
        5: "钻石会员"
    }
    
    def __init__(self, db: Session):
        self.db = db
    
    def _calculate_age(self, birthday: date) -> Optional[int]:
        """计算年龄"""
        if not birthday:
            return None
        
        today = date.today()
        age = today.year - birthday.year
        if today.month < birthday.month or (today.month == birthday.month and today.day < birthday.day):
            age -= 1
        return age if age >= 0 else None
    
    def _build_member_response(self, member: Member) -> MemberResponse:
        """构建会员响应对象"""
        return MemberResponse(
            id=member.id,
            card_no=member.card_no,
            name=member.name,
            phone=member.phone,
            birthday=member.birthday,
            points=member.points,
            level=member.level,
            status=member.status,
            createtime=member.createtime,
            updatetime=member.updatetime,
            level_name=self.LEVEL_NAMES.get(member.level, f"等级{member.level}"),
            age=self._calculate_age(member.birthday) if member.birthday else None
        )
    
    async def get_members(
        self, 
        page: int = 1, 
        page_size: int = 20,
        status: Optional[int] = None,
        level: Optional[int] = None,
        keyword: Optional[str] = None
    ) -> MemberListResponse:
        """
        获取会员列表
        
        Args:
            page: 页码
            page_size: 每页数量
            status: 状态筛选
            level: 等级筛选
            keyword: 搜索关键词（会员卡号、姓名、电话）
        """
        try:
            # 构建查询条件
            query = self.db.query(Member)
            
            # 状态筛选
            if status is not None:
                query = query.filter(Member.status == status)
            
            # 等级筛选
            if level is not None:
                query = query.filter(Member.level == level)
            
            # 关键词搜索
            if keyword:
                keyword = f"%{keyword.strip()}%"
                query = query.filter(
                    or_(
                        Member.card_no.like(keyword),
                        Member.name.like(keyword),
                        Member.phone.like(keyword)
                    )
                )
            
            # 计算总数
            total = query.count()
            
            # 分页查询
            offset = (page - 1) * page_size
            members = query.order_by(desc(Member.id)).offset(offset).limit(page_size).all()
            
            # 构建响应对象
            member_responses = [self._build_member_response(member) for member in members]
            
            # 计算总页数
            total_pages = math.ceil(total / page_size) if total > 0 else 1
            
            logger.info(f"获取会员列表: 页码={page}, 每页={page_size}, 总数={total}")
            
            return MemberListResponse(
                items=member_responses,
                total=total,
                page=page,
                page_size=page_size,
                total_pages=total_pages
            )
            
        except Exception as e:
            logger.error(f"获取会员列表失败: {str(e)}")
            raise e
    
    async def get_member_by_id(self, member_id: int) -> Optional[MemberResponse]:
        """根据ID获取会员详情"""
        try:
            member = self.db.query(Member).filter(Member.id == member_id).first()
            if not member:
                return None
            
            logger.info(f"获取会员详情: ID={member_id}")
            return self._build_member_response(member)
            
        except Exception as e:
            logger.error(f"获取会员详情失败: member_id={member_id}, error={str(e)}")
            raise e
    
    async def get_member_by_card_no(self, card_no: str) -> Optional[MemberResponse]:
        """根据会员卡号获取会员详情"""
        try:
            member = self.db.query(Member).filter(Member.card_no == card_no.strip()).first()
            if not member:
                return None
            
            logger.info(f"根据卡号获取会员详情: 卡号={card_no}")
            return self._build_member_response(member)
            
        except Exception as e:
            logger.error(f"根据卡号获取会员详情失败: card_no={card_no}, error={str(e)}")
            raise e
    
    async def check_card_no_exists(self, card_no: str, exclude_id: Optional[int] = None) -> bool:
        """检查会员卡号是否已存在"""
        try:
            query = self.db.query(Member).filter(Member.card_no == card_no.strip())
            if exclude_id:
                query = query.filter(Member.id != exclude_id)
            return query.first() is not None
        except Exception as e:
            logger.error(f"检查会员卡号失败: card_no={card_no}, error={str(e)}")
            raise e
    
    async def create_member(self, member_data: MemberCreate) -> MemberResponse:
        """创建新会员"""
        try:
            # 检查会员卡号是否重复
            if await self.check_card_no_exists(member_data.card_no):
                raise ValueError("会员卡号已存在")
            
            # 创建会员对象
            current_time = int(time.time())
            
            db_member = Member(
                card_no=member_data.card_no.strip(),
                name=member_data.name.strip(),
                phone=member_data.phone.strip() if member_data.phone else None,
                birthday=member_data.birthday,
                points=member_data.points,
                level=member_data.level,
                status=member_data.status,
                createtime=current_time,
                updatetime=current_time
            )
            
            # 保存到数据库
            self.db.add(db_member)
            self.db.commit()
            self.db.refresh(db_member)
            
            logger.info(f"创建会员成功: ID={db_member.id}, 卡号={db_member.card_no}, 姓名={db_member.name}")
            
            return self._build_member_response(db_member)
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"创建会员失败: error={str(e)}")
            raise e
    
    async def update_member(self, member_id: int, member_data: MemberUpdate) -> Optional[MemberResponse]:
        """更新会员信息"""
        try:
            # 获取现有会员
            member = self.db.query(Member).filter(Member.id == member_id).first()
            if not member:
                return None
            
            # 检查卡号重复（如果更新了卡号）
            if member_data.card_no and member_data.card_no.strip() != member.card_no:
                if await self.check_card_no_exists(member_data.card_no, member_id):
                    raise ValueError("会员卡号已存在")
            
            # 更新字段
            if member_data.card_no is not None:
                member.card_no = member_data.card_no.strip()
            if member_data.name is not None:
                member.name = member_data.name.strip()
            if member_data.phone is not None:
                member.phone = member_data.phone.strip() if member_data.phone else None
            if member_data.birthday is not None:
                member.birthday = member_data.birthday
            if member_data.points is not None:
                member.points = member_data.points
            if member_data.level is not None:
                member.level = member_data.level
            if member_data.status is not None:
                member.status = member_data.status
            
            # 更新时间戳
            member.updatetime = int(time.time())
            
            # 保存更改
            self.db.commit()
            self.db.refresh(member)
            
            logger.info(f"更新会员成功: ID={member_id}")
            
            return self._build_member_response(member)
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"更新会员失败: member_id={member_id}, error={str(e)}")
            raise e
    
    async def delete_member(self, member_id: int) -> bool:
        """删除会员"""
        try:
            member = self.db.query(Member).filter(Member.id == member_id).first()
            if not member:
                return False
            
            # 这里可以添加更多的关联数据检查
            # 例如检查是否有购买记录、积分变更记录等
            # 目前先简单删除
            
            # 执行删除
            self.db.delete(member)
            self.db.commit()
            
            logger.info(f"删除会员成功: ID={member_id}, 卡号={member.card_no}, 姓名={member.name}")
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"删除会员失败: member_id={member_id}, error={str(e)}")
            raise e
    
    async def update_member_status(self, member_id: int, status: int) -> Optional[MemberResponse]:
        """更新会员状态"""
        try:
            member = self.db.query(Member).filter(Member.id == member_id).first()
            if not member:
                return None
            
            member.status = status
            member.updatetime = int(time.time())
            
            self.db.commit()
            self.db.refresh(member)
            
            logger.info(f"更新会员状态成功: ID={member_id}, 状态={status}")
            
            return self._build_member_response(member)
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"更新会员状态失败: member_id={member_id}, error={str(e)}")
            raise e
    
    async def update_member_points(self, member_id: int, points_data: MemberPointsUpdate) -> Optional[MemberResponse]:
        """更新会员积分"""
        try:
            member = self.db.query(Member).filter(Member.id == member_id).first()
            if not member:
                return None
            
            # 计算新的积分
            new_points = member.points + points_data.points
            if new_points < 0:
                raise ValueError("积分不足，无法扣除")
            
            # 更新积分
            member.points = new_points
            member.updatetime = int(time.time())
            
            # 根据积分自动调整会员等级
            if new_points >= 10000:
                member.level = 5  # 钻石会员
            elif new_points >= 5000:
                member.level = 4  # 白金会员
            elif new_points >= 2000:
                member.level = 3  # 金卡会员
            elif new_points >= 500:
                member.level = 2  # 银卡会员
            else:
                member.level = 1  # 普通会员
            
            self.db.commit()
            self.db.refresh(member)
            
            logger.info(f"更新会员积分成功: ID={member_id}, 变化={points_data.points}, 当前积分={new_points}")
            
            return self._build_member_response(member)
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"更新会员积分失败: member_id={member_id}, error={str(e)}")
            raise e
    
    async def get_member_statistics(self) -> MemberStatistics:
        """获取会员统计信息"""
        try:
            # 基础统计
            total_members = self.db.query(Member).count()
            active_members = self.db.query(Member).filter(Member.status == 1).count()
            disabled_members = self.db.query(Member).filter(Member.status == 0).count()
            
            # 积分统计
            points_result = self.db.query(func.sum(Member.points)).first()
            total_points = points_result[0] if points_result[0] else 0
            avg_points = total_points / total_members if total_members > 0 else 0.0
            
            # 等级分布统计
            level_distribution = {}
            for level in range(1, 6):
                count = self.db.query(Member).filter(Member.level == level).count()
                level_name = self.LEVEL_NAMES.get(level, f"等级{level}")
                level_distribution[level_name] = count
            
            logger.info("获取会员统计信息成功")
            
            return MemberStatistics(
                total_members=total_members,
                active_members=active_members,
                disabled_members=disabled_members,
                total_points=total_points,
                level_distribution=level_distribution,
                avg_points=round(avg_points, 2)
            )
            
        except Exception as e:
            logger.error(f"获取会员统计失败: error={str(e)}")
            raise e 