# 回收系统功能增强实施计划

## 项目概述
本文档记录了回收系统的两个主要功能增强：
1. 回收单列表添加门店筛选功能
2. 处理工单批量处理功能

## 功能一：回收单列表门店筛选功能

### 需求描述
- 在回收单列表页面的操作员标签右侧添加门店下拉框
- 参考"新建出库单"界面的门店下拉框样式（32px高度，紧凑设计）
- 支持按门店筛选回收单数据

### 实施步骤

#### 1. 后端API修改
**文件**: `/mnt/c/WorkSpace/GoldManager/GoldManager_FastAdmin_API/app/services/recycling_service.py`
**修改内容**:
- 在 `get_recycling_orders_with_pagination` 方法添加 `store_id` 参数
- 在查询条件中添加门店筛选逻辑
```python
if store_id:
    query = query.filter(RecyclingOrder.store_id == store_id)
```

#### 2. 前端服务层修改
**文件**: `/mnt/c/WorkSpace/GoldManager/gold_manager_flutter/lib/features/recycling/services/recycling_service.dart`
**修改内容**:
- 在 `getRecyclingOrdersWithPagination` 方法添加 `int? storeId` 参数
- 在请求参数中添加 `if (storeId != null) 'store_id': storeId`

#### 3. 控制器层修改
**文件**: `/mnt/c/WorkSpace/GoldManager/gold_manager_flutter/lib/features/recycling/controllers/recycling_controller.dart`
**修改内容**:
```dart
// 添加状态变量
final RxInt selectedStoreId = 0.obs;
final RxList<Store> storeList = <Store>[].obs;
final StoreService _storeService = Get.find<StoreService>();

// 添加获取门店列表方法
Future<void> fetchStores() async {
  try {
    final stores = await _storeService.getAllStores();
    storeList.value = stores;
  } catch (e) {
    // 错误处理
  }
}

// 修改 fetchRecyclingOrders 方法，添加 storeId 参数
```

#### 4. UI层修改
**文件**: `/mnt/c/WorkSpace/GoldManager/gold_manager_flutter/lib/features/recycling/presentation/recycling_list_page.dart`
**修改内容**:
- 在操作员标签后添加门店下拉框
- 样式参考出库单界面：
```dart
// 门店筛选
const SizedBox(width: 24),
const Text('门店:', style: TextStyle(fontSize: 13, fontWeight: FontWeight.w500)),
const SizedBox(width: 8),
SizedBox(
  width: 150,
  height: 32,
  child: Obx(() => Container(
    height: 32,
    decoration: AppBorderStyles.standardBoxDecoration,
    child: DropdownButtonHideUnderline(
      child: DropdownButton<int>(
        value: controller.selectedStoreId.value == 0 ? null : controller.selectedStoreId.value,
        hint: const Text('全部门店', style: TextStyle(fontSize: 13)),
        isExpanded: true,
        items: [
          const DropdownMenuItem<int>(
            value: 0,
            child: Text('全部门店'),
          ),
          ...controller.storeList.map((store) => DropdownMenuItem<int>(
            value: store.id,
            child: Text(store.name),
          )),
        ],
        onChanged: (value) {
          controller.selectedStoreId.value = value ?? 0;
          controller.fetchRecyclingOrders();
        },
      ),
    ),
  )),
),
```

## 功能二：处理工单批量处理功能

### 需求描述
- 支持批量选择处理工单（复选框 + Ctrl/Shift快捷键）
- 批量操作包括：金银分离、翻新加工、直接熔炼、直接卖出
- 权限控制：
  - 金银分离/翻新加工/熔炼：需要 `recycling.process.edit` 权限
  - 直接卖出：需要 `recycling.process.complete` 权限

### 实施步骤

#### 1. 后端批量处理API
**文件**: `/mnt/c/WorkSpace/GoldManager/GoldManager_FastAdmin_API/app/api/api_v1/endpoints/recycling_process.py`
**新增内容**:
```python
@router.post("/process/batch", response_model=dict, summary="批量处理工单")
def batch_process(
    *,
    db: Session = Depends(get_db),
    process_ids: List[int] = Body(..., description="处理工单ID列表"),
    action: str = Body(..., description="操作类型: separate/refurbish/melt/sell"),
    current_user: Admin = Depends(get_current_user)
) -> dict:
    """批量处理工单"""
    # 权限检查
    if action in ['separate', 'refurbish', 'melt']:
        require_permission("recycling.process.edit")(current_user)
    elif action == 'sell':
        require_permission("recycling.process.complete")(current_user)
    
    service = RecyclingProcessService(db)
    result = service.batch_process(process_ids, action, current_user.id)
    return result
```

#### 2. 后端服务层批量处理
**文件**: `/mnt/c/WorkSpace/GoldManager/GoldManager_FastAdmin_API/app/services/recycling_process_service.py`
**新增内容**:
```python
def batch_process(self, process_ids: List[int], action: str, operator_id: int) -> dict:
    """批量处理工单"""
    success_count = 0
    failed_count = 0
    errors = []
    
    for process_id in process_ids:
        try:
            # 根据action执行相应操作
            if action == 'separate':
                self._process_separate(process_id, operator_id)
            elif action == 'refurbish':
                self._process_refurbish(process_id, operator_id)
            elif action == 'melt':
                self._process_melt(process_id, operator_id)
            elif action == 'sell':
                self._process_sell(process_id, operator_id)
            
            success_count += 1
        except Exception as e:
            failed_count += 1
            errors.append({"process_id": process_id, "error": str(e)})
    
    self.db.commit()
    
    return {
        "success": success_count,
        "failed": failed_count,
        "errors": errors
    }
```

#### 3. 前端批量选择功能
**文件**: `/mnt/c/WorkSpace/GoldManager/gold_manager_flutter/lib/features/recycling/controllers/recycling_process_new_controller.dart`
**新增内容**:
```dart
// 批量选择相关状态
final RxSet<int> selectedProcessIds = <int>{}.obs;
final RxBool isSelectMode = false.obs;
final RxBool isAllSelected = false.obs;

// 切换选择模式
void toggleSelectMode() {
  isSelectMode.value = !isSelectMode.value;
  if (!isSelectMode.value) {
    selectedProcessIds.clear();
  }
}

// 切换单个选择
void toggleSelection(int processId) {
  if (selectedProcessIds.contains(processId)) {
    selectedProcessIds.remove(processId);
  } else {
    selectedProcessIds.add(processId);
  }
  _updateAllSelectedStatus();
}

// 全选/反选
void toggleSelectAll() {
  if (isAllSelected.value) {
    selectedProcessIds.clear();
  } else {
    selectedProcessIds.addAll(processList.map((p) => p.id));
  }
  isAllSelected.value = !isAllSelected.value;
}

// 批量操作
Future<void> batchProcess(String action) async {
  if (selectedProcessIds.isEmpty) {
    Get.snackbar('提示', '请先选择要处理的工单');
    return;
  }
  
  // 显示确认对话框
  final confirmed = await _showBatchConfirmDialog(action, selectedProcessIds.length);
  if (!confirmed) return;
  
  try {
    isLoading.value = true;
    final result = await _processService.batchProcess(
      selectedProcessIds.toList(),
      action,
    );
    
    if (result['success'] > 0) {
      Get.snackbar('成功', '批量处理完成：成功${result['success']}个，失败${result['failed']}个');
      fetchProcessList(); // 刷新列表
      toggleSelectMode(); // 退出选择模式
    }
  } catch (e) {
    Get.snackbar('错误', '批量处理失败：$e');
  } finally {
    isLoading.value = false;
  }
}
```

#### 4. 前端UI批量操作界面
**文件**: `/mnt/c/WorkSpace/GoldManager/gold_manager_flutter/lib/features/recycling/views/process_list_view.dart`
**修改内容**:

1. 在表格头部添加复选框列：
```dart
// 表格头部
if (controller.isSelectMode.value)
  Container(
    width: 40,
    child: Checkbox(
      value: controller.isAllSelected.value,
      onChanged: (_) => controller.toggleSelectAll(),
    ),
  ),
```

2. 在数据行添加复选框：
```dart
// 数据行
if (controller.isSelectMode.value)
  Container(
    width: 40,
    child: Checkbox(
      value: controller.selectedProcessIds.contains(process.id),
      onChanged: (_) => controller.toggleSelection(process.id),
    ),
  ),
```

3. 添加批量操作按钮组：
```dart
// 批量操作按钮（显示在选择模式下）
if (controller.isSelectMode.value) ...[
  ElevatedButton.icon(
    icon: Icon(Icons.splitscreen),
    label: Text('金银分离'),
    onPressed: controller.canBatchSeparate 
      ? () => controller.batchProcess('separate')
      : null,
  ),
  ElevatedButton.icon(
    icon: Icon(Icons.build),
    label: Text('翻新加工'),
    onPressed: controller.canBatchRefurbish
      ? () => controller.batchProcess('refurbish')
      : null,
  ),
  ElevatedButton.icon(
    icon: Icon(Icons.whatshot),
    label: Text('直接熔炼'),
    onPressed: controller.canBatchMelt
      ? () => controller.batchProcess('melt')
      : null,
  ),
  ElevatedButton.icon(
    icon: Icon(Icons.attach_money),
    label: Text('直接卖出'),
    onPressed: controller.canBatchSell
      ? () => controller.batchProcess('sell')
      : null,
  ),
]
```

4. 添加快捷键支持：
```dart
// 在表格行添加GestureDetector
GestureDetector(
  onTap: () {
    if (controller.isSelectMode.value) {
      if (HardwareKeyboard.instance.isControlPressed) {
        controller.toggleSelection(process.id);
      } else if (HardwareKeyboard.instance.isShiftPressed) {
        controller.selectRange(process.id);
      } else {
        controller.toggleSelection(process.id);
      }
    }
  },
  child: // ... 表格行内容
)
```

#### 5. 权限控制实现
**前端权限检查**:
```dart
// 在控制器中添加权限检查
bool get canBatchSeparate => 
  selectedProcessIds.isNotEmpty && 
  authService.hasPermission('recycling.process.edit') &&
  selectedProcessIds.every((id) => 
    processList.firstWhere((p) => p.id == id).status == 0);

bool get canBatchSell => 
  selectedProcessIds.isNotEmpty && 
  authService.hasPermission('recycling.process.complete') &&
  selectedProcessIds.every((id) => 
    processList.firstWhere((p) => p.id == id).status == 2);
```

## 测试计划

### 功能一测试
1. 验证门店下拉框显示正确
2. 选择不同门店后列表数据正确筛选
3. 切换"全部门店"显示所有数据
4. 非管理员用户只能看到自己门店的数据

### 功能二测试
1. 单选、多选、全选功能正常
2. Ctrl+Click和Shift+Click快捷键正常工作
3. 批量操作权限控制正确
4. 批量操作成功/失败反馈正确
5. 事务控制：部分失败不影响成功的操作

## 注意事项
1. 确保后端批量操作使用事务，保证数据一致性
2. 批量操作前进行状态检查，避免无效操作
3. 提供详细的操作反馈，包括成功/失败数量
4. 批量操作数量较大时考虑添加进度提示