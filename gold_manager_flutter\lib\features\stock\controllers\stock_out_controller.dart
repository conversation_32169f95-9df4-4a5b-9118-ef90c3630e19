import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/constants/app_permissions.dart';
import '../../../core/utils/logger.dart';
import '../../../core/routes/app_pages.dart';
import '../../../models/stock/stock_out.dart';
import '../../../models/common/document_status.dart';
import '../../../models/store/store.dart';
import '../../../services/stock_service.dart';
import '../../../services/store_service.dart';
import '../../../services/auth_service.dart';
import 'stock_tab_controller.dart';
import '../widgets/stock_out_detail_dialog.dart';

/// 出库管理控制器
class StockOutController extends GetxController {
  // 服务
  final StockService _stockService = Get.find<StockService>();
  final StoreService _storeService = Get.find<StoreService>();
  final AuthService _authService = Get.find<AuthService>();

  // 状态变量
  final RxBool isLoading = false.obs;
  final RxBool isSearching = false.obs;
  final RxList<StockOut> stockOutList = <StockOut>[].obs;
  final RxList<Store> storeList = <Store>[].obs;

  // 筛选条件
  final TextEditingController searchController = TextEditingController();
  final Rx<DateTime?> startDate = Rx<DateTime?>(null);
  final Rx<DateTime?> endDate = Rx<DateTime?>(null);
  final RxInt selectedStoreId = 0.obs;
  final Rx<DocumentStatus?> selectedStatus = Rx<DocumentStatus?>(null);

  // 分页
  final RxInt currentPage = 1.obs;
  final RxInt totalPages = 1.obs;
  final int pageSize = 20;

  @override
  void onInit() {
    super.onInit();
    LoggerService.d('StockOutController 初始化');

    // 延迟初始化，确保权限已加载
    Future.delayed(const Duration(milliseconds: 100), () {
      // 输出权限调试信息
      LoggerService.i('=== 出库管理权限调试信息 ===');
      LoggerService.i('用户名: ${_authService.userName.value}');
      LoggerService.i('用户角色: ${_authService.userRole.value}');
      LoggerService.i('所有权限: ${_authService.permissions.toList()}');
      LoggerService.i('stockView权限: ${_authService.hasPermission(AppPermissions.stockView)}');
      LoggerService.i('stockOut权限: ${_authService.hasPermission(AppPermissions.stockOut)}');
      LoggerService.i('stockAudit权限: ${_authService.hasPermission(AppPermissions.stockAudit)}');
      LoggerService.i('superAdmin权限: ${_authService.hasPermission(AppPermissions.superAdmin)}');
      LoggerService.i('storeView权限: ${_authService.hasPermission(AppPermissions.storeView)}');
      LoggerService.i('=== 权限调试信息结束 ===');

      if (_authService.hasPermission(AppPermissions.storeView)) {
        fetchStores();
      } else {
        LoggerService.w('用户没有门店查看权限，跳过获取门店列表');
      }

      if (_authService.hasPermission(AppPermissions.stockView)) {
        fetchStockOutList();
      } else {
        LoggerService.w('用户没有库存查看权限，跳过获取出库单列表');
      }
    });
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }

  /// 获取门店列表
  Future<void> fetchStores() async {
    try {
      isLoading.value = true;
      // 使用getAllStores确保获取所有门店，不受分页限制
      final stores = await _storeService.getAllStores();
      storeList.value = stores;

      // 如果用户不是管理员，自动选择用户所属门店
      if (_authService.userRole.value != 'admin') {
        selectedStoreId.value = _authService.storeId.value;
      }
    } catch (e) {
      LoggerService.e('获取门店列表失败', e);
      Get.snackbar(
        '错误',
        '获取门店列表失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// 获取出库单列表
  Future<void> fetchStockOutList() async {
    try {
      isLoading.value = true;

      final Map<String, dynamic> params = {
        'page': currentPage.value,
        'limit': pageSize,
      };

      // 添加筛选条件
      if (searchController.text.isNotEmpty) {
        params['search'] = searchController.text;
      }

      if (selectedStoreId.value > 0) {
        params['store_id'] = selectedStoreId.value;
      }

      if (selectedStatus.value != null) {
        params['status'] = selectedStatus.value!.value;
      }

      if (startDate.value != null) {
        params['start_date'] = (startDate.value!.millisecondsSinceEpoch ~/ 1000).toString();
      }

      if (endDate.value != null) {
        params['end_date'] = (endDate.value!.millisecondsSinceEpoch ~/ 1000).toString();
      }

      final result = await _stockService.getStockOutList(params);

      stockOutList.value = result.data;
      totalPages.value = result.lastPage;
    } catch (e) {
      LoggerService.e('获取出库单列表失败', e);
      Get.snackbar(
        '错误',
        '获取出库单列表失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// 搜索
  void search() {
    currentPage.value = 1;
    fetchStockOutList();
  }

  /// 重置筛选条件
  void resetFilters() {
    searchController.clear();
    startDate.value = null;
    endDate.value = null;
    selectedStoreId.value = 0;
    selectedStatus.value = null;
    currentPage.value = 1;
    fetchStockOutList();
  }

  /// 分页
  void goToPage(int page) {
    if (page < 1 || page > totalPages.value) return;
    currentPage.value = page;
    fetchStockOutList();
  }

  /// 创建新的出库单 - 使用标签页方式打开
  void createNewStockOut() {
    // 检查权限
    if (!_authService.hasPermission(AppPermissions.stockOut)) {
      Get.snackbar(
        '权限不足',
        '您没有出库操作权限',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    try {
      // 尝试获取标签页控制器
      final stockTabController = Get.find<StockTabController>();

      // 使用标签页方式打开新建出库单表单
      stockTabController.openStockOutForm();

    } catch (e) {
      LoggerService.e('无法找到StockTabController，使用传统页面跳转方式', e);

      // 如果没有找到标签页控制器，使用原来的路由方式作为备用
      Get.toNamed(Routes.STOCK_OUT_FORM)?.then((value) {
        if (value == true) {
          fetchStockOutList();
        }
      });
    }
  }

  /// 查看出库单详情
  void viewStockOutDetail(int id) {
    Get.dialog(
      StockOutDetailDialog(stockOutId: id),
      barrierDismissible: true,
    );
  }

  /// 编辑出库单
  void editStockOut(int id) {
    // 检查权限
    if (!_authService.hasPermission(AppPermissions.stockOut)) {
      Get.snackbar(
        '权限不足',
        '您没有出库操作权限',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    try {
      // 尝试获取标签页控制器
      final stockTabController = Get.find<StockTabController>();
      
      // 查找对应的出库单信息
      final stockOut = stockOutList.firstWhereOrNull((item) => item.id == id);
      final stockOutNo = stockOut?.stockOutNo ?? 'OUT$id';

      // 使用标签页方式打开编辑出库单表单
      stockTabController.openStockOutEditForm(id, stockOutNo);

    } catch (e) {
      LoggerService.e('无法找到StockTabController，使用传统页面跳转方式', e);

      // 如果没有找到标签页控制器，使用原来的路由方式作为备用
      Get.toNamed(Routes.STOCK_OUT_FORM, arguments: {'id': id})?.then((value) {
        if (value == true) {
          fetchStockOutList();
        }
      });
    }
  }

  /// 删除出库单
  Future<void> deleteStockOut(int id) async {
    // 检查权限
    if (!_authService.hasPermission(AppPermissions.stockOut)) {
      Get.snackbar(
        '权限不足',
        '您没有出库操作权限',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    final confirmed = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('确认删除'),
        content: const Text('确定要删除此出库单吗？此操作无法撤销。'),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            child: const Text('确定'),
          ),
        ],
      ),
    ) ?? false;

    if (!confirmed) return;

    try {
      isLoading.value = true;
      await _stockService.deleteStockOut(id);
      Get.snackbar(
        '成功',
        '出库单已成功删除',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      fetchStockOutList();
    } catch (e) {
      LoggerService.e('删除出库单失败', e);
      Get.snackbar(
        '错误',
        '删除出库单失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }


  /// 审核出库单
  Future<void> approveStockOut(int id, bool isApproved, String? reason) async {
    // 检查权限
    if (!_authService.hasPermission(AppPermissions.stockAudit)) {
      Get.snackbar(
        '权限不足',
        '您没有库存审核权限',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    try {
      isLoading.value = true;
      await _stockService.approveStockOut(id, isApproved, reason);
      Get.snackbar(
        '成功',
        '出库单审核已完成',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      fetchStockOutList();
    } catch (e) {
      LoggerService.e('审核出库单失败', e);
      Get.snackbar(
        '错误',
        '审核出库单失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// 获取状态显示颜色
  Color getStatusColor(DocumentStatus status) {
    switch (status) {
      case DocumentStatus.draft:
        return Colors.grey;
      case DocumentStatus.pending:
        return Colors.orange;
      case DocumentStatus.approved:
        return Colors.green;
      case DocumentStatus.rejected:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}