import 'package:intl/intl.dart';

/// 旧料回收单数据模型
class RecyclingOrder {
  final int id;
  final String orderNo;
  final DateTime orderDate;
  final int customerId;
  final String customerName;
  final String customerPhone;
  final double totalWeight;
  final double totalAmount;
  final int itemCount; // 件数字段
  final String remark;
  final int status; // 0: 待处理, 1: 已处理, 2: 已完成, 3: 已取消
  final DateTime createdAt;
  final int createdBy;
  final String creatorName;
  final int storeId; // 门店ID
  final String storeName; // 门店名称
  final List<RecyclingItem> items;

  /// 构造函数
  const RecyclingOrder({
    required this.id,
    required this.orderNo,
    required this.orderDate,
    required this.customerId,
    required this.customerName,
    required this.customerPhone,
    required this.totalWeight,
    required this.totalAmount,
    required this.itemCount,
    this.remark = '',
    required this.status,
    required this.createdAt,
    required this.createdBy,
    required this.creatorName,
    required this.storeId,
    required this.storeName,
    required this.items,
  });

  /// 从JSON构造
  factory RecyclingOrder.fromJson(Map<String, dynamic> json) {
    print('🔍 RecyclingOrder.fromJson 开始解析');
    print('📊 原始JSON数据: $json');

    List<RecyclingItem> itemList = [];

    // 详细检查items字段
    print('📦 检查items字段...');
    if (json['items'] != null) {
      print('✅ items字段存在');
      print('📦 items原始数据: ${json['items']}');
      print('📦 items类型: ${json['items'].runtimeType}');

      if (json['items'] is List) {
        final itemsData = json['items'] as List;
        print('📦 items数组长度: ${itemsData.length}');

        for (int i = 0; i < itemsData.length; i++) {
          print('📦 处理第${i + 1}个明细项: ${itemsData[i]}');
          try {
            final item = RecyclingItem.fromJson(itemsData[i]);
            itemList.add(item);
            print('✅ 成功解析明细项: ${item.itemName}');
          } catch (e) {
            print('❌ 解析明细项失败: $e');
          }
        }
      } else {
        print('❌ items不是数组类型');
      }
    } else {
      print('⚠️ items字段为null');
    }

    print('📦 最终解析的明细数量: ${itemList.length}');

    // 安全的数字转换函数
    double parseDouble(dynamic value) {
      if (value == null) return 0.0;
      if (value is double) return value;
      if (value is int) return value.toDouble();
      if (value is String) {
        return double.tryParse(value) ?? 0.0;
      }
      return 0.0;
    }

    // 处理时间戳（后端返回的是Unix时间戳）
    DateTime parseTimestamp(dynamic timestamp) {
      if (timestamp == null) return DateTime.now();
      if (timestamp is int) {
        return DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
      }
      return DateTime.now();
    }

    final order = RecyclingOrder(
      id: json['id'] ?? 0,
      orderNo: json['recycle_no'] ?? json['order_no'] ?? '', // 后端字段名为recycle_no
      orderDate: parseTimestamp(json['createtime'] ?? json['order_date']),
      customerId:
          json['member_id'] ?? json['customer_id'] ?? 0, // 后端使用member_id
      customerName: json['customer_name'] ?? '',
      customerPhone: json['phone'] ?? json['customer_phone'] ?? '',
      totalWeight:
          parseDouble(json['gold_weight']) +
          parseDouble(json['silver_weight']), // 金重+银重
      totalAmount: parseDouble(json['total_amount']),
      itemCount:
          json['item_count'] ?? itemList.length, // 优先使用API返回的件数，否则使用items长度
      remark: json['remark'] ?? '',
      status: json['status'] ?? 0,
      createdAt: parseTimestamp(json['createtime']),
      createdBy: json['operator_id'] ?? json['created_by'] ?? 0,
      creatorName: json['operator_name'] ?? json['creator_name'] ?? '',
      storeId: json['store_id'] ?? 0, // 门店ID
      storeName: json['store_name'] ?? '', // 门店名称
      items: itemList,
    );

    print('✅ RecyclingOrder创建完成: ${order.orderNo}, 明细数量: ${order.items.length}');
    print('📊 最终回收单信息:');
    print('   - ID: ${order.id}');
    print('   - 单号: ${order.orderNo}');
    print('   - 客户: ${order.customerName}');
    print('   - 总金额: ${order.totalAmount}');
    print('   - 明细数量: ${order.items.length}');

    return order;
  }

  /// 转为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'order_no': orderNo,
      'order_date': orderDate.millisecondsSinceEpoch ~/ 1000,
      'customer_id': customerId,
      'customer_name': customerName,
      'customer_phone': customerPhone,
      'total_weight': totalWeight,
      'total_amount': totalAmount,
      'item_count': itemCount,
      'remark': remark,
      'status': status,
      'created_at': createdAt.millisecondsSinceEpoch ~/ 1000,
      'created_by': createdBy,
      'creator_name': creatorName,
      'store_id': storeId,
      'store_name': storeName,
      'items': items.map((item) => item.toJson()).toList(),
    };
  }

  /// 获取状态文本
  String get statusText {
    switch (status) {
      case 0:
        return '待处理';
      case 1:
        return '已处理';
      case 2:
        return '已完成';
      case 3:
        return '已取消';
      default:
        return '未知状态';
    }
  }

  /// 获取格式化的日期
  String get formattedDate {
    return DateFormat('yyyy-MM-dd').format(orderDate);
  }

  /// 获取格式化的完整日期时间（精确到秒）
  String get formattedDateTime {
    return DateFormat('yyyy-MM-dd HH:mm:ss').format(orderDate);
  }

  /// 获取格式化的创建时间
  String get formattedCreatedAt {
    return DateFormat('yyyy-MM-dd HH:mm').format(createdAt);
  }
}

/// 回收物品数据模型
class RecyclingItem {
  final int id;
  final int orderId;
  final int categoryId;
  final String categoryName;
  final String itemName;
  final double weight; // 总重量（金重+银重）
  final double price; // 平均单价
  final double amount; // 总金额

  // 金相关字段
  final double goldWeight; // 金重
  final double goldPrice; // 金价
  final double goldAmount; // 金额

  // 银相关字段
  final double silverWeight; // 银重
  final double silverPrice; // 银价
  final double silverAmount; // 银额

  // 折扣相关字段
  final double discountRate; // 折扣率
  final double discountAmount; // 折扣金额

  final String? photoUrl;
  final String? remark;
  final int status; // 0: 待处理, 1: 已加工, 2: 已丢弃, 3: 已售出

  /// 构造函数
  const RecyclingItem({
    required this.id,
    required this.orderId,
    required this.categoryId,
    required this.categoryName,
    required this.itemName,
    required this.weight,
    required this.price,
    required this.amount,
    required this.goldWeight,
    required this.goldPrice,
    required this.goldAmount,
    required this.silverWeight,
    required this.silverPrice,
    required this.silverAmount,
    required this.discountRate,
    required this.discountAmount,
    this.photoUrl,
    this.remark,
    required this.status,
  });

  /// 从JSON构造
  factory RecyclingItem.fromJson(Map<String, dynamic> json) {
    print('🔍 RecyclingItem.fromJson 开始解析明细项');
    print('📦 明细项原始数据: $json');

    // 安全的数字转换函数
    double parseDouble(dynamic value) {
      if (value == null) return 0.0;
      if (value is double) return value;
      if (value is int) return value.toDouble();
      if (value is String) {
        return double.tryParse(value) ?? 0.0;
      }
      return 0.0;
    }

    // 检查关键字段
    print('📦 检查关键字段:');
    print('   - id: ${json['id']}');
    print('   - name: ${json['name']}');
    print('   - item_name: ${json['item_name']}');
    print('   - category_name: ${json['category_name']}');
    print('   - gold_weight: ${json['gold_weight']}');
    print('   - silver_weight: ${json['silver_weight']}');
    print('   - total_amount: ${json['total_amount']}');
    print('   - amount: ${json['amount']}');

    final goldWeight = parseDouble(json['gold_weight']);
    final silverWeight = parseDouble(json['silver_weight']);
    final totalWeight = goldWeight + silverWeight;
    final totalAmount = parseDouble(json['total_amount']);

    // 解析金相关字段
    final goldPrice = parseDouble(json['gold_price']);
    final goldAmount = parseDouble(json['gold_amount']);

    // 解析银相关字段
    final silverPrice = parseDouble(json['silver_price']);
    final silverAmount = parseDouble(json['silver_amount']);

    // 解析折扣相关字段
    final discountRate = parseDouble(json['discount_rate']);
    final discountAmount = parseDouble(json['discount_amount']);

    print('📊 计算结果:');
    print('   - 金重: ${goldWeight}g, 金价: ${goldPrice}元/g, 金额: ${goldAmount}元');
    print('   - 银重: ${silverWeight}g, 银价: ${silverPrice}元/g, 银额: ${silverAmount}元');
    print('   - 总重量: ${totalWeight}g');
    print('   - 总金额: ${totalAmount}元');
    print('   - 折扣率: ${discountRate}%, 折扣金额: ${discountAmount}元');
    print('   - 平均单价: ${totalWeight > 0 ? totalAmount / totalWeight : 0}元/g');

    final item = RecyclingItem(
      id: json['id'] ?? 0,
      orderId: json['recycling_id'] ?? json['order_id'] ?? 0,
      categoryId: json['category_id'] ?? 0,
      categoryName: json['category_name'] ?? '未知分类',
      itemName: json['name'] ?? json['item_name'] ?? '未知物品',
      weight: totalWeight,
      price: totalWeight > 0 ? totalAmount / totalWeight : 0,
      amount: totalAmount,
      goldWeight: goldWeight,
      goldPrice: goldPrice,
      goldAmount: goldAmount,
      silverWeight: silverWeight,
      silverPrice: silverPrice,
      silverAmount: silverAmount,
      discountRate: discountRate,
      discountAmount: discountAmount,
      photoUrl: json['photo_url'],
      remark: json['remark'],
      status: json['status'] ?? 0,
    );

    print('✅ 成功创建RecyclingItem: ${item.itemName}, 重量: ${item.weight}, 金额: ${item.amount}');
    return item;
  }

  /// 转为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'order_id': orderId,
      'category_id': categoryId,
      'category_name': categoryName,
      'item_name': itemName,
      'weight': weight,
      'price': price,
      'amount': amount,
      'gold_weight': goldWeight,
      'gold_price': goldPrice,
      'gold_amount': goldAmount,
      'silver_weight': silverWeight,
      'silver_price': silverPrice,
      'silver_amount': silverAmount,
      'discount_rate': discountRate,
      'discount_amount': discountAmount,
      'photo_url': photoUrl,
      'remark': remark,
      'status': status,
    };
  }

  /// 获取状态文本
  String get statusText {
    switch (status) {
      case 0:
        return '待处理';
      case 1:
        return '已加工';
      case 2:
        return '已丢弃';
      case 3:
        return '已售出';
      default:
        return '未知状态';
    }
  }
}

/// 回收物品分类
class RecyclingCategory {
  final int id;
  final String name;
  final String? description;
  final bool isActive;

  /// 构造函数
  const RecyclingCategory({
    required this.id,
    required this.name,
    this.description,
    required this.isActive,
  });

  /// 从JSON构造
  factory RecyclingCategory.fromJson(Map<String, dynamic> json) {
    return RecyclingCategory(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      isActive: json['is_active'] == 1,
    );
  }

  /// 转为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'is_active': isActive ? 1 : 0,
    };
  }
}
