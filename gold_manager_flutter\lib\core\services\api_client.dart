import 'dart:io';
import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:get/get.dart' hide Response;
import '../config/app_config.dart';
import '../models/api_response.dart';
import '../utils/logger_service.dart';
import 'storage_service.dart';

/// API客户端服务
/// 专门处理与后端API的通信，包含JWT认证
class ApiClient extends GetxService {
  late Dio _dio;
  final StorageService _storageService = Get.find<StorageService>();

  @override
  void onInit() {
    super.onInit();
    _initDio();
    LoggerService.i('ApiClient initialized');
  }

  /// 初始化Dio客户端
  void _initDio() {
    _dio = Dio(BaseOptions(
      baseUrl: AppConfig.apiBaseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));

    // 开发环境配置（如果需要HTTPS证书忽略，可以在这里添加）
    if (AppConfig.isDevelopment && AppConfig.apiBaseUrl.startsWith('https')) {
      (_dio.httpClientAdapter as IOHttpClientAdapter).createHttpClient = () {
        final client = HttpClient();
        client.badCertificateCallback = (cert, host, port) => true;
        return client;
      };
    }

    // 添加请求拦截器
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        // 添加JWT Token
        final token = _storageService.getToken();
        if (token != null && token.isNotEmpty) {
          options.headers['Authorization'] = 'Bearer $token';
          LoggerService.d('🔐 JWT Token已添加到请求头: Bearer ${token.substring(0, 20)}...');
        } else {
          LoggerService.w('⚠️ 没有JWT Token，请求可能会被拒绝');
        }

        // 只记录审核相关的API请求
        if (options.path.contains('/status') || options.method == 'PATCH') {
          LoggerService.d('📡 [API客户端] ${options.method} ${options.path}');
          LoggerService.d('📋 [API客户端] Headers: ${options.headers}');
          if (options.data != null) {
            LoggerService.d('📦 [API客户端] Data: ${options.data}');
          }
        }

        handler.next(options);
      },
      onResponse: (response, handler) {
        // 只记录审核相关的API响应
        if (response.requestOptions.path.contains('/status') || response.requestOptions.method == 'PATCH') {
          LoggerService.d('📡 [API客户端] Response: ${response.statusCode} ${response.requestOptions.path}');
        }
        handler.next(response);
      },
      onError: (error, handler) async {
        LoggerService.e('API Error: ${error.message}', error);
        LoggerService.e('Error Response: ${error.response?.data}');

        // 处理401错误（Token过期）
        if (error.response?.statusCode == 401) {
          final refreshed = await _handleTokenExpired();
          if (refreshed) {
            // 重试原请求
            try {
              final clonedRequest = await _dio.request(
                error.requestOptions.path,
                options: Options(
                  method: error.requestOptions.method,
                  headers: error.requestOptions.headers,
                ),
                data: error.requestOptions.data,
                queryParameters: error.requestOptions.queryParameters,
              );
              return handler.resolve(clonedRequest);
            } catch (e) {
              LoggerService.e('Retry request failed', e);
            }
          }
        }

        handler.next(error);
      },
    ));
  }

  /// 处理Token过期
  /// 返回是否成功刷新Token
  Future<bool> _handleTokenExpired() async {
    LoggerService.w('Token expired, attempting refresh...');

    try {
      final refreshToken = _storageService.getRefreshToken();
      if (refreshToken == null || refreshToken.isEmpty) {
        LoggerService.w('No refresh token available');
        await _storageService.clearTokens();
        Get.offAllNamed('/login');
        return false;
      }

      final response = await _dio.post(
        '${AppConfig.apiEndpoint['auth']}/refresh',
        data: RefreshTokenRequest(refreshToken: refreshToken).toJson(),
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200) {
        final apiResponse = ApiResponse<RefreshTokenResponseData>.fromJson(
          response.data,
          (data) => RefreshTokenResponseData.fromJson(data),
        );

        if (apiResponse.isSuccess && apiResponse.data != null) {
          await _storageService.saveToken(apiResponse.data!.accessToken);
          await _storageService.saveRefreshToken(apiResponse.data!.refreshToken);
          LoggerService.i('Token refreshed successfully');
          return true;
        } else {
          LoggerService.e('Token refresh failed: ${apiResponse.errorMessage}');
        }
      }
    } catch (e) {
      LoggerService.e('Token refresh failed', e);
    }

    // 刷新失败，清除Token并跳转到登录页
    await _storageService.clearTokens();
    Get.offAllNamed('/login');
    return false;
  }

  /// 用户登录
  /// 根据后端实际响应格式实现登录流程
  Future<LoginResponseData> login(String username, String password, {bool rememberMe = false}) async {
    try {
      LoggerService.i('📡 发送登录请求: $username');

      final loginRequest = LoginRequest(
        username: username,
        password: password,
        rememberMe: rememberMe,
      );

      final response = await _dio.post(
        '${AppConfig.apiEndpoint['auth']}/login',
        data: loginRequest.toJson(),
      );

      LoggerService.i('📡 API响应状态码: ${response.statusCode}');

      if (response.statusCode == 200) {
        LoggerService.i('📋 原始API响应数据:');
        LoggerService.i('${response.data}');

        // 后端直接返回登录数据，不包装在标准格式中
        final loginData = LoginResponseData.fromJson(response.data);

        LoggerService.i('🔍 解析后的登录数据:');
        LoggerService.i('   - accessToken: ${loginData.accessToken.substring(0, 20)}...');
        LoggerService.i('   - adminInfo.id: ${loginData.adminInfo.id}');
        LoggerService.i('   - adminInfo.username: ${loginData.adminInfo.username}');
        LoggerService.i('   - adminInfo.storeId: ${loginData.adminInfo.storeId}');
        LoggerService.i('   - adminInfo.storeName: ${loginData.adminInfo.storeName}');

        // 保存Token和用户信息
        await _storageService.saveToken(loginData.accessToken);
        await _storageService.saveRefreshToken(loginData.refreshToken);
        await _storageService.setRememberMe(rememberMe);

        LoggerService.i('✅ API登录成功，返回数据');
        return loginData;
      } else {
        throw Exception('登录失败: HTTP ${response.statusCode}');
      }
    } on DioException catch (e) {
      LoggerService.e('Login failed', e);

      String errorMessage = '登录失败';
      if (e.response?.data != null) {
        try {
          // 尝试解析错误响应
          if (e.response!.data is Map<String, dynamic>) {
            final errorData = e.response!.data as Map<String, dynamic>;
            errorMessage = errorData['detail'] ?? errorData['message'] ?? '登录失败';
          } else {
            errorMessage = e.response!.data.toString();
          }
        } catch (_) {
          errorMessage = e.message ?? '登录失败';
        }
      } else {
        errorMessage = e.message ?? '网络连接失败';
      }

      throw Exception(errorMessage);
    }
  }

  /// 用户登出
  Future<void> logout() async {
    try {
      await _dio.post('${AppConfig.apiEndpoint['auth']}/logout');
    } catch (e) {
      LoggerService.w('Logout API call failed', e);
    } finally {
      // 无论API调用是否成功，都清除本地Token
      await _storageService.clearTokens();
      LoggerService.i('User logged out');
    }
  }

  /// 获取当前用户信息
  /// 根据后端实际响应格式获取用户信息
  Future<AdminInfo> getCurrentUser() async {
    try {
      final response = await _dio.get('${AppConfig.apiEndpoint['auth']}/me');

      if (response.statusCode == 200) {
        // 后端直接返回用户数据，不包装在标准格式中
        final adminInfo = AdminInfo.fromJson(response.data);

        LoggerService.i('Get current user successful');
        return adminInfo;
      } else {
        throw Exception('获取用户信息失败: HTTP ${response.statusCode}');
      }
    } on DioException catch (e) {
      LoggerService.e('Get current user failed', e);

      String errorMessage = '获取用户信息失败';
      if (e.response?.data != null) {
        try {
          if (e.response!.data is Map<String, dynamic>) {
            final errorData = e.response!.data as Map<String, dynamic>;
            errorMessage = errorData['detail'] ?? errorData['message'] ?? '获取用户信息失败';
          } else {
            errorMessage = e.response!.data.toString();
          }
        } catch (_) {
          errorMessage = e.message ?? '获取用户信息失败';
        }
      } else {
        errorMessage = e.message ?? '网络连接失败';
      }

      throw Exception(errorMessage);
    }
  }

  /// 通用GET请求
  Future<Response> get(String path, {Map<String, dynamic>? queryParameters}) async {
    try {
      return await _dio.get(path, queryParameters: queryParameters);
    } on DioException catch (e) {
      LoggerService.e('GET request failed: $path', e);
      _handleApiError(e);
      rethrow;
    }
  }

  /// 通用POST请求
  Future<Response> post(String path, {dynamic data, Map<String, dynamic>? queryParameters}) async {
    try {
      return await _dio.post(path, data: data, queryParameters: queryParameters);
    } on DioException catch (e) {
      LoggerService.e('POST request failed: $path', e);
      _handleApiError(e);
      rethrow;
    }
  }

  /// 通用PUT请求
  Future<Response> put(String path, {dynamic data}) async {
    try {
      return await _dio.put(path, data: data);
    } on DioException catch (e) {
      LoggerService.e('PUT request failed: $path', e);
      _handleApiError(e);
      rethrow;
    }
  }

  /// 通用DELETE请求
  Future<Response> delete(String path) async {
    try {
      return await _dio.delete(path);
    } on DioException catch (e) {
      LoggerService.e('DELETE request failed: $path', e);
      _handleApiError(e);
      rethrow;
    }
  }

  /// 通用PATCH请求
  Future<Response> patch(String path, {dynamic data}) async {
    try {
      return await _dio.patch(path, data: data);
    } on DioException catch (e) {
      LoggerService.e('PATCH request failed: $path', e);
      _handleApiError(e);
      rethrow;
    }
  }

  /// 处理API错误
  void _handleApiError(DioException e) {
    String message = '网络请求失败';

    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        message = '请求超时，请检查网络连接';
        break;
      case DioExceptionType.badResponse:
        final statusCode = e.response?.statusCode;
        switch (statusCode) {
          case 400:
            message = '请求参数错误';
            break;
          case 401:
            message = '未授权访问，请重新登录';
            break;
          case 403:
            message = '权限不足，无法执行此操作';
            break;
          case 404:
            message = '请求的资源不存在';
            break;
          case 422:
            message = '请求参数验证失败';
            break;
          case 500:
            message = '服务器内部错误';
            break;
          default:
            message = '网络请求失败 ($statusCode)';
        }

        // 尝试从响应中获取详细错误信息
        try {
          final errorData = e.response?.data;
          if (errorData != null && errorData['detail'] != null) {
            message = errorData['detail'];
          }
        } catch (_) {}
        break;
      case DioExceptionType.cancel:
        message = '请求已取消';
        break;
      case DioExceptionType.unknown:
        if (e.error is SocketException) {
          message = '网络连接失败，请检查网络设置';
        } else {
          message = '未知错误: ${e.message}';
        }
        break;
      default:
        message = '网络请求失败: ${e.message}';
    }

    LoggerService.e('API Error: $message');
  }

  /// 检查网络连接状态
  Future<bool> checkConnection() async {
    try {
      final response = await _dio.get('/health');
      return response.statusCode == 200;
    } catch (e) {
      LoggerService.w('Network check failed', e);
      return false;
    }
  }
}
