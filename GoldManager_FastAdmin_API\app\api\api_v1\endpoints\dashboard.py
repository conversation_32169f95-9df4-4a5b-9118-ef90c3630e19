"""
仪表板API端点
提供系统概览数据和统计信息
"""

from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session
from typing import Optional
from datetime import datetime, timedelta
from ....core.database import get_db
from ....core.dependencies import get_current_user
from ....schemas.auth import CurrentUserResponse
from ....services.dashboard_service import DashboardService
from ....schemas.dashboard import (
    DashboardOverviewResponse,
    SalesStatisticsResponse,
    InventoryStatisticsResponse,
    MemberStatisticsResponse,
    FinancialStatisticsResponse,
    RecentActivitiesResponse,
    SalesTrendResponse,
    StoreComparisonResponse,
    CategoryRankingResponse,
    LowStockAlertsResponse,
    MemberGrowthResponse
)

# 创建路由器
router = APIRouter()


@router.get("/overview", response_model=DashboardOverviewResponse, summary="获取仪表板概览")
async def get_overview(
    db: Session = Depends(get_db),
    current_user: CurrentUserResponse = Depends(get_current_user)
):
    """
    获取仪表板概览数据

    包含：
    - 商品总数、门店总数、会员总数
    - 今日销售额、本月销售额
    - 库存总值、待处理订单数
    """
    dashboard_service = DashboardService(db)
    return await dashboard_service.get_overview()


@router.get("/sales-statistics", response_model=SalesStatisticsResponse, summary="获取销售统计")
async def get_sales_statistics(
    start_date: Optional[str] = Query(None, description="开始日期 (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="结束日期 (YYYY-MM-DD)"),
    store_id: Optional[int] = Query(None, description="门店ID"),
    db: Session = Depends(get_db)
):
    """
    获取销售统计数据

    包含：
    - 销售总额、订单数量、平均客单价
    - 销售趋势、门店对比、商品排行
    """
    dashboard_service = DashboardService(db)
    return await dashboard_service.get_sales_statistics(start_date, end_date, store_id)


@router.get("/inventory-statistics", response_model=InventoryStatisticsResponse, summary="获取库存统计")
async def get_inventory_statistics(
    store_id: Optional[int] = Query(None, description="门店ID"),
    db: Session = Depends(get_db)
):
    """
    获取库存统计数据

    包含：
    - 库存总值、商品总数、低库存预警
    - 入库出库统计、库存周转率
    """
    dashboard_service = DashboardService(db)
    return await dashboard_service.get_inventory_statistics(store_id)


@router.get("/member-statistics", response_model=MemberStatisticsResponse, summary="获取会员统计")
async def get_member_statistics(
    start_date: Optional[str] = Query(None, description="开始日期 (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="结束日期 (YYYY-MM-DD)"),
    db: Session = Depends(get_db)
):
    """
    获取会员统计数据

    包含：
    - 会员总数、新增会员、活跃会员
    - 会员等级分布、消费排行、积分统计
    """
    dashboard_service = DashboardService(db)
    return await dashboard_service.get_member_statistics(start_date, end_date)


@router.get("/financial-statistics", response_model=FinancialStatisticsResponse, summary="获取财务统计")
async def get_financial_statistics(
    start_date: Optional[str] = Query(None, description="开始日期 (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="结束日期 (YYYY-MM-DD)"),
    store_id: Optional[int] = Query(None, description="门店ID"),
    db: Session = Depends(get_db)
):
    """
    获取财务统计数据

    包含：
    - 收入支出统计、利润分析
    - 回收业务统计、成本分析
    """
    dashboard_service = DashboardService(db)
    return await dashboard_service.get_financial_statistics(start_date, end_date, store_id)


@router.get("/recent-activities", response_model=RecentActivitiesResponse, summary="获取最近活动")
async def get_recent_activities(
    limit: int = Query(20, description="返回数量限制", ge=1, le=100),
    db: Session = Depends(get_db)
):
    """
    获取最近活动记录

    包含：
    - 最近的入库、出库、销售记录
    - 最近的会员注册、积分变动
    """
    dashboard_service = DashboardService(db)
    return await dashboard_service.get_recent_activities(limit)


@router.get("/sales-trend", response_model=SalesTrendResponse, summary="获取销售趋势")
async def get_sales_trend(
    period: str = Query("month", description="统计周期: day/week/month/year"),
    days: int = Query(30, description="统计天数", ge=1, le=365),
    store_id: Optional[int] = Query(None, description="门店ID"),
    db: Session = Depends(get_db)
):
    """
    获取销售趋势数据

    支持按日、周、月、年统计
    """
    dashboard_service = DashboardService(db)
    return await dashboard_service.get_sales_trend(period, days, store_id)


@router.get("/store-comparison", response_model=StoreComparisonResponse, summary="获取门店对比")
async def get_store_comparison(
    start_date: Optional[str] = Query(None, description="开始日期 (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="结束日期 (YYYY-MM-DD)"),
    db: Session = Depends(get_db)
):
    """
    获取门店销售对比数据

    包含各门店的销售额、订单数、客单价对比
    """
    dashboard_service = DashboardService(db)
    return await dashboard_service.get_store_comparison(start_date, end_date)


@router.get("/category-ranking", response_model=CategoryRankingResponse, summary="获取商品分类排行")
async def get_category_ranking(
    start_date: Optional[str] = Query(None, description="开始日期 (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="结束日期 (YYYY-MM-DD)"),
    store_id: Optional[int] = Query(None, description="门店ID"),
    limit: int = Query(10, description="返回数量限制", ge=1, le=50),
    db: Session = Depends(get_db)
):
    """
    获取商品分类销售排行

    按销售额或销售数量排序
    """
    dashboard_service = DashboardService(db)
    return await dashboard_service.get_category_ranking(start_date, end_date, store_id, limit)


@router.get("/low-stock-alerts", response_model=LowStockAlertsResponse, summary="获取低库存预警")
async def get_low_stock_alerts(
    store_id: Optional[int] = Query(None, description="门店ID"),
    threshold: int = Query(5, description="库存预警阈值", ge=0),
    db: Session = Depends(get_db)
):
    """
    获取低库存预警商品列表

    显示库存数量低于阈值的商品
    """
    dashboard_service = DashboardService(db)
    return await dashboard_service.get_low_stock_alerts(store_id, threshold)


@router.get("/member-growth", response_model=MemberGrowthResponse, summary="获取会员增长趋势")
async def get_member_growth(
    period: str = Query("month", description="统计周期: day/week/month"),
    days: int = Query(30, description="统计天数", ge=1, le=365),
    db: Session = Depends(get_db)
):
    """
    获取会员增长趋势数据

    显示指定时间段内的会员注册趋势
    """
    dashboard_service = DashboardService(db)
    return await dashboard_service.get_member_growth(period, days)