import 'package:dio/dio.dart';
import '../utils/logger_service.dart';
import 'interceptors/auth_interceptor.dart';
import 'interceptors/error_interceptor.dart';
import '../config/app_config.dart';

/// API提供程序类，统一处理网络请求
class ApiProvider {
  late final Dio _dio;
  
  /// 构造函数
  ApiProvider() {
    _dio = Dio(
      BaseOptions(
        baseUrl: AppConfig.apiBaseUrl,
        connectTimeout: const Duration(seconds: 30),
        receiveTimeout: const Duration(seconds: 30),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ),
    );
    
    // 添加拦截器
    _dio.interceptors.add(AuthInterceptor());
    _dio.interceptors.add(ErrorInterceptor());
    
    // 添加日志拦截器（仅在调试模式下）
    if (AppConfig.isDebug) {
      _dio.interceptors.add(LogInterceptor(
        request: true,
        requestHeader: true,
        requestBody: true,
        responseHeader: true,
        responseBody: true,
        error: true,
        logPrint: (log) => LoggerService.d('DIO: $log'),
      ));
    }
  }
  
  /// GET请求
  Future<Response> get(String path, {Map<String, dynamic>? queryParams}) async {
    try {
      return await _dio.get(path, queryParameters: queryParams);
    } catch (e) {
      LoggerService.e('GET请求失败: $path', e);
      rethrow;
    }
  }
  
  /// POST请求
  Future<Response> post(String path, {dynamic data}) async {
    try {
      return await _dio.post(path, data: data);
    } catch (e) {
      LoggerService.e('POST请求失败: $path', e);
      rethrow;
    }
  }
  
  /// PUT请求
  Future<Response> put(String path, {dynamic data}) async {
    try {
      return await _dio.put(path, data: data);
    } catch (e) {
      LoggerService.e('PUT请求失败: $path', e);
      rethrow;
    }
  }
  
  /// DELETE请求
  Future<Response> delete(String path, {dynamic data}) async {
    try {
      return await _dio.delete(path, data: data);
    } catch (e) {
      LoggerService.e('DELETE请求失败: $path', e);
      rethrow;
    }
  }
  
  /// 文件上传
  Future<Response> uploadFile(String path, FormData formData) async {
    try {
      return await _dio.post(path, data: formData);
    } catch (e) {
      LoggerService.e('文件上传失败: $path', e);
      rethrow;
    }
  }
  
  /// 文件下载
  Future<Response> downloadFile(String path, String savePath) async {
    try {
      return await _dio.download(path, savePath);
    } catch (e) {
      LoggerService.e('文件下载失败: $path', e);
      rethrow;
    }
  }
} 