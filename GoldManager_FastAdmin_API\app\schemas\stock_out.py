"""
出库管理数据验证模型
定义出库单相关的Pydantic模型用于API数据验证
"""

from typing import List, Optional
from decimal import Decimal
from datetime import datetime
from pydantic import BaseModel, Field, validator


# ================= 出库单明细模型 =================

class StockOutItemBase(BaseModel):
    """出库单明细基础模型"""
    jewelry_id: int = Field(..., description="首饰ID")
    barcode: str = Field(..., max_length=50, description="商品条码")
    name: str = Field(..., max_length=100, description="商品名称")
    category_id: int = Field(..., description="分类ID")
    ring_size: Optional[str] = Field(None, max_length=20, description="圈口号")
    
    # 金重相关
    gold_weight: Optional[Decimal] = Field(default=Decimal('0.00'), ge=0, description="金重(克)")
    gold_price: Optional[Decimal] = Field(default=Decimal('0.00'), ge=0, description="金价(克价)")
    gold_cost: Optional[Decimal] = Field(default=Decimal('0.00'), ge=0, description="金成本")
    
    # 银重相关
    silver_weight: Optional[Decimal] = Field(default=Decimal('0.00'), ge=0, description="银重(克)")
    total_weight: Optional[Decimal] = Field(default=Decimal('0.00'), ge=0, description="总重(克)")
    silver_price: Optional[Decimal] = Field(default=Decimal('0.00'), ge=0, description="银价(克价)")
    silver_cost: Optional[Decimal] = Field(default=Decimal('0.00'), ge=0, description="银成本")
    
    # 工费相关
    silver_work_type: Optional[int] = Field(default=0, description="工费方式:0=按克,1=按件")
    silver_work_price: Optional[Decimal] = Field(default=Decimal('0.00'), ge=0, description="银工费")
    plating_cost: Optional[Decimal] = Field(default=Decimal('0.00'), ge=0, description="电铸费")
    piece_work_price: Optional[Decimal] = Field(default=Decimal('0.00'), ge=0, description="件工费")
    work_price: Optional[Decimal] = Field(default=Decimal('0.00'), ge=0, description="实际工费")
    
    # 成本和定价
    total_cost: Optional[Decimal] = Field(default=Decimal('0.00'), ge=0, description="总成本")
    total_amount: Optional[Decimal] = Field(default=Decimal('0.00'), ge=0, description="商品金额")
    sale_type: Optional[str] = Field(default='retail', description="销售类型:retail=零售,wholesale=批发")
    
    @validator('sale_type')
    def validate_sale_type(cls, v):
        if v not in ['retail', 'wholesale']:
            raise ValueError('销售类型必须是 retail 或 wholesale')
        return v


class StockOutItemCreate(StockOutItemBase):
    """创建出库单明细模型"""
    pass


class StockOutItemUpdate(BaseModel):
    """更新出库单明细模型"""
    jewelry_id: Optional[int] = Field(None, description="首饰ID")
    barcode: Optional[str] = Field(None, max_length=50, description="商品条码")
    name: Optional[str] = Field(None, max_length=100, description="商品名称")
    category_id: Optional[int] = Field(None, description="分类ID")
    ring_size: Optional[str] = Field(None, max_length=20, description="圈口号")
    total_amount: Optional[Decimal] = Field(None, ge=0, description="商品金额")
    sale_type: Optional[str] = Field(None, description="销售类型")


class StockOutItemResponse(StockOutItemBase):
    """出库单明细响应模型"""
    id: int = Field(..., description="明细ID")
    stock_out_id: int = Field(..., description="出库单ID")
    createtime: Optional[int] = Field(None, description="创建时间")
    updatetime: Optional[int] = Field(None, description="更新时间")
    
    # 关联对象
    jewelry: Optional[dict] = Field(None, description="关联首饰信息")

    class Config:
        from_attributes = True


# ================= 收款信息模型 =================

class PaymentInfo(BaseModel):
    """收款信息模型"""
    payment_method: Optional[str] = Field(None, max_length=20, description="收款方式")
    payment_remark: Optional[str] = Field(None, max_length=255, description="支付备注")
    cash_amount: Optional[Decimal] = Field(default=Decimal('0.00'), ge=0, description="现金金额")
    wechat_amount: Optional[Decimal] = Field(default=Decimal('0.00'), ge=0, description="微信金额")
    alipay_amount: Optional[Decimal] = Field(default=Decimal('0.00'), ge=0, description="支付宝金额")
    card_amount: Optional[Decimal] = Field(default=Decimal('0.00'), ge=0, description="刷卡金额")
    discount_amount: Optional[Decimal] = Field(default=Decimal('0.00'), ge=0, description="抹零金额")
    actual_amount: Optional[Decimal] = Field(default=Decimal('0.00'), ge=0, description="实收金额")


# ================= 出库单主表模型 =================

class StockOutBase(BaseModel):
    """出库单基础模型"""
    store_id: int = Field(..., description="出库门店ID")
    customer: Optional[str] = Field(None, max_length=50, description="客户")
    recycling_id: Optional[int] = Field(default=0, description="关联的回收单ID")
    sale_type: Optional[str] = Field(default='retail', description="销售类型:retail=零售,wholesale=批发")
    remark: Optional[str] = Field(None, max_length=255, description="备注")
    
    @validator('sale_type')
    def validate_sale_type(cls, v):
        if v not in ['retail', 'wholesale']:
            raise ValueError('销售类型必须是 retail 或 wholesale')
        return v


class StockOutCreate(StockOutBase):
    """创建出库单模型"""
    items: List[StockOutItemCreate] = Field(..., description="出库商品明细")
    
    @validator('items')
    def validate_items_not_empty(cls, v):
        if not v:
            raise ValueError('出库商品明细不能为空')
        return v


class StockOutUpdate(BaseModel):
    """更新出库单模型"""
    store_id: Optional[int] = Field(None, description="出库门店ID")
    customer: Optional[str] = Field(None, max_length=50, description="客户")
    recycling_id: Optional[int] = Field(None, description="关联的回收单ID")
    sale_type: Optional[str] = Field(None, description="销售类型")
    remark: Optional[str] = Field(None, max_length=255, description="备注")
    items: Optional[List[StockOutItemCreate]] = Field(None, description="出库商品明细")


class StockOutResponse(StockOutBase):
    """出库单响应模型"""
    id: int = Field(..., description="出库单ID")
    order_no: str = Field(..., description="出库单号")
    total_weight: Decimal = Field(..., description="总重量(克)")
    total_amount: Decimal = Field(..., description="总金额")
    operator_id: int = Field(..., description="操作员ID")
    status: int = Field(..., description="状态:1=待审核,2=已通过,3=未通过,4=已作废")
    
    # 时间字段
    createtime: Optional[int] = Field(None, description="创建时间")
    updatetime: Optional[int] = Field(None, description="更新时间")
    audit_time: Optional[int] = Field(None, description="审核时间")
    
    # 审核相关
    audit_user_id: Optional[int] = Field(None, description="审核人ID")
    audit_remark: Optional[str] = Field(None, description="审核备注")
    
    # 收款相关
    payment_status: int = Field(..., description="收款状态:0=未收款,1=已收款")
    payment_time: Optional[int] = Field(None, description="收款时间")
    payment_method: Optional[str] = Field(None, description="收款方式")
    payment_remark: Optional[str] = Field(None, description="支付备注")
    cash_amount: Decimal = Field(default=Decimal('0.00'), description="现金金额")
    wechat_amount: Decimal = Field(default=Decimal('0.00'), description="微信金额")
    alipay_amount: Decimal = Field(default=Decimal('0.00'), description="支付宝金额")
    card_amount: Decimal = Field(default=Decimal('0.00'), description="刷卡金额")
    discount_amount: Decimal = Field(default=Decimal('0.00'), description="抹零金额")
    actual_amount: Decimal = Field(default=Decimal('0.00'), description="实收金额")
    
    # 关联对象的简化信息
    store_name: Optional[str] = Field(None, description="门店名称")
    operator_name: Optional[str] = Field(None, description="操作员姓名")
    auditor_name: Optional[str] = Field(None, description="审核员姓名")

    # 🔧 修复关键问题：添加items字段定义
    items: Optional[List[StockOutItemResponse]] = Field(None, description="出库商品明细")

    class Config:
        from_attributes = True


# ================= 出库单统计模型 =================

class StockOutStatistics(BaseModel):
    """出库单统计模型"""
    total_count: int = Field(..., description="总数量")
    pending_count: int = Field(..., description="待审核数量")
    approved_count: int = Field(..., description="已通过数量")
    rejected_count: int = Field(..., description="未通过数量")
    cancelled_count: int = Field(..., description="已作废数量")
    total_amount: Decimal = Field(..., description="总金额")
    total_weight: Decimal = Field(..., description="总重量")
    store_distribution: List[dict] = Field(..., description="门店分布")
    sale_type_distribution: List[dict] = Field(..., description="销售类型分布")


# ================= 审核模型 =================

class StockOutAuditUpdate(BaseModel):
    """出库单审核模型"""
    status: int = Field(..., ge=2, le=4, description="审核状态:2=已通过,3=未通过,4=已作废")
    audit_remark: Optional[str] = Field(None, max_length=255, description="审核备注")
    
    @validator('status')
    def validate_status(cls, v):
        if v not in [2, 3, 4]:
            raise ValueError('审核状态必须是 2(已通过)、3(未通过)或4(已作废)')
        return v


# ================= 收款模型 =================

class StockOutPaymentUpdate(BaseModel):
    """出库单收款模型"""
    payment_status: int = Field(..., ge=0, le=1, description="收款状态:0=未收款,1=已收款")
    payment_info: Optional[PaymentInfo] = Field(None, description="收款信息")
    
    @validator('payment_status')
    def validate_payment_status(cls, v):
        if v not in [0, 1]:
            raise ValueError('收款状态必须是 0(未收款)或1(已收款)')
        return v


# ================= 查询参数模型 =================

class StockOutQueryParams(BaseModel):
    """出库单查询参数模型"""
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(20, ge=1, le=100, description="每页数量")
    keyword: Optional[str] = Field(None, description="关键词搜索(单号、客户、备注)")
    store_id: Optional[int] = Field(None, description="门店ID筛选")
    status: Optional[int] = Field(None, ge=1, le=4, description="状态筛选:1=待审核,2=已通过,3=未通过,4=已作废")
    sale_type: Optional[str] = Field(None, description="销售类型筛选:retail=零售,wholesale=批发")
    payment_status: Optional[int] = Field(None, ge=0, le=1, description="收款状态筛选:0=未收款,1=已收款")
    start_date: Optional[str] = Field(None, description="开始日期(YYYY-MM-DD)")
    end_date: Optional[str] = Field(None, description="结束日期(YYYY-MM-DD)")
    operator_id: Optional[int] = Field(None, description="操作员ID筛选")
    customer: Optional[str] = Field(None, description="客户筛选")
    
    @validator('sale_type')
    def validate_sale_type(cls, v):
        if v is not None and v not in ['retail', 'wholesale']:
            raise ValueError('销售类型必须是 retail 或 wholesale')
        return v 