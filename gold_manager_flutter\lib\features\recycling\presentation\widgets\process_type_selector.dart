import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../models/common/enums.dart';

/// 处理方式选择组件
class ProcessTypeSelector extends StatelessWidget {
  final RecyclingProcessType selectedType;
  final Function(RecyclingProcessType) onTypeChanged;
  final String description;

  const ProcessTypeSelector({
    super.key,
    required this.selectedType,
    required this.onTypeChanged,
    required this.description,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.backgroundLight,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.border),
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '处理方式',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 16),
          Wrap(
            spacing: 10,
            runSpacing: 10,
            children: [
              _buildTypeButton(RecyclingProcessType.sell, '直接卖出', Icons.attach_money),
              _buildTypeButton(RecyclingProcessType.separate, '金银分离', Icons.science),
              _buildTypeButton(RecyclingProcessType.repair, '维修再销售', Icons.build),
              _buildTypeButton(RecyclingProcessType.resell, '直接二次销售', Icons.shopping_bag),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: _getTypeColor().withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: _getTypeColor().withOpacity(0.3)),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  _getTypeIcon(),
                  color: _getTypeColor(),
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    description,
                    style: TextStyle(
                      color: _getTypeColor(),
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTypeButton(RecyclingProcessType type, String label, IconData icon) {
    final bool isSelected = selectedType == type;
    
    return GestureDetector(
      onTap: () => onTypeChanged(type),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: isSelected ? _getColorForType(type) : Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? _getColorForType(type) : AppColors.border,
          ),
          boxShadow: isSelected 
              ? [BoxShadow(
                  color: _getColorForType(type).withOpacity(0.3),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                )]
              : null,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: isSelected ? Colors.white : _getColorForType(type),
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              label,
              style: TextStyle(
                color: isSelected ? Colors.white : AppColors.textPrimary,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getColorForType(RecyclingProcessType type) {
    switch (type) {
      case RecyclingProcessType.sell:
        return AppColors.success;
      case RecyclingProcessType.separate:
        return AppColors.primary;
      case RecyclingProcessType.repair:
        return AppColors.warning;
      case RecyclingProcessType.resell:
        return AppColors.info;
      default:
        return AppColors.textSecondary;
    }
  }
  
  Color _getTypeColor() {
    return _getColorForType(selectedType);
  }
  
  IconData _getTypeIcon() {
    switch (selectedType) {
      case RecyclingProcessType.sell:
        return Icons.attach_money;
      case RecyclingProcessType.separate:
        return Icons.science;
      case RecyclingProcessType.repair:
        return Icons.build;
      case RecyclingProcessType.resell:
        return Icons.shopping_bag;
      default:
        return Icons.help_outline;
    }
  }
} 