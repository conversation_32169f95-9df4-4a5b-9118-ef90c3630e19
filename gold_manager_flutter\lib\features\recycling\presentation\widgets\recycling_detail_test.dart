import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../models/recycling/recycling_model.dart';
import 'recycling_detail_dialog.dart';

/// 旧料回收详情对话框测试页面
/// 用于测试详情对话框的功能
class RecyclingDetailTest extends StatelessWidget {
  const RecyclingDetailTest({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('旧料回收详情测试'),
      ),
      body: Center(
        child: ElevatedButton(
          onPressed: _showTestDialog,
          child: const Text('显示测试详情对话框'),
        ),
      ),
    );
  }

  /// 显示测试对话框
  void _showTestDialog() {
    // 创建测试数据
    final testOrder = RecyclingOrder(
      id: 1,
      orderNo: 'R202412190001',
      orderDate: DateTime.now(),
      customerId: 1,
      customerName: '张三',
      customerPhone: '13800138000',
      totalWeight: 33.7,
      totalAmount: 9407.0,
      itemCount: 3,
      remark: '客户要求快速处理',
      status: 0,
      createdAt: DateTime.now(),
      createdBy: 1,
      creatorName: '李四',
      storeId: 1,
      storeName: '总店',
      items: [
        RecyclingItem(
          id: 1,
          orderId: 1,
          categoryId: 1,
          categoryName: '黄金首饰',
          itemName: '黄金项链',
          weight: 15.5,
          price: 380.0,
          amount: 5890.0,
          photoUrl: null,
          remark: '成色较好',
          status: 0,
        ),
        RecyclingItem(
          id: 2,
          orderId: 1,
          categoryId: 2,
          categoryName: '银饰',
          itemName: '银手镯',
          weight: 10.0,
          price: 36.0,
          amount: 360.0,
          photoUrl: null,
          remark: null,
          status: 1,
        ),
        RecyclingItem(
          id: 3,
          orderId: 1,
          categoryId: 1,
          categoryName: '黄金首饰',
          itemName: '黄金戒指',
          weight: 8.2,
          price: 385.0,
          amount: 3157.0,
          photoUrl: null,
          remark: '有轻微磨损',
          status: 2,
        ),
      ],
    );

    // 显示详情对话框
    Get.dialog(
      RecyclingDetailDialog(recyclingOrder: testOrder),
      barrierDismissible: true,
    );
  }
}
