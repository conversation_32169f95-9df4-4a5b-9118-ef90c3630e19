import 'package:flutter/material.dart';

import '../../../models/print/print_template_config.dart';
import '../../../core/constants/border_styles.dart';

/// 打印模板实时预览组件
///
/// 提供真实的PDF预览效果，与全屏预览保持一致
class PrintTemplatePreview extends StatefulWidget {
  /// 模板配置
  final PrintTemplateConfig template;
  
  /// 示例数据
  final Map<String, dynamic> sampleData;
  
  /// 字段位置变更回调
  final Function(String fieldKey, FieldPosition position)? onFieldPositionChanged;
  
  /// 是否启用拖拽
  final bool enableDrag;
  
  /// 缩放比例
  final double scale;

  const PrintTemplatePreview({
    super.key,
    required this.template,
    required this.sampleData,
    this.onFieldPositionChanged,
    this.enableDrag = true,
    this.scale = 1.0,
  });

  @override
  State<PrintTemplatePreview> createState() => _PrintTemplatePreviewState();
}

class _PrintTemplatePreviewState extends State<PrintTemplatePreview> {
  /// 当前拖拽的字段
  String? _draggingField;

  /// 当前选中的字段
  String? _selectedField;

  /// 字段位置缓存
  final Map<String, FieldPosition> _fieldPositions = {};

  /// 字段层级管理 - 数值越大越在上层
  final Map<String, int> _fieldZIndex = {};

  /// 右键菜单位置
  Offset? _contextMenuPosition;

  @override
  void initState() {
    super.initState();
    _initializeFieldPositions();
  }

  @override
  void didUpdateWidget(PrintTemplatePreview oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.template != widget.template) {
      _initializeFieldPositions();
    }
  }

  /// 初始化字段位置
  void _initializeFieldPositions() {
    _fieldPositions.clear();
    _fieldZIndex.clear();

    // 公司信息字段
    _fieldPositions['company_name'] = widget.template.companyInfo.namePosition;
    _fieldPositions['company_address'] = widget.template.companyInfo.addressPosition;
    _fieldPositions['company_phone'] = widget.template.companyInfo.phonePosition;

    // 表头字段
    _fieldPositions['order_no'] = widget.template.headerConfig.orderNoPosition;
    _fieldPositions['customer'] = widget.template.headerConfig.customerPosition;
    _fieldPositions['sale_type'] = widget.template.headerConfig.saleTypePosition;
    _fieldPositions['date_time'] = widget.template.headerConfig.dateTimePosition;

    // 表格位置
    _fieldPositions['item_table'] = widget.template.itemTableConfig.tablePosition;

    // 初始化层级 - 按默认顺序分配层级
    int zIndex = 0;
    for (final key in _fieldPositions.keys) {
      _fieldZIndex[key] = zIndex++;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppBorderStyles.borderColor),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // 预览工具栏
          _buildPreviewToolbar(),
          const Divider(height: 1),
          
          // 预览画布
          Expanded(
            child: _buildPreviewCanvas(),
          ),
        ],
      ),
    );
  }

  /// 构建预览工具栏
  Widget _buildPreviewToolbar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Icon(Icons.preview, color: Colors.blue[600], size: 20),
          const SizedBox(width: 8),
          const Text(
            '实时预览',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const Spacer(),
          Text(
            '比例: ${(widget.scale * 100).toInt()}%',
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
          ),
          const SizedBox(width: 16),
          Text(
            '${widget.template.pageConfig.width.toInt()}mm × ${widget.template.pageConfig.height.toInt()}mm',
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建预览画布
  Widget _buildPreviewCanvas() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // 计算画布尺寸，保持210mm × 101.6mm的比例
        final aspectRatio = widget.template.pageConfig.width / widget.template.pageConfig.height;
        double canvasWidth = constraints.maxWidth - 32; // 留出边距
        double canvasHeight = canvasWidth / aspectRatio;

        if (canvasHeight > constraints.maxHeight - 32) {
          canvasHeight = constraints.maxHeight - 32;
          canvasWidth = canvasHeight * aspectRatio;
        }

        // 应用缩放
        canvasWidth *= widget.scale;
        canvasHeight *= widget.scale;

        return Center(
          child: GestureDetector(
            onTap: () {
              // 点击空白区域取消选中和隐藏右键菜单
              setState(() {
                _selectedField = null;
                _contextMenuPosition = null;
              });
            },
            child: DragTarget<String>(
              onWillAcceptWithDetails: (details) => true,
              onAcceptWithDetails: (details) {
                // 拖拽接受处理
              },
              builder: (context, candidateData, rejectedData) {
                return Container(
                  key: const ValueKey('preview_canvas'), // 添加key用于RenderBox查找
                  width: canvasWidth,
                  height: canvasHeight,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border.all(color: Colors.grey[400]!),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Stack(
                    children: [
                      // 网格背景
                      _buildGridBackground(canvasWidth, canvasHeight),

                      // 字段元素 - 按层级排序
                      ..._buildFieldElements(canvasWidth, canvasHeight),

                      // 右键菜单
                      if (_contextMenuPosition != null && _selectedField != null)
                        _buildContextMenu(),
                    ],
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  /// 构建网格背景
  Widget _buildGridBackground(double width, double height) {
    return CustomPaint(
      size: Size(width, height),
      painter: GridPainter(
        gridSize: 10.0 * widget.scale, // 10mm网格
        color: Colors.grey[300]!,
      ),
    );
  }

  /// 构建字段元素
  List<Widget> _buildFieldElements(double canvasWidth, double canvasHeight) {
    final elements = <Widget>[];

    // 计算像素比例 (画布像素 / 实际mm)
    final pixelRatio = canvasWidth / widget.template.pageConfig.width;

    // 创建字段元素映射，用于按层级排序
    final fieldElements = <String, Widget>{};

    // 公司名称
    if (widget.template.companyInfo.companyName.isNotEmpty) {
      final position = _fieldPositions['company_name'];
      if (position != null) {
        fieldElements['company_name'] = _buildDraggableField(
          key: 'company_name',
          text: widget.template.companyInfo.companyName,
          position: position,
          fontSize: widget.template.companyInfo.fontSize,
          isBold: widget.template.companyInfo.isBold,
          pixelRatio: pixelRatio,
        );
      }
    }

    // 公司地址
    if (widget.template.companyInfo.showAddress) {
      final position = _fieldPositions['company_address'];
      if (position != null) {
        final address = widget.sampleData['stockOutData']?['store_address'] ?? '广东省深圳市罗湖区黄金珠宝城A座101号';
        fieldElements['company_address'] = _buildDraggableField(
          key: 'company_address',
          text: address,
          position: position,
          fontSize: widget.template.companyInfo.fontSize * 0.8,
          isBold: false,
          pixelRatio: pixelRatio,
        );
      }
    }

    // 公司电话
    if (widget.template.companyInfo.showPhone) {
      final position = _fieldPositions['company_phone'];
      if (position != null) {
        final phone = widget.sampleData['stockOutData']?['store_phone'] ?? '0755-12345678';
        fieldElements['company_phone'] = _buildDraggableField(
          key: 'company_phone',
          text: '电话: $phone',
          position: position,
          fontSize: widget.template.companyInfo.fontSize * 0.8,
          isBold: false,
          pixelRatio: pixelRatio,
        );
      }
    }

    // 单号
    if (widget.template.headerConfig.showOrderNo) {
      final position = _fieldPositions['order_no'];
      if (position != null) {
        final orderNo = widget.sampleData['stockOutData']?['order_no'] ?? 'CK20241201001';
        fieldElements['order_no'] = _buildDraggableField(
          key: 'order_no',
          text: '单号: $orderNo',
          position: position,
          fontSize: widget.template.headerConfig.fontSize,
          isBold: false,
          pixelRatio: pixelRatio,
        );
      }
    }

    // 客户
    if (widget.template.headerConfig.showCustomer) {
      final position = _fieldPositions['customer'];
      if (position != null) {
        final customer = widget.sampleData['stockOutData']?['customer'] ?? '张三';
        fieldElements['customer'] = _buildDraggableField(
          key: 'customer',
          text: '客户: $customer',
          position: position,
          fontSize: widget.template.headerConfig.fontSize,
          isBold: false,
          pixelRatio: pixelRatio,
        );
      }
    }

    // 销售类型
    if (widget.template.headerConfig.showSaleType) {
      final position = _fieldPositions['sale_type'];
      if (position != null) {
        final saleType = widget.sampleData['stockOutData']?['sale_type'] == 'retail' ? '零售' : '批发';
        fieldElements['sale_type'] = _buildDraggableField(
          key: 'sale_type',
          text: '类型: $saleType',
          position: position,
          fontSize: widget.template.headerConfig.fontSize,
          isBold: false,
          pixelRatio: pixelRatio,
        );
      }
    }

    // 日期时间
    if (widget.template.headerConfig.showDateTime) {
      final position = _fieldPositions['date_time'];
      if (position != null) {
        final dateTime = DateTime.now().toString().substring(0, 19);
        fieldElements['date_time'] = _buildDraggableField(
          key: 'date_time',
          text: '时间: $dateTime',
          position: position,
          fontSize: widget.template.headerConfig.fontSize,
          isBold: false,
          pixelRatio: pixelRatio,
        );
      }
    }

    // 商品明细表格
    final tablePosition = _fieldPositions['item_table'];
    if (tablePosition != null) {
      fieldElements['item_table'] = _buildDraggableTable(
        key: 'item_table',
        position: tablePosition,
        pixelRatio: pixelRatio,
      );
    }

    // 按层级排序并返回
    final sortedKeys = fieldElements.keys.toList()
      ..sort((a, b) => (_fieldZIndex[a] ?? 0).compareTo(_fieldZIndex[b] ?? 0));

    for (final key in sortedKeys) {
      final element = fieldElements[key];
      if (element != null) {
        elements.add(element);
      }
    }

    return elements;
  }

  /// 构建可拖拽字段
  Widget _buildDraggableField({
    required String key,
    required String text,
    required FieldPosition position,
    required double fontSize,
    required bool isBold,
    required double pixelRatio,
  }) {
    final isSelected = _selectedField == key;
    final isDragging = _draggingField == key;

    return Positioned(
      left: position.x * pixelRatio,
      top: position.y * pixelRatio,
      child: widget.enableDrag
          ? Draggable<String>(
              data: key,
              feedback: Material(
                color: Colors.transparent,
                child: _buildFieldText(text, fontSize, isBold, true, false),
              ),
              childWhenDragging: Container(
                padding: const EdgeInsets.symmetric(horizontal: 2, vertical: 1),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.2),
                  border: Border.all(color: Colors.blue, width: 1, style: BorderStyle.solid),
                  borderRadius: BorderRadius.circular(2),
                ),
                child: Text(
                  text,
                  style: TextStyle(
                    fontSize: fontSize * widget.scale,
                    fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
                    color: Colors.blue.withValues(alpha: 0.6),
                  ),
                ),
              ),
              onDragStarted: () {
                setState(() {
                  _draggingField = key;
                  _selectedField = key; // 拖拽时自动选中
                });
              },
              onDragEnd: (details) {
                setState(() {
                  _draggingField = null;
                });

                _handleDragEnd(key, details.offset, pixelRatio);
              },
              child: GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedField = _selectedField == key ? null : key; // 切换选中状态
                    _contextMenuPosition = null; // 隐藏右键菜单
                  });
                },
                onSecondaryTapDown: (details) {
                  setState(() {
                    _selectedField = key; // 右键时选中组件
                    // 使用局部坐标，相对于预览区域
                    _contextMenuPosition = details.localPosition;
                  });
                },
                child: _buildFieldText(text, fontSize, isBold, isDragging, isSelected),
              ),
            )
          : GestureDetector(
              onTap: () {
                setState(() {
                  _selectedField = _selectedField == key ? null : key; // 切换选中状态
                  _contextMenuPosition = null; // 隐藏右键菜单
                });
              },
              onSecondaryTapDown: (details) {
                setState(() {
                  _selectedField = key; // 右键时选中组件
                  // 使用局部坐标，相对于预览区域
                  _contextMenuPosition = details.localPosition;
                });
              },
              child: _buildFieldText(text, fontSize, isBold, false, isSelected),
            ),
    );
  }

  /// 处理拖拽结束事件
  void _handleDragEnd(String fieldKey, Offset globalOffset, double pixelRatio, {bool isTable = false}) {
    try {
      // 查找画布容器的RenderBox
      const canvasKey = ValueKey('preview_canvas');
      final canvasContext = _findContextByKey(context, canvasKey);

      if (canvasContext != null) {
        final canvasRenderBox = canvasContext.findRenderObject() as RenderBox?;
        if (canvasRenderBox != null) {
          // 将全局坐标转换为画布本地坐标
          final localPosition = canvasRenderBox.globalToLocal(globalOffset);

          // 转换为毫米坐标
          final newX = localPosition.dx / pixelRatio;
          final newY = localPosition.dy / pixelRatio;

          // 确保位置在有效范围内
          double clampedX, clampedY;
          if (isTable) {
            // 表格组件 - 修复拖拽边界限制问题
            final tableSize = _calculateTableSize();
            final margins = widget.template.pageConfig.margins;
            final pageWidth = widget.template.pageConfig.width;
            final pageHeight = widget.template.pageConfig.height;

            // 计算可拖拽的X范围：确保表格不超出页面边界
            final minX = margins.left;
            final maxX = pageWidth - margins.right - tableSize.width;

            // 计算可拖拽的Y范围：确保表格不超出页面边界
            final minY = margins.top;
            final maxY = pageHeight - margins.bottom - tableSize.height;

            // 使用更宽松的边界检查，允许表格拖拽到页面任意位置
            // 确保最大值不小于最小值，避免无效的clamp范围
            final validMaxX = maxX < minX ? pageWidth - margins.right : maxX;
            final validMaxY = maxY < minY ? pageHeight - margins.bottom : maxY;

            clampedX = newX.clamp(minX, validMaxX);
            clampedY = newY.clamp(minY, validMaxY);
          } else {
            // 文本字段 - 允许在整个页面范围内拖拽
            final margins = widget.template.pageConfig.margins;
            clampedX = newX.clamp(margins.left, widget.template.pageConfig.width - margins.right);
            clampedY = newY.clamp(margins.top, widget.template.pageConfig.height - margins.bottom);
          }

          final newPosition = FieldPosition(x: clampedX, y: clampedY);

          // 更新本地位置缓存
          setState(() {
            _fieldPositions[fieldKey] = newPosition;
          });

          // 通知位置变更
          widget.onFieldPositionChanged?.call(fieldKey, newPosition);
        }
      }
    } catch (e) {
      // 拖拽失败时保持原位置
      debugPrint('拖拽处理失败: $e');
    }
  }

  /// 通过Key查找Context
  BuildContext? _findContextByKey(BuildContext context, Key key) {
    BuildContext? result;

    void visitor(Element element) {
      if (element.widget.key == key) {
        result = element;
        return;
      }
      element.visitChildren(visitor);
    }

    context.visitChildElements(visitor);
    return result;
  }

  /// 构建右键菜单
  Widget _buildContextMenu() {
    if (_contextMenuPosition == null || _selectedField == null) {
      return const SizedBox.shrink();
    }

    return Positioned(
      left: _contextMenuPosition!.dx,
      top: _contextMenuPosition!.dy,
      child: Material(
        elevation: 8,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          width: 120,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildContextMenuItem(
                icon: Icons.vertical_align_top,
                text: '置于顶层',
                onTap: () => _moveToTop(_selectedField!),
              ),
              _buildContextMenuItem(
                icon: Icons.vertical_align_bottom,
                text: '置于底层',
                onTap: () => _moveToBottom(_selectedField!),
              ),
              _buildContextMenuItem(
                icon: Icons.keyboard_arrow_up,
                text: '上移一层',
                onTap: () => _moveUp(_selectedField!),
              ),
              _buildContextMenuItem(
                icon: Icons.keyboard_arrow_down,
                text: '下移一层',
                onTap: () => _moveDown(_selectedField!),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建右键菜单项
  Widget _buildContextMenuItem({
    required IconData icon,
    required String text,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: () {
        onTap();
        setState(() {
          _contextMenuPosition = null; // 隐藏菜单
        });
      },
      child: Container(
        height: 36,
        padding: const EdgeInsets.symmetric(horizontal: 12),
        child: Row(
          children: [
            Icon(icon, size: 16, color: Colors.grey[600]),
            const SizedBox(width: 8),
            Text(
              text,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[800],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 移动到顶层
  void _moveToTop(String fieldKey) {
    final maxZIndex = _fieldZIndex.values.fold(0, (max, value) => value > max ? value : max);
    setState(() {
      _fieldZIndex[fieldKey] = maxZIndex + 1;
    });
  }

  /// 移动到底层
  void _moveToBottom(String fieldKey) {
    final minZIndex = _fieldZIndex.values.fold(0, (min, value) => value < min ? value : min);
    setState(() {
      _fieldZIndex[fieldKey] = minZIndex - 1;
    });
  }

  /// 上移一层
  void _moveUp(String fieldKey) {
    final currentZIndex = _fieldZIndex[fieldKey] ?? 0;
    setState(() {
      _fieldZIndex[fieldKey] = currentZIndex + 1;
    });
  }

  /// 下移一层
  void _moveDown(String fieldKey) {
    final currentZIndex = _fieldZIndex[fieldKey] ?? 0;
    setState(() {
      _fieldZIndex[fieldKey] = currentZIndex - 1;
    });
  }

  /// 构建字段文本
  Widget _buildFieldText(String text, double fontSize, bool isBold, bool isDragging, bool isSelected) {
    Color? backgroundColor;
    Color? borderColor;
    Color textColor = Colors.black;

    if (isDragging) {
      backgroundColor = Colors.blue.withValues(alpha: 0.1);
      borderColor = Colors.blue;
      textColor = Colors.blue;
    } else if (isSelected) {
      backgroundColor = Colors.orange.withValues(alpha: 0.1);
      borderColor = Colors.orange;
      textColor = Colors.orange[700]!;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 2, vertical: 1),
      decoration: (isDragging || isSelected)
          ? BoxDecoration(
              color: backgroundColor,
              border: Border.all(color: borderColor!, width: 1),
              borderRadius: BorderRadius.circular(2),
            )
          : null,
      child: Text(
        text,
        style: TextStyle(
          fontSize: fontSize * widget.scale,
          fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
          color: textColor,
        ),
      ),
    );
  }

  /// 构建可拖拽表格
  Widget _buildDraggableTable({
    required String key,
    required FieldPosition position,
    required double pixelRatio,
  }) {
    final isSelected = _selectedField == key;
    final isDragging = _draggingField == key;

    return Positioned(
      left: position.x * pixelRatio,
      top: position.y * pixelRatio,
      child: widget.enableDrag
          ? Draggable<String>(
              data: key,
              feedback: Material(
                color: Colors.transparent,
                child: _buildTableWidget(key, pixelRatio, true, false),
              ),
              childWhenDragging: _buildDragPlaceholder(pixelRatio),
              onDragStarted: () {
                setState(() {
                  _draggingField = key;
                  _selectedField = key; // 拖拽时自动选中
                });
              },
              onDragEnd: (details) {
                setState(() {
                  _draggingField = null;
                });

                _handleDragEnd(key, details.offset, pixelRatio, isTable: true);
              },
              child: GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedField = _selectedField == key ? null : key; // 切换选中状态
                    _contextMenuPosition = null; // 隐藏右键菜单
                  });
                },
                onSecondaryTapDown: (details) {
                  setState(() {
                    _selectedField = key; // 右键时选中组件
                    // 使用局部坐标，相对于预览区域
                    _contextMenuPosition = details.localPosition;
                  });
                },
                child: _buildTableWidget(key, pixelRatio, isDragging, isSelected),
              ),
            )
          : GestureDetector(
              onTap: () {
                setState(() {
                  _selectedField = _selectedField == key ? null : key; // 切换选中状态
                  _contextMenuPosition = null; // 隐藏右键菜单
                });
              },
              onSecondaryTapDown: (details) {
                setState(() {
                  _selectedField = key; // 右键时选中组件
                  // 使用局部坐标，相对于预览区域
                  _contextMenuPosition = details.localPosition;
                });
              },
              child: _buildTableWidget(key, pixelRatio, false, isSelected),
            ),
    );
  }

  /// 构建表格组件
  Widget _buildTableWidget(String key, double pixelRatio, bool isDragging, bool isSelected) {
    Color borderColor = Colors.grey;
    Color? backgroundColor = Colors.grey[50];
    Color textColor = Colors.grey[600]!;
    int borderWidth = 1;

    if (isDragging) {
      borderColor = Colors.blue;
      backgroundColor = Colors.blue.withValues(alpha: 0.1);
      textColor = Colors.blue;
      borderWidth = 2;
    } else if (isSelected) {
      borderColor = Colors.orange;
      backgroundColor = Colors.orange.withValues(alpha: 0.1);
      textColor = Colors.orange[700]!;
      borderWidth = 2;
    }

    // 获取可见列配置
    final visibleColumns = widget.template.itemTableConfig.visibleColumns;
    final actualColumnWidths = widget.template.itemTableConfig.getActualColumnWidths();
    final headerFontSize = widget.template.itemTableConfig.headerFontSize * widget.scale;
    final contentFontSize = widget.template.itemTableConfig.contentFontSize * widget.scale;

    // 计算表格总宽度，与全屏预览保持一致的计算逻辑
    double totalWidth = 0;
    for (final column in visibleColumns) {
      final width = actualColumnWidths[column] ?? 50.0; // 默认宽度50
      totalWidth += width;
    }

    // 计算表格的可用宽度（页面宽度减去边距和位置偏移）
    final pageWidth = widget.template.pageConfig.width; // mm
    final leftMargin = widget.template.pageConfig.margins.left; // mm
    final rightMargin = widget.template.pageConfig.margins.right; // mm
    final position = widget.template.itemTableConfig.tablePosition;
    final availableWidth = pageWidth - leftMargin - rightMargin - position.x; // mm

    // 使用可用宽度和配置宽度的较小值，确保表格不会超出页面
    final effectiveWidth = totalWidth.clamp(0.0, availableWidth);
    final tableWidthPx = effectiveWidth * pixelRatio;

    // 不设置固定高度，让内容自适应
    return Container(
      width: tableWidthPx,
      decoration: BoxDecoration(
        border: Border.all(
          color: borderColor,
          width: borderWidth.toDouble(),
        ),
        color: backgroundColor,
      ),
      child: ClipRect(
        child: _buildTableContent(
          visibleColumns: visibleColumns,
          columnWidths: actualColumnWidths,
          headerFontSize: headerFontSize,
          contentFontSize: contentFontSize,
          pixelRatio: pixelRatio,
          textColor: textColor,
          borderColor: borderColor,
        ),
      ),
    );
  }

  /// 构建表格内容
  Widget _buildTableContent({
    required List<String> visibleColumns,
    required Map<String, double> columnWidths,
    required double headerFontSize,
    required double contentFontSize,
    required double pixelRatio,
    required Color textColor,
    required Color borderColor,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min, // 防止溢出
      children: [
        // 表头行
        Flexible(
          child: _buildTableRow(
            columns: visibleColumns,
            columnWidths: columnWidths,
            pixelRatio: pixelRatio,
            fontSize: headerFontSize,
            textColor: textColor,
            borderColor: borderColor,
            isHeader: true,
            data: visibleColumns, // 表头显示列名
          ),
        ),
        // 示例数据行1
        Flexible(
          child: _buildTableRow(
            columns: visibleColumns,
            columnWidths: columnWidths,
            pixelRatio: pixelRatio,
            fontSize: contentFontSize,
            textColor: textColor,
            borderColor: borderColor,
            isHeader: false,
            data: _getSampleRowData(visibleColumns, 1),
          ),
        ),
        // 示例数据行2
        Flexible(
          child: _buildTableRow(
            columns: visibleColumns,
            columnWidths: columnWidths,
            pixelRatio: pixelRatio,
            fontSize: contentFontSize,
            textColor: textColor,
            borderColor: borderColor,
            isHeader: false,
            data: _getSampleRowData(visibleColumns, 2),
          ),
        ),
      ],
    );
  }

  /// 构建表格行
  Widget _buildTableRow({
    required List<String> columns,
    required Map<String, double> columnWidths,
    required double pixelRatio,
    required double fontSize,
    required Color textColor,
    required Color borderColor,
    required bool isHeader,
    required List<String> data,
  }) {
    // 获取配置的行高并转换为像素
    // 注意：pixelRatio已经包含了widget.scale，所以不需要再次应用缩放
    final rowHeight = widget.template.itemTableConfig.rowHeight;
    final rowHeightPx = rowHeight * pixelRatio;

    return SizedBox(
      height: rowHeightPx,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: columns.asMap().entries.map((entry) {
          final index = entry.key;
          final column = entry.value;
          final cellData = index < data.length ? data[index] : '';

          // 按比例计算flex值
          final configWidth = columnWidths[column] ?? 50.0;
          final flex = (configWidth * 100).round(); // 转换为整数flex值

          return Expanded(
            flex: flex,
            child: Container(
              height: rowHeightPx,
              padding: EdgeInsets.symmetric(horizontal: 2 * widget.scale, vertical: 0),
              decoration: BoxDecoration(
                border: Border(
                  right: index < columns.length - 1
                      ? BorderSide(color: borderColor, width: 0.5)
                      : BorderSide.none,
                  bottom: BorderSide(color: borderColor, width: 0.5),
                ),
              ),
              child: Center(
                child: Text(
                  cellData,
                  style: TextStyle(
                    fontSize: fontSize,
                    color: textColor,
                    fontWeight: isHeader ? FontWeight.bold : FontWeight.normal,
                  ),
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  /// 获取示例行数据
  List<String> _getSampleRowData(List<String> columns, int rowIndex) {
    final sampleData = <String, List<String>>{
      '序号': ['1', '2'],
      '条码': ['JW001', 'YS002'],
      '商品名称': ['黄金戒指', '银手镯'],
      '规格': ['16号', ''],
      '数量': ['1', '1'],
      '总重(g)': ['5.20', '15.80'],
      '金重(g)': ['5.20', '0.00'],
      '银重(g)': ['0.00', '15.80'],
      '单价(¥)': ['450.00', '8.00'],
      '工费(¥)': ['30.00', '5.00'],
      '金额(¥)': ['2496.00', '205.40'],
      // 兼容旧字段名
      '重量': ['5.20', '15.80'],
      '单价': ['450.00', '8.00'],
      '金额': ['2496.00', '205.40'],
    };

    return columns.map((column) {
      final columnData = sampleData[column];
      if (columnData != null && rowIndex - 1 < columnData.length) {
        return columnData[rowIndex - 1];
      }
      return '';
    }).toList();
  }

  /// 构建拖拽时的占位符
  Widget _buildDragPlaceholder(double pixelRatio) {
    // 获取可见列配置
    final visibleColumns = widget.template.itemTableConfig.visibleColumns;
    final actualColumnWidths = widget.template.itemTableConfig.getActualColumnWidths();

    // 计算表格总宽度，与主表格保持一致的计算逻辑
    double totalWidth = 0;
    for (final column in visibleColumns) {
      final width = actualColumnWidths[column] ?? 50.0; // 默认宽度50
      totalWidth += width;
    }

    // 计算表格的可用宽度（页面宽度减去边距和位置偏移）
    final pageWidth = widget.template.pageConfig.width; // mm
    final leftMargin = widget.template.pageConfig.margins.left; // mm
    final rightMargin = widget.template.pageConfig.margins.right; // mm
    final position = widget.template.itemTableConfig.tablePosition;
    final availableWidth = pageWidth - leftMargin - rightMargin - position.x; // mm

    // 使用可用宽度和配置宽度的较小值，确保表格不会超出页面
    final effectiveWidth = totalWidth.clamp(0.0, availableWidth);
    final tableWidthPx = effectiveWidth * pixelRatio;

    // 不设置固定高度，让内容自适应
    return Container(
      width: tableWidthPx,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.blue, width: 2),
        color: Colors.blue.withValues(alpha: 0.1),
      ),
      child: Center(
        child: Text(
          '商品明细表格',
          style: TextStyle(
            fontSize: 10 * widget.scale,
            color: Colors.blue.withValues(alpha: 0.6),
          ),
        ),
      ),
    );
  }

  /// 计算表格尺寸（毫米单位）
  Size _calculateTableSize() {
    // 获取可见列配置
    final visibleColumns = widget.template.itemTableConfig.visibleColumns;
    final actualColumnWidths = widget.template.itemTableConfig.getActualColumnWidths();

    // 计算表格总宽度（毫米）
    double totalWidth = 0;
    for (final column in visibleColumns) {
      final width = actualColumnWidths[column] ?? 50.0; // 默认宽度50mm
      totalWidth += width;
    }

    // 计算表格的可用宽度（页面宽度减去边距）
    final pageWidth = widget.template.pageConfig.width; // mm
    final leftMargin = widget.template.pageConfig.margins.left; // mm
    final rightMargin = widget.template.pageConfig.margins.right; // mm
    final availableWidth = pageWidth - leftMargin - rightMargin; // mm

    // 使用可用宽度和配置宽度的较小值，确保表格不会超出页面
    final effectiveWidth = totalWidth.clamp(0.0, availableWidth);

    // 计算表格高度（毫米）- 使用配置的行高
    final rowHeight = widget.template.itemTableConfig.rowHeight;
    final tableHeight = rowHeight * 3; // 表头 + 2行示例数据

    return Size(effectiveWidth, tableHeight);
  }
}

/// 网格画笔
class GridPainter extends CustomPainter {
  final double gridSize;
  final Color color;

  GridPainter({
    required this.gridSize,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 0.5;

    // 绘制垂直线
    for (double x = 0; x <= size.width; x += gridSize) {
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }

    // 绘制水平线
    for (double y = 0; y <= size.height; y += gridSize) {
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
