import 'package:get/get.dart';
import '../core/config/app_config.dart';
import '../core/models/api_response.dart';
import '../core/utils/logger.dart';
import '../models/user/user.dart';
import '../core/services/api_client.dart';
import '../core/services/storage_service.dart';
import '../core/constants/app_permissions.dart';

/// 认证服务
/// 处理用户认证、权限检查等功能
class AuthService extends GetxService {
  final ApiClient _apiClient = Get.find<ApiClient>();
  final StorageService _storageService = Get.find<StorageService>();

  // 用户信息
  final RxBool isLoggedIn = false.obs;
  final RxString token = ''.obs;
  final RxInt userId = 0.obs;
  final RxString userName = ''.obs;
  final RxString userNickname = ''.obs;
  final RxString userRole = ''.obs;
  final RxInt storeId = 0.obs;
  final RxString storeName = ''.obs;
  final Rx<User?> currentUser = Rx<User?>(null);

  // 权限列表
  final RxList<String> permissions = <String>[].obs;

  /// 初始化服务
  Future<AuthService> init() async {
    LoggerService.d('AuthService 初始化');

    // 从本地存储加载用户认证信息
    await _loadUserFromStorage();

    return this;
  }

  /// 从本地存储加载用户信息
  Future<void> _loadUserFromStorage() async {
    try {
      final savedToken = _storageService.getToken();
      if (savedToken != null && savedToken.isNotEmpty) {
        token.value = savedToken;

        // 暂时设置为已登录状态，稍后在需要时验证Token
        isLoggedIn.value = true;

        // 尝试从存储中加载用户基本信息
        final savedUserId = _storageService.getUserId();
        final savedUserName = _storageService.getUserName();
        final savedUserNickname = _storageService.getUserNickname();
        final savedUserRole = _storageService.getUserRole();
        final savedStoreId = _storageService.getStoreId();
        final savedStoreName = _storageService.getStoreName();
        final savedPermissions = _storageService.getUserPermissions(); // 🔧 新增：加载权限信息

        if (savedUserId != null && savedUserName != null) {
          userId.value = savedUserId;
          userName.value = savedUserName;
          userNickname.value = savedUserNickname ?? '';
          userRole.value = savedUserRole ?? 'admin'; // 默认为admin角色
          storeId.value = savedStoreId ?? 0;

          // 🔧 关键修复：优先使用保存的权限信息
          if (savedPermissions != null && savedPermissions.isNotEmpty) {
            // 使用保存的权限信息
            permissions.value = savedPermissions;
            LoggerService.i('✅ 从本地存储恢复权限: $savedPermissions');
          } else {
            // 兜底逻辑：根据用户名判断权限（与后端逻辑保持一致）
            if (savedUserName == 'admin') {
              // admin用户是超级管理员
              permissions.value = ['super.admin'];
              LoggerService.i('⚠️  兜底设置admin用户为超级管理员权限');
            } else {
              // 其他用户给予基础权限
              permissions.value = [
                'store.view', 'jewelry.view', 'member.view',
                'stock.view', 'system.dashboard'
              ];
              LoggerService.i('⚠️  兜底设置普通用户基础权限');
            }
          }

          // 🔧 修复：使用智能门店名称设置逻辑
          _setStoreNameFromStorage(savedStoreName, savedUserName);

          LoggerService.i('✅ 用户信息从本地存储恢复成功:');
          LoggerService.i('   - 用户名: $savedUserName');
          LoggerService.i('   - 角色: ${userRole.value}');
          LoggerService.i('   - 门店ID: ${storeId.value}');
          LoggerService.i('   - 门店名称: ${storeName.value}');
          LoggerService.i('   - 权限: ${permissions.toList()}');
        }
      }
    } catch (e) {
      LoggerService.w('Failed to load user from storage: $e');
      _clearUserInfo();
    }
  }

  /// 🔧 智能门店名称设置逻辑（用于本地存储恢复）
  void _setStoreNameFromStorage(String? savedStoreName, String userName) {
    LoggerService.i('🏪 设置门店名称从本地存储:');
    LoggerService.i('   - savedStoreName: $savedStoreName');
    LoggerService.i('   - userName: $userName');
    LoggerService.i('   - 当前权限: ${permissions.toList()}');

    // 🔧 优化：优先检查权限，确保超级管理员始终显示"所有门店"
    if (permissions.contains('super.admin')) {
      storeName.value = '所有门店';
      LoggerService.i('   ✅ 超级管理员权限，强制显示: ${storeName.value}');
    } else if (userName == 'admin') {
      // 兜底逻辑：如果权限还没设置但用户名是admin
      storeName.value = '所有门店';
      LoggerService.i('   ✅ admin用户兜底显示: ${storeName.value}');
    } else if (savedStoreName != null && savedStoreName.isNotEmpty) {
      // 普通用户使用保存的门店名称
      storeName.value = savedStoreName;
      LoggerService.i('   ✅ 普通用户使用保存的门店名称: ${storeName.value}');
    } else {
      storeName.value = '未知门店';
      LoggerService.i('   ⚠️  默认显示: ${storeName.value}');
    }
  }

  /// 从登录响应数据更新用户信息
  void _updateUserInfoFromLoginData(LoginResponseData loginData) {
    final adminInfo = loginData.adminInfo;

    LoggerService.i('🔍 开始处理登录响应数据');
    LoggerService.i('📋 AdminInfo原始数据: ${adminInfo.toJson()}');

    userId.value = adminInfo.id;
    userName.value = adminInfo.username;
    userNickname.value = adminInfo.nickname;
    userRole.value = _determineUserRole(adminInfo.permissions); // 根据权限确定角色
    storeId.value = adminInfo.storeId ?? 0;

    // 根据用户角色和门店信息设置门店名称
    if (adminInfo.storeName != null) {
      storeName.value = adminInfo.storeName!;
    } else if (adminInfo.permissions.contains('super.admin')) {
      storeName.value = '所有门店'; // 超级管理员显示"所有门店"
    } else {
      storeName.value = '未知门店'; // 其他情况显示"未知门店"
    }

    LoggerService.i('🏪 门店信息处理结果:');
    LoggerService.i('   - adminInfo.storeId: ${adminInfo.storeId} (${adminInfo.storeId.runtimeType})');
    LoggerService.i('   - adminInfo.storeName: ${adminInfo.storeName} (${adminInfo.storeName.runtimeType})');
    LoggerService.i('   - storeId.value: ${storeId.value}');
    LoggerService.i('   - storeName.value: ${storeName.value}');

    // 使用后端返回的权限
    permissions.value = adminInfo.permissions;

    // 创建用户对象
    currentUser.value = User(
      id: userId.value,
      username: userName.value,
      name: adminInfo.nickname,
      role: userRole.value,
      storeId: storeId.value,
      permissions: permissions,
    );

    // 保存用户信息到本地存储
    _storageService.setUserId(userId.value);
    _storageService.setUserName(userName.value);
    _storageService.setUserNickname(adminInfo.nickname);
    if (adminInfo.email != null) {
      _storageService.setUserEmail(adminInfo.email!);
    }
    _storageService.setUserRole(userRole.value);
    if (adminInfo.storeId != null) {
      _storageService.setStoreId(adminInfo.storeId!);
    }

    // 🔧 关键修复：超级管理员门店名称保存逻辑
    if (adminInfo.permissions.contains('super.admin')) {
      // 超级管理员始终保存"所有门店"，不管后端返回什么
      _storageService.setStoreName('所有门店');
      LoggerService.i('✅ 超级管理员门店名称已保存到本地存储: 所有门店');
    } else if (adminInfo.storeName != null) {
      // 普通用户保存后端返回的门店名称
      _storageService.setStoreName(adminInfo.storeName!);
      LoggerService.i('✅ 普通用户门店名称已保存到本地存储: ${adminInfo.storeName}');
    } else {
      LoggerService.w('⚠️  门店名称为null，未保存到本地存储');
    }

    // 🔧 关键修复：保存权限信息到本地存储
    _storageService.setUserPermissions(adminInfo.permissions);
    LoggerService.i('✅ 权限信息已保存到本地存储: ${adminInfo.permissions}');

    LoggerService.i('💾 用户信息保存完成，当前内存状态:');
    LoggerService.i('   - storeName.value: ${storeName.value}');
    LoggerService.i('   - storeId.value: ${storeId.value}');
  }

  /// 从AdminInfo更新用户信息
  void _updateUserInfoFromAdminInfo(AdminInfo adminInfo) {
    userId.value = adminInfo.id;
    userName.value = adminInfo.username;
    userNickname.value = adminInfo.nickname;
    userRole.value = _determineUserRole(adminInfo.permissions); // 根据权限确定角色
    storeId.value = adminInfo.storeId ?? 0;

    // 根据用户角色和门店信息设置门店名称
    if (adminInfo.storeName != null) {
      storeName.value = adminInfo.storeName!;
    } else if (adminInfo.permissions.contains('super.admin')) {
      storeName.value = '所有门店'; // 超级管理员显示"所有门店"
    } else {
      storeName.value = '未知门店'; // 其他情况显示"未知门店"
    }

    // 使用后端返回的权限
    permissions.value = adminInfo.permissions;

    // 创建用户对象
    currentUser.value = User(
      id: userId.value,
      username: userName.value,
      name: adminInfo.nickname,
      role: userRole.value,
      storeId: storeId.value,
      permissions: permissions,
    );

    // 保存用户信息到本地存储
    _storageService.setUserId(userId.value);
    _storageService.setUserName(userName.value);
    _storageService.setUserNickname(adminInfo.nickname);
    if (adminInfo.email != null) {
      _storageService.setUserEmail(adminInfo.email!);
    }
    _storageService.setUserRole(userRole.value);
    if (adminInfo.storeId != null) {
      _storageService.setStoreId(adminInfo.storeId!);
    }

    // 🔧 关键修复：超级管理员门店名称保存逻辑（与上面保持一致）
    if (adminInfo.permissions.contains('super.admin')) {
      // 超级管理员始终保存"所有门店"，不管后端返回什么
      _storageService.setStoreName('所有门店');
      LoggerService.i('✅ 超级管理员门店名称已保存到本地存储: 所有门店');
    } else if (adminInfo.storeName != null) {
      // 普通用户保存后端返回的门店名称
      _storageService.setStoreName(adminInfo.storeName!);
      LoggerService.i('✅ 普通用户门店名称已保存到本地存储: ${adminInfo.storeName}');
    } else {
      LoggerService.w('⚠️  门店名称为null，未保存到本地存储');
    }

    // 🔧 关键修复：保存权限信息到本地存储
    _storageService.setUserPermissions(adminInfo.permissions);
    LoggerService.i('✅ 权限信息已保存到本地存储: ${adminInfo.permissions}');
  }



  /// 根据权限确定用户角色
  String _determineUserRole(List<String> userPermissions) {
    // 超级管理员
    if (userPermissions.contains('super.admin')) {
      return 'admin'; // 返回admin而不是中文，保持与前端逻辑一致
    }

    // 管理员 - 拥有大部分权限
    if (userPermissions.contains('admin.create') ||
        userPermissions.contains('admin.update') ||
        userPermissions.contains('admin.delete')) {
      return 'admin';
    }

    // 店长 - 拥有门店管理权限
    if (userPermissions.contains('store.create') ||
        userPermissions.contains('store.update')) {
      return 'manager';
    }

    // 经理 - 拥有部分管理权限
    if (userPermissions.contains('jewelry.create') &&
        userPermissions.contains('stock.in') &&
        userPermissions.contains('stock.out')) {
      return 'supervisor';
    }

    // 默认为员工
    return 'staff';
  }

  /// 清除用户信息
  void _clearUserInfo() {
    isLoggedIn.value = false;
    token.value = '';
    userId.value = 0;
    userName.value = '';
    userNickname.value = '';
    userRole.value = '';
    storeId.value = 0;
    storeName.value = '';
    permissions.value = [];
    currentUser.value = null;

    // 清除本地存储的用户信息
    _storageService.clearTokens();
    _storageService.remove('${AppConfig.storagePrefix}${AppConfig.userIdKey}');
    _storageService.remove('${AppConfig.storagePrefix}${AppConfig.userNameKey}');
    _storageService.remove('${AppConfig.storagePrefix}${AppConfig.userNicknameKey}');
    _storageService.remove('${AppConfig.storagePrefix}${AppConfig.userEmailKey}');
    _storageService.remove('${AppConfig.storagePrefix}${AppConfig.userRoleKey}');
    _storageService.remove('${AppConfig.storagePrefix}${AppConfig.storeIdKey}');
    _storageService.remove('${AppConfig.storagePrefix}${AppConfig.storeNameKey}');
    // 🔧 新增：清除权限信息
    _storageService.remove('${AppConfig.storagePrefix}user_permissions');

    LoggerService.i('✅ 用户信息已完全清除（内存+本地存储）');
  }

  /// 🔧 新增：强制清理本地存储数据（用于测试和调试）
  Future<void> forceCleanStorage() async {
    LoggerService.i('🧹 开始强制清理本地存储数据...');

    // 清除内存状态
    _clearUserInfo();

    // 额外清理可能遗留的数据
    await _storageService.clearUserData();

    LoggerService.i('✅ 本地存储数据强制清理完成');
  }

  /// 登录
  Future<bool> login(String username, String password, {bool rememberMe = false}) async {
    try {
      LoggerService.i('🚀 开始登录流程: $username');

      // 调用真实API登录
      final loginData = await _apiClient.login(username, password, rememberMe: rememberMe);

      LoggerService.i('📡 API登录响应成功，开始处理用户信息');

      // 更新用户信息
      _updateUserInfoFromLoginData(loginData);
      token.value = _storageService.getToken() ?? '';
      isLoggedIn.value = true;

      LoggerService.i('✅ 登录流程完成');
      LoggerService.i('🎯 最终状态检查:');
      LoggerService.i('   - isLoggedIn: ${isLoggedIn.value}');
      LoggerService.i('   - userName: ${userName.value}');
      LoggerService.i('   - storeName: ${storeName.value}');
      LoggerService.i('   - storeId: ${storeId.value}');

      return true;

    } catch (e) {
      LoggerService.e('❌ 登录失败', e);
      _clearUserInfo();
      return false;
    }
  }

  /// 登出
  Future<void> logout() async {
    try {
      // 尝试调用API登出
      try {
        await _apiClient.logout();
        LoggerService.i('API logout successful');
      } catch (e) {
        LoggerService.w('API logout failed: $e');
        // API登出失败不影响本地清理
      }

      // 清除认证信息
      _clearUserInfo();
      LoggerService.i('User logged out successfully');
    } catch (e) {
      LoggerService.e('登出失败', e);
      rethrow;
    }
  }

  /// 检查是否已登录
  bool checkLoggedIn() {
    return isLoggedIn.value;
  }

  /// 检查是否有特定权限
  bool hasPermission(String permission) {
    LoggerService.d('检查权限: $permission, 当前权限列表: $permissions');

    // 超级管理员拥有所有权限
    if (permissions.contains('super.admin')) {
      LoggerService.d('超级管理员权限，允许访问: $permission');
      return true;
    }

    bool hasPermission = permissions.contains(permission);
    LoggerService.d('权限检查结果: $permission = $hasPermission');
    return hasPermission;
  }

  /// 检查是否有任一权限
  bool hasAnyPermission(List<String> requiredPermissions) {
    // 超级管理员拥有所有权限
    if (permissions.contains('super.admin')) {
      return true;
    }

    return AppPermissions.hasAnyPermission(permissions, requiredPermissions);
  }

  /// 检查是否有所有权限
  bool hasAllPermissions(List<String> requiredPermissions) {
    // 超级管理员拥有所有权限
    if (permissions.contains('super.admin')) {
      return true;
    }

    return AppPermissions.hasAllPermissions(permissions, requiredPermissions);
  }

  /// 获取用户信息
  Future<User> getUserInfo() async {
    // 如果已有用户对象，直接返回
    if (currentUser.value != null) {
      return currentUser.value!;
    }

    // 从API获取最新用户信息
    final userData = await _apiClient.getCurrentUser();
    _updateUserInfoFromAdminInfo(userData);
    return currentUser.value!;
  }
}