import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../models/recycling/recycling_process.dart';
import '../../../../models/common/enums.dart';

/// 处理记录卡片组件
class ProcessItemCard extends StatelessWidget {
  final RecyclingProcess process;
  final VoidCallback onTap;
  final Function(int) onStatusChange;
  final VoidCallback onDelete;

  const ProcessItemCard({
    super.key,
    required this.process,
    required this.onTap,
    required this.onStatusChange,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: _getBorderColor(),
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '编号: ${process.processNo}',
                    style: AppTextStyles.subtitle,
                  ),
                  _buildStatusChip(),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Icon(
                    _getProcessTypeIcon(),
                    size: 16,
                    color: AppColors.textSecondary,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    process.getProcessTypeText(),
                    style: AppTextStyles.body,
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  const Icon(
                    Icons.calendar_today,
                    size: 16,
                    color: AppColors.textSecondary,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    process.formattedProcessTime,
                    style: AppTextStyles.body,
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  const Icon(
                    Icons.monetization_on,
                    size: 16,
                    color: AppColors.textSecondary,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '收益: ¥${process.income.toStringAsFixed(2)} | 利润: ¥${process.profit.toStringAsFixed(2)}',
                    style: AppTextStyles.body,
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  if (process.status == 0) // 进行中
                    TextButton(
                      onPressed: () => onStatusChange(1), // 完成
                      child: const Text('完成'),
                    ),
                  if (process.status == 0)
                    TextButton(
                      onPressed: () => onStatusChange(2), // 取消
                      child: const Text('取消'),
                    ),
                  TextButton(
                    onPressed: onDelete,
                    child: Text(
                      '删除',
                      style: TextStyle(color: Colors.red.shade700),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip() {
    Color chipColor;
    String statusText = process.statusText;

    switch (process.status) {
      case 0: // 进行中
        chipColor = Colors.blue;
        break;
      case 1: // 已完成
        chipColor = Colors.green;
        break;
      case 2: // 已取消
        chipColor = Colors.orange;
        break;
      default:
        chipColor = Colors.grey;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        color: chipColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: chipColor.withOpacity(0.5)),
      ),
      child: Text(
        statusText,
        style: AppTextStyles.caption.copyWith(color: chipColor),
      ),
    );
  }

  Color _getBorderColor() {
    switch (process.status) {
      case 0: // 进行中
        return Colors.blue.shade100;
      case 1: // 已完成
        return Colors.green.shade100;
      case 2: // 已取消
        return Colors.orange.shade100;
      default:
        return Colors.grey.shade200;
    }
  }

  IconData _getProcessTypeIcon() {
    switch (process.processType) {
      case RecyclingProcessType.sell:
        return Icons.attach_money;
      case RecyclingProcessType.separate:
        return Icons.content_cut;
      case RecyclingProcessType.repair:
        return Icons.construction;
      case RecyclingProcessType.resell:
        return Icons.shopping_bag;
      default:
        return Icons.help_outline;
    }
  }
} 