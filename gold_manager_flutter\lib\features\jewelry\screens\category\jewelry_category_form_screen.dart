import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';

import '../../controllers/jewelry_category_controller.dart';
import '../../../../widgets/loading_state.dart';

/// 首饰分类表单页面 - 用于添加和编辑首饰分类
class JewelryCategoryFormScreen extends StatefulWidget {
  final int? categoryId;

  const JewelryCategoryFormScreen({
    super.key,
    this.categoryId,
  });

  @override
  State<JewelryCategoryFormScreen> createState() => _JewelryCategoryFormScreenState();
}

class _JewelryCategoryFormScreenState extends State<JewelryCategoryFormScreen> {
  late final JewelryCategoryController controller;
  bool _isInitialized = false;
  
  @override
  void initState() {
    super.initState();
    controller = Get.find<JewelryCategoryController>();
    _initializeData();
  }
  
  Future<void> _initializeData() async {
    if (widget.categoryId != null) {
      await controller.fetchCategoryDetail(widget.categoryId!);
    } else {
      controller.clearSelection();
    }
    setState(() {
      _isInitialized = true;
    });
  }
  
  @override
  Widget build(BuildContext context) {
    final isEditing = widget.categoryId != null;
    
    return Scaffold(
      appBar: AppBar(
        title: Text(isEditing ? '编辑首饰分类' : '添加首饰分类'),
        actions: [
          if (isEditing)
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: () => _confirmDelete(context),
              tooltip: '删除',
            ),
        ],
      ),
      body: !_isInitialized
          ? const LoadingState(text: '正在加载数据...', timeoutSeconds: 30)
          : _buildForm(context),
      bottomNavigationBar: _buildBottomActions(context),
    );
  }
  
  Widget _buildForm(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildBasicInfoSection(),
          const SizedBox(height: 24),
          _buildAdvancedSection(),
          const SizedBox(height: 24),
        ],
      ),
    );
  }
  
  Widget _buildBasicInfoSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '基本信息',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: controller.nameController,
              decoration: const InputDecoration(
                labelText: '分类名称',
                hintText: '输入分类名称',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.category),
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: controller.codeController,
              decoration: const InputDecoration(
                labelText: '分类编码',
                hintText: '输入分类编码',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.code),
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: controller.descriptionController,
              maxLines: 3,
              decoration: const InputDecoration(
                labelText: '分类描述',
                hintText: '输入分类描述（可选）',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.description),
                alignLabelWithHint: true,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildAdvancedSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '高级设置',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Obx(() => SwitchListTile(
              title: const Text('启用状态'),
              subtitle: const Text('禁用后，该分类将不会在选择框中显示'),
              value: controller.isActive.value,
              onChanged: (value) => controller.isActive.value = value,
              activeColor: Get.theme.primaryColor,
            )),
            const Divider(),
            const SizedBox(height: 8),
            Obx(() => ListTile(
              title: const Text('分类颜色'),
              subtitle: const Text('设置分类的显示颜色'),
              trailing: Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: controller.selectedColor.value,
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.grey.shade300),
                ),
              ),
              onTap: () => _showColorPicker(context),
            )),
            const Divider(),
            const SizedBox(height: 8),
            // 父分类选择（未实现）
            // 序号输入（未实现）
          ],
        ),
      ),
    );
  }
  
  Widget _buildBottomActions(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Obx(() => Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: controller.isProcessing.value ? null : () => Get.back(),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: const Text('取消'),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: controller.isProcessing.value
                  ? null
                  : () => _saveCategory(context),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: controller.isProcessing.value
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text(widget.categoryId != null ? '保存' : '添加'),
            ),
          ),
        ],
      )),
    );
  }
  
  void _showColorPicker(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('选择分类颜色'),
        content: SingleChildScrollView(
          child: Obx(() => BlockPicker(
            pickerColor: controller.selectedColor.value,
            onColorChanged: (color) {
              controller.selectedColor.value = color;
            },
          )),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
  
  Future<void> _saveCategory(BuildContext context) async {
    bool success;
    if (widget.categoryId != null) {
      success = await controller.editCategory();
    } else {
      success = await controller.addCategory();
    }
    
    if (success) {
      Get.back();
      Get.snackbar(
        '成功',
        widget.categoryId != null ? '分类更新成功' : '分类添加成功',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    }
  }
  
  Future<void> _confirmDelete(BuildContext context) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除确认'),
        content: const Text('确定要删除此分类吗？此操作不可撤销，如果有商品属于此分类，将无法删除。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('删除'),
          ),
        ],
      ),
    );
    
    if (result == true && widget.categoryId != null) {
      final success = await controller.deleteCategory(widget.categoryId!);
      if (success) {
        Get.back();
        Get.snackbar(
          '成功',
          '分类删除成功',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      }
    }
  }
} 