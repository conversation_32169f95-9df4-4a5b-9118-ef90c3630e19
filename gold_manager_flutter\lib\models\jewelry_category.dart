import 'package:flutter/material.dart';

/// 首饰分类模型
class JewelryCategory {
  final int id;
  final String name;
  final String code;
  final String? description;
  final int? parentId;
  final int sort;
  final bool isActive;
  final int createTime;
  final int updateTime;
  final Color? color;
  
  /// 构造函数
  const JewelryCategory({
    required this.id,
    required this.name,
    required this.code,
    this.description,
    this.parentId,
    required this.sort,
    required this.isActive,
    required this.createTime,
    required this.updateTime,
    this.color,
  });
  
  /// 从JSON构造
  factory JewelryCategory.fromJson(Map<String, dynamic> json) {
    return JewelryCategory(
      id: json['id'],
      name: json['name'],
      code: json['code'],
      description: json['description'],
      parentId: json['parent_id'],
      sort: json['sort'] ?? 0,
      isActive: json['is_active'] == 1,
      createTime: json['createtime'],
      updateTime: json['updatetime'],
      color: json['color'] != null 
          ? Color(int.parse(json['color'].toString().replaceAll('#', '0xFF'))) 
          : null,
    );
  }
  
  /// 转为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'code': code,
      'description': description,
      'parent_id': parentId,
      'sort': sort,
      'is_active': isActive ? 1 : 0,
      'createtime': createTime,
      'updatetime': updateTime,
      'color': color != null 
          ? '#${color!.value.toRadixString(16).substring(2).toUpperCase()}' 
          : null,
    };
  }
  
  /// 复制并修改对象
  JewelryCategory copyWith({
    int? id,
    String? name,
    String? code,
    String? description,
    int? parentId,
    int? sort,
    bool? isActive,
    int? createTime,
    int? updateTime,
    Color? color,
  }) {
    return JewelryCategory(
      id: id ?? this.id,
      name: name ?? this.name,
      code: code ?? this.code,
      description: description ?? this.description,
      parentId: parentId ?? this.parentId,
      sort: sort ?? this.sort,
      isActive: isActive ?? this.isActive,
      createTime: createTime ?? this.createTime,
      updateTime: updateTime ?? this.updateTime,
      color: color ?? this.color,
    );
  }
  
  /// 创建新分类对象
  factory JewelryCategory.create({
    required String name,
    required String code,
    String? description,
    int? parentId,
    int sort = 0,
    bool isActive = true,
    Color? color,
  }) {
    final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    return JewelryCategory(
      id: 0, // 0表示新对象，API会分配真实ID
      name: name,
      code: code,
      description: description,
      parentId: parentId,
      sort: sort,
      isActive: isActive,
      createTime: now,
      updateTime: now,
      color: color,
    );
  }
} 