"""
库存盘点数据验证模型

老板，这个模块定义了库存盘点的数据验证和响应模型：
1. 盘点单创建、更新、响应模型
2. 盘点明细相关模型
3. 统计分析模型
4. 查询参数模型

使用Pydantic v2语法，确保数据验证的准确性。
"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, ConfigDict
from datetime import datetime
from decimal import Decimal


# ==================== 盘点明细模型 ====================

class InventoryCheckItemBase(BaseModel):
    """盘点明细基础模型"""
    jewelry_id: int = Field(..., description="商品ID")
    barcode: str = Field(..., max_length=50, description="商品条码")
    name: str = Field(..., max_length=100, description="商品名称")
    category_id: int = Field(..., description="分类ID")
    ring_size: Optional[str] = Field(None, max_length=20, description="圈口号")
    system_stock: int = Field(1, description="系统库存")
    remark: Optional[str] = Field(None, max_length=255, description="备注")


class InventoryCheckItemCreate(InventoryCheckItemBase):
    """创建盘点明细模型"""
    pass


class InventoryCheckItemUpdate(BaseModel):
    """更新盘点明细模型"""
    actual_stock: Optional[int] = Field(None, description="实际库存")
    remark: Optional[str] = Field(None, max_length=255, description="备注")


class InventoryCheckItemResponse(InventoryCheckItemBase):
    """盘点明细响应模型"""
    model_config = ConfigDict(from_attributes=True)
    
    id: int = Field(..., description="明细ID")
    check_id: int = Field(..., description="盘点单ID")
    status: int = Field(..., description="状态:0=未盘点,1=已盘点")
    actual_stock: Optional[int] = Field(None, description="实际库存")
    difference: Optional[int] = Field(None, description="差异")
    check_time: Optional[int] = Field(None, description="盘点时间")
    check_user_id: Optional[int] = Field(None, description="盘点人员ID")
    createtime: Optional[int] = Field(None, description="创建时间")
    
    # 关联信息
    category_name: Optional[str] = Field(None, description="分类名称")
    check_user_name: Optional[str] = Field(None, description="盘点人员姓名")


# ==================== 盘点单模型 ====================

class InventoryCheckBase(BaseModel):
    """盘点单基础模型"""
    store_id: int = Field(..., description="盘点门店ID")
    remark: Optional[str] = Field(None, description="备注")


class InventoryCheckCreate(InventoryCheckBase):
    """创建盘点单模型"""
    items: List[InventoryCheckItemCreate] = Field(..., min_length=1, description="盘点商品明细")


class InventoryCheckUpdate(BaseModel):
    """更新盘点单模型"""
    remark: Optional[str] = Field(None, description="备注")


class InventoryCheckStatusUpdate(BaseModel):
    """盘点单状态更新模型"""
    status: int = Field(..., ge=0, le=2, description="状态:0=进行中,1=已完成,2=已取消")
    remark: Optional[str] = Field(None, description="操作备注")


class InventoryCheckResponse(InventoryCheckBase):
    """盘点单响应模型"""
    model_config = ConfigDict(from_attributes=True)
    
    id: int = Field(..., description="盘点单ID")
    check_no: str = Field(..., description="盘点单号")
    status: int = Field(..., description="状态:0=进行中,1=已完成,2=已取消")
    start_time: Optional[int] = Field(None, description="开始时间")
    end_time: Optional[int] = Field(None, description="结束时间")
    operator_id: int = Field(..., description="操作员ID")
    total_count: int = Field(..., description="应盘总数")
    checked_count: int = Field(..., description="已盘数量")
    difference_count: int = Field(..., description="差异数量")
    createtime: Optional[int] = Field(None, description="创建时间")
    updatetime: Optional[int] = Field(None, description="更新时间")
    
    # 关联信息
    store_name: Optional[str] = Field(None, description="门店名称")
    operator_name: Optional[str] = Field(None, description="操作员姓名")
    
    # 状态描述
    status_text: Optional[str] = Field(None, description="状态描述")
    progress_percent: Optional[float] = Field(None, description="盘点进度百分比")


class InventoryCheckDetailResponse(InventoryCheckResponse):
    """盘点单详情响应模型"""
    items: List[InventoryCheckItemResponse] = Field(default_factory=list, description="盘点明细列表")


# ==================== 查询参数模型 ====================

class InventoryCheckQueryParams(BaseModel):
    """盘点单查询参数模型"""
    keyword: Optional[str] = Field(None, description="关键词搜索(单号、备注)")
    store_id: Optional[int] = Field(None, description="门店ID筛选")
    status: Optional[int] = Field(None, ge=0, le=2, description="状态筛选")
    operator_id: Optional[int] = Field(None, description="操作员ID筛选")
    start_date: Optional[str] = Field(None, pattern=r'^\d{4}-\d{2}-\d{2}$', description="开始日期")
    end_date: Optional[str] = Field(None, pattern=r'^\d{4}-\d{2}-\d{2}$', description="结束日期")


# ==================== 统计模型 ====================

class InventoryCheckStatistics(BaseModel):
    """盘点统计模型"""
    total_checks: int = Field(..., description="总盘点单数")
    in_progress_count: int = Field(..., description="进行中数量")
    completed_count: int = Field(..., description="已完成数量")
    cancelled_count: int = Field(..., description="已取消数量")
    
    total_items: int = Field(..., description="总盘点商品数")
    checked_items: int = Field(..., description="已盘点商品数")
    difference_items: int = Field(..., description="有差异商品数")
    
    # 状态分布
    status_distribution: Dict[str, int] = Field(default_factory=dict, description="状态分布")
    
    # 门店分布
    store_distribution: List[Dict[str, Any]] = Field(default_factory=list, description="门店分布")
    
    # 进度统计
    average_progress: float = Field(0.0, description="平均盘点进度")
    completion_rate: float = Field(0.0, description="完成率")


# ==================== 盘点操作模型 ====================

class InventoryCheckItemCheck(BaseModel):
    """商品盘点操作模型"""
    actual_stock: int = Field(..., ge=0, description="实际库存")
    remark: Optional[str] = Field(None, max_length=255, description="盘点备注")


class InventoryCheckBatchCheck(BaseModel):
    """批量盘点操作模型"""
    items: List[Dict[str, Any]] = Field(..., min_length=1, description="批量盘点数据")
    # items格式: [{"item_id": 1, "actual_stock": 1, "remark": "正常"}]
