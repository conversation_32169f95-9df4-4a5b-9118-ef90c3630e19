# Flutter Debug Script
$projectPath = "C:\WorkSpace\GoldManager\gold_manager_flutter"
Set-Location $projectPath

Write-Host "当前目录: $(Get-Location)"
Write-Host "开始运行Flutter应用..."

# 尝试不同的运行方式
try {
    # 方式1: 标准调试模式
    Write-Host "尝试标准调试模式..."
    flutter run -d windows --verbose
}
catch {
    Write-Host "标准模式失败，尝试其他方式..."
    
    # 方式2: 指定端口
    try {
        Write-Host "尝试指定调试端口..."
        flutter run -d windows --observatory-port=9999
    }
    catch {
        # 方式3: 先编译再运行
        Write-Host "尝试先编译再运行..."
        flutter build windows --debug
        
        $exePath = "$projectPath\build\windows\x64\runner\Debug\gold_manager_flutter.exe"
        if (Test-Path $exePath) {
            Write-Host "启动编译好的应用: $exePath"
            Start-Process -FilePath $exePath
        }
    }
}

Write-Host "按任意键继续..."
Read-Host