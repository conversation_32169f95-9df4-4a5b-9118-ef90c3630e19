/// 文档状态枚举
enum DocumentStatus {
  /// 草稿
  draft(0, '草稿'),
  
  /// 待审核
  pending(1, '待审核'),
  
  /// 已审核/已通过
  approved(2, '已审核'),
  
  /// 已拒绝
  rejected(3, '已拒绝'),
  
  /// 已取消
  cancelled(4, '已取消');

  const DocumentStatus(this.value, this.label);

  /// 状态值
  final int value;
  
  /// 状态标签
  final String label;

  /// 从整数值创建状态
  static DocumentStatus fromValue(int value) {
    switch (value) {
      case 0:
        return DocumentStatus.draft;
      case 1:
        return DocumentStatus.pending;
      case 2:
        return DocumentStatus.approved;
      case 3:
        return DocumentStatus.rejected;
      case 4:
        return DocumentStatus.cancelled;
      default:
        return DocumentStatus.draft;
    }
  }

  /// 获取状态显示文本
  String get displayText => label;

  /// 是否可以编辑
  bool get canEdit => this == DocumentStatus.draft || this == DocumentStatus.pending;

  /// 是否可以删除
  bool get canDelete => this == DocumentStatus.draft || this == DocumentStatus.pending;

  /// 是否可以审核
  bool get canApprove => this == DocumentStatus.pending;

  /// 是否已完成
  bool get isCompleted => this == DocumentStatus.approved;

  /// 是否被拒绝
  bool get isRejected => this == DocumentStatus.rejected;

  /// 是否被取消
  bool get isCancelled => this == DocumentStatus.cancelled;
}
