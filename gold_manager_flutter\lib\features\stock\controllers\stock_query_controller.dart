import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/utils/logger.dart';
import '../../../models/jewelry/jewelry.dart';
import '../../../models/jewelry/jewelry_category.dart';
import '../../../models/common/enums.dart';
import '../../../models/store/store.dart';
import '../../../services/jewelry_service.dart';
import '../../../services/store_service.dart';
import '../../../services/auth_service.dart';


/// 库存查询控制器
class StockQueryController extends GetxController {
  // 服务
  final JewelryService _jewelryService = Get.find<JewelryService>();
  final StoreService _storeService = Get.find<StoreService>();
  final AuthService _authService = Get.find<AuthService>();

  // 状态变量
  final RxBool isLoading = false.obs;
  final RxList<Jewelry> jewelryList = <Jewelry>[].obs;
  final RxList<Store> storeList = <Store>[].obs;
  final RxList<JewelryCategory> categoryList = <JewelryCategory>[].obs;

  // 筛选条件
  final TextEditingController searchController = TextEditingController();
  final RxInt selectedStoreId = 0.obs;
  final Rx<JewelryStatus?> selectedStatus = Rx<JewelryStatus?>(JewelryStatus.onShelf);
  final RxInt selectedCategoryId = 0.obs;

  // 分页
  final RxInt currentPage = 1.obs;
  final RxInt totalPages = 1.obs;
  final int pageSize = 20;

  // 统计信息
  final RxInt totalCount = 0.obs;
  final RxDouble totalGoldWeight = 0.0.obs;
  final RxDouble totalSilverWeight = 0.0.obs;
  final RxDouble totalWeight = 0.0.obs;

  @override
  void onInit() {
    super.onInit();
    LoggerService.d('StockQueryController 初始化');

    // 🔧 修复：每次初始化时重置为初始状态
    _resetToInitialStateSync();

    // 延迟初始化，确保权限已加载
    Future.delayed(const Duration(milliseconds: 100), () {
      if (_authService.hasPermission('store.view')) {
        fetchStores();
      } else {
        LoggerService.w('用户没有store.view权限，跳过获取门店列表');
      }

      if (_authService.hasPermission('jewelry.view')) {
        fetchCategories();
        fetchJewelryList();
      } else {
        LoggerService.w('用户没有jewelry.view权限，跳过获取首饰列表');
      }
    });
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }

  /// 获取门店列表
  Future<void> fetchStores() async {
    try {
      isLoading.value = true;
      // 使用getAllStores确保获取所有门店，不受分页限制
      final stores = await _storeService.getAllStores();
      storeList.value = stores;

      // 如果用户不是管理员，自动选择用户所属门店
      if (_authService.userRole.value != 'admin') {
        selectedStoreId.value = _authService.storeId.value;
      }
    } catch (e) {
      LoggerService.e('获取门店列表失败', e);
      Get.snackbar(
        '错误',
        '获取门店列表失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// 获取分类列表
  Future<void> fetchCategories() async {
    try {
      LoggerService.d('🔄 开始获取分类列表');
      final categories = await _jewelryService.getCategories();
      categoryList.value = categories;
      LoggerService.d('✅ 分类列表获取成功，共${categories.length}个分类');
    } catch (e) {
      LoggerService.e('❌ 获取分类列表失败', e);
      // 分类获取失败不影响主要功能，只记录日志
    }
  }

  /// 获取库存列表 - 完整的库存查询功能
  Future<void> fetchJewelryList() async {
    try {
      isLoading.value = true;
      LoggerService.d('🔄 开始获取库存列表');

      final params = <String, dynamic>{
        'page': currentPage.value,
        'page_size': pageSize,
      };

      // 构建查询参数
      if (searchController.text.isNotEmpty) {
        params['keyword'] = searchController.text.trim();
        LoggerService.d('🔍 搜索关键词: ${searchController.text.trim()}');
      }

      if (selectedStoreId.value > 0) {
        params['store_id'] = selectedStoreId.value;
        LoggerService.d('🏪 门店筛选: ${selectedStoreId.value}');
      }

      if (selectedStatus.value != null) {
        params['status'] = selectedStatus.value!.value.toString();
        LoggerService.d('📊 状态筛选: ${selectedStatus.value!.label}');
      }

      if (selectedCategoryId.value > 0) {
        params['category_id'] = selectedCategoryId.value;
        LoggerService.d('📂 分类筛选: ${selectedCategoryId.value}');
      }

      LoggerService.d('📋 查询参数: $params');

      // 设置超时保护
      final result = await _jewelryService.getJewelryList(params).timeout(
        const Duration(seconds: 30),
        onTimeout: () {
          LoggerService.e('❌ 获取库存列表超时');
          throw Exception('请求超时，请检查网络连接');
        },
      );

      // 解析API响应
      jewelryList.value = result.data;

      // 计算总页数
      totalPages.value = result.lastPage;

      // 计算统计信息
      _calculateStatistics();

      LoggerService.d('✅ 库存列表获取成功，共${result.data.length}条数据，总页数: ${result.lastPage}');

      // 记录权限信息
      if (_authService.userRole.value != 'admin') {
        LoggerService.d('👤 当前用户门店权限: ${_authService.storeName.value}');
      }

    } catch (e) {
      LoggerService.e('❌ 获取库存列表失败', e);

      // 清空列表，避免显示过期数据
      jewelryList.clear();
      totalPages.value = 1;

      // 根据错误类型显示不同的提示信息
      String errorMessage = '获取库存列表失败';
      if (e.toString().contains('403')) {
        errorMessage = '权限不足，无法访问该门店的库存';
      } else if (e.toString().contains('timeout')) {
        errorMessage = '网络请求超时，请检查网络连接';
      } else if (e.toString().contains('network')) {
        errorMessage = '网络连接失败，请检查网络设置';
      } else {
        errorMessage = '获取库存列表失败: ${e.toString()}';
      }

      Get.snackbar(
        '错误',
        errorMessage,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// 搜索
  void search() {
    currentPage.value = 1;
    fetchJewelryList();
  }

  /// 重置筛选条件
  void resetFilters() {
    LoggerService.d('🔄 重置筛选条件');

    searchController.clear();
    selectedStatus.value = JewelryStatus.onShelf;
    selectedCategoryId.value = 0;
    currentPage.value = 1;

    // 🔧 修复：根据用户角色正确重置门店选择
    final isAdmin = _authService.userRole.value == 'admin' || _authService.hasPermission('super.admin');
    if (isAdmin) {
      // 管理员重置为0（显示"全部门店"）
      selectedStoreId.value = 0;
      LoggerService.d('   管理员用户，重置为全部门店');
    } else {
      // 普通用户重置为其所属门店ID
      selectedStoreId.value = _authService.storeId.value;
      LoggerService.d('   普通用户，重置为所属门店: ${_authService.storeName.value} (ID: ${_authService.storeId.value})');
    }

    fetchJewelryList();
  }

  /// 同步重置页面状态到初始状态（用于onInit）
  void _resetToInitialStateSync() {
    LoggerService.d('🔄 同步重置库存查询页面到初始状态');

    // 重置筛选条件
    searchController.clear();
    selectedStatus.value = JewelryStatus.onShelf;
    selectedCategoryId.value = 0;

    // 🔧 修复：根据用户角色正确重置门店选择
    final isAdmin = _authService.userRole.value == 'admin' || _authService.hasPermission('super.admin');
    if (isAdmin) {
      // 管理员重置为0（显示"全部门店"）
      selectedStoreId.value = 0;
    } else {
      // 普通用户重置为其所属门店ID
      selectedStoreId.value = _authService.storeId.value;
    }

    // 重置分页
    currentPage.value = 1;
    totalPages.value = 1;

    // 清空数据
    jewelryList.clear();

    // 重置统计信息
    totalCount.value = 0;
    totalGoldWeight.value = 0.0;
    totalSilverWeight.value = 0.0;
    totalWeight.value = 0.0;

    // 重置加载状态
    isLoading.value = false;
  }

  /// 重置页面状态到初始状态
  void resetToInitialState() {
    LoggerService.d('🔄 重置库存查询页面到初始状态');

    // 调用同步重置
    _resetToInitialStateSync();

    // 重新初始化数据
    Future.delayed(const Duration(milliseconds: 100), () {
      if (_authService.hasPermission('store.view')) {
        fetchStores();
      }
      if (_authService.hasPermission('jewelry.view')) {
        fetchCategories();
        fetchJewelryList();
      }
    });
  }

  /// 分页
  void goToPage(int page) {
    if (page < 1 || page > totalPages.value) return;
    currentPage.value = page;
    fetchJewelryList();
  }



  /// 删除商品
  Future<void> deleteJewelry(int id) async {
    try {
      LoggerService.d('🗑️ 开始删除商品，ID: $id');

      // 调用删除API
      await _jewelryService.deleteJewelry(id);

      LoggerService.d('✅ 商品删除成功');
    } catch (e) {
      LoggerService.e('❌ 删除商品失败', e);
      throw Exception('删除失败：${e.toString()}');
    }
  }

  /// 获取状态显示颜色
  Color getStatusColor(JewelryStatus status) {
    switch (status) {
      case JewelryStatus.offShelf:
        return Colors.grey;
      case JewelryStatus.onShelf:
        return Colors.green;
      case JewelryStatus.pendingOut:
        return Colors.orange;
    }
  }

  /// 计算统计信息
  void _calculateStatistics() {
    if (jewelryList.isEmpty) {
      totalCount.value = 0;
      totalGoldWeight.value = 0.0;
      totalSilverWeight.value = 0.0;
      totalWeight.value = 0.0;
      return;
    }

    totalCount.value = jewelryList.length;

    double goldWeight = 0.0;
    double silverWeight = 0.0;
    double weight = 0.0;

    for (final jewelry in jewelryList) {
      goldWeight += jewelry.goldWeight;
      silverWeight += jewelry.silverWeight;
      weight += jewelry.totalWeight;
    }

    totalGoldWeight.value = goldWeight;
    totalSilverWeight.value = silverWeight;
    totalWeight.value = weight;

    LoggerService.d('📊 统计信息更新: 总件数=${totalCount.value}, 总金重=${totalGoldWeight.value}g, 总银重=${totalSilverWeight.value}g, 总重=${totalWeight.value}g');
  }
}