import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/controllers/paginated_controller.dart';
import '../../../core/models/paginated_response.dart';
import '../../../models/recycling/recycling_model.dart';
import '../../../models/store/store.dart';
import '../services/recycling_service.dart';
import '../../../services/store_service.dart';
import '../../dashboard/controllers/dashboard_controller.dart';
import '../../../core/utils/logger_service.dart';

/// 优化后的回收管理控制器
/// 使用了最新的GetX最佳实践和基础控制器
class OptimizedRecyclingController extends PaginatedController<RecyclingOrder> 
    with StateMixin<List<RecyclingOrder>> {
  
  late final RecyclingService _recyclingService;
  late final StoreService _storeService;
  
  // 筛选相关状态
  final RxInt statusFilter = (-1).obs; // -1 表示全部
  final RxInt selectedStoreId = 0.obs; // 0 表示全部门店
  final RxList<Store> storeList = <Store>[].obs;
  
  // 详情页状态
  final Rx<RecyclingOrder?> currentOrder = Rx<RecyclingOrder?>(null);
  final RxBool isDetailLoading = false.obs;
  
  // Worker管理
  late Worker _statusFilterWorker;
  late Worker _storeFilterWorker;

  @override
  void onInit() {
    super.onInit();
    
    // 初始化服务
    _recyclingService = RecyclingService();
    _storeService = Get.find<StoreService>();
    
    // 设置Worker并保存引用以便释放
    _setupWorkers();
    
    // 初始化数据
    _updateDashboardNavigation();
    _initializeData();
  }

  /// 设置Workers
  void _setupWorkers() {
    // 监听状态筛选变化
    _statusFilterWorker = ever(statusFilter, (_) {
      refresh();
    });
    
    // 监听门店筛选变化
    _storeFilterWorker = ever(selectedStoreId, (_) {
      refresh();
    });
  }

  /// 初始化数据
  Future<void> _initializeData() async {
    await executeSafely(() async {
      await fetchStores();
      await loadFirstPage();
    }, errorContext: 'Initialize Data');
  }

  /// 更新仪表盘导航状态
  void _updateDashboardNavigation() {
    try {
      final dashboardController = Get.find<DashboardController>();
      dashboardController.updateSelectedIndex('/recycling');
    } catch (e) {
      LoggerService.w('无法找到DashboardController: $e');
    }
  }

  /// 获取门店列表
  Future<void> fetchStores() async {
    await executeSafely(() async {
      final stores = await _storeService.getAllStores();
      storeList.value = stores;
    }, errorContext: 'Fetch Stores');
  }

  @override
  Map<String, dynamic> get filters {
    final Map<String, dynamic> params = super.filters;
    
    // 添加状态筛选
    if (statusFilter.value != -1) {
      params['status'] = statusFilter.value;
    }
    
    // 添加门店筛选
    if (selectedStoreId.value > 0) {
      params['store_id'] = selectedStoreId.value;
    }
    
    return params;
  }

  @override
  Future<PaginatedResponse<RecyclingOrder>> fetchItems(
    int page, 
    int pageSize, 
    Map<String, dynamic> filters
  ) async {
    try {
      // 使用StateMixin更新状态
      if (page == 1) {
        change(null, status: RxStatus.loading());
      }
      
      final response = await _recyclingService.getRecyclingOrders(
        page: page,
        pageSize: pageSize,
        filters: filters,
      );
      
      // 更新StateMixin状态
      if (page == 1) {
        if (response.items.isEmpty) {
          change([], status: RxStatus.empty());
        } else {
          change(response.items, status: RxStatus.success());
        }
      }
      
      return response;
    } catch (e) {
      if (page == 1) {
        change(null, status: RxStatus.error('加载失败: $e'));
      }
      rethrow;
    }
  }

  @override
  Widget buildItemWidget(BuildContext context, RecyclingOrder item, int index) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        title: Text('回收单号: ${item.orderNo}'),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('门店: ${item.storeName}'),
            Text('状态: ${_getStatusText(item.status)}'),
            Text('创建时间: ${_formatDateTime(item.createTime)}'),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              '￥${item.totalAmount.toStringAsFixed(2)}',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            const Icon(Icons.chevron_right),
          ],
        ),
        onTap: () => viewOrderDetail(item),
      ),
    );
  }

  /// 查看订单详情
  Future<void> viewOrderDetail(RecyclingOrder order) async {
    currentOrder.value = order;
    
    await executeWithLoading(() async {
      // 这里可以加载更详细的订单信息
      final detailOrder = await _recyclingService.getOrderDetail(order.id);
      currentOrder.value = detailOrder;
    });
    
    // 导航到详情页
    Get.toNamed('/recycling/detail', arguments: currentOrder.value);
  }

  /// 创建新的回收单
  Future<void> createNewOrder() async {
    final result = await Get.toNamed('/recycling/create');
    
    if (result == true) {
      // 刷新列表
      refresh();
      showSuccess('回收单创建成功');
    }
  }

  /// 更新订单状态
  Future<void> updateOrderStatus(RecyclingOrder order, int newStatus) async {
    final confirmed = await showConfirmDialog(
      title: '确认操作',
      content: '确定要${_getStatusText(newStatus)}吗？',
    );
    
    if (!confirmed) return;
    
    await executeWithLoading(() async {
      await _recyclingService.updateOrderStatus(order.id, newStatus);
      
      // 更新本地数据
      final index = items.indexWhere((item) => item.id == order.id);
      if (index != -1) {
        final updatedOrder = order.copyWith(status: newStatus);
        items[index] = updatedOrder;
      }
    }, 
    successMessage: '状态更新成功',
    showSuccessMessage: true);
  }

  /// 删除订单
  Future<void> deleteOrder(RecyclingOrder order) async {
    final confirmed = await showConfirmDialog(
      title: '确认删除',
      content: '确定要删除这个回收单吗？此操作不可恢复。',
      confirmText: '删除',
      confirmColor: Colors.red,
    );
    
    if (!confirmed) return;
    
    await executeWithLoading(() async {
      await _recyclingService.deleteOrder(order.id);
      removeItem(order);
    },
    successMessage: '删除成功',
    showSuccessMessage: true);
  }

  /// 导出数据
  Future<void> exportData() async {
    await executeWithLoading(() async {
      final data = await _recyclingService.exportOrders(filters);
      // 处理导出逻辑
      showSuccess('数据导出成功');
    });
  }

  /// 批量操作
  Future<void> batchOperation(List<RecyclingOrder> selectedOrders, String operation) async {
    if (selectedOrders.isEmpty) {
      showWarning('请先选择要操作的订单');
      return;
    }
    
    final confirmed = await showConfirmDialog(
      title: '批量操作',
      content: '确定要对${selectedOrders.length}个订单执行$operation操作吗？',
    );
    
    if (!confirmed) return;
    
    await executeWithLoading(() async {
      final orderIds = selectedOrders.map((order) => order.id).toList();
      await _recyclingService.batchOperation(orderIds, operation);
      
      // 刷新列表
      refresh();
    },
    successMessage: '批量操作完成',
    showSuccessMessage: true);
  }

  /// 获取状态文本
  String _getStatusText(int status) {
    switch (status) {
      case 0: return '待处理';
      case 1: return '处理中';
      case 2: return '已完成';
      case 3: return '已取消';
      default: return '未知';
    }
  }

  /// 格式化日期时间
  String _formatDateTime(DateTime? dateTime) {
    if (dateTime == null) return '';
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  @override
  String getEmptyMessage() {
    if (statusFilter.value != -1 || selectedStoreId.value > 0 || searchQuery.value.isNotEmpty) {
      return '没有符合条件的回收记录';
    }
    return '暂无回收记录\n点击右下角按钮创建新的回收单';
  }

  /// 重置筛选条件
  void resetFilters() {
    statusFilter.value = -1;
    selectedStoreId.value = 0;
    clearSearch();
  }

  /// 获取筛选条件摘要
  String get filterSummary {
    List<String> conditions = [];
    
    if (statusFilter.value != -1) {
      conditions.add('状态: ${_getStatusText(statusFilter.value)}');
    }
    
    if (selectedStoreId.value > 0) {
      final store = storeList.firstWhereOrNull((s) => s.id == selectedStoreId.value);
      if (store != null) {
        conditions.add('门店: ${store.name}');
      }
    }
    
    if (searchQuery.value.isNotEmpty) {
      conditions.add('搜索: ${searchQuery.value}');
    }
    
    return conditions.isEmpty ? '全部' : conditions.join(', ');
  }

  @override
  void onClose() {
    // 释放Workers
    _statusFilterWorker.dispose();
    _storeFilterWorker.dispose();
    
    super.onClose();
  }
}