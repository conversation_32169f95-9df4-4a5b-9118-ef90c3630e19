"""
出库管理业务服务类
处理出库单相关的所有业务逻辑，包括审核、收款等流程
"""

import time
from typing import List, Optional, Dict, Any, Tuple
from decimal import Decimal
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import func, desc, asc, and_, or_
from datetime import datetime

from ..models.stock_out import StockOut, StockOutItem
from ..models.store import Store
from ..models.admin import Admin
from ..models.jewelry import Jewelry
from ..schemas.stock_out import (
    StockOutCreate, StockOutUpdate, StockOutAuditUpdate, StockOutPaymentUpdate,
    StockOutQueryParams, StockOutResponse, StockOutStatistics, PaymentInfo
)


class StockOutService:
    """出库管理服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def generate_order_no(self) -> str:
        """生成出库单号"""
        now = datetime.now()
        prefix = f"OUT{now.year}{now.month:02d}{now.day:02d}"
        
        # 查询今天已有的出库单数量
        today_start = int(datetime(now.year, now.month, now.day).timestamp())
        today_end = today_start + 86400  # 24小时后
        
        count = self.db.query(StockOut).filter(
            and_(
                StockOut.createtime >= today_start,
                StockOut.createtime < today_end
            )
        ).count()
        
        return f"{prefix}{count + 1:04d}"
    
    def create_stock_out(self, stock_out_data: StockOutCreate, operator_id: int) -> StockOut:
        """创建出库单"""
        try:
            # 验证门店是否存在
            store = self.db.query(Store).filter(Store.id == stock_out_data.store_id).first()
            if not store:
                raise ValueError(f"门店ID {stock_out_data.store_id} 不存在")
            
            # 验证操作员是否存在
            operator = self.db.query(Admin).filter(Admin.id == operator_id).first()
            if not operator:
                raise ValueError(f"操作员ID {operator_id} 不存在")
            
            # 验证所有商品是否存在并计算总金额和总重量
            total_amount = Decimal('0.00')
            total_weight = Decimal('0.00')
            for item_data in stock_out_data.items:
                jewelry = self.db.query(Jewelry).filter(Jewelry.id == item_data.jewelry_id).first()
                if not jewelry:
                    raise ValueError(f"首饰ID {item_data.jewelry_id} 不存在")
                total_amount += item_data.total_amount
                total_weight += item_data.total_weight
            
            current_time = int(time.time())
            
            # 创建出库单主表
            stock_out = StockOut(
                order_no=self.generate_order_no(),
                store_id=stock_out_data.store_id,
                customer=stock_out_data.customer,
                recycling_id=stock_out_data.recycling_id,
                total_weight=total_weight,
                sale_type=stock_out_data.sale_type,
                total_amount=total_amount,
                operator_id=operator_id,
                remark=stock_out_data.remark,
                status=1,  # 待审核状态
                createtime=current_time,
                updatetime=current_time
            )
            
            self.db.add(stock_out)
            self.db.flush()  # 获取出库单ID
            
            # 创建出库单明细
            for item_data in stock_out_data.items:
                stock_out_item = StockOutItem(
                    stock_out_id=stock_out.id,
                    jewelry_id=item_data.jewelry_id,
                    barcode=item_data.barcode,
                    name=item_data.name,
                    category_id=item_data.category_id,
                    ring_size=item_data.ring_size,
                    gold_weight=item_data.gold_weight,
                    gold_price=item_data.gold_price,
                    gold_cost=item_data.gold_cost,
                    silver_weight=item_data.silver_weight,
                    total_weight=item_data.total_weight,
                    silver_price=item_data.silver_price,
                    silver_cost=item_data.silver_cost,
                    silver_work_type=item_data.silver_work_type,
                    silver_work_price=item_data.silver_work_price,
                    plating_cost=item_data.plating_cost,
                    piece_work_price=item_data.piece_work_price,
                    work_price=item_data.work_price,
                    total_cost=item_data.total_cost,
                    total_amount=item_data.total_amount,
                    sale_type=item_data.sale_type,
                    createtime=current_time
                )
                self.db.add(stock_out_item)
            
            self.db.commit()
            self.db.refresh(stock_out)

            # 重新查询以获取完整的关联信息（包括门店信息）
            stock_out_with_relations = self.db.query(StockOut).options(
                joinedload(StockOut.store),
                joinedload(StockOut.operator),
                joinedload(StockOut.items).joinedload(StockOutItem.jewelry).joinedload(Jewelry.category)
            ).filter(StockOut.id == stock_out.id).first()

            return stock_out_with_relations
            
        except Exception as e:
            self.db.rollback()
            raise e
    
    def get_stock_out_list(self, params: StockOutQueryParams) -> Tuple[List[StockOut], int]:
        """获取出库单列表"""
        query = self.db.query(StockOut)
        
        # 关联查询
        query = query.options(
            joinedload(StockOut.store),
            joinedload(StockOut.operator),
            joinedload(StockOut.auditor),
            joinedload(StockOut.items).joinedload(StockOutItem.jewelry)
        )
        
        # 关键词搜索
        if params.keyword:
            keyword = f"%{params.keyword}%"
            query = query.filter(
                or_(
                    StockOut.order_no.like(keyword),
                    StockOut.customer.like(keyword),
                    StockOut.remark.like(keyword)
                )
            )
        
        # 门店筛选
        if params.store_id:
            query = query.filter(StockOut.store_id == params.store_id)
        
        # 状态筛选
        if params.status is not None:
            query = query.filter(StockOut.status == params.status)
        
        # 销售类型筛选
        if params.sale_type:
            query = query.filter(StockOut.sale_type == params.sale_type)
        
        # 收款状态筛选
        if params.payment_status is not None:
            query = query.filter(StockOut.payment_status == params.payment_status)
        
        # 操作员筛选
        if params.operator_id:
            query = query.filter(StockOut.operator_id == params.operator_id)
        
        # 客户筛选
        if params.customer:
            customer_keyword = f"%{params.customer}%"
            query = query.filter(StockOut.customer.like(customer_keyword))
        
        # 日期范围筛选
        if params.start_date:
            try:
                start_timestamp = int(datetime.strptime(params.start_date, "%Y-%m-%d").timestamp())
                query = query.filter(StockOut.createtime >= start_timestamp)
            except ValueError:
                pass
        
        if params.end_date:
            try:
                end_timestamp = int(datetime.strptime(params.end_date, "%Y-%m-%d").timestamp()) + 86400
                query = query.filter(StockOut.createtime < end_timestamp)
            except ValueError:
                pass
        
        # 获取总数
        total = query.count()
        
        # 分页和排序
        query = query.order_by(desc(StockOut.createtime))
        offset = (params.page - 1) * params.page_size
        stock_outs = query.offset(offset).limit(params.page_size).all()
        
        return stock_outs, total
    
    def get_stock_out_by_id(self, stock_out_id: int) -> Optional[StockOut]:
        """根据ID获取出库单详情"""
        print(f"🔍 [DEBUG] 查询出库单 ID={stock_out_id}")

        # 🔧 修复关联查询：确保正确加载items数据
        stock_out = self.db.query(StockOut).options(
            joinedload(StockOut.store),
            joinedload(StockOut.operator),
            joinedload(StockOut.auditor),
            joinedload(StockOut.items).joinedload(StockOutItem.jewelry).joinedload(Jewelry.category)
        ).filter(StockOut.id == stock_out_id).first()

        # 🔧 强制加载items关联数据（如果lazy loading有问题）
        if stock_out and hasattr(stock_out, 'items'):
            # 触发items的加载
            _ = len(stock_out.items)
            print(f"🔧 [DEBUG] 强制加载后items数量: {len(stock_out.items)}")

            # 触发每个item的jewelry加载
            for item in stock_out.items:
                if hasattr(item, 'jewelry') and item.jewelry:
                    _ = item.jewelry.name
                    print(f"   🔧 [DEBUG] 强制加载jewelry: {item.jewelry.name}")

        if stock_out:
            print(f"✅ [DEBUG] 找到出库单: ID={stock_out.id}, 单号={stock_out.order_no}")
            print(f"📦 [DEBUG] 出库单items属性: {stock_out.items}")
            print(f"📊 [DEBUG] items数量: {len(stock_out.items) if stock_out.items else 0}")

            if stock_out.items:
                for i, item in enumerate(stock_out.items):
                    print(f"   📋 [DEBUG] Item {i+1}: ID={item.id}, 条码={item.barcode}, 名称={item.name}")
                    print(f"       💎 [DEBUG] 关联首饰: {item.jewelry.name if item.jewelry else 'None'}")
            else:
                print("⚠️ [DEBUG] 出库单没有关联的items")
        else:
            print(f"❌ [DEBUG] 未找到出库单 ID={stock_out_id}")

        return stock_out
    
    def get_stock_out_by_no(self, order_no: str) -> Optional[StockOut]:
        """根据单号获取出库单详情"""
        return self.db.query(StockOut).options(
            joinedload(StockOut.store),
            joinedload(StockOut.operator),
            joinedload(StockOut.auditor),
            joinedload(StockOut.items).joinedload(StockOutItem.jewelry)
        ).filter(StockOut.order_no == order_no).first()
    
    def update_stock_out(self, stock_out_id: int, stock_out_data: StockOutUpdate) -> Optional[StockOut]:
        """更新出库单"""
        try:
            stock_out = self.db.query(StockOut).filter(StockOut.id == stock_out_id).first()
            if not stock_out:
                return None
            
            # 只有待审核状态才允许修改
            if stock_out.status != 1:
                raise ValueError("只有待审核状态的出库单才能修改")
            
            current_time = int(time.time())
            
            # 更新基本信息
            if stock_out_data.store_id is not None:
                # 验证门店是否存在
                store = self.db.query(Store).filter(Store.id == stock_out_data.store_id).first()
                if not store:
                    raise ValueError(f"门店ID {stock_out_data.store_id} 不存在")
                stock_out.store_id = stock_out_data.store_id
            
            if stock_out_data.customer is not None:
                stock_out.customer = stock_out_data.customer
            
            if stock_out_data.recycling_id is not None:
                stock_out.recycling_id = stock_out_data.recycling_id
            
            if stock_out_data.sale_type is not None:
                stock_out.sale_type = stock_out_data.sale_type
            
            if stock_out_data.remark is not None:
                stock_out.remark = stock_out_data.remark
            
            # 更新明细
            if stock_out_data.items is not None:
                # 删除原有明细
                self.db.query(StockOutItem).filter(
                    StockOutItem.stock_out_id == stock_out_id
                ).delete()
                
                # 验证所有商品是否存在并计算总金额和总重量
                total_amount = Decimal('0.00')
                total_weight = Decimal('0.00')
                for item_data in stock_out_data.items:
                    jewelry = self.db.query(Jewelry).filter(Jewelry.id == item_data.jewelry_id).first()
                    if not jewelry:
                        raise ValueError(f"首饰ID {item_data.jewelry_id} 不存在")
                    total_amount += item_data.total_amount
                    total_weight += item_data.total_weight
                
                # 创建新明细
                for item_data in stock_out_data.items:
                    stock_out_item = StockOutItem(
                        stock_out_id=stock_out_id,
                        jewelry_id=item_data.jewelry_id,
                        barcode=item_data.barcode,
                        name=item_data.name,
                        category_id=item_data.category_id,
                        ring_size=item_data.ring_size,
                        gold_weight=item_data.gold_weight,
                        gold_price=item_data.gold_price,
                        gold_cost=item_data.gold_cost,
                        silver_weight=item_data.silver_weight,
                        total_weight=item_data.total_weight,
                        silver_price=item_data.silver_price,
                        silver_cost=item_data.silver_cost,
                        silver_work_type=item_data.silver_work_type,
                        silver_work_price=item_data.silver_work_price,
                        plating_cost=item_data.plating_cost,
                        piece_work_price=item_data.piece_work_price,
                        work_price=item_data.work_price,
                        total_cost=item_data.total_cost,
                        total_amount=item_data.total_amount,
                        sale_type=item_data.sale_type,
                        createtime=current_time
                    )
                    self.db.add(stock_out_item)
                
                stock_out.total_amount = total_amount
                stock_out.total_weight = total_weight
            
            stock_out.updatetime = current_time
            
            self.db.commit()
            self.db.refresh(stock_out)
            
            return stock_out
            
        except Exception as e:
            self.db.rollback()
            raise e
    
    def delete_stock_out(self, stock_out_id: int) -> bool:
        """删除出库单"""
        try:
            stock_out = self.db.query(StockOut).filter(StockOut.id == stock_out_id).first()
            if not stock_out:
                return False
            
            # 只有待审核状态才允许删除
            if stock_out.status != 1:
                raise ValueError("只有待审核状态的出库单才能删除")
            
            # 删除明细（由于设置了cascade，会自动删除）
            self.db.delete(stock_out)
            self.db.commit()
            
            return True
            
        except Exception as e:
            self.db.rollback()
            raise e
    
    def audit_stock_out(self, stock_out_id: int, audit_data: StockOutAuditUpdate, auditor_id: int) -> Optional[StockOut]:
        """审核出库单"""
        try:
            stock_out = self.db.query(StockOut).filter(StockOut.id == stock_out_id).first()
            if not stock_out:
                return None
            
            # 只有待审核状态才能审核
            if stock_out.status != 1:
                raise ValueError("只有待审核状态的出库单才能审核")
            
            stock_out.status = audit_data.status
            stock_out.audit_user_id = auditor_id
            stock_out.audit_time = int(time.time())
            if audit_data.audit_remark:
                stock_out.audit_remark = audit_data.audit_remark
            stock_out.updatetime = int(time.time())
            
            self.db.commit()
            self.db.refresh(stock_out)
            
            return stock_out
            
        except Exception as e:
            self.db.rollback()
            raise e
    
    def cancel_audit_stock_out(self, stock_out_id: int) -> Optional[StockOut]:
        """取消审核出库单"""
        try:
            stock_out = self.db.query(StockOut).filter(StockOut.id == stock_out_id).first()
            if not stock_out:
                print(f"❌ [取消审核] 出库单不存在: {stock_out_id}")
                return None
            
            print(f"🔍 [取消审核] 出库单信息:")
            print(f"   ID: {stock_out.id}")
            print(f"   状态: {stock_out.status}")
            print(f"   收款状态: {stock_out.payment_status}")
            
            # 只有已审核状态才能取消审核（已通过=2，未通过=3，已作废=4）
            if stock_out.status not in [2, 3, 4]:
                print(f"❌ [取消审核] 状态不允许: {stock_out.status}")
                raise ValueError("只有已审核状态的出库单才能取消审核")
            
            # 如果已收款，不允许取消审核
            if stock_out.payment_status == 1:
                print(f"❌ [取消审核] 已收款不能取消审核")
                raise ValueError("已收款的出库单不能取消审核")
            
            print(f"✅ [取消审核] 验证通过，开始取消审核")
            
            # 重置为待审核状态
            stock_out.status = 1
            stock_out.audit_user_id = None
            stock_out.audit_time = None
            stock_out.audit_remark = None
            stock_out.updatetime = int(time.time())
            
            self.db.commit()
            self.db.refresh(stock_out)
            
            print(f"✅ [取消审核] 成功完成")
            return stock_out
            
        except Exception as e:
            print(f"❌ [取消审核] 异常: {str(e)}")
            self.db.rollback()
            raise e
    
    def update_payment(self, stock_out_id: int, payment_data: StockOutPaymentUpdate) -> Optional[StockOut]:
        """更新收款信息 - 根据收款方案.md实现简化流程"""
        try:
            stock_out = self.db.query(StockOut).filter(StockOut.id == stock_out_id).first()
            if not stock_out:
                return None

            # 根据收款方案.md：简化流程，待审核状态的出库单也可以直接收款
            # 收款时同时完成审核通过操作
            if stock_out.status not in [1, 2]:  # 只有待审核(1)或已通过(2)状态才能收款
                raise ValueError("只有待审核或已通过审核的出库单才能收款")

            stock_out.payment_status = payment_data.payment_status

            if payment_data.payment_status == 1:  # 已收款
                stock_out.payment_time = int(time.time())

                # 根据收款方案.md：收款时同时将状态更新为审核通过
                if stock_out.status == 1:  # 如果是待审核状态，直接变为审核通过
                    stock_out.status = 2
                    stock_out.audit_time = int(time.time())
                    # 审核人ID可以设置为操作员ID或系统自动审核
                    if not stock_out.audit_user_id:
                        stock_out.audit_user_id = stock_out.operator_id
                    if not stock_out.audit_remark:
                        stock_out.audit_remark = "收款时自动审核通过"

                if payment_data.payment_info:
                    payment_info = payment_data.payment_info
                    stock_out.payment_method = payment_info.payment_method
                    stock_out.payment_remark = payment_info.payment_remark
                    stock_out.cash_amount = payment_info.cash_amount
                    stock_out.wechat_amount = payment_info.wechat_amount
                    stock_out.alipay_amount = payment_info.alipay_amount
                    stock_out.card_amount = payment_info.card_amount
                    stock_out.discount_amount = payment_info.discount_amount
                    stock_out.actual_amount = payment_info.actual_amount

                # 🔧 修复问题1：收款成功后更新商品状态
                # 将所有正常商品（jewelry_id > 0）的状态从1（上架在售）更新为0（已出库）
                from ..models.jewelry import Jewelry
                updated_count = 0
                for item in stock_out.items:
                    if item.jewelry_id and item.jewelry_id > 0:  # 只处理正常商品
                        jewelry = self.db.query(Jewelry).filter(Jewelry.id == item.jewelry_id).first()
                        if jewelry and jewelry.status == 1:  # 只更新上架在售的商品
                            jewelry.status = 0  # 设置为已出库
                            jewelry.updatetime = int(time.time())
                            updated_count += 1

                print(f"✅ [收款] 已更新 {updated_count} 个商品状态为已出库")
            else:  # 未收款
                stock_out.payment_time = None

            stock_out.updatetime = int(time.time())

            self.db.commit()
            self.db.refresh(stock_out)

            return stock_out

        except Exception as e:
            self.db.rollback()
            raise e
    
    def convert_stock_out_to_dict(self, stock_out: StockOut) -> dict:
        """将StockOut对象转换为包含关联名称的字典"""
        print(f"🔄 [DEBUG] 开始转换出库单 ID={stock_out.id} 为字典")
        print(f"📦 [DEBUG] stock_out.items: {stock_out.items}")
        print(f"📊 [DEBUG] items数量: {len(stock_out.items) if stock_out.items else 0}")

        result = {
            'id': stock_out.id,
            'order_no': stock_out.order_no,
            'store_id': stock_out.store_id,
            'customer': stock_out.customer,
            'recycling_id': stock_out.recycling_id,
            'total_weight': stock_out.total_weight,
            'sale_type': stock_out.sale_type,
            'total_amount': stock_out.total_amount,
            'operator_id': stock_out.operator_id,
            'remark': stock_out.remark,
            'status': stock_out.status,
            'createtime': stock_out.createtime,
            'updatetime': stock_out.updatetime,
            'audit_time': stock_out.audit_time,
            'audit_user_id': stock_out.audit_user_id,
            'audit_remark': stock_out.audit_remark,
            'payment_status': stock_out.payment_status,
            'payment_time': stock_out.payment_time,
            'payment_method': stock_out.payment_method,
            'payment_remark': stock_out.payment_remark,
            'cash_amount': stock_out.cash_amount,
            'wechat_amount': stock_out.wechat_amount,
            'alipay_amount': stock_out.alipay_amount,
            'card_amount': stock_out.card_amount,
            'discount_amount': stock_out.discount_amount,
            'actual_amount': stock_out.actual_amount,
            'store_name': stock_out.store.name if stock_out.store else None,
            'store_address': stock_out.store.address if stock_out.store else None,
            'store_phone': stock_out.store.phone if stock_out.store else None,
            'operator_name': stock_out.operator.nickname if stock_out.operator else None,
            'auditor_name': stock_out.auditor.nickname if stock_out.auditor else None,
        }

        # 🔧 修复关键问题：添加items字段转换
        print(f"🔧 [DEBUG] 开始处理items字段转换")
        if stock_out.items:
            print(f"✅ [DEBUG] stock_out.items存在，开始转换 {len(stock_out.items)} 个items")
            result['items'] = []
            for i, item in enumerate(stock_out.items):
                print(f"   📋 [DEBUG] 处理第 {i+1} 个item: ID={item.id}, 条码={item.barcode}")
                item_dict = {
                    'id': item.id,
                    'stock_out_id': item.stock_out_id,
                    'jewelry_id': item.jewelry_id,
                    'barcode': item.barcode,
                    'name': item.name,
                    'category_id': item.category_id,
                    'ring_size': item.ring_size,
                    'gold_weight': float(item.gold_weight) if item.gold_weight else 0.0,
                    'gold_price': float(item.gold_price) if item.gold_price else 0.0,
                    'silver_weight': float(item.silver_weight) if item.silver_weight else 0.0,
                    'silver_price': float(item.silver_price) if item.silver_price else 0.0,
                    'total_weight': float(item.total_weight) if item.total_weight else 0.0,
                    'silver_work_price': float(item.silver_work_price) if item.silver_work_price else 0.0,
                    'piece_work_price': float(item.piece_work_price) if item.piece_work_price else 0.0,
                    'work_price': float(item.work_price) if item.work_price else 0.0,
                    'total_amount': float(item.total_amount) if item.total_amount else 0.0,
                    'sale_type': item.sale_type,
                    'createtime': item.createtime,
                    'updatetime': item.updatetime,
                }

                # 添加关联的首饰信息
                if item.jewelry:
                    item_dict['jewelry'] = {
                        'id': item.jewelry.id,
                        'barcode': item.jewelry.barcode,
                        'name': item.jewelry.name,
                        'category_id': item.jewelry.category_id,
                        'ring_size': item.jewelry.ring_size,
                        'gold_weight': float(item.jewelry.gold_weight) if item.jewelry.gold_weight else 0.0,
                        'silver_weight': float(item.jewelry.silver_weight) if item.jewelry.silver_weight else 0.0,
                        'total_weight': float(item.jewelry.total_weight) if item.jewelry.total_weight else 0.0,
                        'status': item.jewelry.status,
                        'store_id': item.jewelry.store_id,
                    }

                    # 添加首饰分类信息
                    if hasattr(item.jewelry, 'category') and item.jewelry.category:
                        item_dict['jewelry']['category'] = {
                            'id': item.jewelry.category.id,
                            'name': item.jewelry.category.name,
                        }
                else:
                    item_dict['jewelry'] = None

                result['items'].append(item_dict)
                print(f"   ✅ [DEBUG] 第 {i+1} 个item转换完成")
        else:
            print(f"❌ [DEBUG] stock_out.items为空或None，设置items=None")
            result['items'] = None

        print(f"🏁 [DEBUG] 出库单转换完成，items字段: {result.get('items', 'NOT_SET')}")
        return result
    
    def get_stock_out_statistics(self) -> StockOutStatistics:
        """获取出库单统计信息"""
        # 基础状态统计
        total_count = self.db.query(StockOut).count()
        pending_count = self.db.query(StockOut).filter(StockOut.status == 1).count()
        approved_count = self.db.query(StockOut).filter(StockOut.status == 2).count()
        rejected_count = self.db.query(StockOut).filter(StockOut.status == 3).count()
        cancelled_count = self.db.query(StockOut).filter(StockOut.status == 4).count()
        
        # 总金额和总重量统计
        total_amount_result = self.db.query(func.sum(StockOut.total_amount)).scalar()
        total_amount = total_amount_result or Decimal('0.00')
        
        total_weight_result = self.db.query(func.sum(StockOut.total_weight)).scalar()
        total_weight = total_weight_result or Decimal('0.00')
        
        # 门店分布统计
        store_stats = self.db.query(
            Store.id,
            Store.name,
            func.count(StockOut.id).label('stock_out_count'),
            func.sum(StockOut.total_amount).label('total_amount'),
            func.sum(StockOut.total_weight).label('total_weight')
        ).outerjoin(StockOut).group_by(Store.id, Store.name).all()
        
        store_distribution = []
        for stat in store_stats:
            store_distribution.append({
                'store_id': stat.id,
                'store_name': stat.name,
                'stock_out_count': stat.stock_out_count or 0,
                'total_amount': float(stat.total_amount or 0),
                'total_weight': float(stat.total_weight or 0)
            })
        
        # 销售类型分布统计
        sale_type_stats = self.db.query(
            StockOut.sale_type,
            func.count(StockOut.id).label('count'),
            func.sum(StockOut.total_amount).label('total_amount'),
            func.sum(StockOut.total_weight).label('total_weight')
        ).group_by(StockOut.sale_type).all()
        
        sale_type_distribution = []
        for stat in sale_type_stats:
            sale_type_distribution.append({
                'sale_type': stat.sale_type,
                'count': stat.count or 0,
                'total_amount': float(stat.total_amount or 0),
                'total_weight': float(stat.total_weight or 0)
            })
        
        return StockOutStatistics(
            total_count=total_count,
            pending_count=pending_count,
            approved_count=approved_count,
            rejected_count=rejected_count,
            cancelled_count=cancelled_count,
            total_amount=total_amount,
            total_weight=total_weight,
            store_distribution=store_distribution,
            sale_type_distribution=sale_type_distribution
        ) 