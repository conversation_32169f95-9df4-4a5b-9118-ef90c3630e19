# 黄金珠宝管理系统 - 开发工具
# 使用方法: make <target>

.PHONY: help install dev test lint format check clean run

# 默认目标
help:
	@echo "🏆 黄金珠宝管理系统 - 开发工具"
	@echo ""
	@echo "可用命令:"
	@echo "  install    - 安装项目依赖"
	@echo "  dev        - 安装开发依赖"
	@echo "  run        - 启动开发服务器"
	@echo "  test       - 运行测试"
	@echo "  lint       - 代码检查"
	@echo "  format     - 代码格式化"
	@echo "  check      - 完整的代码质量检查"
	@echo "  clean      - 清理临时文件"
	@echo "  requirements - 更新依赖文件"

# 安装项目依赖
install:
	@echo "📦 安装项目依赖..."
	pip install -r requirements.txt

# 安装开发依赖
dev: install
	@echo "🛠️ 安装开发依赖..."
	pip install ruff pytest pytest-asyncio httpx

# 启动开发服务器
run:
	@echo "🚀 启动开发服务器..."
	python main.py

# 运行测试
test:
	@echo "🧪 运行测试..."
	pytest tests/ -v

# 代码检查
lint:
	@echo "🔍 运行代码检查..."
	ruff check app/

# 代码格式化
format:
	@echo "🎨 格式化代码..."
	ruff format app/

# 完整的代码质量检查
check:
	@echo "✅ 运行完整代码质量检查..."
	chmod +x scripts/code_quality.sh
	./scripts/code_quality.sh

# 清理临时文件
clean:
	@echo "🧹 清理临时文件..."
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	find . -type d -name ".pytest_cache" -exec rm -rf {} +
	find . -type d -name ".ruff_cache" -exec rm -rf {} +
	rm -rf build/
	rm -rf dist/
	@echo "✅ 清理完成"

# 更新依赖文件
requirements:
	@echo "📋 更新依赖文件..."
	pip freeze > requirements_freeze.txt
	@echo "✅ 依赖文件已更新到 requirements_freeze.txt"

# 生成安全报告
security:
	@echo "🔒 生成安全报告..."
	ruff check app/ --select=S > reports/security_report.txt
	@echo "✅ 安全报告已保存到 reports/security_report.txt"

# 生成复杂度报告
complexity:
	@echo "🧮 生成复杂度报告..."
	ruff check app/ --select=C90 > reports/complexity_report.txt
	@echo "✅ 复杂度报告已保存到 reports/complexity_report.txt"

# CI/CD 检查（用于自动化流水线）
ci: clean lint test
	@echo "🎯 CI/CD 检查完成"