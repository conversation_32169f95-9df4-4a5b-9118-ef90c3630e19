import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/utils/number_formatter.dart';
import '../../../../models/recycling/recycling_model.dart';
import '../../../../core/widgets/responsive_layout.dart';

/// 旧料回收详情查看对话框
/// 参考入库单详情界面设计，保持界面一致性
class RecyclingDetailDialog extends StatefulWidget {
  final RecyclingOrder recyclingOrder;

  const RecyclingDetailDialog({
    super.key,
    required this.recyclingOrder,
  });

  @override
  State<RecyclingDetailDialog> createState() => _RecyclingDetailDialogState();
}

class _RecyclingDetailDialogState extends State<RecyclingDetailDialog> {
  @override
  void initState() {
    super.initState();
    LoggerService.d('🔍 打开旧料回收详情对话框: ${widget.recyclingOrder.orderNo}');
    LoggerService.d('📊 回收单数据: 总件数=${widget.recyclingOrder.itemCount}, 总金额=${widget.recyclingOrder.totalAmount}');
    LoggerService.d('📦 明细数量: ${widget.recyclingOrder.items.length}');

    // 打印每个明细项的信息
    for (int i = 0; i < widget.recyclingOrder.items.length; i++) {
      final item = widget.recyclingOrder.items[i];
      LoggerService.d('📦 明细${i + 1}: ${item.itemName}, 分类: ${item.categoryName}, 金额: ${item.amount}');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: const EdgeInsets.all(16),
      child: Container(
        constraints: const BoxConstraints(
          maxWidth: 1200,
          maxHeight: 800,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(context),
            const Divider(height: 1),
            Expanded(
              child: _buildContent(context),
            ),
            const Divider(height: 1),
            _buildFooter(context),
          ],
        ),
      ),
    );
  }

  /// 构建对话框头部
  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          Icon(
            Icons.recycling,
            color: Colors.purple[600],
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '旧料回收详情',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  widget.recyclingOrder.orderNo,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => Get.back(),
            icon: const Icon(Icons.close),
            tooltip: '关闭',
          ),
        ],
      ),
    );
  }

  /// 构建主要内容区域
  Widget _buildContent(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildBasicInfo(),
          const SizedBox(height: 24),
          _buildItemsList(),
          const SizedBox(height: 24),
          _buildSummaryInfo(),
        ],
      ),
    );
  }

  /// 构建基本信息卡片
  Widget _buildBasicInfo() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
                SizedBox(width: 8),
                Text(
                  '基本信息',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ScreenTypeLayout(
              mobile: _buildBasicInfoMobile(),
              tablet: _buildBasicInfoDesktop(),
              desktop: _buildBasicInfoDesktop(),
            ),
          ],
        ),
      ),
    );
  }

  /// 移动端基本信息布局
  Widget _buildBasicInfoMobile() {
    return Column(
      children: [
        _buildInfoRow('回收单号', widget.recyclingOrder.orderNo, isHighlight: true),
        _buildInfoRow('客户姓名', widget.recyclingOrder.customerName),
        _buildInfoRow('客户电话', widget.recyclingOrder.customerPhone),
        _buildInfoRow('门店', widget.recyclingOrder.storeName),
        _buildInfoRow('回收时间', _formatDateTime(widget.recyclingOrder.orderDate)),
        _buildInfoRow('操作员', widget.recyclingOrder.creatorName),
        _buildInfoRow('状态', _getStatusText(widget.recyclingOrder.status),
                     statusColor: _getStatusColor(widget.recyclingOrder.status)),
        if (widget.recyclingOrder.remark.isNotEmpty)
          _buildInfoRow('备注', widget.recyclingOrder.remark),
      ],
    );
  }

  /// 桌面端基本信息布局
  Widget _buildBasicInfoDesktop() {
    return Row(
      children: [
        Expanded(
          child: Column(
            children: [
              _buildInfoRow('回收单号', widget.recyclingOrder.orderNo, isHighlight: true),
              _buildInfoRow('客户姓名', widget.recyclingOrder.customerName),
              _buildInfoRow('客户电话', widget.recyclingOrder.customerPhone),
              _buildInfoRow('门店', widget.recyclingOrder.storeName),
            ],
          ),
        ),
        Expanded(
          child: Column(
            children: [
              _buildInfoRow('回收时间', _formatDateTime(widget.recyclingOrder.orderDate)),
              _buildInfoRow('操作员', widget.recyclingOrder.creatorName),
              _buildInfoRow('状态', _getStatusText(widget.recyclingOrder.status),
                           statusColor: _getStatusColor(widget.recyclingOrder.status)),
              if (widget.recyclingOrder.remark.isNotEmpty)
                _buildInfoRow('备注', widget.recyclingOrder.remark),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建信息行
  Widget _buildInfoRow(String label, String value, {bool isHighlight = false, Color? statusColor}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: statusColor != null
                ? Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color: statusColor.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          value,
                          style: TextStyle(
                            fontSize: 14,
                            color: statusColor,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  )
                : Text(
                    value,
                    style: TextStyle(
                      fontSize: 14,
                      color: isHighlight ? AppTheme.primaryColor : Colors.black87,
                      fontWeight: isHighlight ? FontWeight.w600 : FontWeight.normal,
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  /// 构建旧料明细列表
  Widget _buildItemsList() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.inventory_2_outlined,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Text(
                  '旧料明细',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '共 ${widget.recyclingOrder.itemCount} 件',
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppTheme.primaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ScreenTypeLayout(
              mobile: _buildItemsListMobile(),
              tablet: _buildItemsTable(),
              desktop: _buildItemsTable(),
            ),
          ],
        ),
      ),
    );
  }

  /// 移动端旧料列表
  Widget _buildItemsListMobile() {
    LoggerService.d('🔍 构建移动端旧料列表，items数量: ${widget.recyclingOrder.items.length}');

    if (widget.recyclingOrder.items.isEmpty) {
      LoggerService.w('⚠️ 旧料明细为空');
      return _buildEmptyItems();
    }

    return Column(
      children: widget.recyclingOrder.items.map((item) {
        LoggerService.d('📦 处理旧料项: ${item.itemName}, 金额: ${item.amount}');
        return _buildItemCard(item);
      }).toList(),
    );
  }

  /// 桌面端旧料表格
  Widget _buildItemsTable() {
    LoggerService.d('🔍 构建桌面端旧料表格，items数量: ${widget.recyclingOrder.items.length}');

    if (widget.recyclingOrder.items.isEmpty) {
      LoggerService.w('⚠️ 旧料明细为空');
      return _buildEmptyItems();
    }

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          _buildTableHeader(),
          ...widget.recyclingOrder.items.map((item) {
            LoggerService.d('📦 处理表格行: ${item.itemName}, 分类: ${item.categoryName}');
            return _buildTableRow(item);
          }),
        ],
      ),
    );
  }

  /// 构建表格头部
  Widget _buildTableHeader() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(8),
          topRight: Radius.circular(8),
        ),
      ),
      child: Row(
        children: [
          Expanded(flex: 3, child: _buildHeaderCell('物品名称')),
          Expanded(flex: 1, child: _buildHeaderCell('分类')),
          Expanded(flex: 2, child: _buildHeaderCell('金重(g)')),
          Expanded(flex: 2, child: _buildHeaderCell('金价(¥)')),
          Expanded(flex: 2, child: _buildHeaderCell('银重(g)')),
          Expanded(flex: 2, child: _buildHeaderCell('银价(¥)')),
          Expanded(flex: 2, child: _buildHeaderCell('总金额(¥)')),
          Expanded(flex: 3, child: _buildHeaderCell('折扣')),
          Expanded(flex: 2, child: _buildHeaderCell('状态')),
          Expanded(flex: 2, child: _buildHeaderCell('备注')),
        ],
      ),
    );
  }

  /// 构建表格头部单元格
  Widget _buildHeaderCell(String text) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
      child: Text(
        text,
        style: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          color: Colors.black87,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  /// 构建表格数据行
  Widget _buildTableRow(RecyclingItem item) {
    return Container(
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(color: Colors.grey[300]!),
        ),
      ),
      child: Row(
        children: [
          Expanded(flex: 3, child: _buildDataCell(item.itemName)),
          Expanded(flex: 1, child: _buildDataCell(item.categoryName)),
          Expanded(flex: 2, child: _buildDataCell(NumberFormatter.formatWeight(item.goldWeight))),
          Expanded(flex: 2, child: _buildDataCell(item.goldPrice.toStringAsFixed(0))),
          Expanded(flex: 2, child: _buildDataCell(NumberFormatter.formatWeight(item.silverWeight))),
          Expanded(flex: 2, child: _buildDataCell(item.silverPrice.toStringAsFixed(1))),
          Expanded(flex: 2, child: _buildDataCell(NumberFormatter.formatCurrency(item.amount))),
          Expanded(flex: 3, child: _buildDataCell(NumberFormatter.formatDiscount(item.discountRate, item.discountAmount))),
          Expanded(flex: 2, child: _buildDataCell(item.statusText)),
          Expanded(flex: 2, child: _buildDataCell(item.remark ?? '-')),
        ],
      ),
    );
  }

  /// 构建数据单元格
  Widget _buildDataCell(String text) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
      child: Text(
        text,
        style: const TextStyle(
          fontSize: 14,
          color: Colors.black87,
        ),
        textAlign: TextAlign.center,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  /// 构建旧料卡片（移动端）
  Widget _buildItemCard(RecyclingItem item) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            item.itemName,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: _buildItemInfo('分类', item.categoryName),
              ),
              Expanded(
                child: _buildItemInfo('总金额', NumberFormatter.formatCurrency(item.amount)),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              Expanded(
                child: _buildItemInfo('金重', NumberFormatter.formatWeight(item.goldWeight)),
              ),
              Expanded(
                child: _buildItemInfo('金价', NumberFormatter.formatPrice(item.goldPrice)),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              Expanded(
                child: _buildItemInfo('银重', NumberFormatter.formatWeight(item.silverWeight)),
              ),
              Expanded(
                child: _buildItemInfo('银价', NumberFormatter.formatPrice(item.silverPrice)),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              Expanded(
                child: _buildItemInfo('折扣', NumberFormatter.formatDiscount(item.discountRate, item.discountAmount)),
              ),
              Expanded(
                child: _buildItemInfo('状态', item.statusText),
              ),
            ],
          ),
          if (item.remark?.isNotEmpty == true) ...[
            const SizedBox(height: 4),
            _buildItemInfo('备注', item.remark!),
          ],
        ],
      ),
    );
  }

  /// 构建物品信息项
  Widget _buildItemInfo(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Text(
            '$label: ',
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.black87,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建空物品状态
  Widget _buildEmptyItems() {
    return Container(
      padding: const EdgeInsets.all(40),
      child: const Column(
        children: [
          Icon(
            Icons.inventory_2_outlined,
            size: 48,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text(
            '暂无旧料明细',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建汇总信息
  Widget _buildSummaryInfo() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(
                  Icons.calculate_outlined,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
                SizedBox(width: 8),
                Text(
                  '汇总信息',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ScreenTypeLayout(
              mobile: _buildSummaryMobile(),
              tablet: _buildSummaryDesktop(),
              desktop: _buildSummaryDesktop(),
            ),
          ],
        ),
      ),
    );
  }

  /// 移动端汇总信息
  Widget _buildSummaryMobile() {
    // 计算折后总金额
    double totalFinalAmount = 0.0;
    for (var item in widget.recyclingOrder.items) {
      // 折后金额 = 原金额 * (折扣率/100)
      double finalAmount = item.amount * (item.discountRate / 100);
      totalFinalAmount += finalAmount;
    }
    
    return Column(
      children: [
        _buildSummaryItem('总件数', '${widget.recyclingOrder.itemCount}件', Icons.inventory),
        _buildSummaryItem('总金额', '¥${totalFinalAmount.toStringAsFixed(2)}', Icons.attach_money),
        _buildSummaryItem('总重量', '${widget.recyclingOrder.totalWeight.toStringAsFixed(2)}g', Icons.scale),
      ],
    );
  }

  /// 桌面端汇总信息
  Widget _buildSummaryDesktop() {
    // 计算折后总金额
    double totalFinalAmount = 0.0;
    for (var item in widget.recyclingOrder.items) {
      // 折后金额 = 原金额 * (折扣率/100)
      double finalAmount = item.amount * (item.discountRate / 100);
      totalFinalAmount += finalAmount;
    }
    
    return Row(
      children: [
        Expanded(child: _buildSummaryItem('总件数', '${widget.recyclingOrder.itemCount}件', Icons.inventory)),
        Expanded(child: _buildSummaryItem('总金额', '¥${totalFinalAmount.toStringAsFixed(2)}', Icons.attach_money)),
        Expanded(child: _buildSummaryItem('总重量', '${widget.recyclingOrder.totalWeight.toStringAsFixed(2)}g', Icons.scale)),
      ],
    );
  }

  /// 构建汇总项
  Widget _buildSummaryItem(String label, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            color: AppTheme.primaryColor,
            size: 20,
          ),
          const SizedBox(width: 8),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                label,
                style: const TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                ),
              ),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建底部按钮区域
  Widget _buildFooter(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('关闭'),
          ),
          const SizedBox(width: 12),
          ElevatedButton.icon(
            onPressed: _copyToClipboard,
            icon: const Icon(Icons.copy, size: 18),
            label: const Text('复制信息'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 复制回收单信息到剪贴板
  void _copyToClipboard() {
    final buffer = StringBuffer();
    buffer.writeln('=== 旧料回收详情 ===');
    buffer.writeln('回收单号: ${widget.recyclingOrder.orderNo}');
    buffer.writeln('客户姓名: ${widget.recyclingOrder.customerName}');
    buffer.writeln('客户电话: ${widget.recyclingOrder.customerPhone}');
    buffer.writeln('门店: ${widget.recyclingOrder.storeName}');
    buffer.writeln('回收时间: ${_formatDateTime(widget.recyclingOrder.orderDate)}');
    buffer.writeln('操作员: ${widget.recyclingOrder.creatorName}');
    buffer.writeln('状态: ${_getStatusText(widget.recyclingOrder.status)}');
    if (widget.recyclingOrder.remark.isNotEmpty) {
      buffer.writeln('备注: ${widget.recyclingOrder.remark}');
    }
    buffer.writeln();
    buffer.writeln('=== 旧料明细 ===');
    for (int i = 0; i < widget.recyclingOrder.items.length; i++) {
      final item = widget.recyclingOrder.items[i];
      buffer.writeln('${i + 1}. ${item.itemName}');
      buffer.writeln('   分类: ${item.categoryName}');
      buffer.writeln('   金重: ${NumberFormatter.formatWeight(item.goldWeight)}');
      buffer.writeln('   金价: ${NumberFormatter.formatPrice(item.goldPrice)}');
      buffer.writeln('   银重: ${NumberFormatter.formatWeight(item.silverWeight)}');
      buffer.writeln('   银价: ${NumberFormatter.formatPrice(item.silverPrice)}');
      buffer.writeln('   总金额: ${NumberFormatter.formatCurrency(item.amount)}');
      buffer.writeln('   折扣: ${NumberFormatter.formatDiscount(item.discountRate, item.discountAmount)}');
      buffer.writeln('   状态: ${item.statusText}');
      if (item.remark?.isNotEmpty == true) {
        buffer.writeln('   备注: ${item.remark}');
      }
      buffer.writeln();
    }
    // 计算折后总金额
    double totalFinalAmount = 0.0;
    for (var item in widget.recyclingOrder.items) {
      // 折后金额 = 原金额 * (折扣率/100)
      double finalAmount = item.amount * (item.discountRate / 100);
      totalFinalAmount += finalAmount;
    }
    
    buffer.writeln('=== 汇总信息 ===');
    buffer.writeln('总件数: ${widget.recyclingOrder.itemCount}件');
    buffer.writeln('总金额: ¥${totalFinalAmount.toStringAsFixed(2)}');
    buffer.writeln('总重量: ${widget.recyclingOrder.totalWeight.toStringAsFixed(2)}g');

    Clipboard.setData(ClipboardData(text: buffer.toString()));

    Get.snackbar(
      '成功',
      '回收单信息已复制到剪贴板',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.green,
      colorText: Colors.white,
      duration: const Duration(seconds: 2),
    );
  }

  /// 格式化日期时间
  String _formatDateTime(DateTime dateTime) {
    return DateFormat('yyyy-MM-dd HH:mm:ss').format(dateTime);
  }

  /// 获取状态文本
  String _getStatusText(int status) {
    switch (status) {
      case 0:
        return '待处理';
      case 1:
        return '已处理';
      case 2:
        return '已完成';
      case 3:
        return '已取消';
      default:
        return '未知状态';
    }
  }

  /// 获取状态颜色
  Color _getStatusColor(int status) {
    switch (status) {
      case 0:
        return Colors.orange;
      case 1:
        return Colors.blue;
      case 2:
        return Colors.green;
      case 3:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
