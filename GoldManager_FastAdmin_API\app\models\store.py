"""
门店相关数据模型
对应FastAdmin数据库中的门店表结构
"""

from sqlalchemy import Column, Integer, String, DateTime
from sqlalchemy.orm import relationship
from ..core.database import Base


class Store(Base):
    """门店表 - 对应 fa_store"""
    __tablename__ = "fa_store"

    id = Column(Integer, primary_key=True, index=True, comment="门店ID")
    name = Column(String(100), nullable=False, comment="门店名称")
    address = Column(String(255), comment="门店地址")
    phone = Column(String(20), comment="联系电话")
    status = Column(Integer, default=1, comment="状态:0=关闭,1=正常")
    createtime = Column(Integer, comment="创建时间")
    updatetime = Column(Integer, comment="更新时间")

    # 关联关系 - 第一阶段已实现的关联
    jewelry_items = relationship("Jewelry", back_populates="store")
    admins = relationship("Admin", back_populates="store")

    # 第二阶段库存管理关联关系 - 已启用入库、出库、退货、盘点和回收管理
    stock_ins = relationship("StockIn", back_populates="store")
    stock_outs = relationship("StockOut", back_populates="store")
    stock_returns = relationship("StockReturn", back_populates="store")
    inventory_checks = relationship("InventoryCheck", back_populates="store")
    recyclings = relationship("Recycling", back_populates="store")

    # 第三阶段业务扩展关联关系 - 门店调拨
    from_transfers = relationship("StoreTransfer", foreign_keys="StoreTransfer.from_store_id", back_populates="from_store")
    to_transfers = relationship("StoreTransfer", foreign_keys="StoreTransfer.to_store_id", back_populates="to_store")