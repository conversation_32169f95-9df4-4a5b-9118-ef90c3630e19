import 'package:flutter/material.dart';

/// 仪表盘菜单项模型
class DashboardMenuItem {
  /// 菜单标题
  final String title;
  
  /// 菜单图标
  final IconData icon;
  
  /// 对应路由
  final String route;
  
  /// 所需权限
  final String permission;
  
  /// 是否活跃
  bool isActive;
  
  DashboardMenuItem({
    required this.title,
    required this.icon,
    required this.route,
    required this.permission,
    this.isActive = false,
  });
} 