import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gold_manager_flutter/core/constants/border_styles.dart';
import '../widgets/store_transfer_dialog.dart';

/// 库存调拨测试页面
/// 用于测试新建调拨对话框的功能
class StoreTransferTestView extends StatelessWidget {
  const StoreTransferTestView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('库存调拨测试'),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Container(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.swap_horiz,
                size: 64,
                color: Colors.blue[600],
              ),
              const SizedBox(height: 24),
              const Text(
                '库存调拨功能测试',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                '点击下方按钮测试新建调拨对话框功能',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              SizedBox(
                width: 200,
                height: 48,
                child: ElevatedButton.icon(
                  onPressed: _showTransferDialog,
                  icon: const Icon(Icons.add, size: 20),
                  label: const Text('新建调拨'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue[600],
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
                    ),
                    textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                  ),
                ),
              ),
              const SizedBox(height: 24),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(AppBorderStyles.mediumBorderRadius),
                  border: Border.all(color: Colors.blue[200]!, width: AppBorderStyles.borderWidth),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.info_outline, color: Colors.blue[600], size: 20),
                        const SizedBox(width: 8),
                        const Text(
                          '功能特性',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Colors.black87,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    const Text(
                      '✅ 严格遵循AppBorderStyles统一边框调用方式\n'
                      '✅ 单行布局设计，包含批量调价功能\n'
                      '✅ 完全复用新建出库单的批量调价实现\n'
                      '✅ 智能商品验证和调拨类型判断\n'
                      '✅ 实时价格计算和汇总统计\n'
                      '✅ 32px高度控件和橙色主题规范',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.black87,
                        height: 1.5,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 显示新建调拨对话框
  void _showTransferDialog() {
    Get.dialog(
      const StoreTransferDialog(),
      barrierDismissible: false,
    ).then((result) {
      if (result == true) {
        Get.snackbar(
          '成功',
          '调拨单操作完成',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      }
    });
  }
}
