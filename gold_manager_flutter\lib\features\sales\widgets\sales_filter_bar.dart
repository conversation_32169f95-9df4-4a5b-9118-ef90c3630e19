import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../core/constants/border_styles.dart';
import '../../../core/widgets/custom_dropdown.dart';
import '../../../core/widgets/custom_text_field.dart';
import '../../../models/store/store.dart';

/// 销售筛选栏组件
class SalesFilterBar extends StatelessWidget {
  final TextEditingController searchController;
  final DateTime? startDate;
  final DateTime? endDate;
  final int selectedStoreId;
  final List<Store> storeList;
  final VoidCallback onSearch;
  final VoidCallback onReset;
  final Function(int) onStoreChanged;
  final Function(DateTime?, DateTime?) onDateRangeChanged;

  const SalesFilterBar({
    super.key,
    required this.searchController,
    required this.startDate,
    required this.endDate,
    required this.selectedStoreId,
    required this.storeList,
    required this.onSearch,
    required this.onReset,
    required this.onStoreChanged,
    required this.onDateRangeChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 第一行：搜索框和门店选择
        Row(
          children: [
            // 搜索框
            Expanded(
              flex: 3,
              child: CustomTextField(
                controller: searchController,
                hintText: '搜索出库单号、条码、商品名称...',
                prefixIcon: const Icon(Icons.search, size: 18, color: Color(0xFF757575)),
                onChanged: (_) {
                  // 可以在这里实现实时搜索，或者保留为空等待用户点击搜索按钮
                },
              ),
            ),
            const SizedBox(width: 12),
            
            // 门店选择下拉框
            Expanded(
              flex: 2,
              child: CustomDropdown<int>(
                value: selectedStoreId,
                hint: '选择门店',
                items: [
                  const DropdownMenuItem<int>(
                    value: 0,
                    child: Text('全部门店'),
                  ),
                  ...storeList.map((store) {
                    return DropdownMenuItem<int>(
                      value: store.id,
                      child: Text(store.name),
                    );
                  }),
                ],
                onChanged: (value) => onStoreChanged(value ?? 0),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        
        // 第二行：时间范围选择和操作按钮
        Row(
          children: [
            // 开始日期
            Expanded(
              flex: 2,
              child: _buildDateSelector(
                context,
                startDate,
                '开始日期',
                () => _selectStartDate(context),
              ),
            ),
            const SizedBox(width: 8),
            
            // 分隔符
            Container(
              width: 16,
              height: 1,
              color: const Color(0xFFBDBDBD),
            ),
            const SizedBox(width: 8),
            
            // 结束日期
            Expanded(
              flex: 2,
              child: _buildDateSelector(
                context,
                endDate,
                '结束日期',
                () => _selectEndDate(context),
              ),
            ),
            const SizedBox(width: 12),
            
            // 快捷日期按钮
            _buildQuickDateButton('今日', () => _setDateRange(DateTime.now(), DateTime.now())),
            const SizedBox(width: 8),
            _buildQuickDateButton('本周', () => _setWeekRange()),
            const SizedBox(width: 8),
            _buildQuickDateButton('本月', () => _setMonthRange()),
            const SizedBox(width: 12),
            
            // 搜索按钮
            ElevatedButton.icon(
              onPressed: onSearch,
              icon: const Icon(Icons.search, size: 16),
              label: const Text('搜索'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF1E88E5),
                foregroundColor: Colors.white,
                minimumSize: const Size(80, 32),
                maximumSize: const Size(80, 32),
                padding: const EdgeInsets.symmetric(horizontal: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
                ),
                textStyle: const TextStyle(
                  fontSize: 13,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            const SizedBox(width: 8),
            
            // 重置按钮
            OutlinedButton.icon(
              onPressed: onReset,
              icon: const Icon(Icons.refresh, size: 16),
              label: const Text('重置'),
              style: OutlinedButton.styleFrom(
                foregroundColor: const Color(0xFF757575),
                side: const BorderSide(color: Color(0xFFE0E0E0)),
                minimumSize: const Size(80, 32),
                maximumSize: const Size(80, 32),
                padding: const EdgeInsets.symmetric(horizontal: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
                ),
                textStyle: const TextStyle(
                  fontSize: 13,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建日期选择器
  Widget _buildDateSelector(
    BuildContext context,
    DateTime? date,
    String placeholder,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 32,
        padding: const EdgeInsets.symmetric(horizontal: 8),
        decoration: BoxDecoration(
          border: Border.all(color: const Color(0xFFE0E0E0)),
          borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
          color: Colors.white,
        ),
        child: Row(
          children: [
            Icon(
              Icons.calendar_today,
              size: 14,
              color: Colors.grey[600],
            ),
            const SizedBox(width: 6),
            Expanded(
              child: Text(
                date != null
                    ? DateFormat('yyyy-MM-dd').format(date)
                    : placeholder,
                style: TextStyle(
                  color: date != null ? const Color(0xFF212121) : const Color(0xFF757575),
                  fontSize: 13,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建快捷日期按钮
  Widget _buildQuickDateButton(String label, VoidCallback onTap) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
        child: Container(
          height: 32,
          padding: const EdgeInsets.symmetric(horizontal: 10),
          decoration: BoxDecoration(
            color: const Color(0xFFF5F5F5),
            borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
            border: Border.all(color: const Color(0xFFE0E0E0)),
          ),
          alignment: Alignment.center,
          child: Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              color: Color(0xFF757575),
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }

  /// 选择开始日期
  Future<void> _selectStartDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: startDate ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      onDateRangeChanged(picked, endDate);
    }
  }

  /// 选择结束日期
  Future<void> _selectEndDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: endDate ?? DateTime.now(),
      firstDate: startDate ?? DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      onDateRangeChanged(startDate, picked);
    }
  }

  /// 设置日期范围
  void _setDateRange(DateTime start, DateTime end) {
    onDateRangeChanged(start, end);
  }

  /// 设置本周范围
  void _setWeekRange() {
    final now = DateTime.now();
    final weekday = now.weekday;
    final startOfWeek = now.subtract(Duration(days: weekday - 1));
    final endOfWeek = startOfWeek.add(const Duration(days: 6));
    
    _setDateRange(
      DateTime(startOfWeek.year, startOfWeek.month, startOfWeek.day),
      DateTime(endOfWeek.year, endOfWeek.month, endOfWeek.day),
    );
  }

  /// 设置本月范围
  void _setMonthRange() {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 0);
    
    _setDateRange(startOfMonth, endOfMonth);
  }
}