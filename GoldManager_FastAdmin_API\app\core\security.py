"""
JWT认证和安全相关工具
提供JWT令牌生成、验证、密码加密等功能
"""

import hashlib
import random
import string
from datetime import datetime, timedelta
from typing import Optional, Union, Any

from jose import JWTError, jwt
from passlib.context import CryptContext
from pydantic import BaseModel

from .config import settings


class TokenData(BaseModel):
    """JWT令牌数据模型"""
    admin_id: Optional[int] = None
    username: Optional[str] = None
    store_id: Optional[int] = None
    permissions: Optional[list] = None


class JWTManager:
    """JWT管理器"""

    def __init__(self):
        self.secret_key = settings.SECRET_KEY
        self.algorithm = settings.ALGORITHM
        self.access_token_expire_minutes = settings.ACCESS_TOKEN_EXPIRE_MINUTES
        self.refresh_token_expire_days = settings.REFRESH_TOKEN_EXPIRE_DAYS

        # 密码加密上下文
        self.pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

    def create_access_token(
        self,
        data: dict,
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """创建访问令牌"""
        to_encode = data.copy()

        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes)

        to_encode.update({"exp": expire, "type": "access"})
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt

    def create_refresh_token(
        self,
        data: dict,
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """创建刷新令牌"""
        to_encode = data.copy()

        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(days=self.refresh_token_expire_days)

        to_encode.update({"exp": expire, "type": "refresh"})
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt

    def verify_token(self, token: str) -> Optional[TokenData]:
        """验证令牌"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            admin_id_str: str = payload.get("sub")
            username: str = payload.get("username")
            store_id: int = payload.get("store_id")
            permissions: list = payload.get("permissions", [])

            if admin_id_str is None:
                return None

            # 将字符串转换为整数
            admin_id = int(admin_id_str)

            token_data = TokenData(
                admin_id=admin_id,
                username=username,
                store_id=store_id,
                permissions=permissions
            )
            return token_data
        except JWTError:
            return None

    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """验证密码"""
        return self.pwd_context.verify(plain_password, hashed_password)

    def get_password_hash(self, password: str) -> str:
        """获取密码哈希"""
        return self.pwd_context.hash(password)


class FastAdminPasswordManager:
    """FastAdmin密码管理器 - 兼容原有密码系统"""

    @staticmethod
    def generate_salt(length: int = 6) -> str:
        """生成盐值"""
        return ''.join(random.choices(string.ascii_letters + string.digits, k=length))

    @staticmethod
    def hash_password(password: str, salt: str) -> str:
        """使用MD5+盐值加密密码 (兼容FastAdmin)"""
        # FastAdmin的加密方式: md5(md5(password) + salt)
        password_md5 = hashlib.md5(password.encode()).hexdigest()
        return hashlib.md5((password_md5 + salt).encode()).hexdigest()

    @staticmethod
    def verify_password(password: str, salt: str, hashed_password: str) -> bool:
        """验证密码"""
        return FastAdminPasswordManager.hash_password(password, salt) == hashed_password

    @staticmethod
    def create_password(password: str) -> tuple[str, str]:
        """创建密码和盐值"""
        salt = FastAdminPasswordManager.generate_salt()
        hashed = FastAdminPasswordManager.hash_password(password, salt)
        return hashed, salt


class PermissionManager:
    """权限管理器"""

    # 权限常量
    PERMISSIONS = {
        # 商品管理权限
        "jewelry.view": "查看商品",
        "jewelry.create": "创建商品",
        "jewelry.update": "更新商品",
        "jewelry.delete": "删除商品",

        # 门店管理权限
        "store.view": "查看门店",
        "store.create": "创建门店",
        "store.update": "更新门店",
        "store.delete": "删除门店",

        # 会员管理权限
        "member.view": "查看会员",
        "member.create": "创建会员",
        "member.update": "更新会员",
        "member.delete": "删除会员",

        # 管理员权限
        "admin.view": "查看管理员",
        "admin.create": "创建管理员",
        "admin.update": "更新管理员",
        "admin.delete": "删除管理员",

        # 库存管理权限
        "stock.view": "查看库存",
        "stock.create": "创建库存单据",
        "stock.update": "更新库存单据",
        "stock.delete": "删除库存单据",
        "stock.audit": "审核库存单据",
        "stock.in": "入库操作",
        "stock.out": "出库操作",
        "stock.return": "退货操作",
        "stock.check": "库存盘点",
        "stock.transfer": "门店调拨",
        "stock.recycle": "回收管理",

        # 系统管理权限
        "system.dashboard": "仪表板",
        "system.export": "数据导出",
        "system.import": "数据导入",
        "system.backup": "数据备份",

        # 超级管理员权限
        "super.admin": "超级管理员"
    }

    @staticmethod
    def check_permission(user_permissions: list, required_permission: str) -> bool:
        """检查权限"""
        # 超级管理员拥有所有权限
        if "super.admin" in user_permissions:
            return True

        return required_permission in user_permissions

    @staticmethod
    def get_default_permissions(role: str = "staff") -> list:
        """获取默认权限"""
        if role == "super_admin":
            return ["super.admin"]
        elif role == "admin":
            return [
                "jewelry.view", "jewelry.create", "jewelry.update",
                "store.view", "member.view", "member.create", "member.update",
                "stock.view", "stock.create", "stock.update", "stock.delete", "stock.audit",
                "stock.in", "stock.out", "stock.return",
                "system.dashboard"
            ]
        elif role == "manager":
            return [
                "jewelry.view", "jewelry.create", "jewelry.update",
                "member.view", "member.create", "member.update",
                "stock.view", "stock.create", "stock.update", "stock.audit",
                "stock.in", "stock.out",
                "system.dashboard"
            ]
        else:  # staff
            return [
                "store.view", "jewelry.view", "member.view",
                "stock.view", "system.dashboard"
            ]


# 创建全局实例
jwt_manager = JWTManager()
password_manager = FastAdminPasswordManager()
permission_manager = PermissionManager()
