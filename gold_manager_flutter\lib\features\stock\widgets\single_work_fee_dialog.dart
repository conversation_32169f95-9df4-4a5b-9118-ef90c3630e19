import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/utils/logger.dart';
import '../../../core/constants/border_styles.dart';

/// 单收工费对话框
class SingleWorkFeeDialog extends StatefulWidget {
  final Function(Map<String, dynamic>) onConfirm;

  const SingleWorkFeeDialog({
    super.key,
    required this.onConfirm,
  });

  @override
  State<SingleWorkFeeDialog> createState() => _SingleWorkFeeDialogState();
}

class _SingleWorkFeeDialogState extends State<SingleWorkFeeDialog> {
  // TextEditingController
  late final TextEditingController nameController;
  late final TextEditingController goldWeightController;
  late final TextEditingController silverWeightController;
  late final TextEditingController workPriceController;
  late final TextEditingController discountController;

  // 响应式变量
  final RxString chargeType = 'per_gram'.obs; // 'per_gram' 或 'per_piece'
  final RxDouble calculatedAmount = 0.0.obs;
  final RxString generatedBarcode = ''.obs;

  // 控制器是否已被dispose的标志
  bool _controllersDisposed = false;

  @override
  void initState() {
    super.initState();
    
    // 初始化控制器
    nameController = TextEditingController();
    goldWeightController = TextEditingController(text: '0');
    silverWeightController = TextEditingController(text: '0');
    workPriceController = TextEditingController(text: '0');
    discountController = TextEditingController(text: '100');

    // 添加监听器
    _setupListeners();

    // 生成条码
    _generateWorkFeeBarcode();

    // 初始计算
    _calculateAmount();
  }

  @override
  void dispose() {
    _safeDisposeControllers();
    super.dispose();
  }

  /// 安全dispose控制器
  void _safeDisposeControllers() {
    if (!_controllersDisposed) {
      _controllersDisposed = true;
      
      // 移除监听器
      goldWeightController.removeListener(_calculateAmount);
      silverWeightController.removeListener(_calculateAmount);
      workPriceController.removeListener(_calculateAmount);
      discountController.removeListener(_calculateAmount);

      // dispose控制器
      nameController.dispose();
      goldWeightController.dispose();
      silverWeightController.dispose();
      workPriceController.dispose();
      discountController.dispose();
    }
  }

  /// 设置监听器
  void _setupListeners() {
    goldWeightController.addListener(_calculateAmount);
    silverWeightController.addListener(_calculateAmount);
    workPriceController.addListener(_calculateAmount);
    discountController.addListener(_calculateAmount);
    chargeType.listen((_) => _calculateAmount());
  }

  /// 计算总金额
  void _calculateAmount() {
    if (_controllersDisposed) return;

    try {
      final goldWeight = double.tryParse(goldWeightController.text) ?? 0.0;
      final silverWeight = double.tryParse(silverWeightController.text) ?? 0.0;
      final workPrice = double.tryParse(workPriceController.text) ?? 0.0;
      final discount = double.tryParse(discountController.text) ?? 100.0;

      double amount = 0.0;
      if (chargeType.value == 'per_gram') {
        // 按克收费：总金额 = (金重 + 银重) × 工费 × 折扣 / 100
        amount = (goldWeight + silverWeight) * workPrice * discount / 100;
      } else {
        // 按件收费：总金额 = 工费 × 折扣 / 100
        amount = workPrice * discount / 100;
      }

      calculatedAmount.value = amount;
    } catch (e) {
      LoggerService.e('❌ 计算工费金额失败', e);
      calculatedAmount.value = 0.0;
    }
  }

  /// 生成工费条码
  Future<void> _generateWorkFeeBarcode() async {
    try {
      final now = DateTime.now();
      final dateStr = '${now.year.toString().substring(2)}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}';
      
      // 简单的序号生成，实际项目中应该查询数据库
      final sequence = DateTime.now().millisecondsSinceEpoch % 1000;
      final barcode = 'GF$dateStr${sequence.toString().padLeft(3, '0')}';
      
      generatedBarcode.value = barcode;
    } catch (e) {
      LoggerService.e('❌ 生成工费条码失败', e);
      generatedBarcode.value = 'GF${DateTime.now().millisecondsSinceEpoch}';
    }
  }

  /// 处理确定按钮点击
  void _handleConfirm() {
    // 验证必填字段
    if (nameController.text.trim().isEmpty) {
      Get.snackbar(
        '提示',
        '请输入商品名称',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      return;
    }

    if (calculatedAmount.value <= 0) {
      Get.snackbar(
        '提示',
        '工费金额必须大于0',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      return;
    }

    // 准备数据
    final workFeeData = {
      'barcode': generatedBarcode.value,
      'name': nameController.text.trim(),
      'chargeType': chargeType.value,
      'goldWeight': double.tryParse(goldWeightController.text) ?? 0.0,
      'silverWeight': double.tryParse(silverWeightController.text) ?? 0.0,
      'workPrice': double.tryParse(workPriceController.text) ?? 0.0,
      'discount': double.tryParse(discountController.text) ?? 100.0,
      'amount': calculatedAmount.value,
    };

    // 关闭对话框
    Navigator.of(context).pop();

    // 调用回调函数
    widget.onConfirm(workFeeData);
  }

  /// 处理取消按钮点击
  void _handleCancel() {
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: Colors.white,
      title: const Text(
        '单收工费',
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: Colors.black87,
        ),
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppBorderStyles.mediumBorderRadius),
      ),
      content: SizedBox(
        width: 500,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 商品条码（自动生成）
              _buildDialogField(
                label: '商品条码',
                child: Obx(() => Container(
                  height: 32,
                  decoration: AppBorderStyles.standardBoxDecoration.copyWith(
                    color: Colors.grey[50],
                  ),
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                  alignment: Alignment.centerLeft,
                  child: Text(
                    generatedBarcode.value,
                    style: const TextStyle(fontSize: 13, color: Colors.grey),
                  ),
                )),
              ),

              const SizedBox(height: 12),

              // 商品名称 - 按照文档标准32px高度输入框模板
              _buildDialogField(
                label: '商品名称',
                child: Container(
                  height: 32,
                  decoration: AppBorderStyles.standardBoxDecoration,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                    child: TextField(
                      controller: nameController,
                      style: const TextStyle(fontSize: 13),
                      decoration: const InputDecoration(
                        hintText: '请输入商品名称',
                        // 完整移除所有边框
                        border: InputBorder.none,
                        enabledBorder: InputBorder.none,
                        focusedBorder: InputBorder.none,
                        disabledBorder: InputBorder.none,
                        // 正确的文字对齐配置
                        contentPadding: EdgeInsets.symmetric(vertical: 8),
                        isDense: true,
                        hintStyle: TextStyle(fontSize: 13, color: Colors.grey),
                      ),
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 12),

              // 收费方式 - 按照文档下拉选择器标准
              _buildDialogField(
                label: '收费方式',
                child: Obx(() => Container(
                  height: 32,
                  decoration: AppBorderStyles.standardBoxDecoration,
                  child: DropdownButtonHideUnderline(
                    child: DropdownButton<String>(
                      value: chargeType.value,
                      isExpanded: true,
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      style: const TextStyle(fontSize: 13, color: Colors.black),
                      items: const [
                        DropdownMenuItem(
                          value: 'per_gram',
                          child: Text(
                            '按克收费',
                            style: TextStyle(fontSize: 13),
                            textAlign: TextAlign.center,
                          ),
                        ),
                        DropdownMenuItem(
                          value: 'per_piece',
                          child: Text(
                            '按件收费',
                            style: TextStyle(fontSize: 13),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ],
                      onChanged: (value) {
                        if (value != null) {
                          chargeType.value = value;
                        }
                      },
                    ),
                  ),
                )),
              ),

              const SizedBox(height: 12),

              // 金重和银重（仅在按克收费时显示）
              Obx(() => chargeType.value == 'per_gram' ? Column(
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: _buildDialogField(
                          label: '金重(g)',
                          child: Container(
                            height: 32,
                            decoration: AppBorderStyles.standardBoxDecoration,
                            child: Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                              child: TextField(
                                controller: goldWeightController,
                                style: const TextStyle(fontSize: 13),
                                textAlign: TextAlign.center,
                                keyboardType: TextInputType.number,
                                decoration: const InputDecoration(
                                  // 完整移除所有边框
                                  border: InputBorder.none,
                                  enabledBorder: InputBorder.none,
                                  focusedBorder: InputBorder.none,
                                  disabledBorder: InputBorder.none,
                                  // 正确的文字对齐配置
                                  contentPadding: EdgeInsets.symmetric(vertical: 8),
                                  isDense: true,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildDialogField(
                          label: '银重(g)',
                          child: Container(
                            height: 32,
                            decoration: AppBorderStyles.standardBoxDecoration,
                            child: Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                              child: TextField(
                                controller: silverWeightController,
                                style: const TextStyle(fontSize: 13),
                                textAlign: TextAlign.center,
                                keyboardType: TextInputType.number,
                                decoration: const InputDecoration(
                                  // 完整移除所有边框
                                  border: InputBorder.none,
                                  enabledBorder: InputBorder.none,
                                  focusedBorder: InputBorder.none,
                                  disabledBorder: InputBorder.none,
                                  // 正确的文字对齐配置
                                  contentPadding: EdgeInsets.symmetric(vertical: 8),
                                  isDense: true,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                ],
              ) : const SizedBox()),

              // 工费和折扣
              Row(
                children: [
                  Expanded(
                    child: Obx(() => _buildDialogField(
                      label: chargeType.value == 'per_gram' ? '工费(元/g)' : '工费(元/件)',
                      child: Container(
                        height: 32,
                        decoration: AppBorderStyles.standardBoxDecoration,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                          child: TextField(
                            controller: workPriceController,
                            style: const TextStyle(fontSize: 13),
                            textAlign: TextAlign.center,
                            keyboardType: TextInputType.number,
                            decoration: const InputDecoration(
                              // 完整移除所有边框
                              border: InputBorder.none,
                              enabledBorder: InputBorder.none,
                              focusedBorder: InputBorder.none,
                              disabledBorder: InputBorder.none,
                              // 正确的文字对齐配置
                              contentPadding: EdgeInsets.symmetric(vertical: 8),
                              isDense: true,
                            ),
                          ),
                        ),
                      ),
                    )),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildDialogField(
                      label: '折扣(%)',
                      child: Container(
                        height: 32,
                        decoration: AppBorderStyles.standardBoxDecoration,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                          child: TextField(
                            controller: discountController,
                            style: const TextStyle(fontSize: 13),
                            textAlign: TextAlign.center,
                            keyboardType: TextInputType.number,
                            decoration: const InputDecoration(
                              // 完整移除所有边框
                              border: InputBorder.none,
                              enabledBorder: InputBorder.none,
                              focusedBorder: InputBorder.none,
                              disabledBorder: InputBorder.none,
                              // 正确的文字对齐配置
                              contentPadding: EdgeInsets.symmetric(vertical: 8),
                              isDense: true,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // 总金额（显示字段）
              _buildDialogField(
                label: '总金额',
                child: Obx(() => Container(
                  height: 32,
                  decoration: AppBorderStyles.standardBoxDecoration.copyWith(
                    color: Colors.green[50],
                  ),
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                  alignment: Alignment.centerLeft,
                  child: Text(
                    '${calculatedAmount.value.round()}元',
                    style: const TextStyle(
                      fontSize: 13,
                      fontWeight: FontWeight.w600,
                      color: Colors.green,
                    ),
                  ),
                )),
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _handleCancel,
          style: TextButton.styleFrom(
            foregroundColor: Colors.grey[600],
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
            ),
          ),
          child: const Text(
            '取消',
            style: TextStyle(fontSize: 13),
          ),
        ),
        const SizedBox(width: 8),
        ElevatedButton(
          onPressed: _handleConfirm,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue[600],
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          ),
          child: const Text(
            '确定',
            style: TextStyle(fontSize: 13, fontWeight: FontWeight.w600),
          ),
        ),
      ],
    );
  }

  /// 构建对话框字段
  Widget _buildDialogField({
    required String label,
    required Widget child,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(bottom: 4),
          child: Text(
            label,
            style: const TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
        ),
        child,
      ],
    );
  }
}
