import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../../core/theme/app_colors.dart';
import '../../controllers/settings_controller.dart';

/// 系统日志Tab页面
class SystemLogTab extends StatelessWidget {
  const SystemLogTab({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<SettingsController>();
    
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildLogHeader(context, controller),
          const SizedBox(height: 16),
          _buildLogFilter(context, controller),
          const SizedBox(height: 16),
          Expanded(
            child: _buildLogList(context, controller),
          ),
        ],
      ),
    );
  }
  
  /// 构建日志头部
  Widget _buildLogHeader(BuildContext context, SettingsController controller) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        const Text(
          '系统操作日志',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        Row(
          children: [
            OutlinedButton.icon(
              onPressed: () {
                _showMessage(context, '导出日志功能即将上线');
              },
              icon: const Icon(Icons.download),
              label: const Text('导出日志'),
            ),
            const SizedBox(width: 16),
            OutlinedButton.icon(
              onPressed: () {
                controller.loadSystemLogs();
              },
              icon: const Icon(Icons.refresh),
              label: const Text('刷新'),
            ),
            const SizedBox(width: 16),
            ElevatedButton.icon(
              onPressed: () {
                _showClearLogsDialog(context, controller);
              },
              icon: const Icon(Icons.delete_sweep),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
              ),
              label: const Text('清除日志'),
            ),
          ],
        ),
      ],
    );
  }
  
  /// 构建日志筛选
  Widget _buildLogFilter(BuildContext context, SettingsController controller) {
    // 日志操作类型
    final actionTypes = [
      {'code': '', 'name': '全部操作'},
      {'code': 'login', 'name': '登录'},
      {'code': 'logout', 'name': '登出'},
      {'code': 'create', 'name': '创建'},
      {'code': 'update', 'name': '更新'},
      {'code': 'delete', 'name': '删除'},
      {'code': 'import', 'name': '导入'},
      {'code': 'export', 'name': '导出'},
      {'code': 'backup', 'name': '备份'},
      {'code': 'restore', 'name': '恢复'},
    ];
    
    // 当前筛选类型
    String actionType = '';
    
    // 日期范围
    DateTime? startDate;
    DateTime? endDate;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: TextField(
                    decoration: const InputDecoration(
                      hintText: '搜索用户名或操作内容',
                      prefixIcon: Icon(Icons.search),
                      border: OutlineInputBorder(),
                      contentPadding: EdgeInsets.symmetric(vertical: 0, horizontal: 16),
                    ),
                    onChanged: (value) {
                      if (value.length > 2 || value.isEmpty) {
                        controller.searchLogs(value);
                      }
                    },
                  ),
                ),
                const SizedBox(width: 16),
                SizedBox(
                  width: 200,
                  child: DropdownButtonFormField<String>(
                    value: actionType,
                    decoration: const InputDecoration(
                      labelText: '操作类型',
                      border: OutlineInputBorder(),
                      contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    ),
                    items: actionTypes
                        .map((action) => DropdownMenuItem<String>(
                              value: action['code'] as String,
                              child: Text(action['name'] as String),
                            ))
                        .toList(),
                    onChanged: (value) {
                      actionType = value ?? '';
                      controller.loadSystemLogs(
                        actionType: actionType.isNotEmpty ? actionType : null,
                        startDate: startDate,
                        endDate: endDate,
                      );
                    },
                  ),
                ),
                const SizedBox(width: 16),
                OutlinedButton.icon(
                  onPressed: () async {
                    final picked = await showDateRangePicker(
                      context: context,
                      firstDate: DateTime(2020),
                      lastDate: DateTime.now(),
                      initialDateRange: DateTimeRange(
                        start: startDate ?? DateTime.now().subtract(const Duration(days: 30)),
                        end: endDate ?? DateTime.now(),
                      ),
                      builder: (context, child) {
                        return Theme(
                          data: Theme.of(context).copyWith(
                            colorScheme: const ColorScheme.light(
                              primary: AppColors.primary,
                              onPrimary: Colors.white,
                              surface: Colors.white,
                              onSurface: Colors.black,
                            ),
                          ),
                          child: child!,
                        );
                      },
                    );
                    
                    if (picked != null) {
                      startDate = picked.start;
                      endDate = picked.end;
                      
                      controller.loadSystemLogs(
                        actionType: actionType.isNotEmpty ? actionType : null,
                        startDate: startDate,
                        endDate: endDate,
                      );
                    }
                  },
                  icon: const Icon(Icons.date_range),
                  label: const Text('日期范围'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  /// 构建日志列表
  Widget _buildLogList(BuildContext context, SettingsController controller) {
    return Obx(() {
      final logs = controller.systemLogs;
      
      if (logs.isEmpty) {
        return const Center(
          child: Text('没有找到日志记录', style: TextStyle(fontSize: 16)),
        );
      }
      
      return Card(
        child: SingleChildScrollView(
          scrollDirection: Axis.vertical,
          child: DataTable(
            headingTextStyle: const TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
            dataTextStyle: const TextStyle(
              color: Colors.black87,
            ),
            columnSpacing: 24,
            columns: const [
              DataColumn(label: Text('时间')),
              DataColumn(label: Text('用户')),
              DataColumn(label: Text('操作类型')),
              DataColumn(label: Text('操作内容')),
              DataColumn(label: Text('IP地址')),
              DataColumn(label: Text('结果')),
            ],
            rows: logs.map((log) {
              return DataRow(
                cells: [
                  DataCell(Text(DateFormat('yyyy-MM-dd HH:mm:ss').format(log.createdAt))),
                  DataCell(Text(log.username)),
                  DataCell(Text(_getActionTypeName(log.actionType))),
                  DataCell(Text(log.content)),
                  DataCell(Text(log.ipAddress)),
                  DataCell(_buildResultBadge(log.result)),
                ],
              );
            }).toList(),
          ),
        ),
      );
    });
  }
  
  /// 获取操作类型名称
  String _getActionTypeName(String actionType) {
    switch (actionType) {
      case 'login':
        return '登录';
      case 'logout':
        return '登出';
      case 'create':
        return '创建';
      case 'update':
        return '更新';
      case 'delete':
        return '删除';
      case 'import':
        return '导入';
      case 'export':
        return '导出';
      case 'backup':
        return '备份';
      case 'restore':
        return '恢复';
      default:
        return actionType;
    }
  }
  
  /// 构建结果标签
  Widget _buildResultBadge(bool result) {
    final color = result ? Colors.green : Colors.red;
    final text = result ? '成功' : '失败';
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontWeight: FontWeight.bold,
          fontSize: 12,
        ),
      ),
    );
  }
  
  /// 显示清除日志对话框
  void _showClearLogsDialog(BuildContext context, SettingsController controller) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('清除日志'),
        content: const Text('确定要清除所有系统日志吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            onPressed: () async {
              Navigator.pop(context);
              
              final success = await controller.clearSystemLogs();
              
              if (success) {
                _showMessage(context, '系统日志已成功清除');
              } else {
                _showMessage(context, '清除系统日志失败，请稍后重试');
              }
            },
            child: const Text('确定清除'),
          ),
        ],
      ),
    );
  }
  
  /// 显示消息提示
  void _showMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
} 