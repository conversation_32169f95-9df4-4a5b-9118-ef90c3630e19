import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../utils/logger_service.dart';

/// 基础控制器类
/// 提供通用的状态管理和错误处理功能
abstract class BaseController extends GetxController {
  // 通用加载状态
  final RxBool isLoading = false.obs;
  
  // 错误信息
  final RxString errorMessage = ''.obs;
  
  // 成功信息
  final RxString successMessage = ''.obs;

  /// 通用错误处理
  void handleError(dynamic error, [String? context]) {
    isLoading.value = false;
    
    String errorText = _formatError(error);
    errorMessage.value = errorText;
    
    LoggerService.e(context ?? 'Controller Error', error);
    
    // 显示错误提示
    _showErrorSnackbar(errorText);
  }

  /// 格式化错误信息
  String _formatError(dynamic error) {
    if (error is String) {
      return error;
    } else if (error is Exception) {
      return error.toString().replaceFirst('Exception: ', '');
    } else {
      return '操作失败，请重试';
    }
  }

  /// 显示错误提示
  void _showErrorSnackbar(String message) {
    if (Get.isSnackbarOpen) {
      Get.closeCurrentSnackbar();
    }
    
    Get.snackbar(
      '错误',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.red.withOpacity(0.9),
      colorText: Colors.white,
      icon: const Icon(Icons.error, color: Colors.white),
      duration: const Duration(seconds: 3),
      margin: const EdgeInsets.all(16),
      borderRadius: 8,
    );
  }

  /// 显示成功提示
  void showSuccess(String message) {
    successMessage.value = message;
    
    if (Get.isSnackbarOpen) {
      Get.closeCurrentSnackbar();
    }
    
    Get.snackbar(
      '成功',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.green.withOpacity(0.9),
      colorText: Colors.white,
      icon: const Icon(Icons.check_circle, color: Colors.white),
      duration: const Duration(seconds: 2),
      margin: const EdgeInsets.all(16),
      borderRadius: 8,
    );
  }

  /// 显示信息提示
  void showInfo(String message) {
    if (Get.isSnackbarOpen) {
      Get.closeCurrentSnackbar();
    }
    
    Get.snackbar(
      '提示',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.blue.withOpacity(0.9),
      colorText: Colors.white,
      icon: const Icon(Icons.info, color: Colors.white),
      duration: const Duration(seconds: 2),
      margin: const EdgeInsets.all(16),
      borderRadius: 8,
    );
  }

  /// 显示警告提示
  void showWarning(String message) {
    if (Get.isSnackbarOpen) {
      Get.closeCurrentSnackbar();
    }
    
    Get.snackbar(
      '警告',
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.orange.withOpacity(0.9),
      colorText: Colors.white,
      icon: const Icon(Icons.warning, color: Colors.white),
      duration: const Duration(seconds: 3),
      margin: const EdgeInsets.all(16),
      borderRadius: 8,
    );
  }

  /// 统一的加载包装器
  /// 自动处理加载状态和错误
  Future<T?> executeWithLoading<T>(
    Future<T> Function() operation, {
    String? loadingMessage,
    String? successMessage,
    bool showSuccessMessage = false,
  }) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';
      
      final result = await operation();
      
      if (showSuccessMessage && successMessage != null) {
        this.showSuccess(successMessage);
      }
      
      return result;
    } catch (e) {
      handleError(e);
      return null;
    } finally {
      isLoading.value = false;
    }
  }

  /// 安全执行操作（不显示加载状态）
  Future<T?> executeSafely<T>(
    Future<T> Function() operation, {
    String? errorContext,
    bool silent = false,
  }) async {
    try {
      return await operation();
    } catch (e) {
      if (!silent) {
        handleError(e, errorContext);
      } else {
        LoggerService.e(errorContext ?? 'Silent Error', e);
      }
      return null;
    }
  }

  /// 显示确认对话框
  Future<bool> showConfirmDialog({
    required String title,
    required String content,
    String confirmText = '确认',
    String cancelText = '取消',
    Color? confirmColor,
  }) async {
    final result = await Get.dialog<bool>(
      AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: Text(cancelText),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            style: TextButton.styleFrom(
              foregroundColor: confirmColor ?? Colors.red,
            ),
            child: Text(confirmText),
          ),
        ],
      ),
    );
    
    return result ?? false;
  }

  /// 显示加载对话框
  void showLoadingDialog({String? message}) {
    Get.dialog(
      AlertDialog(
        content: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(width: 16),
            Text(message ?? '加载中...'),
          ],
        ),
      ),
      barrierDismissible: false,
    );
  }

  /// 关闭加载对话框
  void hideLoadingDialog() {
    if (Get.isDialogOpen == true) {
      Get.back();
    }
  }

  /// 清除错误和成功消息
  void clearMessages() {
    errorMessage.value = '';
    successMessage.value = '';
  }

  /// 检查网络连接状态
  bool get isConnected {
    // 这里可以集成网络连接检查逻辑
    // 例如使用connectivity_plus包
    return true; // 暂时返回true
  }

  /// 重试操作
  Future<void> retry(Future<void> Function() operation) async {
    if (!isConnected) {
      showWarning('请检查网络连接');
      return;
    }
    
    clearMessages();
    await operation();
  }

  @override
  void onClose() {
    // 清理资源
    clearMessages();
    super.onClose();
  }
}