import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../theme/app_theme.dart';

/// 标签页数据模型
class TabData {
  final String id;
  final String title;
  final IconData icon;
  final Widget content;
  final bool canClose;
  final VoidCallback? onClose;

  TabData({
    required this.id,
    required this.title,
    required this.icon,
    required this.content,
    this.canClose = true,
    this.onClose,
  });
}

/// 标签页管理控制器
class TabManagerController extends GetxController {
  /// 所有打开的标签页
  final RxList<TabData> tabs = <TabData>[].obs;

  /// 当前活动的标签页索引
  final RxInt activeTabIndex = 0.obs;

  /// 防抖计时器，避免快速点击
  Timer? _debounceTimer;

  /// 添加新标签页
  void addTab(TabData tab) {
    // 检查是否已经存在相同ID的标签页
    final existingIndex = tabs.indexWhere((t) => t.id == tab.id);
    if (existingIndex != -1) {
      // 如果已存在，切换到该标签页
      activeTabIndex.value = existingIndex;
      return;
    }

    // 添加新标签页并切换到它
    tabs.add(tab);
    activeTabIndex.value = tabs.length - 1;
  }

  /// 关闭标签页
  void closeTab(int index) {
    if (index < 0 || index >= tabs.length) return;
    if (!tabs[index].canClose) return;

    final tab = tabs[index];

    // 调用关闭回调
    if (tab.onClose != null) {
      tab.onClose!();
    }

    tabs.removeAt(index);

    // 调整活动标签页索引
    if (tabs.isEmpty) {
      activeTabIndex.value = 0;
    } else if (activeTabIndex.value >= tabs.length) {
      activeTabIndex.value = tabs.length - 1;
    } else if (activeTabIndex.value > index) {
      activeTabIndex.value--;
    }
  }

  /// 切换到指定标签页（带防抖）
  void switchToTab(int index) {
    if (index >= 0 && index < tabs.length && index != activeTabIndex.value) {
      // 取消之前的防抖计时器
      _debounceTimer?.cancel();

      // 立即切换，提供即时反馈
      activeTabIndex.value = index;

      // 设置防抖计时器，避免快速点击
      _debounceTimer = Timer(const Duration(milliseconds: 50), () {
        // 防抖完成后的处理（如果需要）
      });
    }
  }

  /// 关闭所有可关闭的标签页
  void closeAllTabs() {
    tabs.removeWhere((tab) => tab.canClose);
    if (tabs.isEmpty) {
      activeTabIndex.value = 0;
    } else {
      activeTabIndex.value = 0;
    }
  }

  /// 关闭除当前标签页外的所有可关闭标签页
  void closeOtherTabs() {
    if (tabs.isEmpty) return;

    final currentTab = tabs[activeTabIndex.value];
    tabs.removeWhere((tab) => tab.canClose && tab.id != currentTab.id);

    // 重新找到当前标签页的索引
    final newIndex = tabs.indexWhere((tab) => tab.id == currentTab.id);
    activeTabIndex.value = newIndex >= 0 ? newIndex : 0;
  }

  /// 获取当前活动的标签页
  TabData? get currentTab {
    if (tabs.isEmpty || activeTabIndex.value >= tabs.length) return null;
    return tabs[activeTabIndex.value];
  }

  @override
  void onClose() {
    _debounceTimer?.cancel();
    super.onClose();
  }
}

/// 标签页管理器组件
class TabManager extends StatelessWidget {
  final TabManagerController controller;
  final double tabHeight;
  final Color? backgroundColor;
  final Color? activeTabColor;
  final Color? inactiveTabColor;

  const TabManager({
    super.key,
    required this.controller,
    this.tabHeight = 40.0,
    this.backgroundColor,
    this.activeTabColor,
    this.inactiveTabColor,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.tabs.isEmpty) {
        return const SizedBox.shrink();
      }

      return Column(
        children: [
          // 标签页头部
          _buildTabHeader(),
          // 标签页内容
          Expanded(
            child: _buildTabContent(),
          ),
        ],
      );
    });
  }

  /// 构建标签页头部
  Widget _buildTabHeader() {
    return Container(
      height: tabHeight,
      color: backgroundColor ?? AppTheme.backgroundColor,
      child: Row(
        children: [
          // 标签页列表
          Expanded(
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: controller.tabs.length,
              itemBuilder: (context, index) => _buildTabItem(index),
            ),
          ),
          // 标签页操作按钮
          _buildTabActions(),
        ],
      ),
    );
  }

  /// 构建单个标签页项
  Widget _buildTabItem(int index) {
    final tab = controller.tabs[index];
    final isActive = controller.activeTabIndex.value == index;

    return GestureDetector(
      onTap: () => controller.switchToTab(index),
      child: Container(
        constraints: const BoxConstraints(
          minWidth: 120,
          maxWidth: 200,
        ),
        decoration: BoxDecoration(
          color: isActive
            ? (activeTabColor ?? Colors.white)
            : (inactiveTabColor ?? Colors.grey[200]),
          border: Border(
            right: BorderSide(color: Colors.grey[300]!, width: 1),
            bottom: isActive
              ? BorderSide.none
              : BorderSide(color: Colors.grey[300]!, width: 1),
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 标签页图标
              Icon(
                tab.icon,
                size: 16,
                color: isActive ? AppTheme.primaryColor : Colors.grey[600],
              ),
              const SizedBox(width: 6),
              // 标签页标题
              Flexible(
                child: Text(
                  tab.title,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
                    color: isActive ? Colors.black87 : Colors.grey[600],
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              // 关闭按钮
              if (tab.canClose) ...[
                const SizedBox(width: 4),
                GestureDetector(
                  onTap: () => controller.closeTab(index),
                  child: Icon(
                    Icons.close,
                    size: 14,
                    color: Colors.grey[500],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// 构建标签页操作按钮
  Widget _buildTabActions() {
    return PopupMenuButton<String>(
      icon: Icon(Icons.more_vert, size: 16, color: Colors.grey[600]),
      itemBuilder: (context) => [
        const PopupMenuItem(
          value: 'close_all',
          child: Row(
            children: [
              Icon(Icons.close_fullscreen, size: 16),
              SizedBox(width: 8),
              Text('关闭所有'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'close_others',
          child: Row(
            children: [
              Icon(Icons.tab_unselected, size: 16),
              SizedBox(width: 8),
              Text('关闭其他'),
            ],
          ),
        ),
      ],
      onSelected: (value) {
        switch (value) {
          case 'close_all':
            controller.closeAllTabs();
            break;
          case 'close_others':
            controller.closeOtherTabs();
            break;
        }
      },
    );
  }

  /// 构建标签页内容
  Widget _buildTabContent() {
    final currentTab = controller.currentTab;
    if (currentTab == null) {
      return const Center(
        child: Text('没有打开的标签页'),
      );
    }

    // 使用IndexedStack来保持所有标签页的状态，避免重建
    return IndexedStack(
      index: controller.activeTabIndex.value,
      children: controller.tabs.map((tab) => tab.content).toList(),
    );
  }
}
