import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/border_styles.dart';
import '../../../core/utils/logger.dart';
import '../../../core/utils/date_formatter.dart';
import '../models/inventory_check.dart';
import '../services/inventory_check_service.dart';

/// 查看盘点单详情对话框
/// 
/// 显示盘点单基本信息和关联的库存明细
/// 支持只读模式浏览
class InventoryCheckDetailDialog extends StatefulWidget {
  final InventoryCheck inventoryCheck;

  const InventoryCheckDetailDialog({
    super.key,
    required this.inventoryCheck,
  });

  @override
  State<InventoryCheckDetailDialog> createState() => _InventoryCheckDetailDialogState();
}

class _InventoryCheckDetailDialogState extends State<InventoryCheckDetailDialog> {
  final _inventoryCheckService = Get.find<InventoryCheckService>();
  
  List<InventoryCheckItem> _items = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadInventoryCheckItems();
  }

  /// 加载盘点明细
  Future<void> _loadInventoryCheckItems() async {
    try {
      setState(() => _isLoading = true);
      
      final items = await _inventoryCheckService.getInventoryCheckItems(
        widget.inventoryCheck.id,
      );
      
      setState(() {
        _items = items;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      LoggerService.e('加载盘点明细失败', e);
      Get.snackbar('错误', '加载盘点明细失败: ${e.toString()}');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppBorderStyles.mediumBorderRadius),
      ),
      child: Container(
        width: 900,
        height: 700,
        decoration: AppBorderStyles.standardBoxDecoration.copyWith(
          borderRadius: BorderRadius.circular(AppBorderStyles.mediumBorderRadius),
        ),
        child: Column(
          children: [
            _buildHeader(),
            Expanded(child: _buildContent()),
            _buildActions(),
          ],
        ),
      ),
    );
  }

  /// 构建对话框头部
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(AppBorderStyles.mediumBorderRadius),
          topRight: Radius.circular(AppBorderStyles.mediumBorderRadius),
        ),
        border: const Border(
          bottom: AppBorderStyles.tableBorder,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.visibility,
            color: Colors.blue[600],
            size: 24,
          ),
          const SizedBox(width: 12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '盘点单详情',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              Text(
                '单号: ${widget.inventoryCheck.checkNo}',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          const Spacer(),
          _buildStatusBadge(),
          const SizedBox(width: 12),
          IconButton(
            onPressed: () => Get.back(),
            icon: const Icon(Icons.close, size: 20),
            style: IconButton.styleFrom(
              backgroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建状态徽章
  Widget _buildStatusBadge() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: widget.inventoryCheck.statusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
        border: Border.all(
          color: widget.inventoryCheck.statusColor,
          width: 1,
        ),
      ),
      child: Text(
        widget.inventoryCheck.statusText,
        style: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: widget.inventoryCheck.statusColor,
        ),
      ),
    );
  }

  /// 构建对话框内容
  Widget _buildContent() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          _buildBasicInfo(),
          const SizedBox(height: 20),
          _buildProgressInfo(),
          const SizedBox(height: 20),
          Expanded(child: _buildItemsList()),
        ],
      ),
    );
  }

  /// 构建基本信息
  Widget _buildBasicInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: AppBorderStyles.standardBoxDecoration.copyWith(
        color: Colors.grey[50],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '基本信息',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildInfoItem('盘点门店', widget.inventoryCheck.storeName ?? '-'),
              ),
              Expanded(
                child: _buildInfoItem('操作员', widget.inventoryCheck.operatorName ?? '-'),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  '开始时间',
                  widget.inventoryCheck.startTime != null
                      ? DateFormatter.formatDateTime(widget.inventoryCheck.startTime!)
                      : '-',
                ),
              ),
              Expanded(
                child: _buildInfoItem(
                  '结束时间',
                  widget.inventoryCheck.endTime != null
                      ? DateFormatter.formatDateTime(widget.inventoryCheck.endTime!)
                      : '-',
                ),
              ),
            ],
          ),
          if (widget.inventoryCheck.remark != null && widget.inventoryCheck.remark!.isNotEmpty) ...[
            const SizedBox(height: 8),
            _buildInfoItem('备注', widget.inventoryCheck.remark!),
          ],
        ],
      ),
    );
  }

  /// 构建信息项
  Widget _buildInfoItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建进度信息
  Widget _buildProgressInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: AppBorderStyles.standardBoxDecoration.copyWith(
        color: Colors.blue[50],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '盘点进度',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildProgressItem(
                  '应盘总数',
                  widget.inventoryCheck.totalCount.toString(),
                  Icons.inventory_2,
                  Colors.blue,
                ),
              ),
              Expanded(
                child: _buildProgressItem(
                  '已盘数量',
                  widget.inventoryCheck.checkedCount.toString(),
                  Icons.check_circle,
                  Colors.green,
                ),
              ),
              Expanded(
                child: _buildProgressItem(
                  '差异数量',
                  widget.inventoryCheck.differenceCount.toString(),
                  Icons.warning,
                  Colors.orange,
                ),
              ),
              Expanded(
                child: _buildProgressItem(
                  '完成进度',
                  '${(widget.inventoryCheck.progress * 100).toStringAsFixed(1)}%',
                  Icons.trending_up,
                  Colors.purple,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          LinearProgressIndicator(
            value: widget.inventoryCheck.progress,
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(Colors.blue[600]!),
          ),
        ],
      ),
    );
  }

  /// 构建进度项
  Widget _buildProgressItem(String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, size: 24, color: color),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  /// 构建明细列表
  Widget _buildItemsList() {
    return Container(
      decoration: AppBorderStyles.standardBoxDecoration,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              border: const Border(
                bottom: AppBorderStyles.tableBorder,
              ),
            ),
            child: Row(
              children: [
                const Text(
                  '盘点明细',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const Spacer(),
                Text(
                  '共 ${_items.length} 件商品',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _items.isEmpty
                    ? const Center(
                        child: Text(
                          '暂无盘点明细',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey,
                          ),
                        ),
                      )
                    : _buildItemsTable(),
          ),
        ],
      ),
    );
  }

  /// 构建明细表格
  Widget _buildItemsTable() {
    return SingleChildScrollView(
      child: DataTable(
        border: AppBorderStyles.tableStandardBorder,
        headingRowColor: WidgetStateProperty.all(AppBorderStyles.tableHeaderBackground),
        columns: const [
          DataColumn(label: Text('状态')),
          DataColumn(label: Text('条码')),
          DataColumn(label: Text('商品名称')),
          DataColumn(label: Text('分类')),
          DataColumn(label: Text('圈口号')),
          DataColumn(label: Text('系统库存')),
          DataColumn(label: Text('实际库存')),
          DataColumn(label: Text('差异')),
          DataColumn(label: Text('盘点时间')),
        ],
        rows: _items.map((item) {
          return DataRow(
            cells: [
              DataCell(
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: item.statusColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
                    border: Border.all(color: item.statusColor, width: 1),
                  ),
                  child: Text(
                    item.statusText,
                    style: TextStyle(
                      fontSize: 12,
                      color: item.statusColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
              DataCell(Text(item.barcode)),
              DataCell(Text(item.name)),
              DataCell(Text(item.categoryName ?? '-')),
              DataCell(Text(item.ringSize ?? '-')),
              DataCell(Text(item.systemStock.toString())),
              DataCell(Text(item.actualStock?.toString() ?? '-')),
              DataCell(
                Text(
                  item.differenceText,
                  style: TextStyle(
                    color: item.hasDifference ? Colors.red : Colors.green,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              DataCell(
                Text(
                  item.checkTime != null
                      ? DateFormatter.formatDateTime(item.checkTime!)
                      : '-',
                ),
              ),
            ],
          );
        }).toList(),
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActions() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        border: Border(
          top: AppBorderStyles.tableBorder,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          SizedBox(
            height: 36,
            child: ElevatedButton(
              onPressed: () => Get.back(),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue[600],
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
                ),
              ),
              child: const Text('关闭'),
            ),
          ),
        ],
      ),
    );
  }
}
