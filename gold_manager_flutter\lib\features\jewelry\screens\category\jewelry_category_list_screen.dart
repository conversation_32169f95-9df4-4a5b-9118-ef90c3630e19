import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../models/jewelry_category.dart';
import '../../../../services/jewelry_category_service.dart';
import '../../controllers/jewelry_category_controller.dart';
import 'jewelry_category_form_screen.dart';
import '../../../../core/widgets/empty_state.dart';
import '../../../../widgets/loading_state.dart';
import '../../../../core/widgets/error_state.dart';

/// 首饰分类列表页面
class JewelryCategoryListScreen extends StatelessWidget {
  const JewelryCategoryListScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // 初始化控制器
    final controller = Get.put(JewelryCategoryController(
      categoryService: Get.find<JewelryCategoryService>(),
    ));

    return Scaffold(
      appBar: AppBar(
        title: const Text('首饰分类管理'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: controller.fetchCategories,
            tooltip: '刷新',
          ),
        ],
      ),
      body: _buildBody(controller),
      // 移除FloatingActionButton，使用界面内的具体操作按钮
    );
  }

  /// 构建主体内容
  Widget _buildBody(JewelryCategoryController controller) {
    return Obx(() {
      // 加载状态
      if (controller.isLoading.value) {
        return const LoadingState(text: '正在加载分类数据...', timeoutSeconds: 30);
      }

      // 错误状态
      if (controller.hasError.value) {
        return ErrorState(
          message: '加载失败: ${controller.errorMessage.value}',
          onRetry: controller.fetchCategories,
        );
      }

      // 空状态
      if (controller.categories.isEmpty) {
        return EmptyState(
          icon: Icons.category,
          title: '没有找到首饰分类',
          message: '点击下方按钮添加首个分类',
          buttonText: '添加分类',
          onButtonPressed: () => Get.to(() => const JewelryCategoryFormScreen()),
        );
      }

      // 数据列表
      return _buildCategoryList(controller);
    });
  }

  /// 构建分类列表
  Widget _buildCategoryList(JewelryCategoryController controller) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '共 ${controller.categories.length} 个分类',
            style: TextStyle(
              color: Get.theme.colorScheme.secondary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: _buildCategoryGrid(controller),
          ),
        ],
      ),
    );
  }

  /// 构建分类网格
  Widget _buildCategoryGrid(JewelryCategoryController controller) {
    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithMaxCrossAxisExtent(
        maxCrossAxisExtent: 300,
        childAspectRatio: 2,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: controller.categories.length,
      itemBuilder: (context, index) {
        final category = controller.categories[index];
        return _buildCategoryCard(context, category, controller);
      },
    );
  }

  /// 构建分类卡片
  Widget _buildCategoryCard(
    BuildContext context,
    JewelryCategory category,
    JewelryCategoryController controller,
  ) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () => Get.to(
          () => JewelryCategoryFormScreen(categoryId: category.id),
        ),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: category.color ?? Theme.of(context).primaryColor,
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Row(
                children: [
                  Container(
                    width: 16,
                    height: 16,
                    decoration: BoxDecoration(
                      color: category.color ?? Theme.of(context).primaryColor,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      category.name,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  if (!category.isActive)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade200,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Text(
                        '禁用',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                '编码: ${category.code}',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade700,
                ),
              ),
              if (category.description != null && category.description!.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(top: 4),
                  child: Text(
                    category.description!,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}