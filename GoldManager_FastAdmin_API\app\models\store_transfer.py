"""
门店调拨数据模型

老板，这个模块定义了门店调拨相关的数据模型，包括：
1. StoreTransfer - 门店调拨主表，对应 fa_store_transfer
2. StoreTransferItem - 门店调拨明细表，对应 fa_store_transfer_item

完全兼容FastAdmin数据库结构，支持门店间商品调拨业务。
"""

from sqlalchemy import Column, Integer, String, Text, DECIMAL, ForeignKey, SMALLINT
from sqlalchemy.dialects.mysql import INTEGER
from sqlalchemy.orm import relationship
from ..core.database import Base


class StoreTransfer(Base):
    """门店调拨主表 - 对应 fa_store_transfer"""
    __tablename__ = "fa_store_transfer"

    id = Column(INTEGER(unsigned=True), primary_key=True, index=True, comment="ID")
    transfer_no = Column(String(50), unique=True, nullable=False, comment="调拨单号")
    from_store_id = Column(INTEGER(unsigned=True), ForeignKey("fa_store.id"), nullable=False, comment="源店铺ID")
    to_store_id = Column(INTEGER(unsigned=True), ForeignKey("fa_store.id"), nullable=False, comment="目标店铺ID")
    admin_id = Column(INTEGER(unsigned=True), ForeignKey("fa_admin.id"), nullable=False, comment="操作员ID")
    status = Column(SMALLINT, default=0, comment="状态:0=待审核,1=已通过,2=已拒绝")
    audit_id = Column(INTEGER(unsigned=True), ForeignKey("fa_admin.id"), comment="审核员ID")
    audit_time = Column(INTEGER(unsigned=True), comment="审核时间")
    remark = Column(String(255), comment="备注")

    # 收款相关字段
    payment_status = Column(SMALLINT, default=0, comment="收款状态:0=未收款,1=已收款")
    payment_time = Column(INTEGER(unsigned=True), comment="收款时间")
    payment_method = Column(String(20), comment="收款方式")
    payment_remark = Column(String(255), comment="支付备注")
    cash_amount = Column(DECIMAL(10, 2), default=0.00, comment="现金金额")
    wechat_amount = Column(DECIMAL(10, 2), default=0.00, comment="微信金额")
    alipay_amount = Column(DECIMAL(10, 2), default=0.00, comment="支付宝金额")
    card_amount = Column(DECIMAL(10, 2), default=0.00, comment="刷卡金额")
    discount_amount = Column(DECIMAL(10, 2), default=0.00, comment="抹零金额")
    actual_amount = Column(DECIMAL(10, 2), default=0.00, comment="实收金额")

    createtime = Column(INTEGER(unsigned=True), comment="创建时间")

    # 关联关系
    from_store = relationship("Store", foreign_keys=[from_store_id], back_populates="from_transfers")
    to_store = relationship("Store", foreign_keys=[to_store_id], back_populates="to_transfers")
    admin = relationship("Admin", foreign_keys=[admin_id], back_populates="transfers")
    auditor = relationship("Admin", foreign_keys=[audit_id], back_populates="audited_transfers")
    items = relationship("StoreTransferItem", back_populates="transfer", cascade="all, delete-orphan")


class StoreTransferItem(Base):
    """门店调拨明细表 - 对应 fa_store_transfer_item"""
    __tablename__ = "fa_store_transfer_item"

    id = Column(INTEGER(unsigned=True), primary_key=True, index=True, comment="ID")
    transfer_id = Column(INTEGER(unsigned=True), ForeignKey("fa_store_transfer.id"), nullable=False, comment="调拨单ID")
    jewelry_id = Column(INTEGER(unsigned=True), ForeignKey("fa_jewelry.id"), nullable=False, comment="商品ID")
    transfer_price = Column(DECIMAL(10, 2), nullable=False, comment="调拨价格")
    gold_price = Column(DECIMAL(10, 2), nullable=False, default=0.00, comment="金价")
    silver_price = Column(DECIMAL(10, 2), nullable=False, default=0.00, comment="银价")
    total_weight = Column(DECIMAL(10, 2), nullable=False, default=0.00, comment="总重")
    silver_work_price = Column(DECIMAL(10, 2), nullable=False, default=0.00, comment="工费")
    piece_work_price = Column(DECIMAL(10, 2), nullable=False, default=0.00, comment="单件工费")
    original_data = Column(Text, comment="原始数据JSON")
    createtime = Column(INTEGER(unsigned=True), comment="创建时间")

    # 关联关系
    transfer = relationship("StoreTransfer", back_populates="items")
    jewelry = relationship("Jewelry", back_populates="transfer_items")
