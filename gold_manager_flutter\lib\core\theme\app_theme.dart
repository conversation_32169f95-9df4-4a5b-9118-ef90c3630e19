import 'package:flutter/material.dart';

/// 应用主题定义
/// 基于UI设计规范文档
class AppTheme {
  // 防止实例化
  AppTheme._();

  // 颜色定义

  /// 主色调 - 品牌主色调，用于主按钮、强调元素、重要信息等
  static const Color primaryColor = Color(0xFF1E88E5);

  /// 次要色调 - 用于次要按钮、图标、次要信息等
  static const Color secondaryColor = Color(0xFF26A69A);

  /// 背景色 - 全局背景色
  static const Color backgroundColor = Color(0xFFF5F5F5);

  /// 卡片背景色 - 卡片和面板背景色
  static const Color cardBackgroundColor = Color(0xFFFFFFFF);

  // 功能色

  /// 错误色 - 错误提示、警告信息
  static const Color errorColor = Color(0xFFD32F2F);

  /// 成功色 - 成功提示、完成状态
  static const Color successColor = Color(0xFF388E3C);

  /// 警告色 - 警告提示、注意信息
  static const Color warningColor = Color(0xFFFFA000);

  /// 信息色 - 普通信息提示
  static const Color infoColor = Color(0xFF2196F3);

  // 文本颜色

  /// 主要文本色 - 标题、正文等主要文本
  static const Color primaryTextColor = Color(0xFF212121);

  /// 次要文本色 - 说明文字、次要信息
  static const Color secondaryTextColor = Color(0xFF757575);

  /// 禁用文本色 - 禁用状态的文本
  static const Color disabledTextColor = Color(0xFFBDBDBD);

  /// 反色文本 - 深色背景上的文本
  static const Color reverseTextColor = Color(0xFFFFFFFF);

  // 字体大小

  /// 大标题 - 28px, Bold
  static const double headingLargeSize = 28.0;

  /// 中标题 - 24px, Bold
  static const double headingMediumSize = 24.0;

  /// 小标题 - 20px, SemiBold
  static const double headingSmallSize = 20.0;

  /// 正文 - 16px, Regular
  static const double bodySize = 16.0;

  /// 次要文本 - 14px, Regular
  static const double captionSize = 14.0;

  /// 小文本 - 12px, Regular
  static const double smallSize = 12.0;

  // 圆角大小

  /// 按钮、卡片等组件的标准圆角值
  static const double borderRadius = 12.0;

  /// 小组件的圆角值
  static const double borderRadiusSmall = 8.0;

  /// 大组件的圆角值
  static const double borderRadiusLarge = 16.0;

  /// 圆形按钮的圆角值
  static const double borderRadiusCircular = 24.0;

  // 间距和边距

  /// 组件内部间距 - 常规
  static const double padding = 16.0;

  /// 组件内部间距 - 小
  static const double paddingSmall = 8.0;

  /// 组件内部间距 - 中
  static const double paddingMedium = 12.0;

  /// 组件内部间距 - 大
  static const double paddingLarge = 24.0;

  /// 组件内部间距 - 超大
  static const double paddingXLarge = 32.0;

  // 阴影

  /// 卡片默认阴影
  static List<BoxShadow> get cardShadow => [
    const BoxShadow(
      color: Color(0x1A000000),
      blurRadius: 4,
      offset: Offset(0, 2),
    ),
  ];

  /// 浮动按钮和重要卡片阴影
  static List<BoxShadow> get elevatedShadow => [
    const BoxShadow(
      color: Color(0x33000000),
      blurRadius: 8,
      offset: Offset(0, 4),
    ),
  ];

  // 动画时长

  /// 标准动画持续时间
  static const Duration animationDuration = Duration(milliseconds: 300);

  /// 快速动画持续时间
  static const Duration animationDurationFast = Duration(milliseconds: 150);

  /// 慢速动画持续时间
  static const Duration animationDurationSlow = Duration(milliseconds: 500);

  // 应用主题数据

  /// 亮色主题
  static ThemeData get lightTheme {
    return ThemeData(
      primaryColor: primaryColor,
      scaffoldBackgroundColor: backgroundColor,
      cardColor: cardBackgroundColor,
      canvasColor: cardBackgroundColor,
      colorScheme: const ColorScheme.light(
        primary: primaryColor,
        secondary: secondaryColor,
        error: errorColor,
        surface: cardBackgroundColor,
        onPrimary: reverseTextColor,
        onSecondary: reverseTextColor,
        onError: reverseTextColor,
        onSurface: primaryTextColor,
      ),
      textTheme: const TextTheme(
        displayLarge: TextStyle(
          fontSize: headingLargeSize,
          fontWeight: FontWeight.bold,
          color: primaryTextColor,
          height: 1.5,
        ),
        displayMedium: TextStyle(
          fontSize: headingMediumSize,
          fontWeight: FontWeight.bold,
          color: primaryTextColor,
          height: 1.5,
        ),
        displaySmall: TextStyle(
          fontSize: headingSmallSize,
          fontWeight: FontWeight.w600,
          color: primaryTextColor,
          height: 1.5,
        ),
        bodyLarge: TextStyle(
          fontSize: bodySize,
          fontWeight: FontWeight.normal,
          color: primaryTextColor,
          height: 1.5,
        ),
        bodyMedium: TextStyle(
          fontSize: captionSize,
          fontWeight: FontWeight.normal,
          color: secondaryTextColor,
          height: 1.5,
        ),
        bodySmall: TextStyle(
          fontSize: smallSize,
          fontWeight: FontWeight.normal,
          color: secondaryTextColor,
          height: 1.5,
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          foregroundColor: reverseTextColor,
          padding: const EdgeInsets.symmetric(
            vertical: paddingMedium,
            horizontal: padding,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius),
          ),
          minimumSize: const Size(120, 48),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: primaryColor,
          side: const BorderSide(color: primaryColor),
          padding: const EdgeInsets.symmetric(
            vertical: paddingMedium,
            horizontal: padding,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius),
          ),
          minimumSize: const Size(120, 48),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: cardBackgroundColor,
        contentPadding: const EdgeInsets.symmetric(
          vertical: paddingMedium,
          horizontal: padding,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadius),
          borderSide: const BorderSide(color: disabledTextColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadius),
          borderSide: const BorderSide(color: disabledTextColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadius),
          borderSide: const BorderSide(color: primaryColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadius),
          borderSide: const BorderSide(color: errorColor),
        ),
        errorStyle: const TextStyle(color: errorColor),
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: cardBackgroundColor,
        foregroundColor: primaryTextColor,
        elevation: 0,
        centerTitle: false,
        titleTextStyle: TextStyle(
          color: primaryTextColor,
          fontSize: headingSmallSize,
          fontWeight: FontWeight.bold,
        ),
        iconTheme: IconThemeData(color: primaryTextColor),
      ),
      floatingActionButtonTheme: const FloatingActionButtonThemeData(
        backgroundColor: primaryColor,
        foregroundColor: reverseTextColor,
      ),
      dividerTheme: const DividerThemeData(
        color: disabledTextColor,
        thickness: 1,
        space: 1,
      ),
      tabBarTheme: const TabBarThemeData(
        labelColor: primaryColor,
        unselectedLabelColor: secondaryTextColor,
        indicatorSize: TabBarIndicatorSize.tab,
      ),
      dialogTheme: const DialogThemeData(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(borderRadius)),
        ),
        elevation: 4,
      ),
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: cardBackgroundColor,
        selectedItemColor: primaryColor,
        unselectedItemColor: secondaryTextColor,
        type: BottomNavigationBarType.fixed,
      ),
      buttonTheme: ButtonThemeData(
        buttonColor: primaryColor,
        padding: const EdgeInsets.symmetric(
          vertical: paddingMedium,
          horizontal: padding,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadius),
        ),
      ),
      checkboxTheme: CheckboxThemeData(
        fillColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.disabled)) {
            return disabledTextColor;
          }
          if (states.contains(WidgetState.selected)) {
            return primaryColor;
          }
          return null;
        }),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(4),
        ),
      ),
      radioTheme: RadioThemeData(
        fillColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.disabled)) {
            return disabledTextColor;
          }
          if (states.contains(WidgetState.selected)) {
            return primaryColor;
          }
          return null;
        }),
      ),
      switchTheme: SwitchThemeData(
        thumbColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.disabled)) {
            return disabledTextColor;
          }
          if (states.contains(WidgetState.selected)) {
            return primaryColor;
          }
          return null;
        }),
        trackColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.disabled)) {
            return disabledTextColor.withValues(alpha: 0.5);
          }
          if (states.contains(WidgetState.selected)) {
            return primaryColor.withValues(alpha: 0.5);
          }
          return null;
        }),
      ),
      useMaterial3: true,
    );
  }

  /// 暗色主题
  static ThemeData get darkTheme {
    // 暗色主题可以在后续实现
    return lightTheme;
  }
}