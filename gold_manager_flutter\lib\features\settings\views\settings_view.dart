import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/constants/border_styles.dart';
import '../../../core/routes/app_pages.dart';

/// 系统设置页面
/// 
/// 提供系统配置和管理功能的入口
class SettingsView extends StatelessWidget {
  const SettingsView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 页面标题
            _buildPageHeader(),
            const SizedBox(height: 24),
            
            // 设置项列表
            Expanded(
              child: _buildSettingsList(),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建页面头部
  Widget _buildPageHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppBorderStyles.borderColor),
      ),
      child: Row(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: Colors.blue[100],
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              Icons.settings,
              color: Colors.blue[600],
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          const Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '系统设置',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  '管理系统配置、打印模板、用户权限等设置',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.black54,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建设置项列表
  Widget _buildSettingsList() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppBorderStyles.borderColor),
      ),
      child: Column(
        children: [
          // 列表头部
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            child: const Row(
              children: [
                Text(
                  '设置选项',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          ),
          const Divider(height: 1),
          
          // 设置项
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                _buildSettingItem(
                  icon: Icons.print,
                  title: '打印模板管理',
                  subtitle: '管理收款凭证的打印模板，自定义打印格式和内容',
                  onTap: () => Get.toNamed(Routes.PRINT_TEMPLATE),
                ),
                _buildSettingItem(
                  icon: Icons.store,
                  title: '门店管理',
                  subtitle: '管理门店信息、地址、联系方式等基本信息',
                  onTap: () => _showComingSoon('门店管理'),
                ),
                _buildSettingItem(
                  icon: Icons.people,
                  title: '用户管理',
                  subtitle: '管理系统用户、角色权限和访问控制',
                  onTap: () => _showComingSoon('用户管理'),
                ),
                _buildSettingItem(
                  icon: Icons.category,
                  title: '商品分类管理',
                  subtitle: '管理首饰商品的分类体系和属性配置',
                  onTap: () => _showComingSoon('商品分类管理'),
                ),
                _buildSettingItem(
                  icon: Icons.price_change,
                  title: '价格配置',
                  subtitle: '设置金价、银价、工费等价格参数',
                  onTap: () => _showComingSoon('价格配置'),
                ),
                _buildSettingItem(
                  icon: Icons.backup,
                  title: '数据备份',
                  subtitle: '备份和恢复系统数据，确保数据安全',
                  onTap: () => _showComingSoon('数据备份'),
                ),
                _buildSettingItem(
                  icon: Icons.security,
                  title: '安全设置',
                  subtitle: '密码策略、登录安全、操作日志等安全配置',
                  onTap: () => _showComingSoon('安全设置'),
                ),
                _buildSettingItem(
                  icon: Icons.system_update,
                  title: '系统更新',
                  subtitle: '检查系统更新、版本信息和更新历史',
                  onTap: () => _showComingSoon('系统更新'),
                ),
                _buildSettingItem(
                  icon: Icons.info,
                  title: '关于系统',
                  subtitle: '系统版本、开发信息和技术支持',
                  onTap: () => _showAboutDialog(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建单个设置项
  Widget _buildSettingItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return Container(
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: AppBorderStyles.borderColor),
        ),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
        leading: Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: Colors.blue[50],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: Colors.blue[600],
            size: 24,
          ),
        ),
        title: Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        subtitle: Padding(
          padding: const EdgeInsets.only(top: 4),
          child: Text(
            subtitle,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.black54,
            ),
          ),
        ),
        trailing: const Icon(
          Icons.arrow_forward_ios,
          size: 16,
          color: Colors.grey,
        ),
        onTap: onTap,
      ),
    );
  }

  /// 显示即将推出提示
  void _showComingSoon(String feature) {
    Get.snackbar(
      '功能提示',
      '$feature功能正在开发中，敬请期待',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.orange[600],
      colorText: Colors.white,
      duration: const Duration(seconds: 2),
    );
  }

  /// 显示关于对话框
  void _showAboutDialog() {
    Get.dialog(
      AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.info, color: Colors.blue),
            SizedBox(width: 8),
            Text('关于系统'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '金包银首饰管理系统',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 8),
            Text('版本: 1.0.0'),
            SizedBox(height: 4),
            Text('开发: Augment Agent'),
            SizedBox(height: 4),
            Text('技术栈: Flutter + FastAPI'),
            SizedBox(height: 16),
            Text(
              '专业的首饰行业管理解决方案，提供库存管理、销售管理、打印模板等完整功能。',
              style: TextStyle(
                fontSize: 14,
                color: Colors.black54,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
}
