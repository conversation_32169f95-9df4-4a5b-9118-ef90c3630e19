# 库存盘点管理功能设计文档

## 📋 概述

库存盘点管理功能是黄金珠宝管理系统的重要组成部分，用于定期核查实际库存与系统库存的一致性，确保库存数据的准确性。本文档详细描述了盘点功能的UI设计、业务流程、技术实现和API接口设计。

## 🗂️ 数据库表结构分析

### 1. 盘点主表 (fa_inventory_check)
```sql
CREATE TABLE `fa_inventory_check` (
  `id` int(11) NOT NULL,
  `check_no` varchar(50) NOT NULL COMMENT '盘点单号',
  `store_id` int(11) NOT NULL COMMENT '盘点门店',
  `status` tinyint(1) DEFAULT 0 COMMENT '状态:0=进行中,1=已完成,2=已取消',
  `start_time` int(10) DEFAULT NULL COMMENT '开始时间',
  `end_time` int(10) DEFAULT NULL COMMENT '结束时间',
  `operator_id` int(11) NOT NULL COMMENT '操作员ID',
  `total_count` int(11) DEFAULT 0 COMMENT '应盘总数',
  `checked_count` int(11) DEFAULT 0 COMMENT '已盘数量',
  `difference_count` int(11) DEFAULT 0 COMMENT '差异数量',
  `remark` text DEFAULT NULL COMMENT '备注',
  `createtime` int(10) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) DEFAULT NULL COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存盘点主表';
```

### 2. 盘点明细表 (fa_inventory_check_item)
```sql
CREATE TABLE `fa_inventory_check_item` (
  `id` int(11) NOT NULL,
  `check_id` int(11) NOT NULL COMMENT '盘点单ID',
  `jewelry_id` int(11) NOT NULL COMMENT '商品ID',
  `barcode` varchar(50) NOT NULL COMMENT '条码',
  `name` varchar(100) NOT NULL COMMENT '商品名称',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `ring_size` varchar(20) DEFAULT NULL COMMENT '圈口号',
  `status` tinyint(1) DEFAULT 0 COMMENT '状态:0=未盘点,1=已盘点',
  `system_stock` int(11) DEFAULT 1 COMMENT '系统库存',
  `actual_stock` int(11) DEFAULT NULL COMMENT '实际库存',
  `difference` int(11) DEFAULT NULL COMMENT '差异',
  `check_time` int(10) DEFAULT NULL COMMENT '盘点时间',
  `check_user_id` int(11) DEFAULT NULL COMMENT '盘点人员ID',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `createtime` int(10) DEFAULT NULL COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存盘点明细表';
```

## 🔄 业务流程设计

### 1. 盘点流程状态图
```mermaid
graph TD
    A[创建盘点单] --> B[选择盘点商品]
    B --> C[开始盘点]
    C --> D[扫码盘点]
    D --> E{是否盘点完成}
    E -->|否| D
    E -->|是| F[生成盘点报告]
    F --> G[审核差异]
    G --> H[完成盘点]
    
    C --> I[暂停盘点]
    I --> J[继续盘点]
    J --> D
    
    A --> K[取消盘点]
    C --> K
    I --> K
```

### 2. 盘点状态流转
- **进行中(0)**: 盘点单已创建，正在进行盘点
- **已完成(1)**: 盘点完成，差异已处理
- **已取消(2)**: 盘点被取消，数据不生效

### 3. 核心业务规则
1. **权限控制**: 
   - 管理员可查看所有门店盘点单
   - 普通员工只能查看和操作自己门店的盘点单
2. **盘点范围**: 只盘点状态为"上架在售"(status=1)的商品
3. **差异处理**: 系统自动计算差异，支持手动调整
4. **数据一致性**: 盘点完成后更新商品库存状态

## 🎨 UI界面设计

### 1. 主界面布局 (盘点单列表页面)

#### 1.1 页面结构
```
InventoryCheckView
├── _buildFilterSection()          # 筛选区域
│   ├── _buildSearchField()        # 搜索框(盘点单号、备注)
│   ├── _buildStoreSelector()      # 门店选择器
│   ├── _buildStatusSelector()     # 状态选择器
│   ├── _buildDateRangePicker()    # 日期范围选择
│   └── _buildActionButtons()      # 操作按钮(搜索、重置、新建)
├── _buildStatisticsSection()      # 统计信息区域
│   ├── 总盘点单数
│   ├── 进行中数量
│   ├── 已完成数量
│   └── 差异商品总数
├── _buildInventoryCheckList()     # 盘点单列表
│   ├── _buildDataTable()          # 桌面端表格视图
│   └── _buildListView()           # 移动端卡片视图
└── _buildPagination()             # 分页组件
```

#### 1.2 数据表格字段设计
| 序号 | 字段名 | 显示内容 | 宽度比例 | 说明 |
|------|--------|----------|----------|------|
| 1 | 序号 | 自动编号 | 5% | 行号 |
| 2 | 盘点单号 | check_no | 12% | 可点击查看详情 |
| 3 | 门店名称 | store_name | 10% | 关联门店表 |
| 4 | 状态 | status | 8% | 进行中/已完成/已取消 |
| 5 | 应盘数量 | total_count | 8% | 应盘商品总数 |
| 6 | 已盘数量 | checked_count | 8% | 已盘点商品数 |
| 7 | 差异数量 | difference_count | 8% | 有差异的商品数 |
| 8 | 操作员 | operator_name | 10% | 创建人 |
| 9 | 开始时间 | start_time | 12% | 格式化显示 |
| 10 | 结束时间 | end_time | 12% | 格式化显示 |
| 11 | 操作 | actions | 7% | 查看/编辑/删除 |

#### 1.3 UI规范遵循
严格按照 `统一边框调用方式.md` 文档要求：

```dart
// 主容器使用标准边框装饰
Container(
  decoration: AppBorderStyles.standardBoxDecoration,
  child: // 内容
)

// 数据表格使用统一边框
DataTable(
  border: AppBorderStyles.tableStandardBorder,
  headingRowColor: WidgetStateProperty.all(AppBorderStyles.tableHeaderBackground),
)

// 输入框移除外部边框，保留内部边框
TextField(
  decoration: AppBorderStyles.standardInputDecoration.copyWith(
    border: InputBorder.none,
    enabledBorder: InputBorder.none,
    focusedBorder: InputBorder.none,
  ),
)
```

### 2. 盘点详情页面设计

#### 2.1 页面布局
```
InventoryCheckDetailView
├── _buildHeaderSection()          # 盘点单基本信息
│   ├── 盘点单号、门店、状态
│   ├── 创建时间、操作员
│   └── 进度统计
├── _buildProgressSection()        # 盘点进度
│   ├── 进度条显示
│   ├── 已盘/总数统计
│   └── 差异统计
├── _buildScanSection()            # 扫码盘点区域
│   ├── 扫码输入框
│   ├── 手动输入条码
│   └── 盘点结果显示
├── _buildItemsList()              # 盘点明细列表
│   ├── 筛选(已盘/未盘/有差异)
│   ├── 商品明细表格
│   └── 批量操作
└── _buildActionButtons()          # 操作按钮
    ├── 完成盘点
    ├── 暂停盘点
    └── 取消盘点
```

#### 2.2 盘点明细表格字段
| 序号 | 字段名 | 显示内容 | 宽度比例 |
|------|--------|----------|----------|
| 1 | 状态 | 盘点状态图标 | 6% |
| 2 | 条码 | barcode | 15% |
| 3 | 商品名称 | name | 20% |
| 4 | 分类 | category_name | 10% |
| 5 | 圈口号 | ring_size | 8% |
| 6 | 系统库存 | system_stock | 8% |
| 7 | 实际库存 | actual_stock | 8% |
| 8 | 差异 | difference | 8% |
| 9 | 盘点时间 | check_time | 12% |
| 10 | 操作 | actions | 5% |

## 🔐 权限控制设计

### 1. 权限节点定义
```
inventory_check.view    - 查看盘点单
inventory_check.create  - 创建盘点单
inventory_check.update  - 编辑盘点单
inventory_check.delete  - 删除盘点单
inventory_check.audit   - 审核盘点单
```

### 2. 门店级权限控制
```dart
// 前端权限逻辑
if (_authService.userRole.value != 'admin') {
  // 普通员工：只能查看自己门店的盘点单
  selectedStoreId.value = _authService.storeId.value;
  storeDropdownEnabled.value = false;
} else {
  // 管理员：可以选择所有门店
  storeDropdownEnabled.value = true;
}
```

```python
# 后端权限验证
if current_user.store_id and store_id and store_id != current_user.store_id:
    raise HTTPException(status_code=403, detail="无权访问其他门店的盘点数据")
```

## 📱 响应式设计

### 1. 屏幕适配
```dart
ScreenTypeLayout(
  mobile: _buildMobileLayout(),    // 移动端：卡片式布局
  tablet: _buildTabletLayout(),    // 平板端：混合布局
  desktop: _buildDesktopLayout(),  // 桌面端：表格式布局
)
```

### 2. 数据表格响应式
- **桌面端**: 显示完整11列，列宽总计99%
- **平板端**: 隐藏部分次要列，支持横向滚动
- **移动端**: 卡片式布局，显示核心信息

### 3. 统计信息显示
```dart
// 实时统计信息
Container(
  padding: const EdgeInsets.all(16),
  decoration: AppBorderStyles.standardBoxDecoration,
  child: Row(
    mainAxisAlignment: MainAxisAlignment.spaceAround,
    children: [
      _buildStatItem('总盘点单', totalCount.toString()),
      _buildStatItem('进行中', ongoingCount.toString()),
      _buildStatItem('已完成', completedCount.toString()),
      _buildStatItem('差异商品', differenceCount.toString()),
    ],
  ),
)
```

## 🚀 技术实现要点

### 1. 状态管理
使用GetX进行状态管理，实现响应式数据更新：

```dart
class InventoryCheckController extends GetxController {
  // 盘点单列表
  final inventoryChecks = <InventoryCheck>[].obs;
  
  // 筛选条件
  final selectedStoreId = Rxn<int>();
  final selectedStatus = Rxn<int>();
  final searchKeyword = ''.obs;
  
  // 统计信息
  final totalCount = 0.obs;
  final ongoingCount = 0.obs;
  final completedCount = 0.obs;
  final differenceCount = 0.obs;
  
  // 分页信息
  final currentPage = 1.obs;
  final totalPages = 1.obs;
  final pageSize = 20.obs;
}
```

### 2. 数据模型设计
```dart
class InventoryCheck {
  final int id;
  final String checkNo;
  final int storeId;
  final String storeName;
  final int status;
  final DateTime? startTime;
  final DateTime? endTime;
  final int operatorId;
  final String operatorName;
  final int totalCount;
  final int checkedCount;
  final int differenceCount;
  final String? remark;
  final DateTime createTime;
  final DateTime updateTime;
  
  // 状态显示文本
  String get statusText {
    switch (status) {
      case 0: return '进行中';
      case 1: return '已完成';
      case 2: return '已取消';
      default: return '未知';
    }
  }
  
  // 进度百分比
  double get progress {
    if (totalCount == 0) return 0.0;
    return checkedCount / totalCount;
  }
}
```

### 3. API服务集成
```dart
class InventoryCheckService {
  static const String baseUrl = '/api/v1/inventory-check';
  
  // 获取盘点单列表
  Future<PaginatedResponse<InventoryCheck>> getInventoryCheckList({
    int page = 1,
    int pageSize = 20,
    String? keyword,
    int? storeId,
    int? status,
    String? startDate,
    String? endDate,
  }) async {
    // API调用实现
  }
  
  // 创建盘点单
  Future<InventoryCheck> createInventoryCheck(InventoryCheckCreate data) async {
    // API调用实现
  }
  
  // 更新盘点单
  Future<InventoryCheck> updateInventoryCheck(int id, InventoryCheckUpdate data) async {
    // API调用实现
  }
}
```

## 📊 统计功能设计

### 1. 实时统计计算
基于当前筛选条件实时计算统计信息：

```dart
void _calculateStatistics() {
  totalCount.value = inventoryChecks.length;
  ongoingCount.value = inventoryChecks.where((check) => check.status == 0).length;
  completedCount.value = inventoryChecks.where((check) => check.status == 1).length;
  differenceCount.value = inventoryChecks
      .map((check) => check.differenceCount)
      .fold(0, (sum, count) => sum + count);
}
```

### 2. 统计信息显示
在页面顶部显示统计卡片，包含：
- 总盘点单数
- 进行中盘点单数
- 已完成盘点单数
- 差异商品总数

## 🔄 下一步实现计划

### 阶段1: 基础功能实现 (第1周)
1. 创建盘点单列表页面UI
2. 实现数据表格和筛选功能
3. 添加权限控制和门店过滤
4. 实现分页和搜索功能

### 阶段2: 盘点详情功能 (第2周)
1. 创建盘点详情页面
2. 实现扫码盘点功能
3. 添加盘点明细管理
4. 实现差异处理逻辑

### 阶段3: 完善和优化 (第3周)
1. 添加盘点报告生成
2. 实现批量操作功能
3. 优化响应式设计
4. 添加单元测试

## 🔌 API接口设计

### 1. 盘点单管理接口

#### 1.1 获取盘点单列表
```http
GET /api/v1/inventory-check?page=1&page_size=20&store_id=1&status=0
```

**请求参数**:
- `page`: 页码 (默认1)
- `page_size`: 每页数量 (默认20，最大100)
- `keyword`: 关键词搜索 (盘点单号、备注)
- `store_id`: 门店ID筛选
- `status`: 状态筛选 (0=进行中,1=已完成,2=已取消)
- `operator_id`: 操作员ID筛选
- `start_date`: 开始日期 (YYYY-MM-DD)
- `end_date`: 结束日期 (YYYY-MM-DD)

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "items": [
      {
        "id": 1,
        "check_no": "CHK20241201001",
        "store_id": 1,
        "store_name": "总店",
        "status": 0,
        "start_time": "2024-12-01 09:00:00",
        "end_time": null,
        "operator_id": 1,
        "operator_name": "张三",
        "total_count": 150,
        "checked_count": 80,
        "difference_count": 3,
        "remark": "月度盘点",
        "create_time": "2024-12-01 08:30:00",
        "update_time": "2024-12-01 10:30:00"
      }
    ],
    "total": 1,
    "page": 1,
    "page_size": 20,
    "total_pages": 1
  }
}
```

#### 1.2 创建盘点单
```http
POST /api/v1/inventory-check?operator_id=1
```

**请求体**:
```json
{
  "store_id": 1,
  "remark": "月度盘点",
  "jewelry_ids": [1, 2, 3, 4, 5]  // 可选，指定盘点商品，为空则盘点所有在售商品
}
```

#### 1.3 更新盘点单
```http
PUT /api/v1/inventory-check/{check_id}
```

#### 1.4 删除盘点单
```http
DELETE /api/v1/inventory-check/{check_id}
```

### 2. 盘点明细接口

#### 2.1 获取盘点明细列表
```http
GET /api/v1/inventory-check/{check_id}/items?page=1&status=0
```

#### 2.2 扫码盘点
```http
POST /api/v1/inventory-check/{check_id}/scan
```

**请求体**:
```json
{
  "barcode": "JW202401010001",
  "actual_stock": 1,
  "remark": "正常"
}
```

#### 2.3 批量盘点
```http
POST /api/v1/inventory-check/{check_id}/batch-check
```

**请求体**:
```json
{
  "items": [
    {
      "jewelry_id": 1,
      "actual_stock": 1,
      "remark": "正常"
    },
    {
      "jewelry_id": 2,
      "actual_stock": 0,
      "remark": "缺失"
    }
  ]
}
```

### 3. 盘点统计接口

#### 3.1 获取盘点统计
```http
GET /api/v1/inventory-check/statistics?store_id=1&start_date=2024-12-01&end_date=2024-12-31
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total_checks": 5,
    "ongoing_checks": 1,
    "completed_checks": 4,
    "cancelled_checks": 0,
    "total_items": 750,
    "checked_items": 600,
    "difference_items": 15,
    "accuracy_rate": 97.5
  }
}
```

## 💻 前端代码实现示例

### 1. 盘点单列表控制器
```dart
class InventoryCheckController extends GetxController {
  final InventoryCheckService _service = Get.find<InventoryCheckService>();
  final AuthService _authService = Get.find<AuthService>();

  // 响应式数据
  final inventoryChecks = <InventoryCheck>[].obs;
  final isLoading = false.obs;
  final currentPage = 1.obs;
  final totalPages = 1.obs;
  final pageSize = 20.obs;

  // 筛选条件
  final searchController = TextEditingController();
  final selectedStoreId = Rxn<int>();
  final selectedStatus = Rxn<int>();
  final startDate = Rxn<DateTime>();
  final endDate = Rxn<DateTime>();

  // 统计信息
  final totalCount = 0.obs;
  final ongoingCount = 0.obs;
  final completedCount = 0.obs;
  final differenceCount = 0.obs;

  @override
  void onInit() {
    super.onInit();
    _initializeData();
  }

  void _initializeData() {
    // 权限控制：非管理员用户只能查看自己门店
    if (_authService.userRole.value != 'admin' && _authService.storeId.value != null) {
      selectedStoreId.value = _authService.storeId.value;
    }

    fetchInventoryChecks();
  }

  Future<void> fetchInventoryChecks() async {
    try {
      isLoading.value = true;

      final response = await _service.getInventoryCheckList(
        page: currentPage.value,
        pageSize: pageSize.value,
        keyword: searchController.text.trim().isEmpty ? null : searchController.text.trim(),
        storeId: selectedStoreId.value,
        status: selectedStatus.value,
        startDate: startDate.value?.toIso8601String().split('T')[0],
        endDate: endDate.value?.toIso8601String().split('T')[0],
      );

      inventoryChecks.value = response.items;
      totalPages.value = response.totalPages;

      _calculateStatistics();

    } catch (e) {
      LoggerService.e('获取盘点单列表失败', e);
      Get.snackbar('错误', '获取盘点单列表失败: ${e.toString()}');
    } finally {
      isLoading.value = false;
    }
  }

  void _calculateStatistics() {
    totalCount.value = inventoryChecks.length;
    ongoingCount.value = inventoryChecks.where((check) => check.status == 0).length;
    completedCount.value = inventoryChecks.where((check) => check.status == 1).length;
    differenceCount.value = inventoryChecks
        .map((check) => check.differenceCount)
        .fold(0, (sum, count) => sum + count);
  }

  void resetFilters() {
    searchController.clear();
    selectedStatus.value = null;
    startDate.value = null;
    endDate.value = null;

    // 非管理员用户保持门店筛选
    if (_authService.userRole.value == 'admin') {
      selectedStoreId.value = null;
    }

    currentPage.value = 1;
    fetchInventoryChecks();
  }

  void searchInventoryChecks() {
    currentPage.value = 1;
    fetchInventoryChecks();
  }

  void goToPage(int page) {
    currentPage.value = page;
    fetchInventoryChecks();
  }

  Future<void> createInventoryCheck() async {
    // 跳转到创建盘点单页面
    Get.toNamed('/inventory-check/create');
  }

  Future<void> viewInventoryCheck(InventoryCheck check) async {
    // 跳转到盘点详情页面
    Get.toNamed('/inventory-check/detail/${check.id}');
  }

  Future<void> deleteInventoryCheck(InventoryCheck check) async {
    try {
      final confirmed = await Get.dialog<bool>(
        AlertDialog(
          title: const Text('确认删除'),
          content: Text('确定要删除盘点单 ${check.checkNo} 吗？'),
          actions: [
            TextButton(
              onPressed: () => Get.back(result: false),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () => Get.back(result: true),
              child: const Text('确定'),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        await _service.deleteInventoryCheck(check.id);
        Get.snackbar('成功', '盘点单删除成功');
        fetchInventoryChecks();
      }
    } catch (e) {
      LoggerService.e('删除盘点单失败', e);
      Get.snackbar('错误', '删除盘点单失败: ${e.toString()}');
    }
  }
}
```

### 2. 盘点单列表视图
```dart
class InventoryCheckView extends StatelessWidget {
  final InventoryCheckController controller = Get.put(InventoryCheckController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('库存盘点'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
      ),
      body: Container(
        decoration: AppBorderStyles.standardBoxDecoration,
        child: Column(
          children: [
            _buildFilterSection(),
            _buildStatisticsSection(),
            Expanded(child: _buildInventoryCheckList()),
            _buildPagination(),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterSection() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: AppBorderStyles.tableBorder,
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(flex: 3, child: _buildSearchField()),
              const SizedBox(width: 12),
              Expanded(flex: 2, child: _buildStoreSelector()),
              const SizedBox(width: 12),
              Expanded(flex: 2, child: _buildStatusSelector()),
              const SizedBox(width: 12),
              Expanded(flex: 3, child: _buildDateRangePicker()),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              _buildSearchButton(),
              const SizedBox(width: 8),
              _buildResetButton(),
              const Spacer(),
              _buildCreateButton(),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSearchField() {
    return TextField(
      controller: controller.searchController,
      decoration: AppBorderStyles.standardInputDecoration.copyWith(
        labelText: '搜索',
        hintText: '盘点单号、备注',
        prefixIcon: const Icon(Icons.search, size: 20),
        border: InputBorder.none,
        enabledBorder: InputBorder.none,
        focusedBorder: InputBorder.none,
      ),
      onSubmitted: (_) => controller.searchInventoryChecks(),
    );
  }

  Widget _buildStoreSelector() {
    return Obx(() {
      final authService = Get.find<AuthService>();
      final isAdmin = authService.userRole.value == 'admin';

      if (!isAdmin && authService.storeId.value != null) {
        // 普通员工：显示当前门店，不可选择
        return Container(
          height: 48,
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: AppBorderStyles.standardBoxDecoration.copyWith(
            color: Colors.grey[100],
          ),
          child: Row(
            children: [
              const Icon(Icons.store, size: 16, color: Colors.grey),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  authService.storeName.value ?? '当前门店',
                  style: const TextStyle(color: Colors.grey),
                ),
              ),
            ],
          ),
        );
      }

      // 管理员：可以选择门店
      return DropdownButtonFormField<int>(
        value: controller.selectedStoreId.value,
        decoration: AppBorderStyles.standardInputDecoration.copyWith(
          labelText: '门店',
          border: InputBorder.none,
          enabledBorder: InputBorder.none,
          focusedBorder: InputBorder.none,
        ),
        items: [
          const DropdownMenuItem<int>(
            value: null,
            child: Text('全部门店'),
          ),
          // TODO: 加载门店列表
        ],
        onChanged: (value) {
          controller.selectedStoreId.value = value;
        },
      );
    });
  }

  Widget _buildStatusSelector() {
    return Obx(() => DropdownButtonFormField<int>(
      value: controller.selectedStatus.value,
      decoration: AppBorderStyles.standardInputDecoration.copyWith(
        labelText: '状态',
        border: InputBorder.none,
        enabledBorder: InputBorder.none,
        focusedBorder: InputBorder.none,
      ),
      items: const [
        DropdownMenuItem<int>(value: null, child: Text('全部状态')),
        DropdownMenuItem<int>(value: 0, child: Text('进行中')),
        DropdownMenuItem<int>(value: 1, child: Text('已完成')),
        DropdownMenuItem<int>(value: 2, child: Text('已取消')),
      ],
      onChanged: (value) {
        controller.selectedStatus.value = value;
      },
    ));
  }

  Widget _buildStatisticsSection() {
    return Obx(() => Container(
      padding: const EdgeInsets.all(16),
      decoration: AppBorderStyles.standardBoxDecoration.copyWith(
        color: Colors.blue[50],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem('总盘点单', controller.totalCount.toString(), Icons.inventory),
          _buildStatItem('进行中', controller.ongoingCount.toString(), Icons.play_circle),
          _buildStatItem('已完成', controller.completedCount.toString(), Icons.check_circle),
          _buildStatItem('差异商品', controller.differenceCount.toString(), Icons.warning),
        ],
      ),
    ));
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, size: 24, color: Colors.blue[600]),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }
}
```

## 📝 注意事项

1. **UI一致性**: 严格遵循AppBorderStyles组件规范
2. **权限控制**: 确保门店级数据隔离
3. **响应式设计**: 支持不同屏幕尺寸
4. **数据一致性**: 盘点过程中保证数据完整性
5. **用户体验**: 提供清晰的操作反馈和进度提示
6. **扫码功能**: 集成条码扫描器，支持快速盘点
7. **离线支持**: 考虑网络不稳定情况下的离线盘点功能
8. **数据备份**: 盘点过程中定期保存数据，防止意外丢失
