import 'package:get/get.dart';
import '../../../core/utils/logger_service.dart';

/// 首饰服务类
class JewelryService extends GetxService {
  @override
  void onInit() {
    super.onInit();
    LoggerService.d('JewelryService 初始化完成');
  }

  /// 获取首饰列表
  Future<List<Map<String, dynamic>>> getJewelryList({
    int page = 1,
    int pageSize = 20,
    String? keyword,
    int? categoryId,
    int? storeId,
  }) async {
    try {
      LoggerService.d('获取首饰列表: page=$page, pageSize=$pageSize, keyword=$keyword');
      
      // 模拟API调用
      await Future.delayed(const Duration(milliseconds: 500));
      
      // 返回模拟数据
      return _getMockJewelryList();
    } catch (e) {
      LoggerService.e('获取首饰列表失败', e);
      rethrow;
    }
  }

  /// 获取首饰详情
  Future<Map<String, dynamic>?> getJewelryDetail(int id) async {
    try {
      LoggerService.d('获取首饰详情: id=$id');
      
      // 模拟API调用
      await Future.delayed(const Duration(milliseconds: 300));
      
      // 返回模拟数据
      return _getMockJewelryDetail(id);
    } catch (e) {
      LoggerService.e('获取首饰详情失败', e);
      rethrow;
    }
  }

  /// 创建首饰
  Future<bool> createJewelry(Map<String, dynamic> data) async {
    try {
      LoggerService.d('创建首饰: $data');
      
      // 模拟API调用
      await Future.delayed(const Duration(milliseconds: 800));
      
      return true;
    } catch (e) {
      LoggerService.e('创建首饰失败', e);
      rethrow;
    }
  }

  /// 更新首饰
  Future<bool> updateJewelry(int id, Map<String, dynamic> data) async {
    try {
      LoggerService.d('更新首饰: id=$id, data=$data');
      
      // 模拟API调用
      await Future.delayed(const Duration(milliseconds: 800));
      
      return true;
    } catch (e) {
      LoggerService.e('更新首饰失败', e);
      rethrow;
    }
  }

  /// 删除首饰
  Future<bool> deleteJewelry(int id) async {
    try {
      LoggerService.d('删除首饰: id=$id');
      
      // 模拟API调用
      await Future.delayed(const Duration(milliseconds: 500));
      
      return true;
    } catch (e) {
      LoggerService.e('删除首饰失败', e);
      rethrow;
    }
  }

  /// 获取首饰分类列表
  Future<List<Map<String, dynamic>>> getJewelryCategories() async {
    try {
      LoggerService.d('获取首饰分类列表');
      
      // 模拟API调用
      await Future.delayed(const Duration(milliseconds: 300));
      
      return _getMockCategories();
    } catch (e) {
      LoggerService.e('获取首饰分类列表失败', e);
      rethrow;
    }
  }

  /// 模拟首饰列表数据
  List<Map<String, dynamic>> _getMockJewelryList() {
    return List.generate(10, (index) {
      final id = index + 1;
      return {
        'id': id,
        'name': '黄金项链 $id',
        'barcode': 'JW${id.toString().padLeft(6, '0')}',
        'category_id': (index % 3) + 1,
        'category_name': ['项链', '戒指', '手镯'][index % 3],
        'material': '黄金',
        'weight': 15.5 + index * 2.3,
        'price': 3500.0 + index * 500,
        'cost': 3000.0 + index * 400,
        'stock_quantity': 10 - index,
        'store_id': 1,
        'store_name': '总店',
        'status': index % 4 == 0 ? 'sold' : 'in_stock',
        'created_at': DateTime.now().subtract(Duration(days: index)).toIso8601String(),
        'updated_at': DateTime.now().subtract(Duration(hours: index)).toIso8601String(),
      };
    });
  }

  /// 模拟首饰详情数据
  Map<String, dynamic>? _getMockJewelryDetail(int id) {
    return {
      'id': id,
      'name': '黄金项链 $id',
      'barcode': 'JW${id.toString().padLeft(6, '0')}',
      'category_id': 1,
      'category_name': '项链',
      'material': '黄金',
      'weight': 15.5,
      'price': 3500.0,
      'cost': 3000.0,
      'stock_quantity': 5,
      'store_id': 1,
      'store_name': '总店',
      'status': 'in_stock',
      'description': '精美黄金项链，工艺精湛，适合日常佩戴',
      'images': [
        'https://example.com/jewelry1.jpg',
        'https://example.com/jewelry2.jpg',
      ],
      'created_at': DateTime.now().subtract(const Duration(days: 30)).toIso8601String(),
      'updated_at': DateTime.now().subtract(const Duration(hours: 2)).toIso8601String(),
    };
  }

  /// 模拟分类数据
  List<Map<String, dynamic>> _getMockCategories() {
    return [
      {
        'id': 1,
        'name': '项链',
        'description': '各种款式的项链',
        'sort_order': 1,
        'is_active': true,
      },
      {
        'id': 2,
        'name': '戒指',
        'description': '各种款式的戒指',
        'sort_order': 2,
        'is_active': true,
      },
      {
        'id': 3,
        'name': '手镯',
        'description': '各种款式的手镯',
        'sort_order': 3,
        'is_active': true,
      },
      {
        'id': 4,
        'name': '耳环',
        'description': '各种款式的耳环',
        'sort_order': 4,
        'is_active': true,
      },
    ];
  }
}
