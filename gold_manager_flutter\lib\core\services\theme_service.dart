import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../utils/logger_service.dart';
import 'storage_service.dart';

/// 主题服务
/// 管理应用主题设置和切换
class ThemeService extends GetxService {
  late StorageService _storageService;
  
  // 主题模式
  final Rx<ThemeMode> themeMode = ThemeMode.system.obs;
  
  @override
  void onInit() {
    super.onInit();
    _initTheme();
    LoggerService.i('ThemeService initialized');
  }
  
  /// 初始化主题
  void _initTheme() {
    try {
      _storageService = Get.find<StorageService>();
      final savedTheme = _storageService.getThemeMode();
      
      if (savedTheme != null) {
        switch (savedTheme) {
          case 'light':
            themeMode.value = ThemeMode.light;
            break;
          case 'dark':
            themeMode.value = ThemeMode.dark;
            break;
          default:
            themeMode.value = ThemeMode.system;
        }
      }
    } catch (e) {
      LoggerService.e('初始化主题失败', e);
      themeMode.value = ThemeMode.system;
    }
  }
  
  /// 切换主题
  Future<void> toggleTheme() async {
    if (themeMode.value == ThemeMode.system || themeMode.value == ThemeMode.light) {
      await setTheme(ThemeMode.dark);
    } else {
      await setTheme(ThemeMode.light);
    }
  }
  
  /// 设置主题
  Future<void> setTheme(ThemeMode mode) async {
    themeMode.value = mode;
    
    String themeName;
    switch (mode) {
      case ThemeMode.light:
        themeName = 'light';
        break;
      case ThemeMode.dark:
        themeName = 'dark';
        break;
      default:
        themeName = 'system';
    }
    
    try {
      await _storageService.setThemeMode(themeName);
      Get.changeThemeMode(mode);
      LoggerService.i('主题已切换至: $themeName');
    } catch (e) {
      LoggerService.e('设置主题失败', e);
    }
  }
  
  /// 判断是否是暗色主题
  bool get isDarkMode {
    if (themeMode.value == ThemeMode.system) {
      return WidgetsBinding.instance.window.platformBrightness == Brightness.dark;
    }
    return themeMode.value == ThemeMode.dark;
  }
} 