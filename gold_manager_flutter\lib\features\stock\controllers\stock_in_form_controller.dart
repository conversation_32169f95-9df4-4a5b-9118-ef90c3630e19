import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:uuid/uuid.dart';
import '../../../core/utils/logger.dart';
import '../../../models/stock/stock_in.dart';
import '../../../models/stock/stock_in_item.dart';
import '../../../models/stock/stock_in_jewelry_item.dart';
import '../../../models/stock/stock_in_draft.dart';
import '../../../models/common/document_status.dart';
import '../../../models/common/enums.dart';
import '../../../models/store/store.dart';
import '../../../models/jewelry/jewelry.dart';
import '../../../models/jewelry/jewelry_category.dart';
import '../../../services/stock_service.dart';
import '../../../services/store_service.dart';
import '../../../services/jewelry_service.dart';
import '../../../services/auth_service.dart';
import '../../../services/draft_service.dart';
import 'stock_in_controller.dart';
import 'stock_tab_controller.dart';

/// 入库单表单控制器
class StockInFormController extends GetxController {
  // 服务
  final StockService _stockService = Get.find<StockService>();
  final StoreService _storeService = Get.find<StoreService>();
  final JewelryService _jewelryService = Get.find<JewelryService>();
  final AuthService _authService = Get.find<AuthService>();
  final DraftService _draftService = Get.find<DraftService>();

  // 表单控制器
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  final TextEditingController remarkController = TextEditingController();

  // 状态变量
  final RxBool isLoading = false.obs;
  final RxBool isEditing = false.obs;
  final RxInt selectedStoreId = 0.obs;
  final RxList<Store> storeList = <Store>[].obs;
  final RxList<JewelryCategory> categoryList = <JewelryCategory>[].obs;
  final RxList<StockInItem> itemList = <StockInItem>[].obs;
  final RxDouble totalAmount = 0.0.obs;

  // 草稿相关状态
  final RxBool isDraftSaving = false.obs;
  final RxString draftSaveStatus = ''.obs;
  final RxString currentDraftId = ''.obs;
  Timer? _autoSaveTimer;

  // 当前编辑的入库单
  Rx<StockIn?> currentStockIn = Rx<StockIn?>(null);

  @override
  void onInit() {
    super.onInit();
    LoggerService.d('StockInFormController 初始化');

    // 获取路由参数，检查是否是编辑模式
    if (Get.arguments != null && Get.arguments['id'] != null) {
      isEditing.value = true;
      _loadStockIn(Get.arguments['id']);
      return; // 已经加载了数据，不需要继续执行后面的初始化代码
    }
    
    // 检查是否是从标签页打开的编辑模式
    final tag = Get.parameters['tag'] ?? '';
    if (tag.isNotEmpty && tag.startsWith('stock_in_edit_')) {
      // 从标签ID中提取入库单ID
      final stockInId = int.tryParse(tag.replaceFirst('stock_in_edit_', ''));
      if (stockInId != null) {
        // 注意：现在我们直接从openStockInEditForm调用loadStockInById，
        // 所以这里不需要再次加载数据，以避免重复加载
        isEditing.value = true;
        // _loadStockIn(stockInId); // 不再在这里加载数据
        return; // 不需要继续执行后面的初始化代码
      }
    }
    
    // 新建模式：只有在确认不是编辑模式时才执行这些初始化代码
    // 获取门店列表和分类列表
    fetchStores();
    fetchCategories();

    // 如果用户不是管理员，自动选择用户所属门店
    if (_authService.userRole.value != 'admin') {
      selectedStoreId.value = _authService.storeId.value;
    }

    // 生成草稿ID
    currentDraftId.value = DraftService.generateDraftId();

    // 尝试恢复草稿
    _loadDraftIfExists();

    // 启动自动保存
    _startAutoSave();
  }

  @override
  void onClose() {
    // 停止自动保存
    _autoSaveTimer?.cancel();
    _draftService.stopAutoSave();

    // 释放控制器资源
    remarkController.dispose();
    
    // 清空数据，确保下次打开时不会显示旧数据
    itemList.clear();
    storeList.clear();
    categoryList.clear();
    isEditing.value = false;
    isLoading.value = false;
    totalAmount.value = 0.0;
    selectedStoreId.value = 0;
    currentStockIn.value = null;
    
    super.onClose();
  }

  /// 加载入库单详情
  Future<void> _loadStockIn(int id, {bool autoCloseOnError = true, bool showLoading = true}) async {
    try {
      if (showLoading) {
        isLoading.value = true;
      }

      // 获取门店列表和分类列表（并行执行）
      await Future.wait([
        fetchStores(),
        fetchCategories(),
      ]);

      // 使用Future.delayed给UI一个绘制加载指示器的机会
      await Future.delayed(const Duration(milliseconds: 50));

      // 获取入库单详情
      final stockIn = await _stockService.getStockInById(id);

      // 验证编辑权限
      if (!stockIn.canEdit) {
        Get.snackbar(
          '提示',
          '该入库单已审核，无法编辑',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.orange,
          colorText: Colors.white,
        );
        
        // 仅在autoCloseOnError为true时关闭标签页
        if (autoCloseOnError) {
          // 通过TabManager关闭标签页
          try {
            final stockTabController = Get.find<StockTabController>();
            final tabManager = stockTabController.tabManager;
            final editTabIndex = tabManager.tabs.indexWhere(
              (tab) => tab.id == 'stock_in_edit_$id',
            );
            if (editTabIndex != -1) {
              tabManager.closeTab(editTabIndex);
            }
          } catch (e) {
            // 如果找不到TabManager，尝试使用常规导航返回
            Get.back();
          }
        }
        
        return;
      }

      // 更新表单数据（在一个微任务中批量更新，减少重绘）
      await Future.microtask(() {
        currentStockIn.value = stockIn;
        selectedStoreId.value = stockIn.storeId;
        remarkController.text = stockIn.remark ?? '';

        // 更新商品列表
        if (stockIn.items != null && stockIn.items!.isNotEmpty) {
          // 如果数据量大，分批更新UI以避免卡顿
          if (stockIn.items!.length > 100) {
            // 先显示前100条
            itemList.value = stockIn.items!.sublist(0, 100);
            _calculateTotalAmount();
            
            // 给UI线程一个更新的机会
            Future.delayed(const Duration(milliseconds: 100), () {
              // 然后加载剩余的数据
              itemList.value = stockIn.items!;
              _calculateTotalAmount();
            });
          } else {
            // 数据量小，直接更新
            itemList.value = stockIn.items!;
            _calculateTotalAmount();
          }
        }
      });

      LoggerService.d('入库单数据加载成功: ${stockIn.stockInNo}');

    } catch (e) {
      LoggerService.e('加载入库单详情失败', e);
      Get.snackbar(
        '错误',
        '加载入库单详情失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      
      // 仅在autoCloseOnError为true时关闭标签页
      if (autoCloseOnError) {
        // 通过TabManager关闭标签页
        try {
          final stockTabController = Get.find<StockTabController>();
          final tabManager = stockTabController.tabManager;
          final editTabIndex = tabManager.tabs.indexWhere(
            (tab) => tab.id == 'stock_in_edit_$id',
          );
          if (editTabIndex != -1) {
            tabManager.closeTab(editTabIndex);
          } else {
            Get.back(); // 如果找不到标签页，尝试正常返回
          }
        } catch (e) {
          // 如果找不到TabManager，尝试使用常规导航返回
          Get.back();
        }
      }
      
    } finally {
      if (showLoading) {
        isLoading.value = false;
      }
    }
  }

  /// 获取门店列表
  Future<void> fetchStores() async {
    try {
      isLoading.value = true;
      // 使用getAllStores确保获取所有门店，不受分页限制
      final stores = await _storeService.getAllStores();
      storeList.value = stores;
    } catch (e) {
      LoggerService.e('获取门店列表失败', e);
      Get.snackbar(
        '错误',
        '获取门店列表失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// 获取分类列表
  Future<void> fetchCategories() async {
    try {
      final categories = await _jewelryService.getCategories();
      categoryList.value = categories;
      LoggerService.d('获取商品分类成功，共${categories.length}个分类');
    } catch (e) {
      LoggerService.e('获取商品分类失败', e);
    }
  }

  /// 添加商品
  void addItem(Jewelry jewelry) {
    // 检查是否已经存在该商品
    final existingIndex = itemList.indexWhere((item) => item.jewelryId == jewelry.id);

    if (existingIndex != -1) {
      // 已存在，显示提示
      Get.snackbar(
        '提示',
        '该商品已添加到入库单中',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      return;
    }

    // 创建包含完整价格信息的商品项
    final jewelryItem = StockInJewelryItem(
      id: 0,
      stockInId: currentStockIn.value?.id ?? 0,
      barcode: jewelry.barcode,
      name: jewelry.name,
      categoryId: jewelry.categoryId,
      categoryName: jewelry.category?.name ?? '',
      ringSize: '', // 可以从界面获取
      goldWeight: jewelry.goldWeight,
      goldPrice: 0.0, // 需要从界面获取
      silverWeight: jewelry.silverWeight,
      silverPrice: 0.0, // 需要从界面获取
      workType: 0, // 默认按克
      workPrice: jewelry.workPrice,
      platingCost: 0.0, // 需要从界面获取
      wholesaleWorkPrice: 0.0, // 需要从界面获取
      retailWorkPrice: 0.0, // 需要从界面获取
      pieceWorkPrice: 0.0, // 需要从界面获取
    );

    // 创建StockInItem包装器
    final newItem = StockInItem(
      id: 0, // 新商品ID为0，保存时后端会分配ID
      stockInId: currentStockIn.value?.id ?? 0,
      jewelryId: jewelry.id,
      barcode: jewelry.barcode,
      name: jewelry.name,
      categoryId: jewelry.category?.id ?? 0,
      categoryName: jewelry.category?.name,
      totalPrice: jewelryItem.totalCost,
      jewelry: jewelry,
    );

    itemList.add(newItem);
    _calculateTotalAmount();
  }

  /// 移除商品
  void removeItem(int index) {
    if (index < 0 || index >= itemList.length) return;

    itemList.removeAt(index);
    _calculateTotalAmount();
  }

  /// 更新商品项
  void updateItem(int index, StockInItem updatedItem) {
    if (index < 0 || index >= itemList.length) return;

    itemList[index] = updatedItem;
    _calculateTotalAmount();
  }

  /// 更新商品字段
  void updateItemField(int index, {
    String? barcode,
    String? name,
    int? categoryId,
    String? ringSize,
    double? goldWeight,
    double? goldPrice,
    double? silverWeight,
    double? silverPrice,
    double? totalWeight,
    int? silverWorkType,
    double? silverWorkPrice,
    double? platingCost,
    double? wholesaleWorkPrice,
    double? retailWorkPrice,
    double? pieceWorkPrice,
  }) {
    if (index < 0 || index >= itemList.length) return;

    final currentItem = itemList[index];

    // 🔑 关键修复：实现与StockInJewelryItem相同的自动计算逻辑

    // 获取更新后的值（如果没有传入新值，使用当前值）
    final newGoldWeight = goldWeight ?? currentItem.goldWeight ?? 0.0;
    final newSilverWeight = silverWeight ?? currentItem.silverWeight ?? 0.0;
    final newGoldPrice = goldPrice ?? currentItem.goldPrice ?? 0.0;
    final newSilverPrice = silverPrice ?? currentItem.silverPrice ?? 0.0;
    final newSilverWorkType = silverWorkType ?? currentItem.silverWorkType ?? 0;
    final newSilverWorkPrice = silverWorkPrice ?? currentItem.silverWorkPrice ?? 0.0;
    final newPlatingCost = platingCost ?? currentItem.platingCost ?? 0.0;

    // 🔑 自动计算总重：total_weight = gold_weight + silver_weight
    final calculatedTotalWeight = newGoldWeight + newSilverWeight;

    // 🔑 自动计算各项成本
    final goldCost = newGoldWeight * newGoldPrice;
    final silverCost = newSilverWeight * newSilverPrice;

    // 🔑 自动计算工费成本：根据工费方式区分
    final workCost = newSilverWorkType == 0
        ? calculatedTotalWeight * newSilverWorkPrice  // 按克计算
        : newSilverWorkPrice;                         // 按件计算

    // 🔑 自动计算总成本：total_cost = gold_cost + silver_cost + work_cost + plating_cost
    final calculatedTotalCost = goldCost + silverCost + workCost + newPlatingCost;

    LoggerService.d('🔍 编辑入库单自动计算详情 (第${index + 1}项):');
    LoggerService.d('   金重: ${newGoldWeight}g × 金价: $newGoldPrice元/g = 金成本: ${goldCost.toStringAsFixed(2)}元');
    LoggerService.d('   银重: ${newSilverWeight}g × 银价: $newSilverPrice元/g = 银成本: ${silverCost.toStringAsFixed(2)}元');
    LoggerService.d('   总重: ${calculatedTotalWeight.toStringAsFixed(2)}g (金重 + 银重)');
    LoggerService.d('   工费方式: ${newSilverWorkType == 0 ? "按克" : "按件"}');
    LoggerService.d('   工费计算: ${newSilverWorkType == 0 ? "${calculatedTotalWeight.toStringAsFixed(2)}g × $newSilverWorkPrice元/g" : "$newSilverWorkPrice元/件"} = ${workCost.toStringAsFixed(2)}元');
    LoggerService.d('   电铸费: ${newPlatingCost.toStringAsFixed(2)}元');
    LoggerService.d('   总成本: ${calculatedTotalCost.toStringAsFixed(2)}元 (金成本 + 银成本 + 工费 + 电铸费)');

    final updatedItem = currentItem.copyWith(
      barcode: barcode,
      name: name,
      categoryId: categoryId,
      ringSize: ringSize,
      goldWeight: goldWeight,
      goldPrice: goldPrice,
      goldCost: goldCost,  // 🔑 自动计算的金成本
      silverWeight: silverWeight,
      silverPrice: silverPrice,
      silverCost: silverCost,  // 🔑 自动计算的银成本
      totalWeight: calculatedTotalWeight,  // 🔑 自动计算的总重
      silverWorkType: silverWorkType,
      silverWorkPrice: silverWorkPrice,
      silverWorkCost: workCost,  // 🔑 自动计算的工费成本
      platingCost: platingCost,
      totalCost: calculatedTotalCost,  // 🔑 自动计算的总成本
      wholesaleWorkPrice: wholesaleWorkPrice,
      retailWorkPrice: retailWorkPrice,
      pieceWorkPrice: pieceWorkPrice,
      totalPrice: calculatedTotalCost,  // 🔑 总价等于总成本
    );

    itemList[index] = updatedItem;
    _calculateTotalAmount();
  }

  /// 计算总金额
  void _calculateTotalAmount() {
    double total = 0;
    for (var item in itemList) {
      total += item.amount;
    }
    totalAmount.value = total;
  }

  /// 保存入库单
  Future<void> saveStockIn() async {
    if (!formKey.currentState!.validate()) return;

    // 检查是否选择了门店
    if (selectedStoreId.value <= 0) {
      Get.snackbar(
        '错误',
        '请选择门店',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    // 检查是否添加了商品
    if (itemList.isEmpty) {
      Get.snackbar(
        '错误',
        '请添加商品',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    try {
      isLoading.value = true;

      // 构建入库单数据
      final stockIn = isEditing.value ?
        currentStockIn.value!.copyWith(
          storeId: selectedStoreId.value,
          totalAmount: totalAmount.value,
          remark: remarkController.text,
          items: itemList,
        ) :
        StockIn(
          id: 0, // 新入库单ID为0，保存时后端会分配ID
          stockInNo: 'IN${DateTime.now().year}${DateTime.now().month.toString().padLeft(2, '0')}${const Uuid().v4().substring(0, 6)}',
          storeId: selectedStoreId.value,
          totalAmount: totalAmount.value,
          operatorId: _authService.userId.value,
          remark: remarkController.text,
          status: DocumentStatus.draft,
          createTime: DateTime.now(),
          items: itemList,
        );

      bool success = false;
      if (isEditing.value) {
        // 更新入库单
        success = await _stockService.updateStockIn(stockIn);
      } else {
        // 创建入库单
        final createdStockIn = await _stockService.createStockIn(stockIn);
        if (createdStockIn != null) {
          // 更新当前入库单信息，包括ID
          currentStockIn.value = createdStockIn;
          success = true;
        }
      }

      if (success) {
        Get.snackbar(
          '成功',
          isEditing.value ? '入库单更新成功' : '入库单创建成功',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );

        // 编辑模式下需要特殊处理标签页关闭和列表刷新
        if (isEditing.value) {
          _handleEditModeSuccess();
        } else {
          // 新建模式：返回上一页，并传递更新标志
          Get.back(result: true);
        }
      } else {
        Get.snackbar(
          '错误',
          isEditing.value ? '入库单更新失败' : '入库单创建失败',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      LoggerService.e(isEditing.value ? '更新入库单失败' : '创建入库单失败', e);
      Get.snackbar(
        '错误',
        '${isEditing.value ? '更新' : '创建'}入库单失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// 提交审核
  Future<void> submitForApproval() async {
    try {
      isLoading.value = true;

      LoggerService.d('🚀 开始提交审核流程');
      LoggerService.d('📋 当前入库单状态: ${currentStockIn.value?.id ?? "无ID"}');

      // 先保存入库单
      await saveStockIn();

      LoggerService.d('💾 保存入库单完成，当前ID: ${currentStockIn.value?.id ?? "无ID"}');

      // 如果保存成功，提交审核
      if (currentStockIn.value != null && currentStockIn.value!.id > 0) {
        final auditorId = _authService.userId.value ?? 1;
        final stockInId = currentStockIn.value!.id;

        LoggerService.d('👤 审核员ID: $auditorId');
        LoggerService.d('📄 入库单ID: $stockInId');
        LoggerService.d('🔄 开始调用API提交审核...');

        final result = await _stockService.submitStockInForApproval(
          stockInId,
          auditorId,
        );

        LoggerService.d('✅ API调用结果: $result');

        if (result) {
          Get.snackbar(
            '成功',
            '入库单已提交审核',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.green,
            colorText: Colors.white,
          );

          // 返回上一页，并传递更新标志
          Get.back(result: true);
        } else {
          Get.snackbar(
            '错误',
            '提交审核失败',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
        }
      } else {
        LoggerService.e('❌ 入库单保存失败或ID无效');
        Get.snackbar(
          '错误',
          '请先保存入库单',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      LoggerService.e('❌ 提交审核失败', e);

      // 提供更详细的错误信息
      String errorMessage = '提交审核失败';
      if (e.toString().contains('422')) {
        errorMessage = '请求参数验证失败，请检查入库单数据';
      } else if (e.toString().contains('404')) {
        errorMessage = '入库单不存在，请重新保存';
      } else if (e.toString().contains('401')) {
        errorMessage = '认证失败，请重新登录';
      } else if (e.toString().contains('500')) {
        errorMessage = '服务器内部错误，请稍后重试';
      } else {
        errorMessage = '提交审核失败: ${e.toString()}';
      }

      Get.snackbar(
        '错误',
        errorMessage,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        duration: const Duration(seconds: 5), // 延长显示时间
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// 扫描条码搜索商品
  Future<void> scanBarcode() async {
    // TODO: 实现条码扫描功能
    Get.snackbar(
      '提示',
      '条码扫描功能开发中',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.blue,
      colorText: Colors.white,
    );
  }

  /// 搜索商品
  Future<void> searchJewelry(String keyword) async {
    // TODO: 实现商品搜索功能
    Get.snackbar(
      '提示',
      '商品搜索功能开发中',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.blue,
      colorText: Colors.white,
    );
  }

  /// 启动自动保存
  void _startAutoSave() {
    _draftService.startAutoSave(() {
      _saveDraftSilently();
    });
  }

  /// 加载草稿（如果存在）
  Future<void> _loadDraftIfExists() async {
    try {
      // 获取最新的草稿
      final drafts = await _draftService.getAllDrafts();
      if (drafts.isNotEmpty) {
        final latestDraft = drafts.first;

        // 询问用户是否恢复草稿
        final shouldRestore = await Get.dialog<bool>(
          AlertDialog(
            title: const Text('发现草稿'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('发现未完成的入库单草稿：'),
                const SizedBox(height: 8),
                Text('创建时间: ${_formatDateTime(latestDraft.createTime)}'),
                Text('商品件数: ${latestDraft.itemCount}'),
                Text('总金额: ¥${latestDraft.totalAmount.toStringAsFixed(2)}'),
                const SizedBox(height: 16),
                const Text('是否恢复此草稿？'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Get.back(result: false),
                child: const Text('删除草稿'),
              ),
              ElevatedButton(
                onPressed: () => Get.back(result: true),
                child: const Text('恢复草稿'),
              ),
            ],
          ),
        );

        if (shouldRestore == true) {
          _restoreDraft(latestDraft);
        } else if (shouldRestore == false) {
          // 删除草稿
          await _draftService.deleteDraft(latestDraft.id);
        }
      }
    } catch (e) {
      LoggerService.e('加载草稿失败', e);
    }
  }

  /// 恢复草稿
  void _restoreDraft(StockInDraft draft) {
    try {
      currentDraftId.value = draft.id;
      selectedStoreId.value = draft.storeId;
      remarkController.text = draft.remark ?? '';

      // 转换草稿商品项为入库单商品项
      final items = draft.items.map((draftItem) {
        return StockInItem(
          id: 0,
          stockInId: 0,
          jewelryId: draftItem.jewelryId,
          barcode: draftItem.barcode ?? '',
          name: draftItem.name,
          categoryId: 0, // 草稿项没有categoryId，使用默认值
          totalPrice: draftItem.amount,
          jewelry: Jewelry(
            id: draftItem.jewelryId,
            name: draftItem.name,
            barcode: draftItem.barcode ?? '',
            categoryId: 0, // 临时值
            goldWeight: draftItem.goldWeight,
            goldPrice: 400.0, // 使用默认金价，草稿中没有保存金价信息
            silverWeight: draftItem.silverWeight,
            silverPrice: 6.0, // 使用默认银价，草稿中没有保存银价信息
            totalWeight: draftItem.goldWeight + draftItem.silverWeight, // 计算总重量
            workPrice: draftItem.laborCost,
            retailWorkPrice: 0.0, // 草稿中没有零售工费信息，使用默认值
            wholesaleWorkPrice: 0.0, // 草稿中没有批发工费信息，使用默认值
            pieceWorkPrice: 0.0, // 草稿中没有件工费信息，使用默认值
            salePrice: draftItem.amount,
            storeId: selectedStoreId.value,
            status: JewelryStatus.onShelf,
            createTime: DateTime.now(),
            category: JewelryCategory(
              id: 0,
              name: draftItem.category,
              parentId: 0,
              sortOrder: 0,
              isActive: true,
            ),
          ),
        );
      }).toList();

      itemList.value = items;
      _calculateTotalAmount();

      draftSaveStatus.value = '草稿已恢复';
      LoggerService.d('草稿恢复成功: ${draft.id}');
    } catch (e) {
      LoggerService.e('恢复草稿失败', e);
      Get.snackbar(
        '错误',
        '恢复草稿失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 静默保存草稿
  Future<void> _saveDraftSilently() async {
    if (isEditing.value || itemList.isEmpty) {
      return; // 编辑模式或无商品时不保存草稿
    }

    try {
      isDraftSaving.value = true;

      final draft = _createDraftFromCurrentData();
      final success = await _draftService.saveDraft(draft);

      if (success) {
        draftSaveStatus.value = '自动保存于 ${_formatTime(DateTime.now())}';
      } else {
        draftSaveStatus.value = '自动保存失败';
      }
    } catch (e) {
      LoggerService.e('自动保存草稿失败', e);
      draftSaveStatus.value = '自动保存失败';
    } finally {
      isDraftSaving.value = false;
    }
  }

  /// 手动保存草稿
  Future<void> saveDraftManually() async {
    if (isEditing.value) {
      Get.snackbar(
        '提示',
        '编辑模式下无需保存草稿',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.blue,
        colorText: Colors.white,
      );
      return;
    }

    if (selectedStoreId.value <= 0) {
      Get.snackbar(
        '错误',
        '请先选择门店',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    try {
      isDraftSaving.value = true;

      final draft = _createDraftFromCurrentData();
      final success = await _draftService.saveDraft(draft);

      if (success) {
        draftSaveStatus.value = '草稿已保存';
        Get.snackbar(
          '成功',
          '草稿保存成功',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      } else {
        Get.snackbar(
          '错误',
          '草稿保存失败',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      LoggerService.e('保存草稿失败', e);
      Get.snackbar(
        '错误',
        '保存草稿失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isDraftSaving.value = false;
    }
  }

  /// 从当前数据创建草稿
  StockInDraft _createDraftFromCurrentData() {
    final draftItems = itemList.map((item) {
      return StockInDraftItem(
        jewelryId: item.jewelryId,
        barcode: item.barcode,
        name: item.jewelry?.name ?? '',
        category: item.jewelry?.category?.name ?? '',
        goldWeight: item.jewelry?.goldWeight ?? 0.0,
        silverWeight: item.jewelry?.silverWeight ?? 0.0,
        laborCost: item.jewelry?.workPrice ?? 0.0,
        otherCost: 0.0, // 当前Jewelry模型没有otherCost字段
        amount: item.amount,
      );
    }).toList();

    return StockInDraft(
      id: currentDraftId.value,
      storeId: selectedStoreId.value,
      remark: remarkController.text,
      items: draftItems,
      createTime: DateTime.now(),
      updateTime: DateTime.now(),
    );
  }

  /// 格式化日期时间
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// 格式化时间
  String _formatTime(DateTime dateTime) {
    return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}:${dateTime.second.toString().padLeft(2, '0')}';
  }

  /// 处理编辑模式下保存成功的逻辑
  void _handleEditModeSuccess() {
    try {
      // 获取库存标签页控制器
      final stockTabController = Get.find<StockTabController>();
      final tabManager = stockTabController.tabManager;

      // 查找并关闭编辑标签页
      final editTabIndex = tabManager.tabs.indexWhere(
        (tab) => tab.id.startsWith('stock_in_edit_'),
      );

      if (editTabIndex != -1) {
        tabManager.closeTab(editTabIndex);
      }

      // 刷新入库管理列表
      if (Get.isRegistered<StockInController>()) {
        final stockInController = Get.find<StockInController>();
        stockInController.fetchStockInList();
      }

    } catch (e) {
      LoggerService.e('处理编辑模式成功逻辑失败', e);
      // 如果出错，使用备用方案
      Get.back(result: true);
    }
  }

  /// 加载入库单详情（公开方法，供外部调用）
  Future<void> loadStockInById(int id) async {
    // 这里不设置isLoading=true，因为外部已经显示了加载动画
    try {
      LoggerService.d('🔄 开始分批加载入库单数据: ID=$id');

      // 1. 先并行加载门店和分类基础数据（轻量操作），带超时保护
      await Future.wait([
        fetchStores().timeout(const Duration(seconds: 10)),
        fetchCategories().timeout(const Duration(seconds: 10)),
      ]).timeout(
        const Duration(seconds: 15),
        onTimeout: () {
          LoggerService.e('❌ 基础数据加载超时');
          throw Exception('基础数据加载超时，请检查网络连接');
        },
      );

      // 2. 获取入库单基本信息，带超时保护
      LoggerService.d('📋 获取入库单基本信息...');
      final stockIn = await _stockService.getStockInById(id).timeout(
        const Duration(seconds: 20),
        onTimeout: () {
          LoggerService.e('❌ 入库单基本信息获取超时');
          throw Exception('入库单数据获取超时，请检查网络连接');
        },
      );

      // 3. 验证编辑权限
      if (!stockIn.canEdit) {
        LoggerService.w('⚠️ 入库单已审核，无法编辑: ID=$id');
        Get.snackbar(
          '提示',
          '该入库单已审核，无法编辑',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.orange,
          colorText: Colors.white,
        );
        return;
      }

      // 4. 更新基本表单数据（不包括商品列表）
      LoggerService.d('📝 更新入库单基本信息...');
      currentStockIn.value = stockIn;
      selectedStoreId.value = stockIn.storeId;
      remarkController.text = stockIn.remark ?? '';
      
      // 5. 分批加载商品数据，避免UI卡顿
      if (stockIn.items != null && stockIn.items!.isNotEmpty) {
        final totalItems = stockIn.items!.length;
        LoggerService.d('开始分批加载商品数据，共$totalItems项...');
        
        // 5.1 如果数据量大，分批次加载
        if (totalItems > 50) {
          const batchSize = 50;
          int processedItems = 0;
          
          // 先加载第一批，让用户可以看到内容
          final firstBatch = stockIn.items!.sublist(0, batchSize);
          itemList.value = firstBatch;
          _calculateTotalAmount();
          processedItems += batchSize;
          LoggerService.d('- 已加载第一批商品数据: $batchSize项');
          
          // 分批加载剩余数据
          while (processedItems < totalItems) {
            // 给UI线程喘息的机会
            await Future.delayed(const Duration(milliseconds: 50));
            
            final nextBatchSize = (processedItems + batchSize) > totalItems 
                ? (totalItems - processedItems) 
                : batchSize;
            
            final nextBatch = stockIn.items!.sublist(0, processedItems + nextBatchSize);
            itemList.value = nextBatch;
            _calculateTotalAmount();
            
            processedItems += nextBatchSize;
            LoggerService.d('- 已加载商品数据: $processedItems/$totalItems项');
          }
          
          LoggerService.d('全部商品数据加载完成!');
        } else {
          // 数据量小，直接加载
          itemList.value = stockIn.items!;
          _calculateTotalAmount();
          LoggerService.d('商品数据一次性加载完成: $totalItems项');
        }
      } else {
        LoggerService.d('入库单没有商品数据');
        itemList.clear();
        _calculateTotalAmount();
      }
      
      LoggerService.d('入库单数据加载成功: ${stockIn.stockInNo}');
      
    } catch (e) {
      LoggerService.e('加载入库单详情失败', e);
      Get.snackbar(
        '错误',
        '加载入库单详情失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
}