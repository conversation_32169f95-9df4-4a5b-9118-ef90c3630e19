
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/theme/app_theme.dart';
import '../../../core/constants/border_styles.dart';
import '../../../models/common/enums.dart';
import '../../../models/jewelry/jewelry.dart';
import '../../../services/auth_service.dart';
import '../../../widgets/empty_state.dart';
import '../../../widgets/loading_state.dart';
import '../../../widgets/responsive_builder.dart';
import '../controllers/stock_query_controller.dart';
import 'jewelry_detail_view.dart';
import 'jewelry_edit_dialog.dart';
import '../models/inventory_check.dart';
import '../services/inventory_check_service.dart';
import '../widgets/inventory_check_create_dialog.dart';
import '../widgets/inventory_check_detail_dialog.dart';
import '../widgets/inventory_check_edit_dialog.dart';

/// 库存查询页面
class StockQueryView extends GetView<StockQueryController> {
  const StockQueryView({super.key});

  @override
  Widget build(BuildContext context) {
    return Material(
      color: AppTheme.backgroundColor,
      child: Column(
        children: [
          _buildFilterSection(),
          const Divider(height: 1),
          Expanded(
            child: Obx(() {
              if (controller.isLoading.value) {
                return const LoadingState(text: '加载中...', timeoutSeconds: 30);
              }

              if (controller.jewelryList.isEmpty) {
                return const EmptyState(
                  icon: Icons.inventory,
                  title: '暂无库存数据',
                  message: '请调整筛选条件重新查询',
                );
              }

              return _buildJewelryList();
            }),
          ),
        ],
      ),
    );
  }

  /// 构建筛选区域 - 完全仿照入库管理页面的单行布局结构
  Widget _buildFilterSection() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: AppBorderStyles.tableBorder,
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center, // 确保所有控件垂直居中对齐
        children: [
          // 图标和标题 - 复用入库管理页面的样式
          Icon(
            Icons.search,
            color: Colors.blue[600],
            size: 20
          ),
          const SizedBox(width: 8),
          const Text(
            '库存查询',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(width: 24),

          // 门店选择器 - 紧凑型设计
          const Text(
            '门店:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(width: 8),
          SizedBox(
            width: 150, // 🔧 修复：从120增加到150，解决门店名称换行问题
            child: _buildCompactStoreSelector(),
          ),
          const SizedBox(width: 24),

          // 状态选择器 - 紧凑型设计
          const Text(
            '状态:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(width: 8),
          SizedBox(
            width: 120,
            child: _buildCompactStatusSelector(),
          ),
          const SizedBox(width: 24),

          // 分类选择器 - 紧凑型设计
          const Text(
            '分类:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(width: 8),
          SizedBox(
            width: 120,
            child: _buildCompactCategorySelector(),
          ),
          const SizedBox(width: 24),

          // 搜索框 - 紧凑型设计
          const Text(
            '搜索:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(width: 8),
          SizedBox(
            width: 200,
            child: _buildCompactSearchField(),
          ),
          const SizedBox(width: 24),

          // 操作按钮组
          _buildResetButton(),
          const SizedBox(width: 8),
          _buildSearchButton(),
          const SizedBox(width: 16),
          _buildInventoryCheckButton(),
        ],
      ),
    );
  }

  /// 构建紧凑型搜索框 - 仿照入库管理页面的32px高度设计
  Widget _buildCompactSearchField() {
    return Container(
      height: 32, // 强制高度为32px，与其他控件保持一致
      decoration: AppBorderStyles.standardBoxDecoration,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
        child: TextField(
          controller: controller.searchController,
          decoration: const InputDecoration(
            hintText: '条码、名称等',
            // 完整移除所有边框
            border: InputBorder.none,
            enabledBorder: InputBorder.none,
            focusedBorder: InputBorder.none,
            disabledBorder: InputBorder.none,
            // 正确的文字对齐配置
            contentPadding: EdgeInsets.symmetric(vertical: 8),
            isDense: true,
          ),
          style: const TextStyle(fontSize: 13), // 紧凑字体
          onSubmitted: (_) => controller.search(),
        ),
      ),
    );
  }

  /// 构建紧凑型门店选择器 - 仿照入库管理页面的32px高度设计
  Widget _buildCompactStoreSelector() {
    return Obx(() {
      // 获取认证服务
      final authService = Get.find<AuthService>();
      final isAdmin = authService.userRole.value == 'admin' || authService.hasPermission('super.admin');

      // 构建下拉选项
      List<DropdownMenuItem<int>> items = [];

      // 只有管理员才显示"全部门店"选项
      if (isAdmin) {
        items.add(const DropdownMenuItem<int>(
          value: null,
          child: Text('全部门店', style: TextStyle(fontSize: 13)),
        ));
      }

      // 添加门店选项
      items.addAll(controller.storeList.map((store) => DropdownMenuItem<int>(
        value: store.id,
        child: Text(
          store.name,
          style: const TextStyle(fontSize: 13),
          overflow: TextOverflow.ellipsis, // 🔧 修复：长文本优雅截断
          maxLines: 1, // 确保单行显示
        ),
      )));

      return Container(
        height: 32, // 强制高度为32px，与其他控件保持一致
        decoration: AppBorderStyles.standardBoxDecoration,
        child: DropdownButtonHideUnderline(
          child: DropdownButton<int>(
            value: controller.selectedStoreId.value == 0 ? null : controller.selectedStoreId.value,
            hint: Text(
              isAdmin ? '全部门店' : '请选择门店', // 🔧 修复：根据用户角色显示不同的提示文本
              style: const TextStyle(fontSize: 13, color: Colors.grey),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
            isExpanded: true,
            items: items,
            onChanged: (value) {
              controller.selectedStoreId.value = value ?? 0;
              controller.search();
            },
            padding: const EdgeInsets.symmetric(horizontal: 8),
            style: const TextStyle(fontSize: 13, color: Colors.black87),
          ),
        ),
      );
    });
  }

  /// 构建紧凑型状态选择器 - 仿照入库管理页面的32px高度设计
  Widget _buildCompactStatusSelector() {
    return Obx(() => Container(
      height: 32, // 强制高度为32px，与其他控件保持一致
      decoration: AppBorderStyles.standardBoxDecoration,
      child: DropdownButtonHideUnderline(
        child: DropdownButton<JewelryStatus?>(
          value: controller.selectedStatus.value,
          hint: const Text('全部状态', style: TextStyle(fontSize: 13, color: Colors.grey)),
          isExpanded: true,
          items: [
            const DropdownMenuItem<JewelryStatus?>(
              value: null,
              child: Text('全部状态', style: TextStyle(fontSize: 13)),
            ),
            ...JewelryStatus.values.map((status) => DropdownMenuItem<JewelryStatus?>(
              value: status,
              child: Text(status.label, style: const TextStyle(fontSize: 13)),
            )),
          ],
          onChanged: (value) {
            controller.selectedStatus.value = value;
            controller.search();
          },
          padding: const EdgeInsets.symmetric(horizontal: 8),
          style: const TextStyle(fontSize: 13, color: Colors.black87),
        ),
      ),
    ));
  }

  /// 构建紧凑型分类选择器 - 仿照入库管理页面的32px高度设计
  Widget _buildCompactCategorySelector() {
    return Obx(() => Container(
      height: 32, // 强制高度为32px，与其他控件保持一致
      decoration: AppBorderStyles.standardBoxDecoration,
      child: DropdownButtonHideUnderline(
        child: DropdownButton<int>(
          value: controller.selectedCategoryId.value == 0 ? null : controller.selectedCategoryId.value,
          hint: const Text('全部分类', style: TextStyle(fontSize: 13, color: Colors.grey)),
          isExpanded: true,
          items: [
            const DropdownMenuItem<int>(
              value: null,
              child: Text('全部分类', style: TextStyle(fontSize: 13)),
            ),
            // 动态加载分类列表
            ...controller.categoryList.map((category) => DropdownMenuItem<int>(
              value: category.id,
              child: Text(category.name, style: const TextStyle(fontSize: 13)),
            )),
          ],
          onChanged: (value) {
            controller.selectedCategoryId.value = value ?? 0;
            controller.search();
          },
          padding: const EdgeInsets.symmetric(horizontal: 8),
          style: const TextStyle(fontSize: 13, color: Colors.black87),
        ),
      ),
    ));
  }

  /// 构建重置按钮 - 仿照入库管理页面的紧凑型设计
  Widget _buildResetButton() {
    return SizedBox(
      height: 32, // 强制高度为32px，与其他控件保持一致
      child: OutlinedButton.icon(
        icon: const Icon(Icons.refresh, size: 14),
        label: const Text('重置'),
        style: OutlinedButton.styleFrom(
          foregroundColor: Colors.grey[600],
          padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 12), // 移除垂直内边距，由SizedBox控制高度
          minimumSize: const Size(0, 32), // 固定高度32px
          maximumSize: const Size(double.infinity, 32), // 限制最大高度
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
          ),
          side: const BorderSide(color: AppBorderStyles.borderColor), // 使用统一边框颜色
          textStyle: const TextStyle(fontSize: 13), // 紧凑字体
        ),
        onPressed: controller.resetFilters,
      ),
    );
  }

  /// 构建搜索按钮 - 仿照入库管理页面的紧凑型设计
  Widget _buildSearchButton() {
    return SizedBox(
      height: 32, // 强制高度为32px，与其他控件保持一致
      child: ElevatedButton.icon(
        icon: const Icon(Icons.search, size: 14),
        label: const Text('搜索'),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.blue[600], // 蓝色背景
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 12), // 移除垂直内边距，由SizedBox控制高度
          minimumSize: const Size(0, 32), // 固定高度32px
          maximumSize: const Size(double.infinity, 32), // 限制最大高度
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
          ),
          textStyle: const TextStyle(fontSize: 13), // 紧凑字体
        ),
        onPressed: controller.search,
      ),
    );
  }

  /// 构建库存盘点按钮 - 新增功能入口
  Widget _buildInventoryCheckButton() {
    return SizedBox(
      height: 32, // 强制高度为32px，与其他控件保持一致
      child: ElevatedButton.icon(
        icon: const Icon(Icons.inventory, size: 14),
        label: const Text('库存盘点'),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.purple[600], // 紫色背景，区分其他功能
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 12),
          minimumSize: const Size(0, 32),
          maximumSize: const Size(double.infinity, 32),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
          ),
          textStyle: const TextStyle(fontSize: 13),
        ),
        onPressed: _showInventoryCheckMenu,
      ),
    );
  }

  /// 构建首饰列表
  Widget _buildJewelryList() {
    return Column(
      children: [
        Expanded(
          child: ScreenTypeLayout(
            mobile: _buildListView(),
            tablet: _buildDataTable(),
            desktop: _buildDataTable(),
          ),
        ),
        _buildStatisticsAndPagination(),
      ],
    );
  }

  /// 构建列表视图 - 完全仿照入库管理页面的移动端卡片样式
  Widget _buildListView() {
    return ListView.separated(
      padding: const EdgeInsets.all(16),
      itemCount: controller.jewelryList.length,
      separatorBuilder: (context, index) => const Divider(height: 16),
      itemBuilder: (context, index) {
        final jewelry = controller.jewelryList[index];

        return Card(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppBorderStyles.largeBorderRadius), // 使用统一的大圆角
          ),
          elevation: 2,
          child: InkWell(
            borderRadius: BorderRadius.circular(AppBorderStyles.largeBorderRadius), // 保持一致的圆角
            onTap: () => JewelryDetailDialog.show(
              context,
              jewelry,
              onUpdated: () => controller.search(),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        jewelry.name,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      _buildStatusBadge(jewelry.status),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      const Icon(Icons.qr_code, size: 16, color: Colors.grey),
                      const SizedBox(width: 4),
                      Text(
                        jewelry.barcode,
                        style: const TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      const Icon(Icons.store, size: 16, color: Colors.grey),
                      const SizedBox(width: 4),
                      Text(
                        jewelry.store?.name ?? '未知门店',
                        style: const TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text('金重', style: TextStyle(fontSize: 12, color: Colors.grey)),
                            Text('${jewelry.goldWeight}g', style: const TextStyle(fontWeight: FontWeight.bold)),
                          ],
                        ),
                      ),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text('银重', style: TextStyle(fontSize: 12, color: Colors.grey)),
                            Text('${jewelry.silverWeight}g', style: const TextStyle(fontWeight: FontWeight.bold)),
                          ],
                        ),
                      ),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text('售价', style: TextStyle(fontSize: 12, color: Colors.grey)),
                            Text('¥${jewelry.salePrice.toStringAsFixed(2)}',
                                style: const TextStyle(fontWeight: FontWeight.bold, color: AppTheme.primaryColor)),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// 构建数据表格 - 完全仿照入库管理页面的样式
  Widget _buildDataTable() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // 获取可用宽度
        final availableWidth = constraints.maxWidth;

        // 计算各列的宽度比例 - 添加件工费列，重新调整列宽分配
        final serialWidth = availableWidth * 0.03;     // 3% - 序号 (保持不变)
        final barcodeWidth = availableWidth * 0.08;    // 8% - 条码 (从9%减少1%)
        final nameWidth = availableWidth * 0.12;       // 12% - 商品名称 (保持不变)
        final categoryWidth = availableWidth * 0.04;   // 4% - 分类 (从5%减少1%)
        final storeWidth = availableWidth * 0.10;      // 10% - 所属门店 (保持不变)
        final ringSizeWidth = availableWidth * 0.04;   // 4% - 圈口号 (保持不变)
        final goldWeightWidth = availableWidth * 0.06; // 6% - 金重 (从7%减少1%)
        final goldPriceWidth = availableWidth * 0.05;  // 5% - 金价 (保持不变)
        final silverWeightWidth = availableWidth * 0.06; // 6% - 银重 (从7%减少1%)
        final silverPriceWidth = availableWidth * 0.05; // 5% - 银价 (保持不变)
        final totalWeightWidth = availableWidth * 0.07; // 7% - 总重 (保持不变)
        final workFeeWidth = availableWidth * 0.06;    // 6% - 工费 (从7%减少1%)
        final pieceWorkFeeWidth = availableWidth * 0.06; // 6% - 件工费 (新增列)
        final costWidth = availableWidth * 0.07;       // 7% - 成本 (保持不变)
        final actionWidth = availableWidth * 0.07;     // 7% - 操作 (保持不变)
        // 总计：3+8+12+4+10+4+6+5+6+5+7+6+6+7+7 = 99% (留1%缓冲)

        // 根据可用宽度调整字体大小 - 与入库管理页面完全一致
        final fontSize = availableWidth < 800 ? 12.0 : 14.0;
        final headingFontSize = availableWidth < 800 ? 13.0 : 15.0;

        return Container(
          margin: const EdgeInsets.all(4), // 与入库管理页面保持一致的边距
          decoration: AppBorderStyles.elevatedBoxDecoration.copyWith(
            color: Colors.white, // 明确设置背景色为白色
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius), // 使用标准圆角
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal, // 改为横向滚动
              child: SingleChildScrollView(
                scrollDirection: Axis.vertical,
                child: SizedBox(
                  width: availableWidth < 1200 ? 1200 : availableWidth, // 确保最小宽度，小屏幕时启用横向滚动
                  child: DataTable(
                border: AppBorderStyles.tableStandardBorder, // 🔧 添加统一表格边框
                columnSpacing: 0, // 移除列间距，让列宽完全由我们控制
                horizontalMargin: 0, // 移除水平边距
                headingRowHeight: 44, // 与入库管理页面保持一致的表头高度
                dataRowMinHeight: 48, // 与入库管理页面保持一致的数据行高度
                dataRowMaxHeight: 48,
                headingTextStyle: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                  fontSize: headingFontSize,
                ),
                dataTextStyle: TextStyle(
                  fontSize: fontSize,
                  color: Colors.black87,
                ),
                headingRowColor: WidgetStateProperty.all(AppBorderStyles.tableHeaderBackground), // 使用统一表头背景
                columns: [
                  DataColumn(
                    label: Container(
                      width: serialWidth,
                      padding: const EdgeInsets.symmetric(horizontal: 2),
                      child: const Text('序号', textAlign: TextAlign.center),
                    ),
                  ),
                  DataColumn(
                    label: Container(
                      width: barcodeWidth,
                      padding: const EdgeInsets.symmetric(horizontal: 2),
                      child: const Text('条码', textAlign: TextAlign.center),
                    ),
                  ),
                  DataColumn(
                    label: Container(
                      width: nameWidth,
                      padding: const EdgeInsets.symmetric(horizontal: 2),
                      child: const Text('商品名称', textAlign: TextAlign.center),
                    ),
                  ),
                  DataColumn(
                    label: Container(
                      width: categoryWidth,
                      padding: const EdgeInsets.symmetric(horizontal: 2),
                      child: const Text('分类', textAlign: TextAlign.center),
                    ),
                  ),
                  DataColumn(
                    label: Container(
                      width: storeWidth,
                      padding: const EdgeInsets.symmetric(horizontal: 2),
                      child: const Text('所属门店', textAlign: TextAlign.center),
                    ),
                  ),
                  DataColumn(
                    label: Container(
                      width: ringSizeWidth,
                      padding: const EdgeInsets.symmetric(horizontal: 2),
                      child: const Text('圈口号', textAlign: TextAlign.center),
                    ),
                  ),
                  DataColumn(
                    label: Container(
                      width: goldWeightWidth,
                      padding: const EdgeInsets.symmetric(horizontal: 2),
                      child: const Text('金重(g)', textAlign: TextAlign.center),
                    ),
                  ),
                  DataColumn(
                    label: Container(
                      width: goldPriceWidth,
                      padding: const EdgeInsets.symmetric(horizontal: 2),
                      child: const Text('金价', textAlign: TextAlign.center),
                    ),
                  ),
                  DataColumn(
                    label: Container(
                      width: silverWeightWidth,
                      padding: const EdgeInsets.symmetric(horizontal: 2),
                      child: const Text('银重(g)', textAlign: TextAlign.center),
                    ),
                  ),
                  DataColumn(
                    label: Container(
                      width: silverPriceWidth,
                      padding: const EdgeInsets.symmetric(horizontal: 2),
                      child: const Text('银价', textAlign: TextAlign.center),
                    ),
                  ),
                  DataColumn(
                    label: Container(
                      width: totalWeightWidth,
                      padding: const EdgeInsets.symmetric(horizontal: 2),
                      child: const Text('总重(g)', textAlign: TextAlign.center),
                    ),
                  ),
                  DataColumn(
                    label: Container(
                      width: workFeeWidth,
                      padding: const EdgeInsets.symmetric(horizontal: 2),
                      child: const Text('工费', textAlign: TextAlign.center),
                    ),
                  ),
                  DataColumn(
                    label: Container(
                      width: pieceWorkFeeWidth,
                      padding: const EdgeInsets.symmetric(horizontal: 2),
                      child: const Text('件工费', textAlign: TextAlign.center),
                    ),
                  ),
                  DataColumn(
                    label: Container(
                      width: costWidth,
                      padding: const EdgeInsets.symmetric(horizontal: 2),
                      child: const Text('成本', textAlign: TextAlign.center),
                    ),
                  ),
                  DataColumn(
                    label: Container(
                      width: actionWidth,
                      padding: const EdgeInsets.symmetric(horizontal: 2),
                      child: const Text('操作', textAlign: TextAlign.center),
                    ),
                  ),
                ],
                rows: controller.jewelryList.asMap().entries.map((entry) {
                  final index = entry.key;
                  final jewelry = entry.value;
                  final serialNumber = (controller.currentPage.value - 1) * controller.pageSize + index + 1;

                  return DataRow(
                    cells: [
                      // 序号
                      DataCell(
                        Container(
                          width: serialWidth,
                          height: 48, // 与入库管理页面保持一致的数据行高度
                          padding: const EdgeInsets.symmetric(horizontal: 2),
                          alignment: Alignment.center,
                          child: Text(
                            serialNumber.toString(),
                            style: TextStyle(
                              fontSize: fontSize,
                              fontWeight: FontWeight.w500,
                              color: Colors.grey[600],
                            ),
                          ),
                        ),
                      ),
                      // 条码
                      DataCell(
                        Container(
                          width: barcodeWidth,
                          height: 48, // 与入库管理页面保持一致的数据行高度
                          padding: const EdgeInsets.symmetric(horizontal: 2),
                          alignment: Alignment.center,
                          child: Text(
                            jewelry.barcode,
                            style: TextStyle(
                              fontSize: fontSize,
                              fontWeight: FontWeight.w500,
                              color: AppTheme.primaryColor,
                              fontFamily: 'monospace',
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                        onTap: () => JewelryDetailDialog.show(
                          context,
                          jewelry,
                          onUpdated: () => controller.search(),
                        ),
                      ),
                      // 商品名称
                      DataCell(
                        Container(
                          width: nameWidth,
                          height: 48, // 与入库管理页面保持一致的数据行高度
                          padding: const EdgeInsets.symmetric(horizontal: 2),
                          alignment: Alignment.center,
                          child: Text(
                            jewelry.name,
                            style: TextStyle(
                              fontSize: fontSize,
                              fontWeight: FontWeight.w500,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                      ),
                      // 分类
                      DataCell(
                        Container(
                          width: categoryWidth,
                          height: 48, // 与入库管理页面保持一致的数据行高度
                          padding: const EdgeInsets.symmetric(horizontal: 2),
                          alignment: Alignment.center,
                          child: Text(
                            jewelry.category?.name ?? '未知',
                            style: TextStyle(fontSize: fontSize),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                      ),
                      // 所属门店
                      DataCell(
                        Container(
                          width: storeWidth,
                          height: 48, // 与入库管理页面保持一致的数据行高度
                          padding: const EdgeInsets.symmetric(horizontal: 2),
                          alignment: Alignment.center,
                          child: Text(
                            jewelry.store?.name ?? '未知门店',
                            style: TextStyle(
                              fontSize: fontSize,
                              fontWeight: FontWeight.w500,
                              color: AppTheme.primaryColor,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                      ),
                      // 圈口号
                      DataCell(
                        Container(
                          width: ringSizeWidth,
                          height: 48, // 与入库管理页面保持一致的数据行高度
                          padding: const EdgeInsets.symmetric(horizontal: 2),
                          alignment: Alignment.center,
                          child: Text(
                            jewelry.ringSize ?? '-',
                            style: TextStyle(fontSize: fontSize),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                      ),
                      // 金重
                      DataCell(
                        Container(
                          width: goldWeightWidth,
                          height: 48, // 与入库管理页面保持一致的数据行高度
                          padding: const EdgeInsets.symmetric(horizontal: 2),
                          alignment: Alignment.center,
                          child: Text(
                            jewelry.goldWeight.toStringAsFixed(2),
                            style: TextStyle(
                              fontSize: fontSize,
                              fontWeight: FontWeight.w500,
                              color: Colors.amber[700],
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                      ),
                      // 金价
                      DataCell(
                        Container(
                          width: goldPriceWidth,
                          height: 48, // 与入库管理页面保持一致的数据行高度
                          padding: const EdgeInsets.symmetric(horizontal: 2),
                          alignment: Alignment.center,
                          child: Text(
                            jewelry.goldPrice.toStringAsFixed(0),
                            style: TextStyle(
                              fontSize: fontSize,
                              color: Colors.amber[600],
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                      ),
                      // 银重
                      DataCell(
                        Container(
                          width: silverWeightWidth,
                          height: 48, // 与入库管理页面保持一致的数据行高度
                          padding: const EdgeInsets.symmetric(horizontal: 2),
                          alignment: Alignment.center,
                          child: Text(
                            jewelry.silverWeight.toStringAsFixed(2),
                            style: TextStyle(
                              fontSize: fontSize,
                              fontWeight: FontWeight.w500,
                              color: Colors.grey[600],
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                      ),
                      // 银价
                      DataCell(
                        Container(
                          width: silverPriceWidth,
                          height: 48, // 与入库管理页面保持一致的数据行高度
                          padding: const EdgeInsets.symmetric(horizontal: 2),
                          alignment: Alignment.center,
                          child: Text(
                            jewelry.silverPrice.toStringAsFixed(0),
                            style: TextStyle(
                              fontSize: fontSize,
                              color: Colors.grey[600],
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                      ),
                      // 总重
                      DataCell(
                        Container(
                          width: totalWeightWidth,
                          height: 48, // 与入库管理页面保持一致的数据行高度
                          padding: const EdgeInsets.symmetric(horizontal: 2),
                          alignment: Alignment.center,
                          child: Text(
                            jewelry.totalWeight.toStringAsFixed(2),
                            style: TextStyle(
                              fontSize: fontSize,
                              fontWeight: FontWeight.w500,
                              color: Colors.blue[600],
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                      ),
                      // 工费
                      DataCell(
                        Container(
                          width: workFeeWidth,
                          height: 48, // 与入库管理页面保持一致的数据行高度
                          padding: const EdgeInsets.symmetric(horizontal: 2),
                          alignment: Alignment.center,
                          child: Text(
                            jewelry.workPrice.toStringAsFixed(2),
                            style: TextStyle(
                              fontSize: fontSize,
                              color: Colors.orange[600],
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                      ),
                      // 件工费
                      DataCell(
                        Container(
                          width: pieceWorkFeeWidth,
                          height: 48, // 与入库管理页面保持一致的数据行高度
                          padding: const EdgeInsets.symmetric(horizontal: 2),
                          alignment: Alignment.center,
                          child: Text(
                            jewelry.pieceWorkPrice.toStringAsFixed(2),
                            style: TextStyle(
                              fontSize: fontSize,
                              color: Colors.purple[600],
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                      ),
                      // 成本
                      DataCell(
                        Container(
                          width: costWidth,
                          height: 48, // 与入库管理页面保持一致的数据行高度
                          padding: const EdgeInsets.symmetric(horizontal: 2),
                          alignment: Alignment.center,
                          child: Text(
                            jewelry.totalCost.toStringAsFixed(2),
                            style: TextStyle(
                              fontSize: fontSize,
                              fontWeight: FontWeight.w500,
                              color: Colors.green[600],
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                      ),
                      // 操作
                      DataCell(
                        Container(
                          width: actionWidth,
                          height: 48, // 与入库管理页面保持一致的数据行高度
                          padding: const EdgeInsets.symmetric(horizontal: 2),
                          alignment: Alignment.center,
                          child: _buildActionButtons(context, jewelry),
                        ),
                      ),
                    ],
                  );
                }).toList(),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// 构建统计信息和分页组合区域
  Widget _buildStatisticsAndPagination() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      color: Colors.white,
      child: Column(
        children: [
          // 上方统计信息
          _buildStatisticsInfo(),
          const SizedBox(height: 8),
          // 下方居中分页控件
          _buildPaginationControls(),
        ],
      ),
    );
  }

  /// 构建统计信息显示
  Widget _buildStatisticsInfo() {
    return Obx(() => Wrap(
      spacing: 24,
      runSpacing: 8,
      children: [
        _buildStatItem('总件数', '${controller.totalCount.value}件', Icons.inventory, Colors.blue),
        _buildStatItem('总金重', '${controller.totalGoldWeight.value.toStringAsFixed(2)}g', Icons.monetization_on, Colors.amber),
        _buildStatItem('总银重', '${controller.totalSilverWeight.value.toStringAsFixed(2)}g', Icons.scale, Colors.grey),
        _buildStatItem('总重', '${controller.totalWeight.value.toStringAsFixed(2)}g', Icons.fitness_center, Colors.green),
      ],
    ));
  }

  /// 构建单个统计项
  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 16, color: color),
        const SizedBox(width: 4),
        Text(
          '$label: ',
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  /// 构建分页控件
  Widget _buildPaginationControls() {
    return Obx(() {
      if (controller.totalPages.value <= 1) return const SizedBox.shrink();

      return Row(
        mainAxisAlignment: MainAxisAlignment.center, // 居中对齐
        children: [
          IconButton(
            icon: const Icon(Icons.first_page),
            tooltip: '第一页',
            onPressed: controller.currentPage.value > 1
                ? () => controller.goToPage(1)
                : null,
          ),
          IconButton(
            icon: const Icon(Icons.chevron_left),
            tooltip: '上一页',
            onPressed: controller.currentPage.value > 1
                ? () => controller.goToPage(controller.currentPage.value - 1)
                : null,
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: Text(
              '${controller.currentPage.value} / ${controller.totalPages.value}',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.chevron_right),
            tooltip: '下一页',
            onPressed: controller.currentPage.value < controller.totalPages.value
                ? () => controller.goToPage(controller.currentPage.value + 1)
                : null,
          ),
          IconButton(
            icon: const Icon(Icons.last_page),
            tooltip: '最后一页',
            onPressed: controller.currentPage.value < controller.totalPages.value
                ? () => controller.goToPage(controller.totalPages.value)
                : null,
          ),
        ],
      );
    });
  }

  /// 构建状态徽章 - 完全仿照入库管理页面的样式
  Widget _buildStatusBadge(JewelryStatus status) {
    Color statusColor = controller.getStatusColor(status);
    String statusText = status.label;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 2),
      decoration: BoxDecoration(
        color: statusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius), // 使用统一圆角
        border: Border.all(color: statusColor, width: 1),
      ),
      child: Text(
        statusText,
        style: TextStyle(
          color: statusColor,
          fontSize: 11, // 与入库管理页面保持一致的字体大小
          fontWeight: FontWeight.w500,
        ),
        overflow: TextOverflow.ellipsis, // 防止文字溢出
        maxLines: 1, // 确保单行显示
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons(BuildContext context, Jewelry jewelry) {
    final authService = Get.find<AuthService>();

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 查看详情按钮
        if (authService.hasPermission('jewelry.view'))
          InkWell(
            onTap: () => JewelryDetailDialog.show(
              context,
              jewelry,
              onUpdated: () => controller.search(),
            ),
            borderRadius: BorderRadius.circular(4),
            child: Container(
              padding: const EdgeInsets.all(4),
              child: Icon(
                Icons.visibility,
                size: 16,
                color: Colors.blue[600],
              ),
            ),
          ),

        // 编辑按钮
        if (authService.hasPermission('jewelry.update'))
          InkWell(
            onTap: () => _editJewelry(context, jewelry),
            borderRadius: BorderRadius.circular(4),
            child: Container(
              padding: const EdgeInsets.all(4),
              child: Icon(
                Icons.edit,
                size: 16,
                color: Colors.orange[600],
              ),
            ),
          ),

        // 删除按钮
        if (authService.hasPermission('jewelry.delete'))
          InkWell(
            onTap: () => _deleteJewelry(jewelry),
            borderRadius: BorderRadius.circular(4),
            child: Container(
              padding: const EdgeInsets.all(4),
              child: Icon(
                Icons.delete,
                size: 16,
                color: Colors.red[600],
              ),
            ),
          ),
      ],
    );
  }

  /// 编辑首饰
  void _editJewelry(BuildContext context, Jewelry jewelry) {
    JewelryEditDialog.show(
      context,
      jewelry,
      onUpdated: () => controller.search(),
    );
  }

  /// 删除首饰
  void _deleteJewelry(Jewelry jewelry) {
    Get.dialog(
      AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppBorderStyles.mediumBorderRadius),
        ),
        title: Row(
          children: [
            Icon(Icons.warning, color: Colors.red[600], size: 24),
            const SizedBox(width: 8),
            const Text('确认删除'),
          ],
        ),
        content: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.red[50],
            borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
            border: Border.all(color: Colors.red[200]!, width: 1),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('确定要删除以下商品吗？此操作不可撤销。'),
              const SizedBox(height: 8),
              Text(
                '商品名称：${jewelry.name}',
                style: const TextStyle(fontWeight: FontWeight.w500),
              ),
              Text(
                '商品条码：${jewelry.barcode}',
                style: const TextStyle(fontWeight: FontWeight.w500),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            style: TextButton.styleFrom(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
              ),
            ),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => _confirmDelete(jewelry),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
              ),
            ),
            child: const Text('确认删除'),
          ),
        ],
      ),
    );
  }

  /// 确认删除商品
  void _confirmDelete(Jewelry jewelry) async {
    Get.back(); // 关闭对话框

    try {
      // 显示加载提示
      Get.dialog(
        const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('正在删除...'),
            ],
          ),
        ),
        barrierDismissible: false,
      );

      // 调用删除API
      await controller.deleteJewelry(jewelry.id);

      Get.back(); // 关闭加载对话框

      Get.snackbar(
        '成功',
        '商品删除成功',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
        icon: const Icon(Icons.check_circle, color: Colors.white),
      );

      // 刷新列表
      controller.search();
    } catch (e) {
      Get.back(); // 关闭加载对话框

      Get.snackbar(
        '错误',
        '删除失败：${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        icon: const Icon(Icons.error, color: Colors.white),
      );
    }
  }

  /// 显示库存盘点菜单
  void _showInventoryCheckMenu() {
    Get.bottomSheet(
      Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(AppBorderStyles.mediumBorderRadius),
            topRight: Radius.circular(AppBorderStyles.mediumBorderRadius),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                border: Border(
                  bottom: AppBorderStyles.tableBorder,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.inventory,
                    color: Colors.purple[600],
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  const Text(
                    '库存盘点',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Get.back(),
                    icon: const Icon(Icons.close, size: 20),
                  ),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  _buildInventoryCheckMenuItem(
                    icon: Icons.add_circle,
                    title: '新建盘点单',
                    subtitle: '创建新的库存盘点任务',
                    color: Colors.blue,
                    onTap: _createInventoryCheck,
                  ),
                  const SizedBox(height: 12),
                  _buildInventoryCheckMenuItem(
                    icon: Icons.list_alt,
                    title: '查看盘点单',
                    subtitle: '查看现有盘点单列表',
                    color: Colors.green,
                    onTap: _viewInventoryCheckList,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      isScrollControlled: true,
    );
  }

  /// 构建盘点菜单项
  Widget _buildInventoryCheckMenuItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: AppBorderStyles.standardBoxDecoration.copyWith(
          color: color.withValues(alpha: 0.05),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
              ),
              child: Icon(
                icon,
                color: color,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: Colors.grey[400],
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  /// 创建盘点单
  void _createInventoryCheck() async {
    Get.back(); // 关闭底部菜单

    try {
      // 确保服务已注册
      if (!Get.isRegistered<InventoryCheckService>()) {
        Get.put(InventoryCheckService());
      }

      final result = await Get.dialog<InventoryCheck>(
        const InventoryCheckCreateDialog(),
      );

      if (result != null) {
        // 盘点单创建成功，可以选择跳转到详情页面
        Get.snackbar(
          '成功',
          '盘点单 ${result.checkNo} 创建成功',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
          icon: const Icon(Icons.check_circle, color: Colors.white),
        );
      }
    } catch (e) {
      Get.snackbar(
        '错误',
        '创建盘点单失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        icon: const Icon(Icons.error, color: Colors.white),
      );
    }
  }

  /// 查看盘点单列表
  void _viewInventoryCheckList() async {
    Get.back(); // 关闭底部菜单

    try {
      // 确保服务已注册
      if (!Get.isRegistered<InventoryCheckService>()) {
        Get.put(InventoryCheckService());
      }

      await _showInventoryCheckListDialog();
    } catch (e) {
      Get.snackbar(
        '错误',
        '加载盘点单列表失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        icon: const Icon(Icons.error, color: Colors.white),
      );
    }
  }

  /// 显示盘点单列表对话框
  Future<void> _showInventoryCheckListDialog() async {
    final inventoryCheckService = Get.find<InventoryCheckService>();

    await Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppBorderStyles.mediumBorderRadius),
        ),
        child: Container(
          width: 800,
          height: 600,
          decoration: AppBorderStyles.standardBoxDecoration.copyWith(
            borderRadius: BorderRadius.circular(AppBorderStyles.mediumBorderRadius),
          ),
          child: StatefulBuilder(
            builder: (context, setState) {
              return _InventoryCheckListWidget(
                inventoryCheckService: inventoryCheckService,
              );
            },
          ),
        ),
      ),
    );
  }
}

/// 盘点单列表组件
class _InventoryCheckListWidget extends StatefulWidget {
  final InventoryCheckService inventoryCheckService;

  const _InventoryCheckListWidget({
    required this.inventoryCheckService,
  });

  @override
  State<_InventoryCheckListWidget> createState() => _InventoryCheckListWidgetState();
}

class _InventoryCheckListWidgetState extends State<_InventoryCheckListWidget> {
  List<InventoryCheck> _inventoryChecks = [];
  bool _isLoading = false;
  int _currentPage = 1;
  int _totalPages = 1;

  @override
  void initState() {
    super.initState();
    _loadInventoryChecks();
  }

  /// 加载盘点单列表
  Future<void> _loadInventoryChecks() async {
    try {
      setState(() => _isLoading = true);

      final response = await widget.inventoryCheckService.getInventoryCheckList(
        page: _currentPage,
        pageSize: 20,
      );

      setState(() {
        _inventoryChecks = response.items;
        _totalPages = response.totalPages;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      Get.snackbar('错误', '加载盘点单列表失败: ${e.toString()}');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildHeader(),
        Expanded(child: _buildContent()),
        _buildActions(),
      ],
    );
  }

  /// 构建头部
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.green[50],
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(AppBorderStyles.mediumBorderRadius),
          topRight: Radius.circular(AppBorderStyles.mediumBorderRadius),
        ),
        border: const Border(
          bottom: AppBorderStyles.tableBorder,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.list_alt,
            color: Colors.green[600],
            size: 24,
          ),
          const SizedBox(width: 12),
          const Text(
            '盘点单列表',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const Spacer(),
          IconButton(
            onPressed: () => Get.back(),
            icon: const Icon(Icons.close, size: 20),
            style: IconButton.styleFrom(
              backgroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建内容
  Widget _buildContent() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_inventoryChecks.isEmpty) {
      return const Center(
        child: Text(
          '暂无盘点单',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey,
          ),
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(20),
      child: SingleChildScrollView(
        child: DataTable(
          border: AppBorderStyles.tableStandardBorder,
          headingRowColor: WidgetStateProperty.all(AppBorderStyles.tableHeaderBackground),
          columns: const [
            DataColumn(label: Text('盘点单号')),
            DataColumn(label: Text('门店')),
            DataColumn(label: Text('状态')),
            DataColumn(label: Text('应盘/已盘')),
            DataColumn(label: Text('差异数')),
            DataColumn(label: Text('创建时间')),
            DataColumn(label: Text('操作')),
          ],
          rows: _inventoryChecks.map((check) {
            return DataRow(
              cells: [
                DataCell(Text(check.checkNo)),
                DataCell(Text(check.storeName ?? '-')),
                DataCell(
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: check.statusColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
                      border: Border.all(color: check.statusColor, width: 1),
                    ),
                    child: Text(
                      check.statusText,
                      style: TextStyle(
                        fontSize: 12,
                        color: check.statusColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
                DataCell(Text('${check.totalCount}/${check.checkedCount}')),
                DataCell(
                  Text(
                    check.differenceCount.toString(),
                    style: TextStyle(
                      color: check.differenceCount > 0 ? Colors.red : Colors.green,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                DataCell(Text(check.createTime.toString().substring(0, 16))),
                DataCell(
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: const Icon(Icons.visibility, size: 16),
                        onPressed: () => _viewInventoryCheckDetail(check),
                        tooltip: '查看详情',
                      ),
                      if (check.canEdit)
                        IconButton(
                          icon: const Icon(Icons.edit, size: 16),
                          onPressed: () => _editInventoryCheck(check),
                          tooltip: '编辑',
                        ),
                      if (check.canDelete)
                        IconButton(
                          icon: const Icon(Icons.delete, size: 16, color: Colors.red),
                          onPressed: () => _deleteInventoryCheck(check),
                          tooltip: '删除',
                        ),
                    ],
                  ),
                ),
              ],
            );
          }).toList(),
        ),
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActions() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        border: Border(
          top: AppBorderStyles.tableBorder,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '第 $_currentPage 页，共 $_totalPages 页',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
          Row(
            children: [
              IconButton(
                onPressed: _currentPage > 1 ? _previousPage : null,
                icon: const Icon(Icons.chevron_left),
              ),
              IconButton(
                onPressed: _currentPage < _totalPages ? _nextPage : null,
                icon: const Icon(Icons.chevron_right),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 上一页
  void _previousPage() {
    if (_currentPage > 1) {
      setState(() => _currentPage--);
      _loadInventoryChecks();
    }
  }

  /// 下一页
  void _nextPage() {
    if (_currentPage < _totalPages) {
      setState(() => _currentPage++);
      _loadInventoryChecks();
    }
  }

  /// 查看盘点单详情
  void _viewInventoryCheckDetail(InventoryCheck check) async {
    await Get.dialog(
      InventoryCheckDetailDialog(inventoryCheck: check),
    );
  }

  /// 编辑盘点单
  void _editInventoryCheck(InventoryCheck check) async {
    final result = await Get.dialog<InventoryCheck>(
      InventoryCheckEditDialog(inventoryCheck: check),
    );

    if (result != null) {
      _loadInventoryChecks(); // 刷新列表
    }
  }

  /// 删除盘点单
  void _deleteInventoryCheck(InventoryCheck check) async {
    final confirmed = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除盘点单 ${check.checkNo} 吗？\n此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Get.back(result: true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('删除'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await widget.inventoryCheckService.deleteInventoryCheck(check.id);
        Get.snackbar('成功', '盘点单删除成功');
        _loadInventoryChecks(); // 刷新列表
      } catch (e) {
        Get.snackbar('错误', '删除失败: ${e.toString()}');
      }
    }
  }
}