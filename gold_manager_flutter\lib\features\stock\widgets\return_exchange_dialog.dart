import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/constants/border_styles.dart';
import '../../../core/utils/logger.dart';
import '../../../models/stock/stock_out.dart';
import '../../../models/stock/stock_out_item.dart';
import '../../../services/stock_service.dart';
import '../../../services/auth_service.dart';

/// 退换货对话框
class ReturnExchangeDialog extends StatefulWidget {
  final int storeId;

  const ReturnExchangeDialog({
    super.key,
    required this.storeId,
  });

  @override
  State<ReturnExchangeDialog> createState() => _ReturnExchangeDialogState();
}

class _ReturnExchangeDialogState extends State<ReturnExchangeDialog> {
  final StockService _stockService = Get.find<StockService>();
  final AuthService _authService = Get.find<AuthService>();

  // 搜索控制器
  final TextEditingController _orderNoController = TextEditingController();
  final TextEditingController _barcodeController = TextEditingController();

  // 滚动控制器 - 修复问题2：表格选择后自动跳转问题
  final ScrollController _orderTableScrollController = ScrollController();
  final ScrollController _itemTableScrollController = ScrollController();

  // 数据状态 - 🔧 分离状态管理避免交叉影响
  final RxList<StockOut> _paidOrderList = <StockOut>[].obs;
  final RxList<StockOutItem> _orderItemList = <StockOutItem>[].obs;
  final RxList<StockOutItem> _selectedItems = <StockOutItem>[].obs;
  final Rx<StockOut?> _selectedOrder = Rx<StockOut?>(null);

  // 🔧 分离加载状态：付款单列表加载状态
  final RxBool _isOrderListLoading = false.obs;
  // 🔧 分离加载状态：商品明细加载状态
  final RxBool _isItemListLoading = false.obs;

  @override
  void initState() {
    super.initState();
    _loadPaidOrders();
  }

  @override
  void dispose() {
    _orderNoController.dispose();
    _barcodeController.dispose();
    _orderTableScrollController.dispose();
    _itemTableScrollController.dispose();
    super.dispose();
  }

  /// 加载已收款的出库单
  Future<void> _loadPaidOrders() async {
    try {
      _isOrderListLoading.value = true; // 🔧 使用分离的加载状态
      LoggerService.d('🔍 加载已收款出库单，门店ID: ${widget.storeId}');

      // 查询已收款的出库单 (status=2, payment_status=1)
      final result = await _stockService.getStockOutList({
        'page': 1,
        'page_size': 100,
        'store_id': widget.storeId,
        'payment_status': 1, // 已收款
        'status': 2, // 已审核通过
      });

      _paidOrderList.value = result.data;
      LoggerService.d('✅ 加载到 ${result.data.length} 个已收款出库单');

      // 调试：检查第一条记录的字段信息
      if (result.data.isNotEmpty) {
        final firstOrder = result.data.first;
        LoggerService.d('🔍 第一条出库单详细信息:');
        LoggerService.d('   单号: ${firstOrder.stockOutNo}');
        LoggerService.d('   操作人: ${firstOrder.operatorName}');
        LoggerService.d('   收款时间: ${firstOrder.paymentTime}');
        LoggerService.d('   应付金额: ${firstOrder.totalAmount}');
        LoggerService.d('   实付金额: ${firstOrder.actualAmount}');
        LoggerService.d('   现金金额: ${firstOrder.cashAmount}');
        LoggerService.d('   微信金额: ${firstOrder.wechatAmount}');
        LoggerService.d('   支付宝金额: ${firstOrder.alipayAmount}');
        LoggerService.d('   抹零金额: ${firstOrder.discountAmount}');
      }

    } catch (e) {
      LoggerService.e('❌ 加载已收款出库单失败', e);
      Get.snackbar(
        '错误',
        '加载已收款出库单失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      _isOrderListLoading.value = false; // 🔧 使用分离的加载状态
    }
  }

  /// 搜索出库单
  void _searchOrders() {
    final keyword = _orderNoController.text.trim();
    if (keyword.isEmpty) {
      _loadPaidOrders();
      return;
    }

    // 根据销售单号筛选
    final filteredOrders = _paidOrderList.where((order) {
      return order.stockOutNo.toLowerCase().contains(keyword.toLowerCase());
    }).toList();

    _paidOrderList.value = filteredOrders;
  }

  /// 根据条码搜索出库单
  void _searchByBarcode() {
    final barcode = _barcodeController.text.trim();
    if (barcode.isEmpty) {
      Get.snackbar('提示', '请输入商品条码');
      return;
    }

    // TODO: 实现根据条码查找包含该商品的出库单
    Get.snackbar('提示', '条码搜索功能开发中...');
  }

  /// 选择出库单并加载商品明细
  Future<void> _selectOrder(StockOut order) async {
    try {
      LoggerService.d('🔍 开始加载出库单商品明细');
      LoggerService.d('📋 出库单信息: ID=${order.id}, 单号=${order.stockOutNo}, 门店ID=${order.storeId}');

      // 🔧 关键修复：检查是否已经选中，避免重复操作
      final wasSelected = _selectedOrder.value?.id == order.id;
      if (wasSelected) {
        LoggerService.d('⚠️ 该出库单已经选中，跳过重复操作');
        return;
      }

      // 🔧 关键修复：先设置选中状态，这只会影响选中状态的UI更新
      _selectedOrder.value = order;
      LoggerService.d('✅ 已设置选中状态: ${order.stockOutNo}');

      // 🔧 关键修复：设置商品明细加载状态，不影响付款单表格
      _isItemListLoading.value = true;

      // 获取出库单详情（包含商品明细）
      LoggerService.d('🌐 调用API: getStockOutById(${order.id})');
      final orderDetail = await _stockService.getStockOutById(order.id);

      LoggerService.d('📦 API返回数据: ${orderDetail.toJson()}');
      LoggerService.d('📝 商品明细原始数据: ${orderDetail.items?.map((item) => item.toJson()).toList()}');

      // 设置商品明细数据
      final items = orderDetail.items ?? [];
      _orderItemList.value = items;

      LoggerService.d('✅ 商品明细加载完成: ${items.length} 个商品');
      if (items.isNotEmpty) {
        LoggerService.d('🔍 第一个商品详情: ${items.first.toJson()}');
        LoggerService.d('💎 第一个商品首饰信息: ${items.first.jewelry?.toJson()}');
      }

      // 清空之前的选择
      _selectedItems.clear();
      LoggerService.d('🧹 已清空之前的选择');

    } catch (e, stackTrace) {
      LoggerService.e('❌ 加载商品明细失败', e);
      LoggerService.e('📍 错误堆栈', stackTrace);
      _orderItemList.clear(); // 清空列表避免显示错误数据
      Get.snackbar(
        '错误',
        '加载商品明细失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      _isItemListLoading.value = false; // 🔧 使用分离的加载状态
      LoggerService.d('🏁 商品明细加载流程结束');
    }
  }

  /// 切换商品选择状态
  void _toggleItemSelection(StockOutItem item) {
    final index = _selectedItems.indexWhere((selected) => selected.id == item.id);
    if (index >= 0) {
      _selectedItems.removeAt(index);
    } else {
      _selectedItems.add(item);
    }
  }

  /// 确认退换货
  void _confirmReturnExchange() {
    if (_selectedItems.isEmpty) {
      Get.snackbar('提示', '请选择要退换货的商品');
      return;
    }

    // 返回选中的商品列表
    Get.back(result: _selectedItems.toList());
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题
            Row(
              children: [
                const Icon(Icons.keyboard_return, color: Colors.red),
                const SizedBox(width: 8),
                const Text(
                  '退换货',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Get.back(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const Divider(),

            // 搜索区域
            _buildSearchSection(),
            const SizedBox(height: 16),

            // 内容区域 - 改为上下分层布局
            Expanded(
              child: Column(
                children: [
                  // 上半部分：付款单信息
                  Expanded(
                    flex: 1,
                    child: _buildOrderListSection(),
                  ),
                  const SizedBox(height: 16),
                  // 下半部分：商品清单
                  Expanded(
                    flex: 1,
                    child: _buildItemListSection(),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // 底部按钮
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Get.back(),
                  child: const Text('取消'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _confirmReturnExchange,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue[600],
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('确定'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建搜索区域
  Widget _buildSearchSection() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: AppBorderStyles.standardBoxDecoration,
      child: Row(
        children: [
          // 销售单号搜索
          Expanded(
            child: Container(
              height: 32,
              decoration: AppBorderStyles.standardBoxDecoration,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                child: TextField(
                  controller: _orderNoController,
                  decoration: const InputDecoration(
                    hintText: '销售单号',
                    border: InputBorder.none,
                    enabledBorder: InputBorder.none,
                    focusedBorder: InputBorder.none,
                    disabledBorder: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(vertical: 8),
                    isDense: true,
                  ),
                  style: const TextStyle(fontSize: 13),
                  onChanged: (_) => _searchOrders(),
                ),
              ),
            ),
          ),
          const SizedBox(width: 8),

          // 商品条码搜索
          Expanded(
            child: Container(
              height: 32,
              decoration: AppBorderStyles.standardBoxDecoration,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                child: TextField(
                  controller: _barcodeController,
                  decoration: const InputDecoration(
                    hintText: '商品条码',
                    border: InputBorder.none,
                    enabledBorder: InputBorder.none,
                    focusedBorder: InputBorder.none,
                    disabledBorder: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(vertical: 8),
                    isDense: true,
                  ),
                  style: const TextStyle(fontSize: 13),
                ),
              ),
            ),
          ),
          const SizedBox(width: 8),

          // 搜索按钮
          SizedBox(
            height: 32,
            child: ElevatedButton(
              onPressed: _searchByBarcode,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green[600],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 0),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
                ),
                textStyle: const TextStyle(fontSize: 13),
              ),
              child: const Text('搜索'),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建付款单信息区域
  Widget _buildOrderListSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '付款单信息',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Expanded(
          child: Container(
            decoration: AppBorderStyles.standardBoxDecoration,
            child: Column(
              children: [
                // 表头 - 静态部分，不需要响应式更新
                Container(
                  height: 40,
                  decoration: const BoxDecoration(
                    color: AppBorderStyles.tableHeaderBackground,
                    border: Border(
                      bottom: AppBorderStyles.tableBorder,
                    ),
                  ),
                  child: Row(
                    children: [
                      _buildOrderHeaderCell('销售单号', '17%'),
                      _buildOrderHeaderCell('操作人', '10%'),
                      _buildOrderHeaderCell('收款时间', '15%'),
                      _buildOrderHeaderCell('应付金额', '11%'),
                      _buildOrderHeaderCell('实付金额', '11%'),
                      _buildOrderHeaderCell('抹零', '9%'),
                      _buildOrderHeaderCell('现金', '9%'),
                      _buildOrderHeaderCell('微信', '8%'),
                      _buildOrderHeaderCell('支付宝', '8%'),
                    ],
                  ),
                ),
                // 数据行 - 🔧 关键修复：分离响应式更新，只监听必要的状态
                Expanded(
                  child: Obx(() {
                    LoggerService.d('🎨 付款单表格UI刷新: 订单列表长度=${_paidOrderList.length}, 加载状态=${_isOrderListLoading.value}');

                    if (_isOrderListLoading.value) {
                      LoggerService.d('⏳ 付款单列表加载中...');
                      return const Center(child: CircularProgressIndicator());
                    }

                    if (_paidOrderList.isEmpty) {
                      LoggerService.d('📭 付款单列表为空');
                      return const Center(
                        child: Text('暂无已收款的出库单'),
                      );
                    }

                    LoggerService.d('📋 开始渲染付款单表格，订单数量: ${_paidOrderList.length}');

                    return ListView.builder(
                      controller: _orderTableScrollController, // 🔧 滚动控制器
                      itemCount: _paidOrderList.length,
                      itemBuilder: (context, index) {
                        final order = _paidOrderList[index];

                        return Obx(() {
                          // 🔧 关键修复：选中状态单独监听，避免整个列表重建
                          final isSelected = _selectedOrder.value?.id == order.id;

                          return Container(
                            height: 38,
                            decoration: BoxDecoration(
                              color: isSelected ? Colors.blue[50] : (index % 2 == 0 ? Colors.white : Colors.grey[50]),
                              border: const Border(
                                bottom: AppBorderStyles.tableBorder,
                              ),
                            ),
                            child: InkWell(
                              onTap: () {
                                LoggerService.d('📍 点击付款单: ${order.stockOutNo}');
                                // 🔧 简化点击处理，直接调用选择方法
                                _selectOrder(order);
                              },
                              child: Row(
                                children: [
                                  _buildOrderDataCell(order.stockOutNo, '17%'),
                                  _buildOrderDataCell(order.operatorName ?? '未知', '10%'),
                                  _buildOrderDataCell(
                                    order.paymentTime != null
                                      ? _formatDateTime(order.paymentTime!)
                                      : '未收款',
                                    '15%'
                                  ),
                                  _buildOrderDataCell('¥${order.totalAmount.toStringAsFixed(0)}', '11%'),
                                  _buildOrderDataCell('¥${(order.actualAmount ?? 0).toStringAsFixed(0)}', '11%'),
                                  _buildOrderDataCell('¥${(order.discountAmount ?? 0).toStringAsFixed(0)}', '9%'),
                                  _buildOrderDataCell('¥${(order.cashAmount ?? 0).toStringAsFixed(0)}', '9%'),
                                  _buildOrderDataCell('¥${(order.wechatAmount ?? 0).toStringAsFixed(0)}', '8%'),
                                  _buildOrderDataCell('¥${(order.alipayAmount ?? 0).toStringAsFixed(0)}', '8%'),
                                ],
                              ),
                            ),
                          );
                        });
                      },
                    );
                  }),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// 构建商品清单区域
  Widget _buildItemListSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '商品清单',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Expanded(
          child: Container(
            decoration: AppBorderStyles.standardBoxDecoration,
            child: Obx(() {
              LoggerService.d('🎨 商品清单UI刷新: 选中订单=${_selectedOrder.value?.stockOutNo}, 商品数量=${_orderItemList.length}');

              if (_selectedOrder.value == null) {
                LoggerService.d('⚠️ 未选择付款单，显示提示信息');
                return const Center(
                  child: Text('请先选择付款单'),
                );
              }

              if (_isItemListLoading.value) { // 🔧 使用分离的加载状态
                LoggerService.d('⏳ 正在加载商品明细...');
                return const Center(
                  child: CircularProgressIndicator(),
                );
              }

              if (_orderItemList.isEmpty) {
                LoggerService.d('📭 商品明细为空，显示无数据提示');
                return const Center(
                  child: Text('该出库单没有商品明细'),
                );
              }

              LoggerService.d('📋 开始渲染商品明细表格，商品数量: ${_orderItemList.length}');

              return Column(
                children: [
                  // 表头
                  Container(
                    height: 40,
                    decoration: const BoxDecoration(
                      color: AppBorderStyles.tableHeaderBackground,
                      border: Border(
                        bottom: AppBorderStyles.tableBorder,
                      ),
                    ),
                    child: Row(
                      children: [
                        _buildHeaderCell('选择', '5%'),
                        _buildHeaderCell('条码', '8%'),
                        _buildHeaderCell('商品名称', '14%'),
                        _buildHeaderCell('圈口号', '6%'),
                        _buildHeaderCell('金重', '7%'),
                        _buildHeaderCell('金价', '8%'),
                        _buildHeaderCell('银重', '7%'),
                        _buildHeaderCell('银价', '8%'),
                        _buildHeaderCell('总重', '7%'),
                        _buildHeaderCell('工费', '8%'),
                        _buildHeaderCell('件工费', '8%'),
                        _buildHeaderCell('价格', '8%'),
                      ],
                    ),
                  ),
                  // 数据行
                  Expanded(
                    child: ListView.builder(
                      controller: _itemTableScrollController, // 修复问题2：添加滚动控制器
                      itemCount: _orderItemList.length,
                      itemBuilder: (context, index) {
                        final item = _orderItemList[index];

                        return Obx(() {
                          // 🔧 关键修复：将选择状态检查包装在Obx中，确保UI响应状态变化
                          final isSelected = _selectedItems.any((selected) => selected.id == item.id);

                          return Container(
                            height: 38,
                            decoration: BoxDecoration(
                              color: isSelected ? Colors.green[50] : (index % 2 == 0 ? Colors.white : Colors.grey[50]),
                              border: const Border(
                                bottom: AppBorderStyles.tableBorder,
                              ),
                            ),
                            child: Row(
                              children: [
                                _buildDataCell(
                                  Checkbox(
                                    value: isSelected,
                                    onChanged: (_) => _toggleItemSelection(item),
                                  ),
                                  '5%',
                                ),
                                _buildDataCell(item.barcode ?? '', '8%'),
                                _buildDataCell(item.jewelry?.name ?? '', '14%'),
                                _buildDataCell(item.jewelry?.ringSize ?? '-', '6%'),
                                _buildDataCell('${item.jewelry?.goldWeight ?? 0}g', '7%'),
                                _buildDataCell('¥${item.goldPrice.toStringAsFixed(0)}/g', '8%'),
                                _buildDataCell('${item.jewelry?.silverWeight ?? 0}g', '7%'),
                                _buildDataCell('¥${item.silverPrice.toStringAsFixed(0)}/g', '8%'),
                                _buildDataCell('${item.totalWeight.toStringAsFixed(1)}g', '7%'),
                                _buildDataCell('¥${item.workPrice.toStringAsFixed(0)}', '8%'),
                                _buildDataCell('¥${item.pieceWorkPrice.toStringAsFixed(0)}', '8%'),
                                _buildDataCell('¥${item.amount.toStringAsFixed(0)}', '8%'),
                              ],
                            ),
                          );
                        });
                      },
                    ),
                  ),
                ],
              );
            }),
          ),
        ),
      ],
    );
  }

  /// 构建表头单元格
  Widget _buildHeaderCell(String text, String widthPercent) {
    return Container(
      width: MediaQuery.of(context).size.width * 0.9 * (double.parse(widthPercent.replaceAll('%', '')) / 100),
      padding: const EdgeInsets.symmetric(horizontal: 4),
      alignment: Alignment.center,
      child: Text(
        text,
        style: const TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 14,
          color: Colors.black87,
        ),
        textAlign: TextAlign.center,
        overflow: TextOverflow.ellipsis,
        maxLines: 1,
      ),
    );
  }

  /// 构建数据单元格
  Widget _buildDataCell(dynamic content, String widthPercent) {
    return Container(
      width: MediaQuery.of(context).size.width * 0.9 * (double.parse(widthPercent.replaceAll('%', '')) / 100),
      padding: const EdgeInsets.symmetric(horizontal: 4),
      alignment: Alignment.center,
      child: content is Widget ? content : Text(
        content.toString(),
        style: const TextStyle(fontSize: 13),
        textAlign: TextAlign.center,
        overflow: TextOverflow.ellipsis,
        maxLines: 1,
      ),
    );
  }

  /// 构建付款单表头单元格
  Widget _buildOrderHeaderCell(String text, String widthPercent) {
    return Container(
      width: MediaQuery.of(context).size.width * 0.9 * (double.parse(widthPercent.replaceAll('%', '')) / 100),
      padding: const EdgeInsets.symmetric(horizontal: 4),
      alignment: Alignment.center,
      child: Text(
        text,
        style: const TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 14,
          color: Colors.black87,
        ),
        textAlign: TextAlign.center,
        overflow: TextOverflow.ellipsis,
        maxLines: 1,
      ),
    );
  }

  /// 构建付款单数据单元格
  Widget _buildOrderDataCell(String text, String widthPercent) {
    return Container(
      width: MediaQuery.of(context).size.width * 0.9 * (double.parse(widthPercent.replaceAll('%', '')) / 100),
      padding: const EdgeInsets.symmetric(horizontal: 4),
      alignment: Alignment.center,
      child: Text(
        text,
        style: const TextStyle(fontSize: 13),
        textAlign: TextAlign.center,
        overflow: TextOverflow.ellipsis,
        maxLines: 1,
      ),
    );
  }

  /// 格式化日期时间
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
           '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
