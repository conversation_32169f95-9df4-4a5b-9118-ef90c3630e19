-- 为fa_store_transfer表添加收款相关字段
-- 执行时间：2024年12月
-- 说明：为库存调拨功能添加收款结算支持

-- 添加收款相关字段
ALTER TABLE `fa_store_transfer` 
ADD COLUMN `payment_status` SMALLINT DEFAULT 0 COMMENT '收款状态:0=未收款,1=已收款' AFTER `remark`,
ADD COLUMN `payment_time` INT(10) UNSIGNED NULL COMMENT '收款时间' AFTER `payment_status`,
ADD COLUMN `payment_method` VARCHAR(20) NULL COMMENT '收款方式' AFTER `payment_time`,
ADD COLUMN `payment_remark` VARCHAR(255) NULL COMMENT '支付备注' AFTER `payment_method`,
ADD COLUMN `cash_amount` DECIMAL(10,2) DEFAULT 0.00 COMMENT '现金金额' AFTER `payment_remark`,
ADD COLUMN `wechat_amount` DECIMAL(10,2) DEFAULT 0.00 COMMENT '微信金额' AFTER `cash_amount`,
ADD COLUMN `alipay_amount` DECIMAL(10,2) DEFAULT 0.00 COMMENT '支付宝金额' AFTER `wechat_amount`,
ADD COLUMN `card_amount` DECIMAL(10,2) DEFAULT 0.00 COMMENT '刷卡金额' AFTER `alipay_amount`,
ADD COLUMN `discount_amount` DECIMAL(10,2) DEFAULT 0.00 COMMENT '抹零金额' AFTER `card_amount`,
ADD COLUMN `actual_amount` DECIMAL(10,2) DEFAULT 0.00 COMMENT '实收金额' AFTER `discount_amount`;

-- 添加索引以提高查询性能
ALTER TABLE `fa_store_transfer` 
ADD INDEX `idx_payment_status` (`payment_status`),
ADD INDEX `idx_payment_time` (`payment_time`);

-- 验证字段是否添加成功
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM 
    INFORMATION_SCHEMA.COLUMNS 
WHERE 
    TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'fa_store_transfer' 
    AND COLUMN_NAME IN (
        'payment_status', 'payment_time', 'payment_method', 'payment_remark',
        'cash_amount', 'wechat_amount', 'alipay_amount', 'card_amount',
        'discount_amount', 'actual_amount'
    )
ORDER BY ORDINAL_POSITION;
