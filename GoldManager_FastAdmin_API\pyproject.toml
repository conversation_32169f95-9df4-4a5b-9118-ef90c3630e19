[tool.ruff]
# 基础配置
target-version = "py38"
line-length = 100
indent-width = 4

# 选择检查规则
select = [
    "E",      # pycodestyle errors
    "W",      # pycodestyle warnings
    "F",      # Pyflakes
    "UP",     # pyupgrade
    "B",      # flake8-bugbear
    "SIM",    # flake8-simplify
    "I",      # isort
    "N",      # pep8-naming
    "C90",    # mccabe complexity
    "BLE",    # flake8-blind-except
    "FBT",    # flake8-boolean-trap
    "A",      # flake8-builtins
    "C4",     # flake8-comprehensions
    "T10",    # flake8-debugger
    "EM",     # flake8-errmsg
    "ISC",    # flake8-implicit-str-concat
    "ICN",    # flake8-import-conventions
    "G",      # flake8-logging-format
    "PIE",    # flake8-pie
    "T20",    # flake8-print
    "PT",     # flake8-pytest-style
    "Q",      # flake8-quotes
    "RSE",    # flake8-raise
    "RET",    # flake8-return
    "SLF",    # flake8-self
    "SLOT",   # flake8-slots
    "TID",    # flake8-tidy-imports
    "TCH",    # flake8-type-checking
    "ARG",    # flake8-unused-arguments
    "PTH",    # flake8-use-pathlib
    "ERA",    # eradicate
    "PD",     # pandas-vet
    "PGH",    # pygrep-hooks
    "PL",     # Pylint
    "TRY",    # tryceratops
    "FLY",    # flynt
    "NPY",    # NumPy-specific rules
    "PERF",   # Perflint
    "RUF",    # Ruff-specific rules
]

# 忽略的规则
ignore = [
    "E501",   # line too long, handled by formatter
    "B008",   # do not perform function calls in argument defaults
    "C901",   # too complex
    "PLR0913", # too many arguments
    "PD901",  # df is a bad variable name
    "G004",   # logging statement uses f-string
    "TRY003", # avoid specifying long messages outside the exception class
    "TRY301", # abstract raise to an inner function
    "PLR0911", # too many return statements
    "PLR0912", # too many branches
    "PLR0915", # too many statements
    "RET504", # unnecessary variable assignment before return
    "ARG002", # unused method argument
    "PTH123", # use Path.open() instead of open()
    "SLF001", # private member accessed
    "N805",   # first argument of method should be self
    "N806",   # variable should be lowercase
    "UP037",  # remove quotes from type annotation
]

# 排除的文件和目录
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
    "jewelry_env",
    "jewelry_env_mac",
    "logs",
    "ssl",
    "sql"
]

# 每个文件的最大错误数
per-file-ignores = {}

# 允许修复的规则
fixable = ["ALL"]
unfixable = []

# 虚拟环境不检查
respect-gitignore = true

[tool.ruff.format]
# 格式化配置
quote-style = "double"
indent-style = "space"
skip-magic-trailing-comma = false
line-ending = "auto"

[tool.ruff.lint.isort]
# import 排序配置
known-first-party = ["app"]
force-single-line = false
combine-as-imports = true
split-on-trailing-comma = true

[tool.ruff.lint.mccabe]
# 复杂度限制
max-complexity = 10

[tool.ruff.lint.pep8-naming]
# 命名约定
classmethod-decorators = ["classmethod"]
staticmethod-decorators = ["staticmethod"]

[tool.ruff.lint.flake8-quotes]
# 引号配置
docstring-quotes = "double"
inline-quotes = "double"
multiline-quotes = "double"

[tool.ruff.lint.flake8-builtins]
# 内置名称检查
builtins-ignorelist = ["id", "type", "input", "filter", "format"]

[tool.ruff.lint.pylint]
# Pylint配置
max-args = 8
max-branches = 12
max-returns = 6
max-statements = 50

[tool.ruff.lint.pyupgrade]
# Python升级建议
keep-runtime-typing = true