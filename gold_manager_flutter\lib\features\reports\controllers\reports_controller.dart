import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../models/report_model.dart';
import '../services/report_service.dart';

/// 报表统计控制器
class ReportsController extends GetxController with GetSingleTickerProviderStateMixin {
  // 服务实例
  final ReportService _reportService = ReportService();
  
  // Tab控制器
  late TabController tabController;
  
  // 加载状态
  final RxBool isLoading = false.obs;
  
  // 日期范围选择
  final Rx<DateTimeRange> dateRange = Rx<DateTimeRange>(
    DateTimeRange(
      start: DateTime.now().subtract(const Duration(days: 30)),
      end: DateTime.now(),
    ),
  );
  
  // 报表数据
  final Rx<SalesReportData> salesReport = Rx<SalesReportData>(SalesReportData());
  final Rx<InventoryReportData> inventoryReport = Rx<InventoryReportData>(InventoryReportData());
  final Rx<RecyclingReportData> recyclingReport = Rx<RecyclingReportData>(RecyclingReportData());
  
  // 过滤条件
  final RxInt storeFilter = 0.obs; // 0表示全部门店
  final RxInt categoryFilter = 0.obs; // 0表示全部分类
  
  @override
  void onInit() {
    super.onInit();
    
    // 初始化Tab控制器
    tabController = TabController(length: 3, vsync: this);
    
    // 加载初始数据
    loadSalesReport();
    
    // 监听Tab变化
    tabController.addListener(_handleTabChange);
  }
  
  @override
  void onClose() {
    tabController.dispose();
    super.onClose();
  }
  
  /// 处理Tab变化
  void _handleTabChange() {
    if (!tabController.indexIsChanging) {
      return;
    }
    
    switch (tabController.index) {
      case 0:
        loadSalesReport();
        break;
      case 1:
        loadInventoryReport();
        break;
      case 2:
        loadRecyclingReport();
        break;
    }
  }
  
  /// 设置日期范围
  Future<void> setDateRange(DateTimeRange range) async {
    dateRange.value = range;
    
    // 重新加载当前Tab的数据
    switch (tabController.index) {
      case 0:
        await loadSalesReport();
        break;
      case 1:
        await loadInventoryReport();
        break;
      case 2:
        await loadRecyclingReport();
        break;
    }
  }
  
  /// 设置门店过滤器
  Future<void> setStoreFilter(int storeId) async {
    storeFilter.value = storeId;
    
    // 重新加载当前Tab的数据
    switch (tabController.index) {
      case 0:
        await loadSalesReport();
        break;
      case 1:
        await loadInventoryReport();
        break;
      case 2:
        await loadRecyclingReport();
        break;
    }
  }
  
  /// 设置分类过滤器
  Future<void> setCategoryFilter(int categoryId) async {
    categoryFilter.value = categoryId;
    
    // 重新加载当前Tab的数据
    switch (tabController.index) {
      case 0:
        await loadSalesReport();
        break;
      case 1:
        await loadInventoryReport();
        break;
      case 2:
        await loadRecyclingReport();
        break;
    }
  }
  
  /// 加载销售报表数据
  Future<void> loadSalesReport() async {
    try {
      isLoading.value = true;
      
      final params = {
        'start_date': DateFormat('yyyy-MM-dd').format(dateRange.value.start),
        'end_date': DateFormat('yyyy-MM-dd').format(dateRange.value.end),
      };
      
      if (storeFilter.value > 0) {
        params['store_id'] = storeFilter.value.toString();
      }
      
      if (categoryFilter.value > 0) {
        params['category_id'] = categoryFilter.value.toString();
      }
      
      final data = await _reportService.getSalesReport(params);
      salesReport.value = data;
    } catch (e) {
      print('加载销售报表失败: $e');
      Get.snackbar('加载失败', '加载销售报表数据失败，请稍后重试',
          snackPosition: SnackPosition.BOTTOM);
    } finally {
      isLoading.value = false;
    }
  }
  
  /// 加载库存报表数据
  Future<void> loadInventoryReport() async {
    try {
      isLoading.value = true;
      
      final params = {
        'date': DateFormat('yyyy-MM-dd').format(dateRange.value.end),
      };
      
      if (storeFilter.value > 0) {
        params['store_id'] = storeFilter.value.toString();
      }
      
      if (categoryFilter.value > 0) {
        params['category_id'] = categoryFilter.value.toString();
      }
      
      final data = await _reportService.getInventoryReport(params);
      inventoryReport.value = data;
    } catch (e) {
      print('加载库存报表失败: $e');
      Get.snackbar('加载失败', '加载库存报表数据失败，请稍后重试',
          snackPosition: SnackPosition.BOTTOM);
    } finally {
      isLoading.value = false;
    }
  }
  
  /// 加载回收报表数据
  Future<void> loadRecyclingReport() async {
    try {
      isLoading.value = true;
      
      final params = {
        'start_date': DateFormat('yyyy-MM-dd').format(dateRange.value.start),
        'end_date': DateFormat('yyyy-MM-dd').format(dateRange.value.end),
      };
      
      if (storeFilter.value > 0) {
        params['store_id'] = storeFilter.value.toString();
      }
      
      final data = await _reportService.getRecyclingReport(params);
      recyclingReport.value = data;
    } catch (e) {
      print('加载回收报表失败: $e');
      Get.snackbar('加载失败', '加载回收报表数据失败，请稍后重试',
          snackPosition: SnackPosition.BOTTOM);
    } finally {
      isLoading.value = false;
    }
  }
  
  /// 导出报表数据
  Future<bool> exportReport(String type) async {
    try {
      isLoading.value = true;
      
      final params = {
        'start_date': DateFormat('yyyy-MM-dd').format(dateRange.value.start),
        'end_date': DateFormat('yyyy-MM-dd').format(dateRange.value.end),
        'type': type,
      };
      
      if (storeFilter.value > 0) {
        params['store_id'] = storeFilter.value.toString();
      }
      
      if (categoryFilter.value > 0) {
        params['category_id'] = categoryFilter.value.toString();
      }
      
      final success = await _reportService.exportReport(params);
      
      if (success) {
        Get.snackbar('导出成功', '报表数据已成功导出',
            snackPosition: SnackPosition.BOTTOM);
      } else {
        Get.snackbar('导出失败', '报表数据导出失败，请稍后重试',
            snackPosition: SnackPosition.BOTTOM);
      }
      
      return success;
    } catch (e) {
      print('导出报表失败: $e');
      Get.snackbar('导出失败', '报表数据导出失败，请稍后重试',
          snackPosition: SnackPosition.BOTTOM);
      return false;
    } finally {
      isLoading.value = false;
    }
  }
} 