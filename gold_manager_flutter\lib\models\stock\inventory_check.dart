import 'package:equatable/equatable.dart';
import 'package:get/get.dart';
import '../store/store.dart';
import '../user/user.dart';
import 'inventory_check_item.dart';

/// 库存盘点单模型
class InventoryCheck extends Equatable {
  /// 盘点单ID
  final int id;
  
  /// 盘点单号
  final String checkNo;
  
  /// 门店ID
  final int storeId;
  
  /// 门店信息
  final Store? store;
  
  /// 状态：0=进行中，1=已完成，2=已取消
  final int status;
  
  /// 开始时间
  final DateTime? startTime;
  
  /// 结束时间
  final DateTime? endTime;
  
  /// 操作员ID
  final int operatorId;
  
  /// 操作员信息
  final User? operator;
  
  /// 应盘总数
  final int totalCount;
  
  /// 已盘数量
  final int checkedCount;
  
  /// 差异数量
  final int differenceCount;
  
  /// 备注
  final String? remark;
  
  /// 创建时间
  final DateTime createTime;
  
  /// 更新时间
  final DateTime updateTime;
  
  /// 盘点明细列表
  final List<InventoryCheckItem>? items;

  const InventoryCheck({
    required this.id,
    required this.checkNo,
    required this.storeId,
    this.store,
    required this.status,
    this.startTime,
    this.endTime,
    required this.operatorId,
    this.operator,
    required this.totalCount,
    required this.checkedCount,
    required this.differenceCount,
    this.remark,
    required this.createTime,
    required this.updateTime,
    this.items,
  });

  /// 状态文本
  String get statusText {
    switch (status) {
      case 0:
        return '进行中';
      case 1:
        return '已完成';
      case 2:
        return '已取消';
      default:
        return '未知';
    }
  }

  /// 状态颜色
  String get statusColor {
    switch (status) {
      case 0:
        return 'blue';
      case 1:
        return 'green';
      case 2:
        return 'red';
      default:
        return 'grey';
    }
  }

  /// 盘点进度百分比
  double get progress {
    if (totalCount == 0) return 0.0;
    return checkedCount / totalCount;
  }

  /// 盘点进度文本
  String get progressText {
    return '$checkedCount/$totalCount';
  }

  /// 是否可以编辑
  bool get canEdit {
    return status == 0; // 只有进行中状态可以编辑
  }

  /// 是否可以删除
  bool get canDelete {
    return status == 0 || status == 2; // 进行中或已取消状态可以删除
  }

  /// 是否可以完成盘点
  bool get canComplete {
    return status == 0 && checkedCount == totalCount;
  }

  /// 是否可以取消盘点
  bool get canCancel {
    return status == 0;
  }

  /// 获取门店名称
  String get storeName {
    if (store?.name != null && store!.name.isNotEmpty) {
      return store!.name;
    }
    return '未知门店';
  }

  /// 获取操作员名称
  String get operatorName {
    if (operator?.nickname != null && operator!.nickname!.isNotEmpty) {
      return operator!.nickname!;
    }
    if (operator?.name != null && operator!.name.isNotEmpty) {
      return operator!.name;
    }
    if (operator?.username != null && operator!.username.isNotEmpty) {
      return operator!.username;
    }
    return '未知';
  }

  /// 从JSON创建实例
  factory InventoryCheck.fromJson(Map<String, dynamic> json) {
    // 🔧 修复：处理门店信息，优先使用完整的store对象，如果没有则从store_name创建简单对象
    Store? store;
    if (json['store'] != null) {
      // 如果有完整的store对象，直接使用
      store = Store.fromJson(json['store']);
    } else if (json['store_name'] != null && json['store_name'].toString().isNotEmpty) {
      // 如果只有store_name，创建一个简单的Store对象
      store = Store(
        id: json['store_id'] ?? 0,
        name: json['store_name'] ?? '',
      );
    }

    // 🔧 修复：处理操作员信息，优先使用完整的operator对象，如果没有则从operator_name创建简单对象
    User? operator;
    if (json['operator'] != null) {
      // 如果有完整的operator对象，直接使用
      operator = User.fromJson(json['operator']);
    } else if (json['operator_name'] != null && json['operator_name'].toString().isNotEmpty) {
      // 如果只有operator_name，创建一个简单的User对象
      operator = User(
        id: json['operator_id'] ?? 0,
        username: json['operator_name'] ?? '',
        name: json['operator_name'] ?? '',
        nickname: json['operator_name'] ?? '',
        role: 'user', // 默认角色
        storeId: json['store_id'] ?? 0,
        permissions: <String>[].obs, // 空权限列表
      );
    }

    return InventoryCheck(
      id: json['id'] ?? 0,
      checkNo: json['check_no'] ?? '',
      storeId: json['store_id'] ?? 0,
      store: store, // 使用修复后的门店对象
      status: json['status'] ?? 0,
      startTime: json['start_time'] != null
          ? DateTime.fromMillisecondsSinceEpoch((json['start_time'] as int) * 1000)
          : null,
      endTime: json['end_time'] != null
          ? DateTime.fromMillisecondsSinceEpoch((json['end_time'] as int) * 1000)
          : null,
      operatorId: json['operator_id'] ?? 0,
      operator: operator, // 使用修复后的操作员对象
      totalCount: json['total_count'] ?? 0,
      checkedCount: json['checked_count'] ?? 0,
      differenceCount: json['difference_count'] ?? 0,
      remark: json['remark'],
      createTime: DateTime.fromMillisecondsSinceEpoch((json['createtime'] ?? 0) * 1000),
      updateTime: DateTime.fromMillisecondsSinceEpoch((json['updatetime'] ?? 0) * 1000),
      items: json['items'] != null
          ? (json['items'] as List).map((item) => InventoryCheckItem.fromJson(item)).toList()
          : null,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'check_no': checkNo,
      'store_id': storeId,
      'store': store?.toJson(),
      'status': status,
      'start_time': startTime?.millisecondsSinceEpoch != null ? startTime!.millisecondsSinceEpoch ~/ 1000 : null,
      'end_time': endTime?.millisecondsSinceEpoch != null ? endTime!.millisecondsSinceEpoch ~/ 1000 : null,
      'operator_id': operatorId,
      'operator': operator?.toJson(),
      'total_count': totalCount,
      'checked_count': checkedCount,
      'difference_count': differenceCount,
      'remark': remark,
      'createtime': createTime.millisecondsSinceEpoch ~/ 1000,
      'updatetime': updateTime.millisecondsSinceEpoch ~/ 1000,
      'items': items?.map((item) => item.toJson()).toList(),
    };
  }

  /// 复制并修改部分属性
  InventoryCheck copyWith({
    int? id,
    String? checkNo,
    int? storeId,
    Store? store,
    int? status,
    DateTime? startTime,
    DateTime? endTime,
    int? operatorId,
    User? operator,
    int? totalCount,
    int? checkedCount,
    int? differenceCount,
    String? remark,
    DateTime? createTime,
    DateTime? updateTime,
    List<InventoryCheckItem>? items,
  }) {
    return InventoryCheck(
      id: id ?? this.id,
      checkNo: checkNo ?? this.checkNo,
      storeId: storeId ?? this.storeId,
      store: store ?? this.store,
      status: status ?? this.status,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      operatorId: operatorId ?? this.operatorId,
      operator: operator ?? this.operator,
      totalCount: totalCount ?? this.totalCount,
      checkedCount: checkedCount ?? this.checkedCount,
      differenceCount: differenceCount ?? this.differenceCount,
      remark: remark ?? this.remark,
      createTime: createTime ?? this.createTime,
      updateTime: updateTime ?? this.updateTime,
      items: items ?? this.items,
    );
  }

  @override
  List<Object?> get props => [
        id,
        checkNo,
        storeId,
        store,
        status,
        startTime,
        endTime,
        operatorId,
        operator,
        totalCount,
        checkedCount,
        differenceCount,
        remark,
        createTime,
        updateTime,
        items,
      ];

  @override
  String toString() {
    return 'InventoryCheck{id: $id, checkNo: $checkNo, status: $status, progress: $progressText}';
  }
}
