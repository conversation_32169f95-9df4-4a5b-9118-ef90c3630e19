"""
回收管理数据验证模型

老板，这个模块定义了回收管理的数据验证和响应模型：
1. 回收单创建、更新、响应模型
2. 回收明细相关模型
3. 统计分析模型
4. 查询参数模型

使用Pydantic v2语法，确保数据验证的准确性。
"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, ConfigDict, validator
from datetime import datetime
from decimal import Decimal


# ==================== 回收明细模型 ====================

class RecyclingItemBase(BaseModel):
    """回收明细基础模型"""
    name: str = Field(..., max_length=100, description="物品名称")
    category_id: int = Field(..., description="分类ID")
    gold_weight: Optional[Decimal] = Field(Decimal('0.00'), ge=0, description="金重(克)")
    gold_price: Optional[Decimal] = Field(Decimal('0.00'), ge=0, description="金价(克价)")
    silver_weight: Optional[Decimal] = Field(Decimal('0.00'), ge=0, description="银重(克)")
    silver_price: Optional[Decimal] = Field(Decimal('0.00'), ge=0, description="银价(克价)")
    discount_rate: Optional[Decimal] = Field(Decimal('100.00'), ge=0, le=100, description="折扣率(%)")
    remark: Optional[str] = Field(None, max_length=255, description="备注")


class RecyclingItemCreate(RecyclingItemBase):
    """创建回收明细模型"""
    pass


class RecyclingItemUpdate(BaseModel):
    """更新回收明细模型"""
    name: Optional[str] = Field(None, max_length=100, description="物品名称")
    category_id: Optional[int] = Field(None, description="分类ID")
    gold_weight: Optional[Decimal] = Field(None, ge=0, description="金重(克)")
    gold_price: Optional[Decimal] = Field(None, ge=0, description="金价(克价)")
    silver_weight: Optional[Decimal] = Field(None, ge=0, description="银重(克)")
    silver_price: Optional[Decimal] = Field(None, ge=0, description="银价(克价)")
    discount_rate: Optional[Decimal] = Field(None, ge=0, le=100, description="折扣率(%)")
    remark: Optional[str] = Field(None, max_length=255, description="备注")


class RecyclingItemResponse(RecyclingItemBase):
    """回收明细响应模型"""
    model_config = ConfigDict(from_attributes=True)
    
    id: int = Field(..., description="明细ID")
    recycling_id: int = Field(..., description="回收单ID")
    gold_amount: Decimal = Field(..., description="金价金额")
    silver_amount: Decimal = Field(..., description="银价金额")
    total_amount: Decimal = Field(..., description="总金额")
    discount_amount: Decimal = Field(..., description="折后金额")
    createtime: Optional[int] = Field(None, description="创建时间")
    
    # 关联信息
    category_name: Optional[str] = Field(None, description="分类名称")


# ==================== 回收单模型 ====================

class RecyclingBase(BaseModel):
    """回收单基础模型"""
    store_id: int = Field(..., description="门店ID")
    member_id: Optional[int] = Field(None, description="会员ID")
    customer_name: Optional[str] = Field(None, max_length=50, description="客户姓名")
    phone: Optional[str] = Field(None, max_length=20, description="联系电话")
    price: Optional[Decimal] = Field(Decimal('0.00'), ge=0, description="回收价格")
    remark: Optional[str] = Field(None, description="备注")


class RecyclingCreate(RecyclingBase):
    """创建回收单模型"""
    items: List[RecyclingItemCreate] = Field(..., min_length=1, description="回收物品明细")


class RecyclingUpdate(BaseModel):
    """更新回收单模型"""
    member_id: Optional[int] = Field(None, description="会员ID")
    customer_name: Optional[str] = Field(None, max_length=50, description="客户姓名")
    phone: Optional[str] = Field(None, max_length=20, description="联系电话")
    price: Optional[Decimal] = Field(None, ge=0, description="回收价格")
    remark: Optional[str] = Field(None, description="备注")


class RecyclingStatusUpdate(BaseModel):
    """回收单状态更新模型"""
    status: int = Field(..., ge=0, le=1, description="状态:0=作废,1=正常")
    remark: Optional[str] = Field(None, description="操作备注")


class RecyclingResponse(RecyclingBase):
    """回收单响应模型"""
    model_config = ConfigDict(from_attributes=True)

    id: int = Field(..., description="回收单ID")
    recycle_no: str = Field(..., description="回收单号")
    gold_weight: Decimal = Field(..., description="金重(克)")
    gold_price: Decimal = Field(..., description="金价(克价)")
    gold_amount: Decimal = Field(..., description="金价金额")
    silver_weight: Decimal = Field(..., description="银重(克)")
    silver_price: Decimal = Field(..., description="银价(克价)")
    silver_amount: Decimal = Field(..., description="银价金额")
    total_amount: Decimal = Field(..., description="总金额")
    discount_amount: Decimal = Field(..., description="折后总金额")
    operator_id: int = Field(..., description="操作员ID")
    status: int = Field(..., description="状态:0=作废,1=正常")
    createtime: Optional[int] = Field(None, description="创建时间")
    updatetime: Optional[int] = Field(None, description="更新时间")

    # 物品件数统计
    item_count: int = Field(0, description="回收物品件数")

    # 关联信息
    store_name: Optional[str] = Field(None, description="门店名称")
    member_name: Optional[str] = Field(None, description="会员姓名")
    operator_name: Optional[str] = Field(None, description="操作员姓名")

    # 状态描述
    status_text: Optional[str] = Field(None, description="状态描述")


class RecyclingDetailResponse(RecyclingResponse):
    """回收单详情响应模型"""
    items: List[RecyclingItemResponse] = Field(default_factory=list, description="回收明细列表")


# ==================== 查询参数模型 ====================

class RecyclingQueryParams(BaseModel):
    """回收单查询参数模型"""
    keyword: Optional[str] = Field(None, description="关键词搜索(单号、客户姓名、备注)")
    store_id: Optional[int] = Field(None, description="门店ID筛选")
    status: Optional[int] = Field(None, ge=0, le=1, description="状态筛选")
    operator_id: Optional[int] = Field(None, description="操作员ID筛选")
    member_id: Optional[int] = Field(None, description="会员ID筛选")
    start_date: Optional[str] = Field(None, pattern=r'^\d{4}-\d{2}-\d{2}$', description="开始日期")
    end_date: Optional[str] = Field(None, pattern=r'^\d{4}-\d{2}-\d{2}$', description="结束日期")
    min_amount: Optional[Decimal] = Field(None, ge=0, description="最小金额")
    max_amount: Optional[Decimal] = Field(None, ge=0, description="最大金额")


# ==================== 统计模型 ====================

class RecyclingStatistics(BaseModel):
    """回收统计模型"""
    total_recyclings: int = Field(..., description="总回收单数")
    normal_count: int = Field(..., description="正常状态数量")
    cancelled_count: int = Field(..., description="作废状态数量")
    
    total_items: int = Field(..., description="总回收物品数")
    total_gold_weight: Decimal = Field(..., description="总金重(克)")
    total_silver_weight: Decimal = Field(..., description="总银重(克)")
    total_amount: Decimal = Field(..., description="总金额")
    total_discount_amount: Decimal = Field(..., description="总折后金额")
    
    # 状态分布
    status_distribution: Dict[str, int] = Field(default_factory=dict, description="状态分布")
    
    # 门店分布
    store_distribution: List[Dict[str, Any]] = Field(default_factory=list, description="门店分布")
    
    # 分类分布
    category_distribution: List[Dict[str, Any]] = Field(default_factory=list, description="分类分布")
    
    # 金额统计
    average_amount: Decimal = Field(Decimal('0.00'), description="平均回收金额")
    max_amount: Decimal = Field(Decimal('0.00'), description="最大回收金额")
    min_amount: Decimal = Field(Decimal('0.00'), description="最小回收金额")


# ==================== 批量操作模型 ====================

class RecyclingBatchUpdate(BaseModel):
    """批量更新回收单模型"""
    recycling_ids: List[int] = Field(..., min_length=1, description="回收单ID列表")
    status: int = Field(..., ge=0, le=1, description="状态:0=作废,1=正常")
    remark: Optional[str] = Field(None, description="操作备注")


# ==================== 价格计算模型 ====================

class RecyclingPriceCalculation(BaseModel):
    """回收价格计算模型"""
    gold_weight: Decimal = Field(..., ge=0, description="金重(克)")
    gold_price: Decimal = Field(..., ge=0, description="金价(克价)")
    silver_weight: Decimal = Field(..., ge=0, description="银重(克)")
    silver_price: Decimal = Field(..., ge=0, description="银价(克价)")
    discount_rate: Decimal = Field(Decimal('100.00'), ge=0, le=100, description="折扣率(%)")


class RecyclingPriceResult(BaseModel):
    """回收价格计算结果模型"""
    gold_amount: Decimal = Field(..., description="金价金额")
    silver_amount: Decimal = Field(..., description="银价金额")
    total_amount: Decimal = Field(..., description="总金额")
    discount_amount: Decimal = Field(..., description="折后金额")
    discount_value: Decimal = Field(..., description="折扣金额")
