import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

import '../../../core/constants/border_styles.dart';
import '../../../models/print/print_template_config.dart';
import '../controllers/print_template_editor_controller.dart';
import '../widgets/print_template_preview.dart';
import '../widgets/pdf_template_preview.dart';
import '../widgets/coordinate_input.dart';

/// 打印模板编辑器视图
/// 
/// 提供可视化的模板编辑界面，支持实时预览
class PrintTemplateEditorView extends StatelessWidget {
  final PrintTemplateConfig? template;

  const PrintTemplateEditorView({
    super.key,
    this.template,
  });

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(PrintTemplateEditorController(originalTemplate: template));

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Row(
          children: [
            const Icon(Icons.edit, size: 24),
            const SizedBox(width: 8),
            Text(template == null ? '新建模板' : '编辑模板'),
          ],
        ),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        elevation: 1,
        actions: [
          // 预览按钮
          Padding(
            padding: const EdgeInsets.only(right: 8),
            child: OutlinedButton.icon(
              onPressed: () => controller.previewTemplate(),
              icon: const Icon(Icons.preview, size: 18),
              label: const Text('预览'),
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.blue[600],
                side: BorderSide(color: Colors.blue[600]!),
              ),
            ),
          ),
          // 保存按钮
          Padding(
            padding: const EdgeInsets.only(right: 16),
            child: Obx(() => ElevatedButton.icon(
              onPressed: controller.canSave.value ? () => controller.saveTemplate() : null,
              icon: const Icon(Icons.save, size: 18),
              label: const Text('保存'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue[600],
                foregroundColor: Colors.white,
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            )),
          ),
        ],
      ),
      body: Row(
        children: [
          // 左侧编辑面板
          Expanded(
            flex: 3,
            child: _buildEditPanel(controller),
          ),
          
          // 分割线
          Container(
            width: 1,
            color: AppBorderStyles.borderColor,
          ),
          
          // 右侧预览面板
          Expanded(
            flex: 2,
            child: _buildPreviewPanel(controller),
          ),
        ],
      ),
    );
  }

  /// 构建编辑面板
  Widget _buildEditPanel(PrintTemplateEditorController controller) {
    return Container(
      color: Colors.white,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 基本信息
            _buildBasicInfoSection(controller),
            const SizedBox(height: 32),
            
            // 公司信息配置
            _buildCompanyInfoSection(controller),
            const SizedBox(height: 32),
            
            // 页面设置
            _buildPageConfigSection(controller),
            const SizedBox(height: 32),
            
            // 表头配置
            _buildHeaderConfigSection(controller),
            const SizedBox(height: 32),
            
            // 商品明细表格配置
            _buildItemTableConfigSection(controller),
            const SizedBox(height: 32),
            
            // 汇总信息配置
            _buildSummaryConfigSection(controller),
            const SizedBox(height: 32),
            
            // 收款信息配置
            _buildPaymentInfoConfigSection(controller),
            const SizedBox(height: 32),
            
            // 页脚配置
            _buildFooterConfigSection(controller),
          ],
        ),
      ),
    );
  }

  /// 构建预览面板
  Widget _buildPreviewPanel(PrintTemplateEditorController controller) {
    return Container(
      color: Colors.grey[100],
      child: Column(
        children: [
          // 预览头部
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              color: Colors.white,
              border: Border(
                bottom: BorderSide(color: AppBorderStyles.borderColor),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.preview, color: Colors.blue[600], size: 20),
                const SizedBox(width: 8),
                Obx(() => Text(
                  controller.useRealPreview.value ? 'PDF预览' : '编辑预览',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                )),
                const SizedBox(width: 16),
                // 预览模式切换
                Obx(() => ToggleButtons(
                  isSelected: [!controller.useRealPreview.value, controller.useRealPreview.value],
                  onPressed: (index) {
                    controller.useRealPreview.value = index == 1;
                  },
                  borderRadius: BorderRadius.circular(4),
                  constraints: const BoxConstraints(minHeight: 32, minWidth: 60),
                  textStyle: const TextStyle(fontSize: 11),
                  children: const [
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 8),
                      child: Text('编辑'),
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 8),
                      child: Text('PDF'),
                    ),
                  ],
                )),
                const Spacer(),
                OutlinedButton.icon(
                  onPressed: () => controller.previewTemplate(),
                  icon: const Icon(Icons.fullscreen, size: 16),
                  label: const Text('全屏预览'),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    textStyle: const TextStyle(fontSize: 12),
                  ),
                ),
              ],
            ),
          ),

          // 预览内容
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Obx(() => controller.useRealPreview.value
                  ? PdfTemplatePreview(
                      template: controller.buildCurrentTemplate(),
                      sampleData: controller.getSampleData(),
                      scale: controller.previewScale.value,
                    )
                  : PrintTemplatePreview(
                      template: controller.buildCurrentTemplate(),
                      sampleData: controller.getSampleData(),
                      onFieldPositionChanged: controller.updateFieldPosition,
                      enableDrag: true,
                      scale: controller.previewScale.value,
                    )),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建基本信息配置
  Widget _buildBasicInfoSection(PrintTemplateEditorController controller) {
    return _buildSection(
      title: '基本信息',
      icon: Icons.info_outline,
      children: [
        _buildTextField(
          label: '模板名称',
          controller: controller.nameController,
          required: true,
          hint: '请输入模板名称',
        ),
        const SizedBox(height: 16),
        _buildTextField(
          label: '模板描述',
          controller: controller.descriptionController,
          hint: '请输入模板描述（可选）',
          maxLines: 3,
        ),
      ],
    );
  }

  /// 构建公司信息配置
  Widget _buildCompanyInfoSection(PrintTemplateEditorController controller) {
    return _buildSection(
      title: '公司信息',
      icon: Icons.business,
      children: [
        _buildTextField(
          label: '公司名称',
          controller: controller.companyNameController,
          required: true,
          hint: '请输入公司名称',
        ),
        const SizedBox(height: 12),
        // 公司名称位置控制
        Obx(() => CoordinateInput(
          label: '公司名称',
          position: controller.getFieldPosition('company_name'),
          onPositionChanged: (position) => controller.updateFieldPosition('company_name', position),
        )),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: Obx(() => _buildSwitchTile(
                title: '显示地址',
                value: controller.showAddress.value,
                onChanged: (value) => controller.showAddress.value = value,
              )),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Obx(() => _buildSwitchTile(
                title: '显示电话',
                value: controller.showPhone.value,
                onChanged: (value) => controller.showPhone.value = value,
              )),
            ),
          ],
        ),
        const SizedBox(height: 12),
        // 地址和电话位置控制
        Row(
          children: [
            Expanded(
              child: Obx(() => CoordinateInput(
                label: '地址',
                position: controller.getFieldPosition('company_address'),
                onPositionChanged: (position) => controller.updateFieldPosition('company_address', position),
                enabled: controller.showAddress.value,
              )),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Obx(() => CoordinateInput(
                label: '电话',
                position: controller.getFieldPosition('company_phone'),
                onPositionChanged: (position) => controller.updateFieldPosition('company_phone', position),
                enabled: controller.showPhone.value,
              )),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: Obx(() => _buildSliderField(
                label: '字体大小',
                value: controller.companyFontSize.value,
                min: 12.0,
                max: 24.0,
                divisions: 12,
                onChanged: (value) => controller.companyFontSize.value = value,
              )),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Obx(() => _buildSwitchTile(
                title: '加粗显示',
                value: controller.companyBold.value,
                onChanged: (value) => controller.companyBold.value = value,
              )),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建页面设置配置
  Widget _buildPageConfigSection(PrintTemplateEditorController controller) {
    return _buildSection(
      title: '页面设置',
      icon: Icons.settings,
      children: [
        Row(
          children: [
            Expanded(
              child: Obx(() => _buildSliderField(
                label: '页面宽度 (mm)',
                value: controller.pageWidth.value,
                min: 150.0,
                max: 300.0,
                divisions: 15,
                onChanged: (value) => controller.pageWidth.value = value,
              )),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Obx(() => _buildSliderField(
                label: '页面高度 (mm)',
                value: controller.pageHeight.value,
                min: 80.0,
                max: 200.0,
                divisions: 12,
                onChanged: (value) => controller.pageHeight.value = value,
              )),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: Obx(() => _buildSliderField(
                label: '上边距 (mm)',
                value: controller.marginTop.value,
                min: 1.0,
                max: 10.0,
                divisions: 9,
                onChanged: (value) => controller.marginTop.value = value,
              )),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Obx(() => _buildSliderField(
                label: '下边距 (mm)',
                value: controller.marginBottom.value,
                min: 1.0,
                max: 10.0,
                divisions: 9,
                onChanged: (value) => controller.marginBottom.value = value,
              )),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建表头配置
  Widget _buildHeaderConfigSection(PrintTemplateEditorController controller) {
    return _buildSection(
      title: '表头信息',
      icon: Icons.view_headline,
      children: [
        Row(
          children: [
            Expanded(
              child: Obx(() => _buildSwitchTile(
                title: '显示单号',
                value: controller.showOrderNo.value,
                onChanged: (value) => controller.showOrderNo.value = value,
              )),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Obx(() => _buildSwitchTile(
                title: '显示客户',
                value: controller.showCustomer.value,
                onChanged: (value) => controller.showCustomer.value = value,
              )),
            ),
          ],
        ),
        const SizedBox(height: 12),
        // 单号和客户位置控制
        Row(
          children: [
            Expanded(
              child: Obx(() => CoordinateInput(
                label: '单号',
                position: controller.getFieldPosition('order_no'),
                onPositionChanged: (position) => controller.updateFieldPosition('order_no', position),
                enabled: controller.showOrderNo.value,
              )),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Obx(() => CoordinateInput(
                label: '客户',
                position: controller.getFieldPosition('customer'),
                onPositionChanged: (position) => controller.updateFieldPosition('customer', position),
                enabled: controller.showCustomer.value,
              )),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: Obx(() => _buildSwitchTile(
                title: '显示销售类型',
                value: controller.showSaleType.value,
                onChanged: (value) => controller.showSaleType.value = value,
              )),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Obx(() => _buildSwitchTile(
                title: '显示日期时间',
                value: controller.showDateTime.value,
                onChanged: (value) => controller.showDateTime.value = value,
              )),
            ),
          ],
        ),
        const SizedBox(height: 12),
        // 销售类型和日期时间位置控制
        Row(
          children: [
            Expanded(
              child: Obx(() => CoordinateInput(
                label: '销售类型',
                position: controller.getFieldPosition('sale_type'),
                onPositionChanged: (position) => controller.updateFieldPosition('sale_type', position),
                enabled: controller.showSaleType.value,
              )),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Obx(() => CoordinateInput(
                label: '日期时间',
                position: controller.getFieldPosition('date_time'),
                onPositionChanged: (position) => controller.updateFieldPosition('date_time', position),
                enabled: controller.showDateTime.value,
              )),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建商品明细表格配置
  Widget _buildItemTableConfigSection(PrintTemplateEditorController controller) {
    return _buildSection(
      title: '商品明细表格',
      icon: Icons.table_chart,
      children: [
        const Text(
          '选择要显示的列：',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 12),
        Obx(() => Wrap(
          spacing: 8,
          runSpacing: 8,
          children: controller.availableColumns.map((column) {
            final isSelected = controller.selectedColumns.contains(column);
            return FilterChip(
              label: Text(column),
              selected: isSelected,
              onSelected: (selected) {
                if (selected) {
                  controller.selectedColumns.add(column);
                } else {
                  controller.selectedColumns.remove(column);
                }
              },
              selectedColor: Colors.blue[100],
              checkmarkColor: Colors.blue[600],
            );
          }).toList(),
        )),
        const SizedBox(height: 16),
        // 表格位置控制
        Obx(() => CoordinateInput(
          label: '表格',
          position: controller.getFieldPosition('item_table'),
          onPositionChanged: (position) => controller.updateFieldPosition('item_table', position),
        )),
        const SizedBox(height: 16),
        // 表格总宽度配置
        Obx(() => _buildTableWidthField(
          label: '表格总宽度(mm)',
          value: controller.tableTotalWidth.value,
          onChanged: (value) => controller.tableTotalWidth.value = value,
        )),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: Obx(() => _buildSliderField(
                label: '表头字体大小',
                value: controller.tableHeaderFontSize.value,
                min: 8.0,
                max: 14.0,
                divisions: 6,
                onChanged: (value) => controller.tableHeaderFontSize.value = value,
              )),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Obx(() => _buildSliderField(
                label: '内容字体大小',
                value: controller.contentFontSize.value,
                min: 7.0,
                max: 12.0,
                divisions: 5,
                onChanged: (value) => controller.contentFontSize.value = value,
              )),
            ),
          ],
        ),
        const SizedBox(height: 16),
        // 表格行高配置
        Obx(() => _buildSliderField(
          label: '表格行高(mm)',
          value: controller.rowHeight.value,
          min: 10.0,
          max: 30.0,
          divisions: 20,
          onChanged: (value) => controller.rowHeight.value = value,
        )),
      ],
    );
  }

  /// 构建汇总信息配置
  Widget _buildSummaryConfigSection(PrintTemplateEditorController controller) {
    return _buildSection(
      title: '汇总信息',
      icon: Icons.summarize,
      children: [
        Row(
          children: [
            Expanded(
              child: Obx(() => _buildSwitchTile(
                title: '显示合计数量',
                value: controller.showTotalQuantity.value,
                onChanged: (value) => controller.showTotalQuantity.value = value,
              )),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Obx(() => _buildSwitchTile(
                title: '显示合计重量',
                value: controller.showTotalWeight.value,
                onChanged: (value) => controller.showTotalWeight.value = value,
              )),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Obx(() => _buildSwitchTile(
          title: '显示合计金额',
          value: controller.showTotalAmount.value,
          onChanged: (value) => controller.showTotalAmount.value = value,
        )),
      ],
    );
  }

  /// 构建收款信息配置
  Widget _buildPaymentInfoConfigSection(PrintTemplateEditorController controller) {
    return _buildSection(
      title: '收款信息',
      icon: Icons.payment,
      children: [
        Row(
          children: [
            Expanded(
              child: Obx(() => _buildSwitchTile(
                title: '显示收款方式',
                value: controller.showPaymentMethod.value,
                onChanged: (value) => controller.showPaymentMethod.value = value,
              )),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Obx(() => _buildSwitchTile(
                title: '显示收款明细',
                value: controller.showPaymentDetails.value,
                onChanged: (value) => controller.showPaymentDetails.value = value,
              )),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Obx(() => _buildSwitchTile(
          title: '显示找零金额',
          value: controller.showChangeAmount.value,
          onChanged: (value) => controller.showChangeAmount.value = value,
        )),
      ],
    );
  }

  /// 构建页脚配置
  Widget _buildFooterConfigSection(PrintTemplateEditorController controller) {
    return _buildSection(
      title: '页脚信息',
      icon: Icons.notes,
      children: [
        _buildTextField(
          label: '自定义页脚文本',
          controller: controller.customFooterController,
          hint: '请输入自定义页脚文本（可选）',
          maxLines: 2,
        ),
        const SizedBox(height: 16),
        Obx(() => _buildSwitchTile(
          title: '显示打印时间',
          value: controller.showPrintTime.value,
          onChanged: (value) => controller.showPrintTime.value = value,
        )),
      ],
    );
  }



  /// 构建配置区域
  Widget _buildSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppBorderStyles.borderColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 20, color: Colors.blue[600]),
              const SizedBox(width: 8),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...children,
        ],
      ),
    );
  }

  /// 构建文本输入框
  Widget _buildTextField({
    required String label,
    required TextEditingController controller,
    String? hint,
    bool required = false,
    int maxLines = 1,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
            if (required) ...[
              const SizedBox(width: 4),
              const Text(
                '*',
                style: TextStyle(color: Colors.red),
              ),
            ],
          ],
        ),
        const SizedBox(height: 8),
        TextField(
          controller: controller,
          maxLines: maxLines,
          decoration: InputDecoration(
            hintText: hint,
            border: const OutlineInputBorder(),
            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
        ),
      ],
    );
  }

  /// 构建开关组件
  Widget _buildSwitchTile({
    required String title,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        border: Border.all(color: AppBorderStyles.borderColor),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black87,
              ),
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: Colors.blue[600],
          ),
        ],
      ),
    );
  }

  /// 构建滑块组件
  Widget _buildSliderField({
    required String label,
    required double value,
    required double min,
    required double max,
    required int divisions,
    required ValueChanged<double> onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
            Text(
              value.toStringAsFixed(1),
              style: TextStyle(
                fontSize: 14,
                color: Colors.blue[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Slider(
          value: value,
          min: min,
          max: max,
          divisions: divisions,
          onChanged: onChanged,
          activeColor: Colors.blue[600],
        ),
      ],
    );
  }

  /// 构建表格宽度输入字段
  Widget _buildTableWidthField({
    required String label,
    required double? value,
    required ValueChanged<double?> onChanged,
  }) {
    return _TableWidthInput(
      label: label,
      value: value,
      onChanged: onChanged,
    );
  }
}

/// 表格宽度输入组件
///
/// 使用StatefulWidget来正确管理TextEditingController的生命周期
class _TableWidthInput extends StatefulWidget {
  final String label;
  final double? value;
  final ValueChanged<double?> onChanged;

  const _TableWidthInput({
    required this.label,
    required this.value,
    required this.onChanged,
  });

  @override
  State<_TableWidthInput> createState() => _TableWidthInputState();
}

class _TableWidthInputState extends State<_TableWidthInput> {
  late TextEditingController _controller;
  late String _currentText;

  @override
  void initState() {
    super.initState();
    _currentText = widget.value?.toStringAsFixed(1) ?? '';
    _controller = TextEditingController(text: _currentText);
  }

  @override
  void didUpdateWidget(_TableWidthInput oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 只有当外部值真正改变时才更新文本
    final newText = widget.value?.toStringAsFixed(1) ?? '';
    if (newText != _currentText && !_controller.selection.isValid) {
      // 只在没有焦点时更新，避免干扰用户输入
      _currentText = newText;
      _controller.text = newText;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _handleTextChange(String text) {
    _currentText = text;
    if (text.isEmpty) {
      widget.onChanged(null);
    } else {
      final parsedValue = double.tryParse(text);
      if (parsedValue != null && parsedValue > 0) {
        widget.onChanged(parsedValue);
      }
    }
  }

  void _clearInput() {
    _controller.clear();
    _currentText = '';
    widget.onChanged(null);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _controller,
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
                  // 防止多个小数点
                  TextInputFormatter.withFunction((oldValue, newValue) {
                    final text = newValue.text;
                    if (text.isEmpty) return newValue;

                    // 检查小数点数量
                    final dotCount = text.split('.').length - 1;
                    if (dotCount > 1) {
                      return oldValue;
                    }

                    // 检查是否为有效数字格式
                    if (double.tryParse(text) == null && text != '.') {
                      return oldValue;
                    }

                    return newValue;
                  }),
                ],
                decoration: InputDecoration(
                  hintText: '留空使用默认列宽',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                ),
                onChanged: _handleTextChange,
                enableInteractiveSelection: true,
                autocorrect: false,
                enableSuggestions: false,
              ),
            ),
            const SizedBox(width: 8),
            IconButton(
              onPressed: _clearInput,
              icon: const Icon(Icons.clear),
              tooltip: '清除',
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          widget.value != null
              ? '当前设置: ${widget.value!.toStringAsFixed(1)}mm，各列将按比例分配'
              : '未设置总宽度，使用默认列宽配置',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }
}
