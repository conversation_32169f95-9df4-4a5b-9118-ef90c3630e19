# GoldManager 仪表盘UI设计规范

## 📋 设计原则

### 1. 视觉一致性
- 100%复用现有UI组件和样式规范
- 保持与库存管理、销售管理等模块的视觉统一
- 使用统一的AppBorderStyles和AppTheme

### 2. 响应式设计
- 支持桌面(>1200px)、平板(768-1200px)、移动(<768px)三种布局
- 使用ResponsiveBuilder进行自适应布局
- 字体大小和组件尺寸根据屏幕宽度调整

### 3. 数据层次
- 核心指标优先展示(顶部概览卡片)
- 趋势图表居中展示(中部图表区域)
- 详细统计补充展示(底部统计区域)

## 🎨 布局结构

### 整体布局
```
┌─────────────────────────────────────────────────────┐
│                  页面标题栏                          │
├─────────────────────────────────────────────────────┤
│              核心指标卡片区域 (4个)                   │
├─────────────────────────────────────────────────────┤
│                图表展示区域                          │
│  ┌─────────────────┐  ┌─────────────────┐          │
│  │   销售趋势图     │  │   商品排行榜     │          │
│  └─────────────────┘  └─────────────────┘          │
├─────────────────────────────────────────────────────┤
│              详细统计区域 (多个统计卡片)               │
└─────────────────────────────────────────────────────┘
```

### 响应式布局
- **桌面端**: 4列核心指标 + 2列图表 + 4列详细统计
- **平板端**: 2列核心指标 + 1列图表 + 2列详细统计
- **移动端**: 1列布局，垂直排列所有组件

## 🧩 组件规范

### 1. 核心指标卡片 (DashboardMetricCard)

#### 设计规格
```dart
// 尺寸规格
height: 120px (桌面) / 100px (移动)
padding: EdgeInsets.all(16)
borderRadius: AppBorderStyles.mediumBorderRadius (8px)

// 颜色方案
background: Colors.white
border: AppBorderStyles.borderColor (#E0E0E0)
shadow: AppBorderStyles.elevatedBoxDecoration
```

#### 内容结构
```
┌─────────────────────────────┐
│ 📊 图标    标题              │
│                             │
│ 主要数值 (大字体)            │
│ 副标题/单位 (小字体)         │
│                             │
│ 📈 趋势指示器 (+12.5%)      │
└─────────────────────────────┘
```

#### 样式配置
```dart
// 标题样式
titleStyle: TextStyle(
  fontSize: 14,
  fontWeight: FontWeight.w500,
  color: Colors.black87,
)

// 主要数值样式
valueStyle: TextStyle(
  fontSize: 24,
  fontWeight: FontWeight.bold,
  color: AppTheme.primaryColor,
)

// 趋势指示器样式
trendStyle: TextStyle(
  fontSize: 12,
  fontWeight: FontWeight.w600,
  color: Colors.green[700], // 上升趋势
  // color: Colors.red[700], // 下降趋势
)
```

### 2. 图表容器 (DashboardChartCard)

#### 设计规格
```dart
// 尺寸规格
height: 300px
padding: EdgeInsets.all(16)
borderRadius: AppBorderStyles.mediumBorderRadius

// 装饰样式
decoration: AppBorderStyles.elevatedBoxDecoration.copyWith(
  color: Colors.white,
)
```

#### 标题栏设计
```dart
// 图表标题栏
Row(
  mainAxisAlignment: MainAxisAlignment.spaceBetween,
  children: [
    // 左侧标题和图标
    Row(
      children: [
        Icon(chartIcon, size: 20, color: AppTheme.primaryColor),
        SizedBox(width: 8),
        Text(title, style: chartTitleStyle),
      ],
    ),
    // 右侧操作按钮(可选)
    IconButton(
      icon: Icon(Icons.more_vert),
      onPressed: onMorePressed,
    ),
  ],
)
```

### 3. 统计项组件 (DashboardStatItem)

#### 三种样式变体

##### A. 紧凑型 (用于详细统计区域)
```dart
Container(
  padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
  decoration: BoxDecoration(
    color: Colors.white,
    borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
    border: Border.all(color: AppBorderStyles.borderColor),
  ),
  child: Row(
    mainAxisSize: MainAxisSize.min,
    children: [
      Icon(icon, size: 16, color: iconColor),
      SizedBox(width: 8),
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(label, style: compactLabelStyle),
          Text(value, style: compactValueStyle),
        ],
      ),
    ],
  ),
)
```

##### B. 卡片型 (用于重要统计)
```dart
Container(
  padding: EdgeInsets.all(16),
  decoration: AppBorderStyles.standardBoxDecoration.copyWith(
    color: backgroundColor,
  ),
  child: Column(
    children: [
      Icon(icon, size: 24, color: iconColor),
      SizedBox(height: 8),
      Text(value, style: cardValueStyle),
      Text(label, style: cardLabelStyle),
    ],
  ),
)
```

##### C. 行内型 (用于快速概览)
```dart
Row(
  mainAxisSize: MainAxisSize.min,
  children: [
    Icon(icon, size: 16, color: iconColor),
    SizedBox(width: 4),
    Text('$label: ', style: inlineLabelStyle),
    Text(value, style: inlineValueStyle),
  ],
)
```

## 🎯 颜色系统

### 主题色彩
```dart
// 主色调
primaryColor: Color(0xFF1E88E5)    // 蓝色
secondaryColor: Color(0xFF26A69A)  // 青色

// 功能色彩
successColor: Color(0xFF388E3C)    // 绿色 (正向指标)
warningColor: Color(0xFFFFA000)    // 橙色 (警告指标)
errorColor: Color(0xFFD32F2F)      // 红色 (负向指标)
infoColor: Color(0xFF2196F3)       // 蓝色 (信息指标)

// 背景色彩
backgroundColor: Color(0xFFF5F5F5)  // 页面背景
cardBackground: Color(0xFFFFFFFF)  // 卡片背景
```

### 指标色彩映射
```dart
// 销售相关 - 绿色系
salesColors: {
  primary: Colors.green[700],
  background: Colors.green[50],
  border: Colors.green[200],
}

// 库存相关 - 蓝色系
inventoryColors: {
  primary: Colors.blue[700],
  background: Colors.blue[50],
  border: Colors.blue[200],
}

// 回收相关 - 橙色系
recyclingColors: {
  primary: Colors.orange[700],
  background: Colors.orange[50],
  border: Colors.orange[200],
}

// 财务相关 - 紫色系
financialColors: {
  primary: Colors.purple[700],
  background: Colors.purple[50],
  border: Colors.purple[200],
}
```

## 📱 响应式规范

### 断点定义
```dart
// 屏幕断点
static const double mobileBreakpoint = 768;
static const double tabletBreakpoint = 1200;

// 组件尺寸
static const Map<String, double> cardHeights = {
  'mobile': 100,
  'tablet': 110,
  'desktop': 120,
};

static const Map<String, double> chartHeights = {
  'mobile': 250,
  'tablet': 280,
  'desktop': 300,
};
```

### 网格布局
```dart
// 核心指标卡片网格
int getMetricCardColumns(double screenWidth) {
  if (screenWidth > tabletBreakpoint) return 4;  // 桌面: 4列
  if (screenWidth > mobileBreakpoint) return 2;  // 平板: 2列
  return 1;  // 移动: 1列
}

// 图表区域网格
int getChartColumns(double screenWidth) {
  if (screenWidth > tabletBreakpoint) return 2;  // 桌面: 2列
  return 1;  // 平板/移动: 1列
}
```

## 🔧 实现要点

### 1. 组件复用
- 复用现有`_buildStatItem`方法的设计模式
- 使用`AppBorderStyles`统一边框样式
- 继承`ResponsiveBuilder`响应式设计

### 2. 性能优化
- 使用`GetX`响应式状态管理
- 实现图表数据懒加载
- 合理使用`Obx`包装响应式组件

### 3. 可维护性
- 组件化设计，便于复用和维护
- 统一的样式常量，便于主题切换
- 清晰的文件结构和命名规范

## 📂 文件结构

```
lib/features/dashboard/
├── views/
│   ├── dashboard_view.dart           # 主仪表盘页面
│   └── widgets/
│       ├── metric_card.dart          # 核心指标卡片
│       ├── chart_card.dart           # 图表容器
│       ├── stat_item.dart            # 统计项组件
│       └── dashboard_grid.dart       # 响应式网格布局
├── controllers/
│   └── dashboard_controller.dart     # 仪表盘控制器
└── models/
    └── dashboard_data.dart           # 仪表盘数据模型
```

这个设计规范确保了仪表盘与现有系统的完美集成，同时提供了灵活的扩展能力。
