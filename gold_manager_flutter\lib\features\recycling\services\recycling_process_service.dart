import 'package:get/get.dart';
import '../../../core/config/app_config.dart';
import '../../../core/services/api_service.dart';
import '../../../models/recycling/recycling_process.dart';
import '../../../models/recycling/recycling_item.dart';
import '../../../models/common/enums.dart';

/// 旧料处理服务类
class RecyclingProcessService extends GetxService {
  final ApiService _apiService = Get.find<ApiService>();
  
  /// 根据回收物品ID获取处理记录列表
  Future<List<RecyclingProcess>> getProcessList(int recyclingItemId) async {
    try {
      final response = await _apiService.get(
        '${AppConfig.apiEndpoint['recycling']}/process/index',
        queryParameters: {'recycling_item_id': recyclingItemId},
      );
      
      if (response.data['code'] == 1) {
        final List<dynamic> list = response.data['data'];
        return list.map((item) => RecyclingProcess.fromJson(item)).toList();
      } else {
        Get.log('获取处理记录失败: ${response.data['msg']}', isError: true);
        return [];
      }
    } catch (e) {
      Get.log('获取处理记录异常: $e', isError: true);
      return [];
    }
  }
  
  /// 获取处理记录详情
  Future<RecyclingProcess?> getProcessDetail(int id) async {
    try {
      final response = await _apiService.get(
        '${AppConfig.apiEndpoint['recycling']}/process/detail',
        queryParameters: {'id': id},
      );
      
      if (response.data['code'] == 1) {
        return RecyclingProcess.fromJson(response.data['data']);
      } else {
        Get.log('获取处理记录详情失败: ${response.data['msg']}', isError: true);
        return null;
      }
    } catch (e) {
      Get.log('获取处理记录详情异常: $e', isError: true);
      return null;
    }
  }
  
  /// 添加处理记录
  Future<bool> addProcess(RecyclingProcess process) async {
    try {
      final response = await _apiService.post(
        '${AppConfig.apiEndpoint['recycling']}/process/add',
        data: process.toJson(),
      );
      
      if (response.data['code'] == 1) {
        return true;
      } else {
        Get.log('添加处理记录失败: ${response.data['msg']}', isError: true);
        return false;
      }
    } catch (e) {
      Get.log('添加处理记录异常: $e', isError: true);
      return false;
    }
  }
  
  /// 更新处理记录
  Future<bool> updateProcess(RecyclingProcess process) async {
    try {
      final response = await _apiService.post(
        '${AppConfig.apiEndpoint['recycling']}/process/edit',
        data: process.toJson(),
      );
      
      if (response.data['code'] == 1) {
        return true;
      } else {
        Get.log('更新处理记录失败: ${response.data['msg']}', isError: true);
        return false;
      }
    } catch (e) {
      Get.log('更新处理记录异常: $e', isError: true);
      return false;
    }
  }
  
  /// 删除处理记录
  Future<bool> deleteProcess(int id) async {
    try {
      final response = await _apiService.post(
        '${AppConfig.apiEndpoint['recycling']}/process/delete',
        data: {'id': id},
      );
      
      if (response.data['code'] == 1) {
        return true;
      } else {
        Get.log('删除处理记录失败: ${response.data['msg']}', isError: true);
        return false;
      }
    } catch (e) {
      Get.log('删除处理记录异常: $e', isError: true);
      return false;
    }
  }
  
  /// 更新处理记录状态
  Future<bool> updateProcessStatus(int id, int status) async {
    try {
      final response = await _apiService.post(
        '${AppConfig.apiEndpoint['recycling']}/process/status',
        data: {'id': id, 'status': status},
      );
      
      if (response.data['code'] == 1) {
        return true;
      } else {
        Get.log('更新处理记录状态失败: ${response.data['msg']}', isError: true);
        return false;
      }
    } catch (e) {
      Get.log('更新处理记录状态异常: $e', isError: true);
      return false;
    }
  }
  
  /// 生成处理单号
  String generateProcessNo() {
    final now = DateTime.now();
    final dateStr = '${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}';
    final randomStr = (now.millisecondsSinceEpoch % 10000).toString().padLeft(4, '0');
    return 'PC$dateStr$randomStr';
  }
  
  /// 获取处理方式文本说明
  String getProcessTypeDescription(RecyclingProcessType type) {
    switch (type.value) {
      case 'sell':
        return '直接卖出：将收购的旧料直接按照金/银价出售，无需额外处理。';
      case 'separate':
        return '金银分离：通过物理或化学方法将混合金银首饰分离成纯金和纯银。';
      case 'repair':
        return '维修再销售：对旧首饰进行维修、清洗、抛光后作为二手首饰销售。';
      case 'resell':
        return '直接二次销售：不进行处理，直接以二手首饰形式销售。';
      default:
        return '待选择处理方式';
    }
  }
  
  /// 根据处理类型和回收物品计算预估收益
  double calculateEstimatedIncome(RecyclingProcessType type, RecyclingItem item) {
    switch (type.value) {
      case 'sell':
        // 直接卖出，按照金银市场价计算
        final goldValue = item.goldWeight * AppConfig.defaultGoldPrice;
        final silverValue = item.silverWeight * AppConfig.defaultSilverPrice;
        return goldValue + silverValue;
      case 'separate':
        // 金银分离，考虑到分离损耗和成本
        final goldValue = item.goldWeight * AppConfig.defaultGoldPrice * 0.95; // 假设分离后金的纯度为95%
        final silverValue = item.silverWeight * AppConfig.defaultSilverPrice * 0.9; // 假设分离后银的纯度为90%
        return goldValue + silverValue;
      case 'repair':
        // 维修再销售，价值会提升
        return item.amount * 1.5; // 假设维修后价值提升50%
      case 'resell':
        // 直接二次销售，价值略有提升
        return item.amount * 1.3; // 假设直接二次销售价值提升30%
      default:
        return 0.0;
    }
  }
  
  /// 根据处理类型和回收物品计算处理成本
  double calculateProcessCost(RecyclingProcessType type, RecyclingItem item) {
    switch (type.value) {
      case 'sell':
        // 直接卖出，几乎没有额外成本
        return 0.0;
      case 'separate':
        // 金银分离，有一定的分离成本
        final totalWeight = item.goldWeight + item.silverWeight;
        return totalWeight * 10; // 假设每克处理成本为10元
      case 'repair':
        // 维修再销售，有维修成本
        return item.amount * 0.2; // 假设维修成本是原价的20%
      case 'resell':
        // 直接二次销售，有简单处理成本
        return item.amount * 0.05; // 假设处理成本是原价的5%
      default:
        return 0.0;
    }
  }
} 