import 'package:get/get.dart';
import '../services/storage_service.dart';
import 'logger.dart';

/// 条码生成器工具类
/// 用于生成各种类型的条码
class BarcodeGenerator {
  static final StorageService _storage = Get.find<StorageService>();
  
  /// 生成旧料回收条码
  /// 格式：9YYMMDD## (9+年月日+从01开始的递增数字)
  /// 例如：924120101, 924120102, ...
  static Future<String> generateOldMaterialBarcode() async {
    try {
      final now = DateTime.now();
      final datePrefix = '9${now.year.toString().substring(2)}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}';
      
      // 获取今日已生成的条码计数
      final countKey = 'old_material_barcode_count_$datePrefix';
      int count = _storage.getInt(countKey) ?? 0;

      // 递增计数
      count++;

      // 生成完整条码
      final barcode = '$datePrefix${count.toString().padLeft(2, '0')}';

      // 保存新的计数
      await _storage.setInt(countKey, count);
      
      LoggerService.d('生成旧料回收条码: $barcode (今日第$count个)');
      
      return barcode;
    } catch (e) {
      LoggerService.e('生成旧料回收条码失败', e);
      // 如果生成失败，返回一个基于时间戳的备用条码
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      return '9${timestamp.toString().substring(timestamp.toString().length - 8)}';
    }
  }
  
  /// 生成普通商品条码
  /// 格式：JWYYMMDDHHMM### (JW+年月日时分+3位递增数字)
  /// 例如：JW2412010930001, JW2412010930002, ...
  static Future<String> generateJewelryBarcode() async {
    try {
      final now = DateTime.now();
      final dateTimePrefix = 'JW${now.year.toString().substring(2)}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}${now.hour.toString().padLeft(2, '0')}${now.minute.toString().padLeft(2, '0')}';
      
      // 获取当前时间段已生成的条码计数
      final countKey = 'jewelry_barcode_count_$dateTimePrefix';
      int count = _storage.getInt(countKey) ?? 0;

      // 递增计数
      count++;

      // 生成完整条码
      final barcode = '$dateTimePrefix${count.toString().padLeft(3, '0')}';

      // 保存新的计数
      await _storage.setInt(countKey, count);
      
      LoggerService.d('生成商品条码: $barcode (当前时间段第$count个)');
      
      return barcode;
    } catch (e) {
      LoggerService.e('生成商品条码失败', e);
      // 如果生成失败，返回一个基于时间戳的备用条码
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      return 'JW${timestamp.toString().substring(timestamp.toString().length - 10)}';
    }
  }
  
  /// 验证条码格式
  /// 检查条码是否符合系统规范
  static bool validateBarcode(String barcode) {
    if (barcode.isEmpty) return false;
    
    // 验证旧料回收条码格式：9YYMMDD##
    if (barcode.startsWith('9') && barcode.length == 9) {
      final regex = RegExp(r'^9\d{8}$');
      return regex.hasMatch(barcode);
    }
    
    // 验证普通商品条码格式：JWYYMMDDHHMM###
    if (barcode.startsWith('JW') && barcode.length == 15) {
      final regex = RegExp(r'^JW\d{13}$');
      return regex.hasMatch(barcode);
    }
    
    // 其他自定义条码格式（长度在6-20之间的字母数字组合）
    if (barcode.length >= 6 && barcode.length <= 20) {
      final regex = RegExp(r'^[A-Za-z0-9]+$');
      return regex.hasMatch(barcode);
    }
    
    return false;
  }
  
  /// 检查条码是否为旧料回收条码
  static bool isOldMaterialBarcode(String barcode) {
    return barcode.startsWith('9') && barcode.length == 9;
  }
  
  /// 检查条码是否为普通商品条码
  static bool isJewelryBarcode(String barcode) {
    return barcode.startsWith('JW') && barcode.length == 15;
  }
  
  /// 解析条码信息
  /// 返回条码的类型和生成时间信息
  static Map<String, dynamic> parseBarcodeInfo(String barcode) {
    final result = <String, dynamic>{
      'type': 'unknown',
      'isValid': false,
      'generatedDate': null,
      'sequence': null,
    };
    
    if (!validateBarcode(barcode)) {
      return result;
    }
    
    result['isValid'] = true;
    
    if (isOldMaterialBarcode(barcode)) {
      result['type'] = 'old_material';
      // 解析日期：9YYMMDD##
      final yearStr = '20${barcode.substring(1, 3)}';
      final monthStr = barcode.substring(3, 5);
      final dayStr = barcode.substring(5, 7);
      final sequenceStr = barcode.substring(7, 9);
      
      try {
        final date = DateTime(int.parse(yearStr), int.parse(monthStr), int.parse(dayStr));
        result['generatedDate'] = date;
        result['sequence'] = int.parse(sequenceStr);
      } catch (e) {
        LoggerService.w('解析旧料回收条码日期失败: $barcode');
      }
    } else if (isJewelryBarcode(barcode)) {
      result['type'] = 'jewelry';
      // 解析日期时间：JWYYMMDDHHMM###
      final yearStr = '20${barcode.substring(2, 4)}';
      final monthStr = barcode.substring(4, 6);
      final dayStr = barcode.substring(6, 8);
      final hourStr = barcode.substring(8, 10);
      final minuteStr = barcode.substring(10, 12);
      final sequenceStr = barcode.substring(12, 15);
      
      try {
        final date = DateTime(
          int.parse(yearStr),
          int.parse(monthStr),
          int.parse(dayStr),
          int.parse(hourStr),
          int.parse(minuteStr),
        );
        result['generatedDate'] = date;
        result['sequence'] = int.parse(sequenceStr);
      } catch (e) {
        LoggerService.w('解析商品条码日期失败: $barcode');
      }
    } else {
      result['type'] = 'custom';
    }
    
    return result;
  }
  
  /// 重置条码计数器（用于测试或维护）
  /// 由于StorageService没有getAllKeys方法，这里只能重置当天的计数器
  static Future<void> resetBarcodeCounters() async {
    try {
      final now = DateTime.now();
      final datePrefix = '9${now.year.toString().substring(2)}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}';
      final dateTimePrefix = 'JW${now.year.toString().substring(2)}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}${now.hour.toString().padLeft(2, '0')}${now.minute.toString().padLeft(2, '0')}';

      // 重置今日旧料回收条码计数器
      final oldMaterialCountKey = 'old_material_barcode_count_$datePrefix';
      await _storage.remove(oldMaterialCountKey);

      // 重置当前时间段商品条码计数器
      final jewelryCountKey = 'jewelry_barcode_count_$dateTimePrefix';
      await _storage.remove(jewelryCountKey);

      LoggerService.d('已重置当前时间段的条码计数器');
    } catch (e) {
      LoggerService.e('重置条码计数器失败', e);
    }
  }
}
