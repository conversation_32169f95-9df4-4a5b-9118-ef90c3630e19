import '../common/date_utils.dart';
import '../common/document_status.dart';
import '../store/store.dart';
import '../../core/utils/number_utils.dart';
import 'sale_item.dart';

/// 销售单模型
class Sale {
  final int id;
  final String saleNo;
  final int storeId;
  final String? customer; // 客户姓名
  final String? phone; // 客户电话
  final double totalAmount; // 总金额
  final double discountAmount; // 折扣金额
  final double actualAmount; // 实收金额
  final int operatorId; // 操作员ID
  final String? paymentMethod; // 支付方式
  final String? remark; // 备注
  final DocumentStatus status; // 状态
  final DateTime? createTime;
  final DateTime? updateTime;

  // 关联对象
  final Store? store;
  final List<SaleItem>? items;

  const Sale({
    required this.id,
    required this.saleNo,
    required this.storeId,
    this.customer,
    this.phone,
    this.totalAmount = 0.0,
    this.discountAmount = 0.0,
    this.actualAmount = 0.0,
    required this.operatorId,
    this.paymentMethod,
    this.remark,
    this.status = DocumentStatus.draft,
    this.createTime,
    this.updateTime,
    this.store,
    this.items,
  });

  /// 从JSON构造
  factory Sale.fromJson(Map<String, dynamic> json) {
    return Sale(
      id: json['id'],
      saleNo: json['sale_no'],
      storeId: json['store_id'],
      customer: json['customer'],
      phone: json['phone'],
      totalAmount: NumberUtils.toDouble(json['total_amount']),
      discountAmount: NumberUtils.toDouble(json['discount_amount']),
      actualAmount: NumberUtils.toDouble(json['actual_amount']),
      operatorId: json['operator_id'],
      paymentMethod: json['payment_method'],
      remark: json['remark'],
      status: DocumentStatus.fromValue(json['status'] ?? 0),
      createTime: DateUtil.fromUnixTimestamp(json['createtime']),
      updateTime: DateUtil.fromUnixTimestamp(json['updatetime']),
      // 关联对象
      store: json['store'] != null ? Store.fromJson(json['store']) : null,
      items: json['items'] != null
          ? (json['items'] as List).map((e) => SaleItem.fromJson(e)).toList()
          : null,
    );
  }

  /// 转为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'sale_no': saleNo,
      'store_id': storeId,
      'customer': customer,
      'phone': phone,
      'total_amount': totalAmount,
      'discount_amount': discountAmount,
      'actual_amount': actualAmount,
      'operator_id': operatorId,
      'payment_method': paymentMethod,
      'remark': remark,
      'status': status.value,
      'createtime': DateUtil.toUnixTimestamp(createTime),
      'updatetime': DateUtil.toUnixTimestamp(updateTime),
    };
  }

  /// 计算折扣率
  double get discountRate {
    if (totalAmount <= 0) return 0;
    return (discountAmount / totalAmount) * 100;
  }

  /// 是否可编辑
  bool get canEdit => status == DocumentStatus.draft;

  /// 是否可删除
  bool get canDelete => status == DocumentStatus.draft;

  /// 创建一个新实例，但使用部分属性
  Sale copyWith({
    int? id,
    String? saleNo,
    int? storeId,
    String? customer,
    String? phone,
    double? totalAmount,
    double? discountAmount,
    double? actualAmount,
    int? operatorId,
    String? paymentMethod,
    String? remark,
    DocumentStatus? status,
    DateTime? createTime,
    DateTime? updateTime,
    Store? store,
    List<SaleItem>? items,
  }) {
    return Sale(
      id: id ?? this.id,
      saleNo: saleNo ?? this.saleNo,
      storeId: storeId ?? this.storeId,
      customer: customer ?? this.customer,
      phone: phone ?? this.phone,
      totalAmount: totalAmount ?? this.totalAmount,
      discountAmount: discountAmount ?? this.discountAmount,
      actualAmount: actualAmount ?? this.actualAmount,
      operatorId: operatorId ?? this.operatorId,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      remark: remark ?? this.remark,
      status: status ?? this.status,
      createTime: createTime ?? this.createTime,
      updateTime: updateTime ?? this.updateTime,
      store: store ?? this.store,
      items: items ?? this.items,
    );
  }
}
