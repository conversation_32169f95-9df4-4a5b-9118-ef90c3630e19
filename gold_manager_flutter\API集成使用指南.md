# 📚 Flutter API集成使用指南

**老板，这是Flutter前端API集成的完整使用指南！** 🚀

## 🎯 概述

我们已经成功将Flutter前端的模拟数据替换为真实的API数据，实现了智能的API集成方案。

## 🔧 技术特点

### 1. 智能降级机制
- **API优先**：优先使用真实API数据
- **自动降级**：API不可用时自动使用模拟数据
- **无感知切换**：用户无需感知数据来源变化
- **友好提示**：网络异常时显示友好的错误信息

### 2. 完整的认证系统
- **JWT认证**：安全的Token认证机制
- **自动刷新**：Token过期自动刷新
- **权限控制**：基于角色的权限管理
- **安全存储**：Token安全存储和管理

## 🚀 使用方式

### 1. 启动后端API服务

```bash
# 进入API项目目录
cd GoldManager_FastAdmin_API

# 启动API服务
python main.py
```

API服务将在 `http://localhost:8000` 启动

### 2. 启动Flutter应用

```bash
# 进入Flutter项目目录
cd gold_manager_flutter

# 安装依赖
flutter pub get

# 启动应用
flutter run
```

### 3. 测试登录

#### 真实API账号
- **用户名**: admin
- **密码**: 123456

#### 模拟账号（后备方案）
- **用户名**: admin
- **密码**: 123456

## 📊 已集成的模块

### ✅ 1. 用户认证模块
- **登录功能** - JWT认证
- **登出功能** - Token清理
- **用户信息** - 从API获取真实用户数据
- **权限验证** - 基于角色的权限控制

### ✅ 2. 首饰管理模块
- **首饰列表** - 分页、搜索、筛选
- **首饰详情** - 根据ID获取详细信息
- **首饰创建** - 新增首饰功能
- **首饰更新** - 修改首饰信息
- **首饰删除** - 删除首饰功能

### ✅ 3. 门店管理模块
- **门店列表** - 分页、搜索、状态筛选
- **门店详情** - 根据ID获取门店信息
- **门店创建** - 新增门店功能
- **门店更新** - 修改门店信息
- **门店删除** - 删除门店功能

### ✅ 4. 入库管理模块
- **入库单列表** - 分页、搜索、状态筛选
- **入库单详情** - 获取入库单详细信息
- **入库单创建** - 新增入库单功能
- **入库单更新** - 修改入库单信息
- **入库单删除** - 删除入库单功能
- **入库单审核** - 审核流程集成

### ✅ 5. 出库管理模块
- **出库单列表** - 分页、搜索、状态筛选
- **出库单详情** - 获取出库单详细信息
- **出库单创建** - 新增出库单功能
- **出库单更新** - 修改出库单信息
- **出库单删除** - 删除出库单功能
- **出库单审核** - 审核流程集成
- **出库单表单** - 完整的出库单创建/编辑表单

## 🔄 API调用示例

### 1. 获取首饰列表

```dart
final jewelryService = Get.find<JewelryService>();

// 获取首饰列表
final result = await jewelryService.getJewelryList({
  'page': 1,
  'page_size': 20,
  'category_id': 1,
  'keyword': '黄金',
});

// 处理结果
if (result.data.isNotEmpty) {
  // 显示首饰列表
  for (final jewelry in result.data) {
    print('首饰: ${jewelry.name}, 价格: ${jewelry.price}');
  }
}
```

### 2. 创建门店

```dart
final storeService = Get.find<StoreService>();

// 创建新门店
const newStore = Store(
  id: 0,
  name: '新门店',
  code: 'NEW001',
  address: '北京市朝阳区xxx路xxx号',
  phone: '010-12345678',
  manager: '张经理',
);

// 调用API创建
final success = await storeService.createStore(newStore);

if (success) {
  // 创建成功
  Get.snackbar('成功', '门店创建成功');
} else {
  // 创建失败
  Get.snackbar('错误', '门店创建失败');
}
```

### 3. 审核入库单

```dart
final stockService = Get.find<StockService>();

// 审核入库单
final success = await stockService.approveStockIn(
  stockInId,
  true, // 是否通过
  '审核通过，入库单信息完整', // 审核备注
);

if (success) {
  // 审核成功
  Get.snackbar('成功', '入库单审核完成');
}
```

### 4. 创建出库单

```dart
final stockService = Get.find<StockService>();

// 创建新出库单
final newStockOut = StockOut(
  id: 0,
  stockOutNo: 'OUT202501250001',
  storeId: 1,
  operatorId: 1,
  totalAmount: 5000.0,
  status: DocumentStatus.draft,
  createTime: DateTime.now(),
  customer: '张三',
  saleType: 'retail',
  totalWeight: 15.5,
  remark: '零售出库',
  items: [
    StockOutItem(
      id: 0,
      stockOutId: 0,
      jewelryId: 1,
      barcode: 'J001',
      amount: 5000.0,
    ),
  ],
);

// 调用API创建
final success = await stockService.createStockOut(newStockOut);

if (success) {
  // 创建成功
  Get.snackbar('成功', '出库单创建成功');
} else {
  // 创建失败
  Get.snackbar('错误', '出库单创建失败');
}
```

### 5. 审核出库单

```dart
final stockService = Get.find<StockService>();

// 审核出库单
final success = await stockService.approveStockOut(
  stockOutId,
  true, // 是否通过
  '审核通过，出库单信息完整', // 审核备注
);

if (success) {
  // 审核成功
  Get.snackbar('成功', '出库单审核完成');
}
```

## 🛠️ 配置说明

### API配置 (app_config.dart)

```dart
class AppConfig {
  // API基础URL
  static const String apiBaseUrl = 'http://localhost:8000';

  // API端点配置
  static const Map<String, String> apiEndpoint = {
    'auth': '/api/v1/auth',
    'jewelry': '/api/v1/jewelry',
    'store': '/api/v1/store',
    'stockIn': '/api/v1/stock/in',
    'stockOut': '/api/v1/stock/out',
    // ... 其他端点
  };
}
```

### 网络配置

```dart
// 连接超时时间
static const Duration connectTimeout = Duration(seconds: 10);

// 接收超时时间
static const Duration receiveTimeout = Duration(seconds: 30);

// 发送超时时间
static const Duration sendTimeout = Duration(seconds: 30);
```

## 🔍 错误处理

### 1. 网络异常
- **自动重试**：网络异常时自动重试
- **降级处理**：重试失败后降级到模拟数据
- **用户提示**：显示友好的错误信息

### 2. 认证异常
- **Token过期**：自动刷新Token
- **认证失败**：跳转到登录页面
- **权限不足**：显示权限不足提示

### 3. 数据异常
- **数据格式错误**：使用默认值处理
- **必填字段缺失**：显示数据异常提示
- **类型转换错误**：安全的类型转换

## 🧪 测试方式

### 1. 运行API集成测试

```bash
# 运行所有测试
flutter test

# 运行API集成测试
flutter test test/api_integration_test.dart
```

### 2. 手动测试步骤

1. **启动后端API服务**
2. **启动Flutter应用**
3. **测试登录功能**
4. **测试各模块功能**
5. **测试网络异常情况**

### 3. 测试场景

- ✅ **正常API调用**：API服务正常时的数据获取
- ✅ **网络异常**：API服务不可用时的降级处理
- ✅ **认证异常**：Token过期时的自动刷新
- ✅ **数据异常**：API返回异常数据时的处理

## 📈 性能优化

### 1. 数据缓存
- **内存缓存**：常用数据内存缓存
- **本地存储**：重要数据本地持久化
- **缓存策略**：智能的缓存更新策略

### 2. 网络优化
- **请求合并**：相同请求自动合并
- **连接复用**：HTTP连接复用
- **压缩传输**：数据压缩传输

### 3. UI优化
- **懒加载**：列表数据懒加载
- **虚拟滚动**：大列表虚拟滚动
- **预加载**：智能数据预加载

## 🔮 后续计划

### 1. 待集成模块
- **销售管理** - 销售单相关API
- **回收管理** - 回收单相关API
- **会员管理** - 会员相关API
- **库存盘点** - 库存盘点相关API
- **门店调拨** - 门店间调拨相关API
- **系统设置** - 系统配置API

### 2. 功能增强
- **离线支持** - 离线数据同步
- **实时通知** - WebSocket实时通知
- **数据同步** - 多端数据同步
- **性能监控** - API性能监控

## 💡 最佳实践

### 1. API调用
- 总是使用try-catch包装API调用
- 合理设置超时时间
- 实现适当的重试机制
- 提供友好的错误提示

### 2. 数据处理
- 验证API返回的数据格式
- 使用安全的类型转换
- 处理null值和默认值
- 实现数据模型映射

### 3. 用户体验
- 显示加载状态
- 提供错误重试选项
- 实现数据缓存
- 优化网络请求

---

**最后更新**: 2025-01-25 23:30
**当前状态**: 🚀 **库存管理API集成已完成，包括入库和出库管理** ✅
