-- 为回收明细表添加处理状态字段
-- 这些字段支持回收处理功能

ALTER TABLE `fa_recycling_item` 
ADD COLUMN `process_status` int(11) DEFAULT 0 COMMENT '处理状态：0=未处理，1=处理中，2=已处理，3=已入库' AFTER `createtime`,
ADD COLUMN `process_id` int(11) DEFAULT NULL COMMENT '关联的处理工单ID' AFTER `process_status`;

-- 添加索引以提高查询性能
ALTER TABLE `fa_recycling_item` 
ADD INDEX `idx_process_status` (`process_status`),
ADD INDEX `idx_process_id` (`process_id`);

-- 如果有fa_recycling_process表的话，添加外键约束
-- ALTER TABLE `fa_recycling_item` 
-- ADD CONSTRAINT `fk_recycling_item_process` FOREIGN KEY (`process_id`) REFERENCES `fa_recycling_process` (`id`) ON DELETE SET NULL;