import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

/// 主要按钮组件
/// 使用主色调背景，白色文本，圆角12px
class PrimaryButton extends StatelessWidget {
  /// 按钮文本
  final String text;
  
  /// 点击事件
  final VoidCallback? onPressed;
  
  /// 是否加载中
  final bool isLoading;
  
  /// 按钮图标
  final IconData? icon;
  
  /// 按钮大小
  final ButtonSize size;
  
  /// 按钮宽度
  final double? width;
  
  /// 自定义样式
  final ButtonStyle? style;

  const PrimaryButton({
    super.key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.icon,
    this.size = ButtonSize.medium,
    this.width,
    this.style,
  });

  @override
  Widget build(BuildContext context) {
    final buttonStyle = _getButtonStyle();
    
    Widget buttonChild = isLoading
        ? SizedBox(
            height: _getIconSize(),
            width: _getIconSize(),
            child: const CircularProgressIndicator(
              strokeWidth: 2,
              color: AppTheme.reverseTextColor,
            ),
          )
        : Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (icon != null) ...[
                Icon(icon, size: _getIconSize()),
                const SizedBox(width: AppTheme.paddingSmall),
              ],
              Text(
                text,
                style: TextStyle(
                  fontSize: _getFontSize(),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          );

    return SizedBox(
      width: width,
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: style ?? buttonStyle,
        child: buttonChild,
      ),
    );
  }

  ButtonStyle _getButtonStyle() {
    return ElevatedButton.styleFrom(
      backgroundColor: AppTheme.primaryColor,
      foregroundColor: AppTheme.reverseTextColor,
      padding: _getPadding(),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppTheme.borderRadius),
      ),
      minimumSize: Size(_getMinWidth(), _getMinHeight()),
      elevation: 2,
    );
  }

  EdgeInsets _getPadding() {
    switch (size) {
      case ButtonSize.small:
        return const EdgeInsets.symmetric(
          horizontal: AppTheme.paddingMedium,
          vertical: AppTheme.paddingSmall,
        );
      case ButtonSize.medium:
        return const EdgeInsets.symmetric(
          horizontal: AppTheme.padding,
          vertical: AppTheme.paddingMedium,
        );
      case ButtonSize.large:
        return const EdgeInsets.symmetric(
          horizontal: AppTheme.paddingLarge,
          vertical: AppTheme.padding,
        );
    }
  }

  double _getFontSize() {
    switch (size) {
      case ButtonSize.small:
        return AppTheme.captionSize;
      case ButtonSize.medium:
        return AppTheme.bodySize;
      case ButtonSize.large:
        return AppTheme.bodySize;
    }
  }

  double _getIconSize() {
    switch (size) {
      case ButtonSize.small:
        return 16;
      case ButtonSize.medium:
        return 20;
      case ButtonSize.large:
        return 24;
    }
  }

  double _getMinWidth() {
    switch (size) {
      case ButtonSize.small:
        return 80;
      case ButtonSize.medium:
        return 120;
      case ButtonSize.large:
        return 160;
    }
  }

  double _getMinHeight() {
    switch (size) {
      case ButtonSize.small:
        return 36;
      case ButtonSize.medium:
        return 48;
      case ButtonSize.large:
        return 56;
    }
  }
}

/// 次要按钮组件
/// 使用白色背景，主色调边框和文本，圆角12px
class SecondaryButton extends StatelessWidget {
  /// 按钮文本
  final String text;
  
  /// 点击事件
  final VoidCallback? onPressed;
  
  /// 是否加载中
  final bool isLoading;
  
  /// 按钮图标
  final IconData? icon;
  
  /// 按钮大小
  final ButtonSize size;
  
  /// 按钮宽度
  final double? width;
  
  /// 自定义样式
  final ButtonStyle? style;

  const SecondaryButton({
    super.key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.icon,
    this.size = ButtonSize.medium,
    this.width,
    this.style,
  });

  @override
  Widget build(BuildContext context) {
    final buttonStyle = _getButtonStyle();
    
    Widget buttonChild = isLoading
        ? SizedBox(
            height: _getIconSize(),
            width: _getIconSize(),
            child: const CircularProgressIndicator(
              strokeWidth: 2,
              color: AppTheme.primaryColor,
            ),
          )
        : Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (icon != null) ...[
                Icon(icon, size: _getIconSize()),
                const SizedBox(width: AppTheme.paddingSmall),
              ],
              Text(
                text,
                style: TextStyle(
                  fontSize: _getFontSize(),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          );

    return SizedBox(
      width: width,
      child: OutlinedButton(
        onPressed: isLoading ? null : onPressed,
        style: style ?? buttonStyle,
        child: buttonChild,
      ),
    );
  }

  ButtonStyle _getButtonStyle() {
    return OutlinedButton.styleFrom(
      foregroundColor: AppTheme.primaryColor,
      side: const BorderSide(color: AppTheme.primaryColor, width: 1.5),
      padding: _getPadding(),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppTheme.borderRadius),
      ),
      minimumSize: Size(_getMinWidth(), _getMinHeight()),
    );
  }

  EdgeInsets _getPadding() {
    switch (size) {
      case ButtonSize.small:
        return const EdgeInsets.symmetric(
          horizontal: AppTheme.paddingMedium,
          vertical: AppTheme.paddingSmall,
        );
      case ButtonSize.medium:
        return const EdgeInsets.symmetric(
          horizontal: AppTheme.padding,
          vertical: AppTheme.paddingMedium,
        );
      case ButtonSize.large:
        return const EdgeInsets.symmetric(
          horizontal: AppTheme.paddingLarge,
          vertical: AppTheme.padding,
        );
    }
  }

  double _getFontSize() {
    switch (size) {
      case ButtonSize.small:
        return AppTheme.captionSize;
      case ButtonSize.medium:
        return AppTheme.bodySize;
      case ButtonSize.large:
        return AppTheme.bodySize;
    }
  }

  double _getIconSize() {
    switch (size) {
      case ButtonSize.small:
        return 16;
      case ButtonSize.medium:
        return 20;
      case ButtonSize.large:
        return 24;
    }
  }

  double _getMinWidth() {
    switch (size) {
      case ButtonSize.small:
        return 80;
      case ButtonSize.medium:
        return 120;
      case ButtonSize.large:
        return 160;
    }
  }

  double _getMinHeight() {
    switch (size) {
      case ButtonSize.small:
        return 36;
      case ButtonSize.medium:
        return 48;
      case ButtonSize.large:
        return 56;
    }
  }
}

/// 文本按钮组件
/// 仅使用主色调文本，无背景和边框
class TextButton extends StatelessWidget {
  /// 按钮文本
  final String text;
  
  /// 点击事件
  final VoidCallback? onPressed;
  
  /// 按钮图标
  final IconData? icon;
  
  /// 按钮大小
  final ButtonSize size;
  
  /// 文本颜色
  final Color? textColor;

  const TextButton({
    super.key,
    required this.text,
    this.onPressed,
    this.icon,
    this.size = ButtonSize.medium,
    this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(AppTheme.borderRadius),
        child: Padding(
          padding: _getPadding(),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (icon != null) ...[
                Icon(
                  icon,
                  size: _getIconSize(),
                  color: textColor ?? AppTheme.primaryColor,
                ),
                const SizedBox(width: AppTheme.paddingSmall),
              ],
              Text(
                text,
                style: TextStyle(
                  fontSize: _getFontSize(),
                  fontWeight: FontWeight.w500,
                  color: textColor ?? AppTheme.primaryColor,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  EdgeInsets _getPadding() {
    switch (size) {
      case ButtonSize.small:
        return const EdgeInsets.symmetric(
          horizontal: AppTheme.paddingSmall,
          vertical: AppTheme.paddingSmall / 2,
        );
      case ButtonSize.medium:
        return const EdgeInsets.symmetric(
          horizontal: AppTheme.paddingMedium,
          vertical: AppTheme.paddingSmall,
        );
      case ButtonSize.large:
        return const EdgeInsets.symmetric(
          horizontal: AppTheme.padding,
          vertical: AppTheme.paddingMedium,
        );
    }
  }

  double _getFontSize() {
    switch (size) {
      case ButtonSize.small:
        return AppTheme.captionSize;
      case ButtonSize.medium:
        return AppTheme.bodySize;
      case ButtonSize.large:
        return AppTheme.bodySize;
    }
  }

  double _getIconSize() {
    switch (size) {
      case ButtonSize.small:
        return 16;
      case ButtonSize.medium:
        return 20;
      case ButtonSize.large:
        return 24;
    }
  }
}

/// 按钮大小枚举
enum ButtonSize {
  small,
  medium,
  large,
}
