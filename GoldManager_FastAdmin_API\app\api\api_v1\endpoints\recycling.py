"""
回收管理API路由

老板，这个模块提供回收管理的API接口，包括：
1. 回收单的CRUD操作
2. 价格计算功能
3. 统计分析功能
4. 批量操作功能
5. 多条件查询筛选

完整的回收管理业务API接口。
"""

from typing import List, Optional, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.services.recycling_service import RecyclingService
from app.schemas.recycling import (
    RecyclingCreate, RecyclingUpdate, RecyclingResponse,
    RecyclingDetailResponse, RecyclingStatistics,
    RecyclingQueryParams, RecyclingStatusUpdate,
    RecyclingBatchUpdate, RecyclingPriceCalculation,
    RecyclingPriceResult
)
from app.schemas.common import PaginatedResponse, StandardResponse

router = APIRouter()


def get_recycling_service(db: Session = Depends(get_db)) -> RecyclingService:
    """获取回收管理服务实例"""
    return RecyclingService(db)


@router.post("/calculate-price", response_model=StandardResponse[RecyclingPriceResult], summary="计算回收价格")
async def calculate_recycling_price(
    calculation: RecyclingPriceCalculation = Body(..., description="价格计算数据"),
    service: RecyclingService = Depends(get_recycling_service)
) -> Any:
    """
    计算回收价格

    **功能说明:**
    - 根据金重、金价、银重、银价计算回收金额
    - 支持折扣率计算
    - 自动计算各项金额明细

    **计算公式:**
    - 金价金额 = 金重 × 金价
    - 银价金额 = 银重 × 银价
    - 总金额 = 金价金额 + 银价金额
    - 折扣金额 = 总金额 × (100% - 折扣率)
    - 折后金额 = 总金额 - 折扣金额
    """
    try:
        result = service.calculate_price(calculation)
        return StandardResponse(
            success=True,
            code=200,
            message="价格计算成功",
            data=result
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"价格计算失败: {str(e)}")


@router.post("", response_model=StandardResponse[RecyclingResponse], summary="创建回收单")
async def create_recycling(
    recycling_data: RecyclingCreate = Body(..., description="回收单创建数据"),
    operator_id: int = Query(..., description="操作员ID"),
    service: RecyclingService = Depends(get_recycling_service)
) -> Any:
    """
    创建新的回收单

    **功能说明:**
    - 自动生成回收单号(格式: RECYYYYMMDD0001)
    - 验证门店、会员和分类是否存在
    - 自动计算各项金额和总计
    - 支持多个回收物品明细

    **业务规则:**
    - 回收物品明细不能为空
    - 所有分类必须存在于系统中
    - 门店必须存在且状态正常
    - 操作员必须存在且状态正常
    - 会员ID可选，如果提供必须存在

    **自动计算:**
    - 各明细的金额计算
    - 总金重、银重统计
    - 总金额和折后金额
    """
    try:
        result = service.create_recycling(recycling_data, operator_id)
        return StandardResponse(
            success=True,
            code=200,
            message="回收单创建成功",
            data=result
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建回收单失败: {str(e)}")


@router.get("", response_model=PaginatedResponse[RecyclingResponse], summary="获取回收单列表")
async def get_recycling_list(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    keyword: Optional[str] = Query(None, description="关键词搜索(单号、客户姓名、备注)"),
    store_id: Optional[int] = Query(None, description="门店ID筛选"),
    status: Optional[int] = Query(None, ge=0, le=1, description="状态筛选(0=作废,1=正常)"),
    operator_id: Optional[int] = Query(None, description="操作员ID筛选"),
    member_id: Optional[int] = Query(None, description="会员ID筛选"),
    start_date: Optional[str] = Query(None, regex=r'^\d{4}-\d{2}-\d{2}$', description="开始日期(YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, regex=r'^\d{4}-\d{2}-\d{2}$', description="结束日期(YYYY-MM-DD)"),
    min_amount: Optional[float] = Query(None, ge=0, description="最小金额"),
    max_amount: Optional[float] = Query(None, ge=0, description="最大金额"),
    service: RecyclingService = Depends(get_recycling_service)
) -> Any:
    """
    获取回收单列表

    **查询功能:**
    - 支持分页查询
    - 支持关键词搜索(回收单号、客户姓名、备注)
    - 支持多条件筛选(门店、状态、操作员、会员、日期范围、金额范围)
    - 按创建时间倒序排列

    **返回信息:**
    - 回收单基本信息
    - 门店名称
    - 会员姓名
    - 操作员姓名
    - 状态描述
    - 金额统计
    """
    try:
        # 构建查询参数
        params = RecyclingQueryParams(
            keyword=keyword,
            store_id=store_id,
            status=status,
            operator_id=operator_id,
            member_id=member_id,
            start_date=start_date,
            end_date=end_date,
            min_amount=min_amount,
            max_amount=max_amount
        )

        recyclings, total = service.get_recycling_list(page, page_size, params)

        # 计算总页数
        pages = (total + page_size - 1) // page_size

        # 构建分页信息
        from app.schemas.common import PaginationInfo
        pagination_info = PaginationInfo(
            page=page,
            page_size=page_size,
            total=total,
            pages=pages
        )

        return PaginatedResponse(
            success=True,
            code=200,
            message="获取回收单列表成功",
            data=recyclings,
            pagination=pagination_info
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取回收单列表失败: {str(e)}")


@router.get("/{recycling_id}", response_model=StandardResponse[RecyclingDetailResponse], summary="获取回收单详情")
async def get_recycling_detail(
    recycling_id: int = Path(..., description="回收单ID"),
    service: RecyclingService = Depends(get_recycling_service)
) -> Any:
    """
    根据ID获取回收单详情

    **返回信息:**
    - 回收单完整信息
    - 回收明细列表
    - 关联的门店、会员、操作员信息
    - 每个明细的分类信息和金额计算
    """
    try:
        result = service.get_recycling_by_id(recycling_id)
        if not result:
            raise HTTPException(status_code=404, detail="回收单不存在")

        return StandardResponse(
            success=True,
            code=200,
            message="获取回收单详情成功",
            data=result
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取回收单详情失败: {str(e)}")


@router.get("/by-no/{recycle_no}", response_model=StandardResponse[RecyclingDetailResponse], summary="根据单号获取回收单详情")
async def get_recycling_by_no(
    recycle_no: str = Path(..., description="回收单号"),
    service: RecyclingService = Depends(get_recycling_service)
) -> Any:
    """
    根据回收单号获取详情

    **使用场景:**
    - 扫码查询回收单
    - 快速定位回收单
    - 移动端查询
    """
    try:
        result = service.get_recycling_by_no(recycle_no)
        if not result:
            raise HTTPException(status_code=404, detail="回收单不存在")

        return StandardResponse(
            success=True,
            code=200,
            message="获取回收单详情成功",
            data=result
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取回收单详情失败: {str(e)}")


@router.put("/{recycling_id}", response_model=StandardResponse[RecyclingResponse], summary="更新回收单")
async def update_recycling(
    recycling_id: int = Path(..., description="回收单ID"),
    update_data: RecyclingUpdate = Body(..., description="更新数据"),
    service: RecyclingService = Depends(get_recycling_service)
) -> Any:
    """
    更新回收单信息

    **业务规则:**
    - 只有正常状态的回收单才能修改
    - 可修改客户信息、会员关联、回收价格、备注

    **注意事项:**
    - 已作废的回收单不能修改
    - 会员ID为0时表示取消会员关联
    """
    try:
        result = service.update_recycling(recycling_id, update_data)
        if not result:
            raise HTTPException(status_code=404, detail="回收单不存在")

        return StandardResponse(
            success=True,
            code=200,
            message="回收单更新成功",
            data=result
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新回收单失败: {str(e)}")


@router.delete("/{recycling_id}", response_model=StandardResponse[bool], summary="删除回收单")
async def delete_recycling(
    recycling_id: int = Path(..., description="回收单ID"),
    service: RecyclingService = Depends(get_recycling_service)
) -> Any:
    """
    删除回收单

    **业务规则:**
    - 只有正常状态的回收单才能删除
    - 删除回收单会同时删除所有明细

    **注意事项:**
    - 已作废的回收单不能删除
    - 删除操作不可恢复
    """
    try:
        result = service.delete_recycling(recycling_id)
        if not result:
            raise HTTPException(status_code=404, detail="回收单不存在")

        return StandardResponse(
            success=True,
            code=200,
            message="回收单删除成功",
            data=True
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除回收单失败: {str(e)}")


@router.patch("/{recycling_id}/status", response_model=StandardResponse[RecyclingResponse], summary="更新回收单状态")
async def update_recycling_status(
    recycling_id: int = Path(..., description="回收单ID"),
    status_data: RecyclingStatusUpdate = Body(..., description="状态更新数据"),
    service: RecyclingService = Depends(get_recycling_service)
) -> Any:
    """
    更新回收单状态

    **状态说明:**
    - 0: 作废
    - 1: 正常

    **使用场景:**
    - 作废错误的回收单
    - 恢复误作废的回收单
    """
    try:
        result = service.update_recycling_status(recycling_id, status_data)
        if not result:
            raise HTTPException(status_code=404, detail="回收单不存在")

        status_text = {0: "作废", 1: "恢复"}.get(status_data.status, "更新")

        return StandardResponse(
            success=True,
            code=200,
            message=f"回收单{status_text}成功",
            data=result
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新回收单状态失败: {str(e)}")


@router.patch("/batch-status", response_model=StandardResponse[bool], summary="批量更新回收单状态")
async def batch_update_recycling_status(
    batch_data: RecyclingBatchUpdate = Body(..., description="批量更新数据"),
    service: RecyclingService = Depends(get_recycling_service)
) -> Any:
    """
    批量更新回收单状态

    **功能说明:**
    - 一次性更新多个回收单的状态
    - 提高批量操作效率

    **数据格式:**
    ```json
    {
        "recycling_ids": [1, 2, 3],
        "status": 0,
        "remark": "批量作废"
    }
    ```

    **使用场景:**
    - 批量作废回收单
    - 批量恢复回收单
    """
    try:
        result = service.batch_update_status(batch_data)

        status_text = {0: "作废", 1: "恢复"}.get(batch_data.status, "更新")

        return StandardResponse(
            success=True,
            code=200,
            message=f"批量{status_text}成功",
            data=result
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量更新状态失败: {str(e)}")


@router.get("/statistics/summary", response_model=StandardResponse[RecyclingStatistics], summary="获取回收统计")
async def get_recycling_statistics(
    service: RecyclingService = Depends(get_recycling_service)
) -> Any:
    """
    获取回收管理统计信息

    **统计内容:**
    - 回收单数量统计(总数、正常、作废)
    - 回收物品统计(总数)
    - 重量统计(总金重、总银重)
    - 金额统计(总金额、折后金额、平均金额、最大最小金额)
    - 状态分布统计
    - 门店分布统计
    - 分类分布统计

    **使用场景:**
    - 管理仪表板
    - 回收业务分析
    - 业务报告生成
    """
    try:
        result = service.get_statistics()

        return StandardResponse(
            success=True,
            code=200,
            message="获取回收统计成功",
            data=result
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取回收统计失败: {str(e)}")
