"""
会员相关数据模型
对应FastAdmin数据库中的会员表结构
"""

from sqlalchemy import Column, Integer, String, Date
from sqlalchemy.orm import relationship
from ..core.database import Base


class Member(Base):
    """会员表 - 对应 fa_member"""
    __tablename__ = "fa_member"

    id = Column(Integer, primary_key=True, index=True, comment="会员ID")
    card_no = Column(String(20), unique=True, nullable=False, comment="会员卡号")
    name = Column(String(50), nullable=False, comment="会员姓名")
    phone = Column(String(20), comment="联系电话")
    birthday = Column(Date, comment="生日")
    points = Column(Integer, default=0, comment="积分")
    level = Column(Integer, default=1, comment="会员等级")
    status = Column(Integer, default=1, comment="状态:0=禁用,1=正常")
    createtime = Column(Integer, comment="创建时间")
    updatetime = Column(Integer, comment="更新时间")

    # 关联关系
    # stock_outs = relationship("StockOut", back_populates="member")
    # stock_returns = relationship("StockReturn", back_populates="member")
    recyclings = relationship("Recycling", back_populates="member")