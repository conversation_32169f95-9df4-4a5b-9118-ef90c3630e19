// 暂时注释掉以解决Windows编译问题
// import 'package:pdf/pdf.dart';
// import 'package:pdf/widgets.dart' as pw;
// import 'package:printing/printing.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/stock/stock_in.dart';
import '../core/utils/logger_service.dart';
import '../models/print/print_template_config.dart';

/// 打印服务类（暂时简化版本，解决Windows编译问题）
///
/// 提供基础的打印功能，包括：
/// - 入库单打印（暂时禁用）
/// - 收款凭证打印（暂时禁用）
/// - PDF生成（暂时禁用）
/// - 打印预览（暂时禁用）
class PrintService {
  static final PrintService _instance = PrintService._internal();
  factory PrintService() => _instance;
  PrintService._internal();

  /// 打印入库单（暂时禁用）
  ///
  /// [stockIn] 入库单数据
  Future<bool> printStockIn(StockIn stockIn) async {
    try {
      LoggerService.i('打印功能暂时禁用，入库单: ${stockIn.stockInNo}');

      // 显示提示信息
      Get.snackbar(
        '提示',
        '打印功能暂时禁用，正在解决Windows编译问题',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );

      return false;
    } catch (e) {
      LoggerService.e('入库单打印失败', e);
      return false;
    }
  }

  /// 打印收款凭证（暂时禁用）
  ///
  /// [stockOutData] 出库单数据
  /// [paymentData] 收款数据
  /// [items] 商品明细列表
  Future<bool> printReceipt({
    required Map<String, dynamic> stockOutData,
    required Map<String, dynamic> paymentData,
    required List<Map<String, dynamic>> items,
  }) async {
    try {
      LoggerService.i('打印功能暂时禁用，收款凭证: ${stockOutData['order_no']}');

      // 显示提示信息
      Get.snackbar(
        '提示',
        '打印功能暂时禁用，正在解决Windows编译问题',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );

      return false;
    } catch (e) {
      LoggerService.e('收款凭证打印失败', e);
      return false;
    }
  }

  /// 显示收款凭证打印预览对话框（暂时禁用）
  ///
  /// [stockOutData] 出库单数据
  /// [paymentData] 收款数据
  /// [items] 商品明细列表
  /// [template] 可选的自定义模板，如果不提供则使用默认模板
  Future<void> showReceiptPreview({
    required Map<String, dynamic> stockOutData,
    required Map<String, dynamic> paymentData,
    required List<Map<String, dynamic>> items,
    PrintTemplateConfig? template,
  }) async {
    try {
      LoggerService.i('打印预览功能暂时禁用');

      // 显示提示信息
      Get.snackbar(
        '提示',
        '打印预览功能暂时禁用，正在解决Windows编译问题',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );
    } catch (e) {
      LoggerService.e('显示收款凭证预览失败', e);
      Get.snackbar(
        '预览失败',
        '收款凭证预览失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red[600],
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );
    }
  }

  /// 获取PDF数据（暂时禁用）
  Future<Uint8List?> getStockInPdfData(StockIn stockIn) async {
    LoggerService.i('PDF生成功能暂时禁用');
    return null;
  }

  /// 检查打印功能是否支持（暂时返回false）
  bool _isPrintingSupported() {
    return false; // 暂时禁用所有打印功能
  }

  /// 格式化日期时间
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
           '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}:${dateTime.second.toString().padLeft(2, '0')}';
  }

  /// 数字转中文大写
  String _convertToChinese(double amount) {
    if (amount == 0) return '零元整';

    const units = ['', '拾', '佰', '仟'];
    const bigUnits = ['', '万', '亿'];
    const nums = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];

    // 分离整数和小数部分
    int intPart = amount.floor();
    int decimalPart = ((amount - intPart) * 100).round();

    if (intPart == 0 && decimalPart == 0) return '零元整';

    String result = '';

    // 处理整数部分
    if (intPart > 0) {
      String intStr = intPart.toString();
      int len = intStr.length;

      for (int i = 0; i < len; i++) {
        int digit = int.parse(intStr[i]);
        int pos = len - i - 1;

        if (digit != 0) {
          result += nums[digit] + units[pos % 4];
        } else if (result.isNotEmpty && !result.endsWith('零')) {
          result += '零';
        }

        if (pos % 4 == 0 && pos > 0) {
          result += bigUnits[pos ~/ 4];
        }
      }

      result += '元';
    }

    // 处理小数部分
    if (decimalPart > 0) {
      int jiao = decimalPart ~/ 10;
      int fen = decimalPart % 10;

      if (jiao > 0) {
        result += '${nums[jiao]}角';
      }
      if (fen > 0) {
        result += '${nums[fen]}分';
      }
    } else {
      result += '整';
    }

    return result;
  }

  /// 获取店铺信息
  Map<String, String> _getStoreInfo() {
    // 暂时返回默认信息
    return {
      'name': '金包银首饰店',
      'address': '广东省深圳市罗湖区黄金珠宝城A座101号',
      'phone': '0755-12345678',
    };
  }

  /// 获取操作员信息
  Map<String, String> _getOperatorInfo() {
    // 暂时返回默认信息
    return {
      'name': '系统管理员',
      'id': 'admin',
    };
  }
}