import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/utils/dialog_utils.dart';
import '../../../../core/widgets/custom_card.dart';
import '../../../../core/widgets/custom_dropdown.dart';
import '../../../../core/widgets/custom_text_field.dart';
import '../../../../core/widgets/loading_indicator.dart';
import '../../../../core/widgets/empty_state.dart';
import '../../../../models/common/enums.dart';
import '../../../../models/recycling/recycling_item.dart';
import '../../../../models/recycling/recycling_process.dart';
import '../../controllers/recycling_process_controller.dart';
import '../widgets/process_item_card.dart';
import '../widgets/image_upload_widget.dart';

/// 旧料处理页面
class ProcessPage extends StatelessWidget {
  final RecyclingItem recyclingItem;

  const ProcessPage({
    super.key,
    required this.recyclingItem,
  });

  @override
  Widget build(BuildContext context) {
    // 初始化控制器
    final controller = Get.put(
      RecyclingProcessController(recyclingItem: recyclingItem),
      tag: recyclingItem.id.toString(),
    );

    return Scaffold(
      appBar: AppBar(
        title: Text('旧料处理 - ${recyclingItem.itemName}'),
        elevation: 0,
      ),
      body: Obx(
        () => controller.isLoading.value
            ? const Center(child: LoadingIndicator())
            : _buildBody(context, controller),
      ),
    );
  }

  Widget _buildBody(
      BuildContext context, RecyclingProcessController controller) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 左侧：物品信息 + 处理记录列表
        Expanded(
          flex: 1,
          child: _buildLeftPanel(context, controller),
        ),
        
        // 右侧：处理表单
        Expanded(
          flex: 2,
          child: _buildRightPanel(context, controller),
        ),
      ],
    );
  }

  Widget _buildLeftPanel(
      BuildContext context, RecyclingProcessController controller) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 物品信息卡片
          _buildItemInfoCard(context),
          
          const SizedBox(height: 16),
          
          // 历史处理记录标题
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                '处理记录',
                style: AppTextStyles.subtitle,
              ),
              TextButton.icon(
                onPressed: controller.resetForm,
                icon: const Icon(Icons.add, size: 18),
                label: const Text('新增处理'),
              ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          // 处理记录列表
          Expanded(
            child: Obx(
              () => controller.processList.isEmpty
                  ? const EmptyState(
                      icon: Icons.inventory,
                      title: '暂无处理记录',
                      message: '点击"新增处理"按钮添加处理记录',
                    )
                  : ListView.builder(
                      itemCount: controller.processList.length,
                      itemBuilder: (context, index) {
                        final process = controller.processList[index];
                        return ProcessItemCard(
                          process: process,
                          onTap: () => controller.loadProcessDetail(process.id!),
                          onStatusChange: (status) =>
                              controller.updateProcessStatus(process.id!, status),
                          onDelete: () => _confirmDeleteProcess(context, controller, process),
                        );
                      },
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRightPanel(
      BuildContext context, RecyclingProcessController controller) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          left: BorderSide(
            color: Colors.grey.shade200,
            width: 1,
          ),
        ),
      ),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              controller.currentProcess.value?.id != null
                  ? '编辑处理记录'
                  : '新增处理记录',
              style: AppTextStyles.title,
            ),
            
            const SizedBox(height: 24),
            
            // 处理方式选择
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('处理方式', style: AppTextStyles.formLabel),
                const SizedBox(height: 8),
                CustomDropdown<RecyclingProcessType>(
                  value: controller.selectedProcessType.value,
                  items: [
                    RecyclingProcessType.sell,
                    RecyclingProcessType.separate,
                    RecyclingProcessType.repair,
                    RecyclingProcessType.resell,
                  ].map((type) {
                    return DropdownMenuItem<RecyclingProcessType>(
                      value: type,
                      child: Text(type.toString()),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      controller.changeProcessType(value);
                    }
                  },
                ),
                const SizedBox(height: 4),
                Obx(
                  () => Text(
                    controller.getProcessTypeDescription(),
                    style: AppTextStyles.caption.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // 成本和收益表格
            Card(
              elevation: 1,
              margin: EdgeInsets.zero,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
                side: BorderSide(color: Colors.grey.shade200),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('成本与收益预估', style: AppTextStyles.subtitle),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: _buildTableRow(
                            '处理成本',
                            controller.cost.value.toString(),
                            onChanged: (value) {
                              if (value.isNotEmpty) {
                                controller.cost.value = double.tryParse(value) ?? 0;
                                controller.profit.value = controller.income.value - controller.cost.value;
                              }
                            },
                          ),
                        ),
                        Expanded(
                          child: _buildTableRow(
                            '预估收益',
                            controller.income.value.toString(),
                            onChanged: (value) {
                              if (value.isNotEmpty) {
                                controller.income.value = double.tryParse(value) ?? 0;
                                controller.profit.value = controller.income.value - controller.cost.value;
                              }
                            },
                          ),
                        ),
                        Expanded(
                          child: _buildTableRow(
                            '利润',
                            controller.profit.value.toString(),
                            isEditable: false,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // 上传图片
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('处理照片', style: AppTextStyles.formLabel),
                const SizedBox(height: 8),
                Obx(
                  () => ImageUploadWidget(
                    imageUrls: controller.imageUrls.toList(),
                    tempImages: controller.tempImages.toList(),
                    onPickImage: controller.pickImage,
                    onTakePhoto: controller.takePhoto,
                    onRemoveTempImage: controller.removeTempImage,
                    onRemoveUploadedImage: controller.removeUploadedImage,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // 备注
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('备注', style: AppTextStyles.formLabel),
                const SizedBox(height: 8),
                CustomTextField(
                  initialValue: controller.remark.value,
                  maxLines: 3,
                  hintText: '请输入处理备注信息',
                  onChanged: (value) => controller.remark.value = value,
                ),
              ],
            ),
            
            const SizedBox(height: 32),
            
            // 操作按钮
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                OutlinedButton(
                  onPressed: controller.isSubmitting.value
                      ? null
                      : controller.resetForm,
                  child: const Text('取消'),
                ),
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed: controller.isSubmitting.value
                      ? null
                      : () => _submitForm(context, controller),
                  child: Obx(
                    () => controller.isSubmitting.value
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: Colors.white,
                            ),
                          )
                        : const Text('保存'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildItemInfoCard(BuildContext context) {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('物品信息', style: AppTextStyles.subtitle),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildInfoItem('物品名称', recyclingItem.itemName),
              ),
              Expanded(
                child: _buildInfoItem('物品类型', recyclingItem.getTypeName()),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: _buildInfoItem('物品重量', '${recyclingItem.weight}克'),
              ),
              Expanded(
                child: _buildInfoItem(
                  '回收价格',
                  '¥${recyclingItem.price.toStringAsFixed(2)}',
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          _buildInfoItem(
            '回收日期',
            DateFormat('yyyy-MM-dd').format(recyclingItem.recyclingTime),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: AppTextStyles.caption.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: AppTextStyles.body,
          ),
        ],
      ),
    );
  }

  Widget _buildTableRow(String label, String value,
      {bool isEditable = true, Function(String)? onChanged}) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: AppTextStyles.caption.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 8),
          isEditable
              ? SizedBox(
                  height: 40,
                  child: TextField(
                    controller: TextEditingController(text: value),
                    decoration: InputDecoration(
                      isDense: true,
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      prefixText: '¥',
                    ),
                    keyboardType: const TextInputType.numberWithOptions(
                      decimal: true,
                    ),
                    onChanged: onChanged,
                  ),
                )
              : Container(
                  height: 40,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: Colors.grey.shade100,
                  ),
                  alignment: Alignment.centerLeft,
                  child: Text(
                    '¥$value',
                    style: AppTextStyles.body,
                  ),
                ),
        ],
      ),
    );
  }

  // 提交表单
  Future<void> _submitForm(
      BuildContext context, RecyclingProcessController controller) async {
    final success = await controller.submitProcess();
    if (success) {
      DialogUtils.showSuccessSnackBar(
        title: '保存成功',
        message: '处理记录已保存',
      );
    } else {
      DialogUtils.showErrorSnackBar(
        title: '保存失败',
        message: '请检查表单数据并重试',
      );
    }
  }

  // 确认删除处理记录
  Future<void> _confirmDeleteProcess(BuildContext context,
      RecyclingProcessController controller, RecyclingProcess process) async {
    final confirm = await DialogUtils.showConfirmDialog(
      title: '确认删除',
      content: '确定要删除该处理记录吗？此操作不可恢复。',
      confirmText: '删除',
      cancelText: '取消',
    );

    if (confirm == true) {
      final success = await controller.deleteProcess(process.id!);
      if (success) {
        DialogUtils.showSuccessSnackBar(
          title: '删除成功',
          message: '处理记录已删除',
        );
      } else {
        DialogUtils.showErrorSnackBar(
          title: '删除失败',
          message: '删除处理记录时发生错误',
        );
      }
    }
  }
} 