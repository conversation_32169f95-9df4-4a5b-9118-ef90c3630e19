-- 回收处理功能升级SQL脚本
-- 执行时间：2025-01-20
-- 功能说明：添加旧料回收变现功能，支持多门店处理
-- 作者：GoldManager Dev Team

-- ========================================
-- 1. 创建回收处理工单表
-- ========================================
CREATE TABLE IF NOT EXISTS `fa_recycling_process` (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `process_no` varchar(50) NOT NULL COMMENT '处理单号',
  `store_id` int(10) UNSIGNED NOT NULL COMMENT '处理门店ID',
  `source_store_id` int(10) UNSIGNED NOT NULL COMMENT '来源门店ID（回收门店）',
  `recycling_id` int(10) UNSIGNED NOT NULL COMMENT '关联回收单ID',
  `recycling_item_ids` text COMMENT '关联回收明细IDs（JSON格式）',
  `process_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '处理类型：1=金银分离，2=翻新加工，3=直接熔炼',
  `total_weight` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '总重量（克）',
  `estimated_gold_weight` decimal(10,2) DEFAULT '0.00' COMMENT '预估金重（克）',
  `estimated_silver_weight` decimal(10,2) DEFAULT '0.00' COMMENT '预估银重（克）',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0=已取消，1=待处理，2=处理中，3=已完成',
  `start_time` int(10) UNSIGNED DEFAULT NULL COMMENT '开始处理时间',
  `end_time` int(10) UNSIGNED DEFAULT NULL COMMENT '完成时间',
  `operator_id` int(10) UNSIGNED NOT NULL COMMENT '操作员ID',
  `processor_id` int(10) UNSIGNED DEFAULT NULL COMMENT '处理人员ID',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `createtime` int(10) UNSIGNED NOT NULL COMMENT '创建时间',
  `updatetime` int(10) UNSIGNED NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_process_no` (`process_no`),
  KEY `idx_store_id` (`store_id`),
  KEY `idx_source_store_id` (`source_store_id`),
  KEY `idx_recycling_id` (`recycling_id`),
  KEY `idx_status` (`status`),
  KEY `idx_createtime` (`createtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='回收处理工单表';

-- ========================================
-- 2. 创建处理结果表
-- ========================================
CREATE TABLE IF NOT EXISTS `fa_recycling_process_result` (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `process_id` int(10) UNSIGNED NOT NULL COMMENT '处理工单ID',
  `result_type` tinyint(1) NOT NULL COMMENT '结果类型：1=纯金，2=纯银，3=成品首饰，4=金银锭',
  `name` varchar(100) NOT NULL COMMENT '产品名称',
  `weight` decimal(10,2) NOT NULL COMMENT '重量（克）',
  `purity` decimal(5,2) DEFAULT NULL COMMENT '纯度（如99.9）',
  `loss_weight` decimal(10,2) DEFAULT '0.00' COMMENT '损耗重量（克）',
  `loss_rate` decimal(5,2) DEFAULT '0.00' COMMENT '损耗率（%）',
  `process_cost` decimal(10,2) DEFAULT '0.00' COMMENT '加工成本',
  `labor_cost` decimal(10,2) DEFAULT '0.00' COMMENT '人工成本',
  `other_cost` decimal(10,2) DEFAULT '0.00' COMMENT '其他成本',
  `total_cost` decimal(10,2) DEFAULT '0.00' COMMENT '总成本',
  `target_type` tinyint(1) DEFAULT NULL COMMENT '目标类型：1=原料入库，2=商品入库，3=直接销售',
  `target_id` int(10) UNSIGNED DEFAULT NULL COMMENT '目标ID（入库单ID或销售单ID）',
  `is_converted` tinyint(1) DEFAULT '0' COMMENT '是否已转换：0=否，1=是',
  `convert_time` int(10) UNSIGNED DEFAULT NULL COMMENT '转换时间',
  `createtime` int(10) UNSIGNED NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_process_id` (`process_id`),
  KEY `idx_result_type` (`result_type`),
  KEY `idx_is_converted` (`is_converted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='回收处理结果表';

-- ========================================
-- 3. 创建回收转库存记录表
-- ========================================
CREATE TABLE IF NOT EXISTS `fa_recycling_to_stock` (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `convert_no` varchar(50) NOT NULL COMMENT '转换单号',
  `store_id` int(10) UNSIGNED NOT NULL COMMENT '入库门店ID',
  `process_result_id` int(10) UNSIGNED NOT NULL COMMENT '处理结果ID',
  `stock_type` tinyint(1) NOT NULL COMMENT '库存类型：1=原料库存，2=商品库存',
  `stock_in_id` int(10) UNSIGNED DEFAULT NULL COMMENT '关联的入库单ID',
  `jewelry_id` int(10) UNSIGNED DEFAULT NULL COMMENT '新建的商品ID（商品库存时）',
  `material_id` int(10) UNSIGNED DEFAULT NULL COMMENT '原料ID（原料库存时）',
  `quantity` int(10) NOT NULL DEFAULT '1' COMMENT '数量',
  `weight` decimal(10,2) NOT NULL COMMENT '重量（克）',
  `unit_cost` decimal(10,2) NOT NULL COMMENT '单位成本',
  `total_cost` decimal(10,2) NOT NULL COMMENT '总成本',
  `cost_detail` text COMMENT '成本明细（JSON格式）',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0=已取消，1=已转换',
  `operator_id` int(10) UNSIGNED NOT NULL COMMENT '操作员ID',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `createtime` int(10) UNSIGNED NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_convert_no` (`convert_no`),
  KEY `idx_store_id` (`store_id`),
  KEY `idx_process_result_id` (`process_result_id`),
  KEY `idx_stock_in_id` (`stock_in_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='回收转库存记录表';

-- ========================================
-- 4. 修改回收明细表，添加处理相关字段
-- ========================================
ALTER TABLE `fa_recycling_item` 
ADD COLUMN `process_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '处理状态：0=未处理，1=处理中，2=已处理，3=已入库' AFTER `createtime`,
ADD COLUMN `process_id` int(10) UNSIGNED DEFAULT NULL COMMENT '关联的处理工单ID' AFTER `process_status`,
ADD KEY `idx_process_status` (`process_status`),
ADD KEY `idx_process_id` (`process_id`);

-- ========================================
-- 5. 创建原料管理表（如果还没有）
-- ========================================
CREATE TABLE IF NOT EXISTS `fa_material` (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `material_no` varchar(50) NOT NULL COMMENT '原料编号',
  `name` varchar(100) NOT NULL COMMENT '原料名称',
  `type` tinyint(1) NOT NULL COMMENT '原料类型：1=黄金，2=白银，3=其他',
  `purity` decimal(5,2) DEFAULT NULL COMMENT '纯度',
  `unit` varchar(20) NOT NULL DEFAULT '克' COMMENT '单位',
  `store_id` int(10) UNSIGNED NOT NULL COMMENT '所属门店ID',
  `current_stock` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '当前库存量',
  `unit_cost` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '单位成本',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0=停用，1=启用',
  `createtime` int(10) UNSIGNED NOT NULL COMMENT '创建时间',
  `updatetime` int(10) UNSIGNED NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_material_no` (`material_no`),
  KEY `idx_store_id` (`store_id`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='原料管理表';

-- ========================================
-- 6. 添加系统权限
-- ========================================
-- 先查询recycling权限的父ID
SET @recycling_parent_id = (SELECT id FROM fa_auth_rule WHERE name = 'recycling' LIMIT 1);

-- 添加回收处理管理权限组
INSERT INTO `fa_auth_rule` (`type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) 
SELECT 'file', @recycling_parent_id, 'recycling/process', '回收处理管理', 'fa fa-recycle', '', '', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'
WHERE @recycling_parent_id IS NOT NULL;

-- 获取新插入的回收处理管理ID
SET @process_parent_id = (SELECT id FROM fa_auth_rule WHERE name = 'recycling/process' LIMIT 1);

-- 添加子权限
INSERT INTO `fa_auth_rule` (`type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) 
SELECT 'file', @process_parent_id, 'recycling/process/index', '查看', 'fa fa-circle-o', '', '', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'
WHERE @process_parent_id IS NOT NULL;

INSERT INTO `fa_auth_rule` (`type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) 
SELECT 'file', @process_parent_id, 'recycling/process/add', '添加', 'fa fa-circle-o', '', '', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'
WHERE @process_parent_id IS NOT NULL;

INSERT INTO `fa_auth_rule` (`type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) 
SELECT 'file', @process_parent_id, 'recycling/process/edit', '编辑', 'fa fa-circle-o', '', '', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'
WHERE @process_parent_id IS NOT NULL;

INSERT INTO `fa_auth_rule` (`type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) 
SELECT 'file', @process_parent_id, 'recycling/process/del', '删除', 'fa fa-circle-o', '', '', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'
WHERE @process_parent_id IS NOT NULL;

INSERT INTO `fa_auth_rule` (`type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) 
SELECT 'file', @process_parent_id, 'recycling/process/complete', '完成处理', 'fa fa-circle-o', '', '', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'
WHERE @process_parent_id IS NOT NULL;

INSERT INTO `fa_auth_rule` (`type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) 
SELECT 'file', @process_parent_id, 'recycling/process/convert', '转库存', 'fa fa-circle-o', '', '', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal'
WHERE @process_parent_id IS NOT NULL;

-- ========================================
-- 7. 为管理员角色添加新权限
-- ========================================
-- 获取管理员角色组ID（通常是1）
SET @admin_group_id = 1;

-- 获取所有新权限的ID并添加到管理员组
INSERT INTO `fa_auth_group_access` (`uid`, `group_id`)
SELECT a.id, @admin_group_id
FROM `fa_admin` a
WHERE a.id IN (SELECT id FROM fa_admin WHERE username = 'admin')
AND NOT EXISTS (
    SELECT 1 FROM fa_auth_group_access 
    WHERE uid = a.id AND group_id = @admin_group_id
);

-- 更新管理员组的权限规则（假设管理员组有所有权限）
UPDATE `fa_auth_group` 
SET `rules` = (
    SELECT GROUP_CONCAT(id ORDER BY id) 
    FROM `fa_auth_rule` 
    WHERE status = 'normal'
)
WHERE id = @admin_group_id;

-- ========================================
-- 8. 添加数据字典（处理类型、结果类型等）
-- ========================================
-- 处理类型字典
INSERT INTO `fa_config` (`name`, `group`, `title`, `tip`, `type`, `value`, `content`, `rule`, `extend`, `setting`) VALUES
('recycling_process_type', 'dictionary', '回收处理类型', '', 'array', '1:金银分离\r\n2:翻新加工\r\n3:直接熔炼', '', '', '', ''),
('recycling_result_type', 'dictionary', '处理结果类型', '', 'array', '1:纯金\r\n2:纯银\r\n3:成品首饰\r\n4:金银锭', '', '', '', ''),
('material_type', 'dictionary', '原料类型', '', 'array', '1:黄金\r\n2:白银\r\n3:其他', '', '', '', '');

-- ========================================
-- 执行完成提示
-- ========================================
SELECT '回收处理功能升级SQL脚本执行完成！' AS '执行结果';