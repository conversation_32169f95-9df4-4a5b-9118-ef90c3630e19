import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../core/utils/api_diagnostics.dart';
import '../../core/utils/logger.dart';
import '../../core/config/app_config.dart';

/// API测试页面
/// 用于诊断和测试后端API连接
class ApiTestPage extends StatefulWidget {
  const ApiTestPage({super.key});

  @override
  State<ApiTestPage> createState() => _ApiTestPageState();
}

class _ApiTestPageState extends State<ApiTestPage> {
  bool _isRunning = false;
  ApiDiagnosticResult? _result;
  String _logs = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('API连接诊断'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 配置信息
            _buildConfigSection(),
            const SizedBox(height: 20),
            
            // 操作按钮
            _buildActionButtons(),
            const SizedBox(height: 20),
            
            // 诊断结果
            if (_result != null) _buildResultSection(),
            
            // 日志输出
            Expanded(child: _buildLogSection()),
          ],
        ),
      ),
    );
  }

  Widget _buildConfigSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '当前API配置',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text('基础URL: ${AppConfig.apiBaseUrl}'),
            Text('连接超时: ${AppConfig.connectTimeout}ms'),
            Text('接收超时: ${AppConfig.receiveTimeout}ms'),
            const SizedBox(height: 8),
            const Text(
              '测试端点:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const Text('• /api/v1/dashboard/overview'),
            const Text('• /api/v1/dashboard/sales-statistics'),
            const Text('• /api/v1/dashboard/inventory-statistics'),
            const Text('• /api/v1/dashboard/financial-statistics'),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        ElevatedButton.icon(
          onPressed: _isRunning ? null : _runQuickTest,
          icon: const Icon(Icons.flash_on),
          label: const Text('快速测试'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.orange,
            foregroundColor: Colors.white,
          ),
        ),
        const SizedBox(width: 16),
        ElevatedButton.icon(
          onPressed: _isRunning ? null : _runFullDiagnostic,
          icon: const Icon(Icons.medical_services),
          label: const Text('完整诊断'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.green,
            foregroundColor: Colors.white,
          ),
        ),
        const SizedBox(width: 16),
        ElevatedButton.icon(
          onPressed: _clearLogs,
          icon: const Icon(Icons.clear),
          label: const Text('清空日志'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.grey,
            foregroundColor: Colors.white,
          ),
        ),
      ],
    );
  }

  Widget _buildResultSection() {
    final result = _result!;
    
    return Card(
      color: result.allPassed ? Colors.green.shade50 : Colors.red.shade50,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  result.allPassed ? Icons.check_circle : Icons.error,
                  color: result.allPassed ? Colors.green : Colors.red,
                ),
                const SizedBox(width: 8),
                Text(
                  result.allPassed ? '所有测试通过' : '发现 ${result.failedEndpointsCount} 个问题',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: result.allPassed ? Colors.green : Colors.red,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            // 基础连接状态
            _buildStatusRow('基础连接', result.baseConnection),
            _buildStatusRow('健康检查', result.healthCheck),
            _buildStatusRow('认证端点', result.authEndpoint),
            
            // 仪表盘端点状态
            if (result.dashboardEndpoints.isNotEmpty) ...[
              const SizedBox(height: 8),
              const Text('仪表盘端点:', style: TextStyle(fontWeight: FontWeight.bold)),
              ...result.dashboardEndpoints.entries.map(
                (e) => _buildStatusRow('  ${e.key}', e.value),
              ),
            ],
            
            // 核心端点状态
            if (result.coreEndpoints.isNotEmpty) ...[
              const SizedBox(height: 8),
              const Text('核心端点:', style: TextStyle(fontWeight: FontWeight.bold)),
              ...result.coreEndpoints.entries.map(
                (e) => _buildStatusRow('  ${e.key}', e.value),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatusRow(String label, bool status) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2.0),
      child: Row(
        children: [
          Icon(
            status ? Icons.check : Icons.close,
            size: 16,
            color: status ? Colors.green : Colors.red,
          ),
          const SizedBox(width: 8),
          Text(label),
        ],
      ),
    );
  }

  Widget _buildLogSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '诊断日志',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Expanded(
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12.0),
                decoration: BoxDecoration(
                  color: Colors.black87,
                  borderRadius: BorderRadius.circular(8.0),
                ),
                child: SingleChildScrollView(
                  child: Text(
                    _logs.isEmpty ? '点击测试按钮开始诊断...' : _logs,
                    style: const TextStyle(
                      color: Colors.green,
                      fontFamily: 'monospace',
                      fontSize: 12,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _runQuickTest() async {
    setState(() {
      _isRunning = true;
      _logs = '';
    });

    _addLog('⚡ 开始快速连接测试...');
    
    try {
      final success = await ApiDiagnostics.quickConnectionTest();
      _addLog(success ? '✅ 快速测试成功' : '❌ 快速测试失败');
      
      if (success) {
        _addLog('💡 后端服务运行正常，可以进行完整诊断');
      } else {
        _addLog('💡 建议检查:');
        _addLog('   1. 后端服务是否启动');
        _addLog('   2. 端口8000是否可访问');
        _addLog('   3. 防火墙设置');
      }
    } catch (e) {
      _addLog('❌ 快速测试异常: $e');
    }

    setState(() {
      _isRunning = false;
    });
  }

  Future<void> _runFullDiagnostic() async {
    setState(() {
      _isRunning = true;
      _logs = '';
      _result = null;
    });

    _addLog('🔍 开始完整API诊断...');
    
    try {
      final result = await ApiDiagnostics.runFullDiagnostic();
      
      setState(() {
        _result = result;
      });
      
      _addLog('📋 诊断完成');
      _addLog('总体状态: ${result.allPassed ? "✅ 全部通过" : "❌ 发现问题"}');
      
      if (!result.allPassed) {
        _addLog('');
        _addLog('🔧 修复建议:');
        if (!result.baseConnection) {
          _addLog('• 检查后端服务是否在 http://localhost:8000 运行');
        }
        if (result.dashboardEndpoints.values.any((v) => !v)) {
          _addLog('• 检查仪表盘API端点是否正确实现');
        }
      }
    } catch (e) {
      _addLog('❌ 诊断过程异常: $e');
    }

    setState(() {
      _isRunning = false;
    });
  }

  void _clearLogs() {
    setState(() {
      _logs = '';
      _result = null;
    });
  }

  void _addLog(String message) {
    setState(() {
      final timestamp = DateTime.now().toString().substring(11, 19);
      _logs += '[$timestamp] $message\n';
    });
  }
}
