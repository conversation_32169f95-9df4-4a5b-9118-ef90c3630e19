import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gold_manager_flutter/widgets/responsive_builder.dart';

import '../../../core/theme/app_theme.dart';
import '../../../core/routes/app_pages.dart';
import '../../../core/widgets/empty_state.dart';
import '../../../widgets/loading_state.dart';
import '../../../core/widgets/search_filter_bar.dart';
import '../../../core/widgets/standard_buttons.dart';
import '../controllers/jewelry_controller.dart';
import '../../../widgets/jewelry_card.dart';

/// 首饰列表视图
/// 展示所有首饰信息，支持搜索、筛选和分页
class JewelryListView extends GetView<JewelryController> {
  const JewelryListView({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppTheme.backgroundColor,
      padding: const EdgeInsets.all(AppTheme.paddingLarge),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 页面标题和操作按钮
          Row(
            children: [
              const Text(
                '首饰管理',
                style: TextStyle(
                  fontSize: AppTheme.headingMediumSize,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.primaryTextColor,
                ),
              ),
              const Spacer(),
              // 分类管理按钮
              SecondaryButton(
                text: '分类管理',
                icon: Icons.category,
                size: ButtonSize.small,
                onPressed: () => Get.toNamed(Routes.JEWELRY_CATEGORY),
              ),
              const SizedBox(width: AppTheme.paddingSmall),
              // 刷新按钮
              SecondaryButton(
                text: '刷新',
                icon: Icons.refresh,
                size: ButtonSize.small,
                onPressed: controller.refreshData,
              ),
            ],
          ),

          const SizedBox(height: AppTheme.paddingLarge),

          // 头部搜索和筛选区域
          SearchFilterBar(
            searchController: controller.searchController,
            searchHint: '搜索首饰名称或条码',
            onSearchChanged: (_) => controller.applyFilters(),
            onRefresh: controller.refreshData,
            showFilter: true,
            showSort: true,
            showAdd: true,
            addButtonText: '添加首饰',
            onAdd: () {
              // TODO: 跳转到添加首饰页面
            },
            customActions: [
              // 分类筛选下拉框
              SizedBox(
                width: 150,
                child: Obx(() => DropdownButtonFormField<int>(
                  value: controller.selectedCategoryId.value == 0
                      ? null
                      : controller.selectedCategoryId.value,
                  decoration: const InputDecoration(
                    labelText: '分类',
                    isDense: true,
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: AppTheme.paddingMedium,
                      vertical: AppTheme.paddingSmall,
                    ),
                  ),
                  items: [
                    const DropdownMenuItem<int>(
                      value: null,
                      child: Text('全部分类'),
                    ),
                    ...controller.categories.map((category) =>
                      DropdownMenuItem<int>(
                        value: category.id,
                        child: Text(category.name),
                      ),
                    ),
                  ],
                  onChanged: (value) {
                    controller.selectedCategoryId.value = value ?? 0;
                    controller.applyFilters();
                  },
                )),
              ),
              const SizedBox(width: AppTheme.paddingSmall),
              // 排序下拉框
              SizedBox(
                width: 120,
                child: Obx(() => DropdownButtonFormField<String>(
                  value: controller.sortBy.value,
                  decoration: const InputDecoration(
                    labelText: '排序',
                    isDense: true,
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: AppTheme.paddingMedium,
                      vertical: AppTheme.paddingSmall,
                    ),
                  ),
                  items: const [
                    DropdownMenuItem(value: 'name', child: Text('按名称')),
                    DropdownMenuItem(value: 'gold_weight', child: Text('按金重')),
                    DropdownMenuItem(value: 'silver_weight', child: Text('按银重')),
                    DropdownMenuItem(value: 'price', child: Text('按价格')),
                  ],
                  onChanged: (value) {
                    if (value != null) {
                      controller.sortBy.value = value;
                      controller.applyFilters();
                    }
                  },
                )),
              ),
              const SizedBox(width: AppTheme.paddingSmall),
              // 排序方向按钮
              Obx(() => IconButton(
                icon: Icon(
                  controller.isAscending.value
                      ? Icons.arrow_upward
                      : Icons.arrow_downward,
                  color: AppTheme.primaryColor,
                ),
                tooltip: controller.isAscending.value ? '升序' : '降序',
                onPressed: () {
                  controller.isAscending.value = !controller.isAscending.value;
                  controller.applyFilters();
                },
              )),
            ],
          ),

          const SizedBox(height: AppTheme.padding),

          // 主要内容区域
          Expanded(
            child: Obx(() {
              // 加载状态
              if (controller.isLoading.value) {
                return const LoadingState(text: '正在加载首饰数据...', timeoutSeconds: 30);
              }

              // 错误状态
              if (controller.errorMessage.value.isNotEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.error_outline,
                        size: 48,
                        color: AppTheme.errorColor,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        controller.errorMessage.value,
                        style: const TextStyle(color: AppTheme.errorColor),
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: controller.refreshData,
                        child: Text('retry'.tr),
                      ),
                    ],
                  ),
                );
              }

              // 空数据状态
              if (controller.filteredJewelryList.isEmpty) {
                return EmptyState(
                  icon: Icons.diamond_outlined,
                  title: 'no_jewelry'.tr,
                  message: 'no_jewelry_message'.tr,
                  buttonText: 'add_jewelry'.tr,
                  onButtonPressed: () {
                    // TODO: 跳转到添加首饰页面
                  },
                );
              }

              // 数据展示
              return _buildJewelryGrid();
            }),
          ),
        ],
      ),
    );
  }



  /// 构建首饰网格
  Widget _buildJewelryGrid() {
    return ResponsiveBuilder(
      builder: (context, sizingInformation) {
        // 根据屏幕尺寸调整网格列数
        int crossAxisCount;
        if (sizingInformation.isDesktop) {
          crossAxisCount = 4;
        } else if (sizingInformation.isTablet) {
          crossAxisCount = 3;
        } else {
          crossAxisCount = 2;
        }

        return RefreshIndicator(
          onRefresh: () async {
            await controller.refreshData();
          },
          child: Padding(
            padding: const EdgeInsets.all(AppTheme.padding),
            child: GridView.builder(
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: crossAxisCount,
                childAspectRatio: 0.65, // 调整比例，符合设计规范
                crossAxisSpacing: AppTheme.padding, // 使用设计规范的间距
                mainAxisSpacing: AppTheme.padding, // 使用设计规范的间距
              ),
              itemCount: controller.filteredJewelryList.length,
              itemBuilder: (context, index) {
                final jewelry = controller.filteredJewelryList[index];
                return JewelryCard(
                  jewelry: jewelry,
                  onTap: () => controller.viewJewelryDetails(jewelry),
                );
              },
            ),
          ),
        );
      },
    );
  }
}