"""
认证相关的数据验证模型
"""

from typing import Optional, List
from pydantic import BaseModel, Field, validator
from datetime import datetime


class LoginRequest(BaseModel):
    """登录请求模型"""
    username: str = Field(..., min_length=3, max_length=20, description="用户名")
    password: str = Field(..., min_length=6, max_length=50, description="密码")
    remember_me: bool = Field(default=False, description="记住我")
    
    class Config:
        json_schema_extra = {
            "example": {
                "username": "admin",
                "password": "123456",
                "remember_me": False
            }
        }


class LoginResponse(BaseModel):
    """登录响应模型"""
    access_token: str = Field(..., description="访问令牌")
    refresh_token: str = Field(..., description="刷新令牌")
    token_type: str = Field(default="bearer", description="令牌类型")
    expires_in: int = Field(..., description="过期时间(秒)")
    admin_info: dict = Field(..., description="管理员信息")
    
    class Config:
        json_schema_extra = {
            "example": {
                "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                "token_type": "bearer",
                "expires_in": 28800,
                "admin_info": {
                    "id": 1,
                    "username": "admin",
                    "nickname": "管理员",
                    "store_id": 1,
                    "store_name": "总店",
                    "permissions": ["super.admin"]
                }
            }
        }


class RefreshTokenRequest(BaseModel):
    """刷新令牌请求模型"""
    refresh_token: str = Field(..., description="刷新令牌")
    
    class Config:
        json_schema_extra = {
            "example": {
                "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            }
        }


class RefreshTokenResponse(BaseModel):
    """刷新令牌响应模型"""
    access_token: str = Field(..., description="新的访问令牌")
    token_type: str = Field(default="bearer", description="令牌类型")
    expires_in: int = Field(..., description="过期时间(秒)")
    
    class Config:
        json_schema_extra = {
            "example": {
                "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                "token_type": "bearer",
                "expires_in": 28800
            }
        }


class ChangePasswordRequest(BaseModel):
    """修改密码请求模型"""
    old_password: str = Field(..., min_length=6, max_length=50, description="原密码")
    new_password: str = Field(..., min_length=6, max_length=50, description="新密码")
    confirm_password: str = Field(..., min_length=6, max_length=50, description="确认密码")
    
    @validator('confirm_password')
    def passwords_match(cls, v, values):
        if 'new_password' in values and v != values['new_password']:
            raise ValueError('新密码和确认密码不匹配')
        return v
    
    class Config:
        json_schema_extra = {
            "example": {
                "old_password": "123456",
                "new_password": "newpassword123",
                "confirm_password": "newpassword123"
            }
        }


class CurrentUserResponse(BaseModel):
    """当前用户信息响应模型"""
    id: int = Field(..., description="管理员ID")
    username: str = Field(..., description="用户名")
    nickname: str = Field(..., description="昵称")
    email: Optional[str] = Field(None, description="邮箱")
    mobile: Optional[str] = Field(None, description="手机号")
    avatar: Optional[str] = Field(None, description="头像")
    store_id: Optional[int] = Field(None, description="所属门店ID")
    store_name: Optional[str] = Field(None, description="所属门店名称")
    status: str = Field(..., description="状态")
    permissions: List[str] = Field(default=[], description="权限列表")
    last_login_time: Optional[datetime] = Field(None, description="最后登录时间")
    last_login_ip: Optional[str] = Field(None, description="最后登录IP")
    
    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": 1,
                "username": "admin",
                "nickname": "管理员",
                "email": "<EMAIL>",
                "mobile": "13800138000",
                "avatar": "/uploads/avatar/admin.jpg",
                "store_id": 1,
                "store_name": "总店",
                "status": "normal",
                "permissions": ["super.admin"],
                "last_login_time": "2024-01-25T10:30:00",
                "last_login_ip": "*************"
            }
        }


class LogoutResponse(BaseModel):
    """登出响应模型"""
    message: str = Field(default="登出成功", description="响应消息")
    
    class Config:
        json_schema_extra = {
            "example": {
                "message": "登出成功"
            }
        }


class PermissionResponse(BaseModel):
    """权限响应模型"""
    code: str = Field(..., description="权限代码")
    name: str = Field(..., description="权限名称")
    category: str = Field(..., description="权限分类")
    
    class Config:
        json_schema_extra = {
            "example": {
                "code": "jewelry.view",
                "name": "查看商品",
                "category": "商品管理"
            }
        }


class PermissionListResponse(BaseModel):
    """权限列表响应模型"""
    permissions: List[PermissionResponse] = Field(..., description="权限列表")
    categories: List[str] = Field(..., description="权限分类列表")
    
    class Config:
        json_schema_extra = {
            "example": {
                "permissions": [
                    {
                        "code": "jewelry.view",
                        "name": "查看商品",
                        "category": "商品管理"
                    }
                ],
                "categories": ["商品管理", "门店管理", "会员管理"]
            }
        }


class LoginAttemptResponse(BaseModel):
    """登录尝试响应模型"""
    remaining_attempts: int = Field(..., description="剩余尝试次数")
    lock_time: Optional[int] = Field(None, description="锁定时间(分钟)")
    is_locked: bool = Field(..., description="是否被锁定")
    
    class Config:
        json_schema_extra = {
            "example": {
                "remaining_attempts": 3,
                "lock_time": None,
                "is_locked": False
            }
        }
