"""
回收处理API端点
处理回收物品的金银分离、翻新加工、熔炼等接口
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from ....core.dependencies import get_db, get_current_user, require_permission
from ....models.admin import Admin
from ....schemas.recycling_process import (
    RecyclingProcessCreate, RecyclingProcessUpdate, RecyclingProcessResponse,
    ProcessCompleteRequest, RecyclingToStockCreate, RecyclingToStockResponse,
    MaterialCreate, MaterialUpdate, MaterialResponse,
    RecyclingProcessQueryParams
)
from ....services.recycling_process_service import RecyclingProcessService

router = APIRouter()


@router.post("/process", response_model=RecyclingProcessResponse, summary="创建处理工单")
def create_process(
    *,
    db: Session = Depends(get_db),
    process_data: RecyclingProcessCreate,
    current_user: Admin = Depends(require_permission("recycling.process.add"))
) -> RecyclingProcessResponse:
    """创建回收处理工单"""
    service = RecyclingProcessService(db)
    
    try:
        process = service.create_process(process_data, current_user.id)
        
        # 构建响应
        return RecyclingProcessResponse(
            **process.__dict__,
            store_name=process.store.name if process.store else None,
            source_store_name=process.source_store.name if process.source_store else None,
            recycling_no=process.recycling.recycle_no if process.recycling else None,
            operator_name=process.operator.nickname if process.operator else None,
            processor_name=process.processor.nickname if process.processor else None,
            results=[]
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建处理工单失败: {str(e)}")


@router.get("/process", response_model=dict, summary="获取处理工单列表")
def get_process_list(
    *,
    db: Session = Depends(get_db),
    page: int = Query(1, gt=0),
    page_size: int = Query(20, gt=0, le=100),
    keyword: Optional[str] = None,
    store_id: Optional[int] = None,
    status: Optional[int] = None,
    process_type: Optional[int] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    current_user: Admin = Depends(require_permission("recycling.process.index"))
) -> dict:
    """获取处理工单列表"""
    service = RecyclingProcessService(db)
    
    # 构建查询参数
    params = RecyclingProcessQueryParams(
        page=page,
        page_size=page_size,
        keyword=keyword,
        store_id=store_id,
        status=status,
        process_type=process_type,
        start_date=start_date,
        end_date=end_date
    )
    
    # 非管理员只能查看自己门店的数据
    if current_user.username != "admin" and not store_id:
        params.store_id = current_user.store_id
    
    processes, total = service.get_process_list(params)
    
    # 构建响应列表
    process_list = []
    for process in processes:
        process_dict = RecyclingProcessResponse(
            **process.__dict__,
            store_name=process.store.name if process.store else None,
            source_store_name=process.source_store.name if process.source_store else None,
            recycling_no=process.recycling.recycle_no if process.recycling else None,
            operator_name=process.operator.nickname if process.operator else None,
            processor_name=process.processor.nickname if process.processor else None,
            results=[{
                "id": r.id,
                "result_type": r.result_type,
                "name": r.name,
                "weight": float(r.weight),
                "is_converted": r.is_converted
            } for r in process.results] if process.results else []
        )
        process_list.append(process_dict.dict())
    
    return {
        "data": process_list,
        "pagination": {
            "page": page,
            "page_size": page_size,
            "total": total,
            "pages": (total + page_size - 1) // page_size
        }
    }


@router.get("/process/{process_id}", response_model=RecyclingProcessResponse, summary="获取处理工单详情")
def get_process_detail(
    *,
    db: Session = Depends(get_db),
    process_id: int,
    current_user: Admin = Depends(require_permission("recycling.process.index"))
) -> RecyclingProcessResponse:
    """获取处理工单详情"""
    service = RecyclingProcessService(db)
    
    process = service.get_process_by_id(process_id)
    if not process:
        raise HTTPException(status_code=404, detail="处理工单不存在")
    
    # 非管理员只能查看自己门店的数据
    if current_user.username != "admin" and process.store_id != current_user.store_id:
        raise HTTPException(status_code=403, detail="无权查看此工单")
    
    # 构建响应
    return RecyclingProcessResponse(
        **process.__dict__,
        store_name=process.store.name if process.store else None,
        source_store_name=process.source_store.name if process.source_store else None,
        recycling_no=process.recycling.recycle_no if process.recycling else None,
        operator_name=process.operator.nickname if process.operator else None,
        processor_name=process.processor.nickname if process.processor else None,
        results=[{
            "id": r.id,
            "process_id": r.process_id,
            "result_type": r.result_type,
            "name": r.name,
            "weight": float(r.weight),
            "purity": float(r.purity) if r.purity else None,
            "loss_weight": float(r.loss_weight),
            "loss_rate": float(r.loss_rate),
            "process_cost": float(r.process_cost),
            "labor_cost": float(r.labor_cost),
            "other_cost": float(r.other_cost),
            "total_cost": float(r.total_cost),
            "is_converted": r.is_converted,
            "createtime": r.createtime
        } for r in process.results] if process.results else []
    )


@router.put("/process/{process_id}", response_model=RecyclingProcessResponse, summary="更新处理工单")
def update_process(
    *,
    db: Session = Depends(get_db),
    process_id: int,
    process_data: RecyclingProcessUpdate,
    current_user: Admin = Depends(require_permission("recycling.process.edit"))
) -> RecyclingProcessResponse:
    """更新处理工单"""
    service = RecyclingProcessService(db)
    
    try:
        process = service.update_process(process_id, process_data)
        if not process:
            raise HTTPException(status_code=404, detail="处理工单不存在")
        
        # 构建响应
        return RecyclingProcessResponse(
            **process.__dict__,
            store_name=process.store.name if process.store else None,
            source_store_name=process.source_store.name if process.source_store else None,
            recycling_no=process.recycling.recycle_no if process.recycling else None,
            operator_name=process.operator.nickname if process.operator else None,
            processor_name=process.processor.nickname if process.processor else None,
            results=[]
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新处理工单失败: {str(e)}")


@router.post("/process/{process_id}/start", response_model=RecyclingProcessResponse, summary="开始处理")
def start_process(
    *,
    db: Session = Depends(get_db),
    process_id: int,
    current_user: Admin = Depends(require_permission("recycling.process.edit"))
) -> RecyclingProcessResponse:
    """开始处理工单"""
    service = RecyclingProcessService(db)
    
    try:
        process = service.start_process(process_id, current_user.id)
        if not process:
            raise HTTPException(status_code=404, detail="处理工单不存在")
        
        # 构建响应
        return RecyclingProcessResponse(
            **process.__dict__,
            store_name=process.store.name if process.store else None,
            source_store_name=process.source_store.name if process.source_store else None,
            recycling_no=process.recycling.recycle_no if process.recycling else None,
            operator_name=process.operator.nickname if process.operator else None,
            processor_name=process.processor.nickname if process.processor else None,
            results=[]
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"开始处理失败: {str(e)}")


@router.post("/process/{process_id}/complete", response_model=RecyclingProcessResponse, summary="完成处理")
def complete_process(
    *,
    db: Session = Depends(get_db),
    process_id: int,
    data: ProcessCompleteRequest,
    current_user: Admin = Depends(require_permission("recycling.process.complete"))
) -> RecyclingProcessResponse:
    """完成处理并录入结果"""
    service = RecyclingProcessService(db)
    
    try:
        process = service.complete_process(process_id, data)
        if not process:
            raise HTTPException(status_code=404, detail="处理工单不存在")
        
        # 重新查询以获取完整数据
        process = service.get_process_by_id(process_id)
        
        # 构建响应
        return RecyclingProcessResponse(
            **process.__dict__,
            store_name=process.store.name if process.store else None,
            source_store_name=process.source_store.name if process.source_store else None,
            recycling_no=process.recycling.recycle_no if process.recycling else None,
            operator_name=process.operator.nickname if process.operator else None,
            processor_name=process.processor.nickname if process.processor else None,
            results=[{
                "id": r.id,
                "process_id": r.process_id,
                "result_type": r.result_type,
                "name": r.name,
                "weight": float(r.weight),
                "purity": float(r.purity) if r.purity else None,
                "loss_weight": float(r.loss_weight),
                "loss_rate": float(r.loss_rate),
                "process_cost": float(r.process_cost),
                "labor_cost": float(r.labor_cost),
                "other_cost": float(r.other_cost),
                "total_cost": float(r.total_cost),
                "is_converted": r.is_converted,
                "createtime": r.createtime
            } for r in process.results] if process.results else []
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"完成处理失败: {str(e)}")


@router.delete("/process/{process_id}", response_model=dict, summary="取消处理工单")
def cancel_process(
    *,
    db: Session = Depends(get_db),
    process_id: int,
    current_user: Admin = Depends(require_permission("recycling.process.del"))
) -> dict:
    """取消处理工单"""
    service = RecyclingProcessService(db)
    
    try:
        success = service.cancel_process(process_id)
        if not success:
            raise HTTPException(status_code=404, detail="处理工单不存在")
        
        return {"success": True, "message": "处理工单已取消"}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"取消处理工单失败: {str(e)}")


@router.post("/convert-to-stock", response_model=RecyclingToStockResponse, summary="转为库存")
def convert_to_stock(
    *,
    db: Session = Depends(get_db),
    convert_data: RecyclingToStockCreate,
    current_user: Admin = Depends(require_permission("recycling.process.convert"))
) -> RecyclingToStockResponse:
    """将处理结果转为库存"""
    service = RecyclingProcessService(db)
    
    try:
        conversion = service.convert_to_stock(convert_data, current_user.id)
        
        # 构建响应
        return RecyclingToStockResponse(
            **conversion.__dict__,
            store_name=conversion.store.name if conversion.store else None,
            operator_name=conversion.operator.nickname if conversion.operator else None,
            process_result={
                "id": conversion.process_result.id,
                "result_type": conversion.process_result.result_type,
                "name": conversion.process_result.name,
                "weight": float(conversion.process_result.weight),
                "purity": float(conversion.process_result.purity) if conversion.process_result.purity else None
            } if conversion.process_result else None
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"转库存失败: {str(e)}")