import 'package:dio/dio.dart';
import 'package:get/get.dart' hide Response;
import '../config/app_config.dart';
import '../services/api_service.dart';
import 'logger.dart';

/// API连接诊断工具
/// 用于检查后端API服务的连接状态和可用性
class ApiDiagnostics {
  static final ApiService _apiService = Get.find<ApiService>();
  
  /// 执行完整的API诊断
  static Future<ApiDiagnosticResult> runFullDiagnostic() async {
    LoggerService.i('🔍 开始API连接诊断...');
    
    final result = ApiDiagnosticResult();
    
    // 1. 基础连接测试
    result.baseConnection = await _testBaseConnection();
    
    // 2. 健康检查端点测试
    result.healthCheck = await _testHealthCheck();
    
    // 3. 认证端点测试
    result.authEndpoint = await _testAuthEndpoint();
    
    // 4. 仪表盘端点测试
    result.dashboardEndpoints = await _testDashboardEndpoints();
    
    // 5. 其他核心端点测试
    result.coreEndpoints = await _testCoreEndpoints();
    
    // 生成诊断报告
    _generateDiagnosticReport(result);
    
    return result;
  }
  
  /// 测试基础连接
  static Future<bool> _testBaseConnection() async {
    try {
      LoggerService.d('🔗 测试基础连接: ${AppConfig.apiBaseUrl}');
      
      final dio = Dio(BaseOptions(
        baseUrl: AppConfig.apiBaseUrl,
        connectTimeout: const Duration(seconds: 5),
        receiveTimeout: const Duration(seconds: 3),
      ));
      
      final response = await dio.get('/');
      LoggerService.d('✅ 基础连接成功，状态码: ${response.statusCode}');
      return true;
    } catch (e) {
      LoggerService.e('❌ 基础连接失败', e);
      return false;
    }
  }
  
  /// 测试健康检查端点
  static Future<bool> _testHealthCheck() async {
    try {
      LoggerService.d('🏥 测试健康检查端点');
      
      final response = await _apiService.get('/health');
      LoggerService.d('✅ 健康检查成功，状态码: ${response.statusCode}');
      return true;
    } catch (e) {
      LoggerService.e('❌ 健康检查失败', e);
      return false;
    }
  }
  
  /// 测试认证端点
  static Future<bool> _testAuthEndpoint() async {
    try {
      LoggerService.d('🔐 测试认证端点');
      
      final response = await _apiService.get('/api/v1/auth/me');
      LoggerService.d('✅ 认证端点响应，状态码: ${response.statusCode}');
      return true;
    } catch (e) {
      LoggerService.e('❌ 认证端点失败', e);
      return false;
    }
  }
  
  /// 测试仪表盘端点
  static Future<Map<String, bool>> _testDashboardEndpoints() async {
    final endpoints = {
      'overview': '/api/v1/dashboard/overview',
      'sales-statistics': '/api/v1/dashboard/sales-statistics',
      'inventory-statistics': '/api/v1/dashboard/inventory-statistics',
      'financial-statistics': '/api/v1/dashboard/financial-statistics',
    };
    
    final results = <String, bool>{};
    
    for (final entry in endpoints.entries) {
      try {
        LoggerService.d('📊 测试仪表盘端点: ${entry.value}');
        
        final response = await _apiService.get(entry.value);
        results[entry.key] = response.statusCode == 200;
        LoggerService.d('✅ ${entry.key} 端点成功，状态码: ${response.statusCode}');
      } catch (e) {
        results[entry.key] = false;
        LoggerService.e('❌ ${entry.key} 端点失败', e);
      }
    }
    
    return results;
  }
  
  /// 测试其他核心端点
  static Future<Map<String, bool>> _testCoreEndpoints() async {
    final endpoints = {
      'jewelry': '/api/v1/jewelry',
      'stores': '/api/v1/stores',
      'stock': '/api/v1/stock',
      'sales': '/api/v1/sales',
      'recycling': '/api/v1/recycling',
    };
    
    final results = <String, bool>{};
    
    for (final entry in endpoints.entries) {
      try {
        LoggerService.d('🔧 测试核心端点: ${entry.value}');
        
        final response = await _apiService.get(entry.value);
        results[entry.key] = response.statusCode == 200;
        LoggerService.d('✅ ${entry.key} 端点成功，状态码: ${response.statusCode}');
      } catch (e) {
        results[entry.key] = false;
        LoggerService.e('❌ ${entry.key} 端点失败', e);
      }
    }
    
    return results;
  }
  
  /// 生成诊断报告
  static void _generateDiagnosticReport(ApiDiagnosticResult result) {
    LoggerService.i('📋 ========== API诊断报告 ==========');
    LoggerService.i('🔗 基础连接: ${result.baseConnection ? "✅ 成功" : "❌ 失败"}');
    LoggerService.i('🏥 健康检查: ${result.healthCheck ? "✅ 成功" : "❌ 失败"}');
    LoggerService.i('🔐 认证端点: ${result.authEndpoint ? "✅ 成功" : "❌ 失败"}');
    
    LoggerService.i('📊 仪表盘端点:');
    result.dashboardEndpoints.forEach((key, value) {
      LoggerService.i('   - $key: ${value ? "✅ 成功" : "❌ 失败"}');
    });
    
    LoggerService.i('🔧 核心端点:');
    result.coreEndpoints.forEach((key, value) {
      LoggerService.i('   - $key: ${value ? "✅ 成功" : "❌ 失败"}');
    });
    
    // 生成建议
    _generateRecommendations(result);
    
    LoggerService.i('================================');
  }
  
  /// 生成修复建议
  static void _generateRecommendations(ApiDiagnosticResult result) {
    LoggerService.i('💡 修复建议:');
    
    if (!result.baseConnection) {
      LoggerService.i('   1. 检查后端服务是否启动 (http://localhost:8000)');
      LoggerService.i('   2. 检查防火墙设置');
      LoggerService.i('   3. 检查端口8000是否被占用');
    }
    
    if (!result.healthCheck) {
      LoggerService.i('   4. 后端可能缺少健康检查端点 /health');
    }
    
    if (!result.authEndpoint) {
      LoggerService.i('   5. 认证服务可能未正确配置');
    }
    
    final failedDashboard = result.dashboardEndpoints.entries
        .where((e) => !e.value)
        .map((e) => e.key)
        .toList();
    
    if (failedDashboard.isNotEmpty) {
      LoggerService.i('   6. 仪表盘端点失败: ${failedDashboard.join(", ")}');
    }
    
    final failedCore = result.coreEndpoints.entries
        .where((e) => !e.value)
        .map((e) => e.key)
        .toList();
    
    if (failedCore.isNotEmpty) {
      LoggerService.i('   7. 核心功能端点失败: ${failedCore.join(", ")}');
    }
  }
  
  /// 快速连接测试
  static Future<bool> quickConnectionTest() async {
    try {
      LoggerService.d('⚡ 快速连接测试');
      
      final response = await _apiService.get('/').timeout(
        const Duration(seconds: 3),
        onTimeout: () => throw Exception('连接超时'),
      );
      
      LoggerService.d('✅ 快速连接成功');
      return true;
    } catch (e) {
      LoggerService.e('❌ 快速连接失败', e);
      return false;
    }
  }
}

/// API诊断结果
class ApiDiagnosticResult {
  bool baseConnection = false;
  bool healthCheck = false;
  bool authEndpoint = false;
  Map<String, bool> dashboardEndpoints = {};
  Map<String, bool> coreEndpoints = {};
  
  /// 是否所有测试都通过
  bool get allPassed => 
      baseConnection && 
      healthCheck && 
      authEndpoint && 
      dashboardEndpoints.values.every((v) => v) && 
      coreEndpoints.values.every((v) => v);
  
  /// 获取失败的端点数量
  int get failedEndpointsCount {
    int count = 0;
    if (!baseConnection) count++;
    if (!healthCheck) count++;
    if (!authEndpoint) count++;
    count += dashboardEndpoints.values.where((v) => !v).length;
    count += coreEndpoints.values.where((v) => !v).length;
    return count;
  }
}
