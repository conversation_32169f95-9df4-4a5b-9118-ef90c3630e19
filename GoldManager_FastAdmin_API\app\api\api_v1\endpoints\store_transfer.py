"""
门店调拨API路由

老板，这个模块提供门店调拨的API接口，包括：
1. 调拨单的CRUD操作
2. 审核流程管理
3. 统计分析功能
4. 查询和筛选功能

完整的门店调拨业务API接口。
"""

from typing import List, Any, Dict
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body
from sqlalchemy.orm import Session

from ....core.database import get_db
from ....services.store_transfer_service import StoreTransferService
from ....schemas.store_transfer import (
    StoreTransferCreate, StoreTransferUpdate, StoreTransferAuditUpdate,
    StoreTransferResponse, StoreTransferStatistics, StoreTransferQueryParams,
    StoreTransferPaymentUpdate
)
from ....schemas.common import PaginatedResponse, StandardResponse


router = APIRouter()


def get_store_transfer_service(db: Session = Depends(get_db)) -> StoreTransferService:
    """获取门店调拨服务实例"""
    return StoreTransferService(db)


@router.get("/", response_model=PaginatedResponse[StoreTransferResponse], summary="获取调拨单列表")
async def get_transfers(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    status: int = Query(None, ge=0, le=2, description="状态筛选:0=待审核,1=已通过,2=已拒绝"),
    from_store_id: int = Query(None, gt=0, description="源店铺筛选"),
    to_store_id: int = Query(None, gt=0, description="目标店铺筛选"),
    admin_id: int = Query(None, gt=0, description="操作员筛选"),
    keyword: str = Query(None, description="关键词搜索(单号、备注)"),
    start_time: int = Query(None, description="开始时间(时间戳)"),
    end_time: int = Query(None, description="结束时间(时间戳)"),
    service: StoreTransferService = Depends(get_store_transfer_service)
):
    """
    获取调拨单列表

    支持多种筛选条件：
    - 状态筛选
    - 门店筛选
    - 操作员筛选
    - 关键词搜索
    - 时间范围筛选
    """
    try:
        transfers, total = await service.get_transfers(
            page=page,
            page_size=page_size,
            status=status,
            from_store_id=from_store_id,
            to_store_id=to_store_id,
            admin_id=admin_id,
            keyword=keyword,
            start_time=start_time,
            end_time=end_time
        )

        # 计算总页数
        pages = (total + page_size - 1) // page_size

        return PaginatedResponse(
            success=True,
            code=200,
            message="获取调拨单列表成功",
            data=transfers,
            pagination={
                "page": page,
                "page_size": page_size,
                "total": total,
                "pages": pages
            }
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/statistics", response_model=StandardResponse[StoreTransferStatistics], summary="获取调拨单统计")
async def get_transfer_statistics(
    start_time: int = Query(None, description="开始时间(时间戳)"),
    end_time: int = Query(None, description="结束时间(时间戳)"),
    from_store_id: int = Query(None, gt=0, description="源店铺筛选"),
    to_store_id: int = Query(None, gt=0, description="目标店铺筛选"),
    service: StoreTransferService = Depends(get_store_transfer_service)
):
    """
    获取调拨单统计信息

    包括：
    - 状态分布统计
    - 门店分布统计
    - 金额和数量统计
    """
    try:
        result = await service.get_statistics(
            start_time=start_time,
            end_time=end_time,
            from_store_id=from_store_id,
            to_store_id=to_store_id
        )
        return StandardResponse(
            success=True,
            code=200,
            message="获取调拨单统计成功",
            data=result
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/validate-jewelry", response_model=StandardResponse[Dict[str, Any]], summary="验证商品调拨")
async def validate_jewelry(
    barcode: str = Body(..., description="商品条码"),
    from_store_id: int = Body(..., gt=0, description="源店铺ID"),
    to_store_id: int = Body(..., gt=0, description="目标店铺ID"),
    service: StoreTransferService = Depends(get_store_transfer_service)
):
    """
    验证商品是否可以调拨

    检查：
    - 商品是否存在
    - 商品是否在源店铺
    - 商品状态是否允许调拨
    - 源店铺和目标店铺是否有效
    """
    try:
        result = await service.validate_jewelry_transfer(barcode, from_store_id, to_store_id)
        return StandardResponse(
            success=True,
            code=200,
            message="商品验证完成",
            data=result
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/{transfer_id}", response_model=StandardResponse[StoreTransferResponse], summary="获取调拨单详情")
async def get_transfer(
    transfer_id: int = Path(..., gt=0, description="调拨单ID"),
    service: StoreTransferService = Depends(get_store_transfer_service)
):
    """根据ID获取调拨单详情"""
    try:
        transfer = await service.get_transfer_by_id(transfer_id)
        if not transfer:
            raise HTTPException(status_code=404, detail="调拨单不存在")

        return StandardResponse(
            success=True,
            code=200,
            message="获取调拨单详情成功",
            data=transfer
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/by-no/{transfer_no}", response_model=StandardResponse[StoreTransferResponse], summary="根据单号获取调拨单详情")
async def get_transfer_by_no(
    transfer_no: str = Path(..., description="调拨单号"),
    service: StoreTransferService = Depends(get_store_transfer_service)
):
    """根据单号获取调拨单详情"""
    try:
        transfer = await service.get_transfer_by_no(transfer_no)
        if not transfer:
            raise HTTPException(status_code=404, detail="调拨单不存在")

        return StandardResponse(
            success=True,
            code=200,
            message="获取调拨单详情成功",
            data=transfer
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


# 添加不带斜杠的创建调拨单路由以解决 307 重定向问题
@router.post("", response_model=StandardResponse[StoreTransferResponse], summary="创建调拨单（无斜杠）")
async def create_transfer_no_slash(
    transfer_data: StoreTransferCreate = Body(..., description="调拨单数据"),
    admin_id: int = Query(..., gt=0, description="操作员ID"),
    service: StoreTransferService = Depends(get_store_transfer_service)
):
    """
    创建新的调拨单（不带斜杠的路由，解决 307 重定向问题）

    需要提供：
    - 源店铺和目标店铺
    - 调拨商品明细
    - 操作员ID
    """
    try:
        result = await service.create_transfer(transfer_data, admin_id)
        return StandardResponse(
            success=True,
            code=200,
            message="创建调拨单成功",
            data=result
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/", response_model=StandardResponse[StoreTransferResponse], summary="创建调拨单")
async def create_transfer(
    transfer_data: StoreTransferCreate = Body(..., description="调拨单数据"),
    admin_id: int = Query(..., gt=0, description="操作员ID"),
    service: StoreTransferService = Depends(get_store_transfer_service)
):
    """
    创建新的调拨单

    需要提供：
    - 源店铺和目标店铺
    - 调拨商品明细
    - 操作员ID
    """
    try:
        result = await service.create_transfer(transfer_data, admin_id)
        return StandardResponse(
            success=True,
            code=200,
            message="创建调拨单成功",
            data=result
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/{transfer_id}", response_model=StandardResponse[StoreTransferResponse], summary="更新调拨单")
async def update_transfer(
    transfer_id: int = Path(..., gt=0, description="调拨单ID"),
    transfer_data: StoreTransferUpdate = Body(..., description="更新数据"),
    service: StoreTransferService = Depends(get_store_transfer_service)
):
    """
    更新调拨单信息

    注意：只有待审核状态的调拨单才能更新
    """
    try:
        result = await service.update_transfer(transfer_id, transfer_data)
        return StandardResponse(
            success=True,
            code=200,
            message="更新调拨单成功",
            data=result
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/{transfer_id}", response_model=StandardResponse[bool], summary="删除调拨单")
async def delete_transfer(
    transfer_id: int = Path(..., gt=0, description="调拨单ID"),
    service: StoreTransferService = Depends(get_store_transfer_service)
):
    """
    删除调拨单

    注意：只有待审核状态的调拨单才能删除
    """
    try:
        success = await service.delete_transfer(transfer_id)
        if success:
            return StandardResponse(
                success=True,
                code=200,
                message="删除调拨单成功",
                data=True
            )
        else:
            raise HTTPException(status_code=400, detail="删除调拨单失败")
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.patch("/{transfer_id}/audit", response_model=StandardResponse[StoreTransferResponse], summary="审核调拨单")
async def audit_transfer(
    transfer_id: int = Path(..., gt=0, description="调拨单ID"),
    audit_data: StoreTransferAuditUpdate = Body(..., description="审核数据"),
    auditor_id: int = Query(..., gt=0, description="审核员ID"),
    service: StoreTransferService = Depends(get_store_transfer_service)
):
    """
    审核调拨单

    审核通过后，商品将自动转移到目标店铺
    """
    try:
        result = await service.audit_transfer(transfer_id, audit_data, auditor_id)
        status_text = "通过" if audit_data.status == 1 else "拒绝"
        return StandardResponse(
            success=True,
            code=200,
            message=f"调拨单审核{status_text}成功",
            data=result
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.patch("/{transfer_id}/payment", response_model=StandardResponse[StoreTransferResponse], summary="更新调拨单收款信息")
async def update_transfer_payment(
    transfer_id: int = Path(..., gt=0, description="调拨单ID"),
    payment_data: StoreTransferPaymentUpdate = Body(..., description="收款数据"),
    auditor_id: int = Query(..., gt=0, description="审核员ID（收款时同时审核）"),
    service: StoreTransferService = Depends(get_store_transfer_service)
):
    """
    更新调拨单收款信息

    根据收款方案.md实现简化流程：
    - 收款成功后同时完成审核通过操作
    - 状态直接从待审核→审核通过的跳转
    """
    try:
        result = await service.update_payment(transfer_id, payment_data, auditor_id)
        return StandardResponse(
            success=True,
            code=200,
            message="收款信息更新成功",
            data=result
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))
