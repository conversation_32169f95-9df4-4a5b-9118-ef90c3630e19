"""
退货管理数据验证模型

老板，这个模块定义了退货管理的数据验证和序列化模型，包括：
1. 退货单相关的请求和响应模型
2. 退货商品明细的验证模型
3. 分页和统计响应模型

使用Pydantic v2进行数据验证和序列化。
"""

from typing import List, Optional
from decimal import Decimal
from pydantic import BaseModel, Field, ConfigDict
from app.schemas.common import PaginatedResponse


# ==================== 退货商品明细模型 ====================

class StockReturnItemBase(BaseModel):
    """退货商品明细基础模型"""
    jewelry_id: Optional[int] = Field(None, description="商品ID")
    barcode: str = Field(..., max_length=30, description="商品条码")
    name: str = Field(..., max_length=100, description="商品名称")
    category_id: Optional[int] = Field(None, description="分类ID")
    ring_size: Optional[str] = Field(None, max_length=20, description="圈口")

    # 重量信息
    gold_weight: Decimal = Field(Decimal('0.00'), ge=0, description="金重(g)")
    silver_weight: Decimal = Field(Decimal('0.00'), ge=0, description="银重(g)")
    total_weight: Decimal = Field(Decimal('0.00'), ge=0, description="总重(g)")

    # 价格信息
    gold_price: Decimal = Field(Decimal('0.00'), ge=0, description="金价(元/g)")
    silver_price: Decimal = Field(Decimal('0.00'), ge=0, description="银价(元/g)")
    silver_work_price: Decimal = Field(Decimal('0.00'), ge=0, description="银工费(元/g)")
    piece_work_price: Decimal = Field(Decimal('0.00'), ge=0, description="件工费(元)")

    # 退货信息
    amount: Decimal = Field(Decimal('0.00'), ge=0, description="金额")
    source_order_no: Optional[str] = Field(None, max_length=20, description="来源出库单号")


class StockReturnItemCreate(StockReturnItemBase):
    """创建退货商品明细请求模型"""
    pass


class StockReturnItemUpdate(StockReturnItemBase):
    """更新退货商品明细请求模型"""
    barcode: Optional[str] = Field(None, max_length=30, description="商品条码")
    name: Optional[str] = Field(None, max_length=100, description="商品名称")
    amount: Optional[Decimal] = Field(None, ge=0, description="金额")


class StockReturnItemResponse(StockReturnItemBase):
    """退货商品明细响应模型"""
    model_config = ConfigDict(from_attributes=True)

    id: int = Field(..., description="退货明细ID")
    stock_return_id: int = Field(..., description="退货单ID")

    # 时间信息
    createtime: Optional[int] = Field(None, description="创建时间")
    updatetime: Optional[int] = Field(None, description="更新时间")

    # 关联信息
    jewelry_name: Optional[str] = Field(None, description="商品名称")
    category_name: Optional[str] = Field(None, description="分类名称")


# ==================== 退货单模型 ====================

class StockReturnBase(BaseModel):
    """退货单基础模型"""
    store_id: int = Field(..., gt=0, description="退货门店ID")
    customer: Optional[str] = Field(None, max_length=50, description="客户")
    total_amount: Decimal = Field(Decimal('0.00'), ge=0, description="应收金额")
    discount_amount: Decimal = Field(Decimal('0.00'), ge=0, description="优惠金额")
    actual_amount: Decimal = Field(Decimal('0.00'), ge=0, description="实际退款")
    remark: Optional[str] = Field(None, max_length=255, description="备注")


class StockReturnCreate(StockReturnBase):
    """创建退货单请求模型"""
    items: List[StockReturnItemCreate] = Field(..., min_length=1, description="退货商品明细列表")


class StockReturnUpdate(StockReturnBase):
    """更新退货单请求模型"""
    store_id: Optional[int] = Field(None, gt=0, description="退货门店ID")
    items: Optional[List[StockReturnItemUpdate]] = Field(None, description="退货商品明细列表")


class StockReturnAuditUpdate(BaseModel):
    """退货单审核请求模型"""
    status: int = Field(..., ge=2, le=4, description="审核状态:2=已通过,3=未通过,4=已作废")
    audit_remark: Optional[str] = Field(None, max_length=255, description="审核备注")


class StockReturnResponse(StockReturnBase):
    """退货单响应模型"""
    model_config = ConfigDict(from_attributes=True)

    id: int = Field(..., description="退货单ID")
    order_no: str = Field(..., description="退货单号")
    operator_id: int = Field(..., description="操作员ID")
    status: int = Field(..., description="状态:1=待审核,2=已通过,3=未通过,4=已作废")
    audit_user_id: Optional[int] = Field(None, description="审核人ID")
    audit_time: Optional[int] = Field(None, description="审核时间")
    audit_remark: Optional[str] = Field(None, description="审核备注")

    # 时间信息
    createtime: Optional[int] = Field(None, description="创建时间")
    updatetime: Optional[int] = Field(None, description="更新时间")

    # 关联信息
    store_name: Optional[str] = Field(None, description="门店名称")
    operator_name: Optional[str] = Field(None, description="操作员姓名")
    auditor_name: Optional[str] = Field(None, description="审核员姓名")

    # 退货明细
    items: List[StockReturnItemResponse] = Field(default_factory=list, description="退货商品明细列表")


# ==================== 统计模型 ====================

class StockReturnStatistics(BaseModel):
    """退货单统计信息模型"""
    total_count: int = Field(0, description="总退货单数")
    pending_count: int = Field(0, description="待审核数量")
    approved_count: int = Field(0, description="已通过数量")
    rejected_count: int = Field(0, description="未通过数量")
    cancelled_count: int = Field(0, description="已作废数量")

    total_amount: Decimal = Field(Decimal('0.00'), description="总应收金额")
    total_discount: Decimal = Field(Decimal('0.00'), description="总优惠金额")
    total_actual: Decimal = Field(Decimal('0.00'), description="总实际退款")

    # 状态分布
    status_distribution: dict = Field(default_factory=dict, description="状态分布")

    # 门店分布
    store_distribution: dict = Field(default_factory=dict, description="门店分布")


# ==================== 分页响应模型 ====================

class StockReturnListResponse(PaginatedResponse):
    """退货单列表响应模型"""
    items: List[StockReturnResponse] = Field(default_factory=list, description="退货单列表")


# ==================== 状态枚举 ====================

class StockReturnStatus:
    """退货单状态枚举"""
    PENDING = 1      # 待审核
    APPROVED = 2     # 已通过
    REJECTED = 3     # 未通过
    CANCELLED = 4    # 已作废

    @classmethod
    def get_status_name(cls, status: int) -> str:
        """获取状态名称"""
        status_map = {
            cls.PENDING: "待审核",
            cls.APPROVED: "已通过",
            cls.REJECTED: "未通过",
            cls.CANCELLED: "已作废"
        }
        return status_map.get(status, "未知状态")

    @classmethod
    def get_all_status(cls) -> dict:
        """获取所有状态"""
        return {
            cls.PENDING: "待审核",
            cls.APPROVED: "已通过",
            cls.REJECTED: "未通过",
            cls.CANCELLED: "已作废"
        }
