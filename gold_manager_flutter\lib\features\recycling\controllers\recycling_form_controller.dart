import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:uuid/uuid.dart';

import '../../../services/auth_service.dart';
import '../../../services/store_service.dart';
import '../../../services/category_service.dart';
import '../../../core/utils/logger.dart';
import '../../../models/recycling/recycling_model.dart';
import '../../../models/store/store.dart';
import '../../../models/jewelry/category.dart';
import '../services/recycling_service.dart';

/// 回收单表单控制器 - 参考StockOutFormController的实现
class RecyclingFormController extends GetxController {
  final RecyclingService _recyclingService = RecyclingService();
  final AuthService _authService = Get.find<AuthService>();
  final StoreService _storeService = Get.find<StoreService>();
  final CategoryService _categoryService = Get.find<CategoryService>();

  // 表单状态
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  final RxBool isLoading = false.obs;
  final RxBool isEditing = false.obs;

  // 表单控制器
  final customerNameController = TextEditingController();
  final customerPhoneController = TextEditingController();
  final remarkController = TextEditingController();

  // 门店相关
  final RxInt selectedStoreId = 0.obs;
  final RxList<Store> storeList = <Store>[].obs;

  // 商品分类相关
  final RxList<Category> categoryList = <Category>[].obs;

  // 回收物品列表
  final RxList<RecyclingItemForm> itemList = <RecyclingItemForm>[].obs;

  // 计算字段
  final RxDouble totalWeight = 0.0.obs;
  final RxDouble totalAmount = 0.0.obs;
  final RxDouble totalFinalAmount = 0.0.obs; // 总折后金额
  final RxInt itemCount = 0.obs;

  // 当前编辑的回收单
  final Rx<RecyclingOrder?> currentRecyclingOrder = Rx<RecyclingOrder?>(null);

  // UI更新回调
  Function(int, String, String)? _uiUpdateCallback;

  @override
  void onInit() {
    super.onInit();
    _initializeStores();
    _initializeCategories();
    _setupCalculationListeners();
    // 初始化时不添加默认物品，让用户主动添加
  }

  @override
  void onClose() {
    customerNameController.dispose();
    customerPhoneController.dispose();
    remarkController.dispose();
    _clearItemList();
    super.onClose();
  }

  /// 设置UI更新回调
  void setUIUpdateCallback(Function(int, String, String) callback) {
    _uiUpdateCallback = callback;
  }

  /// 初始化门店列表
  Future<void> _initializeStores() async {
    try {
      LoggerService.d('开始获取门店列表');
      // 从API获取真实门店数据
      final stores = await _storeService.getAllStores(status: 1); // 只获取正常状态的门店
      storeList.assignAll(stores);

      // 设置默认门店
      if (storeList.isNotEmpty) {
        // 如果用户有指定门店，优先选择用户门店
        final userStoreId = _authService.storeId.value;
        if (userStoreId > 0 &&
            storeList.any((store) => store.id == userStoreId)) {
          selectedStoreId.value = userStoreId;
        } else {
          selectedStoreId.value = storeList.first.id;
        }
      }

      LoggerService.d('门店列表获取成功，共${storeList.length}个门店');
    } catch (e) {
      LoggerService.e('获取门店列表失败', e);

      // API调用失败时使用备用数据
      storeList.assignAll([
        const Store(
          id: 1,
          name: '总店',
          code: 'MAIN',
          address: '',
          phone: '',
          manager: '',
        ),
        const Store(
          id: 2,
          name: '分店A',
          code: 'BRANCH_A',
          address: '',
          phone: '',
          manager: '',
        ),
        const Store(
          id: 3,
          name: '分店B',
          code: 'BRANCH_B',
          address: '',
          phone: '',
          manager: '',
        ),
        const Store(
          id: 4,
          name: '分店C',
          code: 'BRANCH_C',
          address: '',
          phone: '',
          manager: '',
        ),
      ]);

      // 设置默认门店
      if (storeList.isNotEmpty) {
        selectedStoreId.value = storeList.first.id;
      }

      Get.snackbar(
        '警告',
        '门店数据加载失败，使用默认数据: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
    }
  }

  /// 初始化商品分类列表
  Future<void> _initializeCategories() async {
    try {
      LoggerService.d('开始获取商品分类列表');
      // 从API获取真实分类数据
      final categories = await _categoryService.getCategories();
      categoryList.assignAll(categories);

      LoggerService.d('商品分类列表获取成功，共${categoryList.length}个分类');
    } catch (e) {
      LoggerService.e('获取商品分类列表失败', e);

      // API调用失败时使用备用数据
      categoryList.assignAll([
        const Category(id: 1, name: '黄金首饰', parentId: 0, description: '黄金类首饰'),
        const Category(id: 2, name: '白银首饰', parentId: 0, description: '白银类首饰'),
        const Category(id: 3, name: '铂金首饰', parentId: 0, description: '铂金类首饰'),
        const Category(id: 4, name: '钻石首饰', parentId: 0, description: '钻石类首饰'),
        const Category(id: 5, name: '其他首饰', parentId: 0, description: '其他类型首饰'),
      ]);

      Get.snackbar(
        '警告',
        '分类数据加载失败，使用默认数据: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
    }
  }

  /// 设置计算监听器
  void _setupCalculationListeners() {
    // 监听物品列表变化，自动计算总计
    itemList.listen((_) => _calculateTotals());
  }

  /// 计算总计
  void _calculateTotals() {
    double weight = 0.0;
    double amount = 0.0;
    double finalAmount = 0.0;
    int count = 0;

    for (var item in itemList) {
      // 更新物品的计算
      item.updateCalculations();

      // 计算总重量（金重 + 银重）
      weight += item.goldWeight + item.silverWeight;

      // 计算总金额和总折后金额
      amount += item.amount;
      finalAmount += item.finalAmount;
      count++;

      // 更新兼容字段
      item.weight = item.goldWeight + item.silverWeight;
    }

    totalWeight.value = weight;
    totalAmount.value = amount;
    totalFinalAmount.value = finalAmount;
    itemCount.value = count;

    LoggerService.d(
      '总计更新: 重量=${weight.toStringAsFixed(2)}g, 金额=${amount.toStringAsFixed(2)}元, 折后金额=${finalAmount.toStringAsFixed(2)}元',
    );
  }

  /// 添加回收物品
  void addRecyclingItem() {
    final newItem = RecyclingItemForm();
    itemList.add(newItem);
    _calculateTotals(); // 添加新物品后重新计算总计
    LoggerService.d('添加新回收物品，当前物品数量: ${itemList.length}');
  }

  /// 移除回收物品
  void removeRecyclingItem(int index) {
    if (index >= 0 && index < itemList.length) {
      final item = itemList[index];
      item.dispose(); // 清理控制器
      itemList.removeAt(index);
      _calculateTotals(); // 移除物品后重新计算总计
      LoggerService.d('移除回收物品，索引: $index，剩余物品数量: ${itemList.length}');
    }
  }

  /// 手动触发计算总计 - 供UI调用
  void recalculateTotals() {
    _calculateTotals();
  }

  /// 更新物品字段
  void updateItemField(
    int index, {
    String? itemName,
    int? categoryId,
    double? weight,
    double? price,
    double? amount,
    String? remark,
    double? goldWeight,
    double? goldPrice,
    double? silverWeight,
    double? silverPrice,
    double? discountRate,
    String? type,
    String? category,
  }) {
    if (index >= 0 && index < itemList.length) {
      final item = itemList[index];

      if (itemName != null) item.itemName = itemName;
      if (categoryId != null) item.categoryId = categoryId;
      if (weight != null) item.weight = weight;
      if (price != null) item.price = price;
      if (amount != null) item.amount = amount;
      if (remark != null) item.remark = remark;

      // 更新贵金属专用字段
      if (goldWeight != null) item.goldWeight = goldWeight;
      if (goldPrice != null) item.goldPrice = goldPrice;
      if (silverWeight != null) item.silverWeight = silverWeight;
      if (silverPrice != null) item.silverPrice = silverPrice;
      if (discountRate != null) item.discountRate = discountRate;
      if (type != null) item.type = type;
      if (category != null) item.category = category;

      // 重新计算金额
      bool needsRecalculation =
          goldWeight != null ||
          goldPrice != null ||
          silverWeight != null ||
          silverPrice != null ||
          discountRate != null ||
          weight != null ||
          price != null;

      if (needsRecalculation) {
        item.updateCalculations();

        // 通知UI更新
        if (_uiUpdateCallback != null) {
          _uiUpdateCallback!(index, 'amount', item.amount.toString());
          _uiUpdateCallback!(index, 'finalAmount', item.finalAmount.toString());
        }
      }

      // 手动触发itemList更新以通知UI
      itemList.refresh();

      _calculateTotals();
    }
  }

  /// 保存回收单
  Future<bool> saveRecyclingOrder() async {
    if (!_validateForm()) {
      return false;
    }

    try {
      isLoading.value = true;

      // 构建回收单数据
      final recyclingOrder = RecyclingOrder(
        id: isEditing.value ? currentRecyclingOrder.value!.id : 0,
        orderNo: isEditing.value
            ? currentRecyclingOrder.value!.orderNo
            : 'REC${DateTime.now().year}${DateTime.now().month.toString().padLeft(2, '0')}${const Uuid().v4().substring(0, 6)}',
        orderDate: DateTime.now(),
        customerId: 0, // 暂时设为0
        customerName: customerNameController.text,
        customerPhone: customerPhoneController.text,
        totalWeight: totalWeight.value,
        totalAmount: totalAmount.value,
        itemCount: itemCount.value,
        remark: remarkController.text,
        status: 1, // 直接设置为审核通过状态
        createdAt: DateTime.now(),
        createdBy: _authService.userId.value,
        creatorName: _authService.userNickname.value.isNotEmpty
            ? _authService.userNickname.value
            : _authService.userName.value,
        storeId: selectedStoreId.value,
        storeName: storeList
            .firstWhere((store) => store.id == selectedStoreId.value)
            .name,
        items: itemList
            .map(
              (item) => RecyclingItem(
                id: item.id,
                orderId: 0, // 保存后会更新
                categoryId: item.categoryId,
                categoryName: _getCategoryName(item.categoryId),
                itemName: item.remark.isNotEmpty
                    ? item.remark
                    : '未命名物品', // 使用品名作为物品名称
                weight: item.goldWeight + item.silverWeight, // 总重量
                price: (item.goldWeight + item.silverWeight) > 0
                    ? (item.goldWeight * item.goldPrice +
                              item.silverWeight * item.silverPrice) /
                          (item.goldWeight + item.silverWeight)
                    : 0.0, // 平均价格
                amount: item.finalAmount, // 使用折后金额
                goldWeight: item.goldWeight,
                goldPrice: item.goldPrice,
                goldAmount: item.goldWeight * item.goldPrice,
                silverWeight: item.silverWeight,
                silverPrice: item.silverPrice,
                silverAmount: item.silverWeight * item.silverPrice,
                discountRate: item.discountRate,
                discountAmount: item.discountAmount,
                photoUrl: '',
                status: 0,
                remark: item.remark,
              ),
            )
            .toList(),
      );

      // 调用服务保存 - 使用符合后端API格式的数据
      final orderData = _buildApiData(recyclingOrder);
      await _recyclingService.createRecyclingOrder(orderData);

      // 不在这里显示提示，由UI层处理

      return true;
    } catch (e) {
      LoggerService.e('保存回收单失败', e);
      Get.snackbar(
        '保存失败',
        '保存回收单失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// 构建符合后端API格式的数据
  Map<String, dynamic> _buildApiData(RecyclingOrder recyclingOrder) {
    return {
      'store_id': recyclingOrder.storeId,
      'customer_name': recyclingOrder.customerName,
      'phone': recyclingOrder.customerPhone, // 正确映射客户电话字段
      'remark': recyclingOrder.remark,
      'items': itemList
          .map(
            (item) => {
              'name': item.remark.isNotEmpty ? item.remark : '未命名物品',
              'category_id': item.categoryId,
              // 修复字段映射：使用贵金属专用字段
              'gold_weight': item.goldWeight,
              'gold_price': item.goldPrice,
              'silver_weight': item.silverWeight,
              'silver_price': item.silverPrice,
              'discount_rate': item.discountRate,
              'remark': item.remark,
            },
          )
          .toList(),
    };
  }

  /// 验证表单
  bool _validateForm() {
    if (customerNameController.text.trim().isEmpty) {
      Get.snackbar('验证失败', '请输入客户姓名', snackPosition: SnackPosition.BOTTOM);
      return false;
    }

    if (itemList.isEmpty) {
      Get.snackbar('验证失败', '请至少添加一个回收物品', snackPosition: SnackPosition.BOTTOM);
      return false;
    }

    for (int i = 0; i < itemList.length; i++) {
      final item = itemList[i];

      // 验证品名
      if (item.remark.trim().isEmpty) {
        Get.snackbar(
          '验证失败',
          '第${i + 1}个物品的品名不能为空',
          snackPosition: SnackPosition.BOTTOM,
        );
        return false;
      }

      // 验证重量（金重或银重至少有一个大于0）
      if (item.goldWeight <= 0 && item.silverWeight <= 0) {
        Get.snackbar(
          '验证失败',
          '第${i + 1}个物品的金重或银重必须大于0',
          snackPosition: SnackPosition.BOTTOM,
        );
        return false;
      }

      // 验证价格（如果有金重，金价必须大于0）
      if (item.goldWeight > 0 && item.goldPrice <= 0) {
        Get.snackbar(
          '验证失败',
          '第${i + 1}个物品的金价必须大于0',
          snackPosition: SnackPosition.BOTTOM,
        );
        return false;
      }

      // 验证价格（如果有银重，银价必须大于0）
      if (item.silverWeight > 0 && item.silverPrice <= 0) {
        Get.snackbar(
          '验证失败',
          '第${i + 1}个物品的银价必须大于0',
          snackPosition: SnackPosition.BOTTOM,
        );
        return false;
      }
    }

    return true;
  }

  /// 获取分类名称
  String _getCategoryName(int categoryId) {
    try {
      final category = categoryList.firstWhere((cat) => cat.id == categoryId);
      return category.name;
    } catch (e) {
      // 如果找不到分类，返回默认名称
      switch (categoryId) {
        case 1:
          return '黄金首饰';
        case 2:
          return '白银首饰';
        case 3:
          return '铂金首饰';
        default:
          return '其他';
      }
    }
  }

  /// 清理物品列表
  void _clearItemList() {
    for (var item in itemList) {
      item.dispose();
    }
    itemList.clear();
  }

  /// 重置表单到初始状态
  void resetForm() {
    // 清空表单字段
    customerNameController.clear();
    customerPhoneController.clear();
    remarkController.clear();

    // 重置门店选择
    selectedStoreId.value = storeList.isNotEmpty ? storeList.first.id : 0;

    // 清空物品列表，不添加默认物品
    _clearItemList();

    // 重置编辑状态
    isEditing.value = false;
    currentRecyclingOrder.value = null;

    LoggerService.d('表单已重置到初始状态（空表格）');
  }
}

/// 回收物品表单数据类
class RecyclingItemForm {
  int id = 0;
  String itemName = '';
  int categoryId = 1; // 默认为黄金首饰
  double weight = 0.0;
  double price = 400.0; // 默认价格
  double amount = 0.0;
  String remark = '';
  String type = 'by_weight'; // 按件/按克类型，默认按克
  String? category; // 贵金属分类

  // 贵金属回收专用属性
  double goldWeight = 0.0; // 金重(g)
  double goldPrice = 0.0; // 金价(元/g)
  double silverWeight = 0.0; // 银重(g)
  double silverPrice = 0.0; // 银价(元/g)
  double discountRate = 100.0; // 折扣率(%)，默认100%
  double finalAmount = 0.0; // 折后金额
  double discountAmount = 0.0; // 折扣金额

  RecyclingItemForm() {
    // 设置默认金价和银价
    goldPrice = 400.0; // 默认金价
    silverPrice = 6.0; // 默认银价
  }

  /// 计算金额
  /// 计算公式：金额 = 金重 × 金价 + 银重 × 银价
  void calculateAmount() {
    amount = goldWeight * goldPrice + silverWeight * silverPrice;
    calculateFinalAmount();
  }

  /// 计算折后金额
  /// 计算公式：折后金额 = 金额 × 折扣率(%)
  void calculateFinalAmount() {
    finalAmount = amount * (discountRate / 100);
    discountAmount = amount - finalAmount; // 计算折扣金额
  }

  /// 更新所有计算字段
  void updateCalculations() {
    calculateAmount();
    calculateFinalAmount();
  }

  void dispose() {
    // 如果有TextEditingController，在这里清理
  }
}
