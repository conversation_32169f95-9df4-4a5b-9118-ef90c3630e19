import '../common/document_status.dart';
import '../store/store.dart';
import '../../core/utils/number_utils.dart';
import 'stock_in_item.dart';

/// 入库单模型
class StockIn {
  /// ID
  final int id;

  /// 入库单号
  final String stockInNo;

  /// 门店ID
  final int storeId;

  /// 操作员ID
  final int operatorId;

  /// 总金额
  final double totalAmount;

  /// 状态
  final DocumentStatus status;

  /// 创建时间
  final DateTime createTime;

  /// 备注
  final String? remark;

  /// 审核人ID
  final int? approverUserId;

  /// 审核时间
  final DateTime? approveTime;

  /// 审核备注
  final String? approveRemark;

  /// 商品项列表
  final List<StockInItem>? items;

  /// 关联门店
  final Store? store;

  /// 构造函数
  const StockIn({
    required this.id,
    required this.stockInNo,
    required this.storeId,
    required this.operatorId,
    required this.totalAmount,
    required this.status,
    required this.createTime,
    this.remark,
    this.approverUserId,
    this.approveTime,
    this.approveRemark,
    this.items,
    this.store,
  });

  /// 获取商品件数
  int get itemCount => items?.length ?? 0;

  /// 从JSON构造
  factory StockIn.fromJson(Map<String, dynamic> json) {
    return StockIn(
      id: json['id'],
      stockInNo: json['stock_in_no'],
      storeId: json['store_id'],
      operatorId: json['operator_id'],
      totalAmount: NumberUtils.toDouble(json['total_amount']),
      status: DocumentStatus.fromValue(json['status'] ?? 0),
      createTime: DateTime.fromMillisecondsSinceEpoch(
        (json['create_time'] ?? 0) * 1000,
      ),
      remark: json['remark'],
      approverUserId: json['approver_user_id'],
      approveTime: json['approve_time'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['approve_time'] * 1000)
          : null,
      approveRemark: json['approve_remark'],
      items: json['items'] != null
          ? (json['items'] as List)
                .map(
                  (item) => StockInItem.fromJson(item as Map<String, dynamic>),
                )
                .toList()
          : null,
      store: json['store'] != null ? Store.fromJson(json['store']) : null,
    );
  }

  /// 转为JSON
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = {
      'id': id,
      'stock_in_no': stockInNo,
      'store_id': storeId,
      'operator_id': operatorId,
      'total_amount': totalAmount,
      'status': status.value,
      'create_time': createTime.millisecondsSinceEpoch ~/ 1000,
      'remark': remark,
      'approver_user_id': approverUserId,
      'approve_remark': approveRemark,
    };

    if (approveTime != null) {
      json['approve_time'] = approveTime!.millisecondsSinceEpoch ~/ 1000;
    }

    if (items != null) {
      json['items'] = items!.map((item) => item.toJson()).toList();
    }

    return json;
  }

  /// 是否可编辑
  bool get canEdit =>
      status == DocumentStatus.draft || status == DocumentStatus.pending;

  /// 是否可删除
  bool get canDelete =>
      status == DocumentStatus.draft || status == DocumentStatus.pending;

  /// 是否可审核
  bool get canApprove => status == DocumentStatus.pending;

  /// 是否可取消审核（撤销已审核状态）
  bool get canCancelApprove => status == DocumentStatus.approved;

  /// 复制并修改属性
  StockIn copyWith({
    int? id,
    String? stockInNo,
    int? storeId,
    int? operatorId,
    double? totalAmount,
    DocumentStatus? status,
    DateTime? createTime,
    String? remark,
    int? approverUserId,
    DateTime? approveTime,
    String? approveRemark,
    List<StockInItem>? items,
    Store? store,
  }) {
    return StockIn(
      id: id ?? this.id,
      stockInNo: stockInNo ?? this.stockInNo,
      storeId: storeId ?? this.storeId,
      operatorId: operatorId ?? this.operatorId,
      totalAmount: totalAmount ?? this.totalAmount,
      status: status ?? this.status,
      createTime: createTime ?? this.createTime,
      remark: remark ?? this.remark,
      approverUserId: approverUserId ?? this.approverUserId,
      approveTime: approveTime ?? this.approveTime,
      approveRemark: approveRemark ?? this.approveRemark,
      items: items ?? this.items,
      store: store ?? this.store,
    );
  }
}
