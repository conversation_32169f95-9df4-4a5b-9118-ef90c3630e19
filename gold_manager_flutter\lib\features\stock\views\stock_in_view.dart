
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../core/theme/app_theme.dart';
import '../../../core/constants/border_styles.dart';
import '../../../models/common/document_status.dart';
import '../../../services/auth_service.dart';
import '../../../widgets/empty_state.dart';
import '../../../widgets/loading_state.dart';
import '../../../widgets/responsive_builder.dart';
import '../controllers/stock_in_controller.dart';

/// 入库管理页面
class StockInView extends GetView<StockInController> {
  const StockInView({super.key});

  @override
  Widget build(BuildContext context) {
    return Material(
      color: AppTheme.backgroundColor,
      child: Column(
        children: [
          // 筛选区域
          _buildFilterSection(),
          const Divider(height: 1),
          // 主要内容区域
          Expanded(
            child: Obx(() {
              if (controller.isLoading.value) {
                return const LoadingState(text: '加载中...', timeoutSeconds: 30);
              }

              if (controller.stockInList.isEmpty) {
                return EmptyState(
                  icon: Icons.inventory,
                  title: '暂无入库单',
                  message: '点击右上角按钮创建新的入库单',
                  buttonText: '新建入库单',
                  onButtonPressed: controller.createNewStockIn,
                );
              }

              return _buildStockInList();
            }),
          ),
        ],
      ),
    );
  }

  /// 构建筛选区域 - 复用编辑入库单页面的单行布局结构
  Widget _buildFilterSection() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: AppBorderStyles.tableBorder,
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center, // 确保所有控件垂直居中对齐
        children: [
          // 图标和标题 - 复用编辑入库单页面的样式
          Icon(
            Icons.manage_search,
            color: Colors.blue[600],
            size: 20
          ),
          const SizedBox(width: 8),
          const Text(
            '入库管理',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(width: 24),

          // 操作员信息标签 - 复用编辑入库单页面的样式
          Obx(() {
            final authService = Get.find<AuthService>();
            final operatorName = authService.userNickname.value.isNotEmpty
                ? authService.userNickname.value
                : authService.userName.value;
            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(AppBorderStyles.largeBorderRadius), // 使用统一的大圆角
                border: Border.all(color: Colors.blue[200]!, width: AppBorderStyles.borderWidth), // 使用统一边框宽度
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.person, size: 14, color: Colors.blue[600]),
                  const SizedBox(width: 4),
                  Text(
                    '操作员: $operatorName',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.blue[700],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            );
          }),
          const SizedBox(width: 24),

          // 门店标签和选择器 - 复用编辑入库单页面的样式
          const Text(
            '门店:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(width: 8),
          SizedBox(
            width: 150,
            child: _buildCompactStoreSelector(),
          ),
          const SizedBox(width: 24),

          // 状态标签和选择器 - 新增状态筛选
          const Text(
            '状态:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(width: 8),
          SizedBox(
            width: 120,
            child: _buildCompactStatusSelector(),
          ),
          const SizedBox(width: 24),

          // 搜索框 - 紧凑型设计
          const Text(
            '搜索:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(width: 8),
          SizedBox(
            width: 200,
            child: _buildCompactSearchField(),
          ),
          const SizedBox(width: 24),

          // 日期范围选择器 - 紧凑型设计
          const Text(
            '日期:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(width: 8),
          SizedBox(
            width: 200,
            child: _buildCompactDateRangePicker(),
          ),
          const SizedBox(width: 24),

          // 操作按钮组
          _buildResetButton(),
          const SizedBox(width: 8),
          _buildSearchButton(),
          const SizedBox(width: 8),
          _buildNewStockInButton(),
        ],
      ),
    );
  }

  /// 构建紧凑型搜索字段（单行布局）
  Widget _buildCompactSearchField() {
    return Container(
      height: 32, // 紧凑高度
      decoration: AppBorderStyles.standardBoxDecoration,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: TextField(
          controller: controller.searchController,
          decoration: const InputDecoration(
            hintText: '入库单号、备注等',
            border: InputBorder.none,
            enabledBorder: InputBorder.none,
            focusedBorder: InputBorder.none,
            disabledBorder: InputBorder.none,
            errorBorder: InputBorder.none,
            focusedErrorBorder: InputBorder.none,
            contentPadding: EdgeInsets.zero,
            hintStyle: TextStyle(fontSize: 13, color: Colors.grey),
            isDense: true,
          ),
          style: const TextStyle(fontSize: 13),
          onSubmitted: (_) => controller.search(),
        ),
      ),
    );
  }

  /// 构建紧凑型门店选择器（单行布局）
  Widget _buildCompactStoreSelector() {
    return Obx(() {
      // 检查用户权限
      final authService = Get.find<AuthService>();
      final isAdmin = authService.userRole.value == 'admin' || authService.hasPermission('super.admin');
      final currentUserStoreId = authService.storeId.value;
      final currentStoreName = authService.storeName.value;

      if (!isAdmin && currentUserStoreId > 0) {
        // 普通员工：显示当前用户所属门店，不可选择
        return Container(
          height: 32, // 紧凑高度
          decoration: BoxDecoration(
            border: Border.all(color: AppBorderStyles.borderColor),
            borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
            color: Colors.grey[50],
          ),
          child: Row(
            children: [
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  currentStoreName,
                  style: const TextStyle(
                    fontSize: 13,
                    color: Colors.black87,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const Icon(Icons.lock, size: 14, color: Colors.grey),
              const SizedBox(width: 8),
            ],
          ),
        );
      } else {
        // 管理员：可以选择门店
        return Container(
          height: 32, // 紧凑高度
          decoration: AppBorderStyles.standardBoxDecoration,
          child: DropdownButtonHideUnderline(
            child: DropdownButton<int>(
              value: controller.selectedStoreId.value == 0 ? null : controller.selectedStoreId.value,
              hint: const Text('选择门店', style: TextStyle(fontSize: 13, color: Colors.grey)),
              isExpanded: true,
              items: [
                const DropdownMenuItem<int>(
                  value: null,
                  child: Text('全部门店', style: TextStyle(fontSize: 13)),
                ),
                ...controller.storeList.map((store) => DropdownMenuItem<int>(
                  value: store.id,
                  child: Text(store.name, style: const TextStyle(fontSize: 13)),
                )),
              ],
              onChanged: (value) {
                controller.selectedStoreId.value = value ?? 0;
                controller.search();
              },
              style: const TextStyle(fontSize: 13, color: Colors.black87),
              icon: const Icon(Icons.arrow_drop_down, size: 20),
              iconSize: 20,
              menuMaxHeight: 300,
              padding: const EdgeInsets.symmetric(horizontal: 8),
            ),
          ),
        );
      }
    });
  }

  /// 构建紧凑型状态选择器（单行布局）
  Widget _buildCompactStatusSelector() {
    return Obx(() => Container(
      height: 32, // 紧凑高度
      decoration: AppBorderStyles.standardBoxDecoration,
      child: DropdownButtonHideUnderline(
        child: DropdownButton<DocumentStatus?>(
          value: controller.selectedStatus.value,
          hint: const Text('选择状态', style: TextStyle(fontSize: 13, color: Colors.grey)),
          isExpanded: true,
          items: [
            const DropdownMenuItem<DocumentStatus?>(
              value: null,
              child: Text('全部状态', style: TextStyle(fontSize: 13)),
            ),
            ...DocumentStatus.values.map((status) => DropdownMenuItem<DocumentStatus?>(
              value: status,
              child: Text(status.label, style: const TextStyle(fontSize: 13)),
            )),
          ],
          onChanged: (value) {
            controller.selectedStatus.value = value;
            controller.search();
          },
          style: const TextStyle(fontSize: 13, color: Colors.black87),
          icon: const Icon(Icons.arrow_drop_down, size: 20),
          iconSize: 20,
          menuMaxHeight: 300,
          padding: const EdgeInsets.symmetric(horizontal: 8),
        ),
      ),
    ));
  }

  /// 构建紧凑型日期范围选择器（单行布局）
  Widget _buildCompactDateRangePicker() {
    final dateFormat = DateFormat('MM-dd');

    return Obx(() {
      final startDateText = controller.startDate.value != null
          ? dateFormat.format(controller.startDate.value!)
          : '开始';

      final endDateText = controller.endDate.value != null
          ? dateFormat.format(controller.endDate.value!)
          : '结束';

      return Container(
        height: 32, // 紧凑高度
        decoration: AppBorderStyles.standardBoxDecoration,
        child: Row(
          children: [
            Expanded(
              child: InkWell(
                onTap: () => _selectStartDate(Get.context!),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(AppBorderStyles.borderRadius),
                  bottomLeft: Radius.circular(AppBorderStyles.borderRadius),
                ),
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 8),
                  child: Row(
                    children: [
                      Icon(Icons.calendar_today, size: 14, color: Colors.grey[600]),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          startDateText,
                          style: TextStyle(
                            fontSize: 13,
                            color: controller.startDate.value != null
                                ? Colors.black87
                                : Colors.grey[600],
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            Container(
              width: 1,
              height: 20,
              color: Colors.grey[300],
            ),
            Expanded(
              child: InkWell(
                onTap: () => _selectEndDate(Get.context!),
                borderRadius: const BorderRadius.only(
                  topRight: Radius.circular(AppBorderStyles.borderRadius),
                  bottomRight: Radius.circular(AppBorderStyles.borderRadius),
                ),
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 8),
                  child: Row(
                    children: [
                      Icon(Icons.calendar_today, size: 14, color: Colors.grey[600]),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          endDateText,
                          style: TextStyle(
                            fontSize: 13,
                            color: controller.endDate.value != null
                                ? Colors.black87
                                : Colors.grey[600],
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      );
    });
  }



  /// 构建重置按钮 - 紧凑型设计
  Widget _buildResetButton() {
    return SizedBox(
      height: 32, // 强制高度为32px，与其他控件保持一致
      child: OutlinedButton.icon(
        icon: const Icon(Icons.refresh, size: 14),
        label: const Text('重置'),
        style: OutlinedButton.styleFrom(
          foregroundColor: Colors.grey[600],
          padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 12), // 移除垂直内边距，由SizedBox控制高度
          minimumSize: const Size(0, 32), // 固定高度32px
          maximumSize: const Size(double.infinity, 32), // 限制最大高度
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
          ),
          side: const BorderSide(color: AppBorderStyles.borderColor),
          textStyle: const TextStyle(fontSize: 13), // 紧凑字体
        ),
        onPressed: controller.resetFilters,
      ),
    );
  }

  /// 构建搜索按钮 - 紧凑型设计
  Widget _buildSearchButton() {
    return SizedBox(
      height: 32, // 强制高度为32px，与其他控件保持一致
      child: ElevatedButton.icon(
        icon: const Icon(Icons.search, size: 14),
        label: const Text('搜索'),
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF1E88E5), // 使用UI规范主色调
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 12), // 移除垂直内边距，由SizedBox控制高度
          minimumSize: const Size(0, 32), // 固定高度32px
          maximumSize: const Size(double.infinity, 32), // 限制最大高度
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
          ),
          textStyle: const TextStyle(fontSize: 13), // 紧凑字体
        ),
        onPressed: controller.search,
      ),
    );
  }

  /// 构建新建入库单按钮 - 紧凑型设计
  Widget _buildNewStockInButton() {
    return SizedBox(
      height: 32, // 强制高度为32px，与其他控件保持一致
      child: ElevatedButton.icon(
        icon: const Icon(Icons.add_box, size: 14),
        label: const Text('新建入库单'),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.green[600], // 绿色背景
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 12), // 移除垂直内边距，由SizedBox控制高度
          minimumSize: const Size(0, 32), // 固定高度32px
          maximumSize: const Size(double.infinity, 32), // 限制最大高度
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
          ),
          textStyle: const TextStyle(fontSize: 13), // 紧凑字体
        ),
        onPressed: controller.createNewStockIn,
      ),
    );
  }

  /// 构建入库单列表
  Widget _buildStockInList() {
    return Column(
      children: [
        Expanded(
          child: ScreenTypeLayout(
            mobile: _buildListView(),
            tablet: _buildDataTable(),
            desktop: _buildDataTable(),
          ),
        ),
        _buildPagination(),
      ],
    );
  }

  /// 构建列表视图 (用于移动设备)
  Widget _buildListView() {
    return ListView.separated(
      padding: const EdgeInsets.all(16),
      itemCount: controller.stockInList.length,
      separatorBuilder: (context, index) => const Divider(height: 16),
      itemBuilder: (context, index) {
        final stockIn = controller.stockInList[index];

        return Card(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppBorderStyles.largeBorderRadius), // 使用统一的大圆角
          ),
          elevation: 2,
          child: InkWell(
            borderRadius: BorderRadius.circular(AppBorderStyles.largeBorderRadius), // 使用统一的大圆角
            onTap: () => controller.viewStockInDetail(stockIn.id),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        stockIn.stockInNo,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      _buildStatusBadge(stockIn.status),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      const Icon(Icons.store, size: 16, color: Colors.grey),
                      const SizedBox(width: 4),
                      Text(
                        stockIn.store?.name ?? '未知门店',
                        style: const TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      const Icon(Icons.access_time, size: 16, color: Colors.grey),
                      const SizedBox(width: 4),
                      Text(
                        _formatDate(stockIn.createTime),
                        style: const TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Text(
                        '件数: ${stockIn.itemCount}件',
                        style: const TextStyle(
                          fontWeight: FontWeight.w500,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Text(
                        '金额: ¥${stockIn.totalAmount.toStringAsFixed(2)}',
                        style: const TextStyle(
                          fontWeight: FontWeight.w500,
                          color: Colors.green,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: _buildActionButtons(stockIn),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// 构建数据表格 (用于平板和桌面设备)
  Widget _buildDataTable() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // 获取可用宽度
        final availableWidth = constraints.maxWidth;

        // 优化列宽分配：增加门店和状态列宽度，确保内容完整显示
        final stockInNoWidth = availableWidth * 0.20;  // 20% - 入库单号 (从22%减少到20%)
        final storeWidth = availableWidth * 0.16;      // 16% - 门店 (从13%增加到16%)
        final timeWidth = availableWidth * 0.13;       // 13% - 入库时间 (从15%减少到13%)
        final countWidth = availableWidth * 0.08;      // 8% - 件数 (保持不变)
        final amountWidth = availableWidth * 0.13;     // 13% - 金额 (保持不变)
        final statusWidth = availableWidth * 0.10;     // 10% - 状态 (从7%增加到10%)
        final actionWidth = availableWidth * 0.20;     // 20% - 操作 (从22%减少到20%)

        // 根据可用宽度调整字体大小
        final fontSize = availableWidth < 800 ? 12.0 : 14.0;
        final headingFontSize = availableWidth < 800 ? 13.0 : 15.0;

        return Container(
          margin: const EdgeInsets.all(4), // 进一步减少边距
          decoration: AppBorderStyles.elevatedBoxDecoration.copyWith(
            color: Colors.white,
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
            child: SingleChildScrollView(
              scrollDirection: Axis.vertical,
              child: SizedBox(
                width: availableWidth,
                child: DataTable(
                columnSpacing: 0, // 移除列间距，让列宽完全由我们控制
                horizontalMargin: 0, // 移除水平边距
                headingRowHeight: 44, // 进一步减少表头高度
                dataRowMinHeight: 48, // 进一步减少数据行高度
                dataRowMaxHeight: 48,
                headingTextStyle: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                  fontSize: headingFontSize,
                ),
                dataTextStyle: TextStyle(
                  fontSize: fontSize,
                  color: Colors.black87,
                ),
                headingRowColor: WidgetStateProperty.all(AppBorderStyles.tableHeaderBackground),
                border: AppBorderStyles.tableStandardBorder,
                columns: [
                  DataColumn(
                    label: Container(
                      width: stockInNoWidth,
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: const Text('入库单号', textAlign: TextAlign.center),
                    ),
                  ),
                  DataColumn(
                    label: Container(
                      width: storeWidth,
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: const Text('门店', textAlign: TextAlign.center),
                    ),
                  ),
                  DataColumn(
                    label: Container(
                      width: timeWidth,
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: const Text('入库时间', textAlign: TextAlign.center),
                    ),
                  ),
                  DataColumn(
                    label: Container(
                      width: countWidth,
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: const Text('件数', textAlign: TextAlign.center),
                    ),
                  ),
                  DataColumn(
                    label: Container(
                      width: amountWidth,
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: const Text('金额', textAlign: TextAlign.center),
                    ),
                  ),
                  DataColumn(
                    label: Container(
                      width: statusWidth,
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: const Text('状态', textAlign: TextAlign.center),
                    ),
                  ),
                  DataColumn(
                    label: Container(
                      width: actionWidth,
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: const Text('操作', textAlign: TextAlign.center),
                    ),
                  ),
                ],
                rows: controller.stockInList.map((stockIn) {
                  return DataRow(
                    cells: [
                      DataCell(
                        Container(
                          width: stockInNoWidth,
                          height: 48, // 进一步减少固定高度
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                          alignment: Alignment.center, // 垂直和水平都居中
                          child: Text(
                            stockIn.stockInNo,
                            style: const TextStyle(
                              fontWeight: FontWeight.w500,
                              color: AppTheme.primaryColor,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                        onTap: () => controller.viewStockInDetail(stockIn.id),
                      ),
                      DataCell(
                        Container(
                          width: storeWidth,
                          height: 48, // 进一步减少高度
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                          alignment: Alignment.center,
                          child: Text(
                            stockIn.store?.name ?? '未知门店',
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                      ),
                      DataCell(
                        Container(
                          width: timeWidth,
                          height: 48, // 进一步减少高度
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                          alignment: Alignment.center,
                          child: Text(
                            _formatDate(stockIn.createTime),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                      ),
                      DataCell(
                        Container(
                          width: countWidth,
                          height: 48, // 进一步减少高度
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                          alignment: Alignment.center,
                          child: Text(
                            '${stockIn.itemCount}件',
                            style: const TextStyle(
                              fontWeight: FontWeight.w500,
                              color: AppTheme.primaryColor,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                      ),
                      DataCell(
                        Container(
                          width: amountWidth,
                          height: 48, // 进一步减少高度
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                          alignment: Alignment.center, // 垂直和水平都居中
                          child: Text(
                            '¥${stockIn.totalAmount.toStringAsFixed(2)}',
                            style: const TextStyle(
                              fontWeight: FontWeight.w500,
                              color: Colors.green,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                      ),
                      DataCell(
                        Container(
                          width: statusWidth,
                          height: 48, // 进一步减少高度
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                          alignment: Alignment.center, // 垂直和水平都居中
                          child: _buildStatusBadge(stockIn.status),
                        ),
                      ),
                      DataCell(
                        Container(
                          width: actionWidth,
                          height: 48, // 进一步减少高度
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                          alignment: Alignment.center,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            mainAxisSize: MainAxisSize.min,
                            children: _buildActionButtons(stockIn, availableWidth < 800),
                          ),
                        ),
                      ),
                    ],
                  );
                }).toList(),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// 构建分页控件
  Widget _buildPagination() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 16), // 进一步减少垂直内边距
      color: Colors.white,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          IconButton(
            icon: const Icon(Icons.first_page),
            tooltip: '第一页',
            onPressed: controller.currentPage.value > 1
                ? () => controller.goToPage(1)
                : null,
          ),
          IconButton(
            icon: const Icon(Icons.chevron_left),
            tooltip: '上一页',
            onPressed: controller.currentPage.value > 1
                ? () => controller.goToPage(controller.currentPage.value - 1)
                : null,
          ),
          Obx(() => Text(
            '${controller.currentPage.value} / ${controller.totalPages.value}',
            style: const TextStyle(fontWeight: FontWeight.bold),
          )),
          IconButton(
            icon: const Icon(Icons.chevron_right),
            tooltip: '下一页',
            onPressed: controller.currentPage.value < controller.totalPages.value
                ? () => controller.goToPage(controller.currentPage.value + 1)
                : null,
          ),
          IconButton(
            icon: const Icon(Icons.last_page),
            tooltip: '最后一页',
            onPressed: controller.currentPage.value < controller.totalPages.value
                ? () => controller.goToPage(controller.totalPages.value)
                : null,
          ),
        ],
      ),
    );
  }

  /// 构建状态徽章
  Widget _buildStatusBadge(DocumentStatus status) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 2), // 进一步减少内边距
      decoration: BoxDecoration(
        color: controller.getStatusColor(status).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius), // 使用统一圆角
        border: Border.all(
          color: controller.getStatusColor(status),
          width: 1,
        ),
      ),
      child: Text(
        controller.getStatusText(status),
        style: TextStyle(
          color: controller.getStatusColor(status),
          fontSize: 11, // 减少字体大小确保单行显示
          fontWeight: FontWeight.w500,
        ),
        overflow: TextOverflow.ellipsis, // 防止文字溢出
        maxLines: 1, // 确保单行显示
      ),
    );
  }

  /// 构建操作按钮
  List<Widget> _buildActionButtons(dynamic stockIn, [bool isSmallScreen = false]) {
    final List<Widget> buttons = [];
    final double iconSize = isSmallScreen ? 18 : 20;
    final double buttonSize = isSmallScreen ? 32 : 40;

    // 查看按钮
    buttons.add(
      SizedBox(
        width: buttonSize,
        height: buttonSize,
        child: IconButton(
          icon: Icon(Icons.visibility, color: Colors.blue, size: iconSize),
          tooltip: '查看',
          padding: EdgeInsets.zero,
          onPressed: () => controller.viewStockInDetail(stockIn.id),
        ),
      ),
    );

    // 编辑按钮 (仅草稿和待审核可编辑)
    if (stockIn.canEdit) {
      buttons.add(
        SizedBox(
          width: buttonSize,
          height: buttonSize,
          child: IconButton(
            icon: Icon(Icons.edit, color: Colors.orange, size: iconSize),
            tooltip: '编辑',
            padding: EdgeInsets.zero,
            onPressed: () => controller.editStockIn(stockIn.id),
          ),
        ),
      );
    }

    // 审核按钮 (仅待审核可审核且需要管理员权限)
    if (stockIn.canApprove) {
      buttons.add(
        SizedBox(
          width: buttonSize,
          height: buttonSize,
          child: IconButton(
            icon: Icon(Icons.check_circle, color: Colors.green, size: iconSize),
            tooltip: '审核',
            padding: EdgeInsets.zero,
            onPressed: () => _showApproveDialog(stockIn.id),
          ),
        ),
      );
    }

    // 取消审核按钮 (仅已审核通过的可取消审核，且需要管理员权限)
    if (stockIn.canCancelApprove && controller.canCancelApprove) {
      buttons.add(
        SizedBox(
          width: buttonSize,
          height: buttonSize,
          child: IconButton(
            icon: Icon(Icons.cancel, color: Colors.orange, size: iconSize),
            tooltip: '取消审核',
            padding: EdgeInsets.zero,
            onPressed: () => _showCancelApproveDialog(stockIn.id),
          ),
        ),
      );
    }

    // 删除按钮 (仅草稿和待审核可删除)
    if (stockIn.canDelete) {
      buttons.add(
        SizedBox(
          width: buttonSize,
          height: buttonSize,
          child: IconButton(
            icon: Icon(Icons.delete, color: Colors.red, size: iconSize),
            tooltip: '删除',
            padding: EdgeInsets.zero,
            onPressed: () => controller.deleteStockIn(stockIn.id),
          ),
        ),
      );
    }

    return buttons;
  }

  /// 显示审核对话框
  void _showApproveDialog(int id) {
    final TextEditingController reasonController = TextEditingController();
    final RxBool isLoading = false.obs;

    Get.dialog(
      AlertDialog(
        title: Row(
          children: [
            Icon(Icons.check_circle_outline, color: Colors.blue[600]),
            const SizedBox(width: 8),
            const Text('审核入库单'),
          ],
        ),
        content: SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(AppBorderStyles.mediumBorderRadius), // 使用统一的中等圆角
                  border: Border.all(color: Colors.blue[200]!, width: AppBorderStyles.borderWidth), // 使用统一边框宽度
                ),
                child: Row(
                  children: [
                    Icon(Icons.info_outline, color: Colors.blue[600], size: 20),
                    const SizedBox(width: 8),
                    const Expanded(
                      child: Text(
                        '请仔细审核入库单信息，确认无误后选择审核结果',
                        style: TextStyle(fontSize: 13),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                '审核备注:',
                style: TextStyle(fontWeight: FontWeight.w500),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: reasonController,
                decoration: AppBorderStyles.standardInputDecoration.copyWith(
                  hintText: '请输入审核意见（拒绝时必填）',
                  contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                maxLines: 3,
                maxLength: 255,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          Obx(() => ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red[600],
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
            ),
            onPressed: isLoading.value ? null : () async {
              if (reasonController.text.trim().isEmpty) {
                Get.snackbar(
                  '提示',
                  '拒绝审核时必须填写审核意见',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.orange[600],
                  colorText: Colors.white,
                  duration: const Duration(seconds: 2),
                );
                return;
              }

              isLoading.value = true;
              Get.back();
              await controller.approveStockIn(id, false, reasonController.text.trim());
            },
            child: isLoading.value
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white)
                )
              : const Text('拒绝'),
          )),
          Obx(() => ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green[600],
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
            ),
            onPressed: isLoading.value ? null : () async {
              isLoading.value = true;
              Get.back();
              await controller.approveStockIn(
                id,
                true,
                reasonController.text.trim().isEmpty ? '审核通过' : reasonController.text.trim()
              );
            },
            child: isLoading.value
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white)
                )
              : const Text('通过'),
          )),
        ],
      ),
    );
  }

  /// 选择开始日期
  Future<void> _selectStartDate(BuildContext context) async {
    final DateTime now = DateTime.now();
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: controller.startDate.value ?? now,
      firstDate: DateTime(now.year - 5),
      lastDate: now,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppTheme.primaryColor,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      controller.startDate.value = picked;

      // 如果结束日期小于开始日期，自动更新结束日期
      if (controller.endDate.value != null && controller.endDate.value!.isBefore(picked)) {
        controller.endDate.value = picked;
      }

      controller.search();
    }
  }

  /// 选择结束日期
  Future<void> _selectEndDate(BuildContext context) async {
    final DateTime now = DateTime.now();
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: controller.endDate.value ?? now,
      firstDate: controller.startDate.value ?? DateTime(now.year - 5),
      lastDate: now,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppTheme.primaryColor,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      controller.endDate.value = picked;
      controller.search();
    }
  }

  /// 格式化日期
  String _formatDate(DateTime? date) {
    if (date == null) return '-';
    return DateFormat('yyyy-MM-dd HH:mm').format(date);
  }

  /// 显示取消审核对话框
  void _showCancelApproveDialog(int id) {
    final TextEditingController reasonController = TextEditingController();
    final RxBool isLoading = false.obs;

    Get.dialog(
      AlertDialog(
        title: Row(
          children: [
            Icon(Icons.cancel_outlined, color: Colors.orange[600]),
            const SizedBox(width: 8),
            const Text('取消审核'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '确定要取消此入库单的审核状态吗？',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            const Text(
              '取消原因：',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: reasonController,
              maxLines: 3,
              decoration: AppBorderStyles.standardInputDecoration.copyWith(
                hintText: '请输入取消审核的原因...',
                contentPadding: const EdgeInsets.all(12),
              ),
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange[50],
                borderRadius: BorderRadius.circular(AppBorderStyles.mediumBorderRadius), // 使用统一的中等圆角
                border: Border.all(color: Colors.orange[200]!, width: AppBorderStyles.borderWidth), // 使用统一边框宽度
              ),
              child: Row(
                children: [
                  Icon(Icons.warning_amber, color: Colors.orange[600], size: 20),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      '取消审核后，入库单将回到待审核状态',
                      style: TextStyle(fontSize: 12),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          Obx(() => ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange[600],
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
            ),
            onPressed: isLoading.value ? null : () async {
              if (reasonController.text.trim().isEmpty) {
                Get.snackbar(
                  '提示',
                  '取消审核时必须填写原因',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.orange[600],
                  colorText: Colors.white,
                  duration: const Duration(seconds: 2),
                );
                return;
              }

              isLoading.value = true;
              Get.back();
              await controller.cancelApproveStockIn(id, reasonController.text.trim());
            },
            child: isLoading.value
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white)
                )
              : const Text('确认取消'),
          )),
        ],
      ),
    );
  }
}