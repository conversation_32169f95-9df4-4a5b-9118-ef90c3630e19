
/// 首饰分类模型
class JewelryCategory {
  /// 分类ID
  final int id;

  /// 分类名称
  final String name;

  /// 父分类ID (0表示顶级分类)
  final int parentId;

  /// 排序值
  final int sortOrder;

  /// 是否启用
  final bool isActive;

  /// 构造函数
  const JewelryCategory({
    required this.id,
    required this.name,
    required this.parentId,
    this.sortOrder = 0,
    this.isActive = true,
  });

  /// 从JSON构造
  factory JewelryCategory.fromJson(Map<String, dynamic> json) {
    return JewelryCategory(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      parentId: json['pid'] ?? json['parent_id'] ?? 0,
      sortOrder: json['weigh'] ?? json['sort_order'] ?? 0,
      isActive: (json['status'] ?? 1) == 1,
    );
  }

  /// 转为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'parent_id': parentId,
      'sort_order': sortOrder,
      'is_active': isActive,
    };
  }

  /// 是否为顶级分类
  bool get isTopLevel => parentId == 0;

  /// 复制并修改属性
  JewelryCategory copyWith({
    int? id,
    String? name,
    int? parentId,
    int? sortOrder,
    bool? isActive,
  }) {
    return JewelryCategory(
      id: id ?? this.id,
      name: name ?? this.name,
      parentId: parentId ?? this.parentId,
      sortOrder: sortOrder ?? this.sortOrder,
      isActive: isActive ?? this.isActive,
    );
  }
}