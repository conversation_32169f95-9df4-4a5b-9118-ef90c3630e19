"""
门店管理API端点
提供门店的CRUD操作和统计功能
"""

from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from ....core.database import get_db
from ....core.dependencies import get_current_active_user, require_permission, require_permissions
from ....schemas.store import (
    StoreCreate,
    StoreUpdate,
    StoreResponse,
    StoreListResponse,
    StoreStatistics
)
from ....schemas.auth import CurrentUserResponse
from ....services.store_service import StoreService

# 创建路由器
router = APIRouter()


@router.get("", response_model=StoreListResponse)
async def get_stores(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=500, description="每页数量"),
    status: Optional[int] = Query(None, ge=0, le=1, description="状态筛选:0=关闭,1=正常"),
    keyword: Optional[str] = Query(None, description="搜索关键词(门店名称、地址、电话)"),
    current_user: CurrentUserResponse = Depends(require_permission("store.view")),
    db: Session = Depends(get_db)
):
    """
    获取门店列表

    支持以下筛选条件:
    - status: 按状态筛选(0=关闭,1=正常)
    - keyword: 按门店名称、地址或电话搜索

    权限逻辑:
    - 管理员可以看到所有门店
    - 非管理员只能看到自己所属的门店
    """
    service = StoreService(db)

    # 判断是否为管理员（超级管理员或没有门店限制）
    is_admin = "super.admin" in current_user.permissions or current_user.store_id is None



    return await service.get_stores(
        page=page,
        page_size=page_size,
        status=status,
        keyword=keyword,
        current_user_store_id=current_user.store_id,
        is_admin=is_admin
    )


@router.get("/{store_id}", response_model=StoreResponse)
async def get_store(
    store_id: int,
    current_user: CurrentUserResponse = Depends(require_permission("store.view")),
    db: Session = Depends(get_db)
):
    """获取单个门店详情"""
    service = StoreService(db)
    store = await service.get_store_by_id(store_id)
    if not store:
        raise HTTPException(status_code=404, detail="门店不存在")
    return store


@router.post("", response_model=StoreResponse)
async def create_store(
    store_data: StoreCreate,
    current_user: CurrentUserResponse = Depends(require_permission("store.create")),
    db: Session = Depends(get_db)
):
    """创建新门店"""
    service = StoreService(db)

    try:
        return await service.create_store(store_data)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="创建门店失败")


@router.put("/{store_id}", response_model=StoreResponse)
async def update_store(
    store_id: int,
    store_data: StoreUpdate,
    current_user: CurrentUserResponse = Depends(require_permission("store.update")),
    db: Session = Depends(get_db)
):
    """更新门店信息"""
    service = StoreService(db)

    try:
        store = await service.update_store(store_id, store_data)
        if not store:
            raise HTTPException(status_code=404, detail="门店不存在")
        return store
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="更新门店失败")


@router.delete("/{store_id}")
async def delete_store(
    store_id: int,
    current_user: CurrentUserResponse = Depends(require_permission("store.delete")),
    db: Session = Depends(get_db)
):
    """删除门店"""
    service = StoreService(db)

    try:
        success = await service.delete_store(store_id)
        if not success:
            raise HTTPException(status_code=404, detail="门店不存在")
        return {"message": "门店删除成功"}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="删除门店失败")


@router.patch("/{store_id}/status", response_model=StoreResponse)
async def update_store_status(
    store_id: int,
    status: int = Query(..., ge=0, le=1, description="状态:0=关闭,1=正常"),
    current_user: CurrentUserResponse = Depends(require_permission("store.update")),
    db: Session = Depends(get_db)
):
    """更新门店状态"""
    service = StoreService(db)

    try:
        store = await service.update_store_status(store_id, status)
        if not store:
            raise HTTPException(status_code=404, detail="门店不存在")
        return store
    except Exception as e:
        raise HTTPException(status_code=500, detail="更新门店状态失败")


@router.get("/{store_id}/statistics", response_model=StoreStatistics)
async def get_store_statistics(
    store_id: int,
    db: Session = Depends(get_db)
):
    """获取门店统计信息"""
    service = StoreService(db)

    try:
        statistics = await service.get_store_statistics(store_id)
        if not statistics:
            raise HTTPException(status_code=404, detail="门店不存在")
        return statistics
    except Exception as e:
        raise HTTPException(status_code=500, detail="获取门店统计失败")


@router.get("/{store_id}/jewelry")
async def get_store_jewelry(
    store_id: int,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=500, description="每页数量"),
    status: Optional[int] = Query(None, description="商品状态筛选"),
    db: Session = Depends(get_db)
):
    """获取门店下的商品列表"""
    service = StoreService(db)

    # 先检查门店是否存在
    store = await service.get_store_by_id(store_id)
    if not store:
        raise HTTPException(status_code=404, detail="门店不存在")

    # 这里可以调用JewelryService来获取该门店的商品
    # 为了保持简单，先返回基本信息
    return {
        "message": f"门店 {store.name} 的商品列表",
        "store_id": store_id,
        "page": page,
        "page_size": page_size,
        "note": "完整的商品列表功能将与商品管理API集成"
    }


@router.get("/{store_id}/admins")
async def get_store_admins(
    store_id: int,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=500, description="每页数量"),
    status: Optional[int] = Query(None, description="管理员状态筛选"),
    db: Session = Depends(get_db)
):
    """获取门店下的管理员列表"""
    service = StoreService(db)

    # 先检查门店是否存在
    store = await service.get_store_by_id(store_id)
    if not store:
        raise HTTPException(status_code=404, detail="门店不存在")

    # 这里可以调用AdminService来获取该门店的管理员
    # 为了保持简单，先返回基本信息
    return {
        "message": f"门店 {store.name} 的管理员列表",
        "store_id": store_id,
        "page": page,
        "page_size": page_size,
        "note": "完整的管理员列表功能将与管理员管理API集成"
    }