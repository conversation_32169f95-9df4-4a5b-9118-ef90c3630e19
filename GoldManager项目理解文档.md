# GoldManager 项目理解文档

## 项目概述

GoldManager 是一个专为黄金珠宝店设计的综合管理系统，采用前后端分离架构，提供完整的库存管理、销售管理、回收管理等核心业务功能。

### 技术架构概览

```
GoldManager 系统架构
├── 后端 (GoldManager_FastAdmin_API)
│   ├── FastAPI + SQLAlchemy + MySQL
│   ├── JWT 认证 + RESTful API
│   └── 基于 FastAdmin 数据库结构
├── 前端 (gold_manager_flutter)
│   ├── Flutter + GetX 状态管理
│   ├── 跨平台支持 (Windows/macOS/iOS/Android)
│   └── Material Design UI
└── 数据库
    ├── MySQL 主数据库
    └── 55+ 业务表结构
```

## 后端架构分析

### 技术栈
- **框架**: FastAPI (现代化 Python Web 框架)
- **ORM**: SQLAlchemy (数据库操作)
- **数据库**: MySQL
- **认证**: JWT Token
- **数据验证**: Pydantic
- **API 文档**: Swagger/OpenAPI

### 项目结构
```
GoldManager_FastAdmin_API/
├── app/
│   ├── api/v1/endpoints/     # API 路由端点
│   ├── core/                 # 核心配置 (数据库、安全、设置)
│   ├── models/               # SQLAlchemy 数据模型
│   ├── schemas/              # Pydantic 验证模型
│   ├── services/             # 业务逻辑层
│   └── utils/                # 工具函数
├── docs/                     # API 文档
├── sql/                      # 数据库脚本
└── main.py                   # 应用入口
```

### 核心模块
1. **认证模块** (`auth.py`): JWT 认证、权限控制
2. **商品管理** (`jewelry.py`): 首饰 CRUD、分类管理
3. **库存管理**: 入库、出库、退货、盘点、调拨
4. **回收管理** (`recycling.py`): 旧料回收、处理流程
5. **门店管理** (`store.py`): 多门店支持
6. **会员管理** (`member.py`): 会员信息、积分系统
7. **销售管理** (`sales.py`): 销售订单、退货处理
8. **数据导出** (`data_export.py`): 报表生成

### API 设计特点
- RESTful 风格，统一的响应格式
- 完整的 CRUD 操作支持
- 分页查询和多条件筛选
- 权限控制和操作日志
- 自动数据验证和错误处理

## 前端架构分析

### 技术栈
- **框架**: Flutter 3.32.0
- **状态管理**: GetX
- **网络请求**: Dio + HTTP
- **本地存储**: SharedPreferences + Hive + SQLite
- **UI 设计**: Material Design

### 项目结构
```
gold_manager_flutter/lib/
├── app/                      # 应用核心
├── core/                     # 核心功能
│   ├── config/               # 配置管理
│   ├── routes/               # 路由定义
│   ├── services/             # 核心服务
│   ├── theme/                # 主题系统
│   ├── translations/         # 国际化
│   └── utils/                # 工具类
├── features/                 # 功能模块
│   ├── auth/                 # 认证模块
│   ├── dashboard/            # 仪表盘
│   ├── jewelry/              # 首饰管理
│   ├── stock/                # 库存管理
│   ├── sales/                # 销售管理
│   ├── recycling/            # 旧料回收
│   └── statistics/           # 统计分析
├── models/                   # 数据模型
├── widgets/                  # 公共组件
└── main.dart                 # 入口文件
```

### 架构模式
采用 **GetX MVC 模式**:
- **Controller**: 业务逻辑和状态管理 (继承 GetxController)
- **View**: UI 组件 (通常以 View 或 Page 结尾)
- **Binding**: 依赖注入
- **Service**: API 调用和数据持久化
- **Model**: 数据结构

### 核心功能模块

#### 1. 认证模块 (`features/auth/`)
- 用户登录/登出
- JWT Token 管理
- 权限控制
- 用户状态管理

#### 2. 首饰管理 (`features/jewelry/`)
- 首饰列表展示和搜索
- 首饰详情查看和编辑
- 分类管理
- 图片管理

#### 3. 库存管理 (`features/stock/`)
- **入库管理**: 入库单创建、审核、查询
- **出库管理**: 出库单创建、审核、查询
- **库存查询**: 实时库存查询、筛选
- **库存盘点**: 盘点单管理、差异处理
- **门店调拨**: 跨门店库存调拨、收款结算

#### 4. 销售管理 (`features/sales/`)
- 销售订单管理
- 退货处理
- 收款结算
- 销售统计

#### 5. 回收管理 (`features/recycling/`)
- 回收单管理
- 回收物品分类
- 金属分离处理
- 回收流程跟踪

## 数据库结构分析

### 核心业务表

#### 商品相关
- `fa_jewelry`: 商品主表 (条码、名称、重量、价格等)
- `fa_jewelry_category`: 商品分类
- `fa_jewelry_group`: 商品分组
- `fa_jewelry_group_item`: 分组明细

#### 库存管理
- `fa_stock_in` / `fa_stock_in_item`: 入库主表/明细
- `fa_stock_out` / `fa_stock_out_item`: 出库主表/明细
- `fa_stock_return` / `fa_stock_return_item`: 退货主表/明细
- `fa_inventory_check` / `fa_inventory_check_item`: 盘点主表/明细
- `fa_store_transfer` / `fa_store_transfer_item`: 调拨主表/明细

#### 回收管理
- `fa_recycling` / `fa_recycling_item`: 回收主表/明细
- `fa_recycling_process`: 回收处理工单
- `fa_recycling_process_result`: 处理结果
- `fa_recycling_to_stock`: 回收转库存记录
- `fa_material`: 原料管理

#### 系统管理
- `fa_admin`: 管理员表
- `fa_admin_log`: 操作日志
- `fa_store`: 门店信息
- `fa_member`: 会员信息

### 数据模型特点

#### 商品模型 (fa_jewelry)
```sql
-- 核心字段
id, barcode, name, category_id, ring_size
-- 重量相关
gold_weight, silver_weight, total_weight
-- 价格成本
gold_price, silver_price, gold_cost, silver_cost
-- 工费相关
silver_work_type, silver_work_price, plating_cost
wholesale_work_price, retail_work_price, piece_work_price
-- 状态管理
store_id, status, createtime, updatetime
```

#### 库存单据模型
- **主从表结构**: 主表记录单据信息，明细表记录具体商品
- **审核流程**: 支持草稿、待审核、已通过、已拒绝状态
- **权限控制**: 基于门店和角色的权限管理
- **操作日志**: 完整的操作记录和审计跟踪

#### 回收处理模型
- **处理类型**: 金银分离、翻新加工、直接熔炼
- **成本核算**: 详细的成本计算和分摊
- **状态流转**: 完整的处理流程状态管理
- **转库存**: 支持转为原料库存或商品库存

## 核心业务流程

### 1. 库存管理流程
```
入库流程: 创建入库单 → 添加商品明细 → 提交审核 → 审核通过 → 更新库存
出库流程: 创建出库单 → 选择商品 → 提交审核 → 审核通过 → 减少库存
调拨流程: 创建调拨单 → 选择商品 → 收款结算 → 审核通过 → 跨店转移
```

### 2. 回收管理流程
```
回收流程: 创建回收单 → 录入回收物品 → 评估定价 → 确认回收 → 生成处理工单
处理流程: 选择处理方式 → 执行处理 → 记录结果 → 转入库存/原料
```

### 3. 销售管理流程
```
销售流程: 选择商品 → 创建销售单 → 收款结算 → 确认销售 → 更新库存
退货流程: 选择退货商品 → 创建退货单 → 退款处理 → 恢复库存
```

## UI 设计规范

### 设计系统
- **主题**: Material Design 3.0
- **色彩**: 蓝色主色调 + 渐变背景
- **组件**: 统一的 AppBorderStyles 边框规范
- **布局**: 响应式设计，支持移动端和桌面端

### 组件规范
- **按钮**: PrimaryButton、SecondaryButton、TextButton
- **表单**: 统一的输入框样式和验证
- **卡片**: 渐变背景 + 阴影效果
- **表格**: DataTable 统一样式
- **弹窗**: 响应式弹窗设计

### 交互规范
- **导航**: 侧边栏 + 标签页导航
- **操作**: 弹窗式编辑，避免页面跳转
- **反馈**: 统一的加载状态和错误提示
- **权限**: 基于角色的功能显示/隐藏

## 与现有文档的对比

### 文档准确性验证
✅ **技术栈描述准确**: 文档中的 FastAPI + Flutter + GetX 技术栈与实际代码一致
✅ **功能模块完整**: 文档中描述的核心功能模块在代码中都有对应实现
✅ **数据库结构匹配**: 文档中的数据库表结构与 SQL 文件一致

### 发现的差异
⚠️ **API 端点更新**: 实际代码中的 API 端点比文档描述更丰富
⚠️ **功能完成度**: 部分功能模块已完成但文档未及时更新
⚠️ **新增功能**: 回收处理、打印模板等新功能文档描述不够详细

## 代码质量评估

### 优点
✅ **架构清晰**: 前后端分离，模块化设计
✅ **代码规范**: 统一的命名规范和项目结构
✅ **功能完整**: 核心业务功能实现完整
✅ **UI 一致性**: 严格的 UI 设计规范
✅ **权限控制**: 完善的权限管理机制

### 改进建议
🔧 **文档同步**: 及时更新文档与代码的一致性
🔧 **测试覆盖**: 增加单元测试和集成测试
🔧 **性能优化**: 大数据量场景下的性能优化
🔧 **错误处理**: 完善异常处理和用户提示
🔧 **国际化**: 完善多语言支持

## 总结

GoldManager 是一个架构合理、功能完整的珠宝店管理系统。代码质量较高，遵循现代化开发规范，具有良好的可维护性和扩展性。系统已实现核心业务功能，可以满足珠宝店的日常经营管理需求。
