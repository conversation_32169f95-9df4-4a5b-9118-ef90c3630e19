import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../models/jewelry/jewelry.dart';
import '../../../models/jewelry/jewelry_category.dart';
import '../../../models/store/store.dart';
import '../../../models/common/enums.dart';
import '../../../services/jewelry_service.dart';
import '../../../services/category_service.dart';
import '../../../services/store_service.dart';
import '../../../core/utils/logger_service.dart';

/// 商品编辑控制器
class JewelryEditController extends GetxController {
  final JewelryService _jewelryService = Get.find<JewelryService>();
  final CategoryService _categoryService = Get.find<CategoryService>();
  final StoreService _storeService = Get.find<StoreService>();

  // 表单相关
  final formKey = GlobalKey<FormState>();

  // 基本信息控制器
  final nameController = TextEditingController();
  final barcodeController = TextEditingController();
  final ringSizeController = TextEditingController();
  final totalWeightController = TextEditingController();

  // 重量信息控制器
  final goldWeightController = TextEditingController();
  final silverWeightController = TextEditingController();

  // 价格信息控制器
  final goldPriceController = TextEditingController();
  final silverPriceController = TextEditingController();
  final workPriceController = TextEditingController();

  // 选择状态
  final selectedCategoryId = 0.obs;
  final selectedStoreId = 0.obs;
  final selectedStatus = JewelryStatus.onShelf.obs;

  // 数据列表
  final categoryList = <JewelryCategory>[].obs;
  final storeList = <Store>[].obs;

  // 状态管理
  final isLoading = false.obs;
  final isInitialized = false.obs;

  // 当前编辑的商品
  Jewelry? _currentJewelry;

  /// 构造函数，接收要编辑的商品
  JewelryEditController({required Jewelry jewelry}) {
    _currentJewelry = jewelry;
    _initializeFormData(jewelry);
  }

  @override
  void onInit() {
    super.onInit();
    _loadInitialData();
  }

  @override
  void onClose() {
    // 清理控制器
    nameController.dispose();
    barcodeController.dispose();
    ringSizeController.dispose();
    totalWeightController.dispose();
    goldWeightController.dispose();
    silverWeightController.dispose();
    goldPriceController.dispose();
    silverPriceController.dispose();
    workPriceController.dispose();
    super.onClose();
  }

  /// 初始化表单数据
  void _initializeFormData(Jewelry jewelry) {
    nameController.text = jewelry.name;
    barcodeController.text = jewelry.barcode;
    ringSizeController.text = jewelry.ringSize ?? '';
    goldWeightController.text = jewelry.goldWeight.toString();
    silverWeightController.text = jewelry.silverWeight.toString();
    goldPriceController.text = jewelry.goldPrice.toString();
    silverPriceController.text = jewelry.silverPrice.toString();
    workPriceController.text = jewelry.workPrice.toString();
    totalWeightController.text = jewelry.totalWeight.toString();

    selectedCategoryId.value = jewelry.category?.id ?? 0;
    selectedStoreId.value = jewelry.store?.id ?? 0;
    selectedStatus.value = jewelry.status;
  }

  /// 加载初始数据
  Future<void> _loadInitialData() async {
    try {
      isLoading.value = true;
      LoggerService.d('🔄 开始加载编辑页面初始数据');

      // 并行加载分类列表和门店列表
      await Future.wait([
        _loadCategories(),
        _loadStores(),
      ]);

      isInitialized.value = true;
      LoggerService.d('✅ 编辑页面初始数据加载完成');
    } catch (e) {
      LoggerService.e('❌ 加载编辑页面初始数据失败', e);
      Get.snackbar(
        '错误',
        '加载数据失败：${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// 加载分类列表
  Future<void> _loadCategories() async {
    try {
      LoggerService.d('🔄 开始获取分类列表');
      final categories = await _categoryService.getCategories();
      // 转换为JewelryCategory类型
      categoryList.value = categories.map((cat) => JewelryCategory(
        id: cat.id,
        name: cat.name,
        parentId: cat.parentId ?? 0,
      )).toList();
      LoggerService.d('✅ 获取分类列表成功，共${categories.length}个分类');
    } catch (e) {
      LoggerService.e('❌ 获取分类列表失败', e);
      throw Exception('获取分类列表失败：${e.toString()}');
    }
  }

  /// 加载门店列表
  Future<void> _loadStores() async {
    try {
      LoggerService.d('🔄 开始获取门店列表');
      final stores = await _storeService.getStores();

      // 对门店列表进行去重处理，避免下拉框重复ID错误
      final uniqueStores = <int, Store>{};
      for (final store in stores) {
        uniqueStores[store.id] = store;
      }

      storeList.value = uniqueStores.values.toList();

      // 确保当前商品的门店ID在列表中存在
      if (_currentJewelry?.store?.id != null) {
        final currentStoreExists = storeList.any((store) => store.id == _currentJewelry!.store!.id);
        if (!currentStoreExists && _currentJewelry!.store != null) {
          // 如果当前商品的门店不在列表中，添加它
          storeList.add(_currentJewelry!.store!);
          LoggerService.d('📝 添加当前商品所属门店到列表: ${_currentJewelry!.store!.name}');
        }
      }

      LoggerService.d('✅ 获取门店列表成功，原始${stores.length}个，去重后${storeList.length}个门店');
    } catch (e) {
      LoggerService.e('❌ 获取门店列表失败', e);
      throw Exception('获取门店列表失败：${e.toString()}');
    }
  }

  /// 保存商品
  Future<void> saveJewelry() async {
    // 安全检查表单状态
    if (formKey.currentState == null || !formKey.currentState!.validate()) {
      LoggerService.w('⚠️ 表单验证失败或表单未初始化');
      Get.snackbar(
        '提示',
        '请检查输入信息',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      return;
    }

    try {
      isLoading.value = true;
      LoggerService.d('🔄 开始保存商品信息');
      LoggerService.d('📝 当前商品ID: ${_currentJewelry?.id}');

      // 构建更新数据
      final updateData = {
        'name': nameController.text.trim(),
        'barcode': barcodeController.text.trim(),
        'category_id': selectedCategoryId.value,
        'ring_size': ringSizeController.text.trim().isEmpty ? null : ringSizeController.text.trim(),
        'gold_weight': double.parse(goldWeightController.text),
        'silver_weight': double.parse(silverWeightController.text),
        'total_weight': double.parse(totalWeightController.text),
        'gold_price': double.parse(goldPriceController.text),
        'silver_price': double.parse(silverPriceController.text),
        'work_price': double.parse(workPriceController.text),
        'store_id': selectedStoreId.value,
        'status': selectedStatus.value.value,
      };

      LoggerService.d('📦 更新数据: $updateData');

      // 调用更新API
      if (_currentJewelry != null) {
        LoggerService.d('🌐 调用API更新商品，ID: ${_currentJewelry!.id}');
        final updatedJewelry = await _jewelryService.updateJewelry(_currentJewelry!.id, updateData);
        LoggerService.d('📋 API返回数据: ${updatedJewelry.toString()}');

        LoggerService.d('✅ 控制器：商品信息保存成功');

        // 显示成功消息
        LoggerService.d('📢 控制器：显示成功提示');
        Get.snackbar(
          '成功',
          '商品信息已保存',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
          duration: const Duration(seconds: 2),
        );

        // 保存成功，不在这里关闭弹窗，让弹窗自己处理
        LoggerService.d('🎯 控制器：保存成功，等待弹窗处理关闭逻辑');
        return; // 确保成功时直接返回
      } else {
        LoggerService.e('❌ 当前商品为空，无法保存');
        throw Exception('当前商品信息为空');
      }
    } catch (e) {
      LoggerService.e('❌ 控制器：保存商品信息失败', e);

      // 显示错误消息
      LoggerService.d('📢 控制器：显示错误提示');
      Get.snackbar(
        '错误',
        '保存失败：${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );
      rethrow; // 重新抛出异常，让弹窗知道保存失败
    } finally {
      LoggerService.d('⏳ 控制器：设置加载状态为false');
      isLoading.value = false;
    }
  }

  /// 更新总重量（自动计算）
  void updateTotalWeight() {
    final goldWeight = double.tryParse(goldWeightController.text) ?? 0.0;
    final silverWeight = double.tryParse(silverWeightController.text) ?? 0.0;
    final total = goldWeight + silverWeight;
    totalWeightController.text = total.toStringAsFixed(2);
  }

  /// 计算总重量
  double get totalWeight {
    final goldWeight = double.tryParse(goldWeightController.text) ?? 0.0;
    final silverWeight = double.tryParse(silverWeightController.text) ?? 0.0;
    return goldWeight + silverWeight;
  }

  /// 计算总成本
  double get totalCost {
    final goldWeight = double.tryParse(goldWeightController.text) ?? 0.0;
    final silverWeight = double.tryParse(silverWeightController.text) ?? 0.0;
    final goldPrice = double.tryParse(goldPriceController.text) ?? 0.0;
    final silverPrice = double.tryParse(silverPriceController.text) ?? 0.0;
    final workPrice = double.tryParse(workPriceController.text) ?? 0.0;
    
    return (goldWeight * goldPrice) + (silverWeight * silverPrice) + workPrice;
  }

  /// 重置表单
  void resetForm() {
    if (_currentJewelry != null) {
      _initializeFormData(_currentJewelry!);
    } else {
      nameController.clear();
      barcodeController.clear();
      ringSizeController.clear();
      totalWeightController.clear();
      goldWeightController.clear();
      silverWeightController.clear();
      goldPriceController.clear();
      silverPriceController.clear();
      workPriceController.clear();
      selectedCategoryId.value = 0;
      selectedStoreId.value = 0;
      selectedStatus.value = JewelryStatus.onShelf;
    }
  }

  // 验证方法
  String? validateName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return '请输入商品名称';
    }
    return null;
  }

  String? validateBarcode(String? value) {
    if (value == null || value.trim().isEmpty) {
      return '请输入商品条码';
    }
    return null;
  }

  String? validateCategory(int? value) {
    if (value == null || value == 0) {
      return '请选择商品分类';
    }
    return null;
  }

  String? validateStore(int? value) {
    if (value == null || value == 0) {
      return '请选择所属门店';
    }
    return null;
  }

  String? validateGoldWeight(String? value) {
    if (value == null || value.trim().isEmpty) {
      return '请输入金重';
    }
    final weight = double.tryParse(value);
    if (weight == null || weight < 0) {
      return '请输入有效的金重';
    }
    return null;
  }

  String? validateGoldPrice(String? value) {
    if (value == null || value.trim().isEmpty) {
      return '请输入金价';
    }
    final price = double.tryParse(value);
    if (price == null || price < 0) {
      return '请输入有效的金价';
    }
    return null;
  }

  String? validateSilverWeight(String? value) {
    if (value == null || value.trim().isEmpty) {
      return '请输入银重';
    }
    final weight = double.tryParse(value);
    if (weight == null || weight < 0) {
      return '请输入有效的银重';
    }
    return null;
  }

  String? validateSilverPrice(String? value) {
    if (value == null || value.trim().isEmpty) {
      return '请输入银价';
    }
    final price = double.tryParse(value);
    if (price == null || price < 0) {
      return '请输入有效的银价';
    }
    return null;
  }

  String? validateTotalWeight(String? value) {
    if (value == null || value.trim().isEmpty) {
      return '请输入总重';
    }
    final weight = double.tryParse(value);
    if (weight == null || weight < 0) {
      return '请输入有效的总重';
    }
    return null;
  }

  String? validateWorkPrice(String? value) {
    if (value == null || value.trim().isEmpty) {
      return '请输入工费';
    }
    final price = double.tryParse(value);
    if (price == null || price < 0) {
      return '请输入有效的工费';
    }
    return null;
  }


}
