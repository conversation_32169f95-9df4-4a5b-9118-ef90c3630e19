"""
商品管理API端点
提供商品的CRUD操作和相关业务功能
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from ....core.database import get_db
from ....core.dependencies import get_current_active_user, require_permission, require_permissions
from ....models.jewelry import Jewelry, JewelryCategory
from ....schemas.jewelry import (
    JewelryCreate,
    JewelryUpdate,
    JewelryResponse,
    JewelryCategoryResponse,
    JewelryListResponse
)
from ....schemas.auth import CurrentUserResponse
from ....services.jewelry_service import JewelryService

# 创建路由器
router = APIRouter()

# 商品分类相关API
@router.get("/categories", response_model=List[JewelryCategoryResponse])
async def get_categories(
    status: Optional[int] = Query(None, description="状态筛选:0=禁用,1=正常"),
    current_user: CurrentUserResponse = Depends(require_permission("jewelry.view")),
    db: Session = Depends(get_db)
):
    """获取商品分类列表"""
    service = JewelryService(db)
    return await service.get_categories(status=status)


@router.get("/categories/{category_id}", response_model=JewelryCategoryResponse)
async def get_category(
    category_id: int,
    current_user: CurrentUserResponse = Depends(require_permission("jewelry.view")),
    db: Session = Depends(get_db)
):
    """获取单个商品分类"""
    service = JewelryService(db)
    category = await service.get_category_by_id(category_id)
    if not category:
        raise HTTPException(status_code=404, detail="分类不存在")
    return category


# 商品相关API
@router.get("", response_model=JewelryListResponse)
async def get_jewelry_list(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    category_id: Optional[int] = Query(None, description="分类ID"),
    store_id: Optional[int] = Query(None, description="门店ID"),
    status: Optional[int] = Query(None, description="状态"),
    keyword: Optional[str] = Query(None, description="搜索关键词(商品名称或条码)"),
    current_user: CurrentUserResponse = Depends(require_permission("jewelry.view")),
    db: Session = Depends(get_db)
):
    """
    获取商品库存列表 - 修复门店名称和分类名称显示问题

    支持以下筛选条件:
    - category_id: 按分类筛选
    - store_id: 按门店筛选（权限控制：员工只能查询自己门店）
    - status: 按状态筛选(0=下架,1=上架,2=待出库)
    - keyword: 按商品名称或条码搜索

    权限控制逻辑:
    - 管理员：可查询所有门店库存
    - 门店员工：只能查询自己门店的库存

    返回完整库存信息包括：
    序号、条码、商品名称、分类、圈口号、金重、金价、银重、银价、
    总重、工费、件工费、成本等所有字段

    🔧 修复内容：
    - 使用显式JOIN查询确保关联数据完整性
    - 改进数据转换逻辑，确保门店名称和分类名称正确显示
    - 添加错误处理机制，避免单个商品数据问题影响整体查询
    """
    try:
        service = JewelryService(db)

        # 权限控制：非管理员用户只能查询自己门店的商品
        if current_user.store_id and store_id and store_id != current_user.store_id:
            raise HTTPException(status_code=403, detail="无权访问其他门店的库存")

        # 如果用户有分配门店且未指定store_id，自动使用用户门店
        if current_user.store_id and not store_id:
            store_id = current_user.store_id

        result = await service.get_jewelry_list(
            page=page,
            page_size=page_size,
            category_id=category_id,
            store_id=store_id,
            status=status,
            keyword=keyword
        )

        # 🔧 调试日志：记录返回的数据样本
        if result.items:
            sample_item = result.items[0]
            print(f"📊 API返回数据样本 - 门店名称: {getattr(sample_item, 'store_name', 'N/A')}, 分类名称: {getattr(sample_item, 'category_name', 'N/A')}")

        return result

    except Exception as e:
        print(f"❌ 获取商品列表API出错: {e}")
        raise HTTPException(status_code=500, detail=f"获取商品列表失败: {str(e)}")


@router.get("/{jewelry_id}", response_model=JewelryResponse)
async def get_jewelry(
    jewelry_id: int,
    current_user: CurrentUserResponse = Depends(require_permission("jewelry.view")),
    db: Session = Depends(get_db)
):
    """获取单个商品详情"""
    service = JewelryService(db)
    jewelry = await service.get_jewelry_by_id(jewelry_id)
    if not jewelry:
        raise HTTPException(status_code=404, detail="商品不存在")
    return jewelry


@router.get("/barcode/{barcode}", response_model=JewelryResponse)
async def get_jewelry_by_barcode(
    barcode: str,
    store_id: Optional[int] = Query(None, description="门店ID，用于限制查询范围"),
    current_user: CurrentUserResponse = Depends(require_permission("jewelry.view")),
    db: Session = Depends(get_db)
):
    """根据条码获取商品

    支持门店级别的库存查询：
    - 如果指定store_id，则只查询该门店的商品
    - 如果不指定store_id，则查询全系统商品（管理员权限）
    - 员工账户应该传递其分配的门店ID以确保权限控制
    """
    service = JewelryService(db)

    # 权限检查：非管理员用户只能查询自己门店的商品
    if current_user.store_id and store_id and store_id != current_user.store_id:
        raise HTTPException(status_code=403, detail="无权访问其他门店的商品")

    # 如果用户有分配门店且未指定store_id，自动使用用户门店
    if current_user.store_id and not store_id:
        store_id = current_user.store_id

    jewelry = await service.get_jewelry_by_barcode(barcode, store_id)
    if not jewelry:
        if store_id:
            raise HTTPException(status_code=404, detail="该商品在当前门店中不存在")
        else:
            raise HTTPException(status_code=404, detail="商品不存在")

    # 额外检查：确保商品状态为上架在售（status=1）才能被查询到
    if jewelry.status != 1:
        raise HTTPException(status_code=400, detail="商品状态不可用，只有上架在售的商品才能操作")

    return jewelry


@router.post("", response_model=JewelryResponse)
async def create_jewelry(
    jewelry_data: JewelryCreate,
    current_user: CurrentUserResponse = Depends(require_permission("jewelry.create")),
    db: Session = Depends(get_db)
):
    """创建新商品"""
    service = JewelryService(db)

    # 检查条码是否已存在（全系统范围检查，不限制门店）
    existing = await service.get_jewelry_by_barcode(jewelry_data.barcode, store_id=None)
    if existing:
        raise HTTPException(status_code=400, detail="条码已存在")

    # 检查分类是否存在
    category = await service.get_category_by_id(jewelry_data.category_id)
    if not category:
        raise HTTPException(status_code=400, detail="商品分类不存在")

    return await service.create_jewelry(jewelry_data)


@router.put("/{jewelry_id}", response_model=JewelryResponse)
async def update_jewelry(
    jewelry_id: int,
    jewelry_data: JewelryUpdate,
    current_user: CurrentUserResponse = Depends(require_permission("jewelry.update")),
    db: Session = Depends(get_db)
):
    """更新商品信息"""
    service = JewelryService(db)

    # 检查商品是否存在
    jewelry = await service.get_jewelry_by_id(jewelry_id)
    if not jewelry:
        raise HTTPException(status_code=404, detail="商品不存在")

    # 如果更新条码，检查是否重复（全系统范围检查，不限制门店）
    if jewelry_data.barcode and jewelry_data.barcode != jewelry.barcode:
        existing = await service.get_jewelry_by_barcode(jewelry_data.barcode, store_id=None)
        if existing:
            raise HTTPException(status_code=400, detail="条码已存在")

    return await service.update_jewelry(jewelry_id, jewelry_data)


@router.delete("/{jewelry_id}")
async def delete_jewelry(
    jewelry_id: int,
    current_user: CurrentUserResponse = Depends(require_permission("jewelry.delete")),
    db: Session = Depends(get_db)
):
    """删除商品"""
    service = JewelryService(db)

    jewelry = await service.get_jewelry_by_id(jewelry_id)
    if not jewelry:
        raise HTTPException(status_code=404, detail="商品不存在")

    await service.delete_jewelry(jewelry_id)
    return {"message": "商品删除成功"}


@router.patch("/{jewelry_id}/status", response_model=JewelryResponse)
async def update_jewelry_status(
    jewelry_id: int,
    status: int = Query(..., ge=0, le=2, description="状态:0=下架,1=上架,2=待出库"),
    current_user: CurrentUserResponse = Depends(require_permission("jewelry.update")),
    db: Session = Depends(get_db)
):
    """更新商品状态"""
    service = JewelryService(db)

    jewelry = await service.get_jewelry_by_id(jewelry_id)
    if not jewelry:
        raise HTTPException(status_code=404, detail="商品不存在")

    return await service.update_jewelry_status(jewelry_id, status)


@router.get("/{jewelry_id}/cost-calculation")
async def calculate_jewelry_cost(
    jewelry_id: int,
    current_user: CurrentUserResponse = Depends(require_permission("jewelry.view")),
    db: Session = Depends(get_db)
):
    """计算商品成本明细"""
    service = JewelryService(db)

    jewelry = await service.get_jewelry_by_id(jewelry_id)
    if not jewelry:
        raise HTTPException(status_code=404, detail="商品不存在")

    return await service.calculate_cost_breakdown(jewelry_id)