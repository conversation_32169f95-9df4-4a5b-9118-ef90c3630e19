import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../services/print_template_service.dart';
import '../../../services/print_service.dart';
import '../../../models/print/print_template_config.dart';
import '../../../core/utils/logger_service.dart';

/// 打印模板编辑器控制器
/// 
/// 负责模板编辑的所有逻辑和状态管理
class PrintTemplateEditorController extends GetxController {
  final PrintTemplateConfig? originalTemplate;
  final PrintTemplateService _templateService = Get.find<PrintTemplateService>();

  PrintTemplateEditorController({this.originalTemplate});

  // 基本信息
  late final TextEditingController nameController;
  late final TextEditingController descriptionController;

  // 公司信息
  late final TextEditingController companyNameController;
  final RxBool showAddress = true.obs;
  final RxBool showPhone = true.obs;
  final RxDouble companyFontSize = 16.0.obs;
  final RxBool companyBold = true.obs;

  // 页面设置
  final RxDouble pageWidth = 210.0.obs;
  final RxDouble pageHeight = 101.6.obs;
  final RxDouble marginTop = 3.0.obs;
  final RxDouble marginBottom = 3.0.obs;
  final RxDouble marginLeft = 3.0.obs;
  final RxDouble marginRight = 3.0.obs;

  // 表头配置
  final RxBool showOrderNo = true.obs;
  final RxBool showCustomer = true.obs;
  final RxBool showSaleType = true.obs;
  final RxBool showDateTime = true.obs;
  final RxDouble headerFontSize = 12.0.obs;

  // 商品明细表格配置
  final RxList<String> availableColumns = <String>[
    '序号', '条码', '商品名称', '规格', '重量', '单价', '金额', '分类', '圈口', '金重', '银重', '工费'
  ].obs;
  final RxList<String> selectedColumns = <String>[
    '序号', '条码', '商品名称', '规格', '重量', '单价', '金额'
  ].obs;
  final RxDouble tableHeaderFontSize = 10.0.obs;
  final RxDouble contentFontSize = 9.0.obs;
  final RxDouble rowHeight = 20.0.obs;
  final RxnDouble tableTotalWidth = RxnDouble(); // 表格总宽度，null表示使用默认列宽

  // 汇总信息配置
  final RxBool showTotalQuantity = true.obs;
  final RxBool showTotalWeight = true.obs;
  final RxBool showTotalAmount = true.obs;
  final RxDouble summaryFontSize = 11.0.obs;
  final RxBool summaryBold = true.obs;

  // 收款信息配置
  final RxBool showPaymentMethod = true.obs;
  final RxBool showPaymentDetails = true.obs;
  final RxBool showChangeAmount = true.obs;
  final RxDouble paymentFontSize = 10.0.obs;

  // 页脚配置
  late final TextEditingController customFooterController;
  final RxBool showPrintTime = true.obs;
  final RxDouble footerFontSize = 8.0.obs;

  // 字段位置配置
  final RxMap<String, FieldPosition> fieldPositions = <String, FieldPosition>{}.obs;

  // 预览配置
  final RxDouble previewScale = 1.0.obs;
  final RxBool showGrid = true.obs;
  final RxBool useRealPreview = false.obs;

  // 状态
  final RxBool canSave = false.obs;
  final RxBool isLoading = false.obs;

  @override
  void onInit() {
    super.onInit();
    _initializeControllers();
    _loadTemplateData();
    _setupValidation();
  }

  @override
  void onClose() {
    nameController.dispose();
    descriptionController.dispose();
    companyNameController.dispose();
    customFooterController.dispose();
    super.onClose();
  }

  /// 初始化控制器
  void _initializeControllers() {
    nameController = TextEditingController();
    descriptionController = TextEditingController();
    companyNameController = TextEditingController();
    customFooterController = TextEditingController();
  }

  /// 加载模板数据
  void _loadTemplateData() {
    if (originalTemplate != null) {
      // 编辑模式：加载现有模板数据
      final template = originalTemplate!;
      
      nameController.text = template.name;
      descriptionController.text = template.description ?? '';
      
      // 公司信息
      companyNameController.text = template.companyInfo.companyName;
      showAddress.value = template.companyInfo.showAddress;
      showPhone.value = template.companyInfo.showPhone;
      companyFontSize.value = template.companyInfo.fontSize;
      companyBold.value = template.companyInfo.isBold;
      
      // 页面设置
      pageWidth.value = template.pageConfig.width;
      pageHeight.value = template.pageConfig.height;
      marginTop.value = template.pageConfig.margins.top;
      marginBottom.value = template.pageConfig.margins.bottom;
      marginLeft.value = template.pageConfig.margins.left;
      marginRight.value = template.pageConfig.margins.right;
      
      // 表头配置
      showOrderNo.value = template.headerConfig.showOrderNo;
      showCustomer.value = template.headerConfig.showCustomer;
      showSaleType.value = template.headerConfig.showSaleType;
      showDateTime.value = template.headerConfig.showDateTime;
      headerFontSize.value = template.headerConfig.fontSize;
      
      // 商品明细表格配置
      selectedColumns.value = List<String>.from(template.itemTableConfig.visibleColumns);
      tableHeaderFontSize.value = template.itemTableConfig.headerFontSize;
      contentFontSize.value = template.itemTableConfig.contentFontSize;
      rowHeight.value = template.itemTableConfig.rowHeight;
      tableTotalWidth.value = template.itemTableConfig.totalWidth;
      
      // 汇总信息配置
      showTotalQuantity.value = template.summaryConfig.showTotalQuantity;
      showTotalWeight.value = template.summaryConfig.showTotalWeight;
      showTotalAmount.value = template.summaryConfig.showTotalAmount;
      summaryFontSize.value = template.summaryConfig.fontSize;
      summaryBold.value = template.summaryConfig.isBold;
      
      // 收款信息配置
      showPaymentMethod.value = template.paymentInfoConfig.showPaymentMethod;
      showPaymentDetails.value = template.paymentInfoConfig.showPaymentDetails;
      showChangeAmount.value = template.paymentInfoConfig.showChangeAmount;
      paymentFontSize.value = template.paymentInfoConfig.fontSize;
      
      // 页脚配置
      customFooterController.text = template.footerConfig.customText ?? '';
      showPrintTime.value = template.footerConfig.showPrintTime;
      footerFontSize.value = template.footerConfig.fontSize;

      // 字段位置配置
      _loadFieldPositions(template);

      LoggerService.d('✅ 已加载模板数据: ${template.name}');
    } else {
      // 新建模式：使用默认值
      companyNameController.text = '金包银首饰店';
      customFooterController.text = '谢谢惠顾，欢迎再次光临！';
      _initializeDefaultFieldPositions();
      LoggerService.d('🆕 使用默认模板数据');
    }
  }

  /// 加载字段位置配置
  void _loadFieldPositions(PrintTemplateConfig template) {
    fieldPositions.clear();

    // 公司信息字段
    fieldPositions['company_name'] = template.companyInfo.namePosition;
    fieldPositions['company_address'] = template.companyInfo.addressPosition;
    fieldPositions['company_phone'] = template.companyInfo.phonePosition;

    // 表头字段
    fieldPositions['order_no'] = template.headerConfig.orderNoPosition;
    fieldPositions['customer'] = template.headerConfig.customerPosition;
    fieldPositions['sale_type'] = template.headerConfig.saleTypePosition;
    fieldPositions['date_time'] = template.headerConfig.dateTimePosition;

    // 表格位置
    fieldPositions['item_table'] = template.itemTableConfig.tablePosition;
  }

  /// 初始化默认字段位置
  void _initializeDefaultFieldPositions() {
    fieldPositions.clear();

    // 公司信息字段默认位置
    fieldPositions['company_name'] = const FieldPosition(x: 105.0, y: 10.0);
    fieldPositions['company_address'] = const FieldPosition(x: 105.0, y: 25.0);
    fieldPositions['company_phone'] = const FieldPosition(x: 105.0, y: 35.0);

    // 表头字段默认位置
    fieldPositions['order_no'] = const FieldPosition(x: 10.0, y: 45.0);
    fieldPositions['customer'] = const FieldPosition(x: 105.0, y: 45.0);
    fieldPositions['sale_type'] = const FieldPosition(x: 10.0, y: 55.0);
    fieldPositions['date_time'] = const FieldPosition(x: 105.0, y: 55.0);

    // 表格默认位置
    fieldPositions['item_table'] = const FieldPosition(x: 10.0, y: 65.0);
  }

  /// 更新字段位置
  void updateFieldPosition(String fieldKey, FieldPosition position) {
    fieldPositions[fieldKey] = position;
    // 字段位置已更新
  }

  /// 获取字段位置
  FieldPosition getFieldPosition(String fieldKey) {
    return fieldPositions[fieldKey] ?? const FieldPosition();
  }

  /// 构建当前模板配置（用于实时预览）
  PrintTemplateConfig buildCurrentTemplate() {
    return _buildTemplateConfig();
  }

  /// 获取示例数据（用于实时预览）
  Map<String, dynamic> getSampleData() {
    return _buildSampleData();
  }

  /// 设置验证逻辑
  void _setupValidation() {
    // 监听必填字段变化
    nameController.addListener(_validateForm);
    companyNameController.addListener(_validateForm);

    // 监听选中列变化
    selectedColumns.listen((_) => _validateForm());

    // 初始验证
    _validateForm();
  }

  /// 验证表单
  void _validateForm() {
    final nameValid = nameController.text.trim().isNotEmpty;
    final companyNameValid = companyNameController.text.trim().isNotEmpty;
    final columnsValid = selectedColumns.isNotEmpty;

    LoggerService.d('🔍 表单验证: name=$nameValid, company=$companyNameValid, columns=$columnsValid');
    LoggerService.d('📝 当前值: name="${nameController.text}", company="${companyNameController.text}", columns=${selectedColumns.length}');

    canSave.value = nameValid && companyNameValid && columnsValid;
  }

  /// 保存模板
  Future<void> saveTemplate() async {
    LoggerService.d('🔄 开始保存模板，canSave=${canSave.value}');

    if (!canSave.value) {
      LoggerService.w('⚠️ 验证失败，无法保存模板');
      Get.snackbar(
        '验证失败',
        '请检查必填字段：模板名称、公司名称、表格列',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange[600],
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );
      return;
    }

    try {
      isLoading.value = true;
      LoggerService.d('💾 保存模板: ${nameController.text}');

      final template = _buildTemplateConfig();
      LoggerService.d('📋 模板配置构建完成: ${template.toJson()}');

      bool success;
      if (originalTemplate != null) {
        // 更新现有模板
        LoggerService.d('🔄 更新现有模板: ${originalTemplate!.id}');
        success = await _templateService.updateTemplate(template);
      } else {
        // 创建新模板
        LoggerService.d('🆕 创建新模板: ${template.id}');
        success = await _templateService.createTemplate(template);
      }

      LoggerService.d('💾 保存结果: $success');

      if (success) {
        Get.snackbar(
          '保存成功',
          originalTemplate != null ? '模板已更新' : '模板已创建',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green[600],
          colorText: Colors.white,
          duration: const Duration(seconds: 2),
        );

        // 返回成功结果
        Get.back(result: true);
      } else {
        LoggerService.e('❌ 保存失败，服务返回false');
        Get.snackbar(
          '保存失败',
          '无法保存模板，请重试',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red[600],
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
        );
      }
    } catch (e, stackTrace) {
      LoggerService.e('❌ 保存模板异常', e, stackTrace);
      Get.snackbar(
        '保存失败',
        '保存过程中发生错误: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red[600],
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// 预览模板
  Future<void> previewTemplate() async {
    try {
      LoggerService.d('👁️ 开始全屏预览模板');

      final template = _buildTemplateConfig();
      final sampleData = _buildSampleData();

      // 详细记录模板配置
      LoggerService.d('🎨 全屏预览模板配置:');
      LoggerService.d('  - 模板名称: ${template.name}');
      LoggerService.d('  - 公司名称: ${template.companyInfo.companyName}');
      LoggerService.d('  - 公司字体大小: ${template.companyInfo.fontSize}px');
      LoggerService.d('  - 显示地址: ${template.companyInfo.showAddress}');
      LoggerService.d('  - 显示电话: ${template.companyInfo.showPhone}');
      LoggerService.d('  - 页面尺寸: ${template.pageConfig.width}x${template.pageConfig.height}mm');
      LoggerService.d('  - 页面边距: ${template.pageConfig.margins.top}/${template.pageConfig.margins.bottom}/${template.pageConfig.margins.left}/${template.pageConfig.margins.right}mm');
      LoggerService.d('  - 表头字体: ${template.headerConfig.fontSize}px');
      LoggerService.d('  - 显示订单号: ${template.headerConfig.showOrderNo}');
      LoggerService.d('  - 显示客户: ${template.headerConfig.showCustomer}');
      LoggerService.d('  - 显示日期: ${template.headerConfig.showDateTime}');
      LoggerService.d('  - 表格列: ${template.itemTableConfig.visibleColumns}');
      LoggerService.d('  - 表格表头字体: ${template.itemTableConfig.headerFontSize}px');
      LoggerService.d('  - 表格内容字体: ${template.itemTableConfig.contentFontSize}px');
      LoggerService.d('  - 汇总字体: ${template.summaryConfig.fontSize}px');
      LoggerService.d('  - 收款字体: ${template.paymentInfoConfig.fontSize}px');
      LoggerService.d('  - 页脚字体: ${template.footerConfig.fontSize}px');
      LoggerService.d('  - 页脚自定义文本: ${template.footerConfig.customText}');

      // 使用PrintService进行预览
      final printService = PrintService();
      LoggerService.d('🖨️ 调用PrintService.showReceiptPreview');
      await printService.showReceiptPreview(
        stockOutData: sampleData['stockOutData'] as Map<String, dynamic>,
        paymentData: sampleData['paymentData'] as Map<String, dynamic>,
        items: sampleData['items'] as List<Map<String, dynamic>>,
        template: template,
      );
      LoggerService.d('✅ 全屏预览调用完成');
    } catch (e) {
      LoggerService.e('❌ 预览模板失败', e);
      Get.snackbar(
        '预览失败',
        '无法预览模板: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red[600],
        colorText: Colors.white,
      );
    }
  }

  /// 构建模板配置
  PrintTemplateConfig _buildTemplateConfig() {
    final templateId = originalTemplate?.id ?? 'template_${DateTime.now().millisecondsSinceEpoch}';
    
    return PrintTemplateConfig(
      id: templateId,
      name: nameController.text.trim(),
      description: descriptionController.text.trim().isEmpty ? null : descriptionController.text.trim(),
      isDefault: originalTemplate?.isDefault ?? false,
      companyInfo: CompanyInfoConfig(
        companyName: companyNameController.text.trim(),
        showAddress: showAddress.value,
        showPhone: showPhone.value,
        fontSize: companyFontSize.value,
        isBold: companyBold.value,
        namePosition: getFieldPosition('company_name'),
        addressPosition: getFieldPosition('company_address'),
        phonePosition: getFieldPosition('company_phone'),
      ),
      pageConfig: PageConfig(
        width: pageWidth.value,
        height: pageHeight.value,
        margins: EdgeInsetsConfig(
          top: marginTop.value,
          bottom: marginBottom.value,
          left: marginLeft.value,
          right: marginRight.value,
        ),
      ),
      headerConfig: HeaderConfig(
        showOrderNo: showOrderNo.value,
        showCustomer: showCustomer.value,
        showSaleType: showSaleType.value,
        showDateTime: showDateTime.value,
        fontSize: headerFontSize.value,
        orderNoPosition: getFieldPosition('order_no'),
        customerPosition: getFieldPosition('customer'),
        saleTypePosition: getFieldPosition('sale_type'),
        dateTimePosition: getFieldPosition('date_time'),
      ),
      itemTableConfig: ItemTableConfig(
        visibleColumns: List<String>.from(selectedColumns),
        columnWidths: _buildColumnWidths(),
        totalWidth: tableTotalWidth.value,
        headerFontSize: tableHeaderFontSize.value,
        contentFontSize: contentFontSize.value,
        rowHeight: rowHeight.value,
        tablePosition: getFieldPosition('item_table'),
      ),
      summaryConfig: SummaryConfig(
        showTotalQuantity: showTotalQuantity.value,
        showTotalWeight: showTotalWeight.value,
        showTotalAmount: showTotalAmount.value,
        fontSize: summaryFontSize.value,
        isBold: summaryBold.value,
      ),
      paymentInfoConfig: PaymentInfoConfig(
        showPaymentMethod: showPaymentMethod.value,
        showPaymentDetails: showPaymentDetails.value,
        showChangeAmount: showChangeAmount.value,
        fontSize: paymentFontSize.value,
      ),
      footerConfig: FooterConfig(
        customText: customFooterController.text.trim().isEmpty ? null : customFooterController.text.trim(),
        showPrintTime: showPrintTime.value,
        fontSize: footerFontSize.value,
      ),
      createTime: originalTemplate?.createTime ?? DateTime.now(),
      updateTime: DateTime.now(),
    );
  }

  /// 构建列宽配置
  Map<String, double> _buildColumnWidths() {
    final defaultWidths = {
      '序号': 8.0,
      '条码': 15.0,
      '商品名称': 25.0,
      '规格': 12.0,
      '重量': 12.0,
      '单价': 13.0,
      '金额': 15.0,
      '分类': 10.0,
      '圈口': 8.0,
      '金重': 10.0,
      '银重': 10.0,
      '工费': 10.0,
    };
    
    final result = <String, double>{};
    for (final column in selectedColumns) {
      result[column] = defaultWidths[column] ?? 10.0;
    }
    
    return result;
  }

  /// 构建示例数据
  Map<String, dynamic> _buildSampleData() {
    return {
      'stockOutData': {
        'order_no': 'CK20241201001',
        'customer': '张三',
        'sale_type': 'retail',
        'store_name': companyNameController.text.trim(),
        'store_address': '广东省深圳市罗湖区黄金珠宝城A座101号',
        'store_phone': '0755-12345678',
        'remark': '示例订单',
      },
      'paymentData': {
        'payment_info': {
          'cash_amount': '800.00',
          'wechat_amount': '500.00',
          'alipay_amount': '0.00',
          'card_amount': '0.00',
          'discount_amount': '0.00',
          'actual_amount': '1300.00',
        },
      },
      'items': [
        {
          'barcode': 'JW001',
          'name': '黄金戒指',
          'product_name': '黄金戒指',
          'category': '戒指',
          'ring_size': '16号',
          'specification': '16号',
          'quantity': '1',
          'gold_weight': '5.20',
          'silver_weight': '0.00',
          'total_weight': '5.20',
          'gold_price': '450.00',
          'price': '450.00',
          'work_price': '30.00',
          'labor_cost': '30.00',
          'total_amount': '2496.00',
          'amount': '2496.00',
        },
        {
          'barcode': 'YS002',
          'name': '银手镯',
          'product_name': '银手镯',
          'category': '手镯',
          'ring_size': '',
          'specification': '',
          'quantity': '1',
          'gold_weight': '0.00',
          'silver_weight': '15.80',
          'total_weight': '15.80',
          'silver_price': '8.00',
          'price': '8.00',
          'work_price': '5.00',
          'labor_cost': '5.00',
          'total_amount': '205.40',
          'amount': '205.40',
        },
      ],
    };
  }
}
