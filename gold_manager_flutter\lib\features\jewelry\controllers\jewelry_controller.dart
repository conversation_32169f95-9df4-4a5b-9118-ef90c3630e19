import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/utils/logger_service.dart';
import '../../../models/jewelry/jewelry.dart';
import '../../../models/jewelry/jewelry_category.dart';
import '../../../services/jewelry_service.dart';

/// 首饰控制器
/// 处理首饰相关的业务逻辑
class JewelryController extends GetxController {
  final JewelryService _jewelryService = Get.find<JewelryService>();

  // 搜索控制器
  final TextEditingController searchController = TextEditingController();

  // 状态变量
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;

  // 数据列表
  final RxList<Jewelry> jewelryList = <Jewelry>[].obs;
  final RxList<Jewelry> filteredJewelryList = <Jewelry>[].obs;
  final RxList<JewelryCategory> categories = <JewelryCategory>[].obs;

  // 筛选和排序
  final RxInt selectedCategoryId = 0.obs;
  final RxString sortBy = 'name'.obs;
  final RxBool isAscending = true.obs;

  @override
  void onInit() {
    super.onInit();
    fetchData();
    LoggerService.d('JewelryController 初始化');
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }

  /// 刷新数据
  Future<void> refreshData() async {
    return fetchData();
  }

  /// 获取数据
  Future<void> fetchData() async {
    await Future.wait([
      fetchJewelryList(),
      fetchCategories(),
    ]);
  }

  /// 获取首饰列表
  Future<void> fetchJewelryList() async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      final result = await _jewelryService.getJewelryList({});

      // 解析API返回的数据
      jewelryList.value = result.data;

      applyFilters(); // 应用筛选

    } catch (e) {
      LoggerService.e('获取首饰列表失败', e);
      errorMessage.value = e.toString();
      jewelryList.value = []; // 确保在错误时清空列表
    } finally {
      isLoading.value = false;
    }
  }

  /// 获取首饰分类
  Future<void> fetchCategories() async {
    try {
      final result = await _jewelryService.getCategories();

      // 解析API返回的数据
      categories.value = result;
    } catch (e) {
      LoggerService.e('获取首饰分类失败', e);
      categories.value = []; // 确保在错误时清空列表
    }
  }

  /// 根据关键字搜索
  void searchJewelry(String keyword) {
    applyFilters();
  }

  /// 根据分类筛选
  void filterByCategory(int? categoryId) {
    if (categoryId != null) {
      selectedCategoryId.value = categoryId;
    }
    applyFilters();
  }

  /// 排序首饰列表
  void sortJewelry(String sortField) {
    if (sortBy.value == sortField) {
      // 如果是同一个字段，切换排序方向
      isAscending.value = !isAscending.value;
    } else {
      // 如果是不同字段，设置新字段并默认升序
      sortBy.value = sortField;
      isAscending.value = true;
    }

    // 应用筛选和排序
    applyFilters();
  }

  /// 应用筛选和排序
  void applyFilters() {
    // 复制一份列表用于筛选
    List<Jewelry> result = List<Jewelry>.from(jewelryList);

    // 关键字搜索
    final keyword = searchController.text.trim().toLowerCase();
    if (keyword.isNotEmpty) {
      result = result.where((jewelry) =>
        jewelry.name.toLowerCase().contains(keyword) ||
        jewelry.barcode.toLowerCase().contains(keyword)
        // 注意: Jewelry 类中没有 description 属性
      ).toList();
    }

    // 分类筛选
    if (selectedCategoryId.value > 0) {
      result = result.where((jewelry) =>
        jewelry.categoryId == selectedCategoryId.value
      ).toList();
    }

    // 排序
    result.sort((a, b) {
      int compareResult;

      switch (sortBy.value) {
        case 'name':
          compareResult = a.name.compareTo(b.name);
          break;
        case 'gold_weight':
          compareResult = a.goldWeight.compareTo(b.goldWeight);
          break;
        case 'silver_weight':
          compareResult = a.silverWeight.compareTo(b.silverWeight);
          break;
        case 'price':
          compareResult = a.salePrice.compareTo(b.salePrice); // 使用 salePrice 而不是不存在的 retailPrice
          break;
        default:
          compareResult = 0;
      }

      return isAscending.value ? compareResult : -compareResult;
    });

    filteredJewelryList.value = result;
  }

  /// 查看首饰详情
  void viewJewelryDetails(Jewelry jewelry) {
    Get.toNamed('/jewelry/details', arguments: jewelry);
  }
}