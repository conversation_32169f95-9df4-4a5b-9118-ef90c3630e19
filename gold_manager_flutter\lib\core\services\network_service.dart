import 'dart:io';
import 'dart:async';
import 'package:http/http.dart' as http;

class NetworkService {
  static NetworkService? _instance;
  static NetworkService get instance => _instance ??= NetworkService._();
  
  NetworkService._();
  
  StreamController<bool>? _connectivityController;
  Timer? _connectivityTimer;
  bool _isConnected = true;
  
  Stream<bool> get connectivityStream {
    _connectivityController ??= StreamController<bool>.broadcast();
    _startConnectivityCheck();
    return _connectivityController!.stream;
  }
  
  bool get isConnected => _isConnected;
  
  void _startConnectivityCheck() {
    _connectivityTimer?.cancel();
    _connectivityTimer = Timer.periodic(
      const Duration(seconds: 10),
      (_) => _checkConnectivity(),
    );
    _checkConnectivity(); // 立即检查一次
  }
  
  Future<void> _checkConnectivity() async {
    try {
      // 尝试连接到Google DNS
      final result = await InternetAddress.lookup('google.com');
      final newStatus = result.isNotEmpty && result[0].rawAddress.isNotEmpty;
      
      if (newStatus != _isConnected) {
        _isConnected = newStatus;
        _connectivityController?.add(_isConnected);
      }
    } catch (e) {
      // 如果Google不可达，尝试连接到百度
      try {
        final result = await InternetAddress.lookup('baidu.com');
        final newStatus = result.isNotEmpty && result[0].rawAddress.isNotEmpty;
        
        if (newStatus != _isConnected) {
          _isConnected = newStatus;
          _connectivityController?.add(_isConnected);
        }
      } catch (e) {
        // 所有连接都失败，认为网络不可达
        if (_isConnected) {
          _isConnected = false;
          _connectivityController?.add(_isConnected);
        }
      }
    }
  }
  
  Future<bool> hasInternetConnection() async {
    try {
      final response = await http.get(
        Uri.parse('https://www.google.com'),
        headers: {'Connection': 'close'},
      ).timeout(const Duration(seconds: 5));
      
      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }
  
  void dispose() {
    _connectivityTimer?.cancel();
    _connectivityController?.close();
    _connectivityController = null;
  }
}