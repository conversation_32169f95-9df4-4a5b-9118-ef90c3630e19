import 'package:get/get.dart';
import '../../../core/services/api_client.dart';
import '../../../core/utils/logger.dart';
import '../models/inventory_check.dart';
// 使用现有的InventoryCheckItem模型

/// 库存盘点API服务
/// 
/// 提供盘点单的增删改查功能
/// 集成权限控制和错误处理
class InventoryCheckService extends GetxService {
  final ApiClient _apiClient = Get.find<ApiClient>();
  static const String _baseUrl = '/api/v1/inventory-check';

  /// 获取盘点单列表
  /// 
  /// 支持分页查询和多条件筛选
  /// 自动处理门店级权限控制
  Future<PaginatedInventoryCheckResponse> getInventoryCheckList({
    int page = 1,
    int pageSize = 20,
    String? keyword,
    int? storeId,
    int? status,
    int? operatorId,
    String? startDate,
    String? endDate,
  }) async {
    try {
      LoggerService.i('🔄 获取盘点单列表 - 页码: $page, 门店: $storeId, 状态: $status');

      final params = <String, dynamic>{
        'page': page,
        'page_size': pageSize,
      };

      if (keyword != null && keyword.isNotEmpty) {
        params['keyword'] = keyword;
      }
      if (storeId != null) {
        params['store_id'] = storeId;
      }
      if (status != null) {
        params['status'] = status;
      }
      if (operatorId != null) {
        params['operator_id'] = operatorId;
      }
      if (startDate != null && startDate.isNotEmpty) {
        params['start_date'] = startDate;
      }
      if (endDate != null && endDate.isNotEmpty) {
        params['end_date'] = endDate;
      }

      final response = await _apiClient.get(_baseUrl, queryParameters: params);

      LoggerService.i('✅ 盘点单列表获取成功 - 总数: ${response.data['pagination']['total']}');
      
      return PaginatedInventoryCheckResponse.fromJson(response.data);
    } catch (e) {
      LoggerService.e('❌ 获取盘点单列表失败', e);
      rethrow;
    }
  }

  /// 获取盘点单详情
  ///
  /// 包含盘点单基本信息和明细列表
  Future<InventoryCheck> getInventoryCheckDetail(int checkId) async {
    try {
      LoggerService.i('🔄 获取盘点单详情 - ID: $checkId');

      final response = await _apiClient.get('$_baseUrl/$checkId');

      LoggerService.i('✅ 盘点单详情获取成功');

      return InventoryCheck.fromJson(response.data['data']);
    } catch (e) {
      LoggerService.e('❌ 获取盘点单详情失败', e);
      rethrow;
    }
  }

  /// 创建盘点单
  ///
  /// 创建新的库存盘点单
  /// 可指定盘点商品范围或盘点全部在售商品
  Future<InventoryCheck> createInventoryCheck(InventoryCheckCreateRequest request) async {
    try {
      LoggerService.i('🔄 创建盘点单 - 门店: ${request.storeId}');

      final response = await _apiClient.post(_baseUrl, data: request.toJson());

      LoggerService.i('✅ 盘点单创建成功');

      return InventoryCheck.fromJson(response.data['data']);
    } catch (e) {
      LoggerService.e('❌ 创建盘点单失败', e);
      rethrow;
    }
  }

  /// 更新盘点单
  ///
  /// 更新盘点单基本信息
  /// 只有进行中的盘点单可以更新
  Future<InventoryCheck> updateInventoryCheck(int checkId, InventoryCheckUpdateRequest request) async {
    try {
      LoggerService.i('🔄 更新盘点单 - ID: $checkId');

      final response = await _apiClient.put('$_baseUrl/$checkId', data: request.toJson());

      LoggerService.i('✅ 盘点单更新成功');

      return InventoryCheck.fromJson(response.data['data']);
    } catch (e) {
      LoggerService.e('❌ 更新盘点单失败', e);
      rethrow;
    }
  }

  /// 删除盘点单
  ///
  /// 删除指定的盘点单
  /// 只有进行中的盘点单可以删除
  Future<void> deleteInventoryCheck(int checkId) async {
    try {
      LoggerService.i('🔄 删除盘点单 - ID: $checkId');

      await _apiClient.delete('$_baseUrl/$checkId');

      LoggerService.i('✅ 盘点单删除成功');
    } catch (e) {
      LoggerService.e('❌ 删除盘点单失败', e);
      rethrow;
    }
  }

  /// 获取盘点明细列表
  ///
  /// 获取指定盘点单的商品明细（获取所有数据，无分页限制）
  Future<List<InventoryCheckItem>> getInventoryCheckItems(int checkId, {
    int? status,
  }) async {
    try {
      LoggerService.i('🔄 获取盘点明细 - 盘点单ID: $checkId');

      // 获取所有数据，使用大的页面大小
      final params = <String, dynamic>{
        'page': 1,
        'page_size': 10000, // 设置足够大的页面大小以获取所有数据
      };

      if (status != null) {
        params['status'] = status;
      }

      final response = await _apiClient.get(
        '$_baseUrl/$checkId/items',
        queryParameters: params,
      );

      final items = (response.data['data'] as List<dynamic>?)
          ?.map((item) => InventoryCheckItem.fromJson(item))
          .toList() ?? [];

      LoggerService.i('✅ 盘点明细获取成功 - 数量: ${items.length}');

      return items;
    } catch (e) {
      LoggerService.e('❌ 获取盘点明细失败', e);
      rethrow;
    }
  }

  /// 获取盘点明细列表（分页）
  ///
  /// 分页获取指定盘点单的商品明细，用于大数据量场景
  Future<List<InventoryCheckItem>> getInventoryCheckItemsPaginated(
    int checkId, {
    int page = 1,
    int pageSize = 100,
    int? status,
  }) async {
    try {
      LoggerService.i('🔄 获取盘点明细(分页) - 盘点单ID: $checkId, 页码: $page, 页大小: $pageSize');

      final params = <String, dynamic>{
        'page': page,
        'page_size': pageSize,
      };

      if (status != null) {
        params['status'] = status;
      }

      final response = await _apiClient.get(
        '$_baseUrl/$checkId/items',
        queryParameters: params,
      );

      final items = (response.data['data'] as List<dynamic>?)
          ?.map((item) => InventoryCheckItem.fromJson(item))
          .toList() ?? [];

      LoggerService.i('✅ 盘点明细(分页)获取成功 - 数量: ${items.length}');

      return items;
    } catch (e) {
      LoggerService.e('❌ 获取盘点明细(分页)失败', e);
      rethrow;
    }
  }

  /// 扫码盘点
  ///
  /// 通过扫码进行商品盘点
  Future<InventoryCheckItem> scanInventoryCheck(int checkId, {
    required String barcode,
    required int actualStock,
    String? remark,
  }) async {
    try {
      LoggerService.i('🔄 扫码盘点 - 盘点单ID: $checkId, 条码: $barcode');

      final data = {
        'barcode': barcode,
        'actual_stock': actualStock,
        'remark': remark,
      };

      final response = await _apiClient.post('$_baseUrl/$checkId/scan', data: data);

      LoggerService.i('✅ 扫码盘点成功');

      return InventoryCheckItem.fromJson(response.data['data']);
    } catch (e) {
      LoggerService.e('❌ 扫码盘点失败', e);
      rethrow;
    }
  }

  /// 批量盘点
  ///
  /// 批量更新多个商品的盘点结果
  Future<List<InventoryCheckItem>> batchInventoryCheck(int checkId, List<Map<String, dynamic>> items) async {
    try {
      LoggerService.i('🔄 批量盘点 - 盘点单ID: $checkId, 商品数量: ${items.length}');

      final data = {
        'items': items,
      };

      final response = await _apiClient.post('$_baseUrl/$checkId/batch-check', data: data);

      final resultItems = (response.data['data'] as List<dynamic>?)
          ?.map((item) => InventoryCheckItem.fromJson(item))
          .toList() ?? [];

      LoggerService.i('✅ 批量盘点成功');

      return resultItems;
    } catch (e) {
      LoggerService.e('❌ 批量盘点失败', e);
      rethrow;
    }
  }

  /// 盘点单个商品
  ///
  /// 更新单个商品的盘点结果
  Future<InventoryCheckItem> checkInventoryItem(
    int checkId,
    int itemId,
    int actualQuantity, {
    String? remark,
  }) async {
    try {
      LoggerService.i('🔄 盘点商品 - 盘点单ID: $checkId, 商品ID: $itemId, 实际数量: $actualQuantity');

      final data = {
        'actual_stock': actualQuantity,
        'remark': remark,
      };

      final response = await _apiClient.patch('$_baseUrl/$checkId/items/$itemId/check', data: data);

      LoggerService.i('✅ 商品盘点成功');

      return InventoryCheckItem.fromJson(response.data['data']);
    } catch (e) {
      LoggerService.e('❌ 盘点商品失败', e);
      rethrow;
    }
  }

  /// 盘点单个商品（带备注）
  ///
  /// 这是一个便捷方法，调用主要的盘点方法
  Future<InventoryCheckItem> checkInventoryItemWithRemark(
    int checkId,
    int itemId,
    int actualQuantity,
    String? remark,
  ) async {
    return checkInventoryItem(
      checkId,
      itemId,
      actualQuantity,
      remark: remark,
    );
  }

  /// 完成盘点
  ///
  /// 将盘点单状态设置为已完成
  Future<InventoryCheck> completeInventoryCheck(int checkId) async {
    try {
      LoggerService.i('🔄 完成盘点 - ID: $checkId');

      final request = InventoryCheckUpdateRequest(status: 1);
      final response = await _apiClient.put('$_baseUrl/$checkId', data: request.toJson());

      LoggerService.i('✅ 盘点完成成功');

      return InventoryCheck.fromJson(response.data['data']);
    } catch (e) {
      LoggerService.e('❌ 完成盘点失败', e);
      rethrow;
    }
  }

  /// 取消盘点
  ///
  /// 将盘点单状态设置为已取消
  Future<InventoryCheck> cancelInventoryCheck(int checkId) async {
    try {
      LoggerService.i('🔄 取消盘点 - ID: $checkId');

      final request = InventoryCheckUpdateRequest(status: 2);
      final response = await _apiClient.put('$_baseUrl/$checkId', data: request.toJson());

      LoggerService.i('✅ 盘点取消成功');

      return InventoryCheck.fromJson(response.data['data']);
    } catch (e) {
      LoggerService.e('❌ 取消盘点失败', e);
      rethrow;
    }
  }

  /// 获取盘点统计
  ///
  /// 获取盘点相关的统计信息
  Future<Map<String, dynamic>> getInventoryCheckStatistics({
    int? storeId,
    String? startDate,
    String? endDate,
  }) async {
    try {
      LoggerService.i('🔄 获取盘点统计');

      final params = <String, dynamic>{};

      if (storeId != null) {
        params['store_id'] = storeId;
      }
      if (startDate != null && startDate.isNotEmpty) {
        params['start_date'] = startDate;
      }
      if (endDate != null && endDate.isNotEmpty) {
        params['end_date'] = endDate;
      }

      final response = await _apiClient.get(
        '$_baseUrl/statistics',
        queryParameters: params,
      );

      LoggerService.i('✅ 盘点统计获取成功');

      return response.data['data'];
    } catch (e) {
      LoggerService.e('❌ 获取盘点统计失败', e);
      rethrow;
    }
  }
}
