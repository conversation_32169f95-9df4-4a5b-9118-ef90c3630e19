import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../theme/app_colors.dart';

/// 对话框工具类
class DialogUtils {
  /// 显示确认对话框
  static Future<bool?> showConfirmDialog({
    required String title,
    required String content,
    String confirmText = '确认',
    String cancelText = '取消',
    bool isDismissible = true,
  }) async {
    return await Get.dialog<bool>(
      AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: Text(
              cancelText,
              style: const TextStyle(color: AppColors.textSecondary),
            ),
          ),
          ElevatedButton(
            onPressed: () => Get.back(result: true),
            child: Text(confirmText),
          ),
        ],
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      barrierDismissible: isDismissible,
    );
  }

  /// 显示信息对话框
  static Future<void> showInfoDialog({
    required String title,
    required String content,
    String buttonText = '确定',
    bool isDismissible = true,
  }) async {
    return await Get.dialog(
      AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          ElevatedButton(
            onPressed: () => Get.back(),
            child: Text(buttonText),
          ),
        ],
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      barrierDismissible: isDismissible,
    );
  }

  /// 显示自定义内容对话框
  static Future<T?> showCustomDialog<T>({
    required String title,
    required Widget content,
    List<Widget>? actions,
    bool isDismissible = true,
  }) async {
    return await Get.dialog<T>(
      AlertDialog(
        title: Text(title),
        content: content,
        actions: actions,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      barrierDismissible: isDismissible,
    );
  }

  /// 显示底部弹出菜单
  static Future<T?> showBottomSheet<T>({
    required List<Widget> children,
    String? title,
    bool isDismissible = true,
    bool isScrollControlled = false,
  }) async {
    return await Get.bottomSheet<T>(
      Container(
        padding: const EdgeInsets.all(16),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(16),
            topRight: Radius.circular(16),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (title != null) ...[
              Center(
                child: Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.only(bottom: 16),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
            ],
            ...children,
          ],
        ),
      ),
      isDismissible: isDismissible,
      isScrollControlled: isScrollControlled,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      enableDrag: true,
    );
  }

  /// 显示成功提示
  static void showSuccessSnackBar({
    required String title,
    required String message,
    int duration = 3000,
  }) {
    Get.snackbar(
      title,
      message,
      backgroundColor: AppColors.success.withOpacity(0.1),
      colorText: AppColors.success,
      icon: const Icon(
        Icons.check_circle,
        color: AppColors.success,
      ),
      duration: Duration(milliseconds: duration),
      snackPosition: SnackPosition.BOTTOM,
      margin: const EdgeInsets.all(16),
      borderRadius: 8,
    );
  }

  /// 显示错误提示
  static void showErrorSnackBar({
    required String title,
    required String message,
    int duration = 3000,
  }) {
    Get.snackbar(
      title,
      message,
      backgroundColor: AppColors.error.withOpacity(0.1),
      colorText: AppColors.error,
      icon: const Icon(
        Icons.error,
        color: AppColors.error,
      ),
      duration: Duration(milliseconds: duration),
      snackPosition: SnackPosition.BOTTOM,
      margin: const EdgeInsets.all(16),
      borderRadius: 8,
    );
  }

  /// 显示警告提示
  static void showWarningSnackBar({
    required String title,
    required String message,
    int duration = 3000,
  }) {
    Get.snackbar(
      title,
      message,
      backgroundColor: AppColors.warning.withOpacity(0.1),
      colorText: AppColors.warning,
      icon: const Icon(
        Icons.warning,
        color: AppColors.warning,
      ),
      duration: Duration(milliseconds: duration),
      snackPosition: SnackPosition.BOTTOM,
      margin: const EdgeInsets.all(16),
      borderRadius: 8,
    );
  }

  /// 显示信息提示
  static void showInfoSnackBar({
    required String title,
    required String message,
    int duration = 3000,
  }) {
    Get.snackbar(
      title,
      message,
      backgroundColor: AppColors.info.withOpacity(0.1),
      colorText: AppColors.info,
      icon: const Icon(
        Icons.info,
        color: AppColors.info,
      ),
      duration: Duration(milliseconds: duration),
      snackPosition: SnackPosition.BOTTOM,
      margin: const EdgeInsets.all(16),
      borderRadius: 8,
    );
  }
} 