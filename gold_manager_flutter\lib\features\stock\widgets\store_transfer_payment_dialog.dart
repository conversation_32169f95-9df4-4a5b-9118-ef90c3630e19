import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

import '../../../core/constants/border_styles.dart';
import '../../../core/utils/logger.dart';
import '../controllers/store_transfer_payment_controller.dart';
import '../controllers/store_transfer_form_controller.dart';

/// 库存调拨收款结算对话框
///
/// 根据收款方案.md文档设计，完全复制PaymentDialog的功能：
/// 1. 支持现金、刷卡、微信、支付宝四种支付方式
/// 2. 自动计算抹零金额和找零金额
/// 3. 实时计算总付款金额和实收金额
/// 4. 严格遵循AppBorderStyles设计标准
/// 5. UI设计与PaymentDialog完全一致
class StoreTransferPaymentDialog extends StatelessWidget {
  final double totalAmount; // 应付金额
  final Function(Map<String, dynamic>) onPaymentConfirmed; // 收款确认回调
  final Map<String, dynamic>? transferData; // 调拨单数据
  final List<Map<String, dynamic>>? itemsData; // 商品明细数据
  final StoreTransferFormController? formController; // 调拨单表单控制器引用

  const StoreTransferPaymentDialog({
    super.key,
    required this.totalAmount,
    required this.onPaymentConfirmed,
    this.transferData,
    this.itemsData,
    this.formController, // 新增：接收表单控制器引用
  });

  @override
  Widget build(BuildContext context) {
    // 创建收款控制器实例
    final controller = Get.put(
      StoreTransferPaymentController(totalAmount: totalAmount),
    );

    return Dialog(
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppBorderStyles.mediumBorderRadius),
      ),
      child: Container(
        width: 600, // 固定宽度，适合内容显示
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildDialogHeader(),
            const SizedBox(height: 20),
            _buildPaymentAmountDisplay(controller),
            const SizedBox(height: 24),
            _buildPaymentInputSection(controller),
            const SizedBox(height: 16),
            _buildQuickPaymentButtons(controller),
            const SizedBox(height: 20),
            _buildCalculationResults(controller),
            const SizedBox(height: 24),
            _buildActionButtons(controller),
          ],
        ),
      ),
    );
  }

  /// 构建对话框标题
  Widget _buildDialogHeader() {
    return Row(
      children: [
        Icon(Icons.payment, color: Colors.blue[600], size: 24),
        const SizedBox(width: 12),
        const Text(
          '收款结算',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const Spacer(),
        IconButton(
          onPressed: () => Get.back(),
          icon: const Icon(Icons.close, color: Colors.grey),
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
        ),
      ],
    );
  }

  /// 构建应付金额显示
  Widget _buildPaymentAmountDisplay(StoreTransferPaymentController controller) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(AppBorderStyles.mediumBorderRadius),
        border: Border.all(
          color: Colors.blue[200]!,
          width: AppBorderStyles.borderWidth,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.account_balance_wallet, color: Colors.blue[600], size: 20),
          const SizedBox(width: 8),
          const Text(
            '应付金额: ',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          Text(
            '¥${totalAmount.toStringAsFixed(2)}',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w700,
              color: Colors.blue[700],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建支付方式输入区域 - 使用垂直分层布局
  Widget _buildPaymentInputSection(StoreTransferPaymentController controller) {
    return Column(
      children: [
        // 第一行：现金、刷卡、微信
        Row(
          children: [
            Expanded(
              child: _buildPaymentInputField(
                controller,
                '现金',
                controller.cashController,
                Icons.money,
                Colors.green,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildPaymentInputField(
                controller,
                '刷卡',
                controller.cardController,
                Icons.credit_card,
                Colors.blue,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildPaymentInputField(
                controller,
                '微信',
                controller.wechatController,
                Icons.chat,
                Colors.green,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        // 第二行：支付宝、抹零金额、找零金额
        Row(
          children: [
            Expanded(
              child: _buildPaymentInputField(
                controller,
                '支付宝',
                controller.alipayController,
                Icons.account_balance,
                Colors.blue,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(child: _buildDiscountInputField(controller)),
            const SizedBox(width: 16),
            Expanded(
              child: _buildCalculationResultField(
                '找零金额',
                controller.changeAmount,
                Icons.change_circle,
                Colors.purple,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建单个支付方式输入框
  Widget _buildPaymentInputField(
    StoreTransferPaymentController controller,
    String label,
    TextEditingController textController,
    IconData icon,
    Color iconColor,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 16, color: iconColor),
            const SizedBox(width: 4),
            Text(
              label,
              style: const TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
          ],
        ),
        const SizedBox(height: 6),
        Container(
          height: 32,
          decoration: AppBorderStyles.standardBoxDecoration,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
            child: TextField(
              controller: textController,
              keyboardType: const TextInputType.numberWithOptions(
                decimal: true,
              ),
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
              ],
              decoration: const InputDecoration(
                hintText: '¥0.00',
                hintStyle: TextStyle(fontSize: 13, color: Colors.grey),
                border: InputBorder.none,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                disabledBorder: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(vertical: 8),
                isDense: true,
              ),
              style: const TextStyle(fontSize: 13),
              onChanged: (value) => controller.calculateTotals(),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建抹零金额输入框（带自动计算提示）
  Widget _buildDiscountInputField(StoreTransferPaymentController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Icon(Icons.discount, size: 16, color: Colors.orange),
            const SizedBox(width: 4),
            const Text(
              '抹零金额',
              style: TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
            const SizedBox(width: 4),
            // 自动计算提示图标
            Obx(
              () => controller.isDiscountAutoFilled.value
                  ? Tooltip(
                      message: '自动计算的抹零金额，可手动修改',
                      child: Icon(
                        Icons.auto_awesome,
                        size: 12,
                        color: Colors.orange[600],
                      ),
                    )
                  : const SizedBox.shrink(),
            ),
          ],
        ),
        const SizedBox(height: 6),
        Obx(
          () => Container(
            height: 32,
            decoration: controller.isDiscountAutoFilled.value
                ? AppBorderStyles.standardBoxDecoration.copyWith(
                    border: Border.all(color: Colors.orange[300]!, width: 1.5),
                  )
                : AppBorderStyles.standardBoxDecoration,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
              child: TextField(
                controller: controller.discountController,
                keyboardType: const TextInputType.numberWithOptions(
                  decimal: true,
                ),
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
                ],
                decoration: InputDecoration(
                  hintText: '¥0.00',
                  hintStyle: TextStyle(
                    fontSize: 13,
                    color: controller.isDiscountAutoFilled.value
                        ? Colors.orange[300]
                        : Colors.grey,
                  ),
                  border: InputBorder.none,
                  enabledBorder: InputBorder.none,
                  focusedBorder: InputBorder.none,
                  disabledBorder: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(vertical: 8),
                  isDense: true,
                ),
                style: TextStyle(
                  fontSize: 13,
                  color: controller.isDiscountAutoFilled.value
                      ? Colors.orange[700]
                      : Colors.black87,
                ),
                onChanged: (value) {
                  // 标记为手动输入
                  if (!controller.isAutoCalculating.value) {
                    controller.isManualDiscount.value = value.isNotEmpty;
                    controller.isDiscountAutoFilled.value = false;
                  }
                  controller.calculateTotals();
                },
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建计算结果显示字段（只读）
  Widget _buildCalculationResultField(
    String label,
    RxDouble value,
    IconData icon,
    Color iconColor,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 16, color: iconColor),
            const SizedBox(width: 4),
            Text(
              label,
              style: const TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
          ],
        ),
        const SizedBox(height: 6),
        Container(
          height: 32,
          decoration: AppBorderStyles.standardBoxDecoration.copyWith(
            color: Colors.grey[50], // 只读字段使用浅灰色背景
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
            child: Obx(
              () => Container(
                alignment: Alignment.centerLeft,
                child: Text(
                  '¥${value.value.toStringAsFixed(2)}',
                  style: TextStyle(
                    fontSize: 13,
                    color: value.value >= 0 ? Colors.black87 : Colors.red[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建快速支付按钮
  Widget _buildQuickPaymentButtons(StoreTransferPaymentController controller) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
        border: Border.all(color: AppBorderStyles.borderColor, width: 0.5),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.flash_on, size: 14, color: Colors.orange[600]),
              const SizedBox(width: 4),
              const Text(
                '快速支付',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Colors.black54,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: _buildQuickPayButton(
                  '现金',
                  Icons.money,
                  Colors.green[600]!,
                  () => controller.setFullCash(),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildQuickPayButton(
                  '刷卡',
                  Icons.credit_card,
                  Colors.blue[600]!,
                  () => controller.setFullCard(),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildQuickPayButton(
                  '微信',
                  Icons.chat,
                  Colors.green[600]!,
                  () => controller.setFullWechat(),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildQuickPayButton(
                  '支付宝',
                  Icons.account_balance,
                  Colors.blue[600]!,
                  () => controller.setFullAlipay(),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildQuickPayButton(
                  '清空',
                  Icons.clear,
                  Colors.grey[600]!,
                  () => controller.clearAll(),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建单个快速支付按钮
  Widget _buildQuickPayButton(
    String label,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return SizedBox(
      height: 28,
      child: OutlinedButton.icon(
        icon: Icon(icon, size: 12),
        label: Text(label),
        onPressed: onPressed,
        style: OutlinedButton.styleFrom(
          foregroundColor: color,
          side: BorderSide(color: color, width: 1),
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
          ),
          textStyle: const TextStyle(fontSize: 11),
        ),
      ),
    );
  }

  /// 构建计算结果汇总区域
  Widget _buildCalculationResults(StoreTransferPaymentController controller) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(AppBorderStyles.mediumBorderRadius),
        border: Border.all(
          color: AppBorderStyles.borderColor,
          width: AppBorderStyles.borderWidth,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildSummaryItem(
            '总付款金额',
            controller.totalPayment,
            Colors.blue[600]!,
          ),
          Container(width: 1, height: 30, color: AppBorderStyles.borderColor),
          _buildSummaryItem(
            '实收金额',
            controller.actualAmount,
            Colors.green[600]!,
          ),
        ],
      ),
    );
  }

  /// 构建汇总项目
  Widget _buildSummaryItem(String label, RxDouble value, Color color) {
    return Column(
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 13,
            color: Colors.black54,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 4),
        Obx(
          () => Text(
            '¥${value.value.toStringAsFixed(2)}',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w700,
              color: color,
            ),
          ),
        ),
      ],
    );
  }

  /// 构建操作按钮
  ///
  /// 根据收款方案.md文档设计，包含5个按钮：
  /// 保存、打印、预览、取消、电子单
  Widget _buildActionButtons(StoreTransferPaymentController controller) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // 保存按钮（原确认收款功能）
        Obx(
          () => _buildActionButton(
            label: controller.isSaving.value ? '保存中...' : '保存',
            icon: controller.isSaving.value
                ? Icons.hourglass_empty
                : Icons.save,
            color: Colors.blue[600]!,
            onPressed:
                controller.isSaveDisabled.value || controller.isSaving.value
                ? null
                : () => _handlePaymentConfirm(controller),
            isPrimary: true,
          ),
        ),
        const SizedBox(width: 12),
        // 打印按钮
        _buildActionButton(
          label: '打印',
          icon: Icons.print,
          color: Colors.green[600]!,
          onPressed: () => _handlePrint(controller),
          isPrimary: false,
        ),
        const SizedBox(width: 12),
        // 预览按钮
        _buildActionButton(
          label: '预览',
          icon: Icons.preview,
          color: Colors.orange[600]!,
          onPressed: () => _handlePreview(controller),
          isPrimary: false,
        ),
        const SizedBox(width: 12),
        // 取消按钮
        _buildActionButton(
          label: '取消',
          icon: Icons.close,
          color: Colors.grey[600]!,
          onPressed: () => _handleCancel(controller),
          isPrimary: false,
        ),
        const SizedBox(width: 12),
        // 电子单按钮
        _buildActionButton(
          label: '电子单',
          icon: Icons.receipt_long,
          color: Colors.purple[600]!,
          onPressed: () => _handleElectronicReceipt(controller),
          isPrimary: false,
        ),
      ],
    );
  }

  /// 构建单个操作按钮
  Widget _buildActionButton({
    required String label,
    required IconData icon,
    required Color color,
    required VoidCallback? onPressed, // 改为可选参数
    required bool isPrimary,
  }) {
    return SizedBox(
      height: 32,
      width: 90, // 增加宽度避免"保存中..."文字换行
      child: isPrimary
          ? ElevatedButton.icon(
              icon: Icon(icon, size: 14),
              label: Text(label),
              onPressed: onPressed,
              style: ElevatedButton.styleFrom(
                backgroundColor: color,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(
                    AppBorderStyles.borderRadius,
                  ),
                ),
                textStyle: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            )
          : OutlinedButton.icon(
              icon: Icon(icon, size: 14),
              label: Text(label),
              onPressed: onPressed,
              style: OutlinedButton.styleFrom(
                foregroundColor: color,
                side: BorderSide(color: color, width: 1),
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(
                    AppBorderStyles.borderRadius,
                  ),
                ),
                textStyle: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
    );
  }

  /// 处理收款确认（保存按钮）
  Future<void> _handlePaymentConfirm(
    StoreTransferPaymentController controller,
  ) async {
    // 验证收款金额
    final isValid = await controller.validatePayment();
    if (!isValid) {
      return;
    }

    // 禁用保存按钮，防止重复点击
    controller.isSaving.value = true;

    try {
      // 构建收款数据
      final paymentData = controller.getPaymentData();

      // 调用回调函数
      onPaymentConfirmed(paymentData);

      // 显示保存成功提示，但不关闭对话框
      Get.snackbar(
        '保存成功',
        '收款信息已保存，调拨单状态已更新',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green[600],
        colorText: Colors.white,
        duration: const Duration(seconds: 2),
      );

      // 保存成功后重置状态并禁用保存按钮
      controller.isSaving.value = false; // 重置保存中状态
      controller.isSaveDisabled.value = true; // 禁用保存按钮
    } catch (e) {
      // 保存失败时重新启用按钮
      controller.isSaving.value = false;
      Get.snackbar(
        '保存失败',
        '收款保存失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red[600],
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );
    }
  }

  /// 处理打印功能
  void _handlePrint(StoreTransferPaymentController controller) {
    try {
      // 检查是否已保存
      if (!controller.isSaveDisabled.value) {
        Get.snackbar(
          '提示',
          '请先保存收款信息再进行打印',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.orange[600],
          colorText: Colors.white,
          duration: const Duration(seconds: 2),
        );
        return;
      }

      // 构建打印数据
      final printData = _buildPrintData(controller);

      // 调用打印服务
      _printReceipt(printData);
    } catch (e) {
      Get.snackbar(
        '打印失败',
        '打印过程中发生错误: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red[600],
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );
    }
  }

  /// 处理预览功能
  Future<void> _handlePreview(StoreTransferPaymentController controller) async {
    try {
      // 检查是否已保存
      if (!controller.isSaveDisabled.value) {
        Get.snackbar(
          '提示',
          '请先保存收款信息再进行预览',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.orange[600],
          colorText: Colors.white,
          duration: const Duration(seconds: 2),
        );
        return;
      }

      // 构建打印数据（暂时不使用，预览功能开发中）
      // final printData = _buildPrintData(controller);

      // 显示预览提示
      Get.snackbar(
        '预览功能',
        '调拨单预览功能开发中...',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.blue[600],
        colorText: Colors.white,
        duration: const Duration(seconds: 2),
      );
    } catch (e) {
      Get.snackbar(
        '预览失败',
        '预览过程中发生错误: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red[600],
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );
    }
  }

  /// 处理取消按钮
  void _handleCancel(StoreTransferPaymentController controller) {
    // 🔧 修复取消按钮逻辑：根据保存状态决定操作
    if (controller.isSaveDisabled.value) {
      // 如果已经保存成功，需要重置调拨单页面并关闭对话框
      LoggerService.d('🔄 收款已完成，准备重置调拨单页面...');

      // 先关闭收款对话框，避免Snackbar冲突
      Get.back();

      // 延迟执行重置操作，避免Snackbar冲突
      Future.delayed(const Duration(milliseconds: 100), () {
        // 显示提示信息
        Get.snackbar(
          '提示',
          '收款已完成，调拨单已保存',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.blue[600],
          colorText: Colors.white,
          duration: const Duration(seconds: 2),
        );

        // 使用传递的控制器引用进行重置
        if (formController != null) {
          try {
            formController!.resetTransferForm();

            // 然后关闭调拨单对话框，回到调拨单列表
            Get.back(result: true);

            LoggerService.d('✅ 调拨单页面已重置，已返回列表页面');
          } catch (e) {
            LoggerService.e('❌ 重置调拨单页面失败', e);
            // 如果重置失败，仍然关闭对话框
            Get.back(result: true);
          }
        } else {
          LoggerService.w('⚠️ 表单控制器引用为空，直接关闭对话框');
          Get.back(result: true);
        }
      });
    } else {
      // 如果没有保存成功，只关闭收款对话框
      LoggerService.d('🔄 收款未完成，仅关闭收款对话框');
      Get.back();
    }
  }

  /// 处理电子单功能
  Future<void> _handleElectronicReceipt(
    StoreTransferPaymentController controller,
  ) async {
    try {
      // 检查是否已保存
      if (!controller.isSaveDisabled.value) {
        Get.snackbar(
          '提示',
          '请先保存收款信息再导出电子单',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.orange[600],
          colorText: Colors.white,
          duration: const Duration(seconds: 2),
        );
        return;
      }

      // 显示电子单功能提示
      Get.snackbar(
        '电子单功能',
        '调拨单电子单导出功能开发中...',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.purple[600],
        colorText: Colors.white,
        duration: const Duration(seconds: 2),
      );
    } catch (e) {
      Get.snackbar(
        '导出失败',
        '导出过程中发生错误: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red[600],
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );
    }
  }

  /// 构建打印数据
  Map<String, dynamic> _buildPrintData(
    StoreTransferPaymentController controller,
  ) {
    // 使用真实的调拨单数据，如果没有则使用默认值
    final realTransferData =
        transferData ??
        {
          'transfer_no': 'DB${DateTime.now().millisecondsSinceEpoch}',
          'from_store_name': '源店铺',
          'to_store_name': '目标店铺',
        };

    // 使用真实的商品明细数据，如果没有则使用示例数据
    final realItemsData =
        itemsData ??
        [
          {
            'barcode': 'DEMO001',
            'name': '示例商品',
            'transfer_price': totalAmount.toString(),
          },
        ];

    return {
      'transferData': realTransferData,
      'paymentData': controller.getPaymentData(),
      'items': realItemsData,
      'totalAmount': totalAmount,
    };
  }

  /// 打印收款凭证
  Future<void> _printReceipt(Map<String, dynamic> printData) async {
    try {
      // 显示加载对话框
      Get.dialog(
        const Center(
          child: Card(
            child: Padding(
              padding: EdgeInsets.all(20),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('正在生成调拨收款凭证...'),
                ],
              ),
            ),
          ),
        ),
        barrierDismissible: false,
      );

      // 模拟打印处理
      await Future.delayed(const Duration(seconds: 1));

      // 关闭加载对话框
      if (Get.isDialogOpen == true) {
        Get.back();
      }

      Get.snackbar(
        '打印成功',
        '调拨收款凭证已生成',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green[600],
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );
    } catch (e) {
      // 确保关闭加载对话框
      if (Get.isDialogOpen == true) {
        Get.back();
      }

      Get.snackbar(
        '打印失败',
        '打印过程中发生错误: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red[600],
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );
    }
  }
}
