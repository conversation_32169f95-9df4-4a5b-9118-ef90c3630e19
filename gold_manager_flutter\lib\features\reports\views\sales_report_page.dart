import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:fl_chart/fl_chart.dart';

import '../../../core/theme/app_colors.dart';
import '../../../widgets/empty_state_widget.dart';
import '../controllers/reports_controller.dart';
import '../models/report_model.dart';

/// 销售报表页面
class SalesReportPage extends StatelessWidget {
  const SalesReportPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<ReportsController>();
    
    return Obx(() {
      final report = controller.salesReport.value;
      
      // 检查是否有数据
      if (report.items.isEmpty && report.chartData.isEmpty) {
        return const EmptyStateWidget(
          icon: Icons.bar_chart,
          title: '暂无销售数据',
          message: '所选时间范围内没有销售记录',
        );
      }
      
      return SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSummaryCards(report),
            const SizedBox(height: 24),
            _buildSalesChart(report.chartData),
            const SizedBox(height: 24),
            _buildCategoryPieChart(report.categoryData),
            const SizedBox(height: 24),
            _buildSalesTable(report.items),
          ],
        ),
      );
    });
  }
  
  /// 构建概览卡片
  Widget _buildSummaryCards(SalesReportData report) {
    return Row(
      children: [
        Expanded(
          child: _buildSummaryCard(
            title: '销售总额',
            value: '¥${NumberFormat('#,##0.00').format(report.totalAmount)}',
            icon: Icons.attach_money,
            color: AppColors.primary,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildSummaryCard(
            title: '销售数量',
            value: '${NumberFormat('#,##0').format(report.totalCount)}件',
            icon: Icons.shopping_cart,
            color: Colors.green,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildSummaryCard(
            title: '平均单价',
            value: '¥${NumberFormat('#,##0.00').format(report.averagePrice)}',
            icon: Icons.trending_up,
            color: Colors.orange,
          ),
        ),
      ],
    );
  }
  
  /// 构建单个概览卡片
  Widget _buildSummaryCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 24),
                const SizedBox(width: 8),
                Text(title, style: const TextStyle(
                  fontSize: 16, 
                  fontWeight: FontWeight.w500,
                )),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              value,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  /// 构建销售趋势图表
  Widget _buildSalesChart(List<ChartData> chartData) {
    if (chartData.isEmpty) {
      return const SizedBox.shrink();
    }
    
    // 准备图表数据
    final spots = chartData
        .map((data) => FlSpot(
              data.date.millisecondsSinceEpoch.toDouble(),
              data.value,
            ))
        .toList();
    
    // 获取最大和最小值用于Y轴范围
    double minY = 0;
    double maxY = 0;
    
    if (spots.isNotEmpty) {
      maxY = spots.map((spot) => spot.y).reduce((a, b) => a > b ? a : b) * 1.2;
    }
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '销售趋势',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 24),
            SizedBox(
              height: 300,
              child: LineChart(
                LineChartData(
                  gridData: FlGridData(
                    show: true,
                    drawVerticalLine: true,
                    getDrawingHorizontalLine: (value) {
                      return FlLine(
                        color: Colors.grey.withOpacity(0.3),
                        strokeWidth: 1,
                      );
                    },
                    getDrawingVerticalLine: (value) {
                      return FlLine(
                        color: Colors.grey.withOpacity(0.3),
                        strokeWidth: 1,
                      );
                    },
                  ),
                  titlesData: FlTitlesData(
                    show: true,
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 30,
                        getTitlesWidget: (value, meta) {
                          final date = DateTime.fromMillisecondsSinceEpoch(value.toInt());
                          return SideTitleWidget(
                            axisSide: meta.axisSide,
                            child: Text(
                              DateFormat('MM-dd').format(date),
                              style: const TextStyle(
                                color: Colors.black,
                                fontSize: 12,
                              ),
                            ),
                          );
                        },
                        interval: 24 * 60 * 60 * 1000.0 * 2, // 2天
                      ),
                    ),
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 50,
                        getTitlesWidget: (value, meta) {
                          return SideTitleWidget(
                            axisSide: meta.axisSide,
                            child: Text(
                              '¥${NumberFormat.compact().format(value)}',
                              style: const TextStyle(
                                color: Colors.black,
                                fontSize: 12,
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                    topTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    rightTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                  ),
                  borderData: FlBorderData(
                    show: true,
                    border: Border.all(color: Colors.grey.withOpacity(0.3)),
                  ),
                  minX: spots.first.x,
                  maxX: spots.last.x,
                  minY: minY,
                  maxY: maxY,
                  lineBarsData: [
                    LineChartBarData(
                      spots: spots,
                      isCurved: true,
                      color: AppColors.primary,
                      barWidth: 3,
                      isStrokeCapRound: true,
                      dotData: FlDotData(show: false),
                      belowBarData: BarAreaData(
                        show: true,
                        color: AppColors.primary.withOpacity(0.2),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  /// 构建分类饼图
  Widget _buildCategoryPieChart(List<CategoryData> categoryData) {
    if (categoryData.isEmpty) {
      return const SizedBox.shrink();
    }
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '销售分类占比',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 24),
            Row(
              children: [
                SizedBox(
                  height: 250,
                  width: 250,
                  child: PieChart(
                    PieChartData(
                      sectionsSpace: 0,
                      centerSpaceRadius: 40,
                      sections: _getPieChartSections(categoryData),
                    ),
                  ),
                ),
                const SizedBox(width: 24),
                Expanded(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: categoryData.map((category) {
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 8.0),
                        child: _buildCategoryIndicator(
                          color: _getCategoryColor(category.name, categoryData.indexOf(category)),
                          text: category.name,
                          value: '¥${NumberFormat('#,##0.00').format(category.value)}',
                          percentage: category.percentage,
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  /// 获取饼图数据
  List<PieChartSectionData> _getPieChartSections(List<CategoryData> categoryData) {
    return categoryData.asMap().entries.map((entry) {
      final index = entry.key;
      final category = entry.value;
      
      return PieChartSectionData(
        color: _getCategoryColor(category.name, index),
        value: category.percentage,
        title: '${category.percentage.toStringAsFixed(1)}%',
        radius: 100,
        titleStyle: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      );
    }).toList();
  }
  
  /// 根据分类获取颜色
  Color _getCategoryColor(String category, int index) {
    final colors = [
      AppColors.primary,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.teal,
    ];
    
    if (category == '黄金首饰') {
      return Colors.amber;
    } else if (category == '银饰') {
      return Colors.grey;
    } else if (category == '金银混合') {
      return Colors.blueGrey;
    } else {
      return colors[index % colors.length];
    }
  }
  
  /// 构建分类指示器
  Widget _buildCategoryIndicator({
    required Color color,
    required String text,
    required String value,
    required double percentage,
  }) {
    return Row(
      children: [
        Container(
          width: 16,
          height: 16,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            text,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(width: 8),
        Text(
          '${percentage.toStringAsFixed(1)}%',
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.grey,
          ),
        ),
      ],
    );
  }
  
  /// 构建销售明细表格
  Widget _buildSalesTable(List<SalesReportItem> items) {
    if (items.isEmpty) {
      return const SizedBox.shrink();
    }
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '销售明细',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                headingTextStyle: const TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
                dataTextStyle: const TextStyle(
                  color: Colors.black87,
                ),
                columnSpacing: 24,
                columns: const [
                  DataColumn(label: Text('日期')),
                  DataColumn(label: Text('订单号')),
                  DataColumn(label: Text('商品名称')),
                  DataColumn(label: Text('分类')),
                  DataColumn(label: Text('数量')),
                  DataColumn(label: Text('金重(g)')),
                  DataColumn(label: Text('银重(g)')),
                  DataColumn(label: Text('金额(元)')),
                  DataColumn(label: Text('门店')),
                ],
                rows: items.map((item) {
                  return DataRow(
                    cells: [
                      DataCell(Text(DateFormat('yyyy-MM-dd').format(item.date))),
                      DataCell(Text(item.orderNo)),
                      DataCell(Text(item.itemName)),
                      DataCell(Text(item.categoryName)),
                      DataCell(Text('${item.quantity}')),
                      DataCell(Text(item.goldWeight.toStringAsFixed(2))),
                      DataCell(Text(item.silverWeight.toStringAsFixed(2))),
                      DataCell(Text('¥${item.amount.toStringAsFixed(2)}')),
                      DataCell(Text(item.storeName)),
                    ],
                  );
                }).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }
} 