import 'package:flutter/material.dart';
import 'package:gold_manager_flutter/models/jewelry/jewelry.dart';

/// 库存调拨单模型
class StoreTransfer {
  final int id;
  final String transferNo;
  final int fromStoreId;
  final int toStoreId;
  final String? fromStoreName;
  final String? toStoreName;
  final int operatorId;
  final String? operatorName;
  final double totalAmount;
  final int status; // 0=草稿, 1=待审核, 2=已通过, 3=已拒绝
  final DateTime createTime;
  final DateTime? auditTime;
  final int? auditUserId;
  final String? auditUserName;
  final String? remark;
  final String? auditRemark;
  final List<StoreTransferItem> items;

  StoreTransfer({
    required this.id,
    required this.transferNo,
    required this.fromStoreId,
    required this.toStoreId,
    this.fromStoreName,
    this.toStoreName,
    required this.operatorId,
    this.operatorName,
    required this.totalAmount,
    required this.status,
    required this.createTime,
    this.auditTime,
    this.auditUserId,
    this.auditUserName,
    this.remark,
    this.auditRemark,
    required this.items,
  });

  factory StoreTransfer.fromJson(Map<String, dynamic> json) {
    return StoreTransfer(
      id: json['id'] ?? 0,
      transferNo: json['transfer_no'] ?? '',
      fromStoreId: json['from_store_id'] ?? 0,
      toStoreId: json['to_store_id'] ?? 0,
      fromStoreName: json['from_store_name'],
      toStoreName: json['to_store_name'],
      operatorId: json['operator_id'] ?? 0,
      operatorName: json['operator_name'],
      totalAmount: _parseDouble(json['total_amount']),
      status: json['status'] ?? 0,
      createTime: DateTime.parse(json['create_time'] ?? DateTime.now().toIso8601String()),
      auditTime: json['audit_time'] != null ? DateTime.parse(json['audit_time']) : null,
      auditUserId: json['audit_user_id'],
      auditUserName: json['audit_user_name'],
      remark: json['remark'],
      auditRemark: json['audit_remark'],
      items: (json['items'] as List<dynamic>?)
          ?.map((item) => StoreTransferItem.fromJson(item))
          .toList() ?? [],
    );
  }

  /// 安全解析double类型数据，支持字符串和数字类型
  static double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      try {
        return double.parse(value);
      } catch (e) {
        print('⚠️ 解析double失败: $value, 错误: $e');
        return 0.0;
      }
    }
    return 0.0;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'transfer_no': transferNo,
      'from_store_id': fromStoreId,
      'to_store_id': toStoreId,
      'from_store_name': fromStoreName,
      'to_store_name': toStoreName,
      'operator_id': operatorId,
      'operator_name': operatorName,
      'total_amount': totalAmount,
      'status': status,
      'create_time': createTime.toIso8601String(),
      'audit_time': auditTime?.toIso8601String(),
      'audit_user_id': auditUserId,
      'audit_user_name': auditUserName,
      'remark': remark,
      'audit_remark': auditRemark,
      'items': items.map((item) => item.toJson()).toList(),
    };
  }

  StoreTransfer copyWith({
    int? id,
    String? transferNo,
    int? fromStoreId,
    int? toStoreId,
    String? fromStoreName,
    String? toStoreName,
    int? operatorId,
    String? operatorName,
    double? totalAmount,
    int? status,
    DateTime? createTime,
    DateTime? auditTime,
    int? auditUserId,
    String? auditUserName,
    String? remark,
    String? auditRemark,
    List<StoreTransferItem>? items,
  }) {
    return StoreTransfer(
      id: id ?? this.id,
      transferNo: transferNo ?? this.transferNo,
      fromStoreId: fromStoreId ?? this.fromStoreId,
      toStoreId: toStoreId ?? this.toStoreId,
      fromStoreName: fromStoreName ?? this.fromStoreName,
      toStoreName: toStoreName ?? this.toStoreName,
      operatorId: operatorId ?? this.operatorId,
      operatorName: operatorName ?? this.operatorName,
      totalAmount: totalAmount ?? this.totalAmount,
      status: status ?? this.status,
      createTime: createTime ?? this.createTime,
      auditTime: auditTime ?? this.auditTime,
      auditUserId: auditUserId ?? this.auditUserId,
      auditUserName: auditUserName ?? this.auditUserName,
      remark: remark ?? this.remark,
      auditRemark: auditRemark ?? this.auditRemark,
      items: items ?? this.items,
    );
  }

  /// 获取状态文本
  String get statusText {
    switch (status) {
      case 0:
        return '草稿';
      case 1:
        return '待审核';
      case 2:
        return '已通过';
      case 3:
        return '已拒绝';
      default:
        return '未知';
    }
  }

  /// 获取状态颜色
  Color get statusColor {
    switch (status) {
      case 0:
        return Colors.grey;
      case 1:
        return Colors.orange;
      case 2:
        return Colors.green;
      case 3:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}

/// 库存调拨明细模型
class StoreTransferItem {
  final int id;
  final int transferId;
  final int jewelryId;
  final String barcode;
  final double transferPrice;
  final String transferType; // 'normal'=正向调拨, 'return'=退货调拨
  final Jewelry? jewelry;

  StoreTransferItem({
    required this.id,
    required this.transferId,
    required this.jewelryId,
    required this.barcode,
    required this.transferPrice,
    required this.transferType,
    this.jewelry,
  });

  factory StoreTransferItem.fromJson(Map<String, dynamic> json) {
    return StoreTransferItem(
      id: json['id'] ?? 0,
      transferId: json['transfer_id'] ?? 0,
      jewelryId: json['jewelry_id'] ?? 0,
      barcode: json['barcode'] ?? '',
      transferPrice: StoreTransfer._parseDouble(json['transfer_price']),
      transferType: json['transfer_type'] ?? 'normal',
      jewelry: json['jewelry'] != null ? Jewelry.fromJson(json['jewelry']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'transfer_id': transferId,
      'jewelry_id': jewelryId,
      'barcode': barcode,
      'transfer_price': transferPrice,
      'transfer_type': transferType,
      'jewelry': jewelry?.toJson(),
    };
  }

  StoreTransferItem copyWith({
    int? id,
    int? transferId,
    int? jewelryId,
    String? barcode,
    double? transferPrice,
    String? transferType,
    Jewelry? jewelry,
  }) {
    return StoreTransferItem(
      id: id ?? this.id,
      transferId: transferId ?? this.transferId,
      jewelryId: jewelryId ?? this.jewelryId,
      barcode: barcode ?? this.barcode,
      transferPrice: transferPrice ?? this.transferPrice,
      transferType: transferType ?? this.transferType,
      jewelry: jewelry ?? this.jewelry,
    );
  }

  /// 获取调拨类型文本
  String get transferTypeText {
    switch (transferType) {
      case 'normal':
        return '正向';
      case 'return':
        return '退货';
      default:
        return '未知';
    }
  }

  /// 获取调拨类型颜色
  Color get transferTypeColor {
    switch (transferType) {
      case 'normal':
        return Colors.blue;
      case 'return':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }
}

/// 调拨验证结果模型
class TransferValidationResult {
  final bool isValid;
  final String message;
  final String transferType;
  final double suggestedPrice;
  final Jewelry? jewelry;

  TransferValidationResult({
    required this.isValid,
    required this.message,
    required this.transferType,
    required this.suggestedPrice,
    this.jewelry,
  });

  factory TransferValidationResult.fromJson(Map<String, dynamic> json) {
    return TransferValidationResult(
      isValid: json['is_valid'] ?? false,
      message: json['message'] ?? '',
      transferType: json['transfer_type'] ?? 'normal',
      suggestedPrice: StoreTransfer._parseDouble(json['suggested_price']),
      jewelry: json['jewelry'] != null ? Jewelry.fromJson(json['jewelry']) : null,
    );
  }
}
