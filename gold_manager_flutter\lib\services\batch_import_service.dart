import 'dart:io';
import 'dart:convert';
import 'package:get/get.dart';
import 'package:file_picker/file_picker.dart';
import 'package:excel/excel.dart';
import 'package:csv/csv.dart';
import '../models/stock/batch_import_result.dart';
import '../core/utils/logger.dart';

/// 批量导入服务
class BatchImportService extends GetxService {
  
  /// 选择文件
  Future<File?> pickFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['xlsx', 'xls', 'csv'],
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = File(result.files.first.path!);
        LoggerService.d('选择文件: ${file.path}');
        return file;
      }

      return null;
    } catch (e) {
      LoggerService.e('选择文件失败', e);
      return null;
    }
  }

  /// 解析Excel文件
  Future<BatchImportPreviewData?> parseExcelFile(File file) async {
    try {
      final bytes = await file.readAsBytes();
      final excel = Excel.decodeBytes(bytes);
      
      // 获取第一个工作表
      final sheet = excel.tables.values.first;
      if (sheet.rows.isEmpty) {
        throw Exception('Excel文件为空或格式不正确');
      }

      // 提取表头
      final headerRow = sheet.rows.first;
      final headers = headerRow.map((cell) => cell?.value?.toString() ?? '').toList();

      // 提取数据行
      final dataRows = <List<String>>[];
      for (int i = 1; i < sheet.rows.length; i++) {
        final row = sheet.rows[i];
        final rowData = row.map((cell) => cell?.value?.toString() ?? '').toList();
        
        // 跳过空行
        if (rowData.any((cell) => cell.isNotEmpty)) {
          dataRows.add(rowData);
        }
      }

      // 生成字段映射
      final fieldMapping = _generateFieldMapping(headers);

      // 验证数据
      final validationResults = _validateData(dataRows, fieldMapping, headers);

      // 检测重复条码
      final duplicateBarcodeResult = _detectDuplicateBarcodes(dataRows, fieldMapping, headers);

      return BatchImportPreviewData(
        headers: headers,
        rows: dataRows,
        fieldMapping: fieldMapping,
        validationResults: validationResults,
        duplicateBarcodeResult: duplicateBarcodeResult,
      );
    } catch (e) {
      LoggerService.e('解析Excel文件失败', e);
      return null;
    }
  }

  /// 解析CSV文件
  Future<BatchImportPreviewData?> parseCsvFile(File file) async {
    try {
      final content = await file.readAsString(encoding: utf8);
      final csvData = const CsvToListConverter().convert(content);
      
      if (csvData.isEmpty) {
        throw Exception('CSV文件为空');
      }

      // 提取表头
      final headers = csvData.first.map((cell) => cell.toString()).toList();

      // 提取数据行
      final dataRows = <List<String>>[];
      for (int i = 1; i < csvData.length; i++) {
        final row = csvData[i];
        final rowData = row.map((cell) => cell.toString()).toList();
        
        // 跳过空行
        if (rowData.any((cell) => cell.isNotEmpty)) {
          dataRows.add(rowData);
        }
      }

      // 生成字段映射
      final fieldMapping = _generateFieldMapping(headers);

      // 验证数据
      final validationResults = _validateData(dataRows, fieldMapping, headers);

      // 检测重复条码
      final duplicateBarcodeResult = _detectDuplicateBarcodes(dataRows, fieldMapping, headers);

      return BatchImportPreviewData(
        headers: headers,
        rows: dataRows,
        fieldMapping: fieldMapping,
        validationResults: validationResults,
        duplicateBarcodeResult: duplicateBarcodeResult,
      );
    } catch (e) {
      LoggerService.e('解析CSV文件失败', e);
      return null;
    }
  }

  /// 生成字段映射
  Map<String, String> _generateFieldMapping(List<String> headers) {
    final mapping = <String, String>{};

    for (final header in headers) {
      final cleanHeader = header.trim();

      // 精确匹配优先
      if (StockInImportFieldMapping.defaultMapping.containsKey(cleanHeader)) {
        mapping[cleanHeader] = StockInImportFieldMapping.defaultMapping[cleanHeader]!;
        continue;
      }

      // 模糊匹配（去除空格、大小写不敏感）
      for (final entry in StockInImportFieldMapping.defaultMapping.entries) {
        final mappingKey = entry.key.replaceAll(' ', '').toLowerCase();
        final headerKey = cleanHeader.replaceAll(' ', '').toLowerCase();

        if (mappingKey == headerKey ||
            mappingKey.contains(headerKey) ||
            headerKey.contains(mappingKey)) {
          mapping[cleanHeader] = entry.value;
          break;
        }
      }

      // 特殊处理常见的变体
      _handleSpecialMappings(cleanHeader, mapping);
    }

    return mapping;
  }

  /// 处理特殊字段映射
  void _handleSpecialMappings(String header, Map<String, String> mapping) {
    final lowerHeader = header.toLowerCase();

    // 商品名称的各种变体
    if (lowerHeader.contains('名称') || lowerHeader.contains('商品') ||
        lowerHeader.contains('name') || lowerHeader.contains('产品')) {
      mapping[header] = 'name';
    }
    // 条码的各种变体
    else if (lowerHeader.contains('条码') || lowerHeader.contains('barcode') ||
             lowerHeader.contains('编码') || lowerHeader.contains('code')) {
      mapping[header] = 'barcode';
    }
    // 类别的各种变体
    else if (lowerHeader.contains('类别') || lowerHeader.contains('分类') ||
             lowerHeader.contains('category') || lowerHeader.contains('种类')) {
      mapping[header] = 'category';
    }
    // 金重的各种变体
    else if (lowerHeader.contains('金重') || lowerHeader.contains('gold')) {
      mapping[header] = 'goldWeight';
    }
    // 银重的各种变体
    else if (lowerHeader.contains('银重') || lowerHeader.contains('silver')) {
      mapping[header] = 'silverWeight';
    }
    // 工费方式的各种变体（优先匹配，避免与工费混淆）
    else if (lowerHeader.contains('工费方式') || lowerHeader.contains('计费方式') ||
             lowerHeader.contains('工费类型') || lowerHeader.contains('计算方式') ||
             lowerHeader.contains('worktype') || lowerHeader.contains('work_type')) {
      mapping[header] = 'workType';
    }
    // 工费的各种变体
    else if (lowerHeader.contains('工费') || lowerHeader.contains('labor') ||
             lowerHeader.contains('加工费')) {
      mapping[header] = 'laborCost';
    }
    // 其他费用的各种变体
    else if (lowerHeader.contains('其他费用') || lowerHeader.contains('other') ||
             lowerHeader.contains('杂费')) {
      mapping[header] = 'otherCost';
    }
    // 总金额的各种变体
    else if (lowerHeader.contains('总金额') || lowerHeader.contains('金额') ||
             lowerHeader.contains('amount') || lowerHeader.contains('价格') ||
             lowerHeader.contains('总价')) {
      mapping[header] = 'amount';
    }
  }

  /// 验证数据
  List<BatchImportValidationResult> _validateData(
    List<List<String>> rows,
    Map<String, String> fieldMapping,
    List<String> headers,
  ) {
    final results = <BatchImportValidationResult>[];

    for (int i = 0; i < rows.length; i++) {
      final row = rows[i];
      final errors = <String>[];
      final warnings = <String>[];

      // 检查是否为空行
      if (row.every((cell) => cell.trim().isEmpty)) {
        errors.add('空行数据');
        results.add(BatchImportValidationResult(
          rowIndex: i + 1,
          isValid: false,
          errors: errors,
          warnings: warnings,
        ));
        continue;
      }

      // 验证必填字段
      for (final entry in StockInImportFieldMapping.requiredFields.entries) {
        final fieldName = entry.key;
        final fieldLabel = entry.value;

        // 查找对应的列索引
        final headerIndex = _findHeaderIndex(fieldMapping, fieldName, headers);
        if (headerIndex == -1) {
          warnings.add('未找到字段: $fieldLabel，将使用默认值');
          continue;
        }

        // 检查数据是否为空
        if (headerIndex >= row.length || row[headerIndex].trim().isEmpty) {
          if (fieldName == 'name') {
            errors.add('$fieldLabel 不能为空');
          } else {
            warnings.add('$fieldLabel 为空，将使用默认值');
          }
        }
      }

      // 验证数值字段
      _validateNumericFields(row, fieldMapping, headers, errors, warnings);

      // 验证业务逻辑
      _validateBusinessLogic(row, fieldMapping, headers, errors, warnings);

      results.add(BatchImportValidationResult(
        rowIndex: i + 1,
        isValid: errors.isEmpty,
        errors: errors,
        warnings: warnings,
      ));
    }

    return results;
  }

  /// 验证数值字段
  void _validateNumericFields(
    List<String> row,
    Map<String, String> fieldMapping,
    List<String> headers,
    List<String> errors,
    List<String> warnings,
  ) {
    final numericFields = [
      'goldWeight', 'silverWeight', 'goldPrice', 'silverPrice',
      'laborCost', 'platingCost', 'wholesaleWorkPrice',
      'retailWorkPrice', 'pieceWorkPrice', 'otherCost', 'amount'
    ];

    for (final fieldName in numericFields) {
      final headerIndex = _findHeaderIndex(fieldMapping, fieldName, headers);
      if (headerIndex == -1 || headerIndex >= row.length) continue;

      final value = row[headerIndex].trim();
      if (value.isEmpty) continue;

      final numValue = double.tryParse(value);
      if (numValue == null) {
        errors.add('${_getFieldLabel(fieldName)} 必须是数字，当前值: "$value"');
      } else if (numValue < 0) {
        errors.add('${_getFieldLabel(fieldName)} 不能为负数');
      }
    }
  }

  /// 验证业务逻辑
  void _validateBusinessLogic(
    List<String> row,
    Map<String, String> fieldMapping,
    List<String> headers,
    List<String> errors,
    List<String> warnings,
  ) {
    // 获取金重和银重
    final goldWeightIndex = _findHeaderIndex(fieldMapping, 'goldWeight', headers);
    final silverWeightIndex = _findHeaderIndex(fieldMapping, 'silverWeight', headers);

    double goldWeight = 0.0;
    double silverWeight = 0.0;

    if (goldWeightIndex != -1 && goldWeightIndex < row.length) {
      goldWeight = double.tryParse(row[goldWeightIndex].trim()) ?? 0.0;
    }

    if (silverWeightIndex != -1 && silverWeightIndex < row.length) {
      silverWeight = double.tryParse(row[silverWeightIndex].trim()) ?? 0.0;
    }

    // 验证总重量
    if (goldWeight <= 0 && silverWeight <= 0) {
      warnings.add('金重和银重都为0，请确认是否正确');
    }

    // 验证金价和银价
    if (goldWeight > 0) {
      final goldPriceIndex = _findHeaderIndex(fieldMapping, 'goldPrice', headers);
      if (goldPriceIndex != -1 && goldPriceIndex < row.length) {
        final goldPrice = double.tryParse(row[goldPriceIndex].trim()) ?? 0.0;
        if (goldPrice <= 0) {
          warnings.add('金重大于0时，建议设置金价');
        }
      }
    }

    if (silverWeight > 0) {
      final silverPriceIndex = _findHeaderIndex(fieldMapping, 'silverPrice', headers);
      if (silverPriceIndex != -1 && silverPriceIndex < row.length) {
        final silverPrice = double.tryParse(row[silverPriceIndex].trim()) ?? 0.0;
        if (silverPrice <= 0) {
          warnings.add('银重大于0时，建议设置银价');
        }
      }
    }
  }

  /// 查找表头索引
  int _findHeaderIndex(Map<String, String> fieldMapping, String fieldName, List<String> headers) {
    for (final entry in fieldMapping.entries) {
      if (entry.value == fieldName) {
        // 在headers中查找对应的索引
        return headers.indexOf(entry.key);
      }
    }
    return -1;
  }

  /// 检测重复条码
  DuplicateBarcodeResult _detectDuplicateBarcodes(
    List<List<String>> rows,
    Map<String, String> fieldMapping,
    List<String> headers,
  ) {
    // 查找条码列的索引
    final barcodeIndex = _findHeaderIndex(fieldMapping, 'barcode', headers);

    // 如果没有条码列，返回空结果
    if (barcodeIndex == -1) {
      return DuplicateBarcodeResult(
        duplicateBarcodes: [],
        duplicateRows: [],
        barcodeToRows: {},
        uniqueRows: List.generate(rows.length, (index) => index + 1),
      );
    }

    // 条码到行号的映射
    final Map<String, List<int>> barcodeToRows = {};

    // 遍历所有数据行
    for (int i = 0; i < rows.length; i++) {
      final row = rows[i];

      // 获取条码值（如果索引超出范围或为空，跳过）
      if (barcodeIndex >= row.length) continue;

      final barcode = row[barcodeIndex].trim();
      if (barcode.isEmpty) continue;

      // 记录条码对应的行号（从1开始）
      final rowIndex = i + 1;
      if (barcodeToRows.containsKey(barcode)) {
        barcodeToRows[barcode]!.add(rowIndex);
      } else {
        barcodeToRows[barcode] = [rowIndex];
      }
    }

    // 找出重复的条码
    final duplicateBarcodes = <String>[];
    final duplicateRows = <int>[];
    final uniqueRows = <int>[];

    for (final entry in barcodeToRows.entries) {
      final barcode = entry.key;
      final rowIndexes = entry.value;

      if (rowIndexes.length > 1) {
        // 有重复条码
        duplicateBarcodes.add(barcode);
        // 除了第一次出现的行，其他都是重复行
        for (int i = 1; i < rowIndexes.length; i++) {
          duplicateRows.add(rowIndexes[i]);
        }
        // 第一次出现的行是唯一行
        uniqueRows.add(rowIndexes.first);
      } else {
        // 唯一条码
        uniqueRows.add(rowIndexes.first);
      }
    }

    // 添加没有条码的行到唯一行列表
    for (int i = 0; i < rows.length; i++) {
      final rowIndex = i + 1;
      final row = rows[i];

      if (barcodeIndex >= row.length || row[barcodeIndex].trim().isEmpty) {
        uniqueRows.add(rowIndex);
      }
    }

    // 排序
    duplicateRows.sort();
    uniqueRows.sort();

    return DuplicateBarcodeResult(
      duplicateBarcodes: duplicateBarcodes,
      duplicateRows: duplicateRows,
      barcodeToRows: barcodeToRows,
      uniqueRows: uniqueRows,
    );
  }

  /// 获取字段标签
  String _getFieldLabel(String fieldName) {
    return StockInImportFieldMapping.allFields[fieldName] ?? fieldName;
  }

  /// 执行导入
  Future<BatchImportResult> executeImport(
    BatchImportPreviewData previewData,
    Function(List<Map<String, dynamic>>) importCallback,
  ) async {
    final startTime = DateTime.now();
    final successData = <Map<String, dynamic>>[];
    final errors = <BatchImportError>[];

    try {
      for (int i = 0; i < previewData.rows.length; i++) {
        final row = previewData.rows[i];
        final validationResult = previewData.validationResults[i];
        final rowIndex = i + 1;

        // 跳过重复条码行（只保留第一次出现的）
        if (previewData.duplicateBarcodeResult.isRowDuplicate(rowIndex)) {
          errors.add(BatchImportError(
            rowIndex: rowIndex,
            rowData: _convertRowToMap(row, previewData.headers),
            errorMessage: '重复条码，已跳过导入',
            errorType: BatchImportErrorType.businessError,
          ));
          continue;
        }

        if (!validationResult.isValid) {
          errors.add(BatchImportError(
            rowIndex: rowIndex,
            rowData: _convertRowToMap(row, previewData.headers),
            errorMessage: validationResult.errors.join('; '),
            errorType: BatchImportErrorType.validationError,
          ));
          continue;
        }

        try {
          // 转换为系统数据格式
          final itemData = _convertToStockInItem(row, previewData.fieldMapping, previewData.headers);
          successData.add(itemData);
        } catch (e) {
          errors.add(BatchImportError(
            rowIndex: rowIndex,
            rowData: _convertRowToMap(row, previewData.headers),
            errorMessage: e.toString(),
            errorType: BatchImportErrorType.systemError,
          ));
        }
      }
      
      // 执行导入回调
      if (successData.isNotEmpty) {
        await importCallback(successData);
      }
      
    } catch (e) {
      LoggerService.e('执行导入失败', e);
    }
    
    final endTime = DateTime.now();
    
    return BatchImportResult(
      totalCount: previewData.rows.length,
      successCount: successData.length,
      failureCount: errors.length,
      successData: successData,
      errors: errors,
      startTime: startTime,
      endTime: endTime,
    );
  }

  /// 转换行数据为Map
  Map<String, dynamic> _convertRowToMap(List<String> row, List<String> headers) {
    final map = <String, dynamic>{};
    for (int i = 0; i < headers.length && i < row.length; i++) {
      map[headers[i]] = row[i];
    }
    return map;
  }

  /// 转换为入库单商品数据
  Map<String, dynamic> _convertToStockInItem(
    List<String> row,
    Map<String, String> fieldMapping,
    List<String> headers,
  ) {
    final item = <String, dynamic>{};

    for (int i = 0; i < headers.length && i < row.length; i++) {
      final header = headers[i];
      final fieldName = fieldMapping[header];
      if (fieldName != null) {
        final value = row[i].trim();

        // 根据字段类型转换数据
        switch (fieldName) {
          // 数值字段
          case 'goldWeight':
          case 'silverWeight':
          case 'goldPrice':
          case 'silverPrice':
          case 'laborCost':
          case 'platingCost':
          case 'wholesaleWorkPrice':
          case 'retailWorkPrice':
          case 'pieceWorkPrice':
          case 'otherCost':
          case 'amount':
            item[fieldName] = double.tryParse(value) ?? 0.0;
            break;
          // 整数字段（分类ID）
          case 'categoryId':
            item[fieldName] = int.tryParse(value) ?? 0;
            break;
          // 工费方式字段（特殊处理：文本转数字）
          case 'workType':
            item[fieldName] = _convertWorkTypeToInt(value);
            break;
          // 字符串字段
          default:
            item[fieldName] = value.isEmpty ? null : value;
        }
      }
    }

    // 设置默认值
    _setDefaultValues(item);

    return item;
  }

  /// 转换工费方式文本为数字
  int _convertWorkTypeToInt(String value) {
    final lowerValue = value.toLowerCase().trim();

    // 按克计算
    if (lowerValue.contains('按克') || lowerValue.contains('克') ||
        lowerValue.contains('weight') || lowerValue.contains('gram') ||
        lowerValue == '0') {
      return 0;
    }
    // 按件计算
    else if (lowerValue.contains('按件') || lowerValue.contains('件') ||
             lowerValue.contains('piece') || lowerValue.contains('item') ||
             lowerValue == '1') {
      return 1;
    }
    // 固定费用
    else if (lowerValue.contains('固定') || lowerValue.contains('定额') ||
             lowerValue.contains('fixed') || lowerValue == '2') {
      return 2;
    }
    // 如果是纯数字，直接转换
    else {
      final intValue = int.tryParse(value);
      if (intValue != null && intValue >= 0 && intValue <= 2) {
        return intValue;
      }
    }

    // 默认按克计算
    return 0;
  }

  /// 设置默认值
  void _setDefaultValues(Map<String, dynamic> item) {
    // 生成条码（如果没有提供）
    if (item['barcode'] == null || item['barcode'].toString().isEmpty) {
      item['barcode'] = 'AUTO_${DateTime.now().millisecondsSinceEpoch}';
    }

    // 设置默认分类（如果没有提供）
    if (item['category'] == null || item['category'].toString().isEmpty) {
      item['category'] = '未分类';
    }

    // 设置默认工费方式（按克计算）
    if (item['workType'] == null) {
      item['workType'] = 0;
    }

    // 确保数值字段不为null
    final numericFields = [
      'goldWeight', 'silverWeight', 'goldPrice', 'silverPrice',
      'laborCost', 'platingCost', 'wholesaleWorkPrice',
      'retailWorkPrice', 'pieceWorkPrice', 'otherCost', 'amount'
    ];

    for (final field in numericFields) {
      if (item[field] == null) {
        item[field] = 0.0;
      }
    }
  }
}
