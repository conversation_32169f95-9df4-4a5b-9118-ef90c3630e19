import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/utils/logger.dart';
import '../../../models/stock/stock_in.dart';
import '../../../models/stock/stock_in_item.dart';
import '../../../models/common/document_status.dart';
import '../../../core/widgets/responsive_layout.dart';
import '../../../services/print_service.dart';

/// 入库单详情查看对话框
class StockInDetailDialog extends StatefulWidget {
  final StockIn stockIn;

  const StockInDetailDialog({
    super.key,
    required this.stockIn,
  });

  @override
  State<StockInDetailDialog> createState() => _StockInDetailDialogState();
}

class _StockInDetailDialogState extends State<StockInDetailDialog> {
  bool _isPrintingInProgress = false;

  @override
  void dispose() {
    // 确保在页面销毁时清理所有状态
    LoggerService.d('🗑️ StockInDetailDialog 正在销毁，清理状态');
    super.dispose();
  }

  /// 安全关闭对话框
  void _safeCloseDialog() {
    try {
      LoggerService.d('🔙 安全关闭入库单详情对话框');

      // 如果正在打印，先取消打印操作
      if (_isPrintingInProgress) {
        _isPrintingInProgress = false;
        // 如果有打印加载对话框，先关闭它
        if (Get.isDialogOpen == true) {
          Get.back();
        }
      }

      // 关闭详情对话框
      Get.back();
    } catch (e) {
      LoggerService.e('❌ 关闭对话框时发生错误', e);
      // 强制关闭
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: const EdgeInsets.all(16),
      child: Container(
        constraints: const BoxConstraints(
          maxWidth: 1200,
          maxHeight: 800,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(context),
            const Divider(height: 1),
            Expanded(
              child: _buildContent(context),
            ),
            const Divider(height: 1),
            _buildFooter(context),
          ],
        ),
      ),
    );
  }

  /// 构建对话框头部
  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          const Icon(
            Icons.visibility,
            color: AppTheme.primaryColor,
            size: 24,
          ),
          const SizedBox(width: 12),
          const Text(
            '入库单详情',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const Spacer(),
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: _safeCloseDialog,
            tooltip: '关闭',
          ),
        ],
      ),
    );
  }

  /// 构建主要内容区域
  Widget _buildContent(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildBasicInfo(),
          const SizedBox(height: 24),
          _buildItemsList(),
          const SizedBox(height: 24),
          _buildSummaryInfo(),
        ],
      ),
    );
  }

  /// 构建基本信息卡片
  Widget _buildBasicInfo() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
                SizedBox(width: 8),
                Text(
                  '基本信息',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ScreenTypeLayout(
              mobile: _buildBasicInfoMobile(),
              tablet: _buildBasicInfoDesktop(),
              desktop: _buildBasicInfoDesktop(),
            ),
          ],
        ),
      ),
    );
  }

  /// 移动端基本信息布局
  Widget _buildBasicInfoMobile() {
    return Column(
      children: [
        _buildInfoRow('入库单号', widget.stockIn.stockInNo, isHighlight: true),
        _buildInfoRow('门店', widget.stockIn.store?.name ?? '未知门店'),
        _buildInfoRow('入库时间', _formatDateTime(widget.stockIn.createTime)),
        _buildInfoRow('状态', _getStatusText(widget.stockIn.status),
                     statusColor: _getStatusColor(widget.stockIn.status)),
        if (widget.stockIn.remark?.isNotEmpty == true)
          _buildInfoRow('备注', widget.stockIn.remark!),
      ],
    );
  }

  /// 桌面端基本信息布局
  Widget _buildBasicInfoDesktop() {
    return Row(
      children: [
        Expanded(
          child: Column(
            children: [
              _buildInfoRow('入库单号', widget.stockIn.stockInNo, isHighlight: true),
              _buildInfoRow('门店', widget.stockIn.store?.name ?? '未知门店'),
            ],
          ),
        ),
        Expanded(
          child: Column(
            children: [
              _buildInfoRow('入库时间', _formatDateTime(widget.stockIn.createTime)),
              _buildInfoRow('状态', _getStatusText(widget.stockIn.status),
                           statusColor: _getStatusColor(widget.stockIn.status)),
            ],
          ),
        ),
        if (widget.stockIn.remark?.isNotEmpty == true)
          Expanded(
            child: _buildInfoRow('备注', widget.stockIn.remark!),
          ),
      ],
    );
  }

  /// 构建信息行
  Widget _buildInfoRow(String label, String value, {
    bool isHighlight = false,
    Color? statusColor,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                color: statusColor ?? (isHighlight ? AppTheme.primaryColor : Colors.black87),
                fontWeight: isHighlight ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建商品明细列表
  Widget _buildItemsList() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.inventory_2_outlined,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Text(
                  '商品明细',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '共 ${widget.stockIn.itemCount} 件',
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppTheme.primaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ScreenTypeLayout(
              mobile: _buildItemsListMobile(),
              tablet: _buildItemsTable(),
              desktop: _buildItemsTable(),
            ),
          ],
        ),
      ),
    );
  }

  /// 移动端商品列表
  Widget _buildItemsListMobile() {
    if (widget.stockIn.items?.isEmpty ?? true) {
      return _buildEmptyItems();
    }

    return Column(
      children: widget.stockIn.items!.map((item) => _buildItemCard(item)).toList(),
    );
  }

  /// 桌面端商品表格
  Widget _buildItemsTable() {
    if (widget.stockIn.items?.isEmpty ?? true) {
      return _buildEmptyItems();
    }

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          _buildTableHeader(),
          ...widget.stockIn.items!.map((item) => _buildTableRow(item)),
        ],
      ),
    );
  }

  /// 构建表格头部
  Widget _buildTableHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(8),
          topRight: Radius.circular(8),
        ),
      ),
      child: Row(
        children: [
          Expanded(flex: 2, child: _buildHeaderCell('条码')),
          Expanded(flex: 3, child: _buildHeaderCell('商品名称')),
          Expanded(flex: 2, child: _buildHeaderCell('分类')),
          Expanded(flex: 1, child: _buildHeaderCell('圈口号')),
          Expanded(flex: 1, child: _buildHeaderCell('金重(g)')),
          Expanded(flex: 1, child: _buildHeaderCell('金价(¥/g)')),
          Expanded(flex: 1, child: _buildHeaderCell('银重(g)')),
          Expanded(flex: 1, child: _buildHeaderCell('银价(¥/g)')),
          Expanded(flex: 1, child: _buildHeaderCell('工费方式')),
          Expanded(flex: 1, child: _buildHeaderCell('工费(¥)')),
          Expanded(flex: 2, child: _buildHeaderCell('总成本(¥)')),
        ],
      ),
    );
  }

  /// 构建表格头部单元格
  Widget _buildHeaderCell(String text) {
    return Text(
      text,
      style: const TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w600,
        color: Colors.black87,
      ),
      textAlign: TextAlign.center,
    );
  }

  /// 构建表格行
  Widget _buildTableRow(StockInItem item) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey[200]!),
        ),
      ),
      child: Row(
        children: [
          Expanded(flex: 2, child: _buildDataCell(item.barcode)),
          Expanded(flex: 3, child: _buildDataCell(item.name)),
          Expanded(flex: 2, child: _buildDataCell(item.categoryName ?? '未知分类')),
          Expanded(flex: 1, child: _buildDataCell(item.ringSize ?? '-')),
          Expanded(flex: 1, child: _buildDataCell(item.goldWeight?.toStringAsFixed(2) ?? '0.00')),
          Expanded(flex: 1, child: _buildDataCell(item.goldPrice?.toStringAsFixed(2) ?? '0.00')),
          Expanded(flex: 1, child: _buildDataCell(item.silverWeight?.toStringAsFixed(2) ?? '0.00')),
          Expanded(flex: 1, child: _buildDataCell(item.silverPrice?.toStringAsFixed(2) ?? '0.00')),
          Expanded(flex: 1, child: _buildDataCell(_getWorkTypeText(item.silverWorkType))),
          Expanded(flex: 1, child: _buildDataCell(item.silverWorkPrice?.toStringAsFixed(2) ?? '0.00')),
          Expanded(flex: 2, child: _buildDataCell('¥${item.totalCost?.toStringAsFixed(2) ?? '0.00'}')),
        ],
      ),
    );
  }

  /// 构建数据单元格
  Widget _buildDataCell(String text) {
    return Text(
      text,
      style: const TextStyle(
        fontSize: 14,
        color: Colors.black87,
      ),
      textAlign: TextAlign.center,
      overflow: TextOverflow.ellipsis,
    );
  }

  /// 构建商品卡片（移动端）
  Widget _buildItemCard(StockInItem item) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            item.name,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: _buildItemInfo('条码', item.barcode),
              ),
              Expanded(
                child: _buildItemInfo('分类', item.categoryName ?? '未知分类'),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              Expanded(
                child: _buildItemInfo('圈口号', item.ringSize ?? '-'),
              ),
              Expanded(
                child: _buildItemInfo('工费方式', _getWorkTypeText(item.silverWorkType)),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              Expanded(
                child: _buildItemInfo('金重', '${item.goldWeight?.toStringAsFixed(2) ?? '0.00'}g'),
              ),
              Expanded(
                child: _buildItemInfo('金价', '¥${item.goldPrice?.toStringAsFixed(2) ?? '0.00'}/g'),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              Expanded(
                child: _buildItemInfo('银重', '${item.silverWeight?.toStringAsFixed(2) ?? '0.00'}g'),
              ),
              Expanded(
                child: _buildItemInfo('银价', '¥${item.silverPrice?.toStringAsFixed(2) ?? '0.00'}/g'),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              Expanded(
                child: _buildItemInfo('工费', '¥${item.silverWorkPrice?.toStringAsFixed(2) ?? '0.00'}'),
              ),
              Expanded(
                child: _buildItemInfo('总成本', '¥${item.totalCost?.toStringAsFixed(2) ?? '0.00'}'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建商品信息项
  Widget _buildItemInfo(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Text(
            '$label: ',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建空商品列表
  Widget _buildEmptyItems() {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        children: [
          Icon(
            Icons.inventory_2_outlined,
            size: 48,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            '暂无商品明细',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建汇总信息
  Widget _buildSummaryInfo() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(
                  Icons.calculate_outlined,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
                SizedBox(width: 8),
                Text(
                  '汇总信息',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ScreenTypeLayout(
              mobile: _buildSummaryMobile(),
              tablet: _buildSummaryDesktop(),
              desktop: _buildSummaryDesktop(),
            ),
          ],
        ),
      ),
    );
  }

  /// 移动端汇总信息
  Widget _buildSummaryMobile() {
    return Column(
      children: [
        _buildSummaryItem('总件数', '${widget.stockIn.itemCount}件', Icons.inventory),
        _buildSummaryItem('总金额', '¥${widget.stockIn.totalAmount.toStringAsFixed(2)}', Icons.attach_money),
        _buildSummaryItem('总金重', '${_calculateTotalGoldWeight().toStringAsFixed(2)}g', Icons.scale),
        _buildSummaryItem('总银重', '${_calculateTotalSilverWeight().toStringAsFixed(2)}g', Icons.scale),
      ],
    );
  }

  /// 桌面端汇总信息
  Widget _buildSummaryDesktop() {
    return Row(
      children: [
        Expanded(child: _buildSummaryItem('总件数', '${widget.stockIn.itemCount}件', Icons.inventory)),
        Expanded(child: _buildSummaryItem('总金额', '¥${widget.stockIn.totalAmount.toStringAsFixed(2)}', Icons.attach_money)),
        Expanded(child: _buildSummaryItem('总金重', '${_calculateTotalGoldWeight().toStringAsFixed(2)}g', Icons.scale)),
        Expanded(child: _buildSummaryItem('总银重', '${_calculateTotalSilverWeight().toStringAsFixed(2)}g', Icons.scale)),
      ],
    );
  }

  /// 构建汇总项
  Widget _buildSummaryItem(String label, String value, IconData icon) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppTheme.primaryColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppTheme.primaryColor.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            size: 20,
            color: AppTheme.primaryColor,
          ),
          const SizedBox(width: 8),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.primaryColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建底部操作区域
  Widget _buildFooter(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          OutlinedButton(
            onPressed: _safeCloseDialog,
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('关闭'),
          ),
          const SizedBox(width: 12),
          ElevatedButton.icon(
            onPressed: _isPrintingInProgress ? null : () async {
              if (_isPrintingInProgress) return;

              setState(() {
                _isPrintingInProgress = true;
              });

              try {
                LoggerService.d('🖨️ 开始打印入库单: ${widget.stockIn.stockInNo}');

                // 显示加载对话框
                Get.dialog(
                  const Center(
                    child: Card(
                      child: Padding(
                        padding: EdgeInsets.all(20),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            CircularProgressIndicator(),
                            SizedBox(height: 16),
                            Text('正在生成PDF文档...'),
                          ],
                        ),
                      ),
                    ),
                  ),
                  barrierDismissible: false,
                );

                // 使用改进的打印服务
                final printService = PrintService();

                // 调用打印功能
                final success = await printService.printStockIn(widget.stockIn);

                // 安全关闭加载对话框
                if (Get.isDialogOpen == true) {
                  Get.back();
                }

                if (success) {
                  Get.snackbar(
                    '成功',
                    'PDF预览已打开，您可以在预览窗口中直接打印或保存文件',
                    snackPosition: SnackPosition.BOTTOM,
                    backgroundColor: Colors.green,
                    colorText: Colors.white,
                    duration: const Duration(seconds: 4),
                  );
                } else {
                  _showPrintErrorDialog();
                }
              } on PlatformException catch (e) {
                LoggerService.e('❌ 打印平台异常', e);

                // 安全关闭加载对话框
                if (Get.isDialogOpen == true) {
                  Get.back();
                }

                if (e.code == 'MissingPluginException') {
                  _showPlatformNotSupportedDialog();
                } else {
                  _showPrintErrorDialog('平台异常: ${e.message}');
                }
              } catch (e) {
                LoggerService.e('❌ 打印功能异常', e);

                // 安全关闭加载对话框
                if (Get.isDialogOpen == true) {
                  Get.back();
                }

                _showPrintErrorDialog('打印功能出现异常: ${e.toString()}');
              } finally {
                if (mounted) {
                  setState(() {
                    _isPrintingInProgress = false;
                  });
                }
              }
            },
            icon: const Icon(Icons.print, size: 18),
            label: const Text('打印'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 计算总金重
  double _calculateTotalGoldWeight() {
    if (widget.stockIn.items?.isEmpty ?? true) return 0.0;
    return widget.stockIn.items!.fold(0.0, (sum, item) => sum + (item.goldWeight ?? 0.0));
  }

  /// 计算总银重
  double _calculateTotalSilverWeight() {
    if (widget.stockIn.items?.isEmpty ?? true) return 0.0;
    return widget.stockIn.items!.fold(0.0, (sum, item) => sum + (item.silverWeight ?? 0.0));
  }

  /// 获取工费方式文本
  String _getWorkTypeText(int? silverWorkType) {
    switch (silverWorkType) {
      case 0:
        return '按克';
      case 1:
        return '按件';
      default:
        return '-';
    }
  }

  /// 格式化日期时间
  String _formatDateTime(DateTime? dateTime) {
    if (dateTime == null) return '未知时间';
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
           '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// 获取状态文本
  String _getStatusText(DocumentStatus status) {
    switch (status) {
      case DocumentStatus.draft:
        return '草稿';
      case DocumentStatus.pending:
        return '待审核';
      case DocumentStatus.approved:
        return '已审核';
      case DocumentStatus.rejected:
        return '已拒绝';
      case DocumentStatus.cancelled:
        return '已取消';
      default:
        return '未知状态';
    }
  }

  /// 获取状态颜色
  Color _getStatusColor(DocumentStatus status) {
    switch (status) {
      case DocumentStatus.draft:
        return Colors.grey;
      case DocumentStatus.pending:
        return Colors.orange;
      case DocumentStatus.approved:
        return Colors.green;
      case DocumentStatus.rejected:
        return Colors.red;
      case DocumentStatus.cancelled:
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  /// 显示打印错误对话框
  void _showPrintErrorDialog([String? message]) {
    Get.dialog(
      AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.error_outline, color: Colors.red),
            SizedBox(width: 8),
            Text('打印失败'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(message ?? '打印功能暂时不可用，已自动保存PDF文件。'),
            const SizedBox(height: 16),
            const Text(
              '解决方案：',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text('• PDF文件已保存到您选择的位置'),
            const Text('• 您可以使用PDF阅读器打开文件'),
            const Text('• 在PDF阅读器中选择打印功能'),
            const Text('• 或者将文件发送到支持打印的设备'),
            const SizedBox(height: 12),
            const Text(
              '注意：Windows桌面版本的打印预览功能可能需要额外配置',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  /// 显示平台不支持对话框
  void _showPlatformNotSupportedDialog() {
    Get.dialog(
      AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.warning_amber_outlined, color: Colors.orange),
            SizedBox(width: 8),
            Text('平台不支持'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('当前平台暂不支持打印功能。'),
            SizedBox(height: 16),
            Text(
              '支持的平台：',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text('• Windows 桌面应用'),
            Text('• macOS 桌面应用'),
            Text('• Linux 桌面应用'),
            Text('• Android 移动应用'),
            Text('• iOS 移动应用'),
            Text('• Web 浏览器'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
}
