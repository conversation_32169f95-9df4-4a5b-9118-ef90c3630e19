import 'dart:convert';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../models/print/print_template_config.dart';
import '../core/utils/logger_service.dart';

/// 打印模板管理服务
/// 
/// 负责打印模板的创建、编辑、删除、存储和加载
class PrintTemplateService extends GetxService {
  static const String _templatesKey = 'print_templates';
  static const String _defaultTemplateKey = 'default_print_template';
  
  /// 模板列表
  final RxList<PrintTemplateConfig> templates = <PrintTemplateConfig>[].obs;
  
  /// 当前默认模板
  final Rx<PrintTemplateConfig?> defaultTemplate = Rx<PrintTemplateConfig?>(null);
  
  /// 是否已初始化
  final RxBool isInitialized = false.obs;

  @override
  Future<void> onInit() async {
    super.onInit();
    await _initializeService();
  }

  /// 初始化服务
  Future<void> _initializeService() async {
    try {
      LoggerService.d('🖨️ 初始化打印模板服务');
      
      // 加载保存的模板
      await _loadTemplates();
      
      // 如果没有模板，创建默认模板
      if (templates.isEmpty) {
        await _createDefaultTemplates();
      }
      
      // 设置默认模板
      await _loadDefaultTemplate();
      
      isInitialized.value = true;
      LoggerService.d('✅ 打印模板服务初始化完成，共 ${templates.length} 个模板');
      
    } catch (e) {
      LoggerService.e('❌ 打印模板服务初始化失败', e);
    }
  }

  /// 加载保存的模板
  Future<void> _loadTemplates() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final templatesJson = prefs.getString(_templatesKey);
      
      if (templatesJson != null) {
        final List<dynamic> templatesList = json.decode(templatesJson);
        templates.value = templatesList
            .map((json) => PrintTemplateConfig.fromJson(json))
            .toList();
        LoggerService.d('📄 已加载 ${templates.length} 个打印模板');
      }
    } catch (e) {
      LoggerService.e('❌ 加载打印模板失败', e);
    }
  }

  /// 保存模板到本地存储
  Future<void> _saveTemplates() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final templatesJson = json.encode(templates.map((t) => t.toJson()).toList());
      await prefs.setString(_templatesKey, templatesJson);
      LoggerService.d('💾 已保存 ${templates.length} 个打印模板');
    } catch (e) {
      LoggerService.e('❌ 保存打印模板失败', e);
    }
  }

  /// 加载默认模板
  Future<void> _loadDefaultTemplate() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final defaultTemplateId = prefs.getString(_defaultTemplateKey);
      
      if (defaultTemplateId != null) {
        defaultTemplate.value = templates.firstWhereOrNull(
          (template) => template.id == defaultTemplateId,
        );
      }
      
      // 如果没有默认模板，使用第一个模板
      if (defaultTemplate.value == null && templates.isNotEmpty) {
        defaultTemplate.value = templates.first;
        await _saveDefaultTemplate(templates.first.id);
      }
      
      LoggerService.d('🎯 默认模板: ${defaultTemplate.value?.name ?? "无"}');
    } catch (e) {
      LoggerService.e('❌ 加载默认模板失败', e);
    }
  }

  /// 保存默认模板ID
  Future<void> _saveDefaultTemplate(String templateId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_defaultTemplateKey, templateId);
      LoggerService.d('🎯 已设置默认模板: $templateId');
    } catch (e) {
      LoggerService.e('❌ 保存默认模板失败', e);
    }
  }

  /// 创建默认模板
  Future<void> _createDefaultTemplates() async {
    try {
      LoggerService.d('🏗️ 创建默认打印模板');
      
      // 标准模板
      final standardTemplate = PrintTemplateConfig(
        id: 'standard',
        name: '标准模板',
        description: '适用于大多数收款凭证的标准打印模板',
        isDefault: true,
        companyInfo: const CompanyInfoConfig(
          companyName: '金包银首饰店',
          showAddress: true,
          showPhone: true,
          fontSize: 16.0,
          isBold: true,
        ),
        pageConfig: const PageConfig(
          width: 210.0,
          height: 101.6,
          margins: EdgeInsetsConfig(top: 3, bottom: 3, left: 3, right: 3),
        ),
        headerConfig: const HeaderConfig(
          showOrderNo: true,
          showCustomer: true,
          showSaleType: true,
          showDateTime: true,
          fontSize: 12.0,
        ),
        itemTableConfig: const ItemTableConfig(
          visibleColumns: ['序号', '条码', '商品名称', '规格', '重量', '单价', '金额'],
          columnWidths: {
            '序号': 8.0,
            '条码': 15.0,
            '商品名称': 25.0,
            '规格': 12.0,
            '重量': 12.0,
            '单价': 13.0,
            '金额': 15.0,
          },
          headerFontSize: 10.0,
          contentFontSize: 9.0,
          rowHeight: 20.0,
        ),
        summaryConfig: const SummaryConfig(
          showTotalQuantity: true,
          showTotalWeight: true,
          showTotalAmount: true,
          fontSize: 11.0,
          isBold: true,
        ),
        paymentInfoConfig: const PaymentInfoConfig(
          showPaymentMethod: true,
          showPaymentDetails: true,
          showChangeAmount: true,
          fontSize: 10.0,
        ),
        footerConfig: const FooterConfig(
          customText: '谢谢惠顾，欢迎再次光临！',
          showPrintTime: true,
          fontSize: 8.0,
        ),
        createTime: DateTime.now(),
        updateTime: DateTime.now(),
      );

      // 简洁模板
      final simpleTemplate = PrintTemplateConfig(
        id: 'simple',
        name: '简洁模板',
        description: '简化版收款凭证，适用于快速打印',
        isDefault: false,
        companyInfo: const CompanyInfoConfig(
          companyName: '金包银首饰店',
          showAddress: false,
          showPhone: true,
          fontSize: 14.0,
          isBold: true,
        ),
        pageConfig: const PageConfig(
          width: 210.0,
          height: 101.6,
          margins: EdgeInsetsConfig(top: 5, bottom: 5, left: 5, right: 5),
        ),
        headerConfig: const HeaderConfig(
          showOrderNo: true,
          showCustomer: false,
          showSaleType: false,
          showDateTime: true,
          fontSize: 11.0,
        ),
        itemTableConfig: const ItemTableConfig(
          visibleColumns: ['序号', '商品名称', '重量', '金额'],
          columnWidths: {
            '序号': 10.0,
            '商品名称': 40.0,
            '重量': 25.0,
            '金额': 25.0,
          },
          headerFontSize: 10.0,
          contentFontSize: 9.0,
          rowHeight: 18.0,
        ),
        summaryConfig: const SummaryConfig(
          showTotalQuantity: false,
          showTotalWeight: true,
          showTotalAmount: true,
          fontSize: 11.0,
          isBold: true,
        ),
        paymentInfoConfig: const PaymentInfoConfig(
          showPaymentMethod: true,
          showPaymentDetails: false,
          showChangeAmount: false,
          fontSize: 10.0,
        ),
        footerConfig: const FooterConfig(
          customText: null,
          showPrintTime: false,
          fontSize: 8.0,
        ),
        createTime: DateTime.now(),
        updateTime: DateTime.now(),
      );

      templates.addAll([standardTemplate, simpleTemplate]);
      await _saveTemplates();
      
      LoggerService.d('✅ 已创建 ${templates.length} 个默认模板');
    } catch (e) {
      LoggerService.e('❌ 创建默认模板失败', e);
    }
  }

  /// 获取所有模板
  List<PrintTemplateConfig> getAllTemplates() {
    return templates.toList();
  }

  /// 根据ID获取模板
  PrintTemplateConfig? getTemplateById(String id) {
    return templates.firstWhereOrNull((template) => template.id == id);
  }

  /// 获取默认模板
  PrintTemplateConfig? getDefaultTemplate() {
    return defaultTemplate.value;
  }

  /// 创建新模板
  Future<bool> createTemplate(PrintTemplateConfig template) async {
    try {
      // 检查ID是否已存在
      if (templates.any((t) => t.id == template.id)) {
        LoggerService.w('⚠️ 模板ID已存在: ${template.id}');
        return false;
      }

      templates.add(template);
      await _saveTemplates();
      
      LoggerService.d('✅ 已创建新模板: ${template.name}');
      return true;
    } catch (e) {
      LoggerService.e('❌ 创建模板失败', e);
      return false;
    }
  }

  /// 更新模板
  Future<bool> updateTemplate(PrintTemplateConfig template) async {
    try {
      final index = templates.indexWhere((t) => t.id == template.id);
      if (index == -1) {
        LoggerService.w('⚠️ 模板不存在: ${template.id}');
        return false;
      }

      templates[index] = template.copyWith(updateTime: DateTime.now());
      await _saveTemplates();
      
      // 如果更新的是默认模板，也要更新默认模板引用
      if (defaultTemplate.value?.id == template.id) {
        defaultTemplate.value = templates[index];
      }
      
      LoggerService.d('✅ 已更新模板: ${template.name}');
      return true;
    } catch (e) {
      LoggerService.e('❌ 更新模板失败', e);
      return false;
    }
  }

  /// 删除模板
  Future<bool> deleteTemplate(String templateId) async {
    try {
      final template = getTemplateById(templateId);
      if (template == null) {
        LoggerService.w('⚠️ 模板不存在: $templateId');
        return false;
      }

      // 不能删除默认模板
      if (template.isDefault || defaultTemplate.value?.id == templateId) {
        LoggerService.w('⚠️ 不能删除默认模板: ${template.name}');
        return false;
      }

      templates.removeWhere((t) => t.id == templateId);
      await _saveTemplates();
      
      LoggerService.d('✅ 已删除模板: ${template.name}');
      return true;
    } catch (e) {
      LoggerService.e('❌ 删除模板失败', e);
      return false;
    }
  }

  /// 设置默认模板
  Future<bool> setDefaultTemplate(String templateId) async {
    try {
      final template = getTemplateById(templateId);
      if (template == null) {
        LoggerService.w('⚠️ 模板不存在: $templateId');
        return false;
      }

      defaultTemplate.value = template;
      await _saveDefaultTemplate(templateId);
      
      LoggerService.d('✅ 已设置默认模板: ${template.name}');
      return true;
    } catch (e) {
      LoggerService.e('❌ 设置默认模板失败', e);
      return false;
    }
  }

  /// 复制模板
  Future<PrintTemplateConfig?> duplicateTemplate(String templateId, String newName) async {
    try {
      final originalTemplate = getTemplateById(templateId);
      if (originalTemplate == null) {
        LoggerService.w('⚠️ 原模板不存在: $templateId');
        return null;
      }

      final newTemplate = originalTemplate.copyWith(
        id: 'copy_${DateTime.now().millisecondsSinceEpoch}',
        name: newName,
        isDefault: false,
        createTime: DateTime.now(),
        updateTime: DateTime.now(),
      );

      final success = await createTemplate(newTemplate);
      if (success) {
        LoggerService.d('✅ 已复制模板: $newName');
        return newTemplate;
      }
      
      return null;
    } catch (e) {
      LoggerService.e('❌ 复制模板失败', e);
      return null;
    }
  }

  /// 重置为默认模板
  Future<void> resetToDefaults() async {
    try {
      LoggerService.d('🔄 重置为默认模板');
      
      templates.clear();
      await _createDefaultTemplates();
      await _loadDefaultTemplate();
      
      LoggerService.d('✅ 已重置为默认模板');
    } catch (e) {
      LoggerService.e('❌ 重置默认模板失败', e);
    }
  }
}
