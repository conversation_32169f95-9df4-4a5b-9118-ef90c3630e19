# 旧料回收相关模型

本目录包含金包银首饰管理系统的旧料回收相关数据模型。这些模型用于管理首饰店的旧料回收、处理和二次销售业务流程。

## 模型结构

```
recycling/
├── recycling.dart            # 回收单主表模型
├── recycling_item.dart       # 回收单明细模型
├── recycling_process.dart    # 旧料处理记录模型
├── metal_separation.dart     # 金银分离记录模型
└── repair_record.dart        # 维修记录模型
```

## 业务流程

旧料回收业务流程主要包括以下几个环节：

1. **回收登记**：创建回收单(Recycling)和回收明细(RecyclingItem)
2. **回收处理**：根据不同处理方式(RecyclingProcessType)创建处理记录(RecyclingProcess)
   - 直接卖出：简单记录卖出价格和利润
   - 金银分离：创建金银分离记录(MetalSeparation)，记录分离后的金银重量和成本
   - 维修再销售：创建维修记录(RepairRecord)，关联新首饰
   - 直接二次销售：关联新首饰，记录二次销售价格

## 模型关系

- 一个回收单(Recycling)包含多个回收物品(RecyclingItem)
- 一个回收物品(RecyclingItem)可以有一个处理记录(RecyclingProcess)
- 处理记录(RecyclingProcess)根据处理方式可能关联:
  - 金银分离记录(MetalSeparation)
  - 维修记录(RepairRecord)
  - 新首饰(Jewelry)

## 使用示例

### 创建回收单及物品

```dart
// 创建回收单
final recycling = Recycling(
  id: 1,
  recyclingNo: 'R001',
  customer: '张三',
  phone: '13800138000',
  storeId: 1,
  operatorId: 1,
);

// 添加回收物品
final item = RecyclingItem(
  id: 1,
  recyclingId: 1,
  name: '黄金项链',
  goldWeight: 10.5,
  goldPrice: 380.0,
  silverWeight: 0.0,
  silverPrice: 0.0,
  amount: 3990.0,
);
```

### 创建金银分离记录

```dart
final separation = MetalSeparation(
  id: 1,
  processId: 1,
  recyclingItemId: 1,
  originalGoldWeight: 10.0,
  originalSilverWeight: 5.0,
  separatedGoldWeight: 9.5,
  separatedSilverWeight: 4.8,
  goldPrice: 380.0,
  silverPrice: 5.0,
  separationCost: 100.0,
);

// 计算分离后的总价值
final totalValue = separation.totalValue; // 分离后金银总价值
final profit = separation.profit; // 分离利润
```

### 创建处理记录

```dart
final process = RecyclingProcess(
  id: 1,
  recyclingItemId: 1,
  processType: RecyclingProcessType.separate,
  processNo: 'P001',
  operatorId: 1,
  storeId: 1,
  cost: 100.0,
  income: 3634.0,
  profit: -291.0, // 可能有损耗导致负利润
);
```

## 特殊字段说明

### RecyclingItem
- `processType`: 处理方式枚举(RecyclingProcessType)
- `processStatus`: 处理状态(0=未处理,1=处理中,2=已处理)

### MetalSeparation
- `lossRate`: 损耗率(%)，表示分离过程中的金银损耗比例
- `separationCost`: 分离成本，包括人工和材料成本

### RecyclingProcess
- `jewelryId`: 关联的新首饰ID，仅在维修再销售或直接二次销售时有值 