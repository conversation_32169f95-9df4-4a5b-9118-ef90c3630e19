import 'package:dio/dio.dart';
import 'package:get/get.dart';

import '../core/config/app_config.dart';
import '../core/services/api_client.dart';
import '../core/utils/logger.dart';
import '../models/common/paginated_response.dart';
import '../models/stock/inventory_check.dart';
import '../models/stock/inventory_check_item.dart';
import '../models/stock/inventory_check_statistics.dart';
import '../services/auth_service.dart';

/// 库存盘点服务
/// 老板，这个服务类提供完整的库存盘点API对接功能
class InventoryCheckService extends GetxService {
  late final ApiClient _apiClient;
  final AuthService _authService = Get.find<AuthService>();

  @override
  void onInit() {
    super.onInit();
    _apiClient = Get.find<ApiClient>();
    LoggerService.d('InventoryCheckService 初始化完成');
  }

  /// 获取盘点单列表
  /// 支持分页查询和多条件筛选
  Future<PaginatedResponse<InventoryCheck>> getInventoryCheckList({
    int page = 1,
    int pageSize = 20,
    String? keyword,
    int? storeId,
    int? status,
    int? operatorId,
    String? startDate,
    String? endDate,
  }) async {
    try {
      LoggerService.d('📋 获取盘点单列表: page=$page, pageSize=$pageSize, keyword=$keyword, storeId=$storeId, status=$status');

      final Map<String, dynamic> params = {
        'page': page,
        'page_size': pageSize,
      };

      // 添加筛选条件
      if (keyword != null && keyword.isNotEmpty) {
        params['keyword'] = keyword;
      }
      if (storeId != null && storeId > 0) {
        params['store_id'] = storeId;
      }
      if (status != null) {
        params['status'] = status;
      }
      if (operatorId != null && operatorId > 0) {
        params['operator_id'] = operatorId;
      }
      if (startDate != null && startDate.isNotEmpty) {
        params['start_date'] = startDate;
      }
      if (endDate != null && endDate.isNotEmpty) {
        params['end_date'] = endDate;
      }

      final response = await _apiClient.get(
        AppConfig.apiEndpoint['inventoryCheck']!,
        queryParameters: params,
      );

      LoggerService.d('✅ 盘点单列表获取成功: ${response.data}');

      // 处理API响应数据
      final responseData = response.data;

      // 检查响应格式
      if (responseData is Map<String, dynamic>) {
        // FastAPI标准响应格式: {code: 200, message: "success", data: {...}}
        if (responseData.containsKey('code') && responseData.containsKey('data')) {
          final data = responseData['data'];

          if (data is Map<String, dynamic>) {
            // 分页数据格式: {items: [...], total: 100, page: 1, page_size: 20, total_pages: 5}
            if (data.containsKey('items')) {
              final items = (data['items'] as List)
                  .map((item) => InventoryCheck.fromJson(item as Map<String, dynamic>))
                  .toList();

              return PaginatedResponse<InventoryCheck>(
                data: items,
                currentPage: data['page'] ?? page,
                lastPage: data['total_pages'] ?? 1,
                perPage: data['page_size'] ?? pageSize,
                total: data['total'] ?? 0,
              );
            }
          }
          // 直接数组格式
          else if (data is List) {
            final items = data.map((item) => InventoryCheck.fromJson(item as Map<String, dynamic>)).toList();

            return PaginatedResponse<InventoryCheck>(
              data: items,
              currentPage: page,
              lastPage: 1,
              perPage: pageSize,
              total: items.length,
            );
          }
        }
        // 简化响应格式（直接包含data和pagination）
        else if (responseData.containsKey('data') && responseData.containsKey('pagination')) {
          final data = responseData['data'] as List;
          final pagination = responseData['pagination'] as Map<String, dynamic>;

          final items = data.map((item) => InventoryCheck.fromJson(item as Map<String, dynamic>)).toList();

          return PaginatedResponse<InventoryCheck>(
            data: items,
            currentPage: pagination['page'] ?? page,
            lastPage: pagination['pages'] ?? 1,
            perPage: pagination['page_size'] ?? pageSize,
            total: pagination['total'] ?? 0,
          );
        }
      }

      // 如果响应格式不符合预期，返回空数据
      LoggerService.w('⚠️ API响应格式不符合预期: $responseData');
      return PaginatedResponse<InventoryCheck>(
        data: [],
        currentPage: page,
        lastPage: 1,
        perPage: pageSize,
        total: 0,
      );

    } catch (e) {
      LoggerService.e('❌ 获取盘点单列表失败', e);

      // 如果是网络错误，返回模拟数据
      if (e is DioException) {
        LoggerService.w('🔄 API不可用，返回模拟数据');
        return _getMockInventoryCheckList(page, pageSize);
      }

      rethrow;
    }
  }

  /// 根据ID获取盘点单详情
  /// 包含盘点明细列表和关联信息
  Future<InventoryCheck> getInventoryCheckById(int id) async {
    try {
      LoggerService.d('🔍 获取盘点单详情: ID=$id');

      final response = await _apiClient.get(
        '${AppConfig.apiEndpoint['inventoryCheck']!}/$id',
      );

      LoggerService.d('✅ 盘点单详情获取成功: ${response.data}');

      // 处理API响应数据
      final responseData = response.data;
      if (responseData is Map<String, dynamic>) {
        // FastAPI标准响应格式: {code: 200, message: "success", data: {...}}
        if (responseData.containsKey('code') && responseData.containsKey('data')) {
          return InventoryCheck.fromJson(responseData['data'] as Map<String, dynamic>);
        }
        // 简化响应格式（直接包含data）
        else if (responseData.containsKey('data')) {
          return InventoryCheck.fromJson(responseData['data'] as Map<String, dynamic>);
        }
        // 直接返回数据
        else {
          return InventoryCheck.fromJson(responseData);
        }
      } else {
        throw Exception('API响应格式错误: $responseData');
      }
    } catch (e) {
      LoggerService.e('❌ 获取盘点单详情失败', e);

      // 如果是网络错误，返回模拟数据
      if (e is DioException) {
        LoggerService.w('🔄 API不可用，返回模拟数据');
        return _getMockInventoryCheckDetail(id);
      }

      rethrow;
    }
  }

  /// 根据单号获取盘点单详情
  /// 支持扫码查询和快速定位
  Future<InventoryCheck> getInventoryCheckByNo(String checkNo) async {
    try {
      LoggerService.d('🔍 根据单号获取盘点单详情: checkNo=$checkNo');

      final response = await _apiClient.get(
        '${AppConfig.apiEndpoint['inventoryCheck']!}/by-no/$checkNo',
      );

      LoggerService.d('✅ 盘点单详情获取成功: ${response.data}');

      // 处理API响应数据
      final responseData = response.data;
      if (responseData is Map<String, dynamic>) {
        // FastAPI标准响应格式: {code: 200, message: "success", data: {...}}
        if (responseData.containsKey('code') && responseData.containsKey('data')) {
          return InventoryCheck.fromJson(responseData['data'] as Map<String, dynamic>);
        }
        // 简化响应格式（直接包含data）
        else if (responseData.containsKey('data')) {
          return InventoryCheck.fromJson(responseData['data'] as Map<String, dynamic>);
        }
        // 直接返回数据
        else {
          return InventoryCheck.fromJson(responseData);
        }
      } else {
        throw Exception('API响应格式错误: $responseData');
      }
    } catch (e) {
      LoggerService.e('❌ 根据单号获取盘点单详情失败', e);

      // 如果是网络错误，返回模拟数据
      if (e is DioException) {
        LoggerService.w('🔄 API不可用，返回模拟数据');
        return _getMockInventoryCheckDetail(1);
      }

      rethrow;
    }
  }

  /// 创建盘点单
  /// 自动生成盘点单号，验证门店和商品信息
  Future<InventoryCheck> createInventoryCheck({
    required int storeId,
    required List<Map<String, dynamic>> items,
    String? remark,
  }) async {
    try {
      LoggerService.d('📝 创建盘点单: storeId=$storeId, items=${items.length}, remark=$remark');

      // 验证必填参数
      if (items.isEmpty) {
        throw ArgumentError('盘点商品明细不能为空');
      }

      final data = {
        'store_id': storeId,
        'items': items,
        if (remark != null && remark.isNotEmpty) 'remark': remark,
      };

      final response = await _apiClient.post(
        '${AppConfig.apiEndpoint['inventoryCheck']!}?operator_id=${_authService.userId.value}',
        data: data,
      );

      LoggerService.d('✅ 盘点单创建成功: ${response.data}');

      // 处理API响应数据
      final responseData = response.data;
      if (responseData is Map<String, dynamic>) {
        // FastAPI标准响应格式: {code: 200, message: "success", data: {...}}
        if (responseData.containsKey('code') && responseData.containsKey('data')) {
          return InventoryCheck.fromJson(responseData['data'] as Map<String, dynamic>);
        }
        // 简化响应格式（直接包含data）
        else if (responseData.containsKey('data')) {
          return InventoryCheck.fromJson(responseData['data'] as Map<String, dynamic>);
        }
        // 直接返回数据
        else {
          return InventoryCheck.fromJson(responseData);
        }
      } else {
        throw Exception('API响应格式错误: $responseData');
      }
    } catch (e) {
      LoggerService.e('❌ 创建盘点单失败', e);

      // 重新抛出异常，让调用方处理
      if (e is DioException) {
        final errorMessage = _extractErrorMessage(e);
        throw Exception('创建盘点单失败: $errorMessage');
      }

      rethrow;
    }
  }

  /// 更新盘点单
  /// 只能更新备注信息，其他信息通过专门的API更新
  Future<InventoryCheck> updateInventoryCheck(int id, {String? remark}) async {
    try {
      LoggerService.d('📝 更新盘点单: ID=$id, remark=$remark');

      final data = <String, dynamic>{};
      if (remark != null && remark.isNotEmpty) {
        data['remark'] = remark;
      }

      final response = await _apiClient.put(
        '${AppConfig.apiEndpoint['inventoryCheck']!}/$id',
        data: data,
      );

      LoggerService.d('✅ 盘点单更新成功: ${response.data}');

      // 处理API响应数据
      final responseData = response.data;
      if (responseData is Map<String, dynamic>) {
        // FastAPI标准响应格式: {code: 200, message: "success", data: {...}}
        if (responseData.containsKey('code') && responseData.containsKey('data')) {
          return InventoryCheck.fromJson(responseData['data'] as Map<String, dynamic>);
        }
        // 简化响应格式（直接包含data）
        else if (responseData.containsKey('data')) {
          return InventoryCheck.fromJson(responseData['data'] as Map<String, dynamic>);
        }
        // 直接返回数据
        else {
          return InventoryCheck.fromJson(responseData);
        }
      } else {
        throw Exception('API响应格式错误: $responseData');
      }
    } catch (e) {
      LoggerService.e('❌ 更新盘点单失败', e);

      if (e is DioException) {
        final errorMessage = _extractErrorMessage(e);
        throw Exception('更新盘点单失败: $errorMessage');
      }

      rethrow;
    }
  }

  /// 删除盘点单
  /// 只有进行中或已取消状态的盘点单可以删除
  Future<void> deleteInventoryCheck(int id) async {
    try {
      LoggerService.d('🗑️ 删除盘点单: ID=$id');

      await _apiClient.delete(
        '${AppConfig.apiEndpoint['inventoryCheck']!}/$id',
      );

      LoggerService.d('✅ 盘点单删除成功');
    } catch (e) {
      LoggerService.e('❌ 删除盘点单失败', e);

      if (e is DioException) {
        final errorMessage = _extractErrorMessage(e);
        throw Exception('删除盘点单失败: $errorMessage');
      }

      rethrow;
    }
  }

  /// 更新盘点单状态
  /// 支持完成盘点和取消盘点操作
  Future<bool> updateInventoryCheckStatus(int id, {
    required int status,
    String? remark,
  }) async {
    try {
      LoggerService.d('🔄 更新盘点单状态: ID=$id, status=$status, remark=$remark');

      final data = {
        'status': status,
        if (remark != null && remark.isNotEmpty) 'remark': remark,
      };

      final response = await _apiClient.patch(
        '${AppConfig.apiEndpoint['inventoryCheck']!}/$id/status',
        data: data,
      );

      LoggerService.d('✅ 盘点单状态更新成功: ${response.data}');

      // 处理API响应数据
      final responseData = response.data;
      if (responseData is Map<String, dynamic>) {
        return responseData['success'] == true || responseData['data'] == true;
      }

      return true;
    } catch (e) {
      LoggerService.e('❌ 更新盘点单状态失败', e);

      if (e is DioException) {
        final errorMessage = _extractErrorMessage(e);
        throw Exception('更新盘点单状态失败: $errorMessage');
      }

      rethrow;
    }
  }

  /// 盘点单个商品
  /// 记录实际库存数量并自动计算差异
  Future<bool> checkInventoryItem(int checkId, int itemId, {
    required int actualStock,
    String? remark,
  }) async {
    try {
      LoggerService.d('✅ 盘点单个商品: checkId=$checkId, itemId=$itemId, actualStock=$actualStock, remark=$remark');

      final data = {
        'actual_stock': actualStock,
        if (remark != null && remark.isNotEmpty) 'remark': remark,
      };

      final response = await _apiClient.patch(
        '${AppConfig.apiEndpoint['inventoryCheck']!}/$checkId/items/$itemId/check?checker_id=${_authService.userId.value}',
        data: data,
      );

      LoggerService.d('✅ 商品盘点成功: ${response.data}');

      // 处理API响应数据
      final responseData = response.data;
      if (responseData is Map<String, dynamic>) {
        return responseData['success'] == true || responseData['data'] == true;
      }

      return true;
    } catch (e) {
      LoggerService.e('❌ 商品盘点失败', e);

      if (e is DioException) {
        final errorMessage = _extractErrorMessage(e);
        throw Exception('商品盘点失败: $errorMessage');
      }

      rethrow;
    }
  }

  /// 获取盘点统计
  /// 支持按门店和日期范围筛选统计数据
  Future<InventoryCheckStatistics> getInventoryCheckStatistics({
    int? storeId,
    String? startDate,
    String? endDate,
  }) async {
    try {
      LoggerService.d('📊 获取盘点统计: storeId=$storeId, startDate=$startDate, endDate=$endDate');

      final Map<String, dynamic> params = {};
      if (storeId != null && storeId > 0) {
        params['store_id'] = storeId;
      }
      if (startDate != null && startDate.isNotEmpty) {
        params['start_date'] = startDate;
      }
      if (endDate != null && endDate.isNotEmpty) {
        params['end_date'] = endDate;
      }

      final response = await _apiClient.get(
        '${AppConfig.apiEndpoint['inventoryCheck']!}/statistics/summary',
        queryParameters: params,
      );

      LoggerService.d('✅ 盘点统计获取成功: ${response.data}');

      // 处理API响应数据
      final responseData = response.data;
      if (responseData is Map<String, dynamic>) {
        // FastAPI标准响应格式: {code: 200, message: "success", data: {...}}
        if (responseData.containsKey('code') && responseData.containsKey('data')) {
          return InventoryCheckStatistics.fromJson(responseData['data'] as Map<String, dynamic>);
        }
        // 简化响应格式（直接包含data）
        else if (responseData.containsKey('data')) {
          return InventoryCheckStatistics.fromJson(responseData['data'] as Map<String, dynamic>);
        }
        // 直接返回数据
        else {
          return InventoryCheckStatistics.fromJson(responseData);
        }
      } else {
        throw Exception('API响应格式错误: $responseData');
      }
    } catch (e) {
      LoggerService.e('❌ 获取盘点统计失败', e);

      // 如果是网络错误，返回模拟数据
      if (e is DioException) {
        LoggerService.w('🔄 API不可用，返回模拟统计数据');
        return _getMockStatistics();
      }

      rethrow;
    }
  }

  /// 批量盘点商品
  /// 支持一次性盘点多个商品
  Future<bool> batchCheckItems(int checkId, List<Map<String, dynamic>> items) async {
    try {
      LoggerService.d('📦 批量盘点商品: checkId=$checkId, items=${items.length}');

      final data = {
        'items': items,
      };

      final response = await _apiClient.patch(
        '${AppConfig.apiEndpoint['inventoryCheck']!}/$checkId/batch-check?checker_id=${_authService.userId.value}',
        data: data,
      );

      LoggerService.d('✅ 批量盘点成功: ${response.data}');

      // 处理API响应数据
      final responseData = response.data;
      if (responseData is Map<String, dynamic>) {
        return responseData['success'] == true || responseData['data'] == true;
      }

      return true;
    } catch (e) {
      LoggerService.e('❌ 批量盘点失败', e);

      if (e is DioException) {
        final errorMessage = _extractErrorMessage(e);
        throw Exception('批量盘点失败: $errorMessage');
      }

      rethrow;
    }
  }

  /// 获取盘点明细列表
  /// 支持分页查询盘点明细
  Future<PaginatedResponse<InventoryCheckItem>> getInventoryCheckItems(
    int checkId, {
    int page = 1,
    int pageSize = 20,
    int? status,
  }) async {
    try {
      LoggerService.d('📋 获取盘点明细列表: checkId=$checkId, page=$page, pageSize=$pageSize, status=$status');

      final Map<String, dynamic> params = {
        'page': page,
        'page_size': pageSize,
      };

      if (status != null) {
        params['status'] = status;
      }

      final response = await _apiClient.get(
        '${AppConfig.apiEndpoint['inventoryCheck']!}/$checkId/items',
        queryParameters: params,
      );

      LoggerService.d('✅ 盘点明细列表获取成功: ${response.data}');

      // 处理API响应数据
      final responseData = response.data;

      if (responseData is Map<String, dynamic>) {
        // 标准响应格式
        if (responseData.containsKey('data') && responseData.containsKey('pagination')) {
          final data = responseData['data'] as List;
          final pagination = responseData['pagination'] as Map<String, dynamic>;

          final items = data.map((item) => InventoryCheckItem.fromJson(item as Map<String, dynamic>)).toList();

          return PaginatedResponse<InventoryCheckItem>(
            data: items,
            currentPage: pagination['page'] ?? page,
            lastPage: pagination['pages'] ?? 1,
            perPage: pagination['page_size'] ?? pageSize,
            total: pagination['total'] ?? 0,
          );
        }
        // 简化响应格式
        else if (responseData.containsKey('data')) {
          final data = responseData['data'];
          if (data is Map<String, dynamic> && data.containsKey('items')) {
            final items = (data['items'] as List)
                .map((item) => InventoryCheckItem.fromJson(item as Map<String, dynamic>))
                .toList();

            return PaginatedResponse<InventoryCheckItem>(
              data: items,
              currentPage: data['page'] ?? page,
              lastPage: data['total_pages'] ?? 1,
              perPage: data['page_size'] ?? pageSize,
              total: data['total'] ?? 0,
            );
          }
        }
      }

      // 如果响应格式不符合预期，返回空数据
      LoggerService.w('⚠️ API响应格式不符合预期，返回空数据');
      return PaginatedResponse<InventoryCheckItem>(
        data: [],
        currentPage: page,
        lastPage: 1,
        perPage: pageSize,
        total: 0,
      );

    } catch (e) {
      LoggerService.e('❌ 获取盘点明细列表失败', e);

      // 如果是网络错误，返回空数据
      if (e is DioException) {
        LoggerService.w('🔄 API不可用，返回空数据');
        return PaginatedResponse<InventoryCheckItem>(
          data: [],
          currentPage: page,
          lastPage: 1,
          perPage: pageSize,
          total: 0,
        );
      }

      rethrow;
    }
  }

  /// 提取错误信息
  /// 从DioException中提取用户友好的错误信息
  String _extractErrorMessage(DioException e) {
    try {
      if (e.response?.data != null) {
        final data = e.response!.data;

        // 处理不同的错误响应格式
        if (data is Map<String, dynamic>) {
          // FastAPI标准错误格式
          if (data.containsKey('detail')) {
            final detail = data['detail'];
            if (detail is String) {
              return detail;
            } else if (detail is List && detail.isNotEmpty) {
              // 验证错误格式
              final firstError = detail.first;
              if (firstError is Map<String, dynamic> && firstError.containsKey('msg')) {
                return firstError['msg'];
              }
            }
          }

          // 自定义错误格式
          if (data.containsKey('message')) {
            return data['message'];
          }
        } else if (data is String) {
          return data;
        }
      }

      // 根据HTTP状态码返回默认错误信息
      switch (e.response?.statusCode) {
        case 400:
          return '请求参数错误';
        case 401:
          return '未授权，请重新登录';
        case 403:
          return '权限不足';
        case 404:
          return '资源不存在';
        case 422:
          return '数据验证失败';
        case 500:
          return '服务器内部错误';
        default:
          return '网络请求失败';
      }
    } catch (ex) {
      LoggerService.e('解析错误信息失败', ex);
      return '未知错误';
    }
  }

  /// 模拟盘点单列表数据
  PaginatedResponse<InventoryCheck> _getMockInventoryCheckList(int page, int pageSize) {
    final mockData = List.generate(5, (index) {
      final id = index + 1;
      return InventoryCheck(
        id: id,
        checkNo: 'CHK20241201${id.toString().padLeft(3, '0')}',
        storeId: 1,
        status: index % 3,
        operatorId: 1,
        totalCount: 100 + index * 10,
        checkedCount: 50 + index * 8,
        differenceCount: index * 2,
        remark: index == 0 ? '月度盘点' : null,
        createTime: DateTime.now().subtract(Duration(days: index)),
        updateTime: DateTime.now().subtract(Duration(hours: index)),
      );
    });

    return PaginatedResponse<InventoryCheck>(
      data: mockData,
      currentPage: page,
      lastPage: 1,
      perPage: pageSize,
      total: mockData.length,
    );
  }

  /// 模拟盘点单详情数据
  InventoryCheck _getMockInventoryCheckDetail(int id) {
    return InventoryCheck(
      id: id,
      checkNo: 'CHK20241201${id.toString().padLeft(3, '0')}',
      storeId: 1,
      status: 0,
      operatorId: 1,
      totalCount: 120,
      checkedCount: 80,
      differenceCount: 3,
      remark: '月度盘点',
      createTime: DateTime.now().subtract(const Duration(days: 1)),
      updateTime: DateTime.now().subtract(const Duration(hours: 2)),
    );
  }

  /// 模拟统计数据
  InventoryCheckStatistics _getMockStatistics() {
    return const InventoryCheckStatistics(
      totalChecks: 5,
      ongoingChecks: 1,
      completedChecks: 4,
      cancelledChecks: 0,
      totalItems: 500,
      checkedItems: 450,
      differenceItems: 15,
      accuracyRate: 0.97,
    );
  }
}
