import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:gold_manager_flutter/core/utils/api_provider.dart';
import 'package:gold_manager_flutter/models/sales/sales_item_model.dart';

/// 销售订单服务
class SalesOrderService extends GetxService {
  final ApiProvider _apiProvider;
  
  SalesOrderService({ApiProvider? apiProvider})
      : _apiProvider = apiProvider ?? Get.find<ApiProvider>();
  
  /// 获取销售单列表
  Future<List<dynamic>> getSalesOrderList({
    int page = 1,
    int limit = 20,
    Map<String, dynamic>? filters,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };
      
      if (filters != null) {
        queryParams.addAll(filters);
      }
      
      final response = await _apiProvider.get(
        '/sales/order/index',
        queryParameters: queryParams,
      );
      
      if (response.statusCode == 200 && response.data['code'] == 0) {
        return response.data['data']['list'];
      } else {
        throw Exception(response.data['msg'] ?? '获取销售单列表失败');
      }
    } catch (e) {
      if (e is DioException) {
        throw Exception('网络错误：${e.message}');
      }
      rethrow;
    }
  }
  
  /// 获取销售单详情
  Future<dynamic> getSalesOrderDetail(int id) async {
    try {
      final response = await _apiProvider.get(
        '/sales/order/detail',
        queryParameters: {'id': id},
      );
      
      if (response.statusCode == 200 && response.data['code'] == 0) {
        return response.data['data'];
      } else {
        throw Exception(response.data['msg'] ?? '获取销售单详情失败');
      }
    } catch (e) {
      if (e is DioException) {
        throw Exception('网络错误：${e.message}');
      }
      rethrow;
    }
  }
  
  /// 获取销售单明细
  Future<List<SalesItem>> getSalesOrderItems(int orderId) async {
    try {
      final response = await _apiProvider.get(
        '/sales/order/items',
        queryParameters: {'order_id': orderId},
      );
      
      if (response.statusCode == 200 && response.data['code'] == 0) {
        final List<dynamic> data = response.data['data'];
        return data.map((json) => SalesItem.fromJson(json)).toList();
      } else {
        throw Exception(response.data['msg'] ?? '获取销售单明细失败');
      }
    } catch (e) {
      if (e is DioException) {
        throw Exception('网络错误：${e.message}');
      }
      rethrow;
    }
  }
} 