import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../models/print/print_template_config.dart';
import '../../../core/constants/border_styles.dart';

/// 坐标输入组件
///
/// 用于在配置区域中输入字段的X、Y坐标
class CoordinateInput extends StatefulWidget {
  /// 字段标签
  final String label;

  /// 当前位置
  final FieldPosition position;

  /// 位置变更回调
  final Function(FieldPosition position) onPositionChanged;

  /// 是否启用
  final bool enabled;

  /// 是否显示标签
  final bool showLabel;

  const CoordinateInput({
    super.key,
    required this.label,
    required this.position,
    required this.onPositionChanged,
    this.enabled = true,
    this.showLabel = true,
  });

  @override
  State<CoordinateInput> createState() => _CoordinateInputState();
}

class _CoordinateInputState extends State<CoordinateInput> {
  late String _xText;
  late String _yText;
  late FocusNode _xFocusNode;
  late FocusNode _yFocusNode;

  @override
  void initState() {
    super.initState();
    _xText = _formatValue(widget.position.x);
    _yText = _formatValue(widget.position.y);
    _xFocusNode = FocusNode();
    _yFocusNode = FocusNode();
  }

  @override
  void didUpdateWidget(CoordinateInput oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.position != widget.position) {
      // 只有在外部更改位置时才更新文本
      if (!_xFocusNode.hasFocus) {
        _xText = _formatValue(widget.position.x);
      }
      if (!_yFocusNode.hasFocus) {
        _yText = _formatValue(widget.position.y);
      }
    }
  }

  @override
  void dispose() {
    _xFocusNode.dispose();
    _yFocusNode.dispose();
    super.dispose();
  }

  /// 智能格式化：整数不显示小数点，小数保留1位
  String _formatValue(double val) {
    if (val == val.roundToDouble()) {
      // 整数，不显示小数点
      return val.round().toString();
    } else {
      // 小数，保留1位
      return val.toStringAsFixed(1);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.showLabel) ...[
          Text(
            '${widget.label}位置',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: widget.enabled ? Colors.grey[700] : Colors.grey[400],
            ),
          ),
          const SizedBox(height: 6),
        ],
        Row(
          children: [
            // X坐标输入
            Expanded(
              child: _buildCoordinateField(
                label: 'X',
                text: _xText,
                focusNode: _xFocusNode,
                onChanged: (value) {
                  setState(() {
                    _xText = value;
                  });
                  final numValue = double.tryParse(value) ?? 0.0;
                  widget.onPositionChanged(widget.position.copyWith(x: numValue));
                },
                onComplete: () {
                  if (_xText.isEmpty) {
                    setState(() {
                      _xText = '0';
                    });
                    widget.onPositionChanged(widget.position.copyWith(x: 0));
                  } else {
                    final numValue = double.tryParse(_xText) ?? 0.0;
                    setState(() {
                      _xText = _formatValue(numValue);
                    });
                  }
                },
              ),
            ),
            const SizedBox(width: 8),

            // Y坐标输入
            Expanded(
              child: _buildCoordinateField(
                label: 'Y',
                text: _yText,
                focusNode: _yFocusNode,
                onChanged: (value) {
                  setState(() {
                    _yText = value;
                  });
                  final numValue = double.tryParse(value) ?? 0.0;
                  widget.onPositionChanged(widget.position.copyWith(y: numValue));
                },
                onComplete: () {
                  if (_yText.isEmpty) {
                    setState(() {
                      _yText = '0';
                    });
                    widget.onPositionChanged(widget.position.copyWith(y: 0));
                  } else {
                    final numValue = double.tryParse(_yText) ?? 0.0;
                    setState(() {
                      _yText = _formatValue(numValue);
                    });
                  }
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建坐标输入字段
  Widget _buildCoordinateField({
    required String label,
    required String text,
    required FocusNode focusNode,
    required Function(String) onChanged,
    required VoidCallback onComplete,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '$label (mm)',
          style: TextStyle(
            fontSize: 11,
            color: widget.enabled ? Colors.grey[600] : Colors.grey[400],
          ),
        ),
        const SizedBox(height: 4),
        // 自定义输入框容器
        Container(
          height: 32,
          decoration: widget.enabled
              ? AppBorderStyles.standardBoxDecoration
              : AppBorderStyles.standardBoxDecoration.copyWith(
                  color: Colors.grey[50],
                  border: Border.all(color: Colors.grey[300]!, width: 1),
                ),
          child: _CustomNumberInput(
            text: text,
            focusNode: focusNode,
            enabled: widget.enabled,
            onChanged: onChanged,
            onComplete: onComplete,
            style: TextStyle(
              fontSize: 13,
              color: widget.enabled ? Colors.black87 : Colors.grey[400],
            ),
          ),
        ),
      ],
    );
  }
}

/// 自定义数字输入组件
/// 
/// 避免使用TextField的自动选择行为
class _CustomNumberInput extends StatefulWidget {
  final String text;
  final FocusNode focusNode;
  final bool enabled;
  final TextStyle style;
  final Function(String) onChanged;
  final VoidCallback onComplete;

  const _CustomNumberInput({
    required this.text,
    required this.focusNode,
    required this.enabled,
    required this.style,
    required this.onChanged,
    required this.onComplete,
  });

  @override
  State<_CustomNumberInput> createState() => _CustomNumberInputState();
}

class _CustomNumberInputState extends State<_CustomNumberInput> {
  late TextEditingController _controller;
  
  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.text);
    _controller.addListener(_handleTextChange);
  }
  
  @override
  void didUpdateWidget(_CustomNumberInput oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.text != widget.text && _controller.text != widget.text) {
      // 断开监听器以防止循环调用
      _controller.removeListener(_handleTextChange);
      _controller.text = widget.text;
      _controller.selection = TextSelection.collapsed(offset: widget.text.length);
      _controller.addListener(_handleTextChange);
    }
  }
  
  @override
  void dispose() {
    _controller.removeListener(_handleTextChange);
    _controller.dispose();
    super.dispose();
  }
  
  void _handleTextChange() {
    final text = _controller.text;
    // 仅在文本实际更改时调用
    if (text != widget.text) {
      widget.onChanged(text);
    }
  }
  
  @override
  Widget build(BuildContext context) {
    // 使用TextField替代EditableText，允许文本选择和更好的布局控制
    return TextField(
      controller: _controller,
      focusNode: widget.focusNode,
      enabled: widget.enabled,
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
        // 防止多个小数点
        TextInputFormatter.withFunction((oldValue, newValue) {
          final text = newValue.text;
          if (text.isEmpty) return newValue;

          // 检查小数点数量
          final dotCount = text.split('.').length - 1;
          if (dotCount > 1) {
            return oldValue;
          }

          // 检查是否为有效数字格式
          if (double.tryParse(text) == null && text != '.') {
            return oldValue;
          }

          return newValue;
        }),
      ],
      style: widget.style,
      decoration: const InputDecoration(
        // 禁用所有边框
        border: InputBorder.none,
        enabledBorder: InputBorder.none,
        focusedBorder: InputBorder.none,
        disabledBorder: InputBorder.none,
        // 调整内边距，确保垂直居中
        contentPadding: EdgeInsets.symmetric(vertical: 8),
        isDense: true,
      ),
      textAlign: TextAlign.center,
      onChanged: (text) {
        widget.onChanged(text);
      },
      onSubmitted: (text) {
        widget.onComplete();
      },
      onEditingComplete: () {
        widget.onComplete();
      },
      // 显式启用文本选择
      enableInteractiveSelection: true,
      // 禁用自动选择
      autofocus: false,
      // 防止自动格式化
      autocorrect: false,
      enableSuggestions: false,
    );
  }
}

/// 内联坐标输入组件
/// 
/// 用于在开关旁边显示的紧凑型坐标输入
class InlineCoordinateInput extends StatefulWidget {
  /// 当前位置
  final FieldPosition position;
  
  /// 位置变更回调
  final Function(FieldPosition position) onPositionChanged;
  
  /// 是否启用
  final bool enabled;

  const InlineCoordinateInput({
    super.key,
    required this.position,
    required this.onPositionChanged,
    this.enabled = true,
  });

  @override
  State<InlineCoordinateInput> createState() => _InlineCoordinateInputState();
}

class _InlineCoordinateInputState extends State<InlineCoordinateInput> {
  late String _xText;
  late String _yText;
  late FocusNode _xFocusNode;
  late FocusNode _yFocusNode;

  @override
  void initState() {
    super.initState();
    _xText = widget.position.x.toStringAsFixed(1);
    _yText = widget.position.y.toStringAsFixed(1);
    _xFocusNode = FocusNode();
    _yFocusNode = FocusNode();
  }

  @override
  void didUpdateWidget(InlineCoordinateInput oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.position != widget.position) {
      // 只有在外部更改位置时才更新文本
      if (!_xFocusNode.hasFocus) {
        _xText = widget.position.x.toStringAsFixed(1);
      }
      if (!_yFocusNode.hasFocus) {
        _yText = widget.position.y.toStringAsFixed(1);
      }
    }
  }

  @override
  void dispose() {
    _xFocusNode.dispose();
    _yFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        // X坐标
        SizedBox(
          width: 50,
          height: 24,
          child: _buildMiniField(
            label: 'X',
            text: _xText,
            focusNode: _xFocusNode,
            onChanged: (value) {
              setState(() {
                _xText = value;
              });
              final numValue = double.tryParse(value) ?? widget.position.x;
              widget.onPositionChanged(widget.position.copyWith(x: numValue));
            },
            onComplete: () {
              if (_xText.isEmpty) {
                setState(() {
                  _xText = '0.0';
                });
                widget.onPositionChanged(widget.position.copyWith(x: 0));
              } else {
                final numValue = double.tryParse(_xText) ?? widget.position.x;
                setState(() {
                  _xText = numValue.toStringAsFixed(1);
                });
              }
            },
          ),
        ),
        const SizedBox(width: 4),
        
        // Y坐标
        SizedBox(
          width: 50,
          height: 24,
          child: _buildMiniField(
            label: 'Y',
            text: _yText,
            focusNode: _yFocusNode,
            onChanged: (value) {
              setState(() {
                _yText = value;
              });
              final numValue = double.tryParse(value) ?? widget.position.y;
              widget.onPositionChanged(widget.position.copyWith(y: numValue));
            },
            onComplete: () {
              if (_yText.isEmpty) {
                setState(() {
                  _yText = '0.0';
                });
                widget.onPositionChanged(widget.position.copyWith(y: 0));
              } else {
                final numValue = double.tryParse(_yText) ?? widget.position.y;
                setState(() {
                  _yText = numValue.toStringAsFixed(1);
                });
              }
            },
          ),
        ),
      ],
    );
  }

  Widget _buildMiniField({
    required String label,
    required String text,
    required FocusNode focusNode,
    required Function(String) onChanged,
    required VoidCallback onComplete,
  }) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          color: focusNode.hasFocus 
              ? AppBorderStyles.focusBorderColor 
              : AppBorderStyles.borderColor,
          width: focusNode.hasFocus 
              ? AppBorderStyles.focusBorderWidth 
              : AppBorderStyles.borderWidth,
        ),
        borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
        color: widget.enabled ? Colors.white : Colors.grey[50],
      ),
      child: _CustomMiniNumberInput(
        text: text,
        focusNode: focusNode,
        enabled: widget.enabled,
        onChanged: onChanged,
        onComplete: onComplete,
        hintText: label,
      ),
    );
  }
}

/// 自定义迷你数字输入组件
class _CustomMiniNumberInput extends StatefulWidget {
  final String text;
  final String hintText;
  final FocusNode focusNode;
  final bool enabled;
  final Function(String) onChanged;
  final VoidCallback onComplete;

  const _CustomMiniNumberInput({
    required this.text,
    required this.hintText,
    required this.focusNode,
    required this.enabled,
    required this.onChanged,
    required this.onComplete,
  });

  @override
  State<_CustomMiniNumberInput> createState() => _CustomMiniNumberInputState();
}

class _CustomMiniNumberInputState extends State<_CustomMiniNumberInput> {
  late TextEditingController _controller;
  
  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.text);
    _controller.addListener(_handleTextChange);
  }
  
  @override
  void didUpdateWidget(_CustomMiniNumberInput oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.text != widget.text && _controller.text != widget.text) {
      // 断开监听器以防止循环调用
      _controller.removeListener(_handleTextChange);
      _controller.text = widget.text;
      _controller.selection = TextSelection.collapsed(offset: widget.text.length);
      _controller.addListener(_handleTextChange);
    }
  }
  
  @override
  void dispose() {
    _controller.removeListener(_handleTextChange);
    _controller.dispose();
    super.dispose();
  }
  
  void _handleTextChange() {
    final text = _controller.text;
    // 仅在文本实际更改时调用
    if (text != widget.text) {
      widget.onChanged(text);
    }
  }
  
  @override
  Widget build(BuildContext context) {
    // 使用TextField替代EditableText，允许文本选择和更好的布局控制
    return TextField(
      controller: _controller,
      focusNode: widget.focusNode,
      enabled: widget.enabled,
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
        // 防止多个小数点
        TextInputFormatter.withFunction((oldValue, newValue) {
          final text = newValue.text;
          if (text.isEmpty) return newValue;

          // 检查小数点数量
          final dotCount = text.split('.').length - 1;
          if (dotCount > 1) {
            return oldValue;
          }

          // 检查是否为有效数字格式
          if (double.tryParse(text) == null && text != '.') {
            return oldValue;
          }

          return newValue;
        }),
      ],
      style: TextStyle(
        fontSize: 9,
        color: widget.enabled ? Colors.black87 : Colors.grey[400],
      ),
      decoration: InputDecoration(
        // 禁用所有边框，因为容器已经有边框
        border: InputBorder.none,
        enabledBorder: InputBorder.none,
        focusedBorder: InputBorder.none,
        disabledBorder: InputBorder.none,
        // 调整内边距，确保垂直居中
        contentPadding: const EdgeInsets.symmetric(vertical: 2),
        isDense: true,
        hintText: widget.hintText,
        hintStyle: TextStyle(
          fontSize: 9,
          color: Colors.grey[400],
        ),
      ),
      textAlign: TextAlign.center,
      onChanged: (text) {
        widget.onChanged(text);
      },
      onSubmitted: (text) {
        widget.onComplete();
      },
      onEditingComplete: () {
        widget.onComplete();
      },
      // 显式启用文本选择
      enableInteractiveSelection: true,
      // 禁用自动选择
      autofocus: false,
      // 防止自动格式化
      autocorrect: false,
      enableSuggestions: false,
    );
  }
}
