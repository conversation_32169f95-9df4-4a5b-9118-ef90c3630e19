import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:get/get.dart';
import '../../../core/theme/app_theme.dart';
import 'metric_card.dart';
import 'chart_card.dart';
import 'chart_theme.dart';
import 'stat_item.dart';
import '../../../widgets/responsive_builder.dart';

/// 库存状况概览组件
/// 展示库存相关的核心指标和分布图表
class InventoryOverviewWidget extends StatelessWidget {
  /// 库存数据
  final InventoryOverviewData data;
  
  /// 是否正在加载
  final bool isLoading;
  
  /// 刷新回调
  final VoidCallback? onRefresh;

  const InventoryOverviewWidget({
    super.key,
    required this.data,
    this.isLoading = false,
    this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, sizingInformation) {
        if (sizingInformation.isMobile) {
          return _buildMobileLayout();
        } else if (sizingInformation.isTablet) {
          return _buildTabletLayout();
        } else {
          return _buildDesktopLayout();
        }
      },
    );
  }

  /// 桌面端布局
  Widget _buildDesktopLayout() {
    return Column(
      children: [
        // 核心指标卡片 - 4列布局
        Row(
          children: [
            Expanded(child: _buildTotalValueCard()),
            const SizedBox(width: 16),
            Expanded(child: _buildTotalItemsCard()),
            const SizedBox(width: 16),
            Expanded(child: _buildLowStockCard()),
            const SizedBox(width: 16),
            Expanded(child: _buildTurnoverRateCard()),
          ],
        ),
        const SizedBox(height: 24),
        
        // 图表和统计区域
        Row(
          children: [
            Expanded(flex: 2, child: _buildCategoryDistributionChart()),
            const SizedBox(width: 16),
            Expanded(child: _buildInventoryStatsCard()),
          ],
        ),
      ],
    );
  }

  /// 平板端布局
  Widget _buildTabletLayout() {
    return Column(
      children: [
        // 核心指标卡片 - 2行2列布局
        Row(
          children: [
            Expanded(child: _buildTotalValueCard()),
            const SizedBox(width: 16),
            Expanded(child: _buildTotalItemsCard()),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(child: _buildLowStockCard()),
            const SizedBox(width: 16),
            Expanded(child: _buildTurnoverRateCard()),
          ],
        ),
        const SizedBox(height: 24),
        
        // 图表区域
        _buildCategoryDistributionChart(),
        const SizedBox(height: 16),
        _buildInventoryStatsCard(),
      ],
    );
  }

  /// 移动端布局
  Widget _buildMobileLayout() {
    return Column(
      children: [
        // 核心指标卡片 - 垂直布局
        _buildTotalValueCard(),
        const SizedBox(height: 12),
        _buildTotalItemsCard(),
        const SizedBox(height: 12),
        _buildLowStockCard(),
        const SizedBox(height: 12),
        _buildTurnoverRateCard(),
        const SizedBox(height: 20),
        
        // 图表区域
        _buildCategoryDistributionChart(),
        const SizedBox(height: 16),
        _buildInventoryStatsCard(),
      ],
    );
  }

  /// 库存总值卡片
  Widget _buildTotalValueCard() {
    return DashboardMetricCard(
      title: '库存总值',
      value: data.totalValue.toStringAsFixed(0),
      unit: '元',
      icon: Icons.inventory,
      iconColor: AppTheme.primaryColor,
      valueColor: AppTheme.primaryColor,
      trendValue: data.valueTrend,
      trendDescription: '较上月',
      isLoading: isLoading,
      onTap: () {
        // TODO: 跳转到库存详情
      },
    );
  }

  /// 商品总数卡片
  Widget _buildTotalItemsCard() {
    return DashboardMetricCard(
      title: '商品总数',
      value: data.totalItems.toString(),
      unit: '件',
      icon: Icons.category,
      iconColor: AppTheme.infoColor,
      valueColor: AppTheme.infoColor,
      trendValue: data.itemsTrend,
      trendDescription: '较上月',
      isLoading: isLoading,
      onTap: () {
        // TODO: 跳转到商品列表
      },
    );
  }

  /// 低库存预警卡片
  Widget _buildLowStockCard() {
    return DashboardMetricCard(
      title: '低库存预警',
      value: data.lowStockCount.toString(),
      unit: '件',
      icon: Icons.warning,
      iconColor: AppTheme.warningColor,
      valueColor: AppTheme.warningColor,
      subtitle: '需要补货',
      isLoading: isLoading,
      onTap: () {
        // TODO: 跳转到低库存列表
      },
    );
  }

  /// 库存周转率卡片
  Widget _buildTurnoverRateCard() {
    return DashboardMetricCard(
      title: '库存周转率',
      value: data.turnoverRate.toStringAsFixed(1),
      unit: '次/月',
      icon: Icons.sync,
      iconColor: AppTheme.successColor,
      valueColor: AppTheme.successColor,
      trendValue: data.turnoverTrend,
      trendDescription: '较上月',
      isLoading: isLoading,
      onTap: () {
        // TODO: 跳转到周转率分析
      },
    );
  }

  /// 分类分布饼图
  Widget _buildCategoryDistributionChart() {
    return DashboardChartCard(
      title: '库存分类分布',
      subtitle: '按商品分类统计',
      icon: Icons.pie_chart,
      height: 300,
      isLoading: isLoading,
      showMoreButton: true,
      onMorePressed: () {
        // TODO: 跳转到分类详情
      },
      chart: isLoading ? const SizedBox.shrink() : _buildPieChart(),
    );
  }

  /// 库存统计卡片
  Widget _buildInventoryStatsCard() {
    return Container(
      height: 300,
      padding: const EdgeInsets.all(16),
      decoration: AppTheme.lightTheme.cardTheme.elevation != null
          ? BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            )
          : null,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Row(
            children: [
              Icon(Icons.analytics, size: 20, color: AppTheme.primaryColor),
              const SizedBox(width: 8),
              const Text(
                '库存统计',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // 统计项列表
          Expanded(
            child: isLoading 
                ? const Center(
                    child: CircularProgressIndicator(
                      color: AppTheme.primaryColor,
                    ),
                  )
                : _buildStatsList(),
          ),
        ],
      ),
    );
  }

  /// 构建饼图
  Widget _buildPieChart() {
    final sections = data.categoryDistribution.asMap().entries.map((entry) {
      final index = entry.key;
      final value = entry.value;
      final color = DashboardChartTheme.getChartColor(index);
      
      return DashboardChartTheme.createPieSection(
        value: value,
        title: '${value.toStringAsFixed(1)}%',
        color: color,
        radius: 60,
      );
    }).toList();

    return PieChart(
      DashboardChartTheme.getDefaultPieChartData(
        sections: sections,
        centerSpaceRadius: 40,
      ),
    );
  }

  /// 构建统计项列表
  Widget _buildStatsList() {
    final stats = [
      StatItemData(
        label: '入库数量',
        value: '${data.inboundCount}件',
        icon: Icons.input,
        color: AppTheme.successColor,
      ),
      StatItemData(
        label: '出库数量',
        value: '${data.outboundCount}件',
        icon: Icons.output,
        color: AppTheme.errorColor,
      ),
      StatItemData(
        label: '金重总计',
        value: '${data.totalGoldWeight.toStringAsFixed(2)}g',
        icon: Icons.monetization_on,
        color: Colors.amber[700]!,
      ),
      StatItemData(
        label: '银重总计',
        value: '${data.totalSilverWeight.toStringAsFixed(2)}g',
        icon: Icons.scale,
        color: Colors.grey[600]!,
      ),
      StatItemData(
        label: '待盘点',
        value: '${data.pendingCheckCount}件',
        icon: Icons.pending_actions,
        color: AppTheme.warningColor,
      ),
      StatItemData(
        label: '库存差异',
        value: '${data.differenceCount}件',
        icon: Icons.error_outline,
        color: AppTheme.errorColor,
      ),
    ];

    return ListView.separated(
      itemCount: stats.length,
      separatorBuilder: (context, index) => const SizedBox(height: 12),
      itemBuilder: (context, index) {
        final stat = stats[index];
        return DashboardStatItem(
          label: stat.label,
          value: stat.value,
          icon: stat.icon,
          color: stat.color,
          style: StatItemStyle.compact,
          showBorder: false,
        );
      },
    );
  }
}

/// 库存概览数据模型
class InventoryOverviewData {
  /// 库存总值
  final double totalValue;
  
  /// 库存总值趋势(百分比)
  final double valueTrend;
  
  /// 商品总数
  final int totalItems;
  
  /// 商品总数趋势(百分比)
  final double itemsTrend;
  
  /// 低库存商品数量
  final int lowStockCount;
  
  /// 库存周转率
  final double turnoverRate;
  
  /// 周转率趋势(百分比)
  final double turnoverTrend;
  
  /// 分类分布数据(百分比)
  final List<double> categoryDistribution;
  
  /// 入库数量
  final int inboundCount;
  
  /// 出库数量
  final int outboundCount;
  
  /// 金重总计
  final double totalGoldWeight;
  
  /// 银重总计
  final double totalSilverWeight;
  
  /// 待盘点数量
  final int pendingCheckCount;
  
  /// 库存差异数量
  final int differenceCount;

  const InventoryOverviewData({
    required this.totalValue,
    required this.valueTrend,
    required this.totalItems,
    required this.itemsTrend,
    required this.lowStockCount,
    required this.turnoverRate,
    required this.turnoverTrend,
    required this.categoryDistribution,
    required this.inboundCount,
    required this.outboundCount,
    required this.totalGoldWeight,
    required this.totalSilverWeight,
    required this.pendingCheckCount,
    required this.differenceCount,
  });

  /// 空数据
  static const InventoryOverviewData empty = InventoryOverviewData(
    totalValue: 0,
    valueTrend: 0,
    totalItems: 0,
    itemsTrend: 0,
    lowStockCount: 0,
    turnoverRate: 0,
    turnoverTrend: 0,
    categoryDistribution: [],
    inboundCount: 0,
    outboundCount: 0,
    totalGoldWeight: 0,
    totalSilverWeight: 0,
    pendingCheckCount: 0,
    differenceCount: 0,
  );

  /// 示例数据
  static const InventoryOverviewData sample = InventoryOverviewData(
    totalValue: 1250000,
    valueTrend: 5.2,
    totalItems: 2580,
    itemsTrend: 3.1,
    lowStockCount: 15,
    turnoverRate: 2.3,
    turnoverTrend: 8.5,
    categoryDistribution: [35.5, 28.2, 18.7, 12.3, 5.3],
    inboundCount: 156,
    outboundCount: 142,
    totalGoldWeight: 1250.5,
    totalSilverWeight: 850.2,
    pendingCheckCount: 8,
    differenceCount: 3,
  );
}
