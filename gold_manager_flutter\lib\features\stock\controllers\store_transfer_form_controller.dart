import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../services/auth_service.dart';
import '../../../core/utils/logger.dart';
import 'package:gold_manager_flutter/models/store/store.dart';
import 'package:gold_manager_flutter/services/store_service.dart';
import '../models/store_transfer.dart';
import 'package:gold_manager_flutter/features/stock/services/store_transfer_service.dart';
import '../../../models/jewelry/jewelry.dart';
import '../../../models/common/enums.dart';
import '../../../models/recycling/old_material_item.dart';
import '../widgets/old_material_recycling_dialog.dart';
import '../widgets/single_work_fee_dialog.dart';
import '../widgets/store_transfer_payment_dialog.dart';

/// 新建调拨对话框控制器
class StoreTransferFormController extends GetxController {
  final StoreTransferService _transferService =
      Get.find<StoreTransferService>();
  final StoreService _storeService = Get.find<StoreService>();
  final AuthService _authService = Get.find<AuthService>();

  // 表单相关
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  final TextEditingController barcodeController = TextEditingController();
  final TextEditingController remarkController = TextEditingController();

  // 响应式变量
  final RxList<Store> storeList = <Store>[].obs;
  final RxList<StoreTransferItem> transferItems = <StoreTransferItem>[].obs;
  final RxBool isLoading = false.obs;
  final RxInt sourceStoreId = 0.obs;
  final RxInt targetStoreId = 0.obs;
  final RxDouble totalAmount = 0.0.obs;
  final RxInt totalCount = 0.obs;

  // 收款状态追踪
  final RxBool isTransferSaved = false.obs; // 调拨单是否已保存成功

  @override
  void onInit() {
    super.onInit();
    _initializeData();
  }

  @override
  void onClose() {
    barcodeController.dispose();
    remarkController.dispose();
    super.onClose();
  }

  /// 初始化数据
  Future<void> _initializeData() async {
    await loadStoreList();
    _setDefaultStores();
  }

  /// 加载门店列表
  Future<void> loadStoreList() async {
    try {
      LoggerService.d('📋 加载门店列表');
      final stores = await _storeService.getAllStores();
      storeList.value = stores;
      LoggerService.d('✅ 门店列表加载成功: ${stores.length}个门店');
    } catch (e) {
      LoggerService.e('❌ 加载门店列表失败', e);
      Get.snackbar(
        '错误',
        '加载门店列表失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 设置默认门店
  void _setDefaultStores() {
    if (_authService.userRole.value != 'admin' &&
        _authService.storeId.value > 0) {
      // 员工默认选择自己的门店作为源门店
      sourceStoreId.value = _authService.storeId.value;
    }
  }

  /// 扫描商品条码
  Future<void> scanBarcode(String barcode) async {
    if (barcode.trim().isEmpty) {
      Get.snackbar(
        '提示',
        '请输入有效的商品条码',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      return;
    }

    // 验证门店选择
    if (sourceStoreId.value == 0 || targetStoreId.value == 0) {
      Get.snackbar(
        '提示',
        '请先选择源门店和目标门店',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      return;
    }

    if (sourceStoreId.value == targetStoreId.value) {
      Get.snackbar(
        '提示',
        '源门店和目标门店不能相同',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      return;
    }

    // 检查是否已经添加过该条码
    final existingIndex = transferItems.indexWhere(
      (item) => item.barcode == barcode,
    );
    if (existingIndex != -1) {
      Get.snackbar(
        '提示',
        '该商品已添加到调拨单中',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      return;
    }

    try {
      // 🔧 修复：移除全局loading状态，避免页面刷新
      LoggerService.d('🔍 验证商品调拨: $barcode');

      // 调用验证接口
      final result = await _transferService.validateJewelry(
        barcode: barcode,
        fromStoreId: sourceStoreId.value,
        toStoreId: targetStoreId.value,
      );

      if (result.isValid) {
        // 创建调拨明细项
        final transferItem = StoreTransferItem(
          id: 0,
          transferId: 0,
          jewelryId: result.jewelry?.id ?? 0,
          barcode: barcode,
          transferPrice: result.suggestedPrice,
          transferType: result.transferType,
          jewelry: result.jewelry,
        );

        // 添加到列表（局部更新）
        transferItems.add(transferItem);

        // 清空扫描框
        barcodeController.clear();

        // 更新汇总信息（局部更新）
        _updateSummary();

        Get.snackbar(
          '成功',
          '已添加商品：${result.jewelry?.name ?? barcode}',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );

        LoggerService.d('✅ 商品添加成功: ${result.jewelry?.name ?? barcode}');
      } else {
        Get.snackbar(
          '错误',
          result.message,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      LoggerService.e('❌ 验证商品调拨失败', e);

      String errorMessage = '商品验证失败';

      // 🎯 优化错误提示信息，参考出库单的错误处理
      if (e.toString().contains('该商品在当前门店中不存在')) {
        errorMessage = '该商品在源门店中不存在，请检查条码是否正确';
      } else if (e.toString().contains('404') ||
          e.toString().contains('商品不存在')) {
        errorMessage = '未找到条码为 $barcode 的商品，请检查条码是否正确';
      } else if (e.toString().contains('403') ||
          e.toString().contains('无权访问')) {
        errorMessage = '无权访问其他门店的商品，请联系管理员';
      } else if (e.toString().contains('400') ||
          e.toString().contains('商品状态不可用')) {
        errorMessage = '商品状态不可用，只有上架在售的商品才能调拨';
      } else if (e.toString().contains('网络')) {
        errorMessage = '网络连接失败，请检查网络设置';
      } else {
        errorMessage = '商品验证失败: ${e.toString()}';
      }

      Get.snackbar(
        '错误',
        errorMessage,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        duration: const Duration(seconds: 5),
      );
    }
    // 🔧 修复：移除finally中的isLoading设置，避免页面刷新
  }

  /// 移除调拨商品
  void removeTransferItem(int index) {
    if (index >= 0 && index < transferItems.length) {
      transferItems.removeAt(index);
      _updateSummary();

      Get.snackbar(
        '成功',
        '商品已从调拨单中移除',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    }
  }

  /// 更新汇总信息
  void _updateSummary() {
    totalCount.value = transferItems.length;
    totalAmount.value = transferItems.fold(
      0.0,
      (sum, item) => sum + item.transferPrice,
    );
  }

  /// 更新单个商品的字段 - 完全复制出库单的实现
  void updateItemField(
    int index, {
    double? goldPrice,
    double? silverPrice,
    double? workPrice,
    double? pieceWorkPrice,
    double? amount,
  }) {
    if (index < 0 || index >= transferItems.length) {
      return;
    }

    final item = transferItems[index];
    final jewelry = item.jewelry;

    if (jewelry == null) {
      return;
    }

    // 创建更新后的珠宝对象（使用批发工费）
    final updatedJewelry = jewelry.copyWith(
      goldPrice: goldPrice ?? jewelry.goldPrice,
      silverPrice: silverPrice ?? jewelry.silverPrice,
      wholesaleWorkPrice: workPrice ?? jewelry.wholesaleWorkPrice,
      pieceWorkPrice: pieceWorkPrice ?? jewelry.pieceWorkPrice,
    );

    // 计算新的调拨价格
    double newTransferPrice;
    if (amount != null) {
      // 如果直接指定了金额，使用指定的金额
      newTransferPrice = amount;
    } else {
      // 否则根据公式计算
      newTransferPrice = _calculateTransferPrice(updatedJewelry);
    }

    // 更新调拨明细
    transferItems[index] = item.copyWith(
      transferPrice: newTransferPrice,
      jewelry: updatedJewelry,
    );

    // 更新汇总信息
    _updateSummary();
  }

  /// 批量更新价格
  Future<void> batchUpdatePrices({
    double? goldPrice,
    double? silverPrice,
    double? workPrice,
    double? pieceWorkPrice,
  }) async {
    if (transferItems.isEmpty) {
      Get.snackbar(
        '提示',
        '当前没有商品，无法进行批量调价',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      return;
    }

    try {
      // 显示加载对话框
      Get.dialog(
        const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('正在批量更新价格...'),
            ],
          ),
        ),
        barrierDismissible: false,
      );

      LoggerService.d('🔄 开始批量更新调拨价格');
      int updatedCount = 0;

      // 遍历所有调拨商品进行批量更新
      for (int i = 0; i < transferItems.length; i++) {
        final item = transferItems[i];
        final jewelry = item.jewelry;

        if (jewelry != null) {
          // 创建更新后的珠宝对象，只更新非空的价格字段（使用批发工费）
          final updatedJewelry = jewelry.copyWith(
            goldPrice: goldPrice ?? jewelry.goldPrice,
            silverPrice: silverPrice ?? jewelry.silverPrice,
            wholesaleWorkPrice: workPrice ?? jewelry.wholesaleWorkPrice,
            pieceWorkPrice: pieceWorkPrice ?? jewelry.pieceWorkPrice,
          );

          // 重新计算调拨价格
          final newPrice = _calculateTransferPrice(updatedJewelry);

          // 更新调拨明细
          transferItems[i] = item.copyWith(
            transferPrice: newPrice,
            jewelry: updatedJewelry,
          );

          updatedCount++;
        }
      }

      // 重新计算总计
      _updateSummary();

      // 关闭加载对话框
      if (Get.isDialogOpen == true) {
        Get.back();
      }

      // 显示成功提示
      Get.snackbar(
        '批量调价成功',
        '已成功更新 $updatedCount 件商品的调拨价格',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );

      LoggerService.d('✅ 批量调价完成，共更新 $updatedCount 件商品');
    } catch (e) {
      // 关闭加载对话框
      if (Get.isDialogOpen == true) {
        Get.back();
      }

      LoggerService.e('❌ 批量调价失败', e);
      Get.snackbar(
        '批量调价失败',
        '更新价格时发生错误: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        duration: const Duration(seconds: 5),
      );
    }
  }

  /// 计算调拨价格（使用批发工费）
  double _calculateTransferPrice(jewelry) {
    return (jewelry.goldWeight * jewelry.goldPrice) +
        (jewelry.silverWeight * jewelry.silverPrice) +
        (jewelry.totalWeight * jewelry.wholesaleWorkPrice) +
        jewelry.pieceWorkPrice;
  }

  /// 保存草稿
  Future<void> saveDraft() async {
    await _saveTransfer(status: 0);
  }

  /// 提交审核 - 修改为弹出收款对话框
  Future<void> submitForAudit() async {
    // 先验证表单
    if (!formKey.currentState!.validate()) {
      return;
    }

    if (sourceStoreId.value == 0 || targetStoreId.value == 0) {
      Get.snackbar(
        '错误',
        '请选择源门店和目标门店',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    if (transferItems.isEmpty) {
      Get.snackbar(
        '错误',
        '请添加调拨商品',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    // 弹出收款结算对话框
    await _showPaymentDialog();
  }

  /// 保存调拨单
  Future<void> _saveTransfer({required int status}) async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    if (sourceStoreId.value == 0 || targetStoreId.value == 0) {
      Get.snackbar(
        '错误',
        '请选择源门店和目标门店',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    if (transferItems.isEmpty) {
      Get.snackbar(
        '错误',
        '请添加调拨商品',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    try {
      // 🔧 修复：使用局部loading状态，避免页面刷新
      Get.dialog(
        const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('正在保存调拨单...'),
            ],
          ),
        ),
        barrierDismissible: false,
      );

      // 构建调拨商品列表数据
      final items = transferItems
          .map(
            (item) => {
              'jewelry_id': item.jewelryId,
              'transfer_price': item.transferPrice,
              'gold_price': item.jewelry?.goldPrice ?? 0.0,
              'silver_price': item.jewelry?.silverPrice ?? 0.0,
              'total_weight': item.jewelry?.totalWeight ?? 0.0,
              'silver_work_price':
                  item.jewelry?.wholesaleWorkPrice ??
                  0.0, // 将批发工费映射到silver_work_price字段
              'piece_work_price': item.jewelry?.pieceWorkPrice ?? 0.0,
            },
          )
          .toList();

      // 调用API保存
      final result = await _transferService.createTransfer(
        fromStoreId: sourceStoreId.value,
        toStoreId: targetStoreId.value,
        items: items,
        remark: remarkController.text.isNotEmpty ? remarkController.text : null,
        adminId: _authService.userId.value,
      );

      // 关闭loading对话框
      if (Get.isDialogOpen == true) {
        Get.back();
      }

      if (result != null) {
        Get.snackbar(
          '成功',
          status == 0 ? '调拨单草稿保存成功' : '调拨单提交审核成功',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
        Get.back(result: true);
      } else {
        throw Exception('创建调拨单失败：服务器返回空结果');
      }
    } catch (e) {
      // 关闭loading对话框
      if (Get.isDialogOpen == true) {
        Get.back();
      }

      LoggerService.e('保存调拨单失败', e);
      Get.snackbar(
        '错误',
        '保存调拨单失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
    // 🔧 修复：移除finally中的isLoading设置，避免页面刷新
  }

  /// 获取用户可见的门店列表
  List<Store> get availableStores {
    if (_authService.userRole.value == 'admin') {
      return storeList;
    } else {
      // 员工只能看到自己的门店
      return storeList
          .where((store) => store.id == _authService.storeId.value)
          .toList();
    }
  }

  /// 旧料回收功能
  /// 打开旧料回收对话框，收集回收信息并添加到调拨单
  Future<void> handleOldMaterialRecycling() async {
    try {
      LoggerService.d('🔄 打开旧料回收对话框');

      // 🔒 前置条件验证：必须先选择门店
      if (sourceStoreId.value == 0 || targetStoreId.value == 0) {
        Get.snackbar(
          '提示',
          '请先选择源门店和目标门店，然后再进行旧料回收',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.orange,
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
        );
        return;
      }

      // 显示旧料回收对话框
      final result = await Get.dialog<OldMaterialItem>(
        const OldMaterialRecyclingDialog(),
        barrierDismissible: false,
      );

      if (result != null) {
        LoggerService.d('✅ 用户确认旧料回收: ${result.toString()}');

        // 将旧料回收项转换为Jewelry对象
        final jewelry = result.toJewelry();

        // 创建调拨明细项（价格为负数表示回收支出）
        final newItem = StoreTransferItem(
          id: 0,
          transferId: 0,
          jewelryId: 0, // 旧料回收项没有真实的首饰ID
          barcode: result.barcode,
          transferPrice: -result.payableAmount, // 负价格表示回收支出
          transferType: 'recycling',
          jewelry: jewelry,
        );

        // 添加到调拨单列表
        transferItems.add(newItem);

        // 重新计算总计
        _updateSummary();

        // 显示成功提示
        Get.snackbar(
          '成功',
          '已添加旧料回收：${result.remark ?? '旧料回收'} (应付: ${result.payableAmount.round()}元)',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.purple,
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
        );

        LoggerService.d('✅ 旧料回收项已添加到调拨单');
      } else {
        LoggerService.d('❌ 用户取消了旧料回收');
      }
    } catch (e) {
      LoggerService.e('旧料回收处理失败', e);
      Get.snackbar(
        '错误',
        '旧料回收处理失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 单收工费功能
  void handleSingleWorkFee() {
    // 🔒 前置条件验证：必须先选择门店
    if (sourceStoreId.value == 0 || targetStoreId.value == 0) {
      Get.snackbar(
        '提示',
        '请先选择源门店和目标门店，然后再添加工费项目',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );
      return;
    }

    _showSingleWorkFeeDialog();
  }

  /// 显示单收工费对话框
  void _showSingleWorkFeeDialog() {
    Get.dialog(
      SingleWorkFeeDialog(
        onConfirm: (workFeeData) {
          _addWorkFeeItem(
            barcode: workFeeData['barcode'],
            name: workFeeData['name'],
            chargeType: workFeeData['chargeType'],
            goldWeight: workFeeData['goldWeight'],
            silverWeight: workFeeData['silverWeight'],
            workPrice: workFeeData['workPrice'],
            discount: workFeeData['discount'],
            amount: workFeeData['amount'],
          );
        },
      ),
      barrierDismissible: false,
    );
  }

  /// 添加工费项目到调拨单
  void _addWorkFeeItem({
    required String barcode,
    required String name,
    required String chargeType,
    required double goldWeight,
    required double silverWeight,
    required double workPrice,
    required double discount,
    required double amount,
  }) {
    try {
      // 验证必填字段
      if (name.trim().isEmpty) {
        Get.snackbar(
          '提示',
          '请输入商品名称',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.orange,
          colorText: Colors.white,
        );
        return;
      }

      if (amount <= 0) {
        Get.snackbar(
          '提示',
          '工费金额必须大于0',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.orange,
          colorText: Colors.white,
        );
        return;
      }

      // 创建工费项目的Jewelry对象
      final workFeeJewelry = Jewelry(
        id: -1, // 工费项目使用特殊ID
        barcode: barcode,
        name: '工费-$name', // 添加工费前缀
        categoryId: 0,
        storeId: sourceStoreId.value,
        goldWeight: goldWeight,
        silverWeight: silverWeight,
        totalWeight: goldWeight + silverWeight,
        goldPrice: 0.0,
        silverPrice: 0.0,
        workPrice: 0.0, // 银工费设为0
        pieceWorkPrice: 0.0,
        retailWorkPrice: 0.0,
        wholesaleWorkPrice: workPrice, // 批发工费设为工费值
        salePrice: amount, // 使用计算后的金额作为销售价格
        status: JewelryStatus.onShelf,
        createTime: DateTime.now(),
      );

      // 创建调拨明细项
      final workFeeItem = StoreTransferItem(
        id: 0,
        transferId: 0,
        jewelryId: -1, // 工费项目使用特殊jewelryId
        barcode: barcode,
        transferPrice: amount,
        transferType: 'work_fee',
        jewelry: workFeeJewelry,
      );

      // 添加到调拨单列表
      transferItems.add(workFeeItem);

      // 重新计算总计
      _updateSummary();

      // 显示成功提示
      Get.snackbar(
        '成功',
        '已添加工费项目：$name (${amount.round()}元)',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );

      LoggerService.d('✅ 成功添加工费项目: $name, 金额: ${amount.round()}元');
    } catch (e) {
      LoggerService.e('❌ 添加工费项目失败', e);
      Get.snackbar(
        '错误',
        '添加工费项目失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 检查是否为工费项目
  /// 工费项目的识别标准：jewelryId为-1，商品名称包含"工费-"
  bool isWorkFeeItem(StoreTransferItem item) {
    return item.jewelryId == -1 &&
        (item.jewelry?.name.startsWith('工费-') == true);
  }

  /// 检查是否为旧料回收项目
  /// 旧料回收项目的识别标准：jewelryId为0，价格为负数，条码以9开头
  bool isRecyclingItem(StoreTransferItem item) {
    return item.jewelryId == 0 &&
        item.transferPrice < 0 &&
        item.barcode.startsWith('9');
  }

  /// 显示收款结算对话框
  Future<void> _showPaymentDialog() async {
    try {
      LoggerService.d('🔄 显示调拨收款结算对话框');

      // 弹出收款对话框
      await Get.dialog(
        StoreTransferPaymentDialog(
          totalAmount: totalAmount.value,
          onPaymentConfirmed: (paymentData) async {
            // 处理收款确认
            await _handlePaymentConfirmed(paymentData);
          },
          transferData: {
            'from_store_id': sourceStoreId.value,
            'to_store_id': targetStoreId.value,
            'remark': remarkController.text,
          },
          itemsData: transferItems
              .map(
                (item) => {
                  'jewelry_id': item.jewelryId,
                  'barcode': item.barcode,
                  'transfer_price': item.transferPrice,
                },
              )
              .toList(),
          formController: this, // 🔧 修复：传递当前控制器引用
        ),
        barrierDismissible: false,
      );
    } catch (e) {
      LoggerService.e('显示收款对话框失败', e);
      Get.snackbar(
        '错误',
        '显示收款对话框失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 处理收款确认
  Future<void> _handlePaymentConfirmed(Map<String, dynamic> paymentData) async {
    try {
      LoggerService.d('🔄 处理调拨收款确认');

      // 先保存调拨单（状态为待审核）
      final transferId = await _saveTransferAndGetId();

      if (transferId != null && transferId > 0) {
        // 调用收款API，传递当前用户ID作为审核员ID
        await _transferService.updatePayment(
          transferId,
          paymentData,
          _authService.userId.value,
        );

        LoggerService.d('✅ 调拨收款处理成功');

        // 🔧 修复：标记调拨单已保存成功
        isTransferSaved.value = true;

        // 显示成功提示并返回
        Get.snackbar(
          '收款成功',
          '调拨单已创建并完成收款，状态已更新为审核通过',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
        );

        // 返回上一页
        Get.back(result: true);
      } else {
        // 创建调拨单失败
        throw Exception('创建调拨单失败，无法进行收款操作');
      }
    } catch (e) {
      LoggerService.e('处理收款确认失败', e);
      Get.snackbar(
        '收款失败',
        '处理收款确认失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 保存调拨单并返回ID
  Future<int?> _saveTransferAndGetId() async {
    try {
      // 构建调拨商品列表数据
      final items = transferItems
          .map(
            (item) => {
              'jewelry_id': item.jewelryId,
              'transfer_price': item.transferPrice,
              'gold_price': item.jewelry?.goldPrice ?? 0.0,
              'silver_price': item.jewelry?.silverPrice ?? 0.0,
              'total_weight': item.jewelry?.totalWeight ?? 0.0,
              'silver_work_price': item.jewelry?.wholesaleWorkPrice ?? 0.0,
              'piece_work_price': item.jewelry?.pieceWorkPrice ?? 0.0,
            },
          )
          .toList();

      // 调用API保存（状态为待审核）
      final result = await _transferService.createTransfer(
        fromStoreId: sourceStoreId.value,
        toStoreId: targetStoreId.value,
        items: items,
        remark: remarkController.text.isNotEmpty ? remarkController.text : null,
        adminId: _authService.userId.value,
      );

      return result?['id'];
    } catch (e) {
      LoggerService.e('保存调拨单失败', e);
      rethrow;
    }
  }

  /// 重置调拨单表单
  /// 用于收款成功后清空页面数据，重新开始新的调拨单
  void resetTransferForm() {
    try {
      LoggerService.d('🔄 重置调拨单表单');

      // 先停止任何加载状态
      isLoading.value = false;

      // 清空商品列表
      transferItems.clear();

      // 重置金额统计
      totalAmount.value = 0.0;
      totalCount.value = 0;

      // 清空输入框
      barcodeController.clear();
      remarkController.clear();

      // 重置门店选择（保持用户权限相关的默认设置）
      if (_authService.userRole.value != 'admin' &&
          _authService.storeId.value > 0) {
        // 员工保持自己的门店作为源门店
        sourceStoreId.value = _authService.storeId.value;
        targetStoreId.value = 0;
      } else {
        // 管理员重置所有门店选择
        sourceStoreId.value = 0;
        targetStoreId.value = 0;
      }

      // 重置保存状态
      isTransferSaved.value = false;

      // 刷新UI（强制重新构建）
      update();

      LoggerService.d('✅ 调拨单表单重置完成');
    } catch (e) {
      LoggerService.e('❌ 重置调拨单表单失败', e);
      // 即使重置失败，也要确保基本状态被清理
      isLoading.value = false;
      isTransferSaved.value = false;
    }
  }
}
