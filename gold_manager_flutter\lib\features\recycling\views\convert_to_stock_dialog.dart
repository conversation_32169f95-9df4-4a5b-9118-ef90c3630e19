/// 转库存对话框
/// 将处理结果转为库存

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../../../core/constants/border_styles.dart';
import '../../../core/widgets/custom_card.dart';
import '../../../core/widgets/custom_text_field.dart';
import '../../../core/widgets/custom_dropdown.dart';
import '../../../models/recycling/recycling_process_new.dart';
import '../../../models/store/store.dart';
import '../../../services/store_service.dart';
import '../../../services/auth_service.dart';
import '../../../core/utils/number_formatter.dart';

class ConvertToStockDialog extends StatefulWidget {
  final RecyclingProcessNew process;
  final Function(Map<String, dynamic>) onConfirm;

  const ConvertToStockDialog({
    Key? key,
    required this.process,
    required this.onConfirm,
  }) : super(key: key);

  @override
  State<ConvertToStockDialog> createState() => _ConvertToStockDialogState();
}

class _ConvertToStockDialogState extends State<ConvertToStockDialog> {
  final StoreService _storeService = Get.find<StoreService>();
  final AuthService _authService = Get.find<AuthService>();

  final _formKey = GlobalKey<FormState>();
  
  // 表单数据
  ProcessResult? selectedResult;
  int storeId = 0;
  StockType stockType = StockType.material;
  int quantity = 1;
  int materialType = 1; // 1=黄金，2=白银，3=其他
  final remarkController = TextEditingController();
  
  // 商品库存相关
  final jewelryNameController = TextEditingController();
  final goldPriceController = TextEditingController();
  final workPriceController = TextEditingController();
  final salePriceController = TextEditingController();
  
  List<Store> stores = [];
  
  @override
  void initState() {
    super.initState();
    _loadStores();
    _initDefaultValues();
  }

  @override
  void dispose() {
    remarkController.dispose();
    jewelryNameController.dispose();
    goldPriceController.dispose();
    workPriceController.dispose();
    salePriceController.dispose();
    super.dispose();
  }

  void _loadStores() async {
    stores = await _storeService.getAllStores();
    if (mounted) {
      setState(() {
        if (!_authService.hasPermission('super.admin') && _authService.storeId.value > 0) {
          storeId = _authService.storeId.value;
        }
      });
    }
  }

  void _initDefaultValues() {
    // 选择第一个未转换的结果
    final unconvertedResults = widget.process.results
        ?.where((r) => !r.isConverted)
        .toList() ?? [];
    
    if (unconvertedResults.isNotEmpty) {
      selectedResult = unconvertedResults.first;
      
      // 根据结果类型设置默认值
      switch (selectedResult!.resultType) {
        case ResultType.pureGold:
          materialType = 1;
          break;
        case ResultType.pureSilver:
          materialType = 2;
          break;
        case ResultType.finishedJewelry:
          stockType = StockType.jewelry;
          jewelryNameController.text = selectedResult!.name;
          break;
        case ResultType.bullion:
          materialType = selectedResult!.name.contains('金') ? 1 : 2;
          break;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
      ),
      child: Container(
        width: 700,
        constraints: const BoxConstraints(maxHeight: 600),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildHeader(),
              Flexible(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildResultSelector(),
                      const SizedBox(height: 24),
                      _buildBasicInfo(),
                      const SizedBox(height: 24),
                      if (stockType == StockType.jewelry)
                        _buildJewelryInfo()
                      else
                        _buildMaterialInfo(),
                      const SizedBox(height: 24),
                      _buildRemark(),
                    ],
                  ),
                ),
              ),
              _buildFooter(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.purple.shade50,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(AppBorderStyles.borderRadius),
          topRight: Radius.circular(AppBorderStyles.borderRadius),
        ),
      ),
      child: Row(
        children: [
          Icon(Icons.inventory, color: Colors.purple.shade700),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              '转库存 - ${widget.process.processNo}',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.purple.shade700,
              ),
            ),
          ),
          IconButton(
            onPressed: () => Get.back(),
            icon: const Icon(Icons.close),
            tooltip: '关闭',
          ),
        ],
      ),
    );
  }

  Widget _buildResultSelector() {
    final unconvertedResults = widget.process.results
        ?.where((r) => !r.isConverted)
        .toList() ?? [];

    return CustomCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '选择处理结果',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<ProcessResult>(
              value: selectedResult,
              decoration: const InputDecoration(
                labelText: '处理结果',
                border: OutlineInputBorder(),
              ),
              items: unconvertedResults.map((result) => DropdownMenuItem(
                value: result,
                child: Text('${result.name} - ${result.weight.toStringAsFixed(2)}g'),
              )).toList(),
              onChanged: (value) {
                setState(() {
                  selectedResult = value;
                  if (value != null) {
                    // 根据结果类型自动调整
                    switch (value.resultType) {
                      case ResultType.finishedJewelry:
                        stockType = StockType.jewelry;
                        jewelryNameController.text = value.name;
                        break;
                      case ResultType.pureGold:
                      case ResultType.pureSilver:
                      case ResultType.bullion:
                        stockType = StockType.material;
                        break;
                    }
                  }
                });
              },
              validator: (value) => value == null ? '请选择处理结果' : null,
            ),
            if (selectedResult != null) ...[
              const SizedBox(height: 12),
              Wrap(
                spacing: 16,
                runSpacing: 8,
                children: [
                  _buildInfoChip('类型', selectedResult!.resultType.label),
                  _buildInfoChip('重量', '${selectedResult!.weight.toStringAsFixed(2)} g'),
                  if (selectedResult!.purity != null)
                    _buildInfoChip('纯度', '${selectedResult!.purity!.toStringAsFixed(1)}%'),
                  _buildInfoChip('成本', '¥${selectedResult!.totalCost.toStringAsFixed(2)}'),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInfoChip(String label, String value) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(AppBorderStyles.smallBorderRadius),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            '$label: ',
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBasicInfo() {
    return CustomCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '基本信息',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: CustomDropdown<int>(
                    value: storeId,
                    items: [
                      const DropdownMenuItem(value: 0, child: Text('请选择门店')),
                      ...stores.map((store) => DropdownMenuItem(
                        value: store.id,
                        child: Text(store.name),
                      )),
                    ],
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          storeId = value;
                        });
                      }
                    },
                    hint: '入库门店',
                    validator: (value) => value == null || value == 0 ? '请选择入库门店' : null,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: CustomDropdown<StockType>(
                    value: stockType,
                    items: StockType.values.map((type) => DropdownMenuItem(
                      value: type,
                      child: Text(type.label),
                    )).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          stockType = value;
                        });
                      }
                    },
                    hint: '库存类型',
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: CustomTextField(
                    initialValue: quantity.toString(),
                    label: '数量',
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    onChanged: (value) {
                      quantity = int.tryParse(value) ?? 1;
                    },
                    validator: (value) {
                      final qty = int.tryParse(value ?? '');
                      if (qty == null || qty <= 0) return '请输入有效数量';
                      return null;
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildJewelryInfo() {
    return CustomCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '商品信息',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            CustomTextField(
              controller: jewelryNameController,
              label: '商品名称',
              validator: (value) => value?.isEmpty ?? true ? '请输入商品名称' : null,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: CustomTextField(
                    controller: goldPriceController,
                    label: '金价(元/g)',
                    keyboardType: const TextInputType.numberWithOptions(decimal: true),
                    inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}'))],
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: CustomTextField(
                    controller: workPriceController,
                    label: '工费(元/g)',
                    keyboardType: const TextInputType.numberWithOptions(decimal: true),
                    inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}'))],
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: CustomTextField(
                    controller: salePriceController,
                    label: '销售价格',
                    keyboardType: const TextInputType.numberWithOptions(decimal: true),
                    inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}'))],
                    validator: (value) {
                      final price = double.tryParse(value ?? '');
                      if (price == null || price <= 0) return '请输入销售价格';
                      return null;
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMaterialInfo() {
    return CustomCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '原料信息',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            CustomDropdown<int>(
              value: materialType,
              items: const [
                DropdownMenuItem(value: 1, child: Text('黄金')),
                DropdownMenuItem(value: 2, child: Text('白银')),
                DropdownMenuItem(value: 3, child: Text('其他')),
              ],
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    materialType = value;
                  });
                }
              },
              hint: '原料类型',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRemark() {
    return CustomCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: CustomTextField(
          controller: remarkController,
          label: '备注',
          maxLines: 3,
        ),
      ),
    );
  }

  Widget _buildFooter() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(AppBorderStyles.borderRadius),
          bottomRight: Radius.circular(AppBorderStyles.borderRadius),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          ElevatedButton(
            onPressed: () => Get.back(),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.grey,
            ),
            child: const Text('取消'),
          ),
          const SizedBox(width: 16),
          ElevatedButton(
            onPressed: _onConfirm,
            child: const Text('确认转库存'),
          ),
        ],
      ),
    );
  }

  void _onConfirm() {
    if (!_formKey.currentState!.validate()) return;

    if (selectedResult == null) {
      Get.snackbar('提示', '请选择处理结果');
      return;
    }

    final params = <String, dynamic>{
      'resultId': selectedResult!.id,
      'storeId': storeId,
      'stockType': stockType,
      'quantity': quantity,
      'remark': remarkController.text.isNotEmpty ? remarkController.text : null,
    };

    if (stockType == StockType.jewelry) {
      params['jewelryInfo'] = {
        'name': jewelryNameController.text,
        'category_id': 1, // 默认分类，实际应该让用户选择
        'gold_price': double.tryParse(goldPriceController.text) ?? 0,
        'work_price': double.tryParse(workPriceController.text) ?? 0,
        'sale_price': double.tryParse(salePriceController.text) ?? 0,
      };
    } else {
      params['materialType'] = materialType;
    }

    widget.onConfirm(params);
  }
}