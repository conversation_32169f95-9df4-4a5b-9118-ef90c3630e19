import 'package:get/get.dart';
import '../controllers/store_transfer_controller.dart';
import '../services/store_transfer_service.dart';

/// 库存调拨绑定类
class StoreTransferBinding extends Bindings {
  @override
  void dependencies() {
    // 注册调拨服务
    Get.lazyPut<StoreTransferService>(() => StoreTransferService());
    
    // 注册调拨控制器
    Get.lazyPut<StoreTransferController>(() => StoreTransferController());
  }
}
