import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gold_manager_flutter/features/sales/controllers/sales_return_controller.dart';
import 'package:gold_manager_flutter/models/sales/sales_item_model.dart';
import 'package:gold_manager_flutter/models/sales/sales_return_model.dart';
import 'package:gold_manager_flutter/services/sales_order_service.dart';
import 'package:gold_manager_flutter/widgets/app_bar.dart';
import 'package:gold_manager_flutter/widgets/loading_state.dart';
import 'package:intl/intl.dart';

/// 销售退货表单页面
class SalesReturnFormView extends StatefulWidget {
  /// 构造函数
  const SalesReturnFormView({super.key});

  @override
  State<SalesReturnFormView> createState() => _SalesReturnFormViewState();
}

class _SalesReturnFormViewState extends State<SalesReturnFormView> {
  final SalesReturnController _controller = Get.find<SalesReturnController>();
  final SalesOrderService _salesService = Get.find<SalesOrderService>();
  
  // 表单控制器
  final _formKey = GlobalKey<FormState>();
  final _returnReasonController = TextEditingController();
  final _remarkController = TextEditingController();

  // 销售单ID和退货项
  int? _selectedOrderId;
  String _selectedOrderNo = '';
  String _selectedCustomerName = '';
  int _selectedStoreId = 0;
  String _selectedStoreName = '';
  
  final List<SalesItem> _orderItems = [];
  final List<SalesReturnItem> _returnItems = <SalesReturnItem>[].obs;
  
  bool _isLoading = false;
  bool _isEdit = false;
  int? _editId;
  
  @override
  void initState() {
    super.initState();
    
    // 检查是否编辑模式
    if (Get.parameters.containsKey('id')) {
      _isEdit = true;
      _editId = int.parse(Get.parameters['id']!);
      _loadSalesReturnData();
    }
  }
  
  @override
  void dispose() {
    _returnReasonController.dispose();
    _remarkController.dispose();
    super.dispose();
  }
  
  /// 加载退货单数据（编辑模式）
  Future<void> _loadSalesReturnData() async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      await _controller.fetchSalesReturnDetail(_editId!);
      final salesReturn = _controller.currentSalesReturn.value;
      
      if (salesReturn != null) {
        _selectedOrderId = salesReturn.orderId;
        _selectedOrderNo = salesReturn.orderNo;
        _selectedCustomerName = salesReturn.customerName;
        _selectedStoreId = salesReturn.storeId;
        _selectedStoreName = salesReturn.storeName;
        _returnReasonController.text = salesReturn.returnReason;
        _remarkController.text = salesReturn.remark;
        
        // 加载销售单明细
        await _loadOrderItems();
        
        // 设置退货明细
        _returnItems.clear();
        _returnItems.addAll(salesReturn.items);
      }
    } catch (e) {
      Get.snackbar('错误', '加载退货单数据失败: $e',
          backgroundColor: Colors.red.shade100);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
  
  /// 加载销售单明细
  Future<void> _loadOrderItems() async {
    if (_selectedOrderId == null) return;
    
    setState(() {
      _isLoading = true;
    });
    
    try {
      final items = await _salesService.getSalesOrderItems(_selectedOrderId!);
      setState(() {
        _orderItems.clear();
        _orderItems.addAll(items);
      });
    } catch (e) {
      Get.snackbar('错误', '加载销售单明细失败: $e',
          backgroundColor: Colors.red.shade100);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
  
  /// 保存退货单
  Future<void> _saveSalesReturn() async {
    if (!_formKey.currentState!.validate()) return;
    
    if (_returnItems.isEmpty) {
      Get.snackbar('错误', '请至少添加一个退货项',
          backgroundColor: Colors.red.shade100);
      return;
    }
    
    setState(() {
      _isLoading = true;
    });
    
    try {
      const userId = 1; // 从全局状态或本地存储获取
      const userName = '管理员'; // 从全局状态或本地存储获取
      
      final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
      final double totalAmount = _returnItems.fold(
          0, (sum, item) => sum + item.amount);
      
      final salesReturn = SalesReturn(
        id: _isEdit ? _editId : null,
        returnNo: _isEdit
            ? _controller.currentSalesReturn.value!.returnNo
            : 'RET${DateFormat('yyyyMMddHHmmss').format(DateTime.now())}',
        orderId: _selectedOrderId!,
        orderNo: _selectedOrderNo,
        customerId: 1, // 需要从销售单中获取
        customerName: _selectedCustomerName,
        storeId: _selectedStoreId,
        storeName: _selectedStoreName,
        userId: userId,
        userName: userName,
        totalAmount: totalAmount,
        returnReason: _returnReasonController.text,
        remark: _remarkController.text,
        status: 0, // 初始状态：待审核
        createdAt: _isEdit
            ? _controller.currentSalesReturn.value!.createdAt
            : now,
        updatedAt: now,
        items: _returnItems,
      );
      
      bool success = false;
      
      if (_isEdit) {
        success = await _controller.updateSalesReturn(salesReturn);
      } else {
        success = await _controller.createSalesReturn(salesReturn);
      }
      
      if (success) {
        Get.back(); // 返回列表页
        Get.snackbar(
          '成功', 
          _isEdit ? '退货单更新成功' : '退货单创建成功',
          backgroundColor: Colors.green.shade100,
        );
      } else {
        Get.snackbar(
          '失败', 
          '${_isEdit ? "更新" : "创建"}退货单失败: ${_controller.errorMessage.value}',
          backgroundColor: Colors.red.shade100,
        );
      }
    } catch (e) {
      Get.snackbar(
        '错误', 
        '${_isEdit ? "更新" : "创建"}退货单时出错: $e',
        backgroundColor: Colors.red.shade100,
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
  
  /// 选择销售单
  Future<void> _selectSalesOrder() async {
    final result = await Get.toNamed('/sales/order/select');
    
    if (result != null && result is Map<String, dynamic>) {
      setState(() {
        _selectedOrderId = result['id'];
        _selectedOrderNo = result['order_no'];
        _selectedCustomerName = result['customer_name'];
        _selectedStoreId = result['store_id'];
        _selectedStoreName = result['store_name'];
        
        // 清空已选择的退货项
        _returnItems.clear();
      });
      
      // 加载销售单明细
      await _loadOrderItems();
    }
  }
  
  /// 添加退货项
  void _addReturnItem(SalesItem item) {
    // 检查是否已存在
    final existingIndex = _returnItems
        .indexWhere((element) => element.orderItemId == item.id);
    
    if (existingIndex != -1) {
      // 如果已存在，更新数量
      final existing = _returnItems[existingIndex];
      final quantity = existing.quantity + 1;
      
      _returnItems[existingIndex] = existing.copyWith(
        quantity: quantity,
        amount: item.price * quantity,
      );
    } else {
      // 添加新项
      _returnItems.add(
        SalesReturnItem.fromSalesItem(
          item,
          quantity: 1,
          returnReason: _returnReasonController.text,
        ),
      );
    }
    
    setState(() {});
  }
  
  /// 移除退货项
  void _removeReturnItem(int index) {
    setState(() {
      _returnItems.removeAt(index);
    });
  }
  
  /// 更新退货项数量
  void _updateReturnItemQuantity(int index, int quantity) {
    final item = _returnItems[index];
    
    if (quantity <= 0) {
      _removeReturnItem(index);
      return;
    }
    
    setState(() {
      _returnItems[index] = item.copyWith(
        quantity: quantity,
        amount: item.price * quantity,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: _isEdit ? '编辑退货单' : '创建退货单',
        actions: [
          IconButton(
            icon: const Icon(Icons.check),
            onPressed: _saveSalesReturn,
            tooltip: '保存',
          ),
        ],
      ),
      body: _isLoading
          ? const LoadingState(text: '加载中...', timeoutSeconds: 30)
          : _buildForm(),
    );
  }
  
  /// 构建表单
  Widget _buildForm() {
    return Form(
      key: _formKey,
      child: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          _buildOrderSelectionSection(),
          const SizedBox(height: 16),
          _buildReturnInfoSection(),
          const SizedBox(height: 16),
          _buildReturnItemsSection(),
        ],
      ),
    );
  }
  
  /// 构建销售单选择区域
  Widget _buildOrderSelectionSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '关联销售单',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            if (_selectedOrderId != null) ...[
              _buildInfoRow('销售单号', _selectedOrderNo),
              _buildInfoRow('客户', _selectedCustomerName),
              _buildInfoRow('门店', _selectedStoreName),
              const SizedBox(height: 8),
            ],
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    icon: const Icon(Icons.search),
                    label: Text(_selectedOrderId == null
                        ? '选择销售单'
                        : '更换销售单'),
                    onPressed: _isEdit 
                        ? null // 编辑模式下不允许更换销售单
                        : _selectSalesOrder,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  /// 构建退货信息区域
  Widget _buildReturnInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '退货信息',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _returnReasonController,
              decoration: const InputDecoration(
                labelText: '退货原因',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return '请输入退货原因';
                }
                return null;
              },
              maxLines: 2,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _remarkController,
              decoration: const InputDecoration(
                labelText: '备注',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }
  
  /// 构建退货项区域
  Widget _buildReturnItemsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  '退货商品',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (_selectedOrderId != null && _orderItems.isNotEmpty) ...[
                  ElevatedButton.icon(
                    icon: const Icon(Icons.add),
                    label: const Text('添加商品'),
                    onPressed: () => _showAddItemsDialog(),
                  ),
                ],
              ],
            ),
            const SizedBox(height: 16),
            if (_returnItems.isEmpty) ...[
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Text('暂无退货商品，请先选择销售单并添加退货商品'),
                ),
              ),
            ] else ...[
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _returnItems.length,
                itemBuilder: (context, index) {
                  final item = _returnItems[index];
                  return _buildReturnItemCard(item, index);
                },
              ),
              const SizedBox(height: 16),
              _buildTotalSection(),
            ],
          ],
        ),
      ),
    );
  }
  
  /// 构建退货项卡片
  Widget _buildReturnItemCard(SalesReturnItem item, int index) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8.0),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item.jewelryName,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text('编码: ${item.jewelryCode}'),
                  const SizedBox(height: 4),
                  Text('金重: ${item.goldWeight.toStringAsFixed(2)}g'),
                  const SizedBox(height: 4),
                  Text('单价: ¥${item.price.toStringAsFixed(2)}'),
                  const SizedBox(height: 4),
                  Text('金额: ¥${item.amount.toStringAsFixed(2)}'),
                ],
              ),
            ),
            Column(
              children: [
                Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.remove),
                      onPressed: () => _updateReturnItemQuantity(
                          index, item.quantity - 1),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '${item.quantity}',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(width: 8),
                    IconButton(
                      icon: const Icon(Icons.add),
                      onPressed: () => _updateReturnItemQuantity(
                          index, item.quantity + 1),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                IconButton(
                  icon: const Icon(Icons.delete, color: Colors.red),
                  onPressed: () => _removeReturnItem(index),
                  tooltip: '移除',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  /// 构建合计区域
  Widget _buildTotalSection() {
    final totalAmount = _returnItems.fold(
        0.0, (sum, item) => sum + item.amount);
    
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Text(
          '合计: ¥${totalAmount.toStringAsFixed(2)}',
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }
  
  /// 显示添加退货项对话框
  void _showAddItemsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('选择退货商品'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: _orderItems.length,
            itemBuilder: (context, index) {
              final item = _orderItems[index];
              return ListTile(
                title: Text(item.jewelryName),
                subtitle: Text(
                    '编码: ${item.jewelryCode} | 单价: ¥${item.price.toStringAsFixed(2)}'),
                onTap: () {
                  _addReturnItem(item);
                  Navigator.pop(context);
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
        ],
      ),
    );
  }
  
  /// 构建信息行
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        children: [
          Text(
            '$label: ',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(value),
        ],
      ),
    );
  }
} 