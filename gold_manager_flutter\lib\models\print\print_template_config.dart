/// 字段位置配置
class FieldPosition {
  /// X坐标(mm)
  final double x;

  /// Y坐标(mm)
  final double y;

  const FieldPosition({
    this.x = 0.0,
    this.y = 0.0,
  });

  factory FieldPosition.fromJson(Map<String, dynamic> json) {
    return FieldPosition(
      x: (json['x'] ?? 0.0).toDouble(),
      y: (json['y'] ?? 0.0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'x': x,
      'y': y,
    };
  }

  FieldPosition copyWith({
    double? x,
    double? y,
  }) {
    return FieldPosition(
      x: x ?? this.x,
      y: y ?? this.y,
    );
  }
}

/// 打印模板配置模型
///
/// 用于定义收款凭证的打印模板，支持自定义字段显示、格式设置等
class PrintTemplateConfig {
  /// 模板ID
  final String id;
  
  /// 模板名称
  final String name;
  
  /// 模板描述
  final String? description;
  
  /// 是否为默认模板
  final bool isDefault;
  
  /// 公司信息配置
  final CompanyInfoConfig companyInfo;
  
  /// 页面设置
  final PageConfig pageConfig;
  
  /// 表头配置
  final HeaderConfig headerConfig;
  
  /// 商品明细表格配置
  final ItemTableConfig itemTableConfig;
  
  /// 汇总信息配置
  final SummaryConfig summaryConfig;
  
  /// 收款信息配置
  final PaymentInfoConfig paymentInfoConfig;
  
  /// 页脚配置
  final FooterConfig footerConfig;
  
  /// 创建时间
  final DateTime createTime;
  
  /// 更新时间
  final DateTime updateTime;

  const PrintTemplateConfig({
    required this.id,
    required this.name,
    this.description,
    this.isDefault = false,
    required this.companyInfo,
    required this.pageConfig,
    required this.headerConfig,
    required this.itemTableConfig,
    required this.summaryConfig,
    required this.paymentInfoConfig,
    required this.footerConfig,
    required this.createTime,
    required this.updateTime,
  });

  /// 从JSON构造
  factory PrintTemplateConfig.fromJson(Map<String, dynamic> json) {
    return PrintTemplateConfig(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'],
      isDefault: json['is_default'] ?? false,
      companyInfo: CompanyInfoConfig.fromJson(json['company_info'] ?? {}),
      pageConfig: PageConfig.fromJson(json['page_config'] ?? {}),
      headerConfig: HeaderConfig.fromJson(json['header_config'] ?? {}),
      itemTableConfig: ItemTableConfig.fromJson(json['item_table_config'] ?? {}),
      summaryConfig: SummaryConfig.fromJson(json['summary_config'] ?? {}),
      paymentInfoConfig: PaymentInfoConfig.fromJson(json['payment_info_config'] ?? {}),
      footerConfig: FooterConfig.fromJson(json['footer_config'] ?? {}),
      createTime: DateTime.fromMillisecondsSinceEpoch((json['create_time'] ?? 0) * 1000),
      updateTime: DateTime.fromMillisecondsSinceEpoch((json['update_time'] ?? 0) * 1000),
    );
  }

  /// 转为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'is_default': isDefault,
      'company_info': companyInfo.toJson(),
      'page_config': pageConfig.toJson(),
      'header_config': headerConfig.toJson(),
      'item_table_config': itemTableConfig.toJson(),
      'summary_config': summaryConfig.toJson(),
      'payment_info_config': paymentInfoConfig.toJson(),
      'footer_config': footerConfig.toJson(),
      'create_time': createTime.millisecondsSinceEpoch ~/ 1000,
      'update_time': updateTime.millisecondsSinceEpoch ~/ 1000,
    };
  }

  /// 复制并修改属性
  PrintTemplateConfig copyWith({
    String? id,
    String? name,
    String? description,
    bool? isDefault,
    CompanyInfoConfig? companyInfo,
    PageConfig? pageConfig,
    HeaderConfig? headerConfig,
    ItemTableConfig? itemTableConfig,
    SummaryConfig? summaryConfig,
    PaymentInfoConfig? paymentInfoConfig,
    FooterConfig? footerConfig,
    DateTime? createTime,
    DateTime? updateTime,
  }) {
    return PrintTemplateConfig(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      isDefault: isDefault ?? this.isDefault,
      companyInfo: companyInfo ?? this.companyInfo,
      pageConfig: pageConfig ?? this.pageConfig,
      headerConfig: headerConfig ?? this.headerConfig,
      itemTableConfig: itemTableConfig ?? this.itemTableConfig,
      summaryConfig: summaryConfig ?? this.summaryConfig,
      paymentInfoConfig: paymentInfoConfig ?? this.paymentInfoConfig,
      footerConfig: footerConfig ?? this.footerConfig,
      createTime: createTime ?? this.createTime,
      updateTime: updateTime ?? this.updateTime,
    );
  }
}

/// 公司信息配置
class CompanyInfoConfig {
  /// 公司名称
  final String companyName;

  /// 是否显示地址
  final bool showAddress;

  /// 是否显示电话
  final bool showPhone;

  /// 字体大小
  final double fontSize;

  /// 是否加粗
  final bool isBold;

  /// 公司名称位置
  final FieldPosition namePosition;

  /// 地址位置
  final FieldPosition addressPosition;

  /// 电话位置
  final FieldPosition phonePosition;

  const CompanyInfoConfig({
    required this.companyName,
    this.showAddress = true,
    this.showPhone = true,
    this.fontSize = 16.0,
    this.isBold = true,
    this.namePosition = const FieldPosition(x: 105.0, y: 10.0),
    this.addressPosition = const FieldPosition(x: 105.0, y: 25.0),
    this.phonePosition = const FieldPosition(x: 105.0, y: 35.0),
  });

  factory CompanyInfoConfig.fromJson(Map<String, dynamic> json) {
    return CompanyInfoConfig(
      companyName: json['company_name'] ?? '金包银首饰店',
      showAddress: json['show_address'] ?? true,
      showPhone: json['show_phone'] ?? true,
      fontSize: (json['font_size'] ?? 16.0).toDouble(),
      isBold: json['is_bold'] ?? true,
      namePosition: FieldPosition.fromJson(json['name_position'] ?? {'x': 105.0, 'y': 10.0}),
      addressPosition: FieldPosition.fromJson(json['address_position'] ?? {'x': 105.0, 'y': 25.0}),
      phonePosition: FieldPosition.fromJson(json['phone_position'] ?? {'x': 105.0, 'y': 35.0}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'company_name': companyName,
      'show_address': showAddress,
      'show_phone': showPhone,
      'font_size': fontSize,
      'is_bold': isBold,
      'name_position': namePosition.toJson(),
      'address_position': addressPosition.toJson(),
      'phone_position': phonePosition.toJson(),
    };
  }
}

/// 页面配置
class PageConfig {
  /// 页面宽度(mm)
  final double width;
  
  /// 页面高度(mm)
  final double height;
  
  /// 边距(mm)
  final EdgeInsetsConfig margins;

  const PageConfig({
    this.width = 210.0,
    this.height = 101.6,
    this.margins = const EdgeInsetsConfig(),
  });

  factory PageConfig.fromJson(Map<String, dynamic> json) {
    return PageConfig(
      width: (json['width'] ?? 210.0).toDouble(),
      height: (json['height'] ?? 101.6).toDouble(),
      margins: EdgeInsetsConfig.fromJson(json['margins'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'width': width,
      'height': height,
      'margins': margins.toJson(),
    };
  }
}

/// 边距配置
class EdgeInsetsConfig {
  final double top;
  final double bottom;
  final double left;
  final double right;

  const EdgeInsetsConfig({
    this.top = 3.0,
    this.bottom = 3.0,
    this.left = 3.0,
    this.right = 3.0,
  });

  factory EdgeInsetsConfig.fromJson(Map<String, dynamic> json) {
    return EdgeInsetsConfig(
      top: (json['top'] ?? 3.0).toDouble(),
      bottom: (json['bottom'] ?? 3.0).toDouble(),
      left: (json['left'] ?? 3.0).toDouble(),
      right: (json['right'] ?? 3.0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'top': top,
      'bottom': bottom,
      'left': left,
      'right': right,
    };
  }
}

/// 表头配置
class HeaderConfig {
  /// 是否显示单号
  final bool showOrderNo;

  /// 是否显示客户
  final bool showCustomer;

  /// 是否显示销售类型
  final bool showSaleType;

  /// 是否显示日期时间
  final bool showDateTime;

  /// 字体大小
  final double fontSize;

  /// 单号位置
  final FieldPosition orderNoPosition;

  /// 客户位置
  final FieldPosition customerPosition;

  /// 销售类型位置
  final FieldPosition saleTypePosition;

  /// 日期时间位置
  final FieldPosition dateTimePosition;

  const HeaderConfig({
    this.showOrderNo = true,
    this.showCustomer = true,
    this.showSaleType = true,
    this.showDateTime = true,
    this.fontSize = 12.0,
    this.orderNoPosition = const FieldPosition(x: 10.0, y: 45.0),
    this.customerPosition = const FieldPosition(x: 105.0, y: 45.0),
    this.saleTypePosition = const FieldPosition(x: 10.0, y: 55.0),
    this.dateTimePosition = const FieldPosition(x: 105.0, y: 55.0),
  });

  factory HeaderConfig.fromJson(Map<String, dynamic> json) {
    return HeaderConfig(
      showOrderNo: json['show_order_no'] ?? true,
      showCustomer: json['show_customer'] ?? true,
      showSaleType: json['show_sale_type'] ?? true,
      showDateTime: json['show_date_time'] ?? true,
      fontSize: (json['font_size'] ?? 12.0).toDouble(),
      orderNoPosition: FieldPosition.fromJson(json['order_no_position'] ?? {'x': 10.0, 'y': 45.0}),
      customerPosition: FieldPosition.fromJson(json['customer_position'] ?? {'x': 105.0, 'y': 45.0}),
      saleTypePosition: FieldPosition.fromJson(json['sale_type_position'] ?? {'x': 10.0, 'y': 55.0}),
      dateTimePosition: FieldPosition.fromJson(json['date_time_position'] ?? {'x': 105.0, 'y': 55.0}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'show_order_no': showOrderNo,
      'show_customer': showCustomer,
      'show_sale_type': showSaleType,
      'show_date_time': showDateTime,
      'font_size': fontSize,
      'order_no_position': orderNoPosition.toJson(),
      'customer_position': customerPosition.toJson(),
      'sale_type_position': saleTypePosition.toJson(),
      'date_time_position': dateTimePosition.toJson(),
    };
  }
}

/// 商品明细表格配置
class ItemTableConfig {
  /// 可见列配置
  final List<String> visibleColumns;

  /// 列宽配置(毫米)
  final Map<String, double> columnWidths;

  /// 表格总宽度(毫米)，如果设置则按比例分配列宽
  final double? totalWidth;

  /// 表头字体大小
  final double headerFontSize;

  /// 内容字体大小
  final double contentFontSize;

  /// 行高
  final double rowHeight;

  /// 表格位置
  final FieldPosition tablePosition;

  const ItemTableConfig({
    this.visibleColumns = const ['序号', '条码', '商品名称', '规格', '重量', '单价', '金额'],
    this.columnWidths = const {
      '序号': 8.0,
      '条码': 15.0,
      '商品名称': 25.0,
      '规格': 12.0,
      '重量': 12.0,
      '单价': 13.0,
      '金额': 15.0,
    },
    this.totalWidth,
    this.headerFontSize = 10.0,
    this.contentFontSize = 9.0,
    this.rowHeight = 20.0,
    this.tablePosition = const FieldPosition(x: 10.0, y: 65.0),
  });

  factory ItemTableConfig.fromJson(Map<String, dynamic> json) {
    return ItemTableConfig(
      visibleColumns: List<String>.from(json['visible_columns'] ?? ['序号', '条码', '商品名称', '规格', '重量', '单价', '金额']),
      columnWidths: Map<String, double>.from(json['column_widths'] ?? {}),
      totalWidth: json['total_width']?.toDouble(),
      headerFontSize: (json['header_font_size'] ?? 10.0).toDouble(),
      contentFontSize: (json['content_font_size'] ?? 9.0).toDouble(),
      rowHeight: (json['row_height'] ?? 20.0).toDouble(),
      tablePosition: FieldPosition.fromJson(json['table_position'] ?? {'x': 10.0, 'y': 65.0}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'visible_columns': visibleColumns,
      'column_widths': columnWidths,
      'total_width': totalWidth,
      'header_font_size': headerFontSize,
      'content_font_size': contentFontSize,
      'row_height': rowHeight,
      'table_position': tablePosition.toJson(),
    };
  }

  /// 获取实际的列宽配置
  /// 如果设置了totalWidth，则按比例分配；否则使用原始配置
  Map<String, double> getActualColumnWidths() {
    if (totalWidth == null || totalWidth! <= 0) {
      return columnWidths;
    }

    // 计算当前列宽总和
    double currentTotal = 0;
    for (final column in visibleColumns) {
      currentTotal += columnWidths[column] ?? 0;
    }

    if (currentTotal <= 0) {
      // 如果没有配置列宽，平均分配
      final averageWidth = totalWidth! / visibleColumns.length;
      return Map.fromEntries(
        visibleColumns.map((column) => MapEntry(column, averageWidth)),
      );
    }

    // 按比例分配
    final ratio = totalWidth! / currentTotal;
    return Map.fromEntries(
      visibleColumns.map((column) {
        final originalWidth = columnWidths[column] ?? 0;
        return MapEntry(column, originalWidth * ratio);
      }),
    );
  }
}

/// 汇总信息配置
class SummaryConfig {
  /// 是否显示合计数量
  final bool showTotalQuantity;
  
  /// 是否显示合计重量
  final bool showTotalWeight;
  
  /// 是否显示合计金额
  final bool showTotalAmount;
  
  /// 字体大小
  final double fontSize;
  
  /// 是否加粗
  final bool isBold;

  const SummaryConfig({
    this.showTotalQuantity = true,
    this.showTotalWeight = true,
    this.showTotalAmount = true,
    this.fontSize = 11.0,
    this.isBold = true,
  });

  factory SummaryConfig.fromJson(Map<String, dynamic> json) {
    return SummaryConfig(
      showTotalQuantity: json['show_total_quantity'] ?? true,
      showTotalWeight: json['show_total_weight'] ?? true,
      showTotalAmount: json['show_total_amount'] ?? true,
      fontSize: (json['font_size'] ?? 11.0).toDouble(),
      isBold: json['is_bold'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'show_total_quantity': showTotalQuantity,
      'show_total_weight': showTotalWeight,
      'show_total_amount': showTotalAmount,
      'font_size': fontSize,
      'is_bold': isBold,
    };
  }
}

/// 收款信息配置
class PaymentInfoConfig {
  /// 是否显示收款方式
  final bool showPaymentMethod;
  
  /// 是否显示收款明细
  final bool showPaymentDetails;
  
  /// 是否显示找零金额
  final bool showChangeAmount;
  
  /// 字体大小
  final double fontSize;

  const PaymentInfoConfig({
    this.showPaymentMethod = true,
    this.showPaymentDetails = true,
    this.showChangeAmount = true,
    this.fontSize = 10.0,
  });

  factory PaymentInfoConfig.fromJson(Map<String, dynamic> json) {
    return PaymentInfoConfig(
      showPaymentMethod: json['show_payment_method'] ?? true,
      showPaymentDetails: json['show_payment_details'] ?? true,
      showChangeAmount: json['show_change_amount'] ?? true,
      fontSize: (json['font_size'] ?? 10.0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'show_payment_method': showPaymentMethod,
      'show_payment_details': showPaymentDetails,
      'show_change_amount': showChangeAmount,
      'font_size': fontSize,
    };
  }
}

/// 页脚配置
class FooterConfig {
  /// 自定义页脚文本
  final String? customText;
  
  /// 是否显示打印时间
  final bool showPrintTime;
  
  /// 字体大小
  final double fontSize;

  const FooterConfig({
    this.customText,
    this.showPrintTime = true,
    this.fontSize = 8.0,
  });

  factory FooterConfig.fromJson(Map<String, dynamic> json) {
    return FooterConfig(
      customText: json['custom_text'],
      showPrintTime: json['show_print_time'] ?? true,
      fontSize: (json['font_size'] ?? 8.0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'custom_text': customText,
      'show_print_time': showPrintTime,
      'font_size': fontSize,
    };
  }
}
