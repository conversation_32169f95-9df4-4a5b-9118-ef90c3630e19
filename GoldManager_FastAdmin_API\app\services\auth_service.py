"""
认证服务类
处理用户登录、令牌管理、权限验证等业务逻辑
"""

import time
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_

from ..models.admin import Admin
from ..models.store import Store
from ..core.security import jwt_manager, password_manager, permission_manager
from ..core.config import settings
from ..schemas.auth import (
    LoginRequest,
    LoginResponse,
    RefreshTokenRequest,
    RefreshTokenResponse,
    ChangePasswordRequest,
    CurrentUserResponse,
    LoginAttemptResponse
)


class AuthService:
    """认证服务类"""

    def __init__(self, db: Session):
        self.db = db
        self._login_attempts = {}  # 简单的内存存储，生产环境应使用Redis

    async def login(self, login_data: LoginRequest, client_ip: str) -> LoginResponse:
        """用户登录"""
        username = login_data.username
        password = login_data.password

        # 检查登录尝试次数
        if self._is_account_locked(username):
            remaining_time = self._get_lock_remaining_time(username)
            raise ValueError(f"账户已被锁定，请{remaining_time}分钟后再试")

        # 查找管理员
        admin = self.db.query(Admin).filter(Admin.username == username).first()
        if not admin:
            self._record_failed_attempt(username)
            raise ValueError("用户名或密码错误")

        # 验证密码
        if not password_manager.verify_password(password, admin.salt, admin.password):
            self._record_failed_attempt(username)
            # 更新失败次数
            admin.loginfailure = (admin.loginfailure or 0) + 1
            self.db.commit()
            raise ValueError("用户名或密码错误")

        # 检查账户状态
        if admin.status != 'normal':
            raise ValueError("账户已被禁用")

        # 清除失败记录
        self._clear_failed_attempts(username)

        # 更新登录信息
        current_time = int(time.time())
        admin.logintime = current_time
        admin.loginip = client_ip
        admin.loginfailure = 0
        self.db.commit()

        # 获取门店信息
        store = None
        if admin.store_id:
            store = self.db.query(Store).filter(Store.id == admin.store_id).first()

        # 获取权限
        permissions = self._get_admin_permissions(admin)

        # 生成令牌
        token_data = {
            "sub": str(admin.id),  # JWT的sub字段必须是字符串
            "username": admin.username,
            "store_id": admin.store_id,
            "permissions": permissions
        }

        # 根据记住我设置过期时间
        if login_data.remember_me:
            access_expires = timedelta(days=7)
            refresh_expires = timedelta(days=30)
        else:
            access_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
            refresh_expires = timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)

        access_token = jwt_manager.create_access_token(token_data, access_expires)
        refresh_token = jwt_manager.create_refresh_token(token_data, refresh_expires)

        # 构建管理员信息
        admin_info = {
            "id": admin.id,
            "username": admin.username,
            "nickname": admin.nickname,
            "store_id": admin.store_id,
            "store_name": store.name if store else None,
            "permissions": permissions
        }

        return LoginResponse(
            access_token=access_token,
            refresh_token=refresh_token,
            expires_in=int(access_expires.total_seconds()),
            admin_info=admin_info
        )

    async def refresh_token(self, refresh_data: RefreshTokenRequest) -> RefreshTokenResponse:
        """刷新访问令牌"""
        token_data = jwt_manager.verify_token(refresh_data.refresh_token)
        if not token_data:
            raise ValueError("无效的刷新令牌")

        # 验证管理员是否仍然有效
        admin = self.db.query(Admin).filter(Admin.id == token_data.admin_id).first()
        if not admin or admin.status != 'normal':
            raise ValueError("账户已被禁用")

        # 生成新的访问令牌
        new_token_data = {
            "sub": str(admin.id),  # JWT的sub字段必须是字符串
            "username": admin.username,
            "store_id": admin.store_id,
            "permissions": self._get_admin_permissions(admin)
        }

        access_token = jwt_manager.create_access_token(new_token_data)

        return RefreshTokenResponse(
            access_token=access_token,
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
        )

    async def get_current_user(self, token_data) -> CurrentUserResponse:
        """获取当前用户信息"""
        admin = self.db.query(Admin).filter(Admin.id == token_data.admin_id).first()
        if not admin:
            raise ValueError("用户不存在")

        # 获取门店信息
        store = None
        if admin.store_id:
            store = self.db.query(Store).filter(Store.id == admin.store_id).first()

        # 转换时间戳
        last_login_time = None
        if admin.logintime:
            last_login_time = datetime.fromtimestamp(admin.logintime)

        return CurrentUserResponse(
            id=admin.id,
            username=admin.username,
            nickname=admin.nickname,
            email=admin.email,
            mobile=admin.mobile,
            avatar=admin.avatar,
            store_id=admin.store_id,
            store_name=store.name if store else None,
            status=admin.status,
            permissions=self._get_admin_permissions(admin),
            last_login_time=last_login_time,
            last_login_ip=admin.loginip
        )

    async def change_password(
        self,
        admin_id: int,
        password_data: ChangePasswordRequest
    ) -> Dict[str, str]:
        """修改密码"""
        admin = self.db.query(Admin).filter(Admin.id == admin_id).first()
        if not admin:
            raise ValueError("用户不存在")

        # 验证原密码
        if not password_manager.verify_password(
            password_data.old_password,
            admin.salt,
            admin.password
        ):
            raise ValueError("原密码错误")

        # 生成新密码
        new_password_hash, new_salt = password_manager.create_password(
            password_data.new_password
        )

        # 更新密码
        admin.password = new_password_hash
        admin.salt = new_salt
        admin.updatetime = int(time.time())

        self.db.commit()

        return {"message": "密码修改成功"}

    def _get_admin_permissions(self, admin: Admin) -> list:
        """获取管理员权限"""
        # 这里可以根据实际需求实现权限逻辑
        # 目前简单根据用户名判断
        if admin.username == 'admin':
            return ["super.admin"]
        else:
            return permission_manager.get_default_permissions("staff")

    def _is_account_locked(self, username: str) -> bool:
        """检查账户是否被锁定"""
        if username not in self._login_attempts:
            return False

        attempt_data = self._login_attempts[username]
        if attempt_data['count'] < settings.LOGIN_MAX_ATTEMPTS:
            return False

        # 检查锁定时间是否已过
        lock_time = attempt_data['lock_time']
        if datetime.now() > lock_time + timedelta(minutes=settings.LOGIN_LOCK_TIME):
            # 锁定时间已过，清除记录
            del self._login_attempts[username]
            return False

        return True

    def _get_lock_remaining_time(self, username: str) -> int:
        """获取锁定剩余时间(分钟)"""
        if username not in self._login_attempts:
            return 0

        attempt_data = self._login_attempts[username]
        lock_time = attempt_data['lock_time']
        unlock_time = lock_time + timedelta(minutes=settings.LOGIN_LOCK_TIME)
        remaining = unlock_time - datetime.now()

        return max(0, int(remaining.total_seconds() / 60))

    def _record_failed_attempt(self, username: str):
        """记录失败尝试"""
        if username not in self._login_attempts:
            self._login_attempts[username] = {'count': 0, 'lock_time': None}

        self._login_attempts[username]['count'] += 1

        if self._login_attempts[username]['count'] >= settings.LOGIN_MAX_ATTEMPTS:
            self._login_attempts[username]['lock_time'] = datetime.now()

    def _clear_failed_attempts(self, username: str):
        """清除失败记录"""
        if username in self._login_attempts:
            del self._login_attempts[username]

    async def get_login_attempts(self, username: str) -> LoginAttemptResponse:
        """获取登录尝试信息"""
        if username not in self._login_attempts:
            return LoginAttemptResponse(
                remaining_attempts=settings.LOGIN_MAX_ATTEMPTS,
                is_locked=False
            )

        attempt_data = self._login_attempts[username]
        remaining = settings.LOGIN_MAX_ATTEMPTS - attempt_data['count']
        is_locked = self._is_account_locked(username)
        lock_time = self._get_lock_remaining_time(username) if is_locked else None

        return LoginAttemptResponse(
            remaining_attempts=max(0, remaining),
            lock_time=lock_time,
            is_locked=is_locked
        )
