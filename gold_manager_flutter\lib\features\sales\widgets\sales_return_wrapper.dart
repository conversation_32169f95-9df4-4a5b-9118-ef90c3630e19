import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../controllers/sales_return_controller.dart';
import '../views/sales_return_list_view.dart';

/// 销售退货管理包装器
/// 负责注入SalesReturnController依赖
class SalesReturnWrapper extends StatelessWidget {
  const SalesReturnWrapper({super.key});

  @override
  Widget build(BuildContext context) {
    // 确保SalesReturnController被注册
    Get.lazyPut<SalesReturnController>(() => SalesReturnController(), fenix: true);
    
    return const SalesReturnListView();
  }
}
