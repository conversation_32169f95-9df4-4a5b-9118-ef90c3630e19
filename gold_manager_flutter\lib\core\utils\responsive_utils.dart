import 'package:flutter/material.dart';

/// 响应式布局工具类
class Responsive {
  /// 移动设备断点
  static const double mobileBreakpoint = 600;
  
  /// 平板设备断点
  static const double tabletBreakpoint = 1000;
  
  /// 桌面设备断点
  static const double desktopBreakpoint = 1200;
  
  /// 判断当前是否是移动设备
  static bool isMobile(BuildContext context) {
    final size = MediaQuery.of(context).size.width;
    return size < mobileBreakpoint;
  }
  
  /// 判断当前是否是平板设备
  static bool isTablet(BuildContext context) {
    final size = MediaQuery.of(context).size.width;
    return size >= mobileBreakpoint && size < tabletBreakpoint;
  }
  
  /// 判断当前是否是桌面设备
  static bool isDesktop(BuildContext context) {
    final size = MediaQuery.of(context).size.width;
    return size >= tabletBreakpoint;
  }
  
  /// 根据屏幕尺寸返回不同的值
  static T value<T>({
    required BuildContext context,
    required T mobile,
    T? tablet,
    required T desktop,
  }) {
    if (isDesktop(context)) {
      return desktop;
    } else if (isTablet(context)) {
      return tablet ?? mobile;
    } else {
      return mobile;
    }
  }
}

/// 响应式设计工具类
class ResponsiveUtils {
  /// 移动设备最大宽度
  static const double mobileMaxWidth = 600;
  
  /// 平板设备最大宽度
  static const double tabletMaxWidth = 1000;
  
  /// 检查是否为移动设备屏幕
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < mobileMaxWidth;
  }
  
  /// 检查是否为平板设备屏幕
  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobileMaxWidth && width < tabletMaxWidth;
  }
  
  /// 检查是否为桌面设备屏幕
  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= tabletMaxWidth;
  }
  
  /// 获取当前设备类型
  static DeviceType getDeviceType(BuildContext context) {
    if (isMobile(context)) {
      return DeviceType.mobile;
    } else if (isTablet(context)) {
      return DeviceType.tablet;
    } else {
      return DeviceType.desktop;
    }
  }
  
  /// 根据不同设备类型返回不同的值
  static T getValueByDeviceType<T>({
    required BuildContext context,
    required T mobile,
    required T tablet,
    required T desktop,
  }) {
    final deviceType = getDeviceType(context);
    switch (deviceType) {
      case DeviceType.mobile:
        return mobile;
      case DeviceType.tablet:
        return tablet;
      case DeviceType.desktop:
        return desktop;
    }
  }
  
  /// 获取基于屏幕宽度的尺寸
  static double getScreenWidthBasedSize(
    BuildContext context, {
    required double mobileSize,
    required double tabletSize,
    required double desktopSize,
  }) {
    return getValueByDeviceType(
      context: context,
      mobile: mobileSize,
      tablet: tabletSize,
      desktop: desktopSize,
    );
  }
  
  /// 获取网格列数
  static int getGridColumnCount(BuildContext context) {
    return getValueByDeviceType(
      context: context,
      mobile: 1,
      tablet: 2,
      desktop: 4,
    );
  }
  
  /// 获取表格列宽
  static double getTableColumnWidth(BuildContext context, double percentage) {
    final width = MediaQuery.of(context).size.width;
    return width * percentage;
  }
}

/// 设备类型枚举
enum DeviceType {
  mobile,
  tablet,
  desktop,
} 