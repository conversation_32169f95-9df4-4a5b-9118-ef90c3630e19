"""
商品相关数据模型
对应FastAdmin数据库中的商品表结构
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey
from sqlalchemy.sql.sqltypes import Numeric
from sqlalchemy.orm import relationship
from sqlalchemy.ext.declarative import declarative_base
from ..core.database import Base


class JewelryCategory(Base):
    """商品分类表 - 对应 fa_jewelry_category"""
    __tablename__ = "fa_jewelry_category"

    id = Column(Integer, primary_key=True, index=True, comment="分类ID")
    name = Column(String(50), nullable=False, comment="分类名称")
    pid = Column(Integer, default=0, comment="父级ID")
    status = Column(Integer, default=1, comment="状态:0=禁用,1=正常")
    weigh = Column(Integer, default=0, comment="权重")
    createtime = Column(Integer, comment="创建时间")
    updatetime = Column(Integer, comment="更新时间")

    # 关联关系
    jewelry_items = relationship("Jewelry", back_populates="category")
    groups = relationship("JewelryGroup", back_populates="category")
    inventory_check_items = relationship("InventoryCheckItem", back_populates="category")
    recycling_items = relationship("RecyclingItem", back_populates="category")


class Jewelry(Base):
    """商品表 - 对应 fa_jewelry"""
    __tablename__ = "fa_jewelry"

    id = Column(Integer, primary_key=True, index=True, comment="商品ID")
    barcode = Column(String(50), unique=True, nullable=False, comment="条码")
    name = Column(String(100), nullable=False, comment="商品名称")
    category_id = Column(Integer, ForeignKey("fa_jewelry_category.id"), nullable=False, comment="分类ID")
    ring_size = Column(String(20), comment="圈口号")

    # 金重相关
    gold_weight = Column(Numeric(10, 2), default=0.00, comment="金重(克)")
    gold_price = Column(Numeric(10, 2), default=0.00, comment="金进价(克价)")
    gold_cost = Column(Numeric(10, 2), default=0.00, comment="金成本(金重*金进价)")

    # 银重相关
    silver_weight = Column(Numeric(10, 2), default=0.00, comment="银重(克)")
    total_weight = Column(Numeric(10, 2), default=0.00, comment="总重")
    silver_price = Column(Numeric(10, 2), default=0.00, comment="银进价(克价)")
    silver_cost = Column(Numeric(10, 2), default=0.00, comment="银成本(银重*银进价)")

    # 工费相关
    silver_work_type = Column(Integer, default=0, comment="银工费方式:0=按克,1=按件")
    silver_work_price = Column(Numeric(10, 2), default=0.00, comment="银工费(按克为克价,按件为件价)")
    plating_cost = Column(Numeric(10, 2), default=0.00, comment="电铸费")

    # 成本和定价
    total_cost = Column(Numeric(10, 2), default=0.00, comment="进货总成本")
    wholesale_work_price = Column(Numeric(10, 2), default=0.00, comment="批发工费")
    retail_work_price = Column(Numeric(10, 2), default=0.00, comment="零售工费")
    piece_work_price = Column(Numeric(10, 2), default=0.00, comment="件工费")

    # 门店和状态
    store_id = Column(Integer, ForeignKey("fa_store.id"), nullable=False, comment="所属门店")
    status = Column(Integer, default=1, comment="状态:0=下架,1=上架,2=待出库")
    createtime = Column(Integer, comment="创建时间")
    updatetime = Column(Integer, comment="更新时间")

    # 关联关系
    category = relationship("JewelryCategory", back_populates="jewelry_items")
    store = relationship("Store", back_populates="jewelry_items")
    group_items = relationship("JewelryGroupItem", back_populates="jewelry")
    stock_in_items = relationship("StockInItem", back_populates="jewelry")
    stock_out_items = relationship("StockOutItem", back_populates="jewelry")
    stock_return_items = relationship("StockReturnItem", back_populates="jewelry")
    inventory_check_items = relationship("InventoryCheckItem", back_populates="jewelry")
    transfer_items = relationship("StoreTransferItem", back_populates="jewelry")


class JewelryGroup(Base):
    """商品分组表 - 对应 fa_jewelry_group"""
    __tablename__ = "fa_jewelry_group"

    id = Column(Integer, primary_key=True, index=True, comment="分组ID")
    name = Column(String(255), nullable=False, comment="分组名称")
    category_id = Column(Integer, ForeignKey("fa_jewelry_category.id"), comment="商品分类ID")
    representative_id = Column(Integer, ForeignKey("fa_jewelry.id"), comment="代表性商品ID")
    createtime = Column(Integer, comment="创建时间")
    updatetime = Column(Integer, comment="更新时间")

    # 关联关系
    category = relationship("JewelryCategory", back_populates="groups")
    representative_jewelry = relationship("Jewelry", foreign_keys=[representative_id])
    group_items = relationship("JewelryGroupItem", back_populates="group")
    images = relationship("GroupImages", back_populates="group")


class JewelryGroupItem(Base):
    """商品分组明细表 - 对应 fa_jewelry_group_item"""
    __tablename__ = "fa_jewelry_group_item"

    id = Column(Integer, primary_key=True, index=True, comment="明细ID")
    group_id = Column(Integer, ForeignKey("fa_jewelry_group.id"), nullable=False, comment="分组ID")
    jewelry_id = Column(Integer, ForeignKey("fa_jewelry.id"), nullable=False, comment="商品ID")
    createtime = Column(Integer, comment="创建时间")

    # 关联关系
    group = relationship("JewelryGroup", back_populates="group_items")
    jewelry = relationship("Jewelry", back_populates="group_items")

    # 设置唯一约束
    __table_args__ = (
        {"comment": "商品分组明细表"},
    )


class GroupImages(Base):
    """分组图片表 - 对应 fa_group_images"""
    __tablename__ = "fa_group_images"

    id = Column(Integer, primary_key=True, index=True, comment="图片ID")
    group_id = Column(Integer, ForeignKey("fa_jewelry_group.id"), nullable=False, comment="分组ID")
    image_url = Column(String(255), nullable=False, comment="图片URL")
    is_main = Column(Integer, default=0, comment="是否主图")
    createtime = Column(Integer, comment="创建时间")
    updatetime = Column(Integer, comment="更新时间")

    # 关联关系
    group = relationship("JewelryGroup", back_populates="images")