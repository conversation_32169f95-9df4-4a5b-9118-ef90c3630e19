import 'dart:io';
import 'package:get/get.dart';
import '../../../core/config/app_config.dart';
import '../../../models/recycling/metal_separation.dart';
import '../services/metal_separation_service.dart';
import '../services/image_upload_service.dart';

/// 金属分离控制器
class MetalSeparationController extends GetxController {
  final MetalSeparationService _metalSeparationService = Get.find<MetalSeparationService>();
  final ImageUploadService _imageUploadService = Get.find<ImageUploadService>();
  
  // 当前回收物品ID
  final int recyclingItemId;
  
  // 状态变量
  final RxBool isLoading = false.obs;
  final RxBool isSubmitting = false.obs;
  final RxBool isUploadingImage = false.obs;
  final RxList<MetalSeparation> separationRecords = <MetalSeparation>[].obs;
  final Rx<MetalSeparation?> currentRecord = Rx<MetalSeparation?>(null);
  
  // 图片相关
  final RxList<String> imageUrls = <String>[].obs;
  final RxList<File> tempImages = <File>[].obs;
  
  // 表单值
  final RxDouble originalGoldWeight = 0.0.obs;
  final RxDouble originalSilverWeight = 0.0.obs;
  final RxDouble separatedGoldWeight = 0.0.obs;
  final RxDouble separatedSilverWeight = 0.0.obs;
  final RxDouble goldPrice = AppConfig.defaultGoldPrice.obs;
  final RxDouble silverPrice = AppConfig.defaultSilverPrice.obs;
  final RxDouble separationCost = 0.0.obs;
  final RxString remark = ''.obs;
  
  // 计算属性
  double get lossRate {
    final originalTotal = originalGoldWeight.value + originalSilverWeight.value;
    final separatedTotal = separatedGoldWeight.value + separatedSilverWeight.value;
    if (originalTotal <= 0) return 0;
    return ((originalTotal - separatedTotal) / originalTotal * 100).clamp(0, 100);
  }
  
  double get goldValue => separatedGoldWeight.value * goldPrice.value;
  double get silverValue => separatedSilverWeight.value * silverPrice.value;
  double get totalValue => goldValue + silverValue;
  double get profit => totalValue - separationCost.value;
  
  MetalSeparationController({required this.recyclingItemId});
  
  @override
  void onInit() {
    super.onInit();
    loadSeparationRecords();
  }
  
  @override
  void onClose() {
    // 清理临时图片
    tempImages.clear();
    super.onClose();
  }
  
  /// 加载金属分离记录
  Future<void> loadSeparationRecords() async {
    isLoading.value = true;
    try {
      final records = await _metalSeparationService.getMetalSeparationList(recyclingItemId);
      separationRecords.assignAll(records);
    } finally {
      isLoading.value = false;
    }
  }
  
  /// 加载金属分离记录详情
  Future<void> loadSeparationDetail(int id) async {
    isLoading.value = true;
    try {
      final record = await _metalSeparationService.getMetalSeparationDetail(id);
      if (record != null) {
        currentRecord.value = record;
        
        // 更新表单数据
        originalGoldWeight.value = record.originalGoldWeight;
        originalSilverWeight.value = record.originalSilverWeight;
        separatedGoldWeight.value = record.separatedGoldWeight;
        separatedSilverWeight.value = record.separatedSilverWeight;
        goldPrice.value = record.goldPrice;
        silverPrice.value = record.silverPrice;
        separationCost.value = record.separationCost;
        remark.value = record.remark ?? '';
        
        // 加载图片
        if (record.imageUrls != null && record.imageUrls!.isNotEmpty) {
          imageUrls.assignAll(record.imageUrls!);
        } else {
          imageUrls.clear();
        }
        tempImages.clear();
      }
    } finally {
      isLoading.value = false;
    }
  }
  
  /// 重置表单
  void resetForm() {
    currentRecord.value = null;
    originalGoldWeight.value = 0.0;
    originalSilverWeight.value = 0.0;
    separatedGoldWeight.value = 0.0;
    separatedSilverWeight.value = 0.0;
    goldPrice.value = AppConfig.defaultGoldPrice;
    silverPrice.value = AppConfig.defaultSilverPrice;
    separationCost.value = 0.0;
    remark.value = '';
    imageUrls.clear();
    tempImages.clear();
  }
  
  /// 从相册选择图片
  Future<void> pickImage() async {
    final image = await _imageUploadService.pickImageFromGallery();
    if (image != null) {
      tempImages.add(image);
    }
  }
  
  /// 拍照
  Future<void> takePhoto() async {
    final image = await _imageUploadService.takePhoto();
    if (image != null) {
      tempImages.add(image);
    }
  }
  
  /// 删除临时图片
  void removeTempImage(int index) {
    if (index >= 0 && index < tempImages.length) {
      tempImages.removeAt(index);
    }
  }
  
  /// 删除已上传图片
  Future<void> removeUploadedImage(int index) async {
    if (index >= 0 && index < imageUrls.length) {
      final url = imageUrls[index];
      final success = await _imageUploadService.deleteImage(url);
      if (success) {
        imageUrls.removeAt(index);
      }
    }
  }
  
  /// 上传所有临时图片
  Future<List<String>> uploadTempImages() async {
    if (tempImages.isEmpty) return [];
    
    isUploadingImage.value = true;
    try {
      final urls = await _imageUploadService.uploadImages(tempImages.toList(), recyclingItemId: recyclingItemId);
      imageUrls.addAll(urls);
      tempImages.clear();
      return urls;
    } finally {
      isUploadingImage.value = false;
    }
  }
  
  /// 提交分离记录
  Future<bool> submitSeparationRecord({int? processId}) async {
    isSubmitting.value = true;
    try {
      // 先上传图片
      if (tempImages.isNotEmpty) {
        await uploadTempImages();
      }
      
      final separation = MetalSeparation(
        id: currentRecord.value?.id,
        processId: processId ?? currentRecord.value?.processId ?? 0,
        recyclingItemId: recyclingItemId,
        originalGoldWeight: originalGoldWeight.value,
        originalSilverWeight: originalSilverWeight.value,
        separatedGoldWeight: separatedGoldWeight.value,
        separatedSilverWeight: separatedSilverWeight.value,
        lossRate: lossRate,
        goldPrice: goldPrice.value,
        silverPrice: silverPrice.value,
        separationCost: separationCost.value,
        remark: remark.value.isEmpty ? null : remark.value,
        operatorId: 1, // 实际应用中应该获取当前用户ID
        operatorName: 'Admin', // 实际应用中应该获取当前用户名
        operationDate: DateTime.now(),
        goldWeight: separatedGoldWeight.value,
        silverWeight: separatedSilverWeight.value,
        otherWeight: 0.0,
        purityRate: 0.0,
        description: '',
        status: 1,
        imageUrls: imageUrls.isEmpty ? null : imageUrls.toList(),
      );
      
      bool success;
      if (currentRecord.value?.id != null) {
        success = await _metalSeparationService.updateMetalSeparation(separation);
      } else {
        success = await _metalSeparationService.addMetalSeparation(separation);
      }
      
      if (success) {
        await loadSeparationRecords();
        resetForm();
      }
      
      return success;
    } finally {
      isSubmitting.value = false;
    }
  }
  
  /// 删除分离记录
  Future<bool> deleteSeparationRecord(int id) async {
    isSubmitting.value = true;
    try {
      final success = await _metalSeparationService.deleteMetalSeparation(id);
      if (success) {
        await loadSeparationRecords();
        if (currentRecord.value?.id == id) {
          resetForm();
        }
      }
      return success;
    } finally {
      isSubmitting.value = false;
    }
  }
} 