import 'dart:io';
import 'package:dio/dio.dart' as dio;
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path/path.dart' as path;
import '../../../core/config/app_config.dart';
import '../../../core/services/api_service.dart';

/// 图片上传服务类
class ImageUploadService extends GetxService {
  final ApiService _apiService = Get.find<ApiService>();
  final ImagePicker _picker = ImagePicker();
  
  /// 从相册选择图片
  Future<File?> pickImageFromGallery() async {
    try {
      final XFile? pickedFile = await _picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 85, // 压缩质量
        maxWidth: 1200,   // 最大宽度
      );
      
      if (pickedFile != null) {
        return File(pickedFile.path);
      }
      return null;
    } catch (e) {
      Get.log('选择图片失败: $e', isError: true);
      return null;
    }
  }
  
  /// 拍照获取图片
  Future<File?> takePhoto() async {
    try {
      final XFile? pickedFile = await _picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 85,
        maxWidth: 1200,
      );
      
      if (pickedFile != null) {
        return File(pickedFile.path);
      }
      return null;
    } catch (e) {
      Get.log('拍照失败: $e', isError: true);
      return null;
    }
  }
  
  /// 上传图片到服务器
  Future<String?> uploadImage(File imageFile, {int? recyclingItemId}) async {
    try {
      String fileName = path.basename(imageFile.path);
      dio.FormData formData = dio.FormData.fromMap({
        'file': await dio.MultipartFile.fromFile(imageFile.path, filename: fileName),
        if (recyclingItemId != null) 'recycling_item_id': recyclingItemId
      });
      
      final response = await _apiService.post(
        '${AppConfig.apiEndpoint['upload']}/image',
        data: formData,
      );
      
      if (response.data['code'] == 1) {
        return response.data['data']['url']; // 返回上传后的图片URL
      } else {
        Get.log('上传图片失败: ${response.data['msg']}', isError: true);
        return null;
      }
    } catch (e) {
      Get.log('上传图片异常: $e', isError: true);
      return null;
    }
  }
  
  /// 批量上传图片
  Future<List<String>> uploadImages(List<File> imageFiles, {int? recyclingItemId}) async {
    List<String> uploadedUrls = [];
    
    for (var imageFile in imageFiles) {
      final url = await uploadImage(imageFile, recyclingItemId: recyclingItemId);
      if (url != null) {
        uploadedUrls.add(url);
      }
    }
    
    return uploadedUrls;
  }
  
  /// 删除图片
  Future<bool> deleteImage(String imageUrl) async {
    try {
      final response = await _apiService.post(
        '${AppConfig.apiEndpoint['upload']}/delete',
        data: {'url': imageUrl},
      );
      
      if (response.data['code'] == 1) {
        return true;
      } else {
        Get.log('删除图片失败: ${response.data['msg']}', isError: true);
        return false;
      }
    } catch (e) {
      Get.log('删除图片异常: $e', isError: true);
      return false;
    }
  }
} 