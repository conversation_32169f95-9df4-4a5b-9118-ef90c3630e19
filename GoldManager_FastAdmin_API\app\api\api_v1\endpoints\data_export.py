"""
数据导入导出API路由
"""

import os
from typing import List
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Query
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.dependencies import get_current_admin
from app.models.admin import Admin
from app.services.data_export_service import DataExportService
from app.schemas.data_export import (
    ExportRequest, ExportResponse, ImportRequest, ImportResponse,
    TemplateRequest, TemplateResponse, ExportHistory, ExportHistoryResponse,
    ExportType, ImportType, ExportFormat
)
from app.schemas.common import StandardResponse

# 创建路由器
router = APIRouter()


@router.post("/export", response_model=ExportResponse, summary="数据导出", description="根据条件导出各类业务数据")
async def export_data(
    request: ExportRequest,
    current_admin: Admin = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """
    数据导出API

    支持导出的数据类型：
    - jewelry: 商品数据
    - store: 门店数据
    - member: 会员数据
    - admin: 管理员数据
    - stock_in: 入库数据
    - stock_out: 出库数据
    - stock_return: 退货数据
    - inventory_check: 盘点数据
    - recycling: 回收数据
    - store_transfer: 调拨数据

    支持的导出格式：
    - excel: Excel文件(.xlsx)
    - csv: CSV文件(.csv)
    """
    try:
        service = DataExportService(db)
        result = await service.export_data(request, current_admin.id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导出失败: {str(e)}")


@router.get("/download/{file_name}", summary="下载导出文件", description="下载已生成的导出文件")
async def download_export_file(
    file_name: str,
    current_admin: Admin = Depends(get_current_admin)
):
    """下载导出文件"""
    file_path = os.path.join("exports", file_name)

    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="文件不存在")

    # 根据文件扩展名设置媒体类型
    if file_name.endswith('.xlsx'):
        media_type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    elif file_name.endswith('.csv'):
        media_type = 'text/csv'
    else:
        media_type = 'application/octet-stream'

    return FileResponse(
        path=file_path,
        filename=file_name,
        media_type=media_type
    )


@router.post("/template", response_model=TemplateResponse, summary="生成导入模板", description="生成数据导入模板文件")
async def generate_import_template(
    request: TemplateRequest,
    current_admin: Admin = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """
    生成导入模板

    支持的模板类型：
    - jewelry: 商品导入模板
    - member: 会员导入模板
    - stock_in: 入库导入模板
    """
    try:
        service = DataExportService(db)
        result = await service.generate_template(request)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"模板生成失败: {str(e)}")


@router.get("/template/{file_name}", summary="下载模板文件", description="下载生成的模板文件")
async def download_template_file(
    file_name: str,
    current_admin: Admin = Depends(get_current_admin)
):
    """下载模板文件"""
    file_path = os.path.join("templates", file_name)

    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="模板文件不存在")

    return FileResponse(
        path=file_path,
        filename=file_name,
        media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )


@router.post("/import", response_model=ImportResponse, summary="数据导入", description="从Excel文件导入数据")
async def import_data(
    import_type: ImportType = Query(..., description="导入类型"),
    store_id: int = Query(None, description="目标门店ID"),
    skip_duplicates: bool = Query(True, description="是否跳过重复数据"),
    update_existing: bool = Query(False, description="是否更新已存在数据"),
    validate_only: bool = Query(False, description="是否仅验证不导入"),
    file: UploadFile = File(..., description="导入文件"),
    current_admin: Admin = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """
    数据导入API

    支持导入的数据类型：
    - jewelry: 商品数据
    - member: 会员数据
    - stock_in: 入库数据

    导入选项：
    - skip_duplicates: 跳过重复数据
    - update_existing: 更新已存在数据
    - validate_only: 仅验证数据格式，不实际导入
    """
    # 检查文件类型
    if not file.filename.endswith(('.xlsx', '.xls')):
        raise HTTPException(status_code=400, detail="只支持Excel文件(.xlsx, .xls)")

    try:
        # 保存上传的文件
        upload_dir = "uploads"
        os.makedirs(upload_dir, exist_ok=True)

        file_path = os.path.join(upload_dir, file.filename)
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)

        # 创建导入请求
        import_request = ImportRequest(
            import_type=import_type,
            store_id=store_id,
            operator_id=current_admin.id,
            skip_duplicates=skip_duplicates,
            update_existing=update_existing,
            validate_only=validate_only
        )

        # 执行导入
        service = DataExportService(db)
        result = await service.import_data(import_request, file_path)

        # 清理临时文件
        if os.path.exists(file_path):
            os.remove(file_path)

        return result

    except Exception as e:
        # 清理临时文件
        if 'file_path' in locals() and os.path.exists(file_path):
            os.remove(file_path)
        raise HTTPException(status_code=500, detail=f"导入失败: {str(e)}")


@router.get("/history", response_model=ExportHistoryResponse, summary="导出历史", description="获取数据导出历史记录")
async def get_export_history(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    export_type: str = Query(None, description="导出类型筛选"),
    current_admin: Admin = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """获取导出历史记录"""
    try:
        service = DataExportService(db)
        result = await service.get_export_history(page, page_size, export_type, current_admin.id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取历史记录失败: {str(e)}")


@router.delete("/history/{history_id}", response_model=StandardResponse, summary="删除导出记录", description="删除指定的导出历史记录")
async def delete_export_history(
    history_id: int,
    current_admin: Admin = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """删除导出历史记录"""
    try:
        service = DataExportService(db)
        result = await service.delete_export_history(history_id, current_admin.id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除记录失败: {str(e)}")


@router.get("/types", summary="获取支持的导出类型", description="获取系统支持的所有导出类型")
async def get_export_types():
    """获取支持的导出类型"""
    export_types = [
        {"value": "jewelry", "label": "商品数据", "description": "导出商品基本信息、价格、库存等"},
        {"value": "store", "label": "门店数据", "description": "导出门店基本信息、联系方式等"},
        {"value": "member", "label": "会员数据", "description": "导出会员信息、积分、等级等"},
        {"value": "admin", "label": "管理员数据", "description": "导出管理员账户信息"},
        {"value": "stock_in", "label": "入库数据", "description": "导出入库单据及明细"},
        {"value": "stock_out", "label": "出库数据", "description": "导出出库单据及明细"},
        {"value": "stock_return", "label": "退货数据", "description": "导出退货单据及明细"},
        {"value": "inventory_check", "label": "盘点数据", "description": "导出库存盘点记录"},
        {"value": "recycling", "label": "回收数据", "description": "导出旧料回收记录"},
        {"value": "store_transfer", "label": "调拨数据", "description": "导出门店间调拨记录"}
    ]

    import_types = [
        {"value": "jewelry", "label": "商品数据", "description": "批量导入商品信息"},
        {"value": "member", "label": "会员数据", "description": "批量导入会员信息"},
        {"value": "stock_in", "label": "入库数据", "description": "批量导入入库单据"}
    ]

    export_formats = [
        {"value": "excel", "label": "Excel文件", "extension": ".xlsx"},
        {"value": "csv", "label": "CSV文件", "extension": ".csv"}
    ]

    return {
        "success": True,
        "message": "获取成功",
        "data": {
            "export_types": export_types,
            "import_types": import_types,
            "export_formats": export_formats
        }
    }


@router.get("/statistics", summary="导出统计", description="获取数据导出统计信息")
async def get_export_statistics(
    current_admin: Admin = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """获取导出统计信息"""
    try:
        service = DataExportService(db)
        result = await service.get_export_statistics(current_admin.id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")
