/// 回收处理控制器（新版本）
/// 管理回收物品的处理流程

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/utils/logger.dart';
import '../../../core/utils/dialog_utils.dart';
import '../../../models/recycling/recycling_process_new.dart';
import '../../../models/recycling/recycling_model.dart';
import '../../../models/store/store.dart';
import '../../../services/recycling_process_service.dart';
import '../../../services/store_service.dart';
import '../../../services/auth_service.dart';

class RecyclingProcessNewController extends GetxController {
  final RecyclingProcessService _processService = Get.find<RecyclingProcessService>();
  final StoreService _storeService = Get.find<StoreService>();
  final AuthService _authService = Get.find<AuthService>();

  // 状态变量
  final RxBool isLoading = false.obs;
  final RxList<RecyclingProcessNew> processList = <RecyclingProcessNew>[].obs;
  final RxList<Store> storeList = <Store>[].obs;
  
  // 筛选条件
  final TextEditingController searchController = TextEditingController();
  final Rx<DateTime?> startDate = Rx<DateTime?>(null);
  final Rx<DateTime?> endDate = Rx<DateTime?>(null);
  final RxInt selectedStoreId = 0.obs;
  final Rx<ProcessStatus?> selectedStatus = Rx<ProcessStatus?>(null);
  final Rx<ProcessType?> selectedProcessType = Rx<ProcessType?>(null);
  
  // 分页
  final RxInt currentPage = 1.obs;
  final RxInt totalPages = 1.obs;
  final int pageSize = 20;
  
  // 当前选中的处理工单
  final Rx<RecyclingProcessNew?> currentProcess = Rx<RecyclingProcessNew?>(null);
  
  // 创建工单相关
  final RxInt createStoreId = 0.obs;
  final RxList<int> selectedItemIds = <int>[].obs;
  final Rx<ProcessType> createProcessType = ProcessType.separation.obs;
  final TextEditingController remarkController = TextEditingController();

  @override
  void onInit() {
    super.onInit();
    _initData();
  }

  @override
  void onClose() {
    searchController.dispose();
    remarkController.dispose();
    super.onClose();
  }

  /// 初始化数据
  Future<void> _initData() async {
    await fetchStores();
    await fetchProcessList();
  }

  /// 获取门店列表
  Future<void> fetchStores() async {
    try {
      final stores = await _storeService.getAllStores();
      storeList.value = stores;
      
      // 非管理员自动选择自己的门店
      if (!_authService.isAdmin && _authService.storeId.value > 0) {
        selectedStoreId.value = _authService.storeId.value;
        createStoreId.value = _authService.storeId.value;
      }
    } catch (e) {
      LoggerService.error('获取门店列表失败', e);
    }
  }

  /// 获取处理工单列表
  Future<void> fetchProcessList() async {
    try {
      isLoading.value = true;
      
      final result = await _processService.getProcessList(
        page: currentPage.value,
        pageSize: pageSize,
        keyword: searchController.text.isNotEmpty ? searchController.text : null,
        storeId: selectedStoreId.value > 0 ? selectedStoreId.value : null,
        status: selectedStatus.value?.value,
        processType: selectedProcessType.value?.value,
        startDate: startDate.value?.toIso8601String().split('T').first,
        endDate: endDate.value?.toIso8601String().split('T').first,
      );
      
      processList.value = result.data;
      totalPages.value = result.lastPage;
    } catch (e) {
      LoggerService.error('获取处理工单列表失败', e);
      Get.snackbar('错误', '获取处理工单列表失败');
    } finally {
      isLoading.value = false;
    }
  }

  /// 搜索
  void search() {
    currentPage.value = 1;
    fetchProcessList();
  }

  /// 重置筛选条件
  void resetFilters() {
    searchController.clear();
    startDate.value = null;
    endDate.value = null;
    selectedStoreId.value = 0;
    selectedStatus.value = null;
    selectedProcessType.value = null;
    currentPage.value = 1;
    fetchProcessList();
  }

  /// 分页
  void goToPage(int page) {
    if (page < 1 || page > totalPages.value) return;
    currentPage.value = page;
    fetchProcessList();
  }

  /// 创建处理工单
  Future<void> createProcess(int recyclingId) async {
    try {
      // 验证
      if (createStoreId.value == 0) {
        Get.snackbar('提示', '请选择处理门店');
        return;
      }
      
      if (selectedItemIds.isEmpty) {
        Get.snackbar('提示', '请选择要处理的明细');
        return;
      }
      
      isLoading.value = true;
      
      final process = await _processService.createProcess(
        storeId: createStoreId.value,
        recyclingId: recyclingId,
        recyclingItemIds: selectedItemIds,
        processType: createProcessType.value.value,
        remark: remarkController.text.isNotEmpty ? remarkController.text : null,
      );
      
      Get.snackbar('成功', '处理工单创建成功');
      
      // 清空表单
      selectedItemIds.clear();
      remarkController.clear();
      
      // 刷新列表
      await fetchProcessList();
      
      // 关闭对话框
      Get.back();
    } catch (e) {
      LoggerService.error('创建处理工单失败', e);
      Get.snackbar('错误', '创建处理工单失败: ${e.toString()}');
    } finally {
      isLoading.value = false;
    }
  }

  /// 开始处理
  Future<void> startProcess(int processId) async {
    try {
      final confirmed = await DialogUtils.confirm(
        title: '确认开始处理',
        content: '确定要开始处理此工单吗？',
      );
      
      if (!confirmed) return;
      
      isLoading.value = true;
      
      await _processService.startProcess(processId);
      
      Get.snackbar('成功', '已开始处理');
      
      // 刷新列表
      await fetchProcessList();
    } catch (e) {
      LoggerService.error('开始处理失败', e);
      Get.snackbar('错误', '开始处理失败: ${e.toString()}');
    } finally {
      isLoading.value = false;
    }
  }

  /// 完成处理
  Future<void> completeProcess(int processId, List<ProcessResult> results) async {
    try {
      isLoading.value = true;
      
      await _processService.completeProcess(
        processId,
        results: results,
      );
      
      Get.snackbar('成功', '处理已完成');
      
      // 刷新列表
      await fetchProcessList();
      
      // 关闭对话框
      Get.back();
    } catch (e) {
      LoggerService.error('完成处理失败', e);
      Get.snackbar('错误', '完成处理失败: ${e.toString()}');
    } finally {
      isLoading.value = false;
    }
  }

  /// 取消处理工单
  Future<void> cancelProcess(int processId) async {
    try {
      final confirmed = await DialogUtils.confirm(
        title: '确认取消',
        content: '确定要取消此处理工单吗？',
      );
      
      if (!confirmed) return;
      
      isLoading.value = true;
      
      await _processService.cancelProcess(processId);
      
      Get.snackbar('成功', '处理工单已取消');
      
      // 刷新列表
      await fetchProcessList();
    } catch (e) {
      LoggerService.error('取消处理工单失败', e);
      Get.snackbar('错误', '取消处理工单失败: ${e.toString()}');
    } finally {
      isLoading.value = false;
    }
  }

  /// 转为库存
  Future<void> convertToStock({
    required int processResultId,
    required int storeId,
    required StockType stockType,
    int quantity = 1,
    String? remark,
    Map<String, dynamic>? jewelryInfo,
    int? materialType,
  }) async {
    try {
      isLoading.value = true;
      
      await _processService.convertToStock(
        storeId: storeId,
        processResultId: processResultId,
        stockType: stockType.value,
        quantity: quantity,
        remark: remark,
        jewelryInfo: jewelryInfo,
        materialType: materialType,
      );
      
      Get.snackbar('成功', '已成功转为库存');
      
      // 刷新列表
      await fetchProcessList();
      
      // 关闭对话框
      Get.back();
    } catch (e) {
      LoggerService.error('转库存失败', e);
      Get.snackbar('错误', '转库存失败: ${e.toString()}');
    } finally {
      isLoading.value = false;
    }
  }

  /// 查看处理工单详情
  Future<void> viewProcessDetail(int processId) async {
    try {
      isLoading.value = true;
      currentProcess.value = await _processService.getProcessDetail(processId);
    } catch (e) {
      LoggerService.error('获取处理工单详情失败', e);
      Get.snackbar('错误', '获取处理工单详情失败');
    } finally {
      isLoading.value = false;
    }
  }

  /// 获取可处理的回收明细
  Future<List<Map<String, dynamic>>> getAvailableRecyclingItems(int recyclingId) async {
    try {
      return await _processService.getAvailableRecyclingItems(recyclingId);
    } catch (e) {
      LoggerService.error('获取可处理的回收明细失败', e);
      return [];
    }
  }

  /// 获取状态颜色
  Color getStatusColor(ProcessStatus status) {
    switch (status) {
      case ProcessStatus.cancelled:
        return Colors.grey;
      case ProcessStatus.pending:
        return Colors.orange;
      case ProcessStatus.processing:
        return Colors.blue;
      case ProcessStatus.completed:
        return Colors.green;
    }
  }

  /// 获取处理类型颜色
  Color getProcessTypeColor(ProcessType type) {
    switch (type) {
      case ProcessType.separation:
        return Colors.purple;
      case ProcessType.refurbish:
        return Colors.teal;
      case ProcessType.melt:
        return Colors.deepOrange;
    }
  }
}