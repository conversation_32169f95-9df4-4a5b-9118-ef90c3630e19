import 'package:intl/intl.dart';

/// 日期格式化工具类
/// 
/// 提供统一的日期时间格式化功能
/// 支持多种常用的日期时间格式
class DateFormatter {
  // 私有构造函数，防止实例化
  DateFormatter._();

  /// 默认日期时间格式：yyyy-MM-dd HH:mm:ss
  static const String _defaultDateTimeFormat = 'yyyy-MM-dd HH:mm:ss';
  
  /// 默认日期格式：yyyy-MM-dd
  static const String _defaultDateFormat = 'yyyy-MM-dd';
  
  /// 默认时间格式：HH:mm:ss
  static const String _defaultTimeFormat = 'HH:mm:ss';
  
  /// 短时间格式：HH:mm
  static const String _shortTimeFormat = 'HH:mm';
  
  /// 中文日期时间格式：yyyy年MM月dd日 HH:mm:ss
  static const String _chineseDateTimeFormat = 'yyyy年MM月dd日 HH:mm:ss';
  
  /// 中文日期格式：yyyy年MM月dd日
  static const String _chineseDateFormat = 'yyyy年MM月dd日';

  /// 格式化日期时间为默认格式
  /// 
  /// [dateTime] 要格式化的日期时间
  /// 返回格式：yyyy-MM-dd HH:mm:ss
  static String formatDateTime(DateTime dateTime) {
    return DateFormat(_defaultDateTimeFormat).format(dateTime);
  }

  /// 格式化日期为默认格式
  /// 
  /// [dateTime] 要格式化的日期时间
  /// 返回格式：yyyy-MM-dd
  static String formatDate(DateTime dateTime) {
    return DateFormat(_defaultDateFormat).format(dateTime);
  }

  /// 格式化时间为默认格式
  /// 
  /// [dateTime] 要格式化的日期时间
  /// 返回格式：HH:mm:ss
  static String formatTime(DateTime dateTime) {
    return DateFormat(_defaultTimeFormat).format(dateTime);
  }

  /// 格式化时间为短格式
  /// 
  /// [dateTime] 要格式化的日期时间
  /// 返回格式：HH:mm
  static String formatShortTime(DateTime dateTime) {
    return DateFormat(_shortTimeFormat).format(dateTime);
  }

  /// 格式化日期时间为中文格式
  /// 
  /// [dateTime] 要格式化的日期时间
  /// 返回格式：yyyy年MM月dd日 HH:mm:ss
  static String formatChineseDateTime(DateTime dateTime) {
    return DateFormat(_chineseDateTimeFormat).format(dateTime);
  }

  /// 格式化日期为中文格式
  /// 
  /// [dateTime] 要格式化的日期时间
  /// 返回格式：yyyy年MM月dd日
  static String formatChineseDate(DateTime dateTime) {
    return DateFormat(_chineseDateFormat).format(dateTime);
  }

  /// 使用自定义格式格式化日期时间
  /// 
  /// [dateTime] 要格式化的日期时间
  /// [pattern] 自定义格式模式
  static String formatCustom(DateTime dateTime, String pattern) {
    return DateFormat(pattern).format(dateTime);
  }

  /// 格式化相对时间（如：刚刚、5分钟前、1小时前等）
  /// 
  /// [dateTime] 要格式化的日期时间
  /// [locale] 语言环境，默认为中文
  static String formatRelativeTime(DateTime dateTime, {String locale = 'zh_CN'}) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inSeconds < 60) {
      return '刚刚';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}分钟前';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}小时前';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}天前';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return '$weeks周前';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return '$months个月前';
    } else {
      final years = (difference.inDays / 365).floor();
      return '$years年前';
    }
  }

  /// 解析日期字符串为DateTime对象
  /// 
  /// [dateString] 日期字符串
  /// [pattern] 日期格式模式，默认为 yyyy-MM-dd HH:mm:ss
  static DateTime? parseDateTime(String dateString, {String? pattern}) {
    try {
      final format = DateFormat(pattern ?? _defaultDateTimeFormat);
      return format.parse(dateString);
    } catch (e) {
      return null;
    }
  }

  /// 解析日期字符串为DateTime对象
  /// 
  /// [dateString] 日期字符串
  /// [pattern] 日期格式模式，默认为 yyyy-MM-dd
  static DateTime? parseDate(String dateString, {String? pattern}) {
    try {
      final format = DateFormat(pattern ?? _defaultDateFormat);
      return format.parse(dateString);
    } catch (e) {
      return null;
    }
  }

  /// 判断两个日期是否为同一天
  /// 
  /// [date1] 第一个日期
  /// [date2] 第二个日期
  static bool isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
           date1.month == date2.month &&
           date1.day == date2.day;
  }

  /// 判断日期是否为今天
  /// 
  /// [date] 要判断的日期
  static bool isToday(DateTime date) {
    return isSameDay(date, DateTime.now());
  }

  /// 判断日期是否为昨天
  /// 
  /// [date] 要判断的日期
  static bool isYesterday(DateTime date) {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return isSameDay(date, yesterday);
  }

  /// 获取友好的日期显示文本
  /// 
  /// [dateTime] 要格式化的日期时间
  /// 如果是今天，显示"今天 HH:mm"
  /// 如果是昨天，显示"昨天 HH:mm"
  /// 如果是本年，显示"MM-dd HH:mm"
  /// 否则显示完整日期时间
  static String formatFriendlyDateTime(DateTime dateTime) {
    final now = DateTime.now();
    
    if (isToday(dateTime)) {
      return '今天 ${formatShortTime(dateTime)}';
    } else if (isYesterday(dateTime)) {
      return '昨天 ${formatShortTime(dateTime)}';
    } else if (dateTime.year == now.year) {
      return DateFormat('MM-dd HH:mm').format(dateTime);
    } else {
      return formatDateTime(dateTime);
    }
  }

  /// 格式化时间戳（秒）为日期时间字符串
  /// 
  /// [timestamp] 时间戳（秒）
  static String formatTimestamp(int timestamp) {
    final dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
    return formatDateTime(dateTime);
  }

  /// 格式化时间戳（毫秒）为日期时间字符串
  /// 
  /// [timestamp] 时间戳（毫秒）
  static String formatTimestampMs(int timestamp) {
    final dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
    return formatDateTime(dateTime);
  }

  /// 获取日期时间的开始时间（当天00:00:00）
  /// 
  /// [dateTime] 日期时间
  static DateTime getStartOfDay(DateTime dateTime) {
    return DateTime(dateTime.year, dateTime.month, dateTime.day);
  }

  /// 获取日期时间的结束时间（当天23:59:59）
  /// 
  /// [dateTime] 日期时间
  static DateTime getEndOfDay(DateTime dateTime) {
    return DateTime(dateTime.year, dateTime.month, dateTime.day, 23, 59, 59);
  }
}
