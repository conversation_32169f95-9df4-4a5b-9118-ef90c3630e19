import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/utils/dialog_utils.dart';
import '../../../models/recycling/recycling_model.dart';
import '../services/category_service.dart';

/// 旧料分类控制器
class CategoryController extends GetxController {
  final CategoryService _categoryService = Get.find<CategoryService>();
  
  // 可观察变量
  final RxBool isLoading = false.obs;
  final RxBool isSubmitting = false.obs;
  final RxList<RecyclingCategory> categories = <RecyclingCategory>[].obs;
  final RxBool showInactiveCategories = true.obs;
  final RxString searchQuery = ''.obs;
  final TextEditingController searchController = TextEditingController();
  
  // 编辑相关
  final Rx<RecyclingCategory?> currentCategory = Rx<RecyclingCategory?>(null);
  final TextEditingController nameController = TextEditingController();
  final TextEditingController descriptionController = TextEditingController();
  final RxBool isActiveController = true.obs;
  
  // 表单验证器
  final formKey = GlobalKey<FormState>();
  
  @override
  void onInit() {
    super.onInit();
    loadCategories();
    
    // 监听搜索查询变化
    debounce(
      searchQuery,
      (_) => filterCategories(),
      time: const Duration(milliseconds: 500),
    );
  }
  
  @override
  void onClose() {
    nameController.dispose();
    descriptionController.dispose();
    searchController.dispose();
    super.onClose();
  }
  
  /// 加载所有分类
  Future<void> loadCategories() async {
    isLoading.value = true;
    try {
      final allCategories = await _categoryService.getAllCategories();
      categories.assignAll(allCategories);
      filterCategories();
    } finally {
      isLoading.value = false;
    }
  }
  
  /// 根据搜索条件和活跃状态筛选分类
  void filterCategories() {
    isLoading.value = true;
    try {
      final query = searchQuery.value.toLowerCase();
      final filteredList = _categoryService.getAllCategories().then((allCategories) {
        if (query.isEmpty && showInactiveCategories.value) {
          return allCategories;
        }
        
        return allCategories.where((category) {
          // 如果不显示非活跃分类且当前分类不活跃，则过滤掉
          if (!showInactiveCategories.value && !category.isActive) {
            return false;
          }
          
          // 如果有搜索查询，则按名称和描述过滤
          if (query.isNotEmpty) {
            final name = category.name.toLowerCase();
            final description = category.description?.toLowerCase() ?? '';
            return name.contains(query) || description.contains(query);
          }
          
          return true;
        }).toList();
      });
      
      filteredList.then((list) => categories.assignAll(list));
    } finally {
      isLoading.value = false;
    }
  }
  
  /// 切换是否显示非活跃分类
  void toggleShowInactive() {
    showInactiveCategories.toggle();
    filterCategories();
  }
  
  /// 设置搜索查询
  void setSearchQuery(String query) {
    searchQuery.value = query;
  }
  
  /// 清除搜索
  void clearSearch() {
    searchQuery.value = '';
    searchController.clear();
  }
  
  /// 初始化编辑表单
  void initEditForm(RecyclingCategory? category) {
    if (category != null) {
      // 编辑模式
      currentCategory.value = category;
      nameController.text = category.name;
      descriptionController.text = category.description ?? '';
      isActiveController.value = category.isActive;
    } else {
      // 添加模式
      currentCategory.value = null;
      nameController.clear();
      descriptionController.clear();
      isActiveController.value = true;
    }
  }
  
  /// 保存分类
  Future<bool> saveCategory() async {
    if (!formKey.currentState!.validate()) {
      return false;
    }
    
    isSubmitting.value = true;
    try {
      final category = RecyclingCategory(
        id: currentCategory.value?.id ?? 0,
        name: nameController.text.trim(),
        description: descriptionController.text.trim().isEmpty ? null : descriptionController.text.trim(),
        isActive: isActiveController.value,
      );
      
      bool success;
      if (currentCategory.value == null) {
        // 添加新分类
        success = await _categoryService.addCategory(category);
        if (success) {
          DialogUtils.showSuccessSnackBar(
            title: '添加成功',
            message: '分类"${category.name}"已添加',
          );
        } else {
          DialogUtils.showErrorSnackBar(
            title: '添加失败',
            message: '添加分类时发生错误',
          );
        }
      } else {
        // 更新分类
        success = await _categoryService.updateCategory(category);
        if (success) {
          DialogUtils.showSuccessSnackBar(
            title: '更新成功',
            message: '分类"${category.name}"已更新',
          );
        } else {
          DialogUtils.showErrorSnackBar(
            title: '更新失败',
            message: '更新分类时发生错误',
          );
        }
      }
      
      if (success) {
        await loadCategories();
        return true;
      }
      
      return false;
    } finally {
      isSubmitting.value = false;
    }
  }
  
  /// 更新分类状态
  Future<bool> updateCategoryStatus(int id, bool isActive) async {
    isSubmitting.value = true;
    try {
      final success = await _categoryService.updateCategoryStatus(id, isActive);
      if (success) {
        final category = categories.firstWhere((c) => c.id == id);
        final statusText = isActive ? '启用' : '禁用';
        DialogUtils.showSuccessSnackBar(
          title: '状态更新成功',
          message: '分类"${category.name}"已$statusText',
        );
        
        await loadCategories();
      } else {
        DialogUtils.showErrorSnackBar(
          title: '状态更新失败',
          message: '更新分类状态时发生错误',
        );
      }
      
      return success;
    } finally {
      isSubmitting.value = false;
    }
  }
  
  /// A删除分类
  Future<bool> deleteCategory(int id) async {
    isSubmitting.value = true;
    try {
      final success = await _categoryService.deleteCategory(id);
      if (success) {
        DialogUtils.showSuccessSnackBar(
          title: '删除成功',
          message: '分类已删除',
        );
        
        await loadCategories();
      } else {
        DialogUtils.showErrorSnackBar(
          title: '删除失败',
          message: '删除分类时发生错误',
        );
      }
      
      return success;
    } finally {
      isSubmitting.value = false;
    }
  }
} 