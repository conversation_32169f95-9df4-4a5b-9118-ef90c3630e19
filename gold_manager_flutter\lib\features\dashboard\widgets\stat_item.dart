import 'package:flutter/material.dart';
import '../../../core/constants/border_styles.dart';

/// 仪表盘统计项组件
/// 提供多种样式的统计数据展示组件
class DashboardStatItem extends StatelessWidget {
  /// 统计项标签
  final String label;
  
  /// 统计项数值
  final String value;
  
  /// 图标
  final IconData icon;
  
  /// 颜色主题
  final Color color;
  
  /// 统计项样式类型
  final StatItemStyle style;
  
  /// 点击回调
  final VoidCallback? onTap;
  
  /// 是否显示边框
  final bool showBorder;
  
  /// 自定义背景色
  final Color? backgroundColor;

  const DashboardStatItem({
    super.key,
    required this.label,
    required this.value,
    required this.icon,
    required this.color,
    this.style = StatItemStyle.compact,
    this.onTap,
    this.showBorder = true,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    switch (style) {
      case StatItemStyle.compact:
        return _buildCompactStyle();
      case StatItemStyle.card:
        return _buildCardStyle();
      case StatItemStyle.inline:
        return _buildInlineStyle();
      case StatItemStyle.vertical:
        return _buildVerticalStyle();
    }
  }

  /// 紧凑型样式 - 用于详细统计区域
  Widget _buildCompactStyle() {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: backgroundColor ?? Colors.white,
            borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
            border: showBorder ? Border.all(color: AppBorderStyles.borderColor) : null,
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(icon, size: 16, color: color),
              const SizedBox(width: 8),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    label,
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                      color: Colors.black87,
                    ),
                  ),
                  Text(
                    value,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: color,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 卡片型样式 - 用于重要统计展示
  Widget _buildCardStyle() {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppBorderStyles.mediumBorderRadius),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: AppBorderStyles.standardBoxDecoration.copyWith(
            color: backgroundColor ?? color.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(AppBorderStyles.mediumBorderRadius),
            border: showBorder ? Border.all(color: color.withValues(alpha: 0.2)) : null,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, size: 24, color: color),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 行内型样式 - 用于快速概览
  Widget _buildInlineStyle() {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(icon, size: 16, color: color),
              const SizedBox(width: 4),
              Text(
                '$label: ',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ),
              Text(
                value,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 垂直型样式 - 用于统计信息区域
  Widget _buildVerticalStyle() {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: backgroundColor ?? color.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
            border: showBorder ? Border.all(color: color.withValues(alpha: 0.2)) : null,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(icon, size: 20, color: color),
              const SizedBox(height: 4),
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// 统计项样式枚举
enum StatItemStyle {
  /// 紧凑型 - 水平布局，适合详细统计
  compact,
  
  /// 卡片型 - 垂直布局，适合重要指标
  card,
  
  /// 行内型 - 最紧凑，适合快速概览
  inline,
  
  /// 垂直型 - 垂直布局，适合统计区域
  vertical,
}

/// 统计项网格布局
/// 用于展示多个统计项
class StatItemGrid extends StatelessWidget {
  final List<DashboardStatItem> items;
  final int crossAxisCount;
  final double mainAxisSpacing;
  final double crossAxisSpacing;
  final double childAspectRatio;
  final EdgeInsetsGeometry? padding;
  final bool shrinkWrap;
  final ScrollPhysics? physics;

  const StatItemGrid({
    super.key,
    required this.items,
    this.crossAxisCount = 4,
    this.mainAxisSpacing = 12,
    this.crossAxisSpacing = 12,
    this.childAspectRatio = 2.5,
    this.padding,
    this.shrinkWrap = true,
    this.physics = const NeverScrollableScrollPhysics(),
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding ?? EdgeInsets.zero,
      child: GridView.count(
        crossAxisCount: crossAxisCount,
        mainAxisSpacing: mainAxisSpacing,
        crossAxisSpacing: crossAxisSpacing,
        childAspectRatio: childAspectRatio,
        shrinkWrap: shrinkWrap,
        physics: physics,
        children: items,
      ),
    );
  }
}

/// 统计项包装器
/// 用于快速创建不同主题色的统计项
class StatItemWrapper extends StatelessWidget {
  final List<StatItemData> items;
  final StatItemStyle style;
  final EdgeInsetsGeometry? padding;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;
  final bool wrap;

  const StatItemWrapper({
    super.key,
    required this.items,
    this.style = StatItemStyle.compact,
    this.padding,
    this.mainAxisAlignment = MainAxisAlignment.spaceAround,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.wrap = false,
  });

  @override
  Widget build(BuildContext context) {
    final widgets = items.map((item) => DashboardStatItem(
      label: item.label,
      value: item.value,
      icon: item.icon,
      color: item.color,
      style: style,
      onTap: item.onTap,
    )).toList();

    if (wrap) {
      return Padding(
        padding: padding ?? EdgeInsets.zero,
        child: Wrap(
          spacing: 12,
          runSpacing: 8,
          children: widgets,
        ),
      );
    }

    return Padding(
      padding: padding ?? EdgeInsets.zero,
      child: Row(
        mainAxisAlignment: mainAxisAlignment,
        crossAxisAlignment: crossAxisAlignment,
        children: widgets,
      ),
    );
  }
}

/// 统计项数据模型
class StatItemData {
  final String label;
  final String value;
  final IconData icon;
  final Color color;
  final VoidCallback? onTap;

  const StatItemData({
    required this.label,
    required this.value,
    required this.icon,
    required this.color,
    this.onTap,
  });
}
