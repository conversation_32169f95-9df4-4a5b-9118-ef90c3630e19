import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../core/constants/border_styles.dart';
import '../../../core/theme/app_theme.dart';
import '../../../models/recycling/recycling_model.dart';
import '../../../widgets/empty_state.dart';
import '../../../widgets/loading_state.dart';
import '../../../widgets/responsive_builder.dart';
import '../../../services/auth_service.dart';
import '../../../core/utils/logger.dart';
import '../controllers/recycling_controller.dart';
import '../controllers/recycling_tab_controller.dart';

import 'recycling_form_page.dart';
import 'widgets/recycling_detail_dialog.dart';
import 'widgets/api_test_dialog.dart';
import 'widgets/diagnosis_page.dart';

/// 旧料回收管理页面 - 完全复用出库管理页面的UI结构
class RecyclingListPage extends StatelessWidget {
  const RecyclingListPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(RecyclingController());

    return Material(
      color: AppTheme.backgroundColor,
      child: Column(
        children: [
          _buildFilterSection(controller),
          const Divider(height: 1),
          Expanded(
            child: Obx(() {
              if (controller.isLoading.value) {
                return const LoadingState(text: '加载中...', timeoutSeconds: 30);
              }

              if (controller.recyclingOrders.isEmpty) {
                return EmptyState(
                  icon: Icons.recycling,
                  title: '暂无回收单据',
                  message: '点击右上角按钮创建新的回收单',
                  buttonText: '新建回收单',
                  onButtonPressed: () => _navigateToAddRecycling(),
                );
              }

              return _buildRecyclingList(controller);
            }),
          ),
        ],
      ),
    );
  }

  /// 构建筛选区域 - 完全复用出库管理页面的单行布局结构
  Widget _buildFilterSection(RecyclingController controller) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(bottom: AppBorderStyles.tableBorder),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center, // 确保所有控件垂直居中对齐
        children: [
          // 图标和标题 - 复用出库管理页面的样式
          Icon(Icons.recycling, color: Colors.purple[600], size: 20),
          const SizedBox(width: 8),
          const Text(
            '旧料回收管理',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(width: 24),

          // 操作员信息标签 - 复用出库管理页面的样式
          Obx(() {
            final authService = Get.find<AuthService>();
            final operatorName = authService.userNickname.value.isNotEmpty
                ? authService.userNickname.value
                : authService.userName.value;
            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.purple[50],
                borderRadius: BorderRadius.circular(
                  AppBorderStyles.largeBorderRadius,
                ), // 使用统一的大圆角
                border: Border.all(
                  color: Colors.purple[200]!,
                  width: AppBorderStyles.borderWidth,
                ), // 使用统一边框宽度
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.person, size: 14, color: Colors.purple[600]),
                  const SizedBox(width: 4),
                  Text(
                    '操作员: $operatorName',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.purple[700],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            );
          }),
          const SizedBox(width: 24),

          // 状态标签和选择器 - 复用出库管理页面的样式
          const Text(
            '状态:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(width: 8),
          SizedBox(width: 120, child: _buildCompactStatusSelector(controller)),
          const SizedBox(width: 24),

          // 搜索框 - 紧凑型设计
          const Text(
            '搜索:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(width: 8),
          SizedBox(width: 200, child: _buildCompactSearchField(controller)),
          const SizedBox(width: 24),

          // 操作按钮组
          _buildResetButton(controller),
          const SizedBox(width: 8),
          _buildSearchButton(controller),
          const SizedBox(width: 8),
          _buildTestButton(),
          const SizedBox(width: 8),
          _buildNewRecyclingButton(),
        ],
      ),
    );
  }

  /// 构建紧凑型搜索字段（单行布局）
  Widget _buildCompactSearchField(RecyclingController controller) {
    return Container(
      height: 32, // 紧凑高度
      decoration: AppBorderStyles.standardBoxDecoration,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: TextField(
          controller: controller.searchController,
          decoration: const InputDecoration(
            hintText: '回收单号、客户名等',
            border: InputBorder.none,
            enabledBorder: InputBorder.none,
            focusedBorder: InputBorder.none,
            disabledBorder: InputBorder.none,
            errorBorder: InputBorder.none,
            focusedErrorBorder: InputBorder.none,
            contentPadding: EdgeInsets.zero,
            hintStyle: TextStyle(fontSize: 13, color: Colors.grey),
            isDense: true,
          ),
          style: const TextStyle(fontSize: 13),
          onSubmitted: (_) {
            controller.searchQuery.value = controller.searchController.text
                .trim();
            controller.search();
          },
        ),
      ),
    );
  }

  /// 构建紧凑型状态选择器（单行布局）
  Widget _buildCompactStatusSelector(RecyclingController controller) {
    return Obx(
      () => Container(
        height: 32, // 紧凑高度
        decoration: AppBorderStyles.standardBoxDecoration,
        child: DropdownButtonHideUnderline(
          child: DropdownButton<int>(
            value: controller.statusFilter.value == -1
                ? null
                : controller.statusFilter.value,
            hint: const Text(
              '选择状态',
              style: TextStyle(fontSize: 13, color: Colors.grey),
            ),
            isExpanded: true,
            items: const [
              DropdownMenuItem<int>(
                value: null,
                child: Text('全部状态', style: TextStyle(fontSize: 13)),
              ),
              DropdownMenuItem<int>(
                value: 0,
                child: Text('待处理', style: TextStyle(fontSize: 13)),
              ),
              DropdownMenuItem<int>(
                value: 1,
                child: Text('已处理', style: TextStyle(fontSize: 13)),
              ),
              DropdownMenuItem<int>(
                value: 2,
                child: Text('已完成', style: TextStyle(fontSize: 13)),
              ),
              DropdownMenuItem<int>(
                value: 3,
                child: Text('已取消', style: TextStyle(fontSize: 13)),
              ),
            ],
            onChanged: (value) {
              controller.statusFilter.value = value ?? -1;
              controller.search(); // 重置到第一页并搜索
            },
            style: const TextStyle(fontSize: 13, color: Colors.black87),
            icon: const Icon(Icons.arrow_drop_down, size: 20),
            iconSize: 20,
            menuMaxHeight: 300,
            padding: const EdgeInsets.symmetric(horizontal: 8),
          ),
        ),
      ),
    );
  }

  /// 构建重置按钮 - 紧凑型设计
  Widget _buildResetButton(RecyclingController controller) {
    return SizedBox(
      height: 32, // 强制高度为32px，与其他控件保持一致
      child: OutlinedButton.icon(
        icon: const Icon(Icons.refresh, size: 14),
        label: const Text('重置'),
        style: OutlinedButton.styleFrom(
          foregroundColor: Colors.grey[600],
          padding: const EdgeInsets.symmetric(
            vertical: 0,
            horizontal: 12,
          ), // 移除垂直内边距，由SizedBox控制高度
          minimumSize: const Size(0, 32), // 固定高度32px
          maximumSize: const Size(double.infinity, 32), // 限制最大高度
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
          ),
          side: const BorderSide(color: AppBorderStyles.borderColor),
          textStyle: const TextStyle(fontSize: 13), // 紧凑字体
        ),
        onPressed: () => controller.resetFilters(),
      ),
    );
  }

  /// 构建搜索按钮 - 紧凑型设计
  Widget _buildSearchButton(RecyclingController controller) {
    return SizedBox(
      height: 32, // 强制高度为32px，与其他控件保持一致
      child: ElevatedButton.icon(
        icon: const Icon(Icons.search, size: 14),
        label: const Text('搜索'),
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF1E88E5), // 使用UI规范主色调
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(
            vertical: 0,
            horizontal: 12,
          ), // 移除垂直内边距，由SizedBox控制高度
          minimumSize: const Size(0, 32), // 固定高度32px
          maximumSize: const Size(double.infinity, 32), // 限制最大高度
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
          ),
          textStyle: const TextStyle(fontSize: 13), // 紧凑字体
        ),
        onPressed: () => controller.loadRecyclingOrders(), // 刷新保持当前页
      ),
    );
  }

  /// 构建测试按钮 - 用于调试API数据解析
  Widget _buildTestButton() {
    return SizedBox(
      height: 32,
      child: ElevatedButton.icon(
        icon: const Icon(Icons.bug_report, size: 14),
        label: const Text('测试'),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.orange[600],
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(
            vertical: 0,
            horizontal: 12,
          ),
          minimumSize: const Size(0, 32),
          maximumSize: const Size(double.infinity, 32),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
          ),
          textStyle: const TextStyle(fontSize: 13),
        ),
        onPressed: () => Get.to(() => const DiagnosisPage()),
      ),
    );
  }

  /// 构建新建回收单按钮 - 紧凑型设计
  Widget _buildNewRecyclingButton() {
    return SizedBox(
      height: 32, // 强制高度为32px，与其他控件保持一致
      child: ElevatedButton.icon(
        icon: const Icon(Icons.add_box, size: 14),
        label: const Text('新建回收单'),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.purple[600], // 紫色背景，与回收主题一致
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(
            vertical: 0,
            horizontal: 12,
          ), // 移除垂直内边距，由SizedBox控制高度
          minimumSize: const Size(0, 32), // 固定高度32px
          maximumSize: const Size(double.infinity, 32), // 限制最大高度
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
          ),
          textStyle: const TextStyle(fontSize: 13), // 紧凑字体
        ),
        onPressed: _navigateToAddRecycling,
      ),
    );
  }

  /// 构建回收单列表 - 完全复用出库管理页面的结构
  Widget _buildRecyclingList(RecyclingController controller) {
    return Column(
      children: [
        Expanded(
          child: ScreenTypeLayout(
            mobile: _buildListView(controller),
            tablet: _buildDataTable(controller),
            desktop: _buildDataTable(controller),
          ),
        ),
        _buildPagination(controller),
      ],
    );
  }

  /// 构建列表视图 (用于移动设备) - 复用出库管理页面的样式
  Widget _buildListView(RecyclingController controller) {
    return ListView.separated(
      padding: const EdgeInsets.all(16),
      itemCount: controller.recyclingOrders.length,
      separatorBuilder: (context, index) => const Divider(height: 16),
      itemBuilder: (context, index) {
        final order = controller.recyclingOrders[index];

        return Card(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(
              AppBorderStyles.largeBorderRadius,
            ), // 使用统一的大圆角
          ),
          elevation: 2,
          child: InkWell(
            borderRadius: BorderRadius.circular(
              AppBorderStyles.largeBorderRadius,
            ), // 使用统一的大圆角
            onTap: () => _navigateToRecyclingDetail(order.id),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        order.orderNo,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      _buildStatusBadge(order.status),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      const Icon(Icons.person, size: 16, color: Colors.grey),
                      const SizedBox(width: 4),
                      Text(
                        order.customerName,
                        style: const TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      const Icon(
                        Icons.access_time,
                        size: 16,
                        color: Colors.grey,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        order.formattedDate,
                        style: const TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      const Icon(
                        Icons.inventory_2,
                        size: 16,
                        color: Colors.grey,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '件数: ${order.itemCount}件',
                        style: const TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '金额: ¥${order.totalAmount.toStringAsFixed(2)}',
                    style: const TextStyle(
                      fontWeight: FontWeight.w500,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: _buildActionButtons(order),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// 格式化日期
  String _formatDate(DateTime date) {
    return DateFormat('yyyy-MM-dd HH:mm').format(date);
  }

  /// 构建数据表格 (用于平板和桌面设备) - 完全复用出库管理页面的样式
  Widget _buildDataTable(RecyclingController controller) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // 获取可用宽度
        final availableWidth = constraints.maxWidth;

        // 优化列宽分配：添加所属门店和操作员列后重新分配比例
        final orderNoWidth = availableWidth * 0.15; // 15% - 回收单号
        final customerWidth = availableWidth * 0.12; // 12% - 客户
        final timeWidth = availableWidth * 0.14; // 14% - 回收时间（增加宽度以显示完整时间）
        final countWidth = availableWidth * 0.06; // 6% - 件数
        final storeWidth = availableWidth * 0.10; // 10% - 所属门店
        final operatorWidth = availableWidth * 0.10; // 10% - 操作员
        final amountWidth = availableWidth * 0.10; // 10% - 金额
        final statusWidth = availableWidth * 0.08; // 8% - 状态
        final actionWidth = availableWidth * 0.15; // 15% - 操作

        // 根据可用宽度调整字体大小
        final fontSize = availableWidth < 800 ? 12.0 : 14.0;
        final headingFontSize = availableWidth < 800 ? 13.0 : 15.0;

        return Container(
          margin: const EdgeInsets.all(4), // 与出库管理页面保持一致的边距
          decoration: AppBorderStyles.elevatedBoxDecoration.copyWith(
            color: Colors.white, // 明确设置背景色为白色
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(
              AppBorderStyles.borderRadius,
            ), // 使用标准圆角
            child: SingleChildScrollView(
              scrollDirection: Axis.vertical,
              child: SizedBox(
                width: availableWidth,
                child: DataTable(
                  columnSpacing: 0, // 移除列间距，让列宽完全由我们控制
                  horizontalMargin: 0, // 移除水平边距
                  headingRowHeight: 44, // 与出库管理页面保持一致的表头高度
                  dataRowMinHeight: 48, // 与出库管理页面保持一致的数据行高度
                  dataRowMaxHeight: 48,
                  headingTextStyle: TextStyle(
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                    fontSize: headingFontSize,
                  ),
                  dataTextStyle: TextStyle(
                    fontSize: fontSize,
                    color: Colors.black87,
                  ),
                  headingRowColor: WidgetStateProperty.all(
                    AppBorderStyles.tableHeaderBackground,
                  ), // 使用统一的表头背景色
                  border: AppBorderStyles.tableStandardBorder, // 使用统一的表格边框
                  columns: [
                    DataColumn(
                      label: Container(
                        width: orderNoWidth,
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: const Text('回收单号', textAlign: TextAlign.center),
                      ),
                    ),
                    DataColumn(
                      label: Container(
                        width: customerWidth,
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: const Text('客户', textAlign: TextAlign.center),
                      ),
                    ),
                    DataColumn(
                      label: Container(
                        width: timeWidth,
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: const Text('回收时间', textAlign: TextAlign.center),
                      ),
                    ),
                    DataColumn(
                      label: Container(
                        width: countWidth,
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: const Text('件数', textAlign: TextAlign.center),
                      ),
                    ),
                    DataColumn(
                      label: Container(
                        width: storeWidth,
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: const Text('所属门店', textAlign: TextAlign.center),
                      ),
                    ),
                    DataColumn(
                      label: Container(
                        width: operatorWidth,
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: const Text('操作员', textAlign: TextAlign.center),
                      ),
                    ),
                    DataColumn(
                      label: Container(
                        width: amountWidth,
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: const Text('金额', textAlign: TextAlign.center),
                      ),
                    ),
                    DataColumn(
                      label: Container(
                        width: statusWidth,
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: const Text('状态', textAlign: TextAlign.center),
                      ),
                    ),
                    DataColumn(
                      label: Container(
                        width: actionWidth,
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: const Text('操作', textAlign: TextAlign.center),
                      ),
                    ),
                  ],
                  rows: controller.recyclingOrders.map((order) {
                    return DataRow(
                      cells: [
                        DataCell(
                          Container(
                            width: orderNoWidth,
                            height: 48, // 与出库管理页面保持一致的固定高度
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                            alignment: Alignment.center, // 垂直和水平都居中
                            child: Text(
                              order.orderNo,
                              style: const TextStyle(
                                fontWeight: FontWeight.w500,
                                color: AppTheme.primaryColor,
                              ),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                          onTap: () => _navigateToRecyclingDetail(order.id),
                        ),
                        DataCell(
                          Container(
                            width: customerWidth,
                            height: 48, // 与出库管理页面保持一致的高度
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                            alignment: Alignment.center,
                            child: Text(
                              order.customerName,
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                        ),
                        DataCell(
                          Container(
                            width: timeWidth,
                            height: 48, // 与出库管理页面保持一致的高度
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                            alignment: Alignment.center,
                            child: Text(
                              order.formattedDateTime, // 使用完整的日期时间格式
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                        ),
                        DataCell(
                          Container(
                            width: countWidth,
                            height: 48, // 与出库管理页面保持一致的高度
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                            alignment: Alignment.center,
                            child: Text(
                              '${order.itemCount}件',
                              style: const TextStyle(
                                fontWeight: FontWeight.w500,
                                color: Colors.black87,
                              ),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                        ),
                        DataCell(
                          Container(
                            width: storeWidth,
                            height: 48, // 与出库管理页面保持一致的高度
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                            alignment: Alignment.center,
                            child: Text(
                              order.storeName,
                              style: const TextStyle(
                                fontWeight: FontWeight.w500,
                                color: Colors.black87,
                              ),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                        ),
                        DataCell(
                          Container(
                            width: operatorWidth,
                            height: 48, // 与出库管理页面保持一致的高度
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                            alignment: Alignment.center,
                            child: Text(
                              order.creatorName,
                              style: const TextStyle(
                                fontWeight: FontWeight.w500,
                                color: Colors.black87,
                              ),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                        ),
                        DataCell(
                          Container(
                            width: amountWidth,
                            height: 48, // 与出库管理页面保持一致的高度
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                            alignment: Alignment.center, // 垂直和水平都居中
                            child: Text(
                              '¥${order.totalAmount.toStringAsFixed(2)}',
                              style: const TextStyle(
                                fontWeight: FontWeight.w500,
                                color: Colors.green,
                              ),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                        ),
                        DataCell(
                          Container(
                            width: statusWidth,
                            height: 48, // 与出库管理页面保持一致的高度
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                            alignment: Alignment.center, // 垂直和水平都居中
                            child: _buildStatusBadge(order.status),
                          ),
                        ),
                        DataCell(
                          Container(
                            width: actionWidth,
                            height: 48, // 与出库管理页面保持一致的高度
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                            alignment: Alignment.center,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              mainAxisSize: MainAxisSize.min,
                              children: _buildActionButtons(
                                order,
                                availableWidth < 800,
                              ),
                            ),
                          ),
                        ),
                      ],
                    );
                  }).toList(),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// 构建分页控件 - 与出库管理页面保持一致的样式
  Widget _buildPagination(RecyclingController controller) {
    return Container(
      padding: const EdgeInsets.symmetric(
        vertical: 4,
        horizontal: 16,
      ), // 与出库管理页面保持一致的内边距
      color: Colors.white,
      child: Obx(
        () => Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            IconButton(
              icon: const Icon(Icons.first_page),
              tooltip: '第一页',
              onPressed: controller.currentPage.value > 1
                  ? () => controller.goToPage(1)
                  : null,
            ),
            IconButton(
              icon: const Icon(Icons.chevron_left),
              tooltip: '上一页',
              onPressed: controller.currentPage.value > 1
                  ? () => controller.goToPage(controller.currentPage.value - 1)
                  : null,
            ),
            Text(
              '${controller.currentPage.value} / ${controller.totalPages.value}',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            IconButton(
              icon: const Icon(Icons.chevron_right),
              tooltip: '下一页',
              onPressed:
                  controller.currentPage.value < controller.totalPages.value
                  ? () => controller.goToPage(controller.currentPage.value + 1)
                  : null,
            ),
            IconButton(
              icon: const Icon(Icons.last_page),
              tooltip: '最后一页',
              onPressed:
                  controller.currentPage.value < controller.totalPages.value
                  ? () => controller.goToPage(controller.totalPages.value)
                  : null,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建状态徽章 - 完全复用出库管理页面的样式
  Widget _buildStatusBadge(int status) {
    Color getStatusColor(int status) {
      switch (status) {
        case 0:
          return Colors.orange; // 待处理
        case 1:
          return Colors.blue; // 已处理
        case 2:
          return Colors.green; // 已完成
        case 3:
          return Colors.red; // 已取消
        default:
          return Colors.grey;
      }
    }

    String getStatusText(int status) {
      switch (status) {
        case 0:
          return '待处理';
        case 1:
          return '已处理';
        case 2:
          return '已完成';
        case 3:
          return '已取消';
        default:
          return '未知';
      }
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 5,
        vertical: 2,
      ), // 与出库管理页面保持一致的内边距
      decoration: BoxDecoration(
        color: getStatusColor(status).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(
          AppBorderStyles.borderRadius,
        ), // 使用统一的标准圆角
        border: Border.all(color: getStatusColor(status), width: 1),
      ),
      child: Text(
        getStatusText(status),
        style: TextStyle(
          color: getStatusColor(status),
          fontSize: 11, // 与出库管理页面保持一致的字体大小
          fontWeight: FontWeight.w500,
        ),
        overflow: TextOverflow.ellipsis, // 防止文字溢出
        maxLines: 1, // 确保单行显示
      ),
    );
  }

  /// 构建操作按钮 - 完全复用出库管理页面的样式和逻辑
  List<Widget> _buildActionButtons(
    RecyclingOrder order, [
    bool isSmallScreen = false,
  ]) {
    final List<Widget> buttons = [];
    final double iconSize = isSmallScreen ? 18 : 20;
    final double buttonSize = isSmallScreen ? 32 : 40;

    // 查看按钮
    buttons.add(
      SizedBox(
        width: buttonSize,
        height: buttonSize,
        child: IconButton(
          icon: Icon(Icons.visibility, color: Colors.blue, size: iconSize),
          tooltip: '查看',
          padding: EdgeInsets.zero,
          onPressed: () => _navigateToRecyclingDetail(order.id),
        ),
      ),
    );

    // 编辑按钮 (仅待处理状态可编辑)
    if (order.status == 0) {
      buttons.add(
        SizedBox(
          width: buttonSize,
          height: buttonSize,
          child: IconButton(
            icon: Icon(Icons.edit, color: Colors.orange, size: iconSize),
            tooltip: '编辑',
            padding: EdgeInsets.zero,
            onPressed: () {
              // TODO: 实现编辑功能
              Get.snackbar('功能开发中', '编辑功能正在开发中');
            },
          ),
        ),
      );
    }

    // 删除按钮 (仅待处理状态可删除)
    if (order.status == 0) {
      buttons.add(
        SizedBox(
          width: buttonSize,
          height: buttonSize,
          child: IconButton(
            icon: Icon(Icons.delete, color: Colors.red, size: iconSize),
            tooltip: '删除',
            padding: EdgeInsets.zero,
            onPressed: () {
              // TODO: 实现删除功能
              Get.snackbar('功能开发中', '删除功能正在开发中');
            },
          ),
        ),
      );
    }

    return buttons;
  }

  void _navigateToAddRecycling() {
    LoggerService.d('🔘 新建回收单按钮被点击');

    try {
      // 尝试获取回收单标签页控制器
      final recyclingTabController = Get.find<RecyclingTabController>();

      // 使用标签页方式打开新建回收单表单
      recyclingTabController.openRecyclingForm();

      LoggerService.d('✅ 成功使用标签页方式打开新建回收单');
    } catch (e) {
      LoggerService.e('❌ 无法找到RecyclingTabController，使用传统页面跳转方式: $e');

      // 如果没有找到标签页控制器，使用原来的路由方式作为备用
      try {
        LoggerService.d('🔄 使用Get.to跳转到RecyclingFormPage...');
        Get.to(() => const RecyclingFormPage());
        LoggerService.d('✅ 成功跳转到RecyclingFormPage');
      } catch (fallbackError) {
        LoggerService.e('❌ 跳转到RecyclingFormPage失败: $fallbackError');
        Get.snackbar(
          '错误',
          '打开新建回收单页面失败: $fallbackError',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    }
  }

  void _navigateToRecyclingDetail(int id) async {
    LoggerService.d('🔍 点击查看按钮，回收单ID: $id');

    try {
      // 显示加载对话框
      Get.dialog(
        const Center(
          child: Card(
            child: Padding(
              padding: EdgeInsets.all(20),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('正在加载回收单详情...'),
                ],
              ),
            ),
          ),
        ),
        barrierDismissible: false,
      );

      // 强制调用API获取详情数据，而不是使用列表缓存
      LoggerService.d('🚀 开始调用API获取回收单详情');
      final controller = Get.find<RecyclingController>();

      // 直接调用详情API
      await controller.loadRecyclingOrderDetail(id);

      // 关闭加载对话框
      if (Get.isDialogOpen == true) {
        Get.back();
      }

      // 获取详情数据
      final recyclingOrder = controller.currentOrder.value;

      if (recyclingOrder != null) {
        LoggerService.d('✅ 成功获取回收单详情: ${recyclingOrder.orderNo}');
        LoggerService.d('📦 明细数量: ${recyclingOrder.items.length}');

        // 显示详情对话框
        Get.dialog(
          RecyclingDetailDialog(recyclingOrder: recyclingOrder),
          barrierDismissible: true,
        );
      } else {
        LoggerService.e('❌ 回收单详情为空');
        Get.snackbar(
          '错误',
          '获取回收单详情失败',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      LoggerService.e('❌ 获取回收单详情异常', e);

      // 关闭加载对话框
      if (Get.isDialogOpen == true) {
        Get.back();
      }

      Get.snackbar(
        '错误',
        '获取回收单详情失败: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
}
