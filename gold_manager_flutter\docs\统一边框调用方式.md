# 黄金珠宝管理系统 - 统一边框样式使用指南

## 概述

为了保持整个应用的UI一致性并简化样式维护，我们创建了统一的边框样式常量。这些常量定义在 `lib/core/constants/border_styles.dart` 文件中，作为 `AppBorderStyles` 类的静态成员。

使用这些预定义的边框样式有以下好处：
- **一致性**：确保整个应用中的边框样式保持一致
- **可维护性**：需要更改样式时，只需修改一处定义
- **可读性**：使代码更简洁、易读
- **开发效率**：避免重复编写相同的样式代码

## 导入方式

在需要使用边框样式的文件中，添加以下导入语句：

```dart
import 'package:gold_manager_flutter/core/constants/border_styles.dart';
```

## 常用边框样式常量

### 基础常量

| 常量名称 | 类型 | 描述 |
|---------|------|-----|
| `AppBorderStyles.borderColor` | `Color` | 标准边框颜色（浅灰色） |
| `AppBorderStyles.focusBorderColor` | `Color` | 焦点边框颜色（蓝色） |
| `AppBorderStyles.borderWidth` | `double` | 标准边框宽度（1.0） |
| `AppBorderStyles.focusBorderWidth` | `double` | 焦点边框宽度（2.0） |
| `AppBorderStyles.borderRadius` | `double` | 标准圆角半径（6.0） |
| `AppBorderStyles.mediumBorderRadius` | `double` | 中等圆角半径（8.0） |
| `AppBorderStyles.largeBorderRadius` | `double` | 大圆角半径（12.0） |

### 边框对象

| 常量名称 | 类型 | 描述 |
|---------|------|-----|
| `AppBorderStyles.standardBorder` | `BorderSide` | 标准边框样式 |
| `AppBorderStyles.focusBorder` | `BorderSide` | 焦点边框样式 |
| `AppBorderStyles.tableBorder` | `BorderSide` | 表格边框样式 |
| `AppBorderStyles.tableBorderDark` | `BorderSide` | 表格暗色边框样式 |

### 装饰对象

| 常量名称 | 类型 | 描述 |
|---------|------|-----|
| `AppBorderStyles.standardBoxDecoration` | `BoxDecoration` | 标准边框盒子装饰 |
| `AppBorderStyles.elevatedBoxDecoration` | `BoxDecoration` | 带阴影的边框盒子装饰 |
| `AppBorderStyles.tableStandardBorder` | `TableBorder` | 表格标准边框 |

### 输入框装饰

| 常量名称 | 类型 | 描述 |
|---------|------|-----|
| `AppBorderStyles.standardInputDecoration` | `InputDecoration` | 标准输入框装饰 |
| `AppBorderStyles.compactInputDecoration` | `InputDecoration` | 紧凑型输入框装饰 |

### 表格样式

| 常量名称 | 类型 | 描述 |
|---------|------|-----|
| `AppBorderStyles.tableHeaderBackground` | `Color` | 表格表头背景色 |
| `AppBorderStyles.tableOddRowBackground` | `Color` | 表格奇数行背景色 |
| `AppBorderStyles.tableEvenRowBackground` | `Color` | 表格偶数行背景色 |
| `AppBorderStyles.cellDecoration()` | `BoxDecoration` | 表格单元格装饰 |
| `AppBorderStyles.headerCellDecoration()` | `BoxDecoration` | 表格表头单元格装饰 |

## 使用示例

### 1. 使用边框基础常量

```dart
Container(
  decoration: BoxDecoration(
    border: Border.all(
      color: AppBorderStyles.borderColor,
      width: AppBorderStyles.borderWidth,
    ),
    borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
  ),
  child: Text('使用基础边框常量'),
)
```

### 2. 使用预定义边框对象

```dart
Container(
  decoration: BoxDecoration(
    border: Border(
      left: AppBorderStyles.standardBorder,
      top: AppBorderStyles.standardBorder,
      right: AppBorderStyles.standardBorder,
      bottom: AppBorderStyles.standardBorder,
    ),
    borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
  ),
  child: Text('使用预定义边框对象'),
)
```

### 3. 使用预定义装饰对象

```dart
Container(
  decoration: AppBorderStyles.standardBoxDecoration,
  child: Text('使用预定义装饰对象'),
)

// 带阴影的容器
Container(
  decoration: AppBorderStyles.elevatedBoxDecoration,
  child: Text('使用带阴影的装饰对象'),
)
```

### 4. 在表格中使用边框样式

```dart
DataTable(
  border: AppBorderStyles.tableStandardBorder,
  headingRowColor: MaterialStateProperty.all(AppBorderStyles.tableHeaderBackground),
  columns: [
    DataColumn(label: Text('标题1')),
    DataColumn(label: Text('标题2')),
  ],
  rows: [
    DataRow(
      color: MaterialStateProperty.all(AppBorderStyles.tableOddRowBackground),
      cells: [DataCell(Text('内容1')), DataCell(Text('内容2'))],
    ),
    DataRow(
      color: MaterialStateProperty.all(AppBorderStyles.tableEvenRowBackground),
      cells: [DataCell(Text('内容3')), DataCell(Text('内容4'))],
    ),
  ],
)
```

### 5. 在表单字段中使用边框样式

```dart
TextField(
  decoration: AppBorderStyles.standardInputDecoration.copyWith(
    labelText: '用户名',
    hintText: '请输入用户名',
  ),
)

// 紧凑型输入框
TextField(
  decoration: AppBorderStyles.compactInputDecoration.copyWith(
    labelText: '备注',
    hintText: '请输入备注信息',
  ),
)
```

### 6. 使用单元格装饰

```dart
// 表格单元格（奇数行）
Container(
  decoration: AppBorderStyles.cellDecoration(isEven: false),
  padding: EdgeInsets.all(8),
  child: Text('单元格内容'),
)

// 表格单元格（偶数行）
Container(
  decoration: AppBorderStyles.cellDecoration(isEven: true),
  padding: EdgeInsets.all(8),
  child: Text('单元格内容'),
)

// 表格表头单元格
Container(
  decoration: AppBorderStyles.headerCellDecoration(),
  padding: EdgeInsets.all(8),
  child: Text('表头内容'),
)
```

## 实际应用案例

以下是在系统中已经应用统一边框样式的实际案例：

### 1. 入库单表格边框样式

在入库单表格中，我们使用统一的边框样式确保表格在新建和编辑模式下的一致性：

```dart
// 表格头部单元格
Widget _buildHeaderCell(String text, double width) {
  return Container(
    width: width,
    height: 48,
    padding: const EdgeInsets.symmetric(horizontal: 4),
    decoration: AppBorderStyles.headerCellDecoration(),
    alignment: Alignment.center,
    child: Text(
      text,
      style: const TextStyle(
        fontWeight: FontWeight.w600,
        fontSize: 14,
        color: Colors.black87,
      ),
      textAlign: TextAlign.center,
      overflow: TextOverflow.ellipsis,
      maxLines: 1,
    ),
  );
}

// 表格数据单元格
Widget _buildDataCell(String text, double width) {
  return Container(
    width: width,
    height: 52,
    padding: const EdgeInsets.symmetric(horizontal: 4),
    decoration: BoxDecoration(
      border: Border(
        right: AppBorderStyles.tableBorder,
      ),
    ),
    alignment: Alignment.center,
    child: Text(text, textAlign: TextAlign.center),
  );
}
```

### 2. 下拉框和输入框的边框样式

我们在表单字段中使用统一的边框样式：

```dart
// 门店选择器
Container(
  height: 32,
  decoration: AppBorderStyles.standardBoxDecoration,
  child: DropdownButtonHideUnderline(
    child: DropdownButton<int>(
      // ... 配置选项
    ),
  ),
)

// 备注输入框 - 正确的32px高度输入框实现
Container(
  height: 32,
  decoration: AppBorderStyles.standardBoxDecoration,
  child: Padding(
    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
    child: TextField(
      decoration: const InputDecoration(
        hintText: '可选',
        border: InputBorder.none,
        enabledBorder: InputBorder.none,
        focusedBorder: InputBorder.none,
        disabledBorder: InputBorder.none,
        contentPadding: EdgeInsets.symmetric(vertical: 8),
        isDense: true,
      ),
    ),
  ),
)
```

### 3. 容器边框样式

对于带有阴影的容器，我们使用统一的装饰样式：

```dart
Container(
  margin: const EdgeInsets.all(16),
  decoration: AppBorderStyles.elevatedBoxDecoration,
  child: Column(
    children: [
      // ... 容器内容
    ],
  ),
)
```

## 注意事项

1. **一致性优先**：始终优先使用预定义的边框样式，避免创建自定义边框样式
2. **扩展而非替换**：需要微调样式时，使用 `copyWith()` 方法扩展预定义样式，而不是创建全新的样式
3. **新增样式**：如果需要新的边框样式，应在 `AppBorderStyles` 类中添加，而不是在各个组件中定义
4. **保持简洁**：使用预定义样式可以让代码更简洁，减少重复代码
5. **谨慎覆盖**：如果必须覆盖预定义样式，请确保有充分的理由并添加注释说明

## 维护与更新

如需修改或新增边框样式，请按照以下步骤操作：

1. 在 `lib/core/constants/border_styles.dart` 文件中进行修改或添加
2. 添加详细的注释，说明样式的用途和适用场景
3. 更新本文档，确保文档与代码保持同步
4. 通知团队成员关于样式变更的信息

### 优化建议

随着项目的发展，以下是一些边框样式优化的建议：

1. **主题整合**：将边框样式整合到应用的主题系统中，支持暗色模式等不同主题
2. **响应式样式**：根据屏幕尺寸自动调整边框样式（如在小屏幕上使用更紧凑的样式）
3. **组件级别样式**：为特定组件（如按钮、卡片等）定义专用的边框样式
4. **动画过渡**：为边框状态变化（如普通状态到焦点状态）添加平滑的动画过渡
5. **无障碍优化**：确保边框样式符合无障碍设计标准，如提供足够的对比度

### 4. 入库管理页面边框样式最佳实践

"入库管理"页面是边框样式规范应用的典型案例，展示了如何在复杂的管理界面中保持样式一致性：

#### 筛选区域边框样式
```dart
// 筛选区域容器 - 使用底部边框分隔
Container(
  padding: const EdgeInsets.all(16.0),
  decoration: BoxDecoration(
    color: Colors.white,
    border: Border(
      bottom: AppBorderStyles.tableBorder, // 使用统一的表格边框
    ),
  ),
  child: Row(
    crossAxisAlignment: CrossAxisAlignment.center,
    children: [
      // ... 筛选控件
    ],
  ),
)

// 操作员信息标签 - 使用大圆角和统一边框
Container(
  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
  decoration: BoxDecoration(
    color: Colors.blue[50],
    borderRadius: BorderRadius.circular(AppBorderStyles.largeBorderRadius), // 大圆角
    border: Border.all(color: Colors.blue[200]!, width: AppBorderStyles.borderWidth), // 统一边框宽度
  ),
  child: Row(
    children: [
      Icon(Icons.person, size: 14, color: Colors.blue[600]),
      Text('操作员: $operatorName'),
    ],
  ),
)
```

#### 表单控件边框样式
```dart
// 搜索输入框 - 使用标准盒子装饰（正确实现）
Container(
  height: 32,
  decoration: AppBorderStyles.standardBoxDecoration, // 统一的标准装饰
  child: Padding(
    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
    child: TextField(
      decoration: const InputDecoration(
        hintText: '入库单号、备注等',
        border: InputBorder.none, // 移除内部边框，使用容器边框
        enabledBorder: InputBorder.none,
        focusedBorder: InputBorder.none,
        disabledBorder: InputBorder.none,
        contentPadding: EdgeInsets.symmetric(vertical: 8),
        isDense: true,
      ),
    ),
  ),
)

// 下拉选择器 - 使用标准盒子装饰
Container(
  height: 32,
  decoration: AppBorderStyles.standardBoxDecoration, // 统一的标准装饰
  child: DropdownButtonHideUnderline(
    child: DropdownButton<int>(
      // ... 下拉选项配置
      padding: const EdgeInsets.symmetric(horizontal: 8),
    ),
  ),
)

// 日期范围选择器 - 使用统一圆角
Container(
  height: 32,
  decoration: AppBorderStyles.standardBoxDecoration,
  child: Row(
    children: [
      Expanded(
        child: InkWell(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(AppBorderStyles.borderRadius),
            bottomLeft: Radius.circular(AppBorderStyles.borderRadius),
          ),
          // ... 日期选择逻辑
        ),
      ),
    ],
  ),
)
```

#### 按钮边框样式
```dart
// 重置按钮 - 使用统一的边框和圆角
SizedBox(
  height: 32,
  child: OutlinedButton.icon(
    icon: const Icon(Icons.refresh, size: 14),
    label: const Text('重置'),
    style: OutlinedButton.styleFrom(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius), // 统一圆角
      ),
      side: BorderSide(color: AppBorderStyles.borderColor), // 统一边框颜色
    ),
  ),
)

// 搜索和新建按钮 - 使用统一圆角
ElevatedButton.icon(
  style: ElevatedButton.styleFrom(
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius), // 统一圆角
    ),
  ),
  // ... 按钮配置
)
```

#### 数据表格边框样式
```dart
// 数据表格容器 - 使用带阴影的装饰
Container(
  margin: const EdgeInsets.all(4),
  decoration: AppBorderStyles.elevatedBoxDecoration.copyWith(
    color: Colors.white,
  ),
  child: ClipRRect(
    borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius), // 统一圆角
    child: DataTable(
      border: AppBorderStyles.tableStandardBorder, // 统一表格边框
      headingRowColor: WidgetStateProperty.all(AppBorderStyles.tableHeaderBackground), // 统一表头背景
      // ... 表格配置
    ),
  ),
)

// 状态徽章 - 使用统一圆角
Container(
  padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 2),
  decoration: BoxDecoration(
    color: statusColor.withValues(alpha: 0.1),
    borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius), // 统一圆角
    border: Border.all(color: statusColor, width: 1),
  ),
  child: Text(statusText),
)
```

#### 对话框边框样式
```dart
// 审核对话框信息提示 - 使用中等圆角
Container(
  padding: const EdgeInsets.all(12),
  decoration: BoxDecoration(
    color: Colors.blue[50],
    borderRadius: BorderRadius.circular(AppBorderStyles.mediumBorderRadius), // 中等圆角
    border: Border.all(color: Colors.blue[200]!, width: AppBorderStyles.borderWidth), // 统一边框宽度
  ),
  child: Row(
    children: [
      Icon(Icons.info_outline, color: Colors.blue[600]),
      Text('请仔细审核入库单信息'),
    ],
  ),
)

// 输入框 - 使用标准输入装饰
TextField(
  decoration: AppBorderStyles.standardInputDecoration.copyWith(
    hintText: '请输入审核意见（拒绝时必填）',
    contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
  ),
)
```

#### 移动端卡片边框样式
```dart
// 移动端列表卡片 - 使用大圆角
Card(
  shape: RoundedRectangleBorder(
    borderRadius: BorderRadius.circular(AppBorderStyles.largeBorderRadius), // 大圆角
  ),
  elevation: 2,
  child: InkWell(
    borderRadius: BorderRadius.circular(AppBorderStyles.largeBorderRadius), // 保持一致的圆角
    onTap: () => onTap(),
    child: Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        // ... 卡片内容
      ),
    ),
  ),
)
```

## 总结

使用统一的边框样式常量已经显著提高了黄金珠宝管理系统UI的一致性和代码的可维护性。特别是在入库单表格界面和入库管理页面，新建、编辑和管理模式下的样式现在保持了完全一致，提升了用户体验。

"入库管理"页面的实现展示了如何在复杂的管理界面中正确应用边框样式规范：
- **筛选区域**：使用底部边框分隔和标准装饰
- **表单控件**：统一使用标准盒子装饰和输入装饰
- **按钮组**：统一圆角和边框颜色
- **数据表格**：使用带阴影装饰和标准表格边框
- **对话框**：根据内容重要性使用不同级别的圆角
- **移动端适配**：使用大圆角提升触摸体验

请所有开发人员在开发过程中遵循本文档的指南，确保应用的边框样式保持一致。如果你发现任何未使用统一边框样式的组件，请优先进行修改，使其符合统一标准。

## 🚨 重要提醒：避免常见的边框样式错误

### **错误示例1：不完整的边框移除**
```dart
// ❌ 错误：只移除了border，会导致焦点时出现边框
TextField(
  decoration: const InputDecoration(
    border: InputBorder.none, // 只移除了默认边框
  ),
)

// ✅ 正确：完整移除所有状态的边框
TextField(
  decoration: const InputDecoration(
    border: InputBorder.none,
    enabledBorder: InputBorder.none,
    focusedBorder: InputBorder.none,
    disabledBorder: InputBorder.none,
  ),
)
```

### **错误示例2：文字对齐配置错误**
```dart
// ❌ 错误：会导致文字在32px容器中偏移
Container(
  height: 32,
  child: Padding(
    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4), // 错误
    child: TextField(
      decoration: const InputDecoration(
        contentPadding: EdgeInsets.zero, // 错误
      ),
    ),
  ),
)

// ✅ 正确：文字在32px容器中完美居中
Container(
  height: 32,
  child: Padding(
    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0), // 正确
    child: TextField(
      decoration: const InputDecoration(
        contentPadding: EdgeInsets.symmetric(vertical: 8), // 正确
        isDense: true, // 重要：启用紧凑模式
      ),
    ),
  ),
)
```

### **标准32px高度输入框模板**
```dart
/// 标准32px高度输入框实现模板
/// 适用于：备注输入框、搜索框、数字输入框等
Container(
  height: 32,
  decoration: AppBorderStyles.standardBoxDecoration,
  child: Padding(
    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
    child: TextField(
      decoration: const InputDecoration(
        hintText: '请输入内容',
        // 完整移除所有边框
        border: InputBorder.none,
        enabledBorder: InputBorder.none,
        focusedBorder: InputBorder.none,
        disabledBorder: InputBorder.none,
        // 正确的文字对齐配置
        contentPadding: EdgeInsets.symmetric(vertical: 8),
        isDense: true,
      ),
      style: const TextStyle(fontSize: 13),
    ),
  ),
)
```

### **验证清单**
在实现输入框时，请确保：
- ✅ 移除了所有状态的内部边框（border、enabledBorder、focusedBorder、disabledBorder）
- ✅ Container的vertical padding设为0
- ✅ TextField的contentPadding使用vertical: 8
- ✅ 添加了isDense: true属性
- ✅ 文字在32px高度容器中垂直居中显示
- ✅ 只显示外部Container的边框，无双重边框问题

## 📝 更新记录

### 2024-12-20 盘点操作界面优化
- ✅ **音频反馈优化**：修复扫码成功提示音，增强音效播放逻辑
- ✅ **输入框尺寸调整**：减小扫码输入框宽度（280px），界面更加紧凑
- ✅ **分页功能完善**：底部统计栏添加分页控件，支持大量数据浏览
- ✅ **用户体验提升**：音频反馈、紧凑布局、便捷分页操作
- ✅ **设计规范遵循**：严格按照统一边框调用方式标准实现

### 2024-12-20 库存盘点功能完善
- ✅ **盘点操作界面**：完善的桌面端表格和移动端卡片布局
- ✅ **条码扫描功能**：支持扫码和手动输入条码
- ✅ **批量操作功能**：支持多选商品批量盘点
- ✅ **数据持久化**：实时保存盘点结果到数据库
- ✅ **权限控制**：完善的权限验证和门店数据隔离
- ✅ **UI设计规范**：严格遵循统一边框调用方式标准
- ✅ **响应式设计**：适配桌面端和移动端设备