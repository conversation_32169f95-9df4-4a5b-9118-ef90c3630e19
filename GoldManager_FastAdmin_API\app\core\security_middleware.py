"""
安全中间件
添加各种安全头和保护措施
"""

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from typing import Callable
import time

class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """安全头中间件"""

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 处理请求
        start_time = time.time()
        response = await call_next(request)
        process_time = time.time() - start_time

        # 添加安全头
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        response.headers["Permissions-Policy"] = "geolocation=(), microphone=(), camera=()"

        # HTTPS相关头（仅在HTTPS时添加）
        if request.url.scheme == "https":
            response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"

        # 添加处理时间头（开发环境）
        response.headers["X-Process-Time"] = str(process_time)

        # 隐藏服务器信息
        if "server" in response.headers:
            del response.headers["server"]

        return response

class RateLimitMiddleware(BaseHTTPMiddleware):
    """简单的速率限制中间件"""

    def __init__(self, app, calls: int = 100, period: int = 60):
        super().__init__(app)
        self.calls = calls
        self.period = period
        self.clients = {}

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 获取客户端IP
        client_ip = request.client.host
        current_time = time.time()

        # 清理过期记录
        self.clients = {
            ip: times for ip, times in self.clients.items()
            if any(t > current_time - self.period for t in times)
        }

        # 检查当前IP的请求次数
        if client_ip not in self.clients:
            self.clients[client_ip] = []

        # 过滤最近时间段内的请求
        recent_requests = [
            t for t in self.clients[client_ip]
            if t > current_time - self.period
        ]

        if len(recent_requests) >= self.calls:
            # 超过限制
            from fastapi.responses import JSONResponse
            return JSONResponse(
                status_code=429,
                content={
                    "code": 429,
                    "message": "请求过于频繁，请稍后再试",
                    "data": None
                }
            )

        # 记录当前请求
        self.clients[client_ip] = recent_requests + [current_time]

        return await call_next(request)
