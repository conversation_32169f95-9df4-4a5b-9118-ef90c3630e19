
/// 入库单草稿数据模型
class StockInDraft {
  /// 草稿ID（基于时间戳生成）
  final String id;
  
  /// 门店ID
  final int storeId;
  
  /// 备注
  final String? remark;
  
  /// 商品项列表
  final List<StockInDraftItem> items;
  
  /// 创建时间
  final DateTime createTime;
  
  /// 最后更新时间
  final DateTime updateTime;

  const StockInDraft({
    required this.id,
    required this.storeId,
    this.remark,
    required this.items,
    required this.createTime,
    required this.updateTime,
  });

  /// 从JSON创建
  factory StockInDraft.fromJson(Map<String, dynamic> json) {
    return StockInDraft(
      id: json['id'] as String,
      storeId: json['storeId'] as int,
      remark: json['remark'] as String?,
      items: (json['items'] as List<dynamic>)
          .map((item) => StockInDraftItem.fromJson(item as Map<String, dynamic>))
          .toList(),
      createTime: DateTime.parse(json['createTime'] as String),
      updateTime: DateTime.parse(json['updateTime'] as String),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'storeId': storeId,
      'remark': remark,
      'items': items.map((item) => item.toJson()).toList(),
      'createTime': createTime.toIso8601String(),
      'updateTime': updateTime.toIso8601String(),
    };
  }

  /// 复制并更新
  StockInDraft copyWith({
    String? id,
    int? storeId,
    String? remark,
    List<StockInDraftItem>? items,
    DateTime? createTime,
    DateTime? updateTime,
  }) {
    return StockInDraft(
      id: id ?? this.id,
      storeId: storeId ?? this.storeId,
      remark: remark ?? this.remark,
      items: items ?? this.items,
      createTime: createTime ?? this.createTime,
      updateTime: updateTime ?? this.updateTime,
    );
  }

  /// 是否过期（7天）
  bool get isExpired {
    final now = DateTime.now();
    return now.difference(updateTime).inDays >= 7;
  }

  /// 计算总金额
  double get totalAmount {
    return items.fold(0.0, (sum, item) => sum + item.amount);
  }

  /// 计算商品件数
  int get itemCount {
    return items.length;
  }
}

/// 入库单草稿商品项
class StockInDraftItem {
  /// 商品ID（如果是新录入的商品则为0）
  final int jewelryId;
  
  /// 条码
  final String? barcode;
  
  /// 商品名称
  final String name;
  
  /// 类别
  final String category;
  
  /// 金重(g)
  final double goldWeight;
  
  /// 银重(g)
  final double silverWeight;
  
  /// 工费
  final double laborCost;
  
  /// 其他费用
  final double otherCost;
  
  /// 总金额
  final double amount;

  const StockInDraftItem({
    required this.jewelryId,
    this.barcode,
    required this.name,
    required this.category,
    required this.goldWeight,
    required this.silverWeight,
    required this.laborCost,
    required this.otherCost,
    required this.amount,
  });

  /// 从JSON创建
  factory StockInDraftItem.fromJson(Map<String, dynamic> json) {
    return StockInDraftItem(
      jewelryId: json['jewelryId'] as int,
      barcode: json['barcode'] as String?,
      name: json['name'] as String,
      category: json['category'] as String,
      goldWeight: (json['goldWeight'] as num).toDouble(),
      silverWeight: (json['silverWeight'] as num).toDouble(),
      laborCost: (json['laborCost'] as num).toDouble(),
      otherCost: (json['otherCost'] as num).toDouble(),
      amount: (json['amount'] as num).toDouble(),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'jewelryId': jewelryId,
      'barcode': barcode,
      'name': name,
      'category': category,
      'goldWeight': goldWeight,
      'silverWeight': silverWeight,
      'laborCost': laborCost,
      'otherCost': otherCost,
      'amount': amount,
    };
  }

  /// 复制并更新
  StockInDraftItem copyWith({
    int? jewelryId,
    String? barcode,
    String? name,
    String? category,
    double? goldWeight,
    double? silverWeight,
    double? laborCost,
    double? otherCost,
    double? amount,
  }) {
    return StockInDraftItem(
      jewelryId: jewelryId ?? this.jewelryId,
      barcode: barcode ?? this.barcode,
      name: name ?? this.name,
      category: category ?? this.category,
      goldWeight: goldWeight ?? this.goldWeight,
      silverWeight: silverWeight ?? this.silverWeight,
      laborCost: laborCost ?? this.laborCost,
      otherCost: otherCost ?? this.otherCost,
      amount: amount ?? this.amount,
    );
  }
}
