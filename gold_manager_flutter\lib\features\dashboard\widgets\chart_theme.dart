import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import '../../../core/theme/app_theme.dart';

/// 仪表盘图表主题配置
/// 统一管理所有图表的样式和主题
class DashboardChartTheme {
  DashboardChartTheme._();

  /// 主色调
  static const Color primaryColor = AppTheme.primaryColor;
  static const Color secondaryColor = AppTheme.secondaryColor;
  static const Color successColor = AppTheme.successColor;
  static const Color warningColor = AppTheme.warningColor;
  static const Color errorColor = AppTheme.errorColor;

  /// 图表背景色
  static const Color chartBackgroundColor = Colors.transparent;
  
  /// 网格线颜色
  static const Color gridLineColor = Color(0xFFE0E0E0);
  
  /// 文本颜色
  static const Color textColor = Color(0xFF666666);
  static const Color titleColor = Color(0xFF333333);

  /// 图表颜色调色板
  static const List<Color> chartColors = [
    Color(0xFF1E88E5), // 蓝色
    Color(0xFF388E3C), // 绿色
    Color(0xFFFFA000), // 橙色
    Color(0xFF7B1FA2), // 紫色
    Color(0xFFD32F2F), // 红色
    Color(0xFF00ACC1), // 青色
    Color(0xFF5E35B1), // 深紫色
    Color(0xFF43A047), // 深绿色
  ];

  /// 获取图表颜色
  static Color getChartColor(int index) {
    return chartColors[index % chartColors.length];
  }

  /// 折线图默认样式
  static LineChartData getDefaultLineChartData({
    required List<FlSpot> spots,
    String? title,
    Color? lineColor,
    bool showDots = true,
    bool showGrid = true,
  }) {
    return LineChartData(
      gridData: FlGridData(
        show: showGrid,
        drawVerticalLine: true,
        drawHorizontalLine: true,
        horizontalInterval: 1,
        verticalInterval: 1,
        getDrawingHorizontalLine: (value) {
          return const FlLine(
            color: gridLineColor,
            strokeWidth: 0.5,
          );
        },
        getDrawingVerticalLine: (value) {
          return const FlLine(
            color: gridLineColor,
            strokeWidth: 0.5,
          );
        },
      ),
      titlesData: FlTitlesData(
        show: true,
        rightTitles: const AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        topTitles: const AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        bottomTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 30,
            interval: 1,
            getTitlesWidget: (value, meta) {
              return Text(
                value.toInt().toString(),
                style: const TextStyle(
                  color: textColor,
                  fontWeight: FontWeight.w400,
                  fontSize: 12,
                ),
              );
            },
          ),
        ),
        leftTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            interval: 1,
            reservedSize: 42,
            getTitlesWidget: (value, meta) {
              return Text(
                value.toInt().toString(),
                style: const TextStyle(
                  color: textColor,
                  fontWeight: FontWeight.w400,
                  fontSize: 12,
                ),
              );
            },
          ),
        ),
      ),
      borderData: FlBorderData(
        show: true,
        border: Border.all(color: gridLineColor, width: 1),
      ),
      minX: 0,
      maxX: spots.isNotEmpty ? spots.last.x : 10,
      minY: 0,
      maxY: spots.isNotEmpty ? spots.map((e) => e.y).reduce((a, b) => a > b ? a : b) * 1.2 : 10,
      lineBarsData: [
        LineChartBarData(
          spots: spots,
          isCurved: true,
          color: lineColor ?? primaryColor,
          barWidth: 3,
          isStrokeCapRound: true,
          dotData: FlDotData(
            show: showDots,
            getDotPainter: (spot, percent, barData, index) {
              return FlDotCirclePainter(
                radius: 4,
                color: lineColor ?? primaryColor,
                strokeWidth: 2,
                strokeColor: Colors.white,
              );
            },
          ),
          belowBarData: BarAreaData(
            show: true,
            color: (lineColor ?? primaryColor).withOpacity(0.1),
          ),
        ),
      ],
    );
  }

  /// 柱状图默认样式
  static BarChartData getDefaultBarChartData({
    required List<BarChartGroupData> barGroups,
    String? title,
    double? maxY,
    bool showGrid = true,
  }) {
    return BarChartData(
      alignment: BarChartAlignment.spaceAround,
      maxY: maxY,
      barTouchData: BarTouchData(
        enabled: true,
        touchTooltipData: BarTouchTooltipData(
          getTooltipColor: (group) => Colors.black87,
          tooltipRoundedRadius: 4,
          getTooltipItem: (group, groupIndex, rod, rodIndex) {
            return BarTooltipItem(
              '${rod.toY.toStringAsFixed(1)}',
              const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            );
          },
        ),
      ),
      titlesData: FlTitlesData(
        show: true,
        rightTitles: const AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        topTitles: const AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        bottomTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            getTitlesWidget: (value, meta) {
              return Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Text(
                  value.toInt().toString(),
                  style: const TextStyle(
                    color: textColor,
                    fontWeight: FontWeight.w400,
                    fontSize: 12,
                  ),
                ),
              );
            },
            reservedSize: 38,
          ),
        ),
        leftTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 42,
            interval: 1,
            getTitlesWidget: (value, meta) {
              return Text(
                value.toInt().toString(),
                style: const TextStyle(
                  color: textColor,
                  fontWeight: FontWeight.w400,
                  fontSize: 12,
                ),
              );
            },
          ),
        ),
      ),
      borderData: FlBorderData(
        show: true,
        border: Border.all(color: gridLineColor, width: 1),
      ),
      barGroups: barGroups,
      gridData: FlGridData(
        show: showGrid,
        drawVerticalLine: false,
        drawHorizontalLine: true,
        horizontalInterval: 1,
        getDrawingHorizontalLine: (value) {
          return const FlLine(
            color: gridLineColor,
            strokeWidth: 0.5,
          );
        },
      ),
    );
  }

  /// 饼图默认样式
  static PieChartData getDefaultPieChartData({
    required List<PieChartSectionData> sections,
    double? centerSpaceRadius,
    bool showPercentage = true,
  }) {
    return PieChartData(
      pieTouchData: PieTouchData(
        touchCallback: (FlTouchEvent event, pieTouchResponse) {
          // 可以在这里添加触摸交互逻辑
        },
      ),
      borderData: FlBorderData(show: false),
      sectionsSpace: 2,
      centerSpaceRadius: centerSpaceRadius ?? 40,
      sections: sections,
    );
  }

  /// 创建饼图扇形数据
  static PieChartSectionData createPieSection({
    required double value,
    required String title,
    required Color color,
    double radius = 60,
    bool showTitle = true,
    bool showPercentage = true,
  }) {
    return PieChartSectionData(
      color: color,
      value: value,
      title: showTitle ? (showPercentage ? '${value.toStringAsFixed(1)}%' : title) : '',
      radius: radius,
      titleStyle: const TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.bold,
        color: Colors.white,
      ),
      titlePositionPercentageOffset: 0.6,
    );
  }

  /// 创建柱状图数据组
  static BarChartGroupData createBarGroup({
    required int x,
    required double y,
    Color? color,
    double width = 20,
  }) {
    return BarChartGroupData(
      x: x,
      barRods: [
        BarChartRodData(
          toY: y,
          color: color ?? getChartColor(x),
          width: width,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(4),
            topRight: Radius.circular(4),
          ),
        ),
      ],
    );
  }

  /// 文本样式
  static const TextStyle chartTitleStyle = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w600,
    color: titleColor,
  );

  static const TextStyle chartSubtitleStyle = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w400,
    color: textColor,
  );

  static const TextStyle chartLabelStyle = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w400,
    color: textColor,
  );
}
