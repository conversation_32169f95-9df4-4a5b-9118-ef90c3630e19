/// 仪表盘数据模型
/// 定义所有仪表盘相关的数据结构

/// 仪表盘概览数据
class DashboardOverviewData {
  final int totalProducts;
  final int totalStores;
  final int totalMembers;
  final double todaySales;
  final double monthSales;
  final double inventoryValue;
  final int pendingOrders;

  const DashboardOverviewData({
    required this.totalProducts,
    required this.totalStores,
    required this.totalMembers,
    required this.todaySales,
    required this.monthSales,
    required this.inventoryValue,
    required this.pendingOrders,
  });

  factory DashboardOverviewData.fromJson(Map<String, dynamic> json) {
    return DashboardOverviewData(
      totalProducts: json['total_products'] ?? 0,
      totalStores: json['total_stores'] ?? 0,
      totalMembers: json['total_members'] ?? 0,
      todaySales: (json['today_sales'] ?? 0).toDouble(),
      monthSales: (json['month_sales'] ?? 0).toDouble(),
      inventoryValue: (json['inventory_value'] ?? 0).toDouble(),
      pendingOrders: json['pending_orders'] ?? 0,
    );
  }

  static const DashboardOverviewData empty = DashboardOverviewData(
    totalProducts: 0,
    totalStores: 0,
    totalMembers: 0,
    todaySales: 0,
    monthSales: 0,
    inventoryValue: 0,
    pendingOrders: 0,
  );
}

/// 销售统计数据
class SalesStatisticsData {
  final double totalAmount;
  final int totalOrders;
  final double avgOrderValue;
  final double totalProfit;
  final double profitMargin;
  final List<double> trendData;

  const SalesStatisticsData({
    required this.totalAmount,
    required this.totalOrders,
    required this.avgOrderValue,
    required this.totalProfit,
    required this.profitMargin,
    required this.trendData,
  });

  factory SalesStatisticsData.fromJson(Map<String, dynamic> json) {
    return SalesStatisticsData(
      totalAmount: (json['total_amount'] ?? 0).toDouble(),
      totalOrders: json['total_orders'] ?? 0,
      avgOrderValue: (json['avg_order_value'] ?? 0).toDouble(),
      totalProfit: (json['total_profit'] ?? 0).toDouble(),
      profitMargin: (json['profit_margin'] ?? 0).toDouble(),
      trendData: (json['trend_data'] as List?)?.map((e) => e.toDouble()).toList() ?? [],
    );
  }

  static const SalesStatisticsData empty = SalesStatisticsData(
    totalAmount: 0,
    totalOrders: 0,
    avgOrderValue: 0,
    totalProfit: 0,
    profitMargin: 0,
    trendData: [],
  );
}

/// 库存统计数据
class InventoryStatisticsData {
  final double totalValue;
  final int totalItems;
  final int lowStockCount;
  final double turnoverRate;
  final List<double> categoryDistribution;

  const InventoryStatisticsData({
    required this.totalValue,
    required this.totalItems,
    required this.lowStockCount,
    required this.turnoverRate,
    required this.categoryDistribution,
  });

  factory InventoryStatisticsData.fromJson(Map<String, dynamic> json) {
    return InventoryStatisticsData(
      totalValue: (json['total_value'] ?? 0).toDouble(),
      totalItems: json['total_items'] ?? 0,
      lowStockCount: json['low_stock_count'] ?? 0,
      turnoverRate: (json['turnover_rate'] ?? 0).toDouble(),
      categoryDistribution: (json['category_distribution'] as List?)?.map((e) => e.toDouble()).toList() ?? [],
    );
  }

  static const InventoryStatisticsData empty = InventoryStatisticsData(
    totalValue: 0,
    totalItems: 0,
    lowStockCount: 0,
    turnoverRate: 0,
    categoryDistribution: [],
  );
}

/// 会员统计数据
class MemberStatisticsData {
  final int totalMembers;
  final int newMembers;
  final int activeMembers;
  final List<int> memberGrowth;

  const MemberStatisticsData({
    required this.totalMembers,
    required this.newMembers,
    required this.activeMembers,
    required this.memberGrowth,
  });

  factory MemberStatisticsData.fromJson(Map<String, dynamic> json) {
    return MemberStatisticsData(
      totalMembers: json['total_members'] ?? 0,
      newMembers: json['new_members'] ?? 0,
      activeMembers: json['active_members'] ?? 0,
      memberGrowth: (json['member_growth'] as List?)?.map((e) => e as int).toList() ?? [],
    );
  }

  static const MemberStatisticsData empty = MemberStatisticsData(
    totalMembers: 0,
    newMembers: 0,
    activeMembers: 0,
    memberGrowth: [],
  );
}

/// 财务统计数据
class FinancialStatisticsData {
  final double totalRevenue;
  final double totalCost;
  final double netProfit;
  final double profitMargin;
  final List<double> profitTrend;

  const FinancialStatisticsData({
    required this.totalRevenue,
    required this.totalCost,
    required this.netProfit,
    required this.profitMargin,
    required this.profitTrend,
  });

  factory FinancialStatisticsData.fromJson(Map<String, dynamic> json) {
    return FinancialStatisticsData(
      totalRevenue: (json['total_revenue'] ?? 0).toDouble(),
      totalCost: (json['total_cost'] ?? 0).toDouble(),
      netProfit: (json['net_profit'] ?? 0).toDouble(),
      profitMargin: (json['profit_margin'] ?? 0).toDouble(),
      profitTrend: (json['profit_trend'] as List?)?.map((e) => e.toDouble()).toList() ?? [],
    );
  }

  static const FinancialStatisticsData empty = FinancialStatisticsData(
    totalRevenue: 0,
    totalCost: 0,
    netProfit: 0,
    profitMargin: 0,
    profitTrend: [],
  );
}

/// 销售趋势数据
class SalesTrendData {
  final List<double> trendData;
  final List<String> labels;

  const SalesTrendData({
    required this.trendData,
    required this.labels,
  });

  factory SalesTrendData.fromJson(Map<String, dynamic> json) {
    return SalesTrendData(
      trendData: (json['trend_data'] as List?)?.map((e) => e.toDouble()).toList() ?? [],
      labels: (json['labels'] as List?)?.map((e) => e.toString()).toList() ?? [],
    );
  }

  static const SalesTrendData empty = SalesTrendData(
    trendData: [],
    labels: [],
  );
}

/// 门店对比数据
class StoreComparisonData {
  final List<String> storeNames;
  final List<double> salesData;

  const StoreComparisonData({
    required this.storeNames,
    required this.salesData,
  });

  factory StoreComparisonData.fromJson(Map<String, dynamic> json) {
    return StoreComparisonData(
      storeNames: (json['store_names'] as List?)?.map((e) => e.toString()).toList() ?? [],
      salesData: (json['sales_data'] as List?)?.map((e) => e.toDouble()).toList() ?? [],
    );
  }

  static const StoreComparisonData empty = StoreComparisonData(
    storeNames: [],
    salesData: [],
  );
}

/// 商品排行数据
class CategoryRankingData {
  final List<String> categoryNames;
  final List<double> salesData;

  const CategoryRankingData({
    required this.categoryNames,
    required this.salesData,
  });

  factory CategoryRankingData.fromJson(Map<String, dynamic> json) {
    return CategoryRankingData(
      categoryNames: (json['category_names'] as List?)?.map((e) => e.toString()).toList() ?? [],
      salesData: (json['sales_data'] as List?)?.map((e) => e.toDouble()).toList() ?? [],
    );
  }

  static const CategoryRankingData empty = CategoryRankingData(
    categoryNames: [],
    salesData: [],
  );
}

/// 低库存预警数据
class LowStockAlertsData {
  final List<String> productNames;
  final List<int> stockCounts;

  const LowStockAlertsData({
    required this.productNames,
    required this.stockCounts,
  });

  factory LowStockAlertsData.fromJson(Map<String, dynamic> json) {
    return LowStockAlertsData(
      productNames: (json['product_names'] as List?)?.map((e) => e.toString()).toList() ?? [],
      stockCounts: (json['stock_counts'] as List?)?.map((e) => e as int).toList() ?? [],
    );
  }

  static const LowStockAlertsData empty = LowStockAlertsData(
    productNames: [],
    stockCounts: [],
  );
}

/// 会员增长数据
class MemberGrowthData {
  final List<int> growthData;
  final List<String> labels;

  const MemberGrowthData({
    required this.growthData,
    required this.labels,
  });

  factory MemberGrowthData.fromJson(Map<String, dynamic> json) {
    return MemberGrowthData(
      growthData: (json['growth_data'] as List?)?.map((e) => e as int).toList() ?? [],
      labels: (json['labels'] as List?)?.map((e) => e.toString()).toList() ?? [],
    );
  }

  static const MemberGrowthData empty = MemberGrowthData(
    growthData: [],
    labels: [],
  );
}
