"""
门店调拨数据验证模型

老板，这个模块定义了门店调拨相关的Pydantic验证模型，包括：
1. 调拨单明细的创建、更新、响应模型
2. 调拨单的创建、更新、响应模型
3. 审核、状态管理模型
4. 统计分析模型

完整的数据验证和序列化支持。
"""

from typing import List, Optional, Dict, Any
from decimal import Decimal
from pydantic import BaseModel, Field, ConfigDict, validator
from enum import IntEnum


# ==================== 枚举定义 ====================

class TransferStatus(IntEnum):
    """调拨单状态枚举"""
    PENDING = 0      # 待审核
    APPROVED = 1     # 已通过
    REJECTED = 2     # 已拒绝


# ==================== 调拨明细模型 ====================

class StoreTransferItemBase(BaseModel):
    """调拨明细基础模型"""
    jewelry_id: int = Field(..., ge=-1, description="商品ID(-1=工费项目,0=旧料回收,>0=正常商品)")
    transfer_price: Decimal = Field(..., description="调拨价格(旧料回收可为负数)")
    gold_price: Decimal = Field(Decimal('0.00'), ge=0, description="金价")
    silver_price: Decimal = Field(Decimal('0.00'), ge=0, description="银价")
    total_weight: Decimal = Field(Decimal('0.00'), ge=0, description="总重")
    silver_work_price: Decimal = Field(Decimal('0.00'), ge=0, description="工费")
    piece_work_price: Decimal = Field(Decimal('0.00'), ge=0, description="单件工费")
    original_data: Optional[str] = Field(None, description="原始数据JSON")

    @validator('transfer_price')
    def validate_transfer_price(cls, v, values):
        """验证调拨价格：旧料回收可以为负数，其他必须为正数"""
        jewelry_id = values.get('jewelry_id', 0)
        
        # 旧料回收(jewelry_id=0)允许负数价格
        if jewelry_id == 0:
            return v
        
        # 其他情况必须为非负数
        if v < 0:
            raise ValueError('调拨价格不能为负数(旧料回收除外)')
        
        return v


class StoreTransferItemCreate(StoreTransferItemBase):
    """创建调拨明细模型"""
    pass


class StoreTransferItemUpdate(BaseModel):
    """更新调拨明细模型"""
    transfer_price: Optional[Decimal] = Field(None, ge=0, description="调拨价格")
    gold_price: Optional[Decimal] = Field(None, ge=0, description="金价")
    silver_price: Optional[Decimal] = Field(None, ge=0, description="银价")
    total_weight: Optional[Decimal] = Field(None, ge=0, description="总重")
    silver_work_price: Optional[Decimal] = Field(None, ge=0, description="工费")
    piece_work_price: Optional[Decimal] = Field(None, ge=0, description="单件工费")
    original_data: Optional[str] = Field(None, description="原始数据JSON")


class StoreTransferItemResponse(StoreTransferItemBase):
    """调拨明细响应模型"""
    model_config = ConfigDict(from_attributes=True)

    id: int = Field(..., description="明细ID")
    transfer_id: int = Field(..., description="调拨单ID")
    createtime: Optional[int] = Field(None, description="创建时间")

    # 关联信息
    jewelry_name: Optional[str] = Field(None, description="商品名称")
    jewelry_barcode: Optional[str] = Field(None, description="商品条码")
    category_name: Optional[str] = Field(None, description="分类名称")


# ==================== 调拨单模型 ====================

class StoreTransferBase(BaseModel):
    """调拨单基础模型"""
    from_store_id: int = Field(..., ge=0, description="源店铺ID")
    to_store_id: int = Field(..., ge=0, description="目标店铺ID")
    remark: Optional[str] = Field(None, max_length=255, description="备注")


class StoreTransferCreate(StoreTransferBase):
    """创建调拨单模型"""
    items: List[StoreTransferItemCreate] = Field(..., min_length=1, description="调拨商品明细")


class StoreTransferUpdate(BaseModel):
    """更新调拨单模型"""
    remark: Optional[str] = Field(None, max_length=255, description="备注")
    items: Optional[List[StoreTransferItemUpdate]] = Field(None, description="调拨商品明细")


class StoreTransferAuditUpdate(BaseModel):
    """调拨单审核模型"""
    status: TransferStatus = Field(..., description="审核状态")
    remark: Optional[str] = Field(None, max_length=255, description="审核备注")


# ================= 收款模型 =================

class StoreTransferPaymentInfo(BaseModel):
    """调拨单收款信息模型"""
    payment_method: Optional[str] = Field(None, max_length=20, description="收款方式")
    payment_remark: Optional[str] = Field(None, max_length=255, description="支付备注")
    cash_amount: Optional[Decimal] = Field(default=Decimal('0.00'), ge=0, description="现金金额")
    wechat_amount: Optional[Decimal] = Field(default=Decimal('0.00'), ge=0, description="微信金额")
    alipay_amount: Optional[Decimal] = Field(default=Decimal('0.00'), ge=0, description="支付宝金额")
    card_amount: Optional[Decimal] = Field(default=Decimal('0.00'), ge=0, description="刷卡金额")
    discount_amount: Optional[Decimal] = Field(default=Decimal('0.00'), ge=0, description="抹零金额")
    actual_amount: Optional[Decimal] = Field(default=Decimal('0.00'), ge=0, description="实收金额")


class StoreTransferPaymentUpdate(BaseModel):
    """调拨单收款模型"""
    payment_status: int = Field(..., ge=0, le=1, description="收款状态:0=未收款,1=已收款")
    payment_info: Optional[StoreTransferPaymentInfo] = Field(None, description="收款信息")

    @validator('payment_status')
    def validate_payment_status(cls, v):
        if v not in [0, 1]:
            raise ValueError('收款状态必须是 0(未收款)或1(已收款)')
        return v


class StoreTransferResponse(StoreTransferBase):
    """调拨单响应模型"""
    model_config = ConfigDict(from_attributes=True)

    id: int = Field(..., description="调拨单ID")
    transfer_no: str = Field(..., description="调拨单号")
    admin_id: int = Field(..., description="操作员ID")
    status: int = Field(..., description="状态")
    audit_id: Optional[int] = Field(None, description="审核员ID")
    audit_time: Optional[int] = Field(None, description="审核时间")
    createtime: Optional[int] = Field(None, description="创建时间")

    # 关联信息
    from_store_name: Optional[str] = Field(None, description="源店铺名称")
    to_store_name: Optional[str] = Field(None, description="目标店铺名称")
    admin_name: Optional[str] = Field(None, description="操作员姓名")
    auditor_name: Optional[str] = Field(None, description="审核员姓名")

    # 统计信息
    item_count: int = Field(0, description="商品数量")
    total_amount: Decimal = Field(Decimal('0.00'), description="总金额")

    # 收款相关
    payment_status: int = Field(default=0, description="收款状态:0=未收款,1=已收款")
    payment_time: Optional[int] = Field(None, description="收款时间")
    payment_method: Optional[str] = Field(None, description="收款方式")
    payment_remark: Optional[str] = Field(None, description="支付备注")
    cash_amount: Decimal = Field(default=Decimal('0.00'), description="现金金额")
    wechat_amount: Decimal = Field(default=Decimal('0.00'), description="微信金额")
    alipay_amount: Decimal = Field(default=Decimal('0.00'), description="支付宝金额")
    card_amount: Decimal = Field(default=Decimal('0.00'), description="刷卡金额")
    discount_amount: Decimal = Field(default=Decimal('0.00'), description="抹零金额")
    actual_amount: Decimal = Field(default=Decimal('0.00'), description="实收金额")

    # 明细信息
    items: List[StoreTransferItemResponse] = Field(default_factory=list, description="调拨明细")


# ==================== 查询参数模型 ====================

class StoreTransferQueryParams(BaseModel):
    """调拨单查询参数"""
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(20, ge=1, le=100, description="每页数量")
    status: Optional[int] = Field(None, ge=0, le=2, description="状态筛选")
    from_store_id: Optional[int] = Field(None, gt=0, description="源店铺筛选")
    to_store_id: Optional[int] = Field(None, gt=0, description="目标店铺筛选")
    admin_id: Optional[int] = Field(None, gt=0, description="操作员筛选")
    keyword: Optional[str] = Field(None, description="关键词搜索")
    start_time: Optional[int] = Field(None, description="开始时间")
    end_time: Optional[int] = Field(None, description="结束时间")


# ==================== 统计模型 ====================

class StoreTransferStatistics(BaseModel):
    """调拨单统计模型"""
    total_count: int = Field(0, description="总调拨单数")
    pending_count: int = Field(0, description="待审核数量")
    approved_count: int = Field(0, description="已通过数量")
    rejected_count: int = Field(0, description="已拒绝数量")
    total_amount: Decimal = Field(Decimal('0.00'), description="总调拨金额")
    total_items: int = Field(0, description="总商品数量")

    # 状态分布
    status_distribution: Dict[str, int] = Field(default_factory=dict, description="状态分布")

    # 门店分布
    from_store_distribution: Dict[str, int] = Field(default_factory=dict, description="源店铺分布")
    to_store_distribution: Dict[str, int] = Field(default_factory=dict, description="目标店铺分布")

    # 时间分布
    daily_distribution: Dict[str, int] = Field(default_factory=dict, description="每日分布")
    monthly_distribution: Dict[str, int] = Field(default_factory=dict, description="每月分布")
