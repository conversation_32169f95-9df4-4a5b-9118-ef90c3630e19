"""
通用响应数据模型
定义API统一的响应格式
"""

from typing import Generic, TypeVar, Optional, List, Any, Dict
from pydantic import BaseModel, Field


# 定义泛型类型
T = TypeVar('T')


class StandardResponse(BaseModel, Generic[T]):
    """标准响应模型"""
    success: bool = Field(..., description="请求是否成功")
    message: str = Field(..., description="响应消息")
    data: Optional[T] = Field(None, description="响应数据")
    code: int = Field(200, description="状态码")

    class Config:
        """配置类"""
        json_schema_extra = {
            "example": {
                "success": True,
                "message": "操作成功",
                "data": None,
                "code": 200
            }
        }


class PaginationInfo(BaseModel):
    """分页信息模型"""
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页数量")
    total: int = Field(..., description="总记录数")
    pages: int = Field(..., description="总页数")

    class Config:
        """配置类"""
        json_schema_extra = {
            "example": {
                "page": 1,
                "page_size": 20,
                "total": 100,
                "pages": 5
            }
        }


class PaginatedResponse(BaseModel, Generic[T]):
    """分页响应模型"""
    success: bool = Field(..., description="请求是否成功")
    message: str = Field(..., description="响应消息")
    data: List[T] = Field(..., description="响应数据列表")
    pagination: PaginationInfo = Field(..., description="分页信息")
    code: int = Field(200, description="状态码")

    class Config:
        """配置类"""
        json_schema_extra = {
            "example": {
                "success": True,
                "message": "获取数据成功",
                "data": [],
                "pagination": {
                    "page": 1,
                    "page_size": 20,
                    "total": 100,
                    "pages": 5
                },
                "code": 200
            }
        }


class SuccessResponse(BaseModel):
    """成功响应模型"""
    success: bool = Field(True, description="请求是否成功")
    message: str = Field(..., description="成功消息")
    code: int = Field(200, description="状态码")

    class Config:
        """配置类"""
        json_schema_extra = {
            "example": {
                "success": True,
                "message": "操作成功",
                "code": 200
            }
        }


class ErrorResponse(BaseModel):
    """错误响应模型"""
    success: bool = Field(False, description="请求是否成功")
    message: str = Field(..., description="错误消息")
    error_code: Optional[str] = Field(None, description="错误代码")
    details: Optional[Dict[str, Any]] = Field(None, description="错误详情")
    code: int = Field(..., description="HTTP状态码")

    class Config:
        """配置类"""
        json_schema_extra = {
            "example": {
                "success": False,
                "message": "参数验证失败",
                "error_code": "VALIDATION_ERROR",
                "details": {
                    "field": "email",
                    "issue": "邮箱格式不正确"
                },
                "code": 400
            }
        }