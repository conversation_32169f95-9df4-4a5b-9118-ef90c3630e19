"""
数据导入导出相关的数据验证模型
"""

from typing import Optional, List, Dict, Any, Union
from pydantic import BaseModel, Field, validator
from datetime import datetime, date
from enum import Enum


class ExportFormat(str, Enum):
    """导出格式枚举"""
    EXCEL = "excel"
    CSV = "csv"
    PDF = "pdf"


class ExportType(str, Enum):
    """导出类型枚举"""
    JEWELRY = "jewelry"           # 商品数据
    STORE = "store"              # 门店数据
    MEMBER = "member"            # 会员数据
    ADMIN = "admin"              # 管理员数据
    STOCK_IN = "stock_in"        # 入库数据
    STOCK_OUT = "stock_out"      # 出库数据
    STOCK_RETURN = "stock_return" # 退货数据
    INVENTORY_CHECK = "inventory_check"  # 盘点数据
    RECYCLING = "recycling"      # 回收数据
    STORE_TRANSFER = "store_transfer"    # 调拨数据
    SALES_REPORT = "sales_report"        # 销售报表
    INVENTORY_REPORT = "inventory_report" # 库存报表
    FINANCIAL_REPORT = "financial_report" # 财务报表


class ImportType(str, Enum):
    """导入类型枚举"""
    JEWELRY = "jewelry"           # 商品数据
    MEMBER = "member"            # 会员数据
    STOCK_IN = "stock_in"        # 入库数据


class ExportRequest(BaseModel):
    """数据导出请求模型"""
    export_type: ExportType = Field(..., description="导出类型")
    export_format: ExportFormat = Field(default=ExportFormat.EXCEL, description="导出格式")
    
    # 筛选条件
    store_id: Optional[int] = Field(None, description="门店ID筛选")
    category_id: Optional[int] = Field(None, description="分类ID筛选")
    status: Optional[int] = Field(None, description="状态筛选")
    
    # 时间范围
    start_date: Optional[date] = Field(None, description="开始日期")
    end_date: Optional[date] = Field(None, description="结束日期")
    
    # 其他筛选条件
    keyword: Optional[str] = Field(None, description="关键词搜索")
    operator_id: Optional[int] = Field(None, description="操作员ID筛选")
    
    # 导出选项
    include_images: bool = Field(default=False, description="是否包含图片")
    include_details: bool = Field(default=True, description="是否包含详细信息")
    
    class Config:
        json_schema_extra = {
            "example": {
                "export_type": "jewelry",
                "export_format": "excel",
                "store_id": 1,
                "category_id": 2,
                "start_date": "2024-01-01",
                "end_date": "2024-12-31",
                "keyword": "戒指",
                "include_images": False,
                "include_details": True
            }
        }


class ExportResponse(BaseModel):
    """数据导出响应模型"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    download_url: Optional[str] = Field(None, description="下载链接")
    file_name: Optional[str] = Field(None, description="文件名")
    file_size: Optional[int] = Field(None, description="文件大小(字节)")
    record_count: Optional[int] = Field(None, description="导出记录数")
    export_time: datetime = Field(default_factory=datetime.now, description="导出时间")
    
    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "message": "数据导出成功",
                "download_url": "/api/v1/export/download/jewelry_20241225_143022.xlsx",
                "file_name": "jewelry_20241225_143022.xlsx",
                "file_size": 1024000,
                "record_count": 500,
                "export_time": "2024-12-25T14:30:22"
            }
        }


class ImportRequest(BaseModel):
    """数据导入请求模型"""
    import_type: ImportType = Field(..., description="导入类型")
    store_id: Optional[int] = Field(None, description="目标门店ID")
    operator_id: int = Field(..., description="操作员ID")
    
    # 导入选项
    skip_duplicates: bool = Field(default=True, description="是否跳过重复数据")
    update_existing: bool = Field(default=False, description="是否更新已存在数据")
    validate_only: bool = Field(default=False, description="是否仅验证不导入")
    
    class Config:
        json_schema_extra = {
            "example": {
                "import_type": "jewelry",
                "store_id": 1,
                "operator_id": 1,
                "skip_duplicates": True,
                "update_existing": False,
                "validate_only": False
            }
        }


class ImportValidationError(BaseModel):
    """导入验证错误模型"""
    row: int = Field(..., description="错误行号")
    column: str = Field(..., description="错误列名")
    value: Any = Field(..., description="错误值")
    error: str = Field(..., description="错误描述")


class ImportResponse(BaseModel):
    """数据导入响应模型"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    
    # 统计信息
    total_rows: int = Field(..., description="总行数")
    success_count: int = Field(..., description="成功导入数")
    error_count: int = Field(..., description="错误数量")
    skip_count: int = Field(..., description="跳过数量")
    
    # 错误详情
    errors: List[ImportValidationError] = Field(default=[], description="错误详情列表")
    
    # 导入结果
    import_time: datetime = Field(default_factory=datetime.now, description="导入时间")
    
    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "message": "数据导入完成",
                "total_rows": 100,
                "success_count": 95,
                "error_count": 3,
                "skip_count": 2,
                "errors": [
                    {
                        "row": 5,
                        "column": "barcode",
                        "value": "ABC123",
                        "error": "条码已存在"
                    }
                ],
                "import_time": "2024-12-25T14:30:22"
            }
        }


class TemplateRequest(BaseModel):
    """模板下载请求模型"""
    template_type: ImportType = Field(..., description="模板类型")
    include_sample_data: bool = Field(default=True, description="是否包含示例数据")
    
    class Config:
        json_schema_extra = {
            "example": {
                "template_type": "jewelry",
                "include_sample_data": True
            }
        }


class TemplateResponse(BaseModel):
    """模板下载响应模型"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    download_url: str = Field(..., description="模板下载链接")
    file_name: str = Field(..., description="模板文件名")
    
    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "message": "模板生成成功",
                "download_url": "/api/v1/export/template/jewelry_template.xlsx",
                "file_name": "jewelry_template.xlsx"
            }
        }


class ExportHistory(BaseModel):
    """导出历史记录模型"""
    id: int = Field(..., description="记录ID")
    export_type: str = Field(..., description="导出类型")
    export_format: str = Field(..., description="导出格式")
    file_name: str = Field(..., description="文件名")
    file_size: int = Field(..., description="文件大小")
    record_count: int = Field(..., description="记录数量")
    operator_id: int = Field(..., description="操作员ID")
    operator_name: Optional[str] = Field(None, description="操作员姓名")
    export_time: datetime = Field(..., description="导出时间")
    download_count: int = Field(default=0, description="下载次数")
    
    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": 1,
                "export_type": "jewelry",
                "export_format": "excel",
                "file_name": "jewelry_20241225_143022.xlsx",
                "file_size": 1024000,
                "record_count": 500,
                "operator_id": 1,
                "operator_name": "张管理员",
                "export_time": "2024-12-25T14:30:22",
                "download_count": 3
            }
        }


class ExportHistoryResponse(BaseModel):
    """导出历史响应模型"""
    success: bool = Field(default=True, description="是否成功")
    message: str = Field(default="获取成功", description="响应消息")
    data: List[ExportHistory] = Field(..., description="导出历史列表")
    total: int = Field(..., description="总记录数")
    
    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "message": "获取成功",
                "data": [],
                "total": 0
            }
        }
