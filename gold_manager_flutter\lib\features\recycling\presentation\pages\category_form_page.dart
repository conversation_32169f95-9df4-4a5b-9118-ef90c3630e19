import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../models/recycling/recycling_model.dart';
import '../../controllers/category_controller.dart';

/// 旧料分类表单页面
/// 用于新增和编辑分类
class CategoryFormPage extends StatefulWidget {
  /// 分类对象，新增时为null
  final RecyclingCategory? category;
  
  const CategoryFormPage({
    super.key,
    this.category,
  });
  
  @override
  State<CategoryFormPage> createState() => _CategoryFormPageState();
}

class _CategoryFormPageState extends State<CategoryFormPage> {
  late final CategoryController _controller;
  
  // 编辑模式
  bool get _isEditMode => widget.category != null;
  
  @override
  void initState() {
    super.initState();
    _controller = Get.find<CategoryController>();
    _controller.initEditForm(widget.category);
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditMode ? '编辑分类' : '添加分类'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildCategoryForm(),
            const SizedBox(height: 24),
            _buildSubmitButton(),
          ],
        ),
      ),
    );
  }
  
  /// 构建分类表单
  Widget _buildCategoryForm() {
    return Card(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _controller.formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '分类信息',
                style: AppTextStyles.subtitle,
              ),
              const SizedBox(height: 24),
              
              // 分类名称
              _buildTextField(
                label: '分类名称',
                hint: '请输入分类名称',
                controller: _controller.nameController,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return '请输入分类名称';
                  }
                  return null;
                },
                isRequired: true,
              ),
              const SizedBox(height: 16),
              
              // 分类描述
              _buildTextField(
                label: '分类描述',
                hint: '请输入分类描述',
                controller: _controller.descriptionController,
                maxLines: 3,
              ),
              const SizedBox(height: 16),
              
              // 分类状态
              _buildStatusSwitch(),
            ],
          ),
        ),
      ),
    );
  }
  
  /// 构建文本输入框
  Widget _buildTextField({
    required String label,
    required String hint,
    required TextEditingController controller,
    int maxLines = 1,
    String? Function(String?)? validator,
    bool isRequired = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              label,
              style: AppTextStyles.formLabel,
            ),
            if (isRequired) ...[
              const SizedBox(width: 4),
              const Text(
                '*',
                style: TextStyle(
                  color: AppColors.error,
                  fontSize: 14,
                ),
              ),
            ],
          ],
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          decoration: InputDecoration(
            hintText: hint,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
          ),
          maxLines: maxLines,
          validator: validator,
        ),
      ],
    );
  }
  
  /// 构建状态开关
  Widget _buildStatusSwitch() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        const Text(
          '启用状态',
          style: AppTextStyles.formLabel,
        ),
        Obx(() => Switch(
          value: _controller.isActiveController.value,
          onChanged: (value) => _controller.isActiveController.value = value,
          activeColor: AppColors.primary,
        )),
      ],
    );
  }
  
  /// 构建提交按钮
  Widget _buildSubmitButton() {
    return Obx(() => ElevatedButton(
      onPressed: _controller.isSubmitting.value
          ? null
          : _submitForm,
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: 16),
        textStyle: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
        ),
      ),
      child: _controller.isSubmitting.value
          ? const SizedBox(
              width: 24,
              height: 24,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            )
          : Text(_isEditMode ? '更新分类' : '添加分类'),
    ));
  }
  
  /// 提交表单
  Future<void> _submitForm() async {
    if (await _controller.saveCategory()) {
      // 返回上一页
      Get.back();
    }
  }
} 