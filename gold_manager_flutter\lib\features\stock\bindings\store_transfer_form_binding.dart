import 'package:get/get.dart';
import '../controllers/store_transfer_form_controller.dart';
import '../services/store_transfer_service.dart';
import '../../../services/store_service.dart';
import '../../../services/auth_service.dart';

/// 新建库存调拨表单绑定
/// 用于依赖注入和控制器初始化
class StoreTransferFormBinding extends Bindings {
  @override
  void dependencies() {
    // 确保必要的服务已注册
    if (!Get.isRegistered<StoreService>()) {
      Get.lazyPut<StoreService>(() => StoreService());
    }
    
    if (!Get.isRegistered<AuthService>()) {
      Get.lazyPut<AuthService>(() => AuthService());
    }
    
    if (!Get.isRegistered<StoreTransferService>()) {
      Get.lazyPut<StoreTransferService>(() => StoreTransferService());
    }

    // 注册库存调拨表单控制器
    Get.lazyPut<StoreTransferFormController>(
      () => StoreTransferFormController(),
    );
  }
}
