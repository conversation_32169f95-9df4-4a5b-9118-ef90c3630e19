import 'package:get/get.dart';
import '../../../core/services/api_service.dart';
import '../../../core/utils/logger_service.dart';
import '../../../core/utils/api_response_handler.dart';
import '../../../models/sales/sales_item_model.dart';
import '../models/sales_item_detail.dart';
import '../models/sales_statistics.dart';

/// 销售管理服务
class SalesService extends GetxService {
  late final ApiService _apiService;
  
  @override
  void onInit() {
    super.onInit();
    _apiService = Get.find<ApiService>();
  }

  /// 获取销售商品明细列表（带分页信息）
  Future<Map<String, dynamic>> getSalesItemListWithPagination({
    int page = 1,
    int limit = 20,
    String? salesType,
    DateTime? startDate,
    DateTime? endDate,
    int? storeId,
    String? search,
    Map<String, dynamic>? additionalFilters,
  }) async {
    try {
      final Map<String, dynamic> params = {
        'page': page,
        'page_size': limit,
      };

      // 添加筛选条件
      if (salesType != null && salesType.isNotEmpty) {
        params['sales_type'] = salesType;
      }

      if (startDate != null) {
        params['start_date'] = startDate.toIso8601String().split('T')[0];
      }

      if (endDate != null) {
        params['end_date'] = endDate.toIso8601String().split('T')[0];
      }

      if (storeId != null && storeId > 0) {
        params['store_id'] = storeId;
      }

      if (search != null && search.isNotEmpty) {
        params['keyword'] = search;
      }

      if (additionalFilters != null) {
        params.addAll(additionalFilters);
      }

      LoggerService.d('请求销售明细列表参数: $params');
      LoggerService.d('请求URL: /api/v1/sales/items, 页码: $page, 每页: $limit');

      final response = await _apiService.get('/api/v1/sales/items', queryParameters: params);
      
      LoggerService.d('API响应状态: ${response.statusCode}, 响应数据类型: ${response.data.runtimeType}');

      // 处理响应数据，提取分页信息
      final responseData = response.data;
      List<SalesItemDetail> items = [];
      int totalCount = 0;
      int totalPages = 1;

      if (responseData is Map<String, dynamic>) {
        // 提取分页信息 - 修复字段映射
        if (responseData.containsKey('pagination')) {
          final pagination = responseData['pagination'] as Map<String, dynamic>?;
          if (pagination != null) {
            totalCount = pagination['total'] ?? 0;
            totalPages = pagination['pages'] ?? 1;
          }
        }
        
        // 如果没有pagination字段，尝试直接查找
        if (totalCount == 0) {
          totalCount = responseData['total'] ?? 0;
          totalPages = responseData['total_pages'] ?? responseData['pages'] ?? 1;
        }
        
        // 提取数据列表
        items = ApiResponseHandler.handleListResponse<SalesItemDetail>(
          responseData,
          (json) => SalesItemDetail.fromJson(json),
          errorContext: '获取销售明细列表',
        );
      } else {
        // 如果响应直接是数组，则使用默认分页信息
        items = ApiResponseHandler.handleListResponse<SalesItemDetail>(
          responseData,
          (json) => SalesItemDetail.fromJson(json),
          errorContext: '获取销售明细列表',
        );
        totalCount = items.length;
        totalPages = (totalCount / limit).ceil();
      }

      // 计算总页数（如果API没有返回正确值）
      if (totalPages <= 1 && totalCount > limit) {
        totalPages = (totalCount / limit).ceil();
      }

      LoggerService.d('最终解析结果: items=${items.length}, totalCount=$totalCount, totalPages=$totalPages, page=$page');
      
      // 如果数据为空，记录详细信息用于调试
      if (items.isEmpty) {
        LoggerService.w('第 $page 页数据为空! 响应数据: ${responseData.toString().substring(0, responseData.toString().length > 500 ? 500 : responseData.toString().length)}...');
      }

      return {
        'items': items,
        'total_count': totalCount,
        'total_pages': totalPages,
        'current_page': page,
        'page_size': limit,
      };
      
    } catch (e) {
      LoggerService.e('获取销售明细列表失败', e);
      
      // 如果是网络错误，返回空数据而不是抛出异常
      if (e.toString().contains('SocketException') || 
          e.toString().contains('TimeoutException') ||
          e.toString().contains('Connection refused')) {
        LoggerService.w('网络连接问题，返回空明细列表');
        return {
          'items': <SalesItemDetail>[],
          'total_count': 0,
          'total_pages': 1,
          'current_page': page,
          'page_size': limit,
        };
      }
      
      rethrow;
    }
  }

  /// 获取销售商品明细列表
  Future<List<SalesItemDetail>> getSalesItemList({
    int page = 1,
    int limit = 20,
    String? salesType,
    DateTime? startDate,
    DateTime? endDate,
    int? storeId,
    String? search,
    Map<String, dynamic>? additionalFilters,
  }) async {
    try {
      final Map<String, dynamic> params = {
        'page': page,
        'page_size': limit,
      };

      // 添加筛选条件
      if (salesType != null && salesType.isNotEmpty) {
        params['sales_type'] = salesType;
      }

      if (startDate != null) {
        params['start_date'] = startDate.toIso8601String().split('T')[0];
      }

      if (endDate != null) {
        params['end_date'] = endDate.toIso8601String().split('T')[0];
      }

      if (storeId != null && storeId > 0) {
        params['store_id'] = storeId;
      }

      if (search != null && search.isNotEmpty) {
        params['keyword'] = search;
      }

      if (additionalFilters != null) {
        params.addAll(additionalFilters);
      }

      LoggerService.d('请求销售明细列表参数: $params');

      final response = await _apiService.get('/api/v1/sales/items', queryParameters: params);

      return ApiResponseHandler.handleListResponse<SalesItemDetail>(
        response.data,
        (json) => SalesItemDetail.fromJson(json),
        errorContext: '获取销售明细列表',
      );
      
    } catch (e) {
      LoggerService.e('获取销售明细列表失败', e);
      
      // 如果是网络错误，返回空列表而不是抛出异常
      if (e.toString().contains('SocketException') || 
          e.toString().contains('TimeoutException') ||
          e.toString().contains('Connection refused')) {
        LoggerService.w('网络连接问题，返回空明细列表');
        return [];
      }
      
      rethrow;
    }
  }

  /// 获取销售统计信息
  Future<SalesStatistics> getSalesStatistics({
    String? salesType,
    DateTime? startDate,
    DateTime? endDate,
    int? storeId,
    String? search,
    Map<String, dynamic>? additionalFilters,
  }) async {
    try {
      final Map<String, dynamic> params = {};

      // 添加筛选条件
      if (salesType != null && salesType.isNotEmpty) {
        params['sales_type'] = salesType;
      }

      if (startDate != null) {
        params['start_date'] = startDate.toIso8601String().split('T')[0];
      }

      if (endDate != null) {
        params['end_date'] = endDate.toIso8601String().split('T')[0];
      }

      if (storeId != null && storeId > 0) {
        params['store_id'] = storeId;
      }

      if (search != null && search.isNotEmpty) {
        params['keyword'] = search;
      }

      if (additionalFilters != null) {
        params.addAll(additionalFilters);
      }

      LoggerService.d('请求销售统计参数: $params');

      final response = await _apiService.get('/api/v1/sales/statistics', queryParameters: params);

      return ApiResponseHandler.handleObjectResponse<SalesStatistics>(
        response.data,
        (json) => SalesStatistics.fromJson(json),
        errorContext: '获取销售统计',
        defaultValue: SalesStatistics.empty(),
      ) ?? SalesStatistics.empty();
      
    } catch (e) {
      LoggerService.e('获取销售统计失败', e);
      
      // 如果是网络错误或服务器错误，返回空统计数据而不是抛出异常
      if (e.toString().contains('SocketException') || 
          e.toString().contains('TimeoutException') ||
          e.toString().contains('Connection refused')) {
        LoggerService.w('网络连接问题，返回空统计数据');
        return SalesStatistics.empty();
      }
      
      rethrow;
    }
  }

  /// 获取单据商品明细
  Future<List<SalesItemDetail>> getOrderItemDetails(String orderNo) async {
    try {
      LoggerService.d('请求单据商品明细: $orderNo');

      // 尝试将orderNo转换为数字ID，如果失败则直接使用字符串
      int? orderId;
      try {
        orderId = int.parse(orderNo);
      } catch (e) {
        // 如果无法转换为数字，可能需要其他方式处理
        throw Exception('单据号格式错误');
      }

      final response = await _apiService.get('/api/v1/sales/order/$orderId/items');

      if (response.data['success'] == true) { // 改为检查success字段
        final List<dynamic> itemsData = response.data['data'] ?? [];
        return itemsData
            .map((json) => SalesItemDetail.fromJson(json))
            .toList();
      } else {
        throw Exception(response.data['message'] ?? '获取单据明细失败');
      }
    } catch (e) {
      LoggerService.e('获取单据商品明细失败', e);
      rethrow;
    }
  }

  /// 导出销售明细数据
  Future<String> exportSalesData({
    String? salesType,
    DateTime? startDate,
    DateTime? endDate,
    int? storeId,
    String? search,
    String format = 'excel',
  }) async {
    try {
      final Map<String, dynamic> params = {
        'format': format,
      };

      // 添加筛选条件
      if (salesType != null && salesType.isNotEmpty) {
        params['sales_type'] = salesType;
      }

      if (startDate != null) {
        params['start_date'] = startDate.toIso8601String().split('T')[0]; // 格式改为YYYY-MM-DD
      }

      if (endDate != null) {
        params['end_date'] = endDate.toIso8601String().split('T')[0]; // 格式改为YYYY-MM-DD
      }

      if (storeId != null && storeId > 0) {
        params['store_id'] = storeId;
      }

      if (search != null && search.isNotEmpty) {
        params['keyword'] = search; // 改为keyword，匹配后端参数
      }

      LoggerService.d('导出销售数据参数: $params');

      final response = await _apiService.get('/api/v1/sales/export', queryParameters: params);

      if (response.data['success'] == true) { // 改为检查success字段
        return response.data['data']['download_url'] ?? '';
      } else {
        throw Exception(response.data['message'] ?? '导出数据失败');
      }
    } catch (e) {
      LoggerService.e('导出销售数据失败', e);
      rethrow;
    }
  }

  /// 获取销售订单列表 (兼容旧版本接口)
  Future<List<Map<String, dynamic>>> getSalesOrderList({
    Map<String, dynamic>? filters,
  }) async {
    try {
      final Map<String, dynamic> params = {};
      
      if (filters != null) {
        params.addAll(filters);
      }

      LoggerService.d('请求销售订单列表参数: $params');

      final response = await _apiService.get('/api/v1/sales/orders', queryParameters: params);

      if (response.data['success'] == true) {
        final List<dynamic> ordersData = response.data['data'] ?? [];
        return ordersData.cast<Map<String, dynamic>>();
      } else {
        throw Exception(response.data['message'] ?? '获取销售订单列表失败');
      }
    } catch (e) {
      LoggerService.e('获取销售订单列表失败', e);
      rethrow;
    }
  }

  /// 获取销售订单商品明细 (兼容旧版本接口)
  Future<List<SalesItem>> getSalesOrderItems(int orderId) async {
    try {
      LoggerService.d('请求销售订单商品明细: $orderId');

      final response = await _apiService.get('/api/v1/sales/orders/$orderId/items');

      return ApiResponseHandler.handleListResponse<SalesItem>(
        response.data,
        (json) => SalesItem.fromJson(json),
        errorContext: '获取销售订单商品明细',
      );
      
    } catch (e) {
      LoggerService.e('获取销售订单商品明细失败', e);
      
      // 如果是网络错误，返回空列表而不是抛出异常
      if (e.toString().contains('SocketException') || 
          e.toString().contains('TimeoutException') ||
          e.toString().contains('Connection refused')) {
        LoggerService.w('网络连接问题，返回空订单明细列表');
        return [];
      }
      
      rethrow;
    }
  }
}
