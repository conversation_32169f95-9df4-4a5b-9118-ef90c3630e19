"""
商品相关的数据验证模型
"""

from typing import Optional, List
from decimal import Decimal
from pydantic import BaseModel, Field, validator


# 商品分类相关模型
class JewelryCategoryBase(BaseModel):
    """商品分类基础模型"""
    name: str = Field(..., description="分类名称", max_length=50)
    pid: int = Field(0, description="父级ID")
    status: int = Field(1, description="状态:0=禁用,1=正常")
    weigh: int = Field(0, description="权重")


class JewelryCategoryCreate(JewelryCategoryBase):
    """创建商品分类模型"""
    pass


class JewelryCategoryUpdate(BaseModel):
    """更新商品分类模型"""
    name: Optional[str] = Field(None, max_length=50)
    pid: Optional[int] = None
    status: Optional[int] = Field(None, ge=0, le=1)
    weigh: Optional[int] = None


class JewelryCategoryResponse(JewelryCategoryBase):
    """商品分类响应模型"""
    id: int
    createtime: Optional[int] = None
    updatetime: Optional[int] = None
    
    class Config:
        from_attributes = True


# 商品相关模型
class JewelryBase(BaseModel):
    """商品基础模型"""
    barcode: str = Field(..., description="条码", max_length=50)
    name: str = Field(..., description="商品名称", max_length=100)
    category_id: int = Field(..., description="分类ID")
    ring_size: Optional[str] = Field(None, description="圈口号", max_length=20)
    
    # 金重相关
    gold_weight: Decimal = Field(Decimal('0.00'), description="金重(克)", ge=0)
    gold_price: Decimal = Field(Decimal('0.00'), description="金进价(克价)", ge=0)
    gold_cost: Decimal = Field(Decimal('0.00'), description="金成本", ge=0)
    
    # 银重相关
    silver_weight: Decimal = Field(Decimal('0.00'), description="银重(克)", ge=0)
    total_weight: Decimal = Field(Decimal('0.00'), description="总重", ge=0)
    silver_price: Decimal = Field(Decimal('0.00'), description="银进价(克价)", ge=0)
    silver_cost: Decimal = Field(Decimal('0.00'), description="银成本", ge=0)
    
    # 工费相关
    silver_work_type: int = Field(0, description="银工费方式:0=按克,1=按件", ge=0, le=1)
    silver_work_price: Decimal = Field(Decimal('0.00'), description="银工费", ge=0)
    plating_cost: Decimal = Field(Decimal('0.00'), description="电铸费", ge=0)
    
    # 成本和定价
    total_cost: Decimal = Field(Decimal('0.00'), description="进货总成本", ge=0)
    wholesale_work_price: Decimal = Field(Decimal('0.00'), description="批发工费", ge=0)
    retail_work_price: Decimal = Field(Decimal('0.00'), description="零售工费", ge=0)
    piece_work_price: Decimal = Field(Decimal('0.00'), description="件工费", ge=0)
    
    # 门店和状态
    store_id: int = Field(..., description="所属门店")
    status: int = Field(1, description="状态:0=下架,1=上架,2=待出库", ge=0, le=2)


class JewelryCreate(JewelryBase):
    """创建商品模型"""
    
    @validator('total_weight')
    def validate_total_weight(cls, v, values):
        """验证总重量"""
        gold_weight = values.get('gold_weight', Decimal('0.00'))
        silver_weight = values.get('silver_weight', Decimal('0.00'))
        calculated_total = gold_weight + silver_weight
        if v != calculated_total:
            return calculated_total
        return v
    
    @validator('gold_cost')
    def validate_gold_cost(cls, v, values):
        """验证金成本"""
        gold_weight = values.get('gold_weight', Decimal('0.00'))
        gold_price = values.get('gold_price', Decimal('0.00'))
        calculated_cost = gold_weight * gold_price
        if v != calculated_cost:
            return calculated_cost
        return v
    
    @validator('silver_cost')
    def validate_silver_cost(cls, v, values):
        """验证银成本"""
        silver_weight = values.get('silver_weight', Decimal('0.00'))
        silver_price = values.get('silver_price', Decimal('0.00'))
        calculated_cost = silver_weight * silver_price
        if v != calculated_cost:
            return calculated_cost
        return v


class JewelryUpdate(BaseModel):
    """更新商品模型"""
    barcode: Optional[str] = Field(None, max_length=50)
    name: Optional[str] = Field(None, max_length=100)
    category_id: Optional[int] = None
    ring_size: Optional[str] = Field(None, max_length=20)
    
    # 金重相关
    gold_weight: Optional[Decimal] = Field(None, ge=0)
    gold_price: Optional[Decimal] = Field(None, ge=0)
    gold_cost: Optional[Decimal] = Field(None, ge=0)
    
    # 银重相关
    silver_weight: Optional[Decimal] = Field(None, ge=0)
    total_weight: Optional[Decimal] = Field(None, ge=0)
    silver_price: Optional[Decimal] = Field(None, ge=0)
    silver_cost: Optional[Decimal] = Field(None, ge=0)
    
    # 工费相关
    silver_work_type: Optional[int] = Field(None, ge=0, le=1)
    silver_work_price: Optional[Decimal] = Field(None, ge=0)
    plating_cost: Optional[Decimal] = Field(None, ge=0)
    
    # 成本和定价
    total_cost: Optional[Decimal] = Field(None, ge=0)
    wholesale_work_price: Optional[Decimal] = Field(None, ge=0)
    retail_work_price: Optional[Decimal] = Field(None, ge=0)
    piece_work_price: Optional[Decimal] = Field(None, ge=0)
    
    # 门店和状态
    store_id: Optional[int] = None
    status: Optional[int] = Field(None, ge=0, le=2)


class JewelryResponse(JewelryBase):
    """商品响应模型"""
    id: int
    createtime: Optional[int] = None
    updatetime: Optional[int] = None
    
    # 关联信息
    category_name: Optional[str] = None
    store_name: Optional[str] = None
    
    class Config:
        from_attributes = True


class JewelryListResponse(BaseModel):
    """商品列表响应模型"""
    items: List[JewelryResponse]
    total: int
    page: int
    page_size: int
    total_pages: int
    
    class Config:
        from_attributes = True


class JewelryCostBreakdown(BaseModel):
    """商品成本明细模型"""
    jewelry_id: int
    jewelry_name: str
    
    # 金成本
    gold_weight: Decimal
    gold_price: Decimal
    gold_cost: Decimal
    
    # 银成本
    silver_weight: Decimal
    silver_price: Decimal
    silver_cost: Decimal
    
    # 工费成本
    silver_work_cost: Decimal
    plating_cost: Decimal
    
    # 总成本
    total_material_cost: Decimal
    total_work_cost: Decimal
    total_cost: Decimal
    
    # 定价
    wholesale_price: Decimal
    retail_price: Decimal
    piece_price: Decimal 