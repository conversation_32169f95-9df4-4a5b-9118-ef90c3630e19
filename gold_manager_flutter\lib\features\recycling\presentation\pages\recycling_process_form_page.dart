import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../features/common/widgets/price_input_field.dart';
import '../../controllers/recycling_process_controller.dart';
import '../widgets/process_type_selector.dart';
import '../widgets/image_picker_widget.dart';

/// 旧料处理表单页面
class RecyclingProcessFormPage extends StatelessWidget {
  final RecyclingProcessController controller;

  const RecyclingProcessFormPage({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Obx(() => Text(controller.currentProcess.value?.id != null
            ? '编辑处理记录'
            : '新增处理记录')),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: '重置表单',
            onPressed: controller.resetForm,
          ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildItemInfoCard(),
              const SizedBox(height: 16),
              
              // 处理方式选择器
              Obx(() => ProcessTypeSelector(
                selectedType: controller.selectedProcessType.value,
                onTypeChanged: controller.changeProcessType,
                description: controller.getProcessTypeDescription(),
              )),
              const SizedBox(height: 24),
              
              // 成本和收益信息
              _buildFinancialInfoCard(),
              const SizedBox(height: 24),
              
              // 备注输入框
              TextFormField(
                decoration: const InputDecoration(
                  labelText: '备注',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.all(Radius.circular(12)),
                  ),
                  hintText: '可选',
                ),
                maxLines: 3,
                onChanged: (value) {
                  controller.remark.value = value;
                },
                initialValue: controller.remark.value,
              ),
              const SizedBox(height: 24),
              
              // 图片上传
              Container(
                decoration: BoxDecoration(
                  color: AppColors.backgroundLight,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: AppColors.border),
                ),
                padding: const EdgeInsets.all(16),
                child: Obx(() => ImagePickerWidget(
                  imageUrls: controller.imageUrls,
                  tempImages: controller.tempImages,
                  onPickImage: controller.pickImage,
                  onTakePhoto: controller.takePhoto,
                  onDeleteTempImage: controller.removeTempImage,
                  onDeleteUploadedImage: controller.removeUploadedImage,
                  isLoading: controller.isUploadingImage.value,
                )),
              ),
              const SizedBox(height: 32),
              
              // 提交按钮
              SizedBox(
                width: double.infinity,
                height: 48,
                child: ElevatedButton(
                  onPressed: controller.isSubmitting.value || controller.selectedProcessType.value.value == 'pending'
                      ? null
                      : _submitForm,
                  style: ElevatedButton.styleFrom(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: controller.isSubmitting.value
                      ? const SizedBox(
                          width: 24,
                          height: 24,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : Text(
                          controller.currentProcess.value?.id != null
                              ? '保存修改'
                              : '提交处理记录',
                          style: const TextStyle(fontSize: 16),
                        ),
                ),
              ),
            ],
          ),
        );
      }),
    );
  }

  // 回收物品信息卡片
  Widget _buildItemInfoCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  '回收物品信息',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
                Text(
                  controller.recyclingItem.name,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
            const Divider(),
            _buildInfoRow(
              '金重',
              '${controller.recyclingItem.goldWeight}g',
              '银重',
              '${controller.recyclingItem.silverWeight}g',
            ),
            const SizedBox(height: 8),
            _buildInfoRow(
              '回收金价',
              '¥${controller.recyclingItem.goldPrice}/g',
              '回收银价',
              '¥${controller.recyclingItem.silverPrice}/g',
            ),
            const SizedBox(height: 8),
            _buildInfoRow(
              '回收价格',
              '¥${controller.recyclingItem.amount.toStringAsFixed(2)}',
              '处理方式',
              controller.recyclingItem.processType.label,
            ),
          ],
        ),
      ),
    );
  }

  // 财务信息卡片
  Widget _buildFinancialInfoCard() {
    return Obx(() => Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '财务信息',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            const Divider(),
            Row(
              children: [
                Expanded(
                  child: PriceInputField(
                    label: '处理成本',
                    value: controller.cost.value,
                    onChanged: (value) {
                      controller.cost.value = value;
                      controller.profit.value = controller.income.value - value;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: PriceInputField(
                    label: '预计收入',
                    value: controller.income.value,
                    onChanged: (value) {
                      controller.income.value = value;
                      controller.profit.value = value - controller.cost.value;
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildProfitInfo(),
          ],
        ),
      ),
    ));
  }

  // 利润信息展示
  Widget _buildProfitInfo() {
    final profit = controller.profit.value;
    final profitColor = profit >= 0 ? AppColors.success : AppColors.error;
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: profitColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          const Text(
            '预计利润',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            '¥${profit.toStringAsFixed(2)}',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: profitColor,
            ),
          ),
        ],
      ),
    );
  }

  // 信息行
  Widget _buildInfoRow(String label1, String value1, String label2, String value2) {
    return Row(
      children: [
        Expanded(
          child: _buildInfoItem(label1, value1),
        ),
        Expanded(
          child: _buildInfoItem(label2, value2),
        ),
      ],
    );
  }

  // 信息项
  Widget _buildInfoItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  // 提交表单
  void _submitForm() async {
    // 验证处理类型
    if (controller.selectedProcessType.value.value == 'pending') {
      Get.snackbar('错误', '请选择处理方式', snackPosition: SnackPosition.BOTTOM);
      return;
    }
    
    // 验证收益
    if (controller.income.value <= 0) {
      Get.snackbar('错误', '预计收入必须大于0', snackPosition: SnackPosition.BOTTOM);
      return;
    }
    
    final success = await controller.submitProcess();
    if (success) {
      Get.back(result: true);
      Get.snackbar(
        '成功',
        controller.currentProcess.value?.id != null
            ? '处理记录已更新'
            : '处理记录已添加',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.success.withOpacity(0.8),
        colorText: Colors.white,
      );
    } else {
      Get.snackbar(
        '失败',
        '操作失败，请重试',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error.withOpacity(0.8),
        colorText: Colors.white,
      );
    }
  }
} 