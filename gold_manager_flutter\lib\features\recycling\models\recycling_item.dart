import '../../../models/common/enums.dart';

/// 回收物品模型 (用于处理页面)
class RecyclingItem {
  final int id;
  final int recyclingId;
  final String name;
  final String? description;
  final double goldWeight;
  final double goldPrice;
  final double silverWeight;
  final double silverPrice;
  final double amount;
  final String? imageUrl;
  final RecyclingProcessType processType;
  final double weight;
  final int type;
  final DateTime? createTime;

  const RecyclingItem({
    required this.id,
    required this.recyclingId,
    required this.name,
    this.description,
    required this.goldWeight,
    required this.goldPrice,
    required this.silverWeight,
    required this.silverPrice,
    required this.amount,
    this.imageUrl,
    this.processType = RecyclingProcessType.pending,
    required this.weight,
    this.type = 1,
    this.createTime,
  });

  /// 获取物品名称
  String get itemName => name;

  /// 获取回收时间
  DateTime get recyclingTime => createTime ?? DateTime.now();

  /// 获取物品单价
  double get price => amount / (weight > 0 ? weight : 1);

  /// 获取物品类型名称
  String getTypeName() {
    switch (type) {
      case 1:
        return '黄金首饰';
      case 2:
        return '银饰';
      case 3:
        return '金银混合';
      case 4:
        return '其他';
      default:
        return '未知';
    }
  }
} 