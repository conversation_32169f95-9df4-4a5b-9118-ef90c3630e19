/// 应用配置类
/// 包含应用程序的全局配置项
class AppConfig {
  /// 应用是否处于调试模式
  static bool get isDebug => true; // 在生产环境中应设置为false

  /// API基础URL - 开发环境使用HTTP（后端未配置SSL证书）
  static const String apiBaseUrl = 'http://localhost:8000';

  /// API超时时间（秒）
  static const int apiTimeout = 30;

  /// 应用版本
  static const String appVersion = '1.0.0';

  /// 应用名称
  static const String appName = '黄金珠宝管理系统';

  /// 应用ID
  static const String appId = 'com.example.goldmanager';

  /// 本地存储键前缀
  static const String storagePrefix = 'goldmanager_';

  /// 数据缓存过期时间（分钟）
  static const int cacheExpiration = 60 * 5; // 5分钟

  /// 每页默认项数
  static const int defaultPageSize = 20;

  /// 数据表页面默认显示列数
  static const int defaultTableColumns = 6;

  /// 价格精度（小数位数）
  static const int pricePrecision = 2;

  /// 重量精度（小数位数）
  static const int weightPrecision = 2;

  // API配置 - 根据API文档调整超时时间
  static const int connectTimeout = 30000; // 30秒
  static const int receiveTimeout = 30000; // 30秒

  // 本地存储Keys - 添加refresh_token支持
  static const String tokenKey = 'access_token';
  static const String refreshTokenKey = 'refresh_token';
  static const String userIdKey = 'user_id';
  static const String userNameKey = 'user_name';
  static const String userNicknameKey = 'user_nickname';
  static const String userEmailKey = 'user_email';
  static const String userRoleKey = 'user_role';
  static const String storeIdKey = 'store_id';
  static const String storeNameKey = 'store_name';
  static const String themeKey = 'app_theme';
  static const String languageKey = 'app_language';
  static const String rememberMeKey = 'remember_me';

  // 默认设置
  static const double defaultGoldPrice = 380.0; // 元/克
  static const double defaultSilverPrice = 6.0; // 元/克

  // 功能开关
  static const bool enableOfflineMode = true;
  static const bool enableDataSync = true;
  static const bool enablePriceAlert = true;

  /// API端点配置
  static final Map<String, String> apiEndpoint = {
    'auth': '/api/v1/auth/',
    'user': '/api/v1/admin/',
    'jewelry': '/api/v1/jewelry/',
    'stock': '/api/v1/stock/',
    'stockIn': '/api/v1/stock-in/',
    'stockOut': '/api/v1/stock-out/',
    'stockReturn': '/api/v1/stock-return/',
    'inventoryCheck': '/api/v1/inventory-check/',
    'recycling': '/api/v1/recycling',
    'storeTransfer': '/api/v1/store-transfer/',
    'store': '/api/v1/store/',
    'member': '/api/v1/member/',
    'admin': '/api/v1/admin/',
    'dashboard': '/api/v1/dashboard/',
    'dataExport': '/api/v1/data-export/',
    'report': '/api/v1/report/',
    'settings': '/api/v1/settings/',
    'upload': '/api/v1/upload/',
  };

  /// 应用主题配置
  static const Map<String, dynamic> themeConfig = {
    'primary': 0xFF1E88E5,
    'secondary': 0xFF26A69A,
    'background': 0xFFF5F5F5,
    'card': 0xFFFFFFFF,
    'error': 0xFFD32F2F,
    'success': 0xFF388E3C,
    'warning': 0xFFFFA000,
    'info': 0xFF2196F3,
  };

  /// 是否为开发环境
  static const bool isDevelopment = true;
}
