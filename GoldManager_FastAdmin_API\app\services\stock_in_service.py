"""
入库管理业务服务类
处理入库单相关的所有业务逻辑
"""

import time
from typing import List, Optional, Dict, Any, Tuple
from decimal import Decimal
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import func, desc, asc, and_, or_
from datetime import datetime

from ..models.stock_in import StockIn, StockInItem
from ..models.store import Store
from ..models.admin import Admin
from ..models.jewelry import Jewelry
from ..schemas.stock_in import (
    StockInCreate, StockInUpdate, StockInStatusUpdate,
    StockInQueryParams, StockInResponse, StockInStatistics
)


class StockInService:
    """入库管理服务类"""

    # ✅ 定义占位符常量：用于表示未创建的首饰记录
    PLACEHOLDER_JEWELRY_ID = -1

    def __init__(self, db: Session):
        self.db = db

    def generate_order_no(self) -> str:
        """生成入库单号"""
        now = datetime.now()
        prefix = f"IN{now.year}{now.month:02d}{now.day:02d}"

        # 查询今天已有的入库单数量
        today_start = int(datetime(now.year, now.month, now.day).timestamp())
        today_end = today_start + 86400  # 24小时后

        count = self.db.query(StockIn).filter(
            and_(
                StockIn.createtime >= today_start,
                StockIn.createtime < today_end
            )
        ).count()

        return f"{prefix}{count + 1:04d}"

    def create_stock_in(self, stock_in_data: StockInCreate, operator_id: int) -> StockIn:
        """创建入库单"""
        try:
            # 验证门店是否存在
            store = self.db.query(Store).filter(Store.id == stock_in_data.store_id).first()
            if not store:
                raise ValueError(f"门店ID {stock_in_data.store_id} 不存在")

            # 验证操作员是否存在
            operator = self.db.query(Admin).filter(Admin.id == operator_id).first()
            if not operator:
                raise ValueError(f"操作员ID {operator_id} 不存在")

            # ✅ 修复：计算总金额，不验证jewelry_id（审核通过后才创建首饰记录）
            total_amount = Decimal('0.00')
            for item_data in stock_in_data.items:
                # 不再验证jewelry_id，因为审核通过前jewelry_id为null
                total_amount += item_data.total_cost

            current_time = int(time.time())

            # 创建入库单主表
            stock_in = StockIn(
                order_no=self.generate_order_no(),
                store_id=stock_in_data.store_id,
                supplier=stock_in_data.supplier,
                total_amount=total_amount,
                operator_id=operator_id,
                remark=stock_in_data.remark,
                status=0,  # ✅ 修复：新建入库单默认为草稿状态
                createtime=current_time,
                updatetime=current_time
            )

            self.db.add(stock_in)
            self.db.flush()  # 获取入库单ID

            # 创建入库单明细
            for item_data in stock_in_data.items:
                # ✅ 修复：使用占位符值代替null，避免数据库约束错误
                jewelry_id = item_data.jewelry_id if item_data.jewelry_id is not None else self.PLACEHOLDER_JEWELRY_ID

                stock_in_item = StockInItem(
                    stock_in_id=stock_in.id,
                    jewelry_id=jewelry_id,  # ✅ 使用占位符值或实际值
                    barcode=item_data.barcode,
                    name=item_data.name,
                    category_id=item_data.category_id,
                    ring_size=item_data.ring_size,
                    gold_weight=item_data.gold_weight,
                    gold_price=item_data.gold_price,
                    gold_cost=item_data.gold_cost,
                    silver_weight=item_data.silver_weight,
                    total_weight=item_data.total_weight,
                    silver_price=item_data.silver_price,
                    silver_cost=item_data.silver_cost,
                    silver_work_type=item_data.silver_work_type,
                    silver_work_price=item_data.silver_work_price,
                    silver_work_cost=item_data.silver_work_cost,
                    plating_cost=item_data.plating_cost,
                    total_cost=item_data.total_cost,
                    wholesale_work_price=item_data.wholesale_work_price,
                    retail_work_price=item_data.retail_work_price,
                    piece_work_price=item_data.piece_work_price,
                    createtime=current_time
                )
                self.db.add(stock_in_item)

                print(f"✅ [创建明细] 商品: {item_data.name}, jewelry_id: {jewelry_id} {'(占位符)' if jewelry_id == self.PLACEHOLDER_JEWELRY_ID else '(实际值)'}")

            self.db.commit()
            self.db.refresh(stock_in)

            return stock_in

        except Exception as e:
            self.db.rollback()
            raise e

    def get_stock_in_list(self, params: StockInQueryParams) -> Tuple[List[StockIn], int]:
        """获取入库单列表"""
        query = self.db.query(StockIn)

        # 关联查询
        query = query.options(
            joinedload(StockIn.store),
            joinedload(StockIn.operator),
            joinedload(StockIn.auditor),
            joinedload(StockIn.items).joinedload(StockInItem.jewelry)
        )

        # 关键词搜索
        if params.keyword:
            keyword = f"%{params.keyword}%"
            query = query.filter(
                or_(
                    StockIn.order_no.like(keyword),
                    StockIn.remark.like(keyword)
                )
            )

        # 门店筛选
        if params.store_id:
            query = query.filter(StockIn.store_id == params.store_id)

        # 状态筛选
        if params.status is not None:
            query = query.filter(StockIn.status == params.status)

        # 操作员筛选
        if params.operator_id:
            query = query.filter(StockIn.operator_id == params.operator_id)

        # 供应商筛选
        if params.supplier:
            supplier_keyword = f"%{params.supplier}%"
            query = query.filter(StockIn.supplier.like(supplier_keyword))

        # 日期范围筛选
        if params.start_date:
            try:
                start_timestamp = int(datetime.strptime(params.start_date, "%Y-%m-%d").timestamp())
                query = query.filter(StockIn.createtime >= start_timestamp)
            except ValueError:
                pass

        if params.end_date:
            try:
                end_timestamp = int(datetime.strptime(params.end_date, "%Y-%m-%d").timestamp()) + 86400
                query = query.filter(StockIn.createtime < end_timestamp)
            except ValueError:
                pass

        # 获取总数
        total = query.count()

        # 分页和排序
        query = query.order_by(desc(StockIn.createtime))
        offset = (params.page - 1) * params.page_size
        stock_ins = query.offset(offset).limit(params.page_size).all()

        return stock_ins, total

    def get_stock_in_by_id(self, stock_in_id: int) -> Optional[StockIn]:
        """根据ID获取入库单详情"""
        return self.db.query(StockIn).options(
            joinedload(StockIn.store),
            joinedload(StockIn.operator),
            joinedload(StockIn.auditor),
            joinedload(StockIn.items).joinedload(StockInItem.jewelry)
        ).filter(StockIn.id == stock_in_id).first()

    def get_stock_in_by_no(self, order_no: str) -> Optional[StockIn]:
        """根据单号获取入库单详情"""
        return self.db.query(StockIn).options(
            joinedload(StockIn.store),
            joinedload(StockIn.operator),
            joinedload(StockIn.auditor),
            joinedload(StockIn.items).joinedload(StockInItem.jewelry)
        ).filter(StockIn.order_no == order_no).first()

    def update_stock_in(self, stock_in_id: int, stock_in_data: StockInUpdate) -> Optional[StockIn]:
        """更新入库单"""
        try:
            stock_in = self.db.query(StockIn).filter(StockIn.id == stock_in_id).first()
            if not stock_in:
                return None

            current_time = int(time.time())

            # 更新基本信息
            if stock_in_data.store_id is not None:
                # 验证门店是否存在
                store = self.db.query(Store).filter(Store.id == stock_in_data.store_id).first()
                if not store:
                    raise ValueError(f"门店ID {stock_in_data.store_id} 不存在")
                stock_in.store_id = stock_in_data.store_id

            if stock_in_data.supplier is not None:
                stock_in.supplier = stock_in_data.supplier

            if stock_in_data.remark is not None:
                stock_in.remark = stock_in_data.remark

            # 更新明细
            if stock_in_data.items is not None:
                # 删除原有明细
                self.db.query(StockInItem).filter(
                    StockInItem.stock_in_id == stock_in_id
                ).delete()

                # ✅ 修复：验证商品并计算总金额，跳过占位符值验证
                total_amount = Decimal('0.00')
                for item_data in stock_in_data.items:
                    # 🔑 关键修复：跳过占位符值的验证
                    if item_data.jewelry_id is not None and item_data.jewelry_id != self.PLACEHOLDER_JEWELRY_ID:
                        # 只验证有效的jewelry_id
                        jewelry = self.db.query(Jewelry).filter(Jewelry.id == item_data.jewelry_id).first()
                        if not jewelry:
                            raise ValueError(f"首饰ID {item_data.jewelry_id} 不存在")

                    # 计算总金额（无论jewelry_id是否有效）
                    total_amount += item_data.total_cost

                # 创建新明细
                for item_data in stock_in_data.items:
                    # ✅ 修复：使用占位符值代替null，避免数据库约束错误
                    jewelry_id = item_data.jewelry_id if item_data.jewelry_id is not None else self.PLACEHOLDER_JEWELRY_ID

                    stock_in_item = StockInItem(
                        stock_in_id=stock_in_id,
                        jewelry_id=jewelry_id,  # 使用处理后的jewelry_id
                        barcode=item_data.barcode,
                        name=item_data.name,
                        category_id=item_data.category_id,
                        ring_size=item_data.ring_size,
                        gold_weight=item_data.gold_weight,
                        gold_price=item_data.gold_price,
                        gold_cost=item_data.gold_cost,
                        silver_weight=item_data.silver_weight,
                        total_weight=item_data.total_weight,
                        silver_price=item_data.silver_price,
                        silver_cost=item_data.silver_cost,
                        silver_work_type=item_data.silver_work_type,
                        silver_work_price=item_data.silver_work_price,
                        silver_work_cost=item_data.silver_work_cost,
                        plating_cost=item_data.plating_cost,
                        total_cost=item_data.total_cost,
                        wholesale_work_price=item_data.wholesale_work_price,
                        retail_work_price=item_data.retail_work_price,
                        piece_work_price=item_data.piece_work_price,
                        createtime=current_time
                    )
                    self.db.add(stock_in_item)

                stock_in.total_amount = total_amount

            stock_in.updatetime = current_time

            self.db.commit()
            self.db.refresh(stock_in)

            return stock_in

        except Exception as e:
            self.db.rollback()
            raise e

    def delete_stock_in(self, stock_in_id: int) -> bool:
        """删除入库单"""
        try:
            stock_in = self.db.query(StockIn).filter(StockIn.id == stock_in_id).first()
            if not stock_in:
                return False

            # 删除明细（由于设置了cascade，会自动删除）
            self.db.delete(stock_in)
            self.db.commit()

            return True

        except Exception as e:
            self.db.rollback()
            raise e

    def update_stock_in_status(self, stock_in_id: int, status_data: StockInStatusUpdate, auditor_id: int) -> Optional[StockIn]:
        """更新入库单状态"""
        print(f"🔍 [审核] 开始更新入库单状态")
        print(f"   入库单ID: {stock_in_id}")
        print(f"   审核员ID: {auditor_id}")
        status_names = {0: '草稿', 1: '待审核', 2: '已通过', 3: '已拒绝'}
        print(f"   新状态: {status_data.status} ({status_names.get(status_data.status, '未知')})")
        print(f"   审核备注: {status_data.audit_note}")

        try:
            # 查询入库单
            stock_in = self.db.query(StockIn).filter(StockIn.id == stock_in_id).first()
            if not stock_in:
                print(f"❌ [审核] 入库单不存在: ID={stock_in_id}")
                return None

            print(f"✅ [审核] 找到入库单: {stock_in.order_no}")
            print(f"   当前状态: {stock_in.status}")
            print(f"   当前审核员: {stock_in.audit_user_id}")

            # 状态流转验证
            self._validate_status_transition(stock_in.status, status_data.status)

            # ✅ 关键修复：审核通过时创建首饰记录
            if status_data.status == 2:  # 审核通过
                print(f"🎯 [审核通过] 开始创建首饰记录")
                self._create_jewelry_records_for_stock_in(stock_in)

            # 更新字段
            old_status = stock_in.status
            stock_in.status = status_data.status
            stock_in.audit_user_id = auditor_id
            stock_in.audit_time = int(time.time())
            if status_data.audit_note:
                stock_in.audit_note = status_data.audit_note
            stock_in.updatetime = int(time.time())

            # ✅ 修复：取消审核时删除首饰记录
            if old_status == 2 and status_data.status == 1:
                print(f"🔄 [取消审核] 执行取消审核操作")
                print(f"   入库单: {stock_in.order_no}")
                print(f"   状态变更: 已通过(2) -> 待审核(1)")
                print(f"   审核员: {auditor_id}")
                print(f"   取消原因: {status_data.audit_note}")
                self._remove_jewelry_records_for_stock_in(stock_in)

            print(f"🔄 [状态更新] 准备提交数据库事务...")
            print(f"   入库单ID: {stock_in_id}")
            print(f"   状态变更: {old_status} -> {status_data.status}")
            print(f"   审核员ID: {auditor_id}")
            print(f"   审核时间: {stock_in.audit_time}")
            print(f"   审核备注: {stock_in.audit_note}")

            print(f"📝 [审核] 更新字段:")
            print(f"   status: {old_status} → {stock_in.status}")
            print(f"   audit_user_id: {stock_in.audit_user_id}")
            print(f"   audit_time: {stock_in.audit_time}")
            print(f"   audit_note: {stock_in.audit_note}")

            # 提交事务
            self.db.commit()
            self.db.refresh(stock_in)

            print(f"✅ [审核] 数据库更新成功")
            print(f"   最终状态: {stock_in.status}")
            print(f"   最终审核员: {stock_in.audit_user_id}")

            return stock_in

        except Exception as e:
            print(f"❌ [审核] 数据库更新失败: {e}")
            import traceback
            traceback.print_exc()
            self.db.rollback()
            raise e

    def _validate_status_transition(self, current_status: int, new_status: int) -> None:
        """验证状态流转是否合法"""
        # 状态定义：0=草稿, 1=待审核, 2=已通过, 3=已拒绝

        # 合法的状态流转规则
        valid_transitions = {
            0: [1],           # 草稿 -> 待审核
            1: [2, 3],        # 待审核 -> 已通过/已拒绝
            2: [1],           # 已通过 -> 可取消审核回到待审核 ✅ 修复：允许取消审核
            3: [1],           # 已拒绝 -> 可重新提交审核
        }

        if new_status not in valid_transitions.get(current_status, []):
            status_names = {0: '草稿', 1: '待审核', 2: '已通过', 3: '已拒绝'}
            current_name = status_names.get(current_status, '未知')
            new_name = status_names.get(new_status, '未知')
            raise ValueError(f"不允许的状态流转: {current_name}({current_status}) -> {new_name}({new_status})")

        print(f"✅ [状态验证] 状态流转合法: {current_status} -> {new_status}")

    def _create_jewelry_records_for_stock_in(self, stock_in: StockIn) -> None:
        """为入库单创建首饰记录（审核通过时调用）"""
        print(f"💎 [创建首饰] 开始为入库单 {stock_in.order_no} 创建首饰记录")

        # 获取入库单明细
        items = self.db.query(StockInItem).filter(StockInItem.stock_in_id == stock_in.id).all()
        print(f"   找到 {len(items)} 个入库单明细")

        for i, item in enumerate(items, 1):
            print(f"💎 [创建首饰] 处理第{i}个明细: {item.name}")
            print(f"   条码: {item.barcode}")
            print(f"   分类ID: {item.category_id}")
            print(f"   金重: {item.gold_weight}g")
            print(f"   银重: {item.silver_weight}g")
            print(f"   总成本: {item.total_cost}元")
            print(f"   当前jewelry_id: {item.jewelry_id} {'(占位符)' if item.jewelry_id == self.PLACEHOLDER_JEWELRY_ID else '(实际值)'}")

            # ✅ 修复：只处理占位符值的明细
            if item.jewelry_id != self.PLACEHOLDER_JEWELRY_ID:
                print(f"ℹ️ [创建首饰] 第{i}个明细已有有效jewelry_id，跳过")
                continue

            # 检查条码是否已存在
            existing_jewelry = self.db.query(Jewelry).filter(Jewelry.barcode == item.barcode).first()
            if existing_jewelry:
                print(f"⚠️ [创建首饰] 条码 {item.barcode} 已存在，使用现有记录")
                # 更新入库单明细的jewelry_id
                item.jewelry_id = existing_jewelry.id
                continue

            # ✅ 修复：创建首饰记录，移除不存在的silver_work_cost字段
            jewelry = Jewelry(
                name=item.name,
                barcode=item.barcode,
                category_id=item.category_id,
                store_id=stock_in.store_id,
                ring_size=item.ring_size,
                gold_weight=item.gold_weight,
                gold_price=item.gold_price,
                gold_cost=item.gold_cost,
                silver_weight=item.silver_weight,
                total_weight=item.total_weight,
                silver_price=item.silver_price,
                silver_cost=item.silver_cost,
                silver_work_type=item.silver_work_type,
                silver_work_price=item.silver_work_price,
                # ❌ 移除：silver_work_cost字段在Jewelry模型中不存在
                plating_cost=item.plating_cost,
                total_cost=item.total_cost,
                wholesale_work_price=item.wholesale_work_price,
                retail_work_price=item.retail_work_price,
                piece_work_price=item.piece_work_price,
                status=1,  # 在库状态
                createtime=int(time.time()),
                updatetime=int(time.time())
            )

            self.db.add(jewelry)
            self.db.flush()  # 获取jewelry ID

            # 更新入库单明细的jewelry_id
            item.jewelry_id = jewelry.id

            print(f"✅ [创建首饰] 第{i}个首饰创建成功，ID: {jewelry.id}")

        print(f"💎 [创建首饰] 所有首饰记录创建完成")

    def _remove_jewelry_records_for_stock_in(self, stock_in: StockIn) -> None:
        """删除入库单对应的首饰记录（取消审核时调用）"""
        print(f"🗑️ [删除首饰] 开始删除入库单 {stock_in.order_no} 的首饰记录")

        # 获取入库单明细
        items = self.db.query(StockInItem).filter(StockInItem.stock_in_id == stock_in.id).all()
        print(f"   找到 {len(items)} 个入库单明细")

        for i, item in enumerate(items, 1):
            print(f"🗑️ [删除首饰] 处理第{i}个明细: {item.name} (首饰ID: {item.jewelry_id})")

            # ✅ 修复：只处理有效的jewelry_id（非占位符值）
            if item.jewelry_id and item.jewelry_id != self.PLACEHOLDER_JEWELRY_ID:
                # 检查首饰是否被其他入库单使用
                other_usage = self.db.query(StockInItem).filter(
                    StockInItem.jewelry_id == item.jewelry_id,
                    StockInItem.stock_in_id != stock_in.id,
                    StockInItem.jewelry_id != self.PLACEHOLDER_JEWELRY_ID  # ✅ 排除占位符值
                ).first()

                if other_usage:
                    print(f"⚠️ [删除首饰] 首饰ID {item.jewelry_id} 被其他入库单使用，不删除")
                else:
                    # 删除首饰记录
                    jewelry = self.db.query(Jewelry).filter(Jewelry.id == item.jewelry_id).first()
                    if jewelry:
                        self.db.delete(jewelry)
                        print(f"✅ [删除首饰] 首饰ID {item.jewelry_id} 删除成功")
                    else:
                        print(f"⚠️ [删除首饰] 首饰ID {item.jewelry_id} 不存在")

                # ✅ 修复：重置为占位符值而不是None
                item.jewelry_id = self.PLACEHOLDER_JEWELRY_ID
                print(f"🔄 [删除首饰] 第{i}个明细jewelry_id重置为占位符值: {self.PLACEHOLDER_JEWELRY_ID}")
            else:
                print(f"ℹ️ [删除首饰] 第{i}个明细jewelry_id为占位符值或无效，跳过")

        print(f"🗑️ [删除首饰] 首饰记录删除完成")

    def convert_stock_in_to_dict(self, stock_in: StockIn) -> dict:
        """将StockIn对象转换为包含关联名称的字典"""
        # 直接查询数据库获取明细数据，避免懒加载问题
        try:
            from ..models.jewelry import JewelryCategory

            # 先尝试简单查询，避免复杂的joinedload
            items_list = self.db.query(StockInItem).filter(
                StockInItem.stock_in_id == stock_in.id
            ).all()

        except Exception as e:
            import traceback
            traceback.print_exc()
            items_list = []

        # 转换商品明细数据
        items_data = []
        for item in items_list:
            try:
                # 获取分类名称
                category_name = None
                if item.jewelry and item.jewelry.category:
                    category_name = item.jewelry.category.name
                elif item.category_id:
                    # 如果jewelry没有加载category，手动查询
                    from ..models.jewelry import JewelryCategory
                    category = self.db.query(JewelryCategory).filter(
                        JewelryCategory.id == item.category_id
                    ).first()
                    if category:
                        category_name = category.name

                item_dict = {
                    'id': item.id,
                    'stock_in_id': item.stock_in_id,
                    'jewelry_id': item.jewelry_id,
                    'barcode': item.barcode,
                    'name': item.name,
                    'category_id': item.category_id,
                    'category_name': category_name,
                    'ring_size': item.ring_size,
                    'gold_weight': float(item.gold_weight) if item.gold_weight else 0.0,
                    'gold_price': float(item.gold_price) if item.gold_price else 0.0,
                    'gold_cost': float(item.gold_cost) if item.gold_cost else 0.0,
                    'silver_weight': float(item.silver_weight) if item.silver_weight else 0.0,
                    'total_weight': float(item.total_weight) if item.total_weight else 0.0,
                    'silver_price': float(item.silver_price) if item.silver_price else 0.0,
                    'silver_cost': float(item.silver_cost) if item.silver_cost else 0.0,
                    'silver_work_type': item.silver_work_type,
                    'silver_work_price': float(item.silver_work_price) if item.silver_work_price else 0.0,
                    'silver_work_cost': float(item.silver_work_cost) if item.silver_work_cost else 0.0,
                    'plating_cost': float(item.plating_cost) if item.plating_cost else 0.0,
                    'total_cost': float(item.total_cost) if item.total_cost else 0.0,
                    'wholesale_work_price': float(item.wholesale_work_price) if item.wholesale_work_price else 0.0,
                    'retail_work_price': float(item.retail_work_price) if item.retail_work_price else 0.0,
                    'piece_work_price': float(item.piece_work_price) if item.piece_work_price else 0.0,
                    'createtime': item.createtime,
                }
                items_data.append(item_dict)
                print(f"     转换明细[{len(items_data)}]: {item.name} (ID={item.id})")
            except Exception as e:
                print(f"   ❌ 转换明细失败: {e}")
                continue

        result = {
            'id': stock_in.id,
            'order_no': stock_in.order_no,
            'store_id': stock_in.store_id,
            'supplier': stock_in.supplier,
            'total_amount': float(stock_in.total_amount) if stock_in.total_amount else 0.0,
            'remark': stock_in.remark,
            'operator_id': stock_in.operator_id,
            'audit_user_id': stock_in.audit_user_id,
            'audit_time': stock_in.audit_time,
            'audit_note': stock_in.audit_note,
            'status': stock_in.status,
            'createtime': stock_in.createtime,
            'updatetime': stock_in.updatetime,
            'store_name': stock_in.store.name if stock_in.store else None,
            'operator_name': stock_in.operator.nickname if stock_in.operator else None,
            'auditor_name': stock_in.auditor.nickname if stock_in.auditor else None,
            'item_count': len(items_data),  # 商品件数
            'items': items_data,  # 添加完整的商品明细数据
        }

        return result

    def get_stock_in_statistics(self) -> StockInStatistics:
        """获取入库单统计信息"""
        # 基础统计
        total_count = self.db.query(StockIn).count()
        normal_count = self.db.query(StockIn).filter(StockIn.status == 2).count()  # ✅ 修复：已通过状态
        disabled_count = self.db.query(StockIn).filter(StockIn.status == 0).count()  # 草稿状态

        # 总金额统计
        total_amount_result = self.db.query(func.sum(StockIn.total_amount)).scalar()
        total_amount = total_amount_result or Decimal('0.00')

        # 门店分布统计
        store_stats = self.db.query(
            Store.id,
            Store.name,
            func.count(StockIn.id).label('stock_in_count'),
            func.sum(StockIn.total_amount).label('total_amount')
        ).outerjoin(StockIn).group_by(Store.id, Store.name).all()

        store_distribution = []
        for stat in store_stats:
            store_distribution.append({
                'store_id': stat.id,
                'store_name': stat.name,
                'stock_in_count': stat.stock_in_count or 0,
                'total_amount': float(stat.total_amount or 0)
            })

        return StockInStatistics(
            total_count=total_count,
            normal_count=normal_count,
            disabled_count=disabled_count,
            total_amount=total_amount,
            store_distribution=store_distribution
        )