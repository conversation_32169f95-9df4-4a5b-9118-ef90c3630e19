import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../widgets/loading_widget.dart';
import '../controllers/settings_controller.dart';

import 'tabs/general_settings_tab.dart';
import 'tabs/user_management_tab.dart';
import 'tabs/role_permission_tab.dart';
import 'tabs/backup_restore_tab.dart';
import 'tabs/system_log_tab.dart';

/// 系统设置页面
class SettingsPage extends StatelessWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    // 获取控制器
    final controller = Get.find<SettingsController>();
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('系统设置'),
        bottom: TabBar(
          controller: controller.tabController,
          isScrollable: true,
          tabs: const [
            Tab(
              icon: Icon(Icons.settings),
              text: '基本设置',
            ),
            Tab(
              icon: Icon(Icons.people),
              text: '用户管理',
            ),
            Tab(
              icon: Icon(Icons.security),
              text: '角色权限',
            ),
            Tab(
              icon: Icon(Icons.backup),
              text: '备份恢复',
            ),
            Tab(
              icon: Icon(Icons.history),
              text: '系统日志',
            ),
          ],
        ),
      ),
      body: Obx(() {
        return controller.isLoading.value
            ? const LoadingWidget()
            : TabBarView(
                controller: controller.tabController,
                children: const [
                  GeneralSettingsTab(),
                  UserManagementTab(),
                  RolePermissionTab(),
                  BackupRestoreTab(),
                  SystemLogTab(),
                ],
              );
      }),
    );
  }
}

/// 系统设置绑定
class SettingsBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<SettingsController>(() => SettingsController());
  }
} 