/// 完成处理对话框
/// 用于录入处理结果

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../../../core/constants/border_styles.dart';
import '../../../core/widgets/custom_card.dart';
import '../../../core/widgets/custom_text_field.dart';
import '../../../models/recycling/recycling_process_new.dart';
import '../../../core/utils/number_formatter.dart';

class ProcessCompleteDialog extends StatefulWidget {
  final RecyclingProcessNew process;
  final Function(List<ProcessResult>) onConfirm;

  const ProcessCompleteDialog({
    Key? key,
    required this.process,
    required this.onConfirm,
  }) : super(key: key);

  @override
  State<ProcessCompleteDialog> createState() => _ProcessCompleteDialogState();
}

class _ProcessCompleteDialogState extends State<ProcessCompleteDialog> {
  final List<_ResultFormData> results = [];
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    // 根据处理类型初始化默认结果
    _initDefaultResults();
  }

  void _initDefaultResults() {
    switch (widget.process.processType) {
      case ProcessType.separation:
        // 金银分离默认创建金和银两个结果
        if (widget.process.estimatedGoldWeight > 0) {
          results.add(_ResultFormData(
            type: ResultType.pureGold,
            name: '纯金',
            weight: widget.process.estimatedGoldWeight,
            purity: 99.9,
          ));
        }
        if (widget.process.estimatedSilverWeight > 0) {
          results.add(_ResultFormData(
            type: ResultType.pureSilver,
            name: '纯银',
            weight: widget.process.estimatedSilverWeight,
            purity: 99.9,
          ));
        }
        break;
      case ProcessType.refurbish:
        // 翻新加工默认创建成品首饰
        results.add(_ResultFormData(
          type: ResultType.finishedJewelry,
          name: '翻新首饰',
          weight: widget.process.totalWeight,
        ));
        break;
      case ProcessType.melt:
        // 熔炼默认创建金银锭
        results.add(_ResultFormData(
          type: ResultType.bullion,
          name: '金银锭',
          weight: widget.process.totalWeight,
          purity: 99.9,
        ));
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
      ),
      child: Container(
        width: 900,
        constraints: const BoxConstraints(maxHeight: 700),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildHeader(),
              Flexible(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildProcessInfo(),
                      const SizedBox(height: 24),
                      _buildResultsSection(),
                    ],
                  ),
                ),
              ),
              _buildFooter(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green.shade50,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(AppBorderStyles.borderRadius),
          topRight: Radius.circular(AppBorderStyles.borderRadius),
        ),
      ),
      child: Row(
        children: [
          Icon(Icons.check_circle, color: Colors.green.shade700),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              '完成处理 - ${widget.process.processNo}',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.green.shade700,
              ),
            ),
          ),
          IconButton(
            onPressed: () => Get.back(),
            icon: const Icon(Icons.close),
            tooltip: '关闭',
          ),
        ],
      ),
    );
  }

  Widget _buildProcessInfo() {
    return CustomCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '工单信息',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 24,
              runSpacing: 8,
              children: [
                _buildInfo('处理类型', widget.process.processType.label),
                _buildInfo('总重量', '${widget.process.totalWeight.toStringAsFixed(2)} g'),
                _buildInfo('预估金重', '${widget.process.estimatedGoldWeight.toStringAsFixed(2)} g'),
                _buildInfo('预估银重', '${widget.process.estimatedSilverWeight.toStringAsFixed(2)} g'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfo(String label, String value) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          '$label：',
          style: const TextStyle(color: Colors.grey),
        ),
        Text(
          value,
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
      ],
    );
  }

  Widget _buildResultsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              '处理结果',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton.icon(
              onPressed: _addResult,
              icon: const Icon(Icons.add),
              label: const Text('添加结果'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        ...results.asMap().entries.map((entry) => _buildResultForm(entry.key, entry.value)),
        if (results.isNotEmpty) ...[
          const SizedBox(height: 16),
          _buildSummary(),
        ],
      ],
    );
  }

  Widget _buildResultForm(int index, _ResultFormData data) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: CustomCard(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '结果 ${index + 1}',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (results.length > 1)
                  IconButton(
                    onPressed: () {
                      setState(() {
                        results.removeAt(index);
                      });
                    },
                    icon: const Icon(Icons.delete, color: Colors.red),
                    tooltip: '删除',
                  ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<ResultType>(
                    value: data.type,
                    decoration: const InputDecoration(
                      labelText: '结果类型',
                      border: OutlineInputBorder(),
                    ),
                    items: ResultType.values.map((type) => DropdownMenuItem(
                      value: type,
                      child: Text(type.label),
                    )).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          data.type = value;
                          // 根据类型自动设置名称
                          data.nameController.text = value.label;
                        });
                      }
                    },
                    validator: (value) => value == null ? '请选择结果类型' : null,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: CustomTextField(
                    controller: data.nameController,
                    label: '产品名称',
                    validator: (value) => value?.isEmpty ?? true ? '请输入产品名称' : null,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: CustomTextField(
                    controller: data.weightController,
                    label: '重量(g)',
                    keyboardType: const TextInputType.numberWithOptions(decimal: true),
                    inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}'))],
                    validator: (value) {
                      if (value?.isEmpty ?? true) return '请输入重量';
                      final weight = double.tryParse(value!);
                      if (weight == null || weight <= 0) return '请输入有效的重量';
                      return null;
                    },
                    onChanged: (_) => setState(() {}),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: CustomTextField(
                    controller: data.purityController,
                    label: '纯度(%)',
                    keyboardType: const TextInputType.numberWithOptions(decimal: true),
                    inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^(100(\.0?)?|[1-9]?\d(\.\d)?)$'))],
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: CustomTextField(
                    controller: data.lossWeightController,
                    label: '损耗重量(g)',
                    keyboardType: const TextInputType.numberWithOptions(decimal: true),
                    inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}'))],
                    onChanged: (_) => setState(() {}),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: CustomTextField(
                    controller: data.processCostController,
                    label: '加工成本(¥)',
                    keyboardType: const TextInputType.numberWithOptions(decimal: true),
                    inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}'))],
                    onChanged: (_) => setState(() {}),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: CustomTextField(
                    controller: data.laborCostController,
                    label: '人工成本(¥)',
                    keyboardType: const TextInputType.numberWithOptions(decimal: true),
                    inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}'))],
                    onChanged: (_) => setState(() {}),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: CustomTextField(
                    controller: data.otherCostController,
                    label: '其他成本(¥)',
                    keyboardType: const TextInputType.numberWithOptions(decimal: true),
                    inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}'))],
                    onChanged: (_) => setState(() {}),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '损耗率：${data.lossRate.toStringAsFixed(1)}%',
                  style: const TextStyle(color: Colors.grey),
                ),
                Text(
                  '总成本：¥${data.totalCost.toStringAsFixed(2)}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.orange,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
      ),
    );
  }

  Widget _buildSummary() {
    final totalWeight = results.fold<double>(
      0,
      (sum, result) => sum + result.weight,
    );
    final totalLoss = results.fold<double>(
      0,
      (sum, result) => sum + result.lossWeight,
    );
    final totalCost = results.fold<double>(
      0,
      (sum, result) => sum + result.totalCost,
    );

    return CustomCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '汇总信息',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildSummaryItem('输入总重', '${widget.process.totalWeight.toStringAsFixed(2)} g'),
                _buildSummaryItem('产出总重', '${totalWeight.toStringAsFixed(2)} g'),
                _buildSummaryItem('损耗总重', '${totalLoss.toStringAsFixed(2)} g'),
                _buildSummaryItem('总成本', '¥${totalCost.toStringAsFixed(2)}'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value) {
    return Column(
      children: [
        Text(
          label,
          style: const TextStyle(
            color: Colors.grey,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildFooter() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(AppBorderStyles.borderRadius),
          bottomRight: Radius.circular(AppBorderStyles.borderRadius),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          ElevatedButton(
            onPressed: () => Get.back(),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.grey,
            ),
            child: const Text('取消'),
          ),
          const SizedBox(width: 16),
          ElevatedButton(
            onPressed: _onConfirm,
            child: const Text('确认完成'),
          ),
        ],
      ),
    );
  }

  void _addResult() {
    setState(() {
      results.add(_ResultFormData(
        type: ResultType.pureGold,
        name: '',
        weight: 0,
      ));
    });
  }

  void _onConfirm() {
    if (!_formKey.currentState!.validate()) return;

    if (results.isEmpty) {
      Get.snackbar('提示', '请至少添加一个处理结果');
      return;
    }

    final processResults = results.map((data) => ProcessResult(
      id: 0,
      processId: widget.process.id,
      resultType: data.type,
      name: data.nameController.text,
      weight: data.weight,
      purity: data.purity > 0 ? data.purity : null,
      lossWeight: data.lossWeight,
      lossRate: data.lossRate,
      processCost: data.processCost,
      laborCost: data.laborCost,
      otherCost: data.otherCost,
      totalCost: data.totalCost,
      isConverted: false,
      createTime: DateTime.now(),
    )).toList();

    widget.onConfirm(processResults);
  }
}

class _ResultFormData {
  ResultType type;
  final String name;

  late final TextEditingController nameController;
  late final TextEditingController weightController;
  late final TextEditingController purityController;
  late final TextEditingController lossWeightController;
  late final TextEditingController processCostController;
  late final TextEditingController laborCostController;
  late final TextEditingController otherCostController;

  _ResultFormData({
    required this.type,
    required this.name,
    double? weight,
    double? purity,
  }) {
    nameController = TextEditingController(text: name);
    weightController = TextEditingController(text: weight != null && weight > 0 ? weight.toString() : '');
    purityController = TextEditingController(text: purity?.toString() ?? '');
    lossWeightController = TextEditingController(text: '0');
    processCostController = TextEditingController(text: '0');
    laborCostController = TextEditingController(text: '0');
    otherCostController = TextEditingController(text: '0');
  }

  double get weight => double.tryParse(weightController.text) ?? 0;
  double get purity => double.tryParse(purityController.text) ?? 0;
  double get lossWeight => double.tryParse(lossWeightController.text) ?? 0;
  double get processCost => double.tryParse(processCostController.text) ?? 0;
  double get laborCost => double.tryParse(laborCostController.text) ?? 0;
  double get otherCost => double.tryParse(otherCostController.text) ?? 0;

  double get lossRate {
    if (weight <= 0) return 0;
    return (lossWeight / weight) * 100;
  }

  double get totalCost => processCost + laborCost + otherCost;
}