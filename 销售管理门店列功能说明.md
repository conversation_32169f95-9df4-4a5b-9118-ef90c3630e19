# 销售管理页面门店列功能实现说明

## 📋 功能概述

为GoldManager销售管理页面的主要数据表格添加了新的"门店"列，支持以下功能：

### ✅ 已实现功能

1. **普通销售显示**：显示销售门店名称（如："门店A"）
2. **调拨数据显示**：使用箭头格式显示调拨路径："源门店->目标门店"（如："门店A->门店B"）
3. **数据源处理**：
   - 普通销售：从销售记录的store_name字段获取门店信息
   - 调拨记录：从customer字段解析目标门店信息，结合store_name显示完整调拨路径
4. **UI一致性**：保持与现有表格列的视觉一致性，使用统一的AppBorderStyles边框样式

## 🔧 技术实现

### 1. 数据模型修改

**文件**: `gold_manager_flutter/lib/features/sales/models/sales_item_detail.dart`

#### 新增方法：`formattedStoreName`
```dart
/// 获取格式化的门店显示文本
/// 普通销售：显示门店名称
/// 调拨数据：显示"源门店->目标门店"格式
String get formattedStoreName {
  if (salesType == 'transfer') {
    // 调拨数据：从customer字段提取目标门店信息
    if (additionalInfo != null && additionalInfo!.containsKey('target_store_name')) {
      final targetStore = additionalInfo!['target_store_name'];
      return '$storeName->$targetStore';
    } else {
      // 从customer字段解析目标门店（格式："调拨至 门店名"）
      final customerText = additionalInfo?['customer'] ?? '';
      if (customerText.toString().startsWith('调拨至 ')) {
        final targetStore = customerText.toString().substring(4); // 移除"调拨至 "前缀
        return '$storeName->$targetStore';
      }
    }
    return '$storeName->未知门店';
  } else {
    // 普通销售：直接显示门店名称
    return storeName;
  }
}
```

#### 修改fromJson方法
增强了对调拨数据的处理，将customer信息添加到additionalInfo中以便门店信息解析。

### 2. UI界面修改

**文件**: `gold_manager_flutter/lib/features/sales/views/sales_management_view.dart`

#### 列宽重新分配
```dart
// 优化列宽分配 - 添加门店列
final orderNoWidth = availableWidth * 0.13;
final typeWidth = availableWidth * 0.08;
final codeWidth = availableWidth * 0.10;
final nameWidth = availableWidth * 0.18;
final storeWidth = availableWidth * 0.15; // 新增门店列
final priceWidth = availableWidth * 0.10;
final costWidth = availableWidth * 0.09;
final profitWidth = availableWidth * 0.09;
final actionWidth = availableWidth * 0.08;
```

#### 新增门店列定义
```dart
DataColumn(
  label: Container(
    width: storeWidth,
    padding: const EdgeInsets.symmetric(horizontal: 8),
    child: const Text('门店', textAlign: TextAlign.center),
  ),
),
```

#### 新增门店数据单元格
```dart
DataCell(
  Container(
    width: storeWidth,
    height: 48,
    padding: const EdgeInsets.symmetric(horizontal: 8),
    alignment: Alignment.center,
    child: Text(
      item.formattedStoreName,
      style: TextStyle(
        fontWeight: FontWeight.w500,
        color: item.salesType == 'transfer' 
            ? Colors.blue[700] 
            : Colors.grey[800],
      ),
      overflow: TextOverflow.ellipsis,
      maxLines: 1,
    ),
  ),
),
```

## 🎨 UI设计特点

### 视觉区分
- **普通销售**：使用灰色文字显示门店名称
- **调拨数据**：使用蓝色文字显示调拨路径，便于用户快速识别

### 响应式设计
- 门店列占用15%的可用宽度
- 支持文本溢出省略显示
- 保持与其他列的高度和边距一致

### 边框样式
- 使用统一的AppBorderStyles设计系统
- 保持表格整体视觉一致性

## 📊 数据显示示例

### 普通销售记录
```
门店A
```

### 调拨记录
```
门店A->门店B
```

### 异常情况处理
```
门店A->未知门店  (当无法解析目标门店时)
```

## 🔄 后端数据结构

### 普通销售数据
- `store_name`: 销售门店名称
- `sales_type`: "retail" 或 "wholesale"

### 调拨数据
- `store_name`: 源门店名称
- `customer`: "调拨至 目标门店名称"
- `sales_type`: "transfer"

## ✅ 完成状态

- [x] 数据模型扩展
- [x] UI界面修改
- [x] 门店信息格式化逻辑
- [x] 调拨路径显示
- [x] 视觉样式统一
- [x] 响应式布局适配

## 🚀 使用说明

1. 打开销售管理页面
2. 在数据表格中可以看到新增的"门店"列
3. 普通销售记录显示门店名称
4. 调拨记录显示"源门店->目标门店"格式
5. 调拨记录使用蓝色文字便于识别

该功能已完全集成到现有系统中，保持了代码的一致性和可维护性。
