import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// 价格输入字段
///
/// 按照统一边框调用方式文档标准实现的价格输入组件
class PriceInputField extends StatelessWidget {
  final String label;
  final double value;
  final ValueChanged<double> onChanged;
  final bool readOnly;

  const PriceInputField({
    super.key,
    required this.label,
    required this.value,
    required this.onChanged,
    this.readOnly = false,
  });

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      decoration: InputDecoration(
        labelText: label,
        border: const OutlineInputBorder(
          borderRadius: BorderRadius.all(Radius.circular(12)),
        ),
        prefixText: '¥',
      ),
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      inputFormatters: [
        // 按照文档标准：允许数字和小数点输入，支持多位数字
        FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
        // 防止多个小数点，限制小数位数为2位
        TextInputFormatter.withFunction((oldValue, newValue) {
          final text = newValue.text;
          if (text.isEmpty) return newValue;

          // 检查小数点数量
          final dotCount = text.split('.').length - 1;
          if (dotCount > 1) {
            return oldValue;
          }

          // 检查小数位数（最多2位）
          if (text.contains('.')) {
            final parts = text.split('.');
            if (parts.length > 1 && parts[1].length > 2) {
              return oldValue;
            }
          }

          // 检查是否为有效数字格式
          if (double.tryParse(text) == null && text != '.') {
            return oldValue;
          }

          return newValue;
        }),
      ],
      initialValue: value.toString(),
      onChanged: (value) {
        if (value.isEmpty) {
          onChanged(0.0);
        } else {
          onChanged(double.tryParse(value) ?? 0.0);
        }
      },
      readOnly: readOnly,
    );
  }
}