-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- 主机： 127.0.0.1
-- 生成日期： 2025-06-15 14:39:02
-- 服务器版本： 10.4.32-MariaDB
-- PHP 版本： 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `shgsgueobavnspo`
--

-- --------------------------------------------------------

--
-- 表的结构 `fa_admin`
--

CREATE TABLE `fa_admin` (
  `id` int(10) UNSIGNED NOT NULL COMMENT 'ID',
  `username` varchar(20) DEFAULT '' COMMENT '用户名',
  `nickname` varchar(50) DEFAULT '' COMMENT '昵称',
  `password` varchar(32) DEFAULT '' COMMENT '密码',
  `salt` varchar(30) DEFAULT '' COMMENT '密码盐',
  `avatar` varchar(255) DEFAULT '' COMMENT '头像',
  `email` varchar(100) DEFAULT '' COMMENT '电子邮箱',
  `mobile` varchar(11) DEFAULT '' COMMENT '手机号码',
  `loginfailure` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '失败次数',
  `logintime` bigint(16) DEFAULT NULL COMMENT '登录时间',
  `loginip` varchar(50) DEFAULT NULL COMMENT '登录IP',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  `token` varchar(59) DEFAULT '' COMMENT 'Session标识',
  `status` varchar(30) NOT NULL DEFAULT 'normal' COMMENT '状态',
  `store_id` int(11) DEFAULT NULL COMMENT '所属门店ID'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='管理员表';

-- --------------------------------------------------------

--
-- 表的结构 `fa_admin_log`
--

CREATE TABLE `fa_admin_log` (
  `id` int(10) UNSIGNED NOT NULL COMMENT 'ID',
  `admin_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '管理员ID',
  `username` varchar(30) DEFAULT '' COMMENT '管理员名字',
  `url` varchar(1500) DEFAULT '' COMMENT '操作页面',
  `title` varchar(100) DEFAULT '' COMMENT '日志标题',
  `content` longtext NOT NULL COMMENT '内容',
  `ip` varchar(50) DEFAULT '' COMMENT 'IP',
  `useragent` varchar(255) DEFAULT '' COMMENT 'User-Agent',
  `createtime` bigint(16) DEFAULT NULL COMMENT '操作时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='管理员日志表';

-- --------------------------------------------------------

--
-- 表的结构 `fa_area`
--

CREATE TABLE `fa_area` (
  `id` int(10) NOT NULL COMMENT 'ID',
  `pid` int(10) DEFAULT NULL COMMENT '父id',
  `shortname` varchar(100) DEFAULT NULL COMMENT '简称',
  `name` varchar(100) DEFAULT NULL COMMENT '名称',
  `mergename` varchar(255) DEFAULT NULL COMMENT '全称',
  `level` tinyint(4) DEFAULT NULL COMMENT '层级:1=省,2=市,3=区/县',
  `pinyin` varchar(100) DEFAULT NULL COMMENT '拼音',
  `code` varchar(100) DEFAULT NULL COMMENT '长途区号',
  `zip` varchar(100) DEFAULT NULL COMMENT '邮编',
  `first` varchar(50) DEFAULT NULL COMMENT '首字母',
  `lng` varchar(100) DEFAULT NULL COMMENT '经度',
  `lat` varchar(100) DEFAULT NULL COMMENT '纬度'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='地区表';

-- --------------------------------------------------------

--
-- 表的结构 `fa_attachment`
--

CREATE TABLE `fa_attachment` (
  `id` int(20) UNSIGNED NOT NULL COMMENT 'ID',
  `category` varchar(50) DEFAULT '' COMMENT '类别',
  `admin_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '管理员ID',
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '会员ID',
  `url` varchar(255) DEFAULT '' COMMENT '物理路径',
  `imagewidth` int(10) UNSIGNED DEFAULT 0 COMMENT '宽度',
  `imageheight` int(10) UNSIGNED DEFAULT 0 COMMENT '高度',
  `imagetype` varchar(30) DEFAULT '' COMMENT '图片类型',
  `imageframes` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '图片帧数',
  `filename` varchar(100) DEFAULT '' COMMENT '文件名称',
  `filesize` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '文件大小',
  `mimetype` varchar(100) DEFAULT '' COMMENT 'mime类型',
  `extparam` varchar(255) DEFAULT '' COMMENT '透传数据',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建日期',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  `uploadtime` bigint(16) DEFAULT NULL COMMENT '上传时间',
  `storage` varchar(100) NOT NULL DEFAULT 'local' COMMENT '存储位置',
  `sha1` varchar(40) DEFAULT '' COMMENT '文件 sha1编码'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='附件表';

-- --------------------------------------------------------

--
-- 表的结构 `fa_auth_group`
--

CREATE TABLE `fa_auth_group` (
  `id` int(10) UNSIGNED NOT NULL,
  `pid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '父组别',
  `name` varchar(100) DEFAULT '' COMMENT '组名',
  `rules` text NOT NULL COMMENT '规则ID',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  `status` varchar(30) DEFAULT '' COMMENT '状态'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='分组表';

-- --------------------------------------------------------

--
-- 表的结构 `fa_auth_group_access`
--

CREATE TABLE `fa_auth_group_access` (
  `uid` int(10) UNSIGNED NOT NULL COMMENT '会员ID',
  `group_id` int(10) UNSIGNED NOT NULL COMMENT '级别ID'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='权限分组表';

-- --------------------------------------------------------

--
-- 表的结构 `fa_auth_rule`
--

CREATE TABLE `fa_auth_rule` (
  `id` int(10) UNSIGNED NOT NULL,
  `type` enum('menu','file') NOT NULL DEFAULT 'file' COMMENT 'menu为菜单,file为权限节点',
  `pid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '父ID',
  `name` varchar(100) DEFAULT '' COMMENT '规则名称',
  `title` varchar(50) DEFAULT '' COMMENT '规则名称',
  `icon` varchar(50) DEFAULT '' COMMENT '图标',
  `url` varchar(255) DEFAULT '' COMMENT '规则URL',
  `condition` varchar(255) DEFAULT '' COMMENT '条件',
  `remark` varchar(255) DEFAULT '' COMMENT '备注',
  `ismenu` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否为菜单',
  `menutype` enum('addtabs','blank','dialog','ajax') DEFAULT NULL COMMENT '菜单类型',
  `extend` varchar(255) DEFAULT '' COMMENT '扩展属性',
  `py` varchar(30) DEFAULT '' COMMENT '拼音首字母',
  `pinyin` varchar(100) DEFAULT '' COMMENT '拼音',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  `weigh` int(10) NOT NULL DEFAULT 0 COMMENT '权重',
  `status` varchar(30) DEFAULT '' COMMENT '状态'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='节点表';

-- --------------------------------------------------------

--
-- 表的结构 `fa_category`
--

CREATE TABLE `fa_category` (
  `id` int(10) UNSIGNED NOT NULL,
  `pid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '父ID',
  `type` varchar(30) DEFAULT '' COMMENT '栏目类型',
  `name` varchar(30) DEFAULT '',
  `nickname` varchar(50) DEFAULT '',
  `flag` set('hot','index','recommend') DEFAULT '',
  `image` varchar(100) DEFAULT '' COMMENT '图片',
  `keywords` varchar(255) DEFAULT '' COMMENT '关键字',
  `description` varchar(255) DEFAULT '' COMMENT '描述',
  `diyname` varchar(30) DEFAULT '' COMMENT '自定义名称',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  `weigh` int(10) NOT NULL DEFAULT 0 COMMENT '权重',
  `status` varchar(30) DEFAULT '' COMMENT '状态'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='分类表';

-- --------------------------------------------------------

--
-- 表的结构 `fa_config`
--

CREATE TABLE `fa_config` (
  `id` int(10) UNSIGNED NOT NULL,
  `name` varchar(30) DEFAULT '' COMMENT '变量名',
  `group` varchar(30) DEFAULT '' COMMENT '分组',
  `title` varchar(100) DEFAULT '' COMMENT '变量标题',
  `tip` varchar(100) DEFAULT '' COMMENT '变量描述',
  `type` varchar(30) DEFAULT '' COMMENT '类型:string,text,int,bool,array,datetime,date,file',
  `visible` varchar(255) DEFAULT '' COMMENT '可见条件',
  `value` text DEFAULT NULL COMMENT '变量值',
  `content` text DEFAULT NULL COMMENT '变量字典数据',
  `rule` varchar(100) DEFAULT '' COMMENT '验证规则',
  `extend` varchar(255) DEFAULT '' COMMENT '扩展属性',
  `setting` varchar(255) DEFAULT '' COMMENT '配置'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='系统配置';

-- --------------------------------------------------------

--
-- 表的结构 `fa_ems`
--

CREATE TABLE `fa_ems` (
  `id` int(10) UNSIGNED NOT NULL COMMENT 'ID',
  `event` varchar(30) DEFAULT '' COMMENT '事件',
  `email` varchar(100) DEFAULT '' COMMENT '邮箱',
  `code` varchar(10) DEFAULT '' COMMENT '验证码',
  `times` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '验证次数',
  `ip` varchar(30) DEFAULT '' COMMENT 'IP',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='邮箱验证码表';

-- --------------------------------------------------------

--
-- 表的结构 `fa_group_images`
--

CREATE TABLE `fa_group_images` (
  `id` int(11) NOT NULL,
  `group_id` int(11) NOT NULL COMMENT '分组ID',
  `image_url` varchar(255) NOT NULL COMMENT '图片URL',
  `is_main` tinyint(1) DEFAULT 0 COMMENT '是否主图',
  `createtime` int(10) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) DEFAULT NULL COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='分组图片表';

-- --------------------------------------------------------

--
-- 表的结构 `fa_inventory_check`
--

CREATE TABLE `fa_inventory_check` (
  `id` int(11) NOT NULL,
  `check_no` varchar(50) NOT NULL COMMENT '盘点单号',
  `store_id` int(11) NOT NULL COMMENT '盘点门店',
  `status` tinyint(1) DEFAULT 0 COMMENT '状态:0=进行中,1=已完成,2=已取消',
  `start_time` int(10) DEFAULT NULL COMMENT '开始时间',
  `end_time` int(10) DEFAULT NULL COMMENT '结束时间',
  `operator_id` int(11) NOT NULL COMMENT '操作员ID',
  `total_count` int(11) DEFAULT 0 COMMENT '应盘总数',
  `checked_count` int(11) DEFAULT 0 COMMENT '已盘数量',
  `difference_count` int(11) DEFAULT 0 COMMENT '差异数量',
  `remark` text DEFAULT NULL COMMENT '备注',
  `createtime` int(10) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) DEFAULT NULL COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='库存盘点主表';

-- --------------------------------------------------------

--
-- 表的结构 `fa_inventory_check_item`
--

CREATE TABLE `fa_inventory_check_item` (
  `id` int(11) NOT NULL,
  `check_id` int(11) NOT NULL COMMENT '盘点单ID',
  `jewelry_id` int(11) NOT NULL COMMENT '商品ID',
  `barcode` varchar(50) NOT NULL COMMENT '条码',
  `name` varchar(100) NOT NULL COMMENT '商品名称',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `ring_size` varchar(20) DEFAULT NULL COMMENT '圈口号',
  `status` tinyint(1) DEFAULT 0 COMMENT '状态:0=未盘点,1=已盘点',
  `system_stock` int(11) DEFAULT 1 COMMENT '系统库存',
  `actual_stock` int(11) DEFAULT NULL COMMENT '实际库存',
  `difference` int(11) DEFAULT NULL COMMENT '差异',
  `check_time` int(10) DEFAULT NULL COMMENT '盘点时间',
  `check_user_id` int(11) DEFAULT NULL COMMENT '盘点人员ID',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `createtime` int(10) DEFAULT NULL COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='库存盘点明细表';

-- --------------------------------------------------------

--
-- 表的结构 `fa_jewelry`
--

CREATE TABLE `fa_jewelry` (
  `id` int(11) NOT NULL,
  `barcode` varchar(50) NOT NULL COMMENT '条码',
  `name` varchar(100) NOT NULL COMMENT '商品名称',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `ring_size` varchar(20) DEFAULT NULL COMMENT '圈口号',
  `gold_weight` decimal(10,2) DEFAULT 0.00 COMMENT '金重(克)',
  `gold_price` decimal(10,2) DEFAULT 0.00 COMMENT '金进价(克价)',
  `gold_cost` decimal(10,2) DEFAULT 0.00 COMMENT '金成本(金重*金进价)',
  `silver_weight` decimal(10,2) DEFAULT 0.00 COMMENT '银重(克)',
  `total_weight` decimal(10,2) DEFAULT 0.00 COMMENT '总重',
  `silver_price` decimal(10,2) DEFAULT 0.00 COMMENT '银进价(克价)',
  `silver_cost` decimal(10,2) DEFAULT 0.00 COMMENT '银成本(银重*银进价)',
  `silver_work_type` tinyint(1) DEFAULT 0 COMMENT '银工费方式:0=按克,1=按件',
  `silver_work_price` decimal(10,2) DEFAULT 0.00 COMMENT '银工费(按克为克价,按件为件价)',
  `plating_cost` decimal(10,2) DEFAULT 0.00 COMMENT '电铸费',
  `total_cost` decimal(10,2) DEFAULT 0.00 COMMENT '进货总成本',
  `wholesale_work_price` decimal(10,2) DEFAULT 0.00 COMMENT '批发工费',
  `retail_work_price` decimal(10,2) DEFAULT 0.00 COMMENT '零售工费',
  `piece_work_price` decimal(10,2) DEFAULT 0.00 COMMENT '件工费',
  `store_id` int(11) NOT NULL COMMENT '所属门店',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态:0=下架,1=上架,2=待出库',
  `createtime` int(10) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) DEFAULT NULL COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品表';

-- --------------------------------------------------------

--
-- 表的结构 `fa_jewelry_category`
--

CREATE TABLE `fa_jewelry_category` (
  `id` int(11) NOT NULL,
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  `pid` int(11) DEFAULT 0 COMMENT '父级ID',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态:0=禁用,1=正常',
  `weigh` int(11) DEFAULT 0 COMMENT '权重',
  `createtime` int(10) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) DEFAULT NULL COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品分类表';

-- --------------------------------------------------------

--
-- 表的结构 `fa_jewelry_group`
--

CREATE TABLE `fa_jewelry_group` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL COMMENT '分组名称',
  `category_id` int(11) DEFAULT NULL COMMENT '商品分类ID',
  `representative_id` int(11) DEFAULT NULL COMMENT '代表性商品ID',
  `createtime` int(10) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) DEFAULT NULL COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品分组表';

-- --------------------------------------------------------

--
-- 表的结构 `fa_jewelry_group_item`
--

CREATE TABLE `fa_jewelry_group_item` (
  `id` int(11) NOT NULL,
  `group_id` int(11) NOT NULL COMMENT '分组ID',
  `jewelry_id` int(11) NOT NULL COMMENT '商品ID',
  `createtime` int(10) DEFAULT NULL COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='商品分组明细表';

-- --------------------------------------------------------

--
-- 表的结构 `fa_member`
--

CREATE TABLE `fa_member` (
  `id` int(11) NOT NULL,
  `card_no` varchar(20) NOT NULL COMMENT '会员卡号',
  `name` varchar(50) NOT NULL COMMENT '会员姓名',
  `phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `birthday` date DEFAULT NULL COMMENT '生日',
  `points` int(11) DEFAULT 0 COMMENT '积分',
  `level` tinyint(1) DEFAULT 1 COMMENT '会员等级',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态:0=禁用,1=正常',
  `createtime` int(10) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) DEFAULT NULL COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='会员表';

-- --------------------------------------------------------

--
-- 表的结构 `fa_recycling`
--

CREATE TABLE `fa_recycling` (
  `id` int(11) NOT NULL,
  `recycle_no` varchar(50) NOT NULL COMMENT '回收单号',
  `store_id` int(11) NOT NULL COMMENT '门店ID',
  `member_id` int(11) DEFAULT NULL COMMENT '会员ID',
  `customer_name` varchar(50) DEFAULT NULL COMMENT '客户姓名',
  `phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `gold_weight` decimal(10,3) DEFAULT 0.000 COMMENT '金重(克)',
  `gold_price` decimal(10,2) DEFAULT 0.00 COMMENT '金价(克价)',
  `gold_amount` decimal(10,2) DEFAULT 0.00 COMMENT '金价金额',
  `silver_weight` decimal(10,3) DEFAULT 0.000 COMMENT '银重(克)',
  `silver_price` decimal(10,2) DEFAULT 0.00 COMMENT '银价(克价)',
  `silver_amount` decimal(10,2) DEFAULT 0.00 COMMENT '银价金额',
  `price` decimal(10,2) DEFAULT 0.00 COMMENT '回收价格',
  `total_amount` decimal(10,2) DEFAULT 0.00 COMMENT '总金额',
  `operator_id` int(11) NOT NULL COMMENT '操作员ID',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态:0=作废,1=正常',
  `remark` text DEFAULT NULL COMMENT '备注',
  `createtime` int(10) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) DEFAULT NULL COMMENT '更新时间',
  `discount_amount` decimal(10,2) DEFAULT 0.00 COMMENT '折后总金额'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='旧料回收表';

-- --------------------------------------------------------

--
-- 表的结构 `fa_recycling_item`
--

CREATE TABLE `fa_recycling_item` (
  `id` int(11) NOT NULL,
  `recycling_id` int(11) NOT NULL COMMENT '回收单ID',
  `name` varchar(100) NOT NULL COMMENT '物品名称',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `gold_weight` decimal(10,2) DEFAULT 0.00 COMMENT '金重(克)',
  `gold_price` decimal(10,2) DEFAULT 0.00 COMMENT '金价(克价)',
  `gold_amount` decimal(10,2) DEFAULT 0.00 COMMENT '金价金额',
  `silver_weight` decimal(10,2) DEFAULT 0.00 COMMENT '银重(克)',
  `silver_price` decimal(10,2) DEFAULT 0.00 COMMENT '银价(克价)',
  `silver_amount` decimal(10,2) DEFAULT 0.00 COMMENT '银价金额',
  `total_amount` decimal(10,2) DEFAULT 0.00 COMMENT '总金额',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `createtime` int(10) DEFAULT NULL COMMENT '创建时间',
  `discount_rate` decimal(10,2) DEFAULT 100.00 COMMENT '折扣率(%)',
  `discount_amount` decimal(10,2) DEFAULT 0.00 COMMENT '折后金额'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='旧料回收明细表';

-- --------------------------------------------------------

--
-- 表的结构 `fa_sales`
--

CREATE TABLE `fa_sales` (
  `id` int(11) NOT NULL,
  `order_no` varchar(50) NOT NULL COMMENT '订单号',
  `store_id` int(11) NOT NULL COMMENT '门店ID',
  `member_id` int(11) DEFAULT NULL COMMENT '会员ID',
  `jewelry_id` int(11) NOT NULL COMMENT '商品ID',
  `price` decimal(10,2) NOT NULL COMMENT '销售价格',
  `quantity` int(11) DEFAULT 1 COMMENT '数量',
  `total_amount` decimal(10,2) NOT NULL COMMENT '总金额',
  `profit` decimal(10,2) DEFAULT 0.00 COMMENT '利润',
  `createtime` int(10) DEFAULT NULL COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='销售记录表';

-- --------------------------------------------------------

--
-- 表的结构 `fa_sms`
--

CREATE TABLE `fa_sms` (
  `id` int(10) UNSIGNED NOT NULL COMMENT 'ID',
  `event` varchar(30) DEFAULT '' COMMENT '事件',
  `mobile` varchar(20) DEFAULT '' COMMENT '手机号',
  `code` varchar(10) DEFAULT '' COMMENT '验证码',
  `times` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '验证次数',
  `ip` varchar(30) DEFAULT '' COMMENT 'IP',
  `createtime` bigint(16) UNSIGNED DEFAULT 0 COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='短信验证码表';

-- --------------------------------------------------------

--
-- 表的结构 `fa_stock_in`
--

CREATE TABLE `fa_stock_in` (
  `id` int(11) NOT NULL,
  `order_no` varchar(50) NOT NULL COMMENT '入库单号',
  `store_id` int(11) NOT NULL COMMENT '入库门店',
  `supplier` varchar(100) DEFAULT NULL COMMENT '供应商',
  `total_amount` decimal(10,2) DEFAULT 0.00 COMMENT '总金额',
  `remark` text DEFAULT NULL COMMENT '备注',
  `operator_id` int(11) NOT NULL COMMENT '操作员ID',
  `audit_user_id` int(11) DEFAULT NULL COMMENT '审核人ID',
  `audit_time` int(10) DEFAULT NULL COMMENT '审核时间',
  `audit_note` varchar(255) DEFAULT NULL COMMENT '审核备注',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态:0=草稿,1=已入库',
  `createtime` int(10) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) DEFAULT NULL COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='入库主表';

-- --------------------------------------------------------

--
-- 表的结构 `fa_stock_in_item`
--

CREATE TABLE `fa_stock_in_item` (
  `id` int(11) NOT NULL,
  `stock_in_id` int(11) NOT NULL COMMENT '入库单ID',
  `jewelry_id` int(11) NOT NULL COMMENT '商品ID',
  `barcode` varchar(50) NOT NULL COMMENT '条码',
  `name` varchar(100) NOT NULL COMMENT '商品名称',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `ring_size` varchar(20) DEFAULT NULL COMMENT '圈口号',
  `gold_weight` decimal(10,2) DEFAULT 0.00 COMMENT '金重(克)',
  `gold_price` decimal(10,2) DEFAULT 0.00 COMMENT '金进价(克价)',
  `gold_cost` decimal(10,2) DEFAULT 0.00 COMMENT '金成本',
  `silver_weight` decimal(10,2) DEFAULT 0.00 COMMENT '银重(克)',
  `total_weight` decimal(10,2) DEFAULT 0.00 COMMENT '总重(克)',
  `silver_price` decimal(10,2) DEFAULT 0.00 COMMENT '银进价(克价)',
  `silver_cost` decimal(10,2) DEFAULT 0.00 COMMENT '银成本',
  `silver_work_type` tinyint(1) DEFAULT 0 COMMENT '银工费方式:0=按克,1=按件',
  `silver_work_price` decimal(10,2) DEFAULT 0.00 COMMENT '银工费',
  `silver_work_cost` decimal(10,2) DEFAULT 0.00 COMMENT '工费成本',
  `plating_cost` decimal(10,2) DEFAULT 0.00 COMMENT '电铸费',
  `total_cost` decimal(10,2) DEFAULT 0.00 COMMENT '总成本',
  `wholesale_work_price` decimal(10,2) DEFAULT 0.00 COMMENT '批发工费',
  `retail_work_price` decimal(10,2) DEFAULT 0.00 COMMENT '零售工费',
  `piece_work_price` decimal(10,2) DEFAULT 0.00 COMMENT '件工费',
  `createtime` int(10) DEFAULT NULL COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='入库明细表';

-- --------------------------------------------------------

--
-- 表的结构 `fa_stock_out`
--

CREATE TABLE `fa_stock_out` (
  `id` int(10) UNSIGNED NOT NULL COMMENT 'ID',
  `order_no` varchar(30) NOT NULL COMMENT '出库单号',
  `store_id` int(10) UNSIGNED NOT NULL COMMENT '出库门店',
  `customer` varchar(50) DEFAULT '' COMMENT '客户',
  `recycling_id` int(11) DEFAULT 0 COMMENT '关联的回收单ID',
  `total_weight` decimal(10,2) DEFAULT 0.00 COMMENT '总重量(克)',
  `sale_type` varchar(20) DEFAULT 'retail' COMMENT '销售类型:retail=零售,wholesale=批发',
  `total_amount` decimal(10,2) DEFAULT 0.00 COMMENT '总金额',
  `operator_id` int(10) UNSIGNED NOT NULL COMMENT '操作员',
  `remark` varchar(255) DEFAULT '' COMMENT '备注',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态:1=待审核,2=已通过,3=未通过,4=已作废',
  `createtime` int(10) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) DEFAULT NULL COMMENT '更新时间',
  `audit_time` int(10) DEFAULT NULL COMMENT '审核时间',
  `audit_user_id` int(11) DEFAULT NULL COMMENT '审核人ID',
  `audit_remark` varchar(255) DEFAULT '' COMMENT '审核备注',
  `payment_status` tinyint(1) DEFAULT 0 COMMENT '收款状态:0=未收款,1=已收款',
  `payment_time` int(10) DEFAULT NULL COMMENT '收款时间',
  `payment_method` varchar(20) DEFAULT NULL COMMENT '收款方式',
  `payment_remark` varchar(255) DEFAULT NULL COMMENT '支付备注',
  `cash_amount` decimal(10,2) DEFAULT 0.00 COMMENT '现金金额',
  `wechat_amount` decimal(10,2) DEFAULT 0.00 COMMENT '微信金额',
  `alipay_amount` decimal(10,2) DEFAULT 0.00 COMMENT '支付宝金额',
  `card_amount` decimal(10,2) DEFAULT 0.00 COMMENT '刷卡金额',
  `discount_amount` decimal(10,2) DEFAULT 0.00 COMMENT '抹零金额',
  `actual_amount` decimal(10,2) DEFAULT 0.00 COMMENT '实收金额'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='出库单';

-- --------------------------------------------------------

--
-- 表的结构 `fa_stock_out_item`
--

CREATE TABLE `fa_stock_out_item` (
  `id` int(11) NOT NULL,
  `stock_out_id` int(11) NOT NULL COMMENT '出库单ID',
  `jewelry_id` int(11) NOT NULL COMMENT '商品ID',
  `barcode` varchar(50) NOT NULL COMMENT '条码',
  `name` varchar(100) NOT NULL COMMENT '商品名称',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `ring_size` varchar(20) DEFAULT NULL COMMENT '圈口号',
  `gold_weight` decimal(10,2) DEFAULT 0.00 COMMENT '金重(克)',
  `gold_price` decimal(10,2) DEFAULT 0.00 COMMENT '金价(克价)',
  `gold_cost` decimal(10,2) DEFAULT 0.00 COMMENT '金成本',
  `silver_weight` decimal(10,2) DEFAULT 0.00 COMMENT '银重(克)',
  `total_weight` decimal(10,2) DEFAULT 0.00 COMMENT '总重(克)',
  `silver_price` decimal(10,2) DEFAULT 0.00 COMMENT '银价(克价)',
  `silver_cost` decimal(10,2) DEFAULT 0.00 COMMENT '银成本',
  `silver_work_type` tinyint(1) DEFAULT 0 COMMENT '工费方式:0=按克,1=按件',
  `silver_work_price` decimal(10,2) DEFAULT 0.00 COMMENT '工费',
  `plating_cost` decimal(10,2) DEFAULT 0.00 COMMENT '电铸费',
  `total_cost` decimal(10,2) DEFAULT 0.00 COMMENT '总成本',
  `createtime` int(10) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) DEFAULT NULL COMMENT '更新时间',
  `piece_work_price` decimal(10,2) DEFAULT 0.00 COMMENT '件工费',
  `total_amount` decimal(10,2) DEFAULT 0.00 COMMENT '商品金额',
  `sale_type` varchar(20) DEFAULT 'retail' COMMENT '销售类型:retail=零售,wholesale=批发',
  `work_price` decimal(10,2) DEFAULT 0.00 COMMENT '实际工费'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='出库明细表';

-- --------------------------------------------------------

--
-- 表的结构 `fa_stock_return`
--

CREATE TABLE `fa_stock_return` (
  `id` int(10) UNSIGNED NOT NULL COMMENT 'ID',
  `order_no` varchar(20) NOT NULL COMMENT '退货单号',
  `store_id` int(10) UNSIGNED NOT NULL COMMENT '退货门店ID',
  `customer` varchar(50) DEFAULT NULL COMMENT '客户',
  `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '应收金额',
  `discount_amount` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '优惠金额',
  `actual_amount` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '实际退款',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `operator_id` int(10) UNSIGNED NOT NULL COMMENT '操作员ID',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态(1:待审核,2:已通过,3:未通过,4:已作废)',
  `audit_user_id` int(10) UNSIGNED DEFAULT NULL COMMENT '审核人ID',
  `audit_time` int(10) UNSIGNED DEFAULT NULL COMMENT '审核时间',
  `audit_remark` varchar(255) DEFAULT NULL COMMENT '审核备注',
  `createtime` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) UNSIGNED DEFAULT NULL COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='退货单';

-- --------------------------------------------------------

--
-- 表的结构 `fa_stock_return_item`
--

CREATE TABLE `fa_stock_return_item` (
  `id` int(10) UNSIGNED NOT NULL COMMENT 'ID',
  `stock_return_id` int(10) UNSIGNED NOT NULL COMMENT '退货单ID',
  `jewelry_id` int(10) UNSIGNED DEFAULT NULL COMMENT '商品ID',
  `barcode` varchar(30) NOT NULL COMMENT '商品条码',
  `name` varchar(100) NOT NULL COMMENT '商品名称',
  `category_id` int(10) UNSIGNED DEFAULT NULL COMMENT '分类ID',
  `ring_size` varchar(20) DEFAULT NULL COMMENT '圈口',
  `gold_weight` decimal(10,2) DEFAULT 0.00 COMMENT '金重(g)',
  `gold_price` decimal(10,2) DEFAULT 0.00 COMMENT '金价(元/g)',
  `silver_weight` decimal(10,2) DEFAULT 0.00 COMMENT '银重(g)',
  `silver_price` decimal(10,2) DEFAULT 0.00 COMMENT '银价(元/g)',
  `total_weight` decimal(10,2) DEFAULT 0.00 COMMENT '总重(g)',
  `silver_work_price` decimal(10,2) DEFAULT 0.00 COMMENT '银工费(元/g)',
  `piece_work_price` decimal(10,2) DEFAULT 0.00 COMMENT '件工费(元)',
  `amount` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '金额',
  `source_order_no` varchar(20) DEFAULT NULL COMMENT '来源出库单号',
  `createtime` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) UNSIGNED DEFAULT NULL COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='退货单明细';

-- --------------------------------------------------------

--
-- 表的结构 `fa_store`
--

CREATE TABLE `fa_store` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL COMMENT '门店名称',
  `address` varchar(255) DEFAULT NULL COMMENT '门店地址',
  `phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态:0=关闭,1=正常',
  `createtime` int(10) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) DEFAULT NULL COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='门店表';

-- --------------------------------------------------------

--
-- 表的结构 `fa_store_transfer`
--

CREATE TABLE `fa_store_transfer` (
  `id` int(10) UNSIGNED NOT NULL COMMENT 'ID',
  `transfer_no` varchar(50) NOT NULL COMMENT '调拨单号',
  `from_store_id` int(10) UNSIGNED NOT NULL COMMENT '源店铺ID',
  `to_store_id` int(10) UNSIGNED NOT NULL COMMENT '目标店铺ID',
  `admin_id` int(10) UNSIGNED NOT NULL COMMENT '操作员ID',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态:0=待审核,1=已通过,2=已拒绝',
  `audit_id` int(10) UNSIGNED DEFAULT NULL COMMENT '审核员ID',
  `audit_time` int(10) UNSIGNED DEFAULT NULL COMMENT '审核时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `payment_status` smallint(6) DEFAULT 0 COMMENT '收款状态:0=未收款,1=已收款',
  `payment_time` int(10) UNSIGNED DEFAULT NULL COMMENT '收款时间',
  `payment_method` varchar(20) DEFAULT NULL COMMENT '收款方式',
  `payment_remark` varchar(255) DEFAULT NULL COMMENT '支付备注',
  `cash_amount` decimal(10,2) DEFAULT 0.00 COMMENT '现金金额',
  `wechat_amount` decimal(10,2) DEFAULT 0.00 COMMENT '微信金额',
  `alipay_amount` decimal(10,2) DEFAULT 0.00 COMMENT '支付宝金额',
  `card_amount` decimal(10,2) DEFAULT 0.00 COMMENT '刷卡金额',
  `discount_amount` decimal(10,2) DEFAULT 0.00 COMMENT '抹零金额',
  `actual_amount` decimal(10,2) DEFAULT 0.00 COMMENT '实收金额',
  `createtime` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='店间调拨主表';

-- --------------------------------------------------------

--
-- 表的结构 `fa_store_transfer_item`
--

CREATE TABLE `fa_store_transfer_item` (
  `id` int(10) UNSIGNED NOT NULL COMMENT 'ID',
  `transfer_id` int(10) UNSIGNED NOT NULL COMMENT '调拨单ID',
  `jewelry_id` int(10) UNSIGNED NOT NULL COMMENT '商品ID',
  `transfer_price` decimal(10,2) NOT NULL COMMENT '调拨价格',
  `gold_price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '金价',
  `silver_price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '银价',
  `total_weight` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '总重',
  `silver_work_price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '工费',
  `piece_work_price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '单件工费',
  `original_data` text DEFAULT NULL COMMENT '原始数据JSON',
  `createtime` int(10) UNSIGNED DEFAULT NULL COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='店间调拨明细表';

-- --------------------------------------------------------

--
-- 表的结构 `fa_test`
--

CREATE TABLE `fa_test` (
  `id` int(10) UNSIGNED NOT NULL COMMENT 'ID',
  `user_id` int(10) DEFAULT 0 COMMENT '会员ID',
  `admin_id` int(10) DEFAULT 0 COMMENT '管理员ID',
  `category_id` int(10) UNSIGNED DEFAULT 0 COMMENT '分类ID(单选)',
  `category_ids` varchar(100) DEFAULT NULL COMMENT '分类ID(多选)',
  `tags` varchar(255) DEFAULT '' COMMENT '标签',
  `week` enum('monday','tuesday','wednesday') DEFAULT NULL COMMENT '星期(单选):monday=星期一,tuesday=星期二,wednesday=星期三',
  `flag` set('hot','index','recommend') DEFAULT '' COMMENT '标志(多选):hot=热门,index=首页,recommend=推荐',
  `genderdata` enum('male','female') DEFAULT 'male' COMMENT '性别(单选):male=男,female=女',
  `hobbydata` set('music','reading','swimming') DEFAULT NULL COMMENT '爱好(多选):music=音乐,reading=读书,swimming=游泳',
  `title` varchar(100) DEFAULT '' COMMENT '标题',
  `content` text DEFAULT NULL COMMENT '内容',
  `image` varchar(100) DEFAULT '' COMMENT '图片',
  `images` varchar(1500) DEFAULT '' COMMENT '图片组',
  `attachfile` varchar(100) DEFAULT '' COMMENT '附件',
  `keywords` varchar(255) DEFAULT '' COMMENT '关键字',
  `description` varchar(255) DEFAULT '' COMMENT '描述',
  `city` varchar(100) DEFAULT '' COMMENT '省市',
  `array` varchar(255) DEFAULT '' COMMENT '数组:value=值',
  `json` varchar(255) DEFAULT '' COMMENT '配置:key=名称,value=值',
  `multiplejson` varchar(1500) DEFAULT '' COMMENT '二维数组:title=标题,intro=介绍,author=作者,age=年龄',
  `price` decimal(10,2) UNSIGNED DEFAULT 0.00 COMMENT '价格',
  `views` int(10) UNSIGNED DEFAULT 0 COMMENT '点击',
  `workrange` varchar(100) DEFAULT '' COMMENT '时间区间',
  `startdate` date DEFAULT NULL COMMENT '开始日期',
  `activitytime` datetime DEFAULT NULL COMMENT '活动时间(datetime)',
  `year` year(4) DEFAULT NULL COMMENT '年',
  `times` time DEFAULT NULL COMMENT '时间',
  `refreshtime` bigint(16) DEFAULT NULL COMMENT '刷新时间',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  `deletetime` bigint(16) DEFAULT NULL COMMENT '删除时间',
  `weigh` int(10) DEFAULT 0 COMMENT '权重',
  `switch` tinyint(1) DEFAULT 0 COMMENT '开关',
  `status` enum('normal','hidden') DEFAULT 'normal' COMMENT '状态',
  `state` enum('0','1','2') DEFAULT '1' COMMENT '状态值:0=禁用,1=正常,2=推荐'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='测试表';

-- --------------------------------------------------------

--
-- 表的结构 `fa_transfer`
--

CREATE TABLE `fa_transfer` (
  `id` int(11) NOT NULL,
  `transfer_no` varchar(50) NOT NULL COMMENT '调拨单号',
  `from_store` int(11) NOT NULL COMMENT '调出门店',
  `to_store` int(11) NOT NULL COMMENT '调入门店',
  `jewelry_id` int(11) NOT NULL COMMENT '商品ID',
  `status` tinyint(1) DEFAULT 0 COMMENT '状态:0=待确认,1=已确认',
  `createtime` int(10) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) DEFAULT NULL COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='库存调拨表';

-- --------------------------------------------------------

--
-- 表的结构 `fa_user`
--

CREATE TABLE `fa_user` (
  `id` int(10) UNSIGNED NOT NULL COMMENT 'ID',
  `group_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '组别ID',
  `username` varchar(32) DEFAULT '' COMMENT '用户名',
  `nickname` varchar(50) DEFAULT '' COMMENT '昵称',
  `password` varchar(32) DEFAULT '' COMMENT '密码',
  `salt` varchar(30) DEFAULT '' COMMENT '密码盐',
  `email` varchar(100) DEFAULT '' COMMENT '电子邮箱',
  `mobile` varchar(11) DEFAULT '' COMMENT '手机号',
  `avatar` varchar(255) DEFAULT '' COMMENT '头像',
  `level` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '等级',
  `gender` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '性别',
  `birthday` date DEFAULT NULL COMMENT '生日',
  `bio` varchar(100) DEFAULT '' COMMENT '格言',
  `money` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '余额',
  `score` int(10) NOT NULL DEFAULT 0 COMMENT '积分',
  `successions` int(10) UNSIGNED NOT NULL DEFAULT 1 COMMENT '连续登录天数',
  `maxsuccessions` int(10) UNSIGNED NOT NULL DEFAULT 1 COMMENT '最大连续登录天数',
  `prevtime` bigint(16) DEFAULT NULL COMMENT '上次登录时间',
  `logintime` bigint(16) DEFAULT NULL COMMENT '登录时间',
  `loginip` varchar(50) DEFAULT '' COMMENT '登录IP',
  `loginfailure` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '失败次数',
  `loginfailuretime` bigint(16) DEFAULT NULL COMMENT '最后登录失败时间',
  `joinip` varchar(50) DEFAULT '' COMMENT '加入IP',
  `jointime` bigint(16) DEFAULT NULL COMMENT '加入时间',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  `token` varchar(50) DEFAULT '' COMMENT 'Token',
  `status` varchar(30) DEFAULT '' COMMENT '状态',
  `verification` varchar(255) DEFAULT '' COMMENT '验证'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='会员表';

-- --------------------------------------------------------

--
-- 表的结构 `fa_user_group`
--

CREATE TABLE `fa_user_group` (
  `id` int(10) UNSIGNED NOT NULL,
  `name` varchar(50) DEFAULT '' COMMENT '组名',
  `rules` text DEFAULT NULL COMMENT '权限节点',
  `createtime` bigint(16) DEFAULT NULL COMMENT '添加时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  `status` enum('normal','hidden') DEFAULT NULL COMMENT '状态'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='会员组表';

-- --------------------------------------------------------

--
-- 表的结构 `fa_user_money_log`
--

CREATE TABLE `fa_user_money_log` (
  `id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '会员ID',
  `money` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '变更余额',
  `before` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '变更前余额',
  `after` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '变更后余额',
  `memo` varchar(255) DEFAULT '' COMMENT '备注',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='会员余额变动表';

-- --------------------------------------------------------

--
-- 表的结构 `fa_user_rule`
--

CREATE TABLE `fa_user_rule` (
  `id` int(10) UNSIGNED NOT NULL,
  `pid` int(10) DEFAULT NULL COMMENT '父ID',
  `name` varchar(50) DEFAULT NULL COMMENT '名称',
  `title` varchar(50) DEFAULT '' COMMENT '标题',
  `remark` varchar(100) DEFAULT NULL COMMENT '备注',
  `ismenu` tinyint(1) DEFAULT NULL COMMENT '是否菜单',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  `weigh` int(10) DEFAULT 0 COMMENT '权重',
  `status` enum('normal','hidden') DEFAULT NULL COMMENT '状态'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='会员规则表';

-- --------------------------------------------------------

--
-- 表的结构 `fa_user_score_log`
--

CREATE TABLE `fa_user_score_log` (
  `id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '会员ID',
  `score` int(10) NOT NULL DEFAULT 0 COMMENT '变更积分',
  `before` int(10) NOT NULL DEFAULT 0 COMMENT '变更前积分',
  `after` int(10) NOT NULL DEFAULT 0 COMMENT '变更后积分',
  `memo` varchar(255) DEFAULT '' COMMENT '备注',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='会员积分变动表';

-- --------------------------------------------------------

--
-- 表的结构 `fa_user_token`
--

CREATE TABLE `fa_user_token` (
  `token` varchar(50) NOT NULL COMMENT 'Token',
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '会员ID',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `expiretime` bigint(16) DEFAULT NULL COMMENT '过期时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='会员Token表';

-- --------------------------------------------------------

--
-- 表的结构 `fa_version`
--

CREATE TABLE `fa_version` (
  `id` int(11) NOT NULL COMMENT 'ID',
  `oldversion` varchar(30) DEFAULT '' COMMENT '旧版本号',
  `newversion` varchar(30) DEFAULT '' COMMENT '新版本号',
  `packagesize` varchar(30) DEFAULT '' COMMENT '包大小',
  `content` varchar(500) DEFAULT '' COMMENT '升级内容',
  `downloadurl` varchar(255) DEFAULT '' COMMENT '下载地址',
  `enforce` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '强制更新',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  `weigh` int(10) NOT NULL DEFAULT 0 COMMENT '权重',
  `status` varchar(30) DEFAULT '' COMMENT '状态'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='版本表';

--
-- 转储表的索引
--

--
-- 表的索引 `fa_admin`
--
ALTER TABLE `fa_admin`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`) USING BTREE;

--
-- 表的索引 `fa_admin_log`
--
ALTER TABLE `fa_admin_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `name` (`username`);

--
-- 表的索引 `fa_area`
--
ALTER TABLE `fa_area`
  ADD PRIMARY KEY (`id`),
  ADD KEY `pid` (`pid`);

--
-- 表的索引 `fa_attachment`
--
ALTER TABLE `fa_attachment`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `fa_auth_group`
--
ALTER TABLE `fa_auth_group`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `fa_auth_group_access`
--
ALTER TABLE `fa_auth_group_access`
  ADD UNIQUE KEY `uid_group_id` (`uid`,`group_id`),
  ADD KEY `uid` (`uid`),
  ADD KEY `group_id` (`group_id`);

--
-- 表的索引 `fa_auth_rule`
--
ALTER TABLE `fa_auth_rule`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `name` (`name`) USING BTREE,
  ADD KEY `pid` (`pid`),
  ADD KEY `weigh` (`weigh`);

--
-- 表的索引 `fa_category`
--
ALTER TABLE `fa_category`
  ADD PRIMARY KEY (`id`),
  ADD KEY `weigh` (`weigh`,`id`),
  ADD KEY `pid` (`pid`);

--
-- 表的索引 `fa_config`
--
ALTER TABLE `fa_config`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `name` (`name`);

--
-- 表的索引 `fa_ems`
--
ALTER TABLE `fa_ems`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 表的索引 `fa_group_images`
--
ALTER TABLE `fa_group_images`
  ADD PRIMARY KEY (`id`),
  ADD KEY `group_id` (`group_id`);

--
-- 表的索引 `fa_inventory_check`
--
ALTER TABLE `fa_inventory_check`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `check_no` (`check_no`),
  ADD KEY `store_id` (`store_id`);

--
-- 表的索引 `fa_inventory_check_item`
--
ALTER TABLE `fa_inventory_check_item`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `check_jewelry` (`check_id`,`jewelry_id`),
  ADD KEY `barcode` (`barcode`),
  ADD KEY `check_id` (`check_id`),
  ADD KEY `status` (`status`);

--
-- 表的索引 `fa_jewelry`
--
ALTER TABLE `fa_jewelry`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `barcode` (`barcode`),
  ADD KEY `category_id` (`category_id`),
  ADD KEY `store_id` (`store_id`);

--
-- 表的索引 `fa_jewelry_category`
--
ALTER TABLE `fa_jewelry_category`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `fa_jewelry_group`
--
ALTER TABLE `fa_jewelry_group`
  ADD PRIMARY KEY (`id`),
  ADD KEY `name` (`name`),
  ADD KEY `category_id` (`category_id`);

--
-- 表的索引 `fa_jewelry_group_item`
--
ALTER TABLE `fa_jewelry_group_item`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `group_jewelry` (`group_id`,`jewelry_id`),
  ADD KEY `jewelry_id` (`jewelry_id`);

--
-- 表的索引 `fa_member`
--
ALTER TABLE `fa_member`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `card_no` (`card_no`);

--
-- 表的索引 `fa_recycling`
--
ALTER TABLE `fa_recycling`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `fa_recycling_item`
--
ALTER TABLE `fa_recycling_item`
  ADD PRIMARY KEY (`id`),
  ADD KEY `recycling_id` (`recycling_id`),
  ADD KEY `category_id` (`category_id`);

--
-- 表的索引 `fa_sales`
--
ALTER TABLE `fa_sales`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `fa_sms`
--
ALTER TABLE `fa_sms`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `fa_stock_in`
--
ALTER TABLE `fa_stock_in`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `order_no` (`order_no`);

--
-- 表的索引 `fa_stock_in_item`
--
ALTER TABLE `fa_stock_in_item`
  ADD PRIMARY KEY (`id`),
  ADD KEY `stock_in_id` (`stock_in_id`),
  ADD KEY `jewelry_id` (`jewelry_id`),
  ADD KEY `barcode` (`barcode`),
  ADD KEY `barcode_2` (`barcode`);

--
-- 表的索引 `fa_stock_out`
--
ALTER TABLE `fa_stock_out`
  ADD PRIMARY KEY (`id`),
  ADD KEY `order_no` (`order_no`),
  ADD KEY `store_id` (`store_id`),
  ADD KEY `operator_id` (`operator_id`);

--
-- 表的索引 `fa_stock_out_item`
--
ALTER TABLE `fa_stock_out_item`
  ADD PRIMARY KEY (`id`),
  ADD KEY `stock_out_id` (`stock_out_id`),
  ADD KEY `jewelry_id` (`jewelry_id`),
  ADD KEY `barcode` (`barcode`);

--
-- 表的索引 `fa_stock_return`
--
ALTER TABLE `fa_stock_return`
  ADD PRIMARY KEY (`id`),
  ADD KEY `order_no` (`order_no`),
  ADD KEY `store_id` (`store_id`),
  ADD KEY `operator_id` (`operator_id`),
  ADD KEY `status` (`status`),
  ADD KEY `createtime` (`createtime`);

--
-- 表的索引 `fa_stock_return_item`
--
ALTER TABLE `fa_stock_return_item`
  ADD PRIMARY KEY (`id`),
  ADD KEY `stock_return_id` (`stock_return_id`),
  ADD KEY `jewelry_id` (`jewelry_id`),
  ADD KEY `barcode` (`barcode`);

--
-- 表的索引 `fa_store`
--
ALTER TABLE `fa_store`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `fa_store_transfer`
--
ALTER TABLE `fa_store_transfer`
  ADD PRIMARY KEY (`id`),
  ADD KEY `transfer_no` (`transfer_no`),
  ADD KEY `from_store_id` (`from_store_id`),
  ADD KEY `to_store_id` (`to_store_id`),
  ADD KEY `admin_id` (`admin_id`),
  ADD KEY `status` (`status`);

--
-- 表的索引 `fa_store_transfer_item`
--
ALTER TABLE `fa_store_transfer_item`
  ADD PRIMARY KEY (`id`),
  ADD KEY `transfer_id` (`transfer_id`),
  ADD KEY `jewelry_id` (`jewelry_id`);

--
-- 表的索引 `fa_test`
--
ALTER TABLE `fa_test`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `fa_transfer`
--
ALTER TABLE `fa_transfer`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `fa_user`
--
ALTER TABLE `fa_user`
  ADD PRIMARY KEY (`id`),
  ADD KEY `username` (`username`),
  ADD KEY `email` (`email`),
  ADD KEY `mobile` (`mobile`);

--
-- 表的索引 `fa_user_group`
--
ALTER TABLE `fa_user_group`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `fa_user_money_log`
--
ALTER TABLE `fa_user_money_log`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `fa_user_rule`
--
ALTER TABLE `fa_user_rule`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `fa_user_score_log`
--
ALTER TABLE `fa_user_score_log`
  ADD PRIMARY KEY (`id`);

--
-- 表的索引 `fa_user_token`
--
ALTER TABLE `fa_user_token`
  ADD PRIMARY KEY (`token`);

--
-- 表的索引 `fa_version`
--
ALTER TABLE `fa_version`
  ADD PRIMARY KEY (`id`) USING BTREE;

--
-- 在导出的表使用AUTO_INCREMENT
--

--
-- 使用表AUTO_INCREMENT `fa_admin`
--
ALTER TABLE `fa_admin`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID';

--
-- 使用表AUTO_INCREMENT `fa_admin_log`
--
ALTER TABLE `fa_admin_log`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID';

--
-- 使用表AUTO_INCREMENT `fa_area`
--
ALTER TABLE `fa_area`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT COMMENT 'ID';

--
-- 使用表AUTO_INCREMENT `fa_attachment`
--
ALTER TABLE `fa_attachment`
  MODIFY `id` int(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID';

--
-- 使用表AUTO_INCREMENT `fa_auth_group`
--
ALTER TABLE `fa_auth_group`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `fa_auth_rule`
--
ALTER TABLE `fa_auth_rule`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `fa_category`
--
ALTER TABLE `fa_category`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `fa_config`
--
ALTER TABLE `fa_config`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `fa_ems`
--
ALTER TABLE `fa_ems`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID';

--
-- 使用表AUTO_INCREMENT `fa_group_images`
--
ALTER TABLE `fa_group_images`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `fa_inventory_check`
--
ALTER TABLE `fa_inventory_check`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `fa_inventory_check_item`
--
ALTER TABLE `fa_inventory_check_item`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `fa_jewelry`
--
ALTER TABLE `fa_jewelry`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `fa_jewelry_category`
--
ALTER TABLE `fa_jewelry_category`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `fa_jewelry_group`
--
ALTER TABLE `fa_jewelry_group`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `fa_jewelry_group_item`
--
ALTER TABLE `fa_jewelry_group_item`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `fa_member`
--
ALTER TABLE `fa_member`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `fa_recycling`
--
ALTER TABLE `fa_recycling`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `fa_recycling_item`
--
ALTER TABLE `fa_recycling_item`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `fa_sales`
--
ALTER TABLE `fa_sales`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `fa_sms`
--
ALTER TABLE `fa_sms`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID';

--
-- 使用表AUTO_INCREMENT `fa_stock_in`
--
ALTER TABLE `fa_stock_in`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `fa_stock_in_item`
--
ALTER TABLE `fa_stock_in_item`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `fa_stock_out`
--
ALTER TABLE `fa_stock_out`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID';

--
-- 使用表AUTO_INCREMENT `fa_stock_out_item`
--
ALTER TABLE `fa_stock_out_item`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `fa_stock_return`
--
ALTER TABLE `fa_stock_return`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID';

--
-- 使用表AUTO_INCREMENT `fa_stock_return_item`
--
ALTER TABLE `fa_stock_return_item`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID';

--
-- 使用表AUTO_INCREMENT `fa_store`
--
ALTER TABLE `fa_store`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `fa_store_transfer`
--
ALTER TABLE `fa_store_transfer`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID';

--
-- 使用表AUTO_INCREMENT `fa_store_transfer_item`
--
ALTER TABLE `fa_store_transfer_item`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID';

--
-- 使用表AUTO_INCREMENT `fa_test`
--
ALTER TABLE `fa_test`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID';

--
-- 使用表AUTO_INCREMENT `fa_transfer`
--
ALTER TABLE `fa_transfer`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `fa_user`
--
ALTER TABLE `fa_user`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID';

--
-- 使用表AUTO_INCREMENT `fa_user_group`
--
ALTER TABLE `fa_user_group`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `fa_user_money_log`
--
ALTER TABLE `fa_user_money_log`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `fa_user_rule`
--
ALTER TABLE `fa_user_rule`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `fa_user_score_log`
--
ALTER TABLE `fa_user_score_log`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `fa_version`
--
ALTER TABLE `fa_version`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID';
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
