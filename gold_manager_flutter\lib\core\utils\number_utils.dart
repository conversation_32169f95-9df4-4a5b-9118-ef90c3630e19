/// 数字转换工具类
/// 提供安全的数字类型转换方法，支持从字符串、整数、浮点数等类型转换
class NumberUtils {
  /// 安全转换为 double 类型
  /// 支持从 null、int、double、String 类型转换
  /// 如果转换失败，返回默认值
  static double toDouble(dynamic value, {double defaultValue = 0.0}) {
    if (value == null) return defaultValue;
    
    if (value is double) return value;
    
    if (value is int) return value.toDouble();
    
    if (value is String) {
      if (value.trim().isEmpty) return defaultValue;
      return double.tryParse(value.trim()) ?? defaultValue;
    }
    
    // 尝试转换为字符串再解析
    try {
      return double.parse(value.toString());
    } catch (e) {
      return defaultValue;
    }
  }
  
  /// 安全转换为 int 类型
  /// 支持从 null、int、double、String 类型转换
  /// 如果转换失败，返回默认值
  static int toInt(dynamic value, {int defaultValue = 0}) {
    if (value == null) return defaultValue;
    
    if (value is int) return value;
    
    if (value is double) return value.round();
    
    if (value is String) {
      if (value.trim().isEmpty) return defaultValue;
      return int.tryParse(value.trim()) ?? defaultValue;
    }
    
    // 尝试转换为字符串再解析
    try {
      return int.parse(value.toString());
    } catch (e) {
      return defaultValue;
    }
  }
  
  /// 安全转换为 bool 类型
  /// 支持从 null、bool、int、String 类型转换
  /// 如果转换失败，返回默认值
  static bool toBool(dynamic value, {bool defaultValue = false}) {
    if (value == null) return defaultValue;
    
    if (value is bool) return value;
    
    if (value is int) return value != 0;
    
    if (value is String) {
      final str = value.trim().toLowerCase();
      if (str == 'true' || str == '1' || str == 'yes') return true;
      if (str == 'false' || str == '0' || str == 'no') return false;
      return defaultValue;
    }
    
    return defaultValue;
  }
  
  /// 格式化数字为指定小数位数的字符串
  static String formatDouble(double value, {int decimals = 2}) {
    return value.toStringAsFixed(decimals);
  }
  
  /// 格式化金额，添加千分位分隔符
  static String formatCurrency(double value, {int decimals = 2, String symbol = '¥'}) {
    final formatted = formatDouble(value, decimals: decimals);
    final parts = formatted.split('.');
    final integerPart = parts[0];
    final decimalPart = parts.length > 1 ? '.${parts[1]}' : '';
    
    // 添加千分位分隔符
    final regex = RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))');
    final integerWithCommas = integerPart.replaceAllMapped(regex, (match) => '${match[1]},');
    
    return '$symbol$integerWithCommas$decimalPart';
  }
  
  /// 格式化重量，保留3位小数
  static String formatWeight(double value) {
    return formatDouble(value, decimals: 3);
  }
  
  /// 检查数字是否为零或接近零
  static bool isZero(double value, {double tolerance = 0.001}) {
    return value.abs() < tolerance;
  }
  
  /// 检查数字是否为正数
  static bool isPositive(double value) {
    return value > 0;
  }
  
  /// 检查数字是否为负数
  static bool isNegative(double value) {
    return value < 0;
  }
}
