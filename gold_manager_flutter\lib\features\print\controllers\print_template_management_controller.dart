import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../services/print_template_service.dart';
import '../../../services/print_service.dart';
import '../../../models/print/print_template_config.dart';
import '../../../core/utils/logger_service.dart';
import '../views/print_template_editor_view.dart';

/// 打印模板管理控制器
/// 
/// 负责模板列表的管理、CRUD操作和用户交互
class PrintTemplateManagementController extends GetxController {
  final PrintTemplateService _templateService = Get.find<PrintTemplateService>();

  /// 模板列表
  final RxList<PrintTemplateConfig> templates = <PrintTemplateConfig>[].obs;
  
  /// 默认模板
  final Rx<PrintTemplateConfig?> defaultTemplate = Rx<PrintTemplateConfig?>(null);
  
  /// 加载状态
  final RxBool isLoading = false.obs;

  @override
  void onInit() {
    super.onInit();
    _loadTemplates();
  }

  /// 加载模板列表
  Future<void> _loadTemplates() async {
    try {
      isLoading.value = true;
      
      // 等待模板服务初始化完成
      if (!_templateService.isInitialized.value) {
        await Future.delayed(const Duration(milliseconds: 100));
        if (!_templateService.isInitialized.value) {
          LoggerService.w('模板服务未初始化，等待初始化完成...');
          await _templateService.onInit();
        }
      }
      
      templates.value = _templateService.getAllTemplates();
      defaultTemplate.value = _templateService.getDefaultTemplate();
      
      LoggerService.d('✅ 已加载 ${templates.length} 个模板');
    } catch (e) {
      LoggerService.e('加载模板列表失败', e);
      Get.snackbar(
        '加载失败',
        '无法加载模板列表: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red[600],
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// 刷新模板列表
  Future<void> refreshTemplates() async {
    await _loadTemplates();
    Get.snackbar(
      '刷新完成',
      '模板列表已更新',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.green[600],
      colorText: Colors.white,
      duration: const Duration(seconds: 2),
    );
  }

  /// 创建新模板
  Future<void> createNewTemplate() async {
    try {
      LoggerService.d('🆕 创建新模板');
      
      // 跳转到模板编辑器
      final result = await Get.to(() => const PrintTemplateEditorView());
      
      if (result == true) {
        // 刷新列表
        await _loadTemplates();
        Get.snackbar(
          '创建成功',
          '新模板已创建',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green[600],
          colorText: Colors.white,
        );
      }
    } catch (e) {
      LoggerService.e('创建模板失败', e);
      Get.snackbar(
        '创建失败',
        '无法创建新模板: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red[600],
        colorText: Colors.white,
      );
    }
  }

  /// 编辑模板
  Future<void> editTemplate(PrintTemplateConfig template) async {
    try {
      LoggerService.d('✏️ 编辑模板: ${template.name}');
      
      // 跳转到模板编辑器
      final result = await Get.to(() => PrintTemplateEditorView(template: template));
      
      if (result == true) {
        // 刷新列表
        await _loadTemplates();
        Get.snackbar(
          '保存成功',
          '模板已更新',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green[600],
          colorText: Colors.white,
        );
      }
    } catch (e) {
      LoggerService.e('编辑模板失败', e);
      Get.snackbar(
        '编辑失败',
        '无法编辑模板: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red[600],
        colorText: Colors.white,
      );
    }
  }

  /// 复制模板
  Future<void> copyTemplate(PrintTemplateConfig template) async {
    try {
      // 显示复制对话框
      final newName = await _showCopyDialog(template.name);
      if (newName == null || newName.isEmpty) return;
      
      LoggerService.d('📋 复制模板: ${template.name} -> $newName');
      
      final newTemplate = await _templateService.duplicateTemplate(template.id, newName);
      if (newTemplate != null) {
        await _loadTemplates();
        Get.snackbar(
          '复制成功',
          '模板"$newName"已创建',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green[600],
          colorText: Colors.white,
        );
      } else {
        Get.snackbar(
          '复制失败',
          '无法复制模板',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red[600],
          colorText: Colors.white,
        );
      }
    } catch (e) {
      LoggerService.e('复制模板失败', e);
      Get.snackbar(
        '复制失败',
        '无法复制模板: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red[600],
        colorText: Colors.white,
      );
    }
  }

  /// 设置默认模板
  Future<void> setDefaultTemplate(PrintTemplateConfig template) async {
    try {
      LoggerService.d('⭐ 设置默认模板: ${template.name}');
      
      final success = await _templateService.setDefaultTemplate(template.id);
      if (success) {
        defaultTemplate.value = template;
        Get.snackbar(
          '设置成功',
          '"${template.name}"已设为默认模板',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green[600],
          colorText: Colors.white,
        );
      } else {
        Get.snackbar(
          '设置失败',
          '无法设置默认模板',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red[600],
          colorText: Colors.white,
        );
      }
    } catch (e) {
      LoggerService.e('设置默认模板失败', e);
      Get.snackbar(
        '设置失败',
        '无法设置默认模板: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red[600],
        colorText: Colors.white,
      );
    }
  }

  /// 删除模板
  Future<void> deleteTemplate(PrintTemplateConfig template) async {
    try {
      // 显示确认对话框
      final confirmed = await _showDeleteConfirmDialog(template.name);
      if (!confirmed) return;
      
      LoggerService.d('🗑️ 删除模板: ${template.name}');
      
      final success = await _templateService.deleteTemplate(template.id);
      if (success) {
        await _loadTemplates();
        Get.snackbar(
          '删除成功',
          '模板"${template.name}"已删除',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green[600],
          colorText: Colors.white,
        );
      } else {
        Get.snackbar(
          '删除失败',
          '无法删除模板（可能是默认模板）',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red[600],
          colorText: Colors.white,
        );
      }
    } catch (e) {
      LoggerService.e('删除模板失败', e);
      Get.snackbar(
        '删除失败',
        '无法删除模板: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red[600],
        colorText: Colors.white,
      );
    }
  }

  /// 预览模板
  Future<void> previewTemplate(PrintTemplateConfig template) async {
    try {
      LoggerService.d('👁️ 预览模板: ${template.name}');
      
      // 构建示例数据
      final sampleData = _buildSampleData();
      
      // 使用PrintService进行预览
      final printService = PrintService();
      await printService.showReceiptPreview(
        stockOutData: sampleData['stockOutData'] as Map<String, dynamic>,
        paymentData: sampleData['paymentData'] as Map<String, dynamic>,
        items: sampleData['items'] as List<Map<String, dynamic>>,
        template: template,
      );
    } catch (e) {
      LoggerService.e('预览模板失败', e);
      Get.snackbar(
        '预览失败',
        '无法预览模板: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red[600],
        colorText: Colors.white,
      );
    }
  }

  /// 显示复制对话框
  Future<String?> _showCopyDialog(String originalName) async {
    final controller = TextEditingController(text: '$originalName - 副本');
    
    return await Get.dialog<String?>(
      AlertDialog(
        title: const Text('复制模板'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('请输入新模板的名称：'),
            const SizedBox(height: 16),
            TextField(
              controller: controller,
              decoration: const InputDecoration(
                labelText: '模板名称',
                border: OutlineInputBorder(),
              ),
              autofocus: true,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Get.back(result: controller.text.trim()),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  /// 显示删除确认对话框
  Future<bool> _showDeleteConfirmDialog(String templateName) async {
    final result = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除模板"$templateName"吗？\n此操作无法撤销。'),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Get.back(result: true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red[600],
              foregroundColor: Colors.white,
            ),
            child: const Text('删除'),
          ),
        ],
      ),
    );
    
    return result ?? false;
  }

  /// 构建示例数据
  Map<String, dynamic> _buildSampleData() {
    return {
      'stockOutData': {
        'order_no': 'CK20241201001',
        'customer': '张三',
        'sale_type': 'retail',
        'store_name': '金包银首饰店',
        'store_address': '广东省深圳市罗湖区黄金珠宝城A座101号',
        'store_phone': '0755-12345678',
        'remark': '示例订单',
      },
      'paymentData': {
        'payment_info': {
          'cash_amount': '800.00',
          'wechat_amount': '500.00',
          'alipay_amount': '0.00',
          'card_amount': '0.00',
          'discount_amount': '0.00',
          'actual_amount': '1300.00',
        },
      },
      'items': [
        {
          'barcode': 'JW001',
          'name': '黄金戒指',
          'product_name': '黄金戒指',
          'category': '戒指',
          'ring_size': '16号',
          'specification': '16号',
          'quantity': '1',
          'gold_weight': '5.20',
          'silver_weight': '0.00',
          'total_weight': '5.20',
          'gold_price': '450.00',
          'price': '450.00',
          'work_price': '30.00',
          'labor_cost': '30.00',
          'total_amount': '2496.00',
          'amount': '2496.00',
        },
        {
          'barcode': 'YS002',
          'name': '银手镯',
          'product_name': '银手镯',
          'category': '手镯',
          'ring_size': '',
          'specification': '',
          'quantity': '1',
          'gold_weight': '0.00',
          'silver_weight': '15.80',
          'total_weight': '15.80',
          'silver_price': '8.00',
          'price': '8.00',
          'work_price': '5.00',
          'labor_cost': '5.00',
          'total_amount': '205.40',
          'amount': '205.40',
        },
      ],
    };
  }
}
