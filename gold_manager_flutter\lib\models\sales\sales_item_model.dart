/// 销售明细项模型
class SalesItem {
  final int? id;
  final int? orderId;
  final int jewelryId;
  final String jewelryName;
  final String jewelryCode;
  final double goldWeight;
  final double silverWeight;
  final double price;
  final double amount;
  final int quantity;
  final String remark;

  const SalesItem({
    this.id,
    this.orderId,
    required this.jewelryId,
    required this.jewelryName,
    required this.jewelryCode,
    required this.goldWeight,
    required this.silverWeight,
    required this.price,
    required this.amount,
    required this.quantity,
    this.remark = '',
  });

  /// 从JSON构造
  factory SalesItem.fromJson(Map<String, dynamic> json) {
    return SalesItem(
      id: json['id'],
      orderId: json['order_id'],
      jewelryId: json['jewelry_id'],
      jewelryName: json['jewelry_name'],
      jewelryCode: json['jewelry_code'],
      goldWeight: json['gold_weight'].toDouble(),
      silverWeight: json['silver_weight'].toDouble(),
      price: json['price'].toDouble(),
      amount: json['amount'].toDouble(),
      quantity: json['quantity'],
      remark: json['remark'] ?? '',
    );
  }

  /// 转为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'order_id': orderId,
      'jewelry_id': jewelryId,
      'jewelry_name': jewelryName,
      'jewelry_code': jewelryCode,
      'gold_weight': goldWeight,
      'silver_weight': silverWeight,
      'price': price,
      'amount': amount,
      'quantity': quantity,
      'remark': remark,
    };
  }

  /// 复制对象并修改部分字段
  SalesItem copyWith({
    int? id,
    int? orderId,
    int? jewelryId,
    String? jewelryName,
    String? jewelryCode,
    double? goldWeight,
    double? silverWeight,
    double? price,
    double? amount,
    int? quantity,
    String? remark,
  }) {
    return SalesItem(
      id: id ?? this.id,
      orderId: orderId ?? this.orderId,
      jewelryId: jewelryId ?? this.jewelryId,
      jewelryName: jewelryName ?? this.jewelryName,
      jewelryCode: jewelryCode ?? this.jewelryCode,
      goldWeight: goldWeight ?? this.goldWeight,
      silverWeight: silverWeight ?? this.silverWeight,
      price: price ?? this.price,
      amount: amount ?? this.amount,
      quantity: quantity ?? this.quantity,
      remark: remark ?? this.remark,
    );
  }
} 