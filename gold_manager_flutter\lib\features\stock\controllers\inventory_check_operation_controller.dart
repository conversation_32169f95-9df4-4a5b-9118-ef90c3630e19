import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

import '../../../core/utils/logger.dart';
import '../../../core/constants/app_permissions.dart';
import '../../../core/services/audio_service.dart';
import '../../../services/auth_service.dart';
import '../services/inventory_check_service.dart';
import '../models/inventory_check.dart' as new_models;
// 使用现有的InventoryCheckItem模型

/// 库存盘点操作控制器
/// 
/// 负责盘点操作页面的业务逻辑，包括：
/// - 商品列表管理
/// - 扫码盘点
/// - 手动录入
/// - 差异计算
/// - 权限控制
class InventoryCheckOperationController extends GetxController {
  // 服务
  final InventoryCheckService _inventoryCheckService = Get.find<InventoryCheckService>();
  final AuthService _authService = Get.find<AuthService>();
  late final AudioService _audioService;

  // 路由参数
  late String checkId;

  // 状态变量
  final RxBool isLoading = false.obs;
  final Rx<new_models.InventoryCheck?> inventoryCheck = Rx<new_models.InventoryCheck?>(null);
  final RxList<new_models.InventoryCheckItem> allItems = <new_models.InventoryCheckItem>[].obs;
  final RxList<new_models.InventoryCheckItem> filteredItems = <new_models.InventoryCheckItem>[].obs;

  // 分页相关
  final RxInt currentPage = 1.obs;
  final RxInt totalPages = 1.obs;
  final RxInt pageSize = 50.obs; // 每页50条数据，适合盘点操作
  final RxBool hasMoreData = true.obs;
  final RxBool isLoadingMore = false.obs;

  // 筛选条件
  final TextEditingController searchController = TextEditingController();
  final RxnInt selectedStatus = RxnInt();

  // 统计信息
  final RxInt totalItems = 0.obs;
  final RxInt checkedItems = 0.obs;
  final RxInt differenceItems = 0.obs;

  /// 检查是否有权限
  bool get canCheck => _authService.hasPermission(AppPermissions.stockCheck);
  bool get canComplete => inventoryCheck.value?.status == 0 && canCheck;

  /// 是否为管理员
  bool get isAdmin => _authService.userRole.value == 'admin';

  @override
  void onInit() {
    super.onInit();

    // 获取全局音频服务实例
    try {
      _audioService = Get.find<AudioService>();
      LoggerService.d('✅ 音频服务获取成功');
    } catch (e) {
      LoggerService.e('❌ 音频服务获取失败', e);
      // 创建备用实例
      _audioService = AudioService();
      _audioService.initialize();
    }

    // 优先从路由参数获取checkId，如果没有则等待外部设置
    final routeCheckId = Get.parameters['id'] ?? '';
    if (routeCheckId.isNotEmpty) {
      checkId = routeCheckId;
      LoggerService.d('InventoryCheckOperationController 从路由参数初始化, checkId: $checkId');
      _initializeData();
    } else {
      LoggerService.d('InventoryCheckOperationController 等待外部设置checkId');
      // 在标签页模式下，checkId会由外部设置，然后调用initializeWithCheckId
    }
  }

  /// 使用指定的checkId初始化数据
  /// 用于标签页模式下的初始化
  void initializeWithCheckId(String newCheckId) {
    if (newCheckId.isEmpty) {
      Get.snackbar(
        '错误',
        '盘点单ID不能为空',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    checkId = newCheckId;
    LoggerService.d('InventoryCheckOperationController 使用外部checkId初始化: $checkId');
    _initializeData();
  }

  @override
  void onClose() {
    searchController.dispose();
    // 🔑 关键修复：不要释放全局音频服务，它是单例服务
    // _audioService.dispose(); // 移除这行，避免释放全局服务
    super.onClose();
  }

  /// 初始化数据
  void _initializeData() async {
    // 先获取盘点单详情，再获取商品列表，确保总页数计算正确
    await fetchInventoryCheckDetail();
    await fetchInventoryCheckItems();
    
    // 确保初始化完成后统计信息正确
    _calculateStatistics();
  }

  /// 获取盘点单详情
  Future<void> fetchInventoryCheckDetail() async {
    try {
      isLoading.value = true;
      
      final check = await _inventoryCheckService.getInventoryCheckDetail(int.parse(checkId));
      inventoryCheck.value = check;

      // 权限检查
      if (!canCheck) {
        Get.snackbar(
          '错误',
          '无权限访问此盘点单',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        Get.back();
        return;
      }

      // 门店权限检查
      if (!isAdmin) {
        if (check.storeId != _authService.storeId.value) {
          Get.snackbar(
            '错误',
            '无权限访问其他门店的盘点单',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
          Get.back();
          return;
        }
      }

      LoggerService.d('✅ 盘点单详情获取成功: ${check.checkNo}');
    } catch (e) {
      LoggerService.e('获取盘点单详情失败', e);
      Get.snackbar(
        '错误',
        '获取盘点单详情失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      Get.back();
    } finally {
      isLoading.value = false;
    }
  }

  /// 获取盘点商品列表（真正的分页加载）
  Future<void> fetchInventoryCheckItems({bool isRefresh = false}) async {
    try {
      isLoading.value = true;

      final items = await _inventoryCheckService.getInventoryCheckItemsPaginated(
        int.parse(checkId),
        page: currentPage.value,
        pageSize: pageSize.value,
      );

      // 直接设置当前页的数据，不累加
      allItems.value = items;
      filteredItems.value = items;

      // 计算总页数（基于盘点单总数量）
      if (inventoryCheck.value != null) {
        totalPages.value = (inventoryCheck.value!.totalCount / pageSize.value).ceil();
        LoggerService.d('📊 分页信息更新: 总商品数=${inventoryCheck.value!.totalCount}, 每页=${pageSize.value}, 总页数=${totalPages.value}');
      } else {
        LoggerService.w('⚠️ 盘点单详情为空，无法计算总页数');
        totalPages.value = 1;
      }

      _calculateStatistics();

      LoggerService.d('✅ 盘点商品列表获取成功: ${items.length} 件商品 (第${currentPage.value}页, 共${totalPages.value}页)');
    } catch (e) {
      LoggerService.e('获取盘点商品列表失败', e);
      Get.snackbar(
        '错误',
        '获取盘点商品列表失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// 加载更多数据
  Future<void> loadMoreItems() async {
    await fetchInventoryCheckItems(isRefresh: false);
  }

  /// 计算统计信息
  /// 优先使用后端返回的统计数据，确保数据一致性
  void _calculateStatistics() {
    LoggerService.d('🔄 开始计算统计信息...');

    // 如果有盘点单数据，优先使用后端统计
    if (inventoryCheck.value != null) {
      final oldTotal = totalItems.value;
      final oldChecked = checkedItems.value;
      final oldDifference = differenceItems.value;

      totalItems.value = inventoryCheck.value!.totalCount;
      checkedItems.value = inventoryCheck.value!.checkedCount;
      differenceItems.value = inventoryCheck.value!.differenceCount;

      LoggerService.d('📊 使用后端统计数据: 总计=${totalItems.value}($oldTotal→${totalItems.value}), 已盘=${checkedItems.value}($oldChecked→${checkedItems.value}), 差异=${differenceItems.value}($oldDifference→${differenceItems.value})');
    } else {
      // 备用方案：使用本地数据计算
      totalItems.value = allItems.length;
      checkedItems.value = allItems.where((item) => item.status == 1).length;
      differenceItems.value = allItems.where((item) => item.hasDifference).length;

      LoggerService.d('📊 使用本地数据计算: 总计=${totalItems.value}, 已盘=${checkedItems.value}, 差异=${differenceItems.value}');
    }
  }

  /// 搜索商品
  void searchItems() {
    filterItems();
  }

  /// 处理主界面扫码输入
  Future<void> handleMainScanInput(String barcode) async {
    if (barcode.trim().isEmpty) {
      Get.snackbar('提示', '请输入条码');
      return;
    }

    try {
      // 直接调用扫码盘点API
      await _handleBarcodeScanned(barcode.trim());

      // 盘点成功后清空输入框
      searchController.clear();

      // 数据已在_handleBarcodeScanned中优化更新，无需重复刷新

    } catch (e) {
      LoggerService.e('主界面扫码盘点失败', e);
      // 错误处理在_handleBarcodeScanned中已经处理
    }
  }

  /// 筛选商品
  void filterItems() {
    final keyword = searchController.text.trim().toLowerCase();
    final status = selectedStatus.value;

    filteredItems.value = allItems.where((item) {
      // 关键词筛选
      if (keyword.isNotEmpty) {
        final matchesKeyword = item.barcode.toLowerCase().contains(keyword) ||
            item.name.toLowerCase().contains(keyword);
        if (!matchesKeyword) return false;
      }

      // 状态筛选
      if (status != null) {
        switch (status) {
          case 0: // 未盘点
            return item.status == 0;
          case 1: // 已盘点
            return item.status == 1;
        }
      }

      return true;
    }).toList();
  }

  /// 开始扫码
  Future<void> startScan() async {
    try {
      // 显示扫码输入对话框
      await _showBarcodeInputDialog();
    } catch (e) {
      LoggerService.e('扫码失败', e);
      Get.snackbar(
        '错误',
        '扫码失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 显示条码输入对话框
  Future<void> _showBarcodeInputDialog() async {
    final barcodeController = TextEditingController();

    await Get.dialog(
      AlertDialog(
        title: const Text('扫码盘点'),
        content: SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('请扫描商品条码或手动输入：'),
              const SizedBox(height: 16),
              TextField(
                controller: barcodeController,
                autofocus: true,
                decoration: const InputDecoration(
                  labelText: '商品条码',
                  hintText: '请扫描或输入商品条码',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.qr_code_scanner),
                ),
                onSubmitted: (value) {
                  if (value.isNotEmpty) {
                    Get.back();
                    _handleBarcodeScanned(value);
                  }
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              final barcode = barcodeController.text.trim();
              if (barcode.isNotEmpty) {
                Get.back();
                _handleBarcodeScanned(barcode);
              } else {
                Get.snackbar('提示', '请输入商品条码');
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue[600],
              foregroundColor: Colors.white,
            ),
            child: const Text('确定'),
          ),
        ],
      ),
    );

    barcodeController.dispose();
  }

  /// 处理扫码结果
  Future<void> _handleBarcodeScanned(String barcode) async {
    final startTime = DateTime.now();
    LoggerService.d('🔍 ===== 扫码流程开始 ===== 时间: $startTime');
    LoggerService.d('🔍 扫码条码: $barcode');
    LoggerService.d('🔍 当前平台: ${Platform.operatingSystem}');

    try {
      LoggerService.d('🔍 步骤1: 开始处理扫码逻辑');

      // 🔑 关键修复：检查商品是否已经盘点过
      try {
        LoggerService.d('🔍 步骤2: 检查商品是否已盘点');
        final existingItem = allItems.firstWhere((item) => item.barcode == barcode);
        LoggerService.d('📦 找到商品: ${existingItem.name}, 状态: ${existingItem.status}');

        if (existingItem.status == 1) {
          // 商品已盘点，显示警告提示并返回
          LoggerService.w('⚠️ 商品已盘点，阻止重复操作');
          LoggerService.d('⚠️ 步骤3a: 显示重复扫码警告SnackBar');

          Get.snackbar(
            '⚠️ 商品已盘点',
            '条码: $barcode 已完成盘点，无需重复操作',
            snackPosition: SnackPosition.TOP,
            backgroundColor: Colors.orange[100],
            colorText: Colors.orange[800],
            duration: const Duration(seconds: 2),
            icon: const Icon(Icons.warning, color: Colors.orange),
          );

          LoggerService.d('⚠️ 步骤4a: 警告SnackBar显示完成，准备播放警告音');
          // 播放警告提示音
          await _playWarningSound();
          LoggerService.d('⚠️ 步骤5a: 警告音播放完成，退出扫码流程');
          return;
        }
      } catch (e) {
        // 商品不在当前页面数据中，继续后端查询
        LoggerService.d('📦 步骤2b: 商品不在当前页面数据中，继续后端查询');
      }

      // 继续正常的盘点流程
      LoggerService.d('✅ 步骤3: 商品状态正常，开始盘点API调用');
      final apiStartTime = DateTime.now();

      final updatedItem = await _inventoryCheckService.scanInventoryCheck(
        int.parse(checkId),
        barcode: barcode,
        actualStock: 1, // 扫码盘点自动设置为1
        remark: '扫码盘点',
      );

      final apiEndTime = DateTime.now();
      LoggerService.d('✅ 步骤4: API调用成功，耗时: ${apiEndTime.difference(apiStartTime).inMilliseconds}ms');
      LoggerService.d('✅ 返回商品信息: ID=${updatedItem.id}, 名称=${updatedItem.name}, 差异=${updatedItem.difference}');

      // 优化：只更新本地对应的商品数据，避免全量刷新
      LoggerService.d('📊 步骤5: 开始更新本地数据');
      _updateLocalItem(updatedItem);

      // 🎯 性能优化：移除自动表格刷新，避免阻碍音频播放
      // 只更新统计信息，不进行网络请求刷新
      LoggerService.d('📊 步骤6: 跳过表格刷新，只更新本地统计信息');

      // 直接从后端返回的数据更新统计信息
      if (inventoryCheck.value != null) {
        // 使用copyWith方法创建新对象，因为字段是final的
        final currentCheck = inventoryCheck.value!;
        final newCheckedCount = currentCheck.checkedCount + 1;

        // 如果有差异，更新差异数量
        final newDifferenceCount = updatedItem.difference != 0
            ? currentCheck.differenceCount + 1
            : currentCheck.differenceCount;

        LoggerService.d('📊 统计更新: 已盘 ${currentCheck.checkedCount} → $newCheckedCount, 差异 ${currentCheck.differenceCount} → $newDifferenceCount');

        // 创建更新后的盘点单对象
        inventoryCheck.value = currentCheck.copyWith(
          checkedCount: newCheckedCount,
          differenceCount: newDifferenceCount,
        );

        LoggerService.d('📊 本地统计更新完成: 已盘=$newCheckedCount, 差异=$newDifferenceCount');
      }

      // 重新计算统计信息，确保界面显示正确
      LoggerService.d('📊 步骤7: 重新计算统计信息');
      _calculateStatistics();

      LoggerService.d('📊 扫码成功后统计信息: 总计=${totalItems.value}, 已盘=${checkedItems.value}, 差异=${differenceItems.value}');

      // 注意：移除了自动搜索和表格刷新，提升扫码性能和音频播放体验
      // 用户可以通过搜索功能手动查看已盘点的商品

      // 先显示成功提示框
      LoggerService.d('🎉 步骤8: 显示成功SnackBar');
      final snackbarStartTime = DateTime.now();

      Get.snackbar(
        '✅ 盘点成功',
        '条码: $barcode 已完成盘点',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.green[100],
        colorText: Colors.green[800],
        duration: const Duration(seconds: 2),
        icon: const Icon(Icons.check_circle, color: Colors.green),
      );

      final snackbarEndTime = DateTime.now();
      LoggerService.d('🎉 步骤9: SnackBar显示调用完成，耗时: ${snackbarEndTime.difference(snackbarStartTime).inMilliseconds}ms');

      // 🔑 关键修复：在所有UI操作完成后播放提示音，避免被SnackBar阻塞
      // 添加小延迟确保SnackBar完全显示后再播放音频
      LoggerService.d('🔊 步骤10: 等待SnackBar显示完成，延迟100ms...');
      final delayStartTime = DateTime.now();
      await Future.delayed(const Duration(milliseconds: 100));
      final delayEndTime = DateTime.now();
      LoggerService.d('🔊 步骤11: 延迟完成，实际耗时: ${delayEndTime.difference(delayStartTime).inMilliseconds}ms');

      LoggerService.d('🔊 步骤12: 开始播放成功提示音（在UI操作后）...');
      final audioStartTime = DateTime.now();

      // 先进行一次简单的音频测试
      LoggerService.d('🧪 步骤12a: 进行音频功能测试');
      await _testSystemSoundDirectly();

      LoggerService.d('🔊 步骤12b: 调用正式的音频播放方法');
      await _playSuccessSound();
      final audioEndTime = DateTime.now();
      LoggerService.d('✅ 步骤13: 音频播放完成，耗时: ${audioEndTime.difference(audioStartTime).inMilliseconds}ms');

      final totalTime = audioEndTime.difference(startTime);
      LoggerService.d('🎯 ===== 扫码流程完成 ===== 总耗时: ${totalTime.inMilliseconds}ms');

    } catch (e, stackTrace) {
      final errorTime = DateTime.now();
      LoggerService.e('❌ ===== 扫码流程异常 ===== 时间: $errorTime');
      LoggerService.e('❌ 异常类型: ${e.runtimeType}');
      LoggerService.e('❌ 异常消息: ${e.toString()}');
      LoggerService.e('❌ 扫码盘点失败', e);
      LoggerService.e('❌ 完整异常堆栈:', stackTrace);

      // 播放失败提示音
      LoggerService.d('🔊 异常处理: 开始播放失败提示音');
      await _playErrorSound();
      LoggerService.d('🔊 异常处理: 失败提示音播放完成');

      // 根据错误类型显示不同的提示信息
      String errorTitle = '❌ 盘点失败';
      String errorMessage = '';

      if (e.toString().contains('不在当前盘点单中')) {
        errorTitle = '❌ 条码不在盘点单中';
        errorMessage = '条码: $barcode 不在当前盘点单中';
      } else if (e.toString().contains('状态为')) {
        errorTitle = '❌ 商品状态异常';
        errorMessage = '该商品状态异常，无法进行盘点';
      } else if (e.toString().contains('只能盘点上架在售的商品')) {
        errorTitle = '❌ 商品状态不符合';
        errorMessage = '只能盘点上架在售状态的商品';
      } else {
        errorMessage = '条码: $barcode 盘点失败';
      }

      Get.snackbar(
        errorTitle,
        errorMessage,
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red[100],
        colorText: Colors.red[800],
        duration: const Duration(seconds: 3),
        icon: const Icon(Icons.error, color: Colors.red),
      );

      // 重新抛出异常，让调用方知道操作失败
      rethrow;
    }
  }

  /// 更新本地商品数据
  void _updateLocalItem(new_models.InventoryCheckItem updatedItem) {
    // 在allItems中找到对应的商品并更新
    final index = allItems.indexWhere((item) => item.id == updatedItem.id);
    if (index != -1) {
      allItems[index] = updatedItem;
    }

    // 优化：扫码后不自动筛选表格，保持当前显示状态
    // 只更新filteredItems中对应的商品数据，避免表格重新筛选
    final filteredIndex = filteredItems.indexWhere((item) => item.id == updatedItem.id);
    if (filteredIndex != -1) {
      filteredItems[filteredIndex] = updatedItem;
    }

    // 只计算统计信息，不重新筛选
    _calculateStatistics();
  }

  /// 检查系统音频状态
  void _checkSystemAudioStatus() {
    try {
      LoggerService.d('🔧 系统状态检查开始');
      LoggerService.d('🔧 操作系统: ${Platform.operatingSystem}');
      LoggerService.d('🔧 操作系统版本: ${Platform.operatingSystemVersion}');
      LoggerService.d('🔧 是否Windows: ${Platform.isWindows}');
      LoggerService.d('🔧 当前时间戳: ${DateTime.now().millisecondsSinceEpoch}');
      LoggerService.d('🔧 SystemSoundType.click值: ${SystemSoundType.click}');
      LoggerService.d('🔧 SystemSoundType.alert值: ${SystemSoundType.alert}');
      LoggerService.d('🔧 系统状态检查完成');
    } catch (e) {
      LoggerService.e('🔧 系统状态检查异常', e);
    }
  }

  /// 直接测试SystemSound功能
  Future<void> _testSystemSoundDirectly() async {
    try {
      LoggerService.d('🧪 直接音频测试开始');
      final testStartTime = DateTime.now();

      LoggerService.d('🧪 测试1: 直接调用SystemSound.play(SystemSoundType.click)');
      await SystemSound.play(SystemSoundType.click);
      LoggerService.d('🧪 测试1: SystemSound.play调用完成');

      await Future.delayed(const Duration(milliseconds: 200));
      LoggerService.d('🧪 测试1: 延迟200ms完成');

      final testEndTime = DateTime.now();
      LoggerService.d('🧪 直接音频测试完成，耗时: ${testEndTime.difference(testStartTime).inMilliseconds}ms');
    } catch (e, stackTrace) {
      LoggerService.e('🧪 直接音频测试失败', e);
      LoggerService.e('🧪 测试异常堆栈:', stackTrace);
    }
  }

  /// 播放成功提示音
  Future<void> _playSuccessSound() async {
    final methodStartTime = DateTime.now();
    LoggerService.d('🔊 ===== 音频播放方法开始 ===== 时间: $methodStartTime');

    // 检查系统状态
    _checkSystemAudioStatus();

    try {
      // 🎯 Windows平台专用音频播放方案
      if (Platform.isWindows) {
        LoggerService.d('🔊 Windows平台: 使用PowerShell播放系统音效');
        await _playWindowsSystemSound();
      } else {
        LoggerService.d('🔊 非Windows平台: 使用SystemSound.play');
        await SystemSound.play(SystemSoundType.click);
        await Future.delayed(const Duration(milliseconds: 500));
      }

      final totalDuration = DateTime.now().difference(methodStartTime);
      LoggerService.d('✅ 音频播放方法完成，总耗时: ${totalDuration.inMilliseconds}ms');

    } catch (e, stackTrace) {
      LoggerService.e('❌ 播放成功提示音异常', e);
      LoggerService.e('❌ 异常类型: ${e.runtimeType}');
      LoggerService.e('❌ 异常消息: ${e.toString()}');
      LoggerService.e('❌ 完整异常堆栈:', stackTrace);

      // 备用方案：尝试SystemSound
      try {
        LoggerService.d('🔧 备用方案: 尝试SystemSound.alert...');
        await SystemSound.play(SystemSoundType.alert);
        await Future.delayed(const Duration(milliseconds: 500));
        LoggerService.d('✅ 备用SystemSound播放成功');
      } catch (e2, stackTrace2) {
        LoggerService.e('❌ 备用方案也失败', e2);
        LoggerService.e('❌ 备用方案异常堆栈:', stackTrace2);
      }
    }
  }

  /// Windows平台专用音频播放
  Future<void> _playWindowsSystemSound() async {
    try {
      LoggerService.d('🔊 Windows音频步骤1: 准备调用PowerShell播放成功音效');
      final startTime = DateTime.now();

      // 🎯 使用自定义频率的蜂鸣声作为成功提示音（高频清脆）
      final result = await Process.run(
        'powershell',
        [
          '-Command',
          '[System.Console]::Beep(1000, 150)'
        ],
        runInShell: true,
      );

      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);

      LoggerService.d('🔊 Windows音频步骤2: PowerShell调用完成');
      LoggerService.d('🔊 PowerShell退出码: ${result.exitCode}');
      LoggerService.d('🔊 PowerShell输出: ${result.stdout}');
      if (result.stderr.isNotEmpty) {
        LoggerService.w('🔊 PowerShell错误: ${result.stderr}');
      }
      LoggerService.d('🔊 Windows成功音效播放耗时: ${duration.inMilliseconds}ms');

      if (result.exitCode == 0) {
        LoggerService.d('✅ Windows成功音效播放成功');
      } else {
        LoggerService.w('⚠️ Windows成功音效可能播放失败，退出码: ${result.exitCode}');
      }

    } catch (e, stackTrace) {
      LoggerService.e('❌ Windows音频播放异常', e);
      LoggerService.e('❌ Windows音频异常堆栈:', stackTrace);
      rethrow;
    }
  }

  /// 播放失败提示音
  Future<void> _playErrorSound() async {
    try {
      LoggerService.d('🔊 ===== 开始播放失败提示音 =====');

      if (Platform.isWindows) {
        LoggerService.d('🔊 Windows平台: 播放错误音效');
        await _playWindowsErrorSound();
      } else {
        LoggerService.d('🔊 非Windows平台: 使用SystemSound.alert');
        await SystemSound.play(SystemSoundType.alert);
        await Future.delayed(const Duration(milliseconds: 500));
      }

      LoggerService.d('✅ 失败提示音播放完成');

    } catch (e) {
      LoggerService.e('❌ 播放失败提示音异常', e);
      try {
        await SystemSound.play(SystemSoundType.click);
        await Future.delayed(const Duration(milliseconds: 500));
        LoggerService.d('✅ 备用click音效播放成功');
      } catch (e2) {
        LoggerService.e('❌ 所有失败音效都失败', e2);
      }
    }
  }

  /// 播放警告提示音
  Future<void> _playWarningSound() async {
    try {
      LoggerService.d('🔊 ===== 开始播放警告提示音 =====');

      if (Platform.isWindows) {
        LoggerService.d('🔊 Windows平台: 播放警告音效');
        await _playWindowsWarningSound();
      } else {
        LoggerService.d('🔊 非Windows平台: 使用SystemSound.alert');
        await SystemSound.play(SystemSoundType.alert);
        await Future.delayed(const Duration(milliseconds: 500));
      }

      LoggerService.d('✅ 警告提示音播放完成');

    } catch (e) {
      LoggerService.e('❌ 播放警告提示音异常', e);
      try {
        await SystemSound.play(SystemSoundType.click);
        await Future.delayed(const Duration(milliseconds: 500));
        LoggerService.d('✅ 备用click音效播放成功');
      } catch (e2) {
        LoggerService.e('❌ 所有警告音效都失败', e2);
      }
    }
  }

  /// Windows平台错误音效
  Future<void> _playWindowsErrorSound() async {
    try {
      final result = await Process.run(
        'powershell',
        ['-Command', '[System.Media.SystemSounds]::Hand.Play()'],
        runInShell: true,
      );
      LoggerService.d('🔊 Windows错误音效播放完成，退出码: ${result.exitCode}');
    } catch (e) {
      LoggerService.e('❌ Windows错误音效播放失败', e);
      rethrow;
    }
  }

  /// Windows平台警告音效
  Future<void> _playWindowsWarningSound() async {
    try {
      final result = await Process.run(
        'powershell',
        ['-Command', '[System.Media.SystemSounds]::Exclamation.Play()'],
        runInShell: true,
      );
      LoggerService.d('🔊 Windows警告音效播放完成，退出码: ${result.exitCode}');
    } catch (e) {
      LoggerService.e('❌ Windows警告音效播放失败', e);
      rethrow;
    }
  }





  /// 显示批量操作对话框
  Future<void> showBatchOperationDialog() async {
    try {
      // 获取未盘点的商品
      final unCheckedItems = allItems.where((item) => item.status == 0).toList();

      if (unCheckedItems.isEmpty) {
        Get.snackbar(
          '提示',
          '没有未盘点的商品',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.orange,
          colorText: Colors.white,
        );
        return;
      }

      await _showBatchCheckDialog(unCheckedItems);
    } catch (e) {
      LoggerService.e('批量操作失败', e);
      Get.snackbar(
        '错误',
        '批量操作失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 显示批量盘点对话框
  Future<void> _showBatchCheckDialog(List<new_models.InventoryCheckItem> items) async {
    final selectedItems = <new_models.InventoryCheckItem>[].obs;

    await Get.dialog(
      AlertDialog(
        title: const Text('批量盘点'),
        content: SizedBox(
          width: 600,
          height: 400,
          child: Column(
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text('共 ${items.length} 件未盘点商品，请选择要批量盘点的商品：'),
                  ),
                  TextButton(
                    onPressed: () {
                      if (selectedItems.length == items.length) {
                        selectedItems.clear();
                      } else {
                        selectedItems.assignAll(items);
                      }
                    },
                    child: Obx(() => Text(
                      selectedItems.length == items.length ? '取消全选' : '全选',
                    )),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Expanded(
                child: ListView.builder(
                  itemCount: items.length,
                  itemBuilder: (context, index) {
                    final item = items[index];
                    return Obx(() => CheckboxListTile(
                      title: Text(item.name),
                      subtitle: Text('条码: ${item.barcode} | 系统库存: ${item.systemStock}'),
                      value: selectedItems.contains(item),
                      onChanged: (checked) {
                        if (checked == true) {
                          selectedItems.add(item);
                        } else {
                          selectedItems.remove(item);
                        }
                      },
                    ));
                  },
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          Obx(() => ElevatedButton(
            onPressed: selectedItems.isNotEmpty
                ? () {
                    Get.back();
                    _batchCheckItems(selectedItems.toList());
                  }
                : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue[600],
              foregroundColor: Colors.white,
            ),
            child: Text('盘点选中商品 (${selectedItems.length})'),
          )),
        ],
      ),
    );
  }

  /// 批量盘点商品
  Future<void> _batchCheckItems(List<new_models.InventoryCheckItem> items) async {
    try {
      // 显示确认对话框
      final confirmed = await Get.dialog<bool>(
        AlertDialog(
          title: const Text('确认批量盘点'),
          content: Text('确定要将选中的 ${items.length} 件商品的实际库存设置为与系统库存一致吗？'),
          actions: [
            TextButton(
              onPressed: () => Get.back(result: false),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () => Get.back(result: true),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue[600],
                foregroundColor: Colors.white,
              ),
              child: const Text('确定'),
            ),
          ],
        ),
      );

      if (confirmed != true) return;

      // 批量盘点
      for (final item in items) {
        await checkItemWithRemark(item, item.systemStock, '批量盘点');
        // 添加小延迟避免请求过快
        await Future.delayed(const Duration(milliseconds: 100));
      }

      Get.snackbar(
        '成功',
        '批量盘点完成，共处理 ${items.length} 件商品',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      LoggerService.e('批量盘点失败', e);
      Get.snackbar(
        '错误',
        '批量盘点失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 盘点商品
  Future<void> checkItem(new_models.InventoryCheckItem item, int actualQuantity) async {
    try {
      final updatedItem = await _inventoryCheckService.checkInventoryItem(
        int.parse(checkId),
        item.id,
        actualQuantity,
      );

      // 优化：只更新本地对应的商品数据
      _updateLocalItem(updatedItem);

      // 只刷新盘点单统计信息
      await fetchInventoryCheckDetail();

      Get.snackbar(
        '成功',
        '商品盘点成功',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      LoggerService.e('盘点商品失败', e);
      Get.snackbar(
        '错误',
        '盘点商品失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 盘点商品（带备注）
  Future<void> checkItemWithRemark(new_models.InventoryCheckItem item, int actualQuantity, String? remark) async {
    try {
      final updatedItem = await _inventoryCheckService.checkInventoryItemWithRemark(
        int.parse(checkId),
        item.id,
        actualQuantity,
        remark,
      );

      // 优化：只更新本地对应的商品数据
      _updateLocalItem(updatedItem);

      // 只刷新盘点单统计信息
      await fetchInventoryCheckDetail();

      Get.snackbar(
        '成功',
        '商品盘点成功',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      LoggerService.e('盘点商品失败', e);
      Get.snackbar(
        '错误',
        '盘点商品失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 完成盘点
  Future<void> completeInventoryCheck() async {
    if (!canComplete) {
      Get.snackbar(
        '提示',
        '无法完成盘点',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      return;
    }

    final confirmed = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('确认完成盘点'),
        content: Text('确定要完成盘点单 ${inventoryCheck.value?.checkNo} 吗？完成后将无法再修改。'),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            child: const Text('确定'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _inventoryCheckService.completeInventoryCheck(int.parse(checkId));

        Get.snackbar(
          '成功',
          '盘点单已完成',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );

        // 返回上一页并刷新
        Get.back(result: true);
      } catch (e) {
        LoggerService.e('完成盘点失败', e);
        Get.snackbar(
          '错误',
          '完成盘点失败: ${e.toString()}',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    }
  }

  /// 跳转到指定页面
  Future<void> goToPage(int page) async {
    if (page < 1 || page > totalPages.value || page == currentPage.value) {
      return;
    }

    currentPage.value = page;
    await fetchInventoryCheckItems();
  }

  /// 刷新当前页面
  Future<void> refreshCurrentPage() async {
    await fetchInventoryCheckItems();
  }
}
