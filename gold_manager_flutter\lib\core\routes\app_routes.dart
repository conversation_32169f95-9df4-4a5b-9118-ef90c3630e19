part of 'app_pages.dart';

/// 应用路由常量
class Routes {
  /// 启动页
  static const SPLASH = '/splash';

  /// 登录页
  static const LOGIN = '/login';

  /// 仪表盘
  static const DASHBOARD = '/dashboard';

  /// 首饰管理
  static const JEWELRY = '/jewelry';

  /// 库存管理
  static const STOCK = '/stock';

  /// 库存查询
  static const STOCK_QUERY = '/stock/query';

  /// 新建库存调拨表单
  static const STORE_TRANSFER_FORM = '/stock/store-transfer/form';

  /// 旧料回收管理
  static const RECYCLING = '/recycling';

  /// 销售管理
  static const SALES = '/sales';

  /// 报表统计
  static const REPORT = '/report';

  /// 系统设置
  static const SETTINGS = '/settings';

  /// 用户管理
  static const USER_MANAGE = '/user_manage';

  /// 角色管理
  static const ROLE_MANAGE = '/role_manage';

  /// 价格管理
  static const PRICE_MANAGE = '/price_manage';
}

/// 应用路由定义
class AppRoutes {
  /// 销售模块相关路由 - 暂时注释掉，等待相关页面实现
  // static List<GetPage> salesRoutes = [
  //   // 销售退货列表
  //   GetPage(
  //     name: '/sales/return',
  //     page: () => const SalesReturnListView(),
  //     binding: SalesBinding(),
  //   ),
  //
  //   // 创建销售退货
  //   GetPage(
  //     name: '/sales/return/create',
  //     page: () => const SalesReturnFormView(),
  //     binding: SalesBinding(),
  //   ),
  //
  //   // 编辑销售退货
  //   GetPage(
  //     name: '/sales/return/edit/:id',
  //     page: () => const SalesReturnFormView(),
  //     binding: SalesBinding(),
  //   ),
  //
  //   // 选择销售订单
  //   GetPage(
  //     name: '/sales/order/select',
  //     page: () => const SalesOrderSelectView(),
  //     binding: SalesBinding(),
  //   ),
  // ];
}