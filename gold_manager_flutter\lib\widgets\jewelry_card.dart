import 'package:flutter/material.dart';

import '../core/theme/app_theme.dart';
import '../core/widgets/standard_card.dart';
import '../models/common/enums.dart';
import '../models/jewelry/jewelry.dart';

/// 首饰卡片组件
/// 用于在列表中展示首饰信息
class JewelryCard extends StatelessWidget {
  /// 首饰数据
  final Jewelry jewelry;

  /// 点击事件
  final VoidCallback? onTap;

  const JewelryCard({
    super.key,
    required this.jewelry,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return StandardCard(
      padding: EdgeInsets.zero,
      onTap: onTap,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 首饰图片
          Expanded(
            flex: 6,
            child: Container(
              width: double.infinity,
              decoration: const BoxDecoration(
                color: AppTheme.backgroundColor,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(AppTheme.borderRadius),
                  topRight: Radius.circular(AppTheme.borderRadius),
                ),
              ),
              child: ClipRRect(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(AppTheme.borderRadius),
                  topRight: Radius.circular(AppTheme.borderRadius),
                ),
                child: jewelry.images != null && jewelry.images!.isNotEmpty
                    ? Image.network(
                        jewelry.images!.first.url,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return _buildPlaceholderImage(Icons.image_not_supported);
                        },
                      )
                    : _buildPlaceholderImage(Icons.diamond_outlined),
              ),
            ),
          ),

          // 首饰信息
          Expanded(
            flex: 4,
            child: Padding(
              padding: const EdgeInsets.all(AppTheme.paddingMedium),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // 首饰名称
                  Text(
                    jewelry.name,
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: AppTheme.captionSize,
                      color: AppTheme.primaryTextColor,
                      height: 1.3,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),

                  const SizedBox(height: AppTheme.paddingSmall),

                  // 重量信息
                  Row(
                    children: [
                      Expanded(
                        child: _buildWeightInfo('金', jewelry.goldWeight),
                      ),
                      const SizedBox(width: AppTheme.paddingSmall),
                      Expanded(
                        child: _buildWeightInfo('银', jewelry.silverWeight),
                      ),
                    ],
                  ),

                  const SizedBox(height: AppTheme.paddingSmall),

                  // 价格信息
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '¥${jewelry.salePrice.toStringAsFixed(0)}',
                        style: const TextStyle(
                          color: AppTheme.primaryColor,
                          fontWeight: FontWeight.bold,
                          fontSize: AppTheme.bodySize,
                        ),
                      ),
                      // 状态标签
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: AppTheme.paddingSmall,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: _getStatusColor(jewelry.status).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
                          border: Border.all(
                            color: _getStatusColor(jewelry.status).withValues(alpha: 0.3),
                            width: 0.5,
                          ),
                        ),
                        child: Text(
                          jewelry.status.label,
                          style: TextStyle(
                            color: _getStatusColor(jewelry.status),
                            fontSize: AppTheme.smallSize,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建占位图片
  Widget _buildPlaceholderImage(IconData icon) {
    return Center(
      child: Icon(
        icon,
        color: AppTheme.disabledTextColor,
        size: 40,
      ),
    );
  }

  /// 构建重量信息
  Widget _buildWeightInfo(String label, double weight) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.paddingSmall,
        vertical: 2,
      ),
      decoration: BoxDecoration(
        color: AppTheme.backgroundColor,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
      ),
      child: Text(
        '$label: ${weight}g',
        style: const TextStyle(
          fontSize: AppTheme.smallSize,
          color: AppTheme.secondaryTextColor,
          fontWeight: FontWeight.w500,
        ),
        textAlign: TextAlign.center,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  /// 获取状态颜色
  Color _getStatusColor(JewelryStatus status) {
    switch (status) {
      case JewelryStatus.offShelf:
        return AppTheme.infoColor;
      case JewelryStatus.onShelf:
        return AppTheme.successColor;
      case JewelryStatus.pendingOut:
        return AppTheme.warningColor;
    }
  }


}