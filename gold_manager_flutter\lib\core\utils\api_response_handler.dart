import '../utils/logger_service.dart';

/// API响应处理工具类
/// 统一处理不同格式的API响应，提供健壮的错误处理
class ApiResponseHandler {
  /// 处理列表类型的API响应
  /// 支持多种响应格式：
  /// 1. {success: true, data: [...]}
  /// 2. {code: 0, data: [...]}
  /// 3. {items: [...]}
  /// 4. 直接返回数组 [...]
  static List<T> handleListResponse<T>(
    dynamic responseData,
    T Function(Map<String, dynamic>) converter, {
    String errorContext = 'API请求',
  }) {
    if (responseData == null) {
      LoggerService.w('$errorContext: API响应数据为空，返回空列表');
      return [];
    }

    List<dynamic> itemsData = [];

    if (responseData is Map<String, dynamic>) {
      // 格式1: {success: true, data: [...]}
      if (responseData.containsKey('success') && responseData['success'] == true) {
        itemsData = responseData['data'] ?? [];
      }
      // 格式2: {code: 0, data: [...]}
      else if (responseData.containsKey('code') && responseData['code'] == 0) {
        itemsData = responseData['data'] ?? [];
      }
      // 格式3: {items: [...]}
      else if (responseData.containsKey('items')) {
        itemsData = responseData['items'] ?? [];
      }
      // 格式4: {list: [...]}
      else if (responseData.containsKey('list')) {
        itemsData = responseData['list'] ?? [];
      }
      // 检查是否有错误信息
      else {
        final errorMessage = _extractErrorMessage(responseData);
        if (errorMessage != null) {
          throw Exception(errorMessage);
        }
        // 如果没有错误信息，可能是未知格式，记录警告
        LoggerService.w('$errorContext: 未知的响应格式，返回空列表。响应数据: $responseData');
        return [];
      }
    } else if (responseData is List) {
      // 格式5: 直接返回数组
      itemsData = responseData;
    } else {
      LoggerService.w('$errorContext: 响应数据类型不支持，返回空列表。数据类型: ${responseData.runtimeType}');
      return [];
    }

    // 安全地转换数据
    final List<T> result = [];
    for (int i = 0; i < itemsData.length; i++) {
      final item = itemsData[i];
      try {
        if (item is Map<String, dynamic>) {
          result.add(converter(item));
        } else {
          LoggerService.w('$errorContext: 跳过非Map类型的数据项 [$i]: $item');
        }
      } catch (e) {
        LoggerService.w('$errorContext: 跳过无效的数据项 [$i]: $item, 错误: $e');
      }
    }

    LoggerService.d('$errorContext: 成功处理列表响应，共 ${result.length} 条数据');
    return result;
  }

  /// 处理对象类型的API响应
  /// 支持多种响应格式：
  /// 1. {success: true, data: {...}}
  /// 2. {code: 0, data: {...}}
  /// 3. 直接返回对象 {...}
  static T? handleObjectResponse<T>(
    dynamic responseData,
    T Function(Map<String, dynamic>) converter, {
    String errorContext = 'API请求',
    T? defaultValue,
  }) {
    if (responseData == null) {
      LoggerService.w('$errorContext: API响应数据为空，返回默认值');
      return defaultValue;
    }

    Map<String, dynamic>? objectData;

    if (responseData is Map<String, dynamic>) {
      // 格式1: {success: true, data: {...}}
      if (responseData.containsKey('success') && responseData['success'] == true) {
        final data = responseData['data'];
        if (data is Map<String, dynamic>) {
          objectData = data;
        }
      }
      // 格式2: {code: 0, data: {...}}
      else if (responseData.containsKey('code') && responseData['code'] == 0) {
        final data = responseData['data'];
        if (data is Map<String, dynamic>) {
          objectData = data;
        }
      }
      // 格式3: 直接返回对象，检查是否包含业务数据字段
      else if (_isBusinessDataObject(responseData)) {
        objectData = responseData;
      }
      // 检查是否有错误信息
      else {
        final errorMessage = _extractErrorMessage(responseData);
        if (errorMessage != null) {
          throw Exception(errorMessage);
        }
        // 如果没有错误信息，可能是未知格式
        LoggerService.w('$errorContext: 未知的响应格式，返回默认值。响应数据: $responseData');
        return defaultValue;
      }
    } else {
      LoggerService.w('$errorContext: 响应数据不是Map类型，返回默认值。数据类型: ${responseData.runtimeType}');
      return defaultValue;
    }

    if (objectData == null) {
      LoggerService.w('$errorContext: 无法提取对象数据，返回默认值');
      return defaultValue;
    }

    try {
      final result = converter(objectData);
      LoggerService.d('$errorContext: 成功处理对象响应');
      return result;
    } catch (e) {
      LoggerService.e('$errorContext: 转换对象数据失败', e);
      if (defaultValue != null) {
        return defaultValue;
      }
      rethrow;
    }
  }

  /// 提取错误信息
  static String? _extractErrorMessage(Map<String, dynamic> responseData) {
    // 常见的错误字段名
    final errorFields = ['message', 'msg', 'error', 'error_message', 'errorMessage'];
    
    for (final field in errorFields) {
      final errorMessage = responseData[field];
      if (errorMessage != null && errorMessage.toString().isNotEmpty) {
        return errorMessage.toString();
      }
    }
    
    return null;
  }

  /// 判断是否是业务数据对象
  /// 通过检查是否包含常见的业务字段来判断
  static bool _isBusinessDataObject(Map<String, dynamic> data) {
    // 常见的业务数据字段
    final businessFields = [
      'id', 'name', 'title', 'code', 'type', 'status', 'created_at', 'updated_at',
      'total_items', 'total_amount', 'count', 'items', 'list'
    ];
    
    // 如果包含任何业务字段，认为是业务数据对象
    return businessFields.any((field) => data.containsKey(field));
  }

  /// 处理网络错误
  static String getNetworkErrorMessage(dynamic error) {
    if (error == null) return '未知错误';
    
    final errorStr = error.toString();
    
    // 网络相关错误
    if (errorStr.contains('SocketException')) {
      return '网络连接失败，请检查网络设置';
    }
    if (errorStr.contains('TimeoutException')) {
      return '请求超时，请稍后重试';
    }
    if (errorStr.contains('Connection refused')) {
      return '服务器连接被拒绝，请联系管理员';
    }
    if (errorStr.contains('No route to host')) {
      return '无法连接到服务器，请检查网络';
    }
    if (errorStr.contains('Connection timed out')) {
      return '连接超时，请检查网络连接';
    }
    
    // HTTP错误
    if (errorStr.contains('404')) {
      return '请求的资源不存在';
    }
    if (errorStr.contains('500')) {
      return '服务器内部错误，请联系管理员';
    }
    if (errorStr.contains('401')) {
      return '身份验证失败，请重新登录';
    }
    if (errorStr.contains('403')) {
      return '权限不足，无法访问该资源';
    }
    
    // API错误
    if (errorStr.contains('Exception:')) {
      return errorStr.split('Exception:').last.trim();
    }
    
    // 其他错误，截断过长的错误信息
    return errorStr.length > 100 ? '${errorStr.substring(0, 100)}...' : errorStr;
  }

  /// 检查是否是网络错误
  static bool isNetworkError(dynamic error) {
    if (error == null) return false;
    
    final errorStr = error.toString();
    return errorStr.contains('SocketException') ||
           errorStr.contains('TimeoutException') ||
           errorStr.contains('Connection refused') ||
           errorStr.contains('No route to host') ||
           errorStr.contains('Connection timed out');
  }
}