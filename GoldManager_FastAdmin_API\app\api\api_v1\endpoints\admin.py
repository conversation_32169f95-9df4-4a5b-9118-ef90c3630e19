"""
管理员API端点
提供管理员的CRUD操作、密码管理和统计功能
"""

from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from ....core.database import get_db
from ....core.dependencies import get_current_user
from ....schemas.auth import CurrentUserResponse
from ....schemas.admin import (
    AdminCreate,
    AdminUpdate,
    AdminResponse,
    AdminListResponse,
    AdminStatistics,
    AdminPasswordUpdate,
    AdminLoginInfo
)
from ....services.admin_service import AdminService

# 创建路由器
router = APIRouter()


@router.get("", response_model=AdminListResponse)
async def get_admins(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    status: Optional[str] = Query(None, description="状态筛选:normal=正常,hidden=禁用"),
    store_id: Optional[int] = Query(None, description="门店筛选"),
    keyword: Optional[str] = Query(None, description="搜索关键词(用户名、昵称、邮箱、手机号)"),
    db: Session = Depends(get_db),
    current_user: CurrentUserResponse = Depends(get_current_user)
):
    """
    获取管理员列表

    支持以下筛选条件:
    - status: 按状态筛选(normal=正常,hidden=禁用)
    - store_id: 按门店筛选
    - keyword: 按用户名、昵称、邮箱或手机号搜索
    """
    service = AdminService(db)
    return await service.get_admins(
        page=page,
        page_size=page_size,
        status=status,
        store_id=store_id,
        keyword=keyword
    )


@router.get("/statistics", response_model=AdminStatistics)
async def get_admin_statistics(
    db: Session = Depends(get_db),
    current_user: CurrentUserResponse = Depends(get_current_user)
):
    """获取管理员统计信息"""
    service = AdminService(db)
    return await service.get_admin_statistics()


@router.get("/{admin_id}", response_model=AdminResponse)
async def get_admin(
    admin_id: int,
    db: Session = Depends(get_db),
    current_user: CurrentUserResponse = Depends(get_current_user)
):
    """获取单个管理员详情"""
    service = AdminService(db)
    admin = await service.get_admin_by_id(admin_id)
    if not admin:
        raise HTTPException(status_code=404, detail="管理员不存在")
    return admin


@router.post("", response_model=AdminResponse)
async def create_admin(
    admin_data: AdminCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUserResponse = Depends(get_current_user)
):
    """创建新管理员"""
    service = AdminService(db)

    try:
        return await service.create_admin(admin_data)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="创建管理员失败")


@router.put("/{admin_id}", response_model=AdminResponse)
async def update_admin(
    admin_id: int,
    admin_data: AdminUpdate,
    db: Session = Depends(get_db)
):
    """更新管理员信息"""
    service = AdminService(db)

    try:
        admin = await service.update_admin(admin_id, admin_data)
        if not admin:
            raise HTTPException(status_code=404, detail="管理员不存在")
        return admin
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="更新管理员失败")


@router.delete("/{admin_id}")
async def delete_admin(
    admin_id: int,
    db: Session = Depends(get_db)
):
    """删除管理员"""
    service = AdminService(db)

    try:
        success = await service.delete_admin(admin_id)
        if not success:
            raise HTTPException(status_code=404, detail="管理员不存在")
        return {"message": "管理员删除成功"}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="删除管理员失败")


@router.patch("/{admin_id}/status", response_model=AdminResponse)
async def update_admin_status(
    admin_id: int,
    status: str = Query(..., description="状态:normal=正常,hidden=禁用"),
    db: Session = Depends(get_db)
):
    """更新管理员状态"""
    if status not in ['normal', 'hidden']:
        raise HTTPException(status_code=400, detail="状态值无效，只能是normal或hidden")

    service = AdminService(db)

    try:
        admin = await service.update_admin_status(admin_id, status)
        if not admin:
            raise HTTPException(status_code=404, detail="管理员不存在")
        return admin
    except Exception as e:
        raise HTTPException(status_code=500, detail="更新管理员状态失败")


@router.patch("/{admin_id}/password", response_model=AdminResponse)
async def update_admin_password(
    admin_id: int,
    password_data: AdminPasswordUpdate,
    db: Session = Depends(get_db)
):
    """
    更新管理员密码

    需要提供原密码进行验证，确保安全性
    """
    service = AdminService(db)

    try:
        admin = await service.update_admin_password(admin_id, password_data)
        if not admin:
            raise HTTPException(status_code=404, detail="管理员不存在")
        return admin
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="更新管理员密码失败")


@router.patch("/{admin_id}/login-info")
async def update_login_info(
    admin_id: int,
    login_info: AdminLoginInfo,
    db: Session = Depends(get_db)
):
    """
    更新管理员登录信息

    用于记录登录成功/失败的信息，包括IP地址、登录时间等
    """
    service = AdminService(db)

    try:
        success = await service.update_login_info(admin_id, login_info)
        if not success:
            raise HTTPException(status_code=404, detail="管理员不存在")
        return {"message": "登录信息更新成功"}
    except Exception as e:
        raise HTTPException(status_code=500, detail="更新登录信息失败")