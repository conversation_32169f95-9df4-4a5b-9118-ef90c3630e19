import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:gold_manager_flutter/features/sales/controllers/sales_return_controller.dart';
import 'package:gold_manager_flutter/models/sales/sales_return_model.dart';
import 'package:gold_manager_flutter/widgets/app_bar.dart';
import 'package:gold_manager_flutter/widgets/empty_state.dart';
import 'package:gold_manager_flutter/widgets/loading_state.dart';

/// 销售退货列表页面
class SalesReturnListView extends StatelessWidget {
  /// 构造函数
  const SalesReturnListView({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<SalesReturnController>();
    return Scaffold(
      appBar: CustomAppBar(
        title: '销售退货管理',
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _navigateToCreateSalesReturn(),
            tooltip: '新建退货单',
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () => _showFilterDialog(context, controller),
            tooltip: '筛选',
          ),
        ],
      ),
      body: _buildBody(context, controller),
    );
  }

  /// 构建页面主体
  Widget _buildBody(BuildContext context, SalesReturnController controller) {
    return Obx(() {
      if (controller.isLoading.value && controller.salesReturnList.isEmpty) {
        return const LoadingState(timeoutSeconds: 30);
      }

      if (!controller.isLoading.value && controller.salesReturnList.isEmpty) {
        return EmptyState(
          icon: Icons.assignment_return,
          title: '暂无退货数据',
          message: '您还没有创建任何退货单据',
          buttonText: '创建退货单',
          onButtonPressed: () => _navigateToCreateSalesReturn(),
        );
      }

      return RefreshIndicator(
        onRefresh: () => controller.fetchSalesReturnList(refresh: true),
        child: _buildListView(context, controller),
      );
    });
  }

  /// 构建列表视图
  Widget _buildListView(
      BuildContext context, SalesReturnController controller) {
    return Obx(() {
      return ListView.builder(
        itemCount: controller.salesReturnList.length + 1,
        itemBuilder: (context, index) {
          if (index < controller.salesReturnList.length) {
            return _buildListItem(
                context, controller, controller.salesReturnList[index]);
          } else {
            // 加载更多指示器
            if (controller.hasMore.value) {
              _loadMore(controller);
              return const Padding(
                padding: EdgeInsets.all(16.0),
                child: Center(child: CircularProgressIndicator()),
              );
            } else {
              return const Padding(
                padding: EdgeInsets.all(16.0),
                child: Center(child: Text('没有更多数据了')),
              );
            }
          }
        },
      );
    });
  }

  /// 构建列表项
  Widget _buildListItem(
      BuildContext context, SalesReturnController controller, SalesReturn item) {
    final dateFormat = DateFormat('yyyy-MM-dd HH:mm');
    final statusColor = _getStatusColor(item.status);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: InkWell(
        onTap: () => _navigateToSalesReturnDetail(item.id!),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '退货单号: ${item.returnNo}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 8.0, vertical: 4.0),
                    decoration: BoxDecoration(
                      color: statusColor.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      item.statusText,
                      style: TextStyle(color: statusColor),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text('关联销售单: ${item.orderNo}'),
              const SizedBox(height: 4),
              Text('客户: ${item.customerName}'),
              const SizedBox(height: 4),
              Text('门店: ${item.storeName}'),
              const SizedBox(height: 4),
              Text('退货金额: ¥${item.totalAmount.toStringAsFixed(2)}'),
              const SizedBox(height: 4),
              Text('退货原因: ${item.returnReason}'),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '创建时间: ${dateFormat.format(DateTime.fromMillisecondsSinceEpoch(item.createdAt * 1000))}',
                    style: const TextStyle(
                      color: Colors.grey,
                      fontSize: 12,
                    ),
                  ),
                  _buildActionButtons(context, controller, item),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons(
      BuildContext context, SalesReturnController controller, SalesReturn item) {
    // 只有待审核状态可以有更多操作
    if (item.status == 0) {
      return Row(
        children: [
          IconButton(
            icon: const Icon(Icons.edit, size: 20),
            onPressed: () => _navigateToEditSalesReturn(item.id!),
            tooltip: '编辑',
            color: Colors.blue,
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
          ),
          const SizedBox(width: 8),
          IconButton(
            icon: const Icon(Icons.check_circle, size: 20),
            onPressed: () => _showApproveDialog(context, controller, item.id!),
            tooltip: '审核',
            color: Colors.green,
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
          ),
          const SizedBox(width: 8),
          IconButton(
            icon: const Icon(Icons.cancel, size: 20),
            onPressed: () => _showCancelDialog(context, controller, item.id!),
            tooltip: '取消',
            color: Colors.orange,
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
          ),
          const SizedBox(width: 8),
          IconButton(
            icon: const Icon(Icons.delete, size: 20),
            onPressed: () => _showDeleteDialog(context, controller, item.id!),
            tooltip: '删除',
            color: Colors.red,
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
          ),
        ],
      );
    } else {
      return IconButton(
        icon: const Icon(Icons.more_horiz, size: 20),
        onPressed: () => _showActionSheet(context, controller, item),
        tooltip: '更多操作',
        padding: EdgeInsets.zero,
        constraints: const BoxConstraints(),
      );
    }
  }

  /// 获取状态对应的颜色
  Color _getStatusColor(int status) {
    switch (status) {
      case 0:
        return Colors.orange;
      case 1:
        return Colors.green;
      case 2:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  /// 加载更多数据
  void _loadMore(SalesReturnController controller) {
    if (!controller.isLoading.value) {
      controller.fetchSalesReturnList();
    }
  }

  /// 导航到创建退货单页面
  void _navigateToCreateSalesReturn() {
    Get.toNamed('/sales/return/create');
  }

  /// 导航到编辑退货单页面
  void _navigateToEditSalesReturn(int id) {
    Get.toNamed('/sales/return/edit/$id');
  }

  /// 导航到退货单详情页面
  void _navigateToSalesReturnDetail(int id) {
    Get.toNamed('/sales/return/detail/$id');
  }

  /// 显示操作表单
  void _showActionSheet(BuildContext context, SalesReturnController controller,
      SalesReturn item) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.visibility),
            title: const Text('查看详情'),
            onTap: () {
              Navigator.pop(context);
              _navigateToSalesReturnDetail(item.id!);
            },
          ),
          if (item.status == 0) ...[
            ListTile(
              leading: const Icon(Icons.edit),
              title: const Text('编辑'),
              onTap: () {
                Navigator.pop(context);
                _navigateToEditSalesReturn(item.id!);
              },
            ),
            ListTile(
              leading: const Icon(Icons.check_circle),
              title: const Text('审核'),
              onTap: () {
                Navigator.pop(context);
                _showApproveDialog(context, controller, item.id!);
              },
            ),
            ListTile(
              leading: const Icon(Icons.cancel),
              title: const Text('取消'),
              onTap: () {
                Navigator.pop(context);
                _showCancelDialog(context, controller, item.id!);
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete),
              title: const Text('删除'),
              onTap: () {
                Navigator.pop(context);
                _showDeleteDialog(context, controller, item.id!);
              },
            ),
          ],
        ],
      ),
    );
  }

  /// 显示审核对话框
  void _showApproveDialog(
      BuildContext context, SalesReturnController controller, int id) {
    final remarkController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('审核退货单'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('确定要审核通过该退货单吗？审核后将自动更新库存。'),
            const SizedBox(height: 16),
            TextField(
              controller: remarkController,
              decoration: const InputDecoration(
                labelText: '备注信息',
                hintText: '请输入审核备注',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              final result = await controller.approveSalesReturn(
                  id, remarkController.text);
              if (result) {
                Get.snackbar('成功', '退货单审核成功',
                    backgroundColor: Colors.green.shade100);
              } else {
                Get.snackbar('失败', '退货单审核失败: ${controller.errorMessage.value}',
                    backgroundColor: Colors.red.shade100);
              }
            },
            child: const Text('确定审核'),
          ),
        ],
      ),
    );
  }

  /// 显示取消对话框
  void _showCancelDialog(
      BuildContext context, SalesReturnController controller, int id) {
    final remarkController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('取消退货单'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('确定要取消该退货单吗？'),
            const SizedBox(height: 16),
            TextField(
              controller: remarkController,
              decoration: const InputDecoration(
                labelText: '取消原因',
                hintText: '请输入取消原因',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('返回'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
            ),
            onPressed: () async {
              Navigator.pop(context);
              final result = await controller.cancelSalesReturn(
                  id, remarkController.text);
              if (result) {
                Get.snackbar('成功', '退货单已取消',
                    backgroundColor: Colors.green.shade100);
              } else {
                Get.snackbar('失败', '退货单取消失败: ${controller.errorMessage.value}',
                    backgroundColor: Colors.red.shade100);
              }
            },
            child: const Text('确定取消'),
          ),
        ],
      ),
    );
  }

  /// 显示删除对话框
  void _showDeleteDialog(
      BuildContext context, SalesReturnController controller, int id) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除退货单'),
        content: const Text('确定要删除该退货单吗？此操作不可恢复。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            onPressed: () async {
              Navigator.pop(context);
              final result = await controller.deleteSalesReturn(id);
              if (result) {
                Get.snackbar('成功', '退货单已删除',
                    backgroundColor: Colors.green.shade100);
              } else {
                Get.snackbar('失败', '退货单删除失败: ${controller.errorMessage.value}',
                    backgroundColor: Colors.red.shade100);
              }
            },
            child: const Text('确定删除'),
          ),
        ],
      ),
    );
  }

  /// 显示筛选对话框
  void _showFilterDialog(BuildContext context, SalesReturnController controller) {
    final orderNoController = TextEditingController(
        text: controller.filters['order_no'] as String? ?? '');
    final customerNameController = TextEditingController(
        text: controller.filters['customer_name'] as String? ?? '');
    final storeIdController = TextEditingController(
        text: controller.filters['store_id']?.toString() ?? '');

    int selectedStatus = controller.filters['status'] as int? ?? -1;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('筛选退货单'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: orderNoController,
                decoration: const InputDecoration(
                  labelText: '销售单号',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: customerNameController,
                decoration: const InputDecoration(
                  labelText: '客户名称',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: storeIdController,
                decoration: const InputDecoration(
                  labelText: '门店ID',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<int>(
                decoration: const InputDecoration(
                  labelText: '状态',
                  border: OutlineInputBorder(),
                ),
                value: selectedStatus,
                items: const [
                  DropdownMenuItem(
                    value: -1,
                    child: Text('全部'),
                  ),
                  DropdownMenuItem(
                    value: 0,
                    child: Text('待审核'),
                  ),
                  DropdownMenuItem(
                    value: 1,
                    child: Text('已审核'),
                  ),
                  DropdownMenuItem(
                    value: 2,
                    child: Text('已取消'),
                  ),
                ],
                onChanged: (value) {
                  selectedStatus = value!;
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              controller.clearFilters();
              controller.fetchSalesReturnList(refresh: true);
              Navigator.pop(context);
            },
            child: const Text('清除筛选'),
          ),
          ElevatedButton(
            onPressed: () {
              // 设置筛选条件
              controller.setFilter('order_no', orderNoController.text);
              controller.setFilter('customer_name', customerNameController.text);

              if (storeIdController.text.isNotEmpty) {
                controller.setFilter('store_id', int.parse(storeIdController.text));
              } else {
                controller.setFilter('store_id', null);
              }

              if (selectedStatus != -1) {
                controller.setFilter('status', selectedStatus);
              } else {
                controller.setFilter('status', null);
              }

              // 应用筛选
              controller.applyFilters();
              Navigator.pop(context);
            },
            child: const Text('应用筛选'),
          ),
        ],
      ),
    );
  }
}