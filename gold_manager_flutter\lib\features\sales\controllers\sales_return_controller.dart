import 'package:get/get.dart';
import 'package:gold_manager_flutter/core/config/app_config.dart';
import 'package:gold_manager_flutter/models/sales/sales_return_model.dart';
import 'package:gold_manager_flutter/services/sales_return_service.dart';
import '../../dashboard/controllers/dashboard_controller.dart';
import '../../../core/utils/logger_service.dart';

/// 销售退货控制器
class SalesReturnController extends GetxController {
  final SalesReturnService _salesReturnService;

  SalesReturnController({SalesReturnService? salesReturnService})
      : _salesReturnService = salesReturnService ?? Get.find<SalesReturnService>();

  // 退货单列表
  final RxList<SalesReturn> salesReturnList = <SalesReturn>[].obs;

  // 当前选中的退货单
  final Rx<SalesReturn?> currentSalesReturn = Rx<SalesReturn?>(null);

  // 是否正在加载
  final RxBool isLoading = false.obs;

  // 是否有更多数据
  final RxBool hasMore = true.obs;

  // 错误信息
  final RxString errorMessage = ''.obs;

  // 页码
  int _page = 1;

  // 每页数量
  final int _limit = AppConfig.defaultPageSize;

  // 筛选条件
  final RxMap<String, dynamic> filters = <String, dynamic>{}.obs;

  @override
  void onInit() {
    super.onInit();
    _updateDashboardNavigation();
    fetchSalesReturnList();
  }

  /// 更新仪表盘导航状态
  void _updateDashboardNavigation() {
    try {
      final dashboardController = Get.find<DashboardController>();
      dashboardController.updateSelectedIndex('/sales');
    } catch (e) {
      LoggerService.w('无法找到DashboardController: $e');
    }
  }

  /// 获取退货单列表
  Future<void> fetchSalesReturnList({bool refresh = false}) async {
    if (refresh) {
      _page = 1;
      hasMore.value = true;
    }

    if (!hasMore.value) return;

    try {
      isLoading.value = true;
      errorMessage.value = '';

      final list = await _salesReturnService.getSalesReturnList(
        page: _page,
        limit: _limit,
        filters: filters,
      );

      if (refresh) {
        salesReturnList.clear();
      }

      if (list.isEmpty) {
        hasMore.value = false;
      } else {
        salesReturnList.addAll(list);
        _page++;
      }
    } catch (e) {
      errorMessage.value = e.toString();
    } finally {
      isLoading.value = false;
    }
  }

  /// 根据ID获取退货单详情
  Future<void> fetchSalesReturnDetail(int id) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      final detail = await _salesReturnService.getSalesReturnDetail(id);
      currentSalesReturn.value = detail;
    } catch (e) {
      errorMessage.value = e.toString();
    } finally {
      isLoading.value = false;
    }
  }

  /// 创建退货单
  Future<bool> createSalesReturn(SalesReturn salesReturn) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      final result = await _salesReturnService.createSalesReturn(salesReturn);
      currentSalesReturn.value = result;

      // 刷新列表
      await fetchSalesReturnList(refresh: true);
      return true;
    } catch (e) {
      errorMessage.value = e.toString();
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// 更新退货单
  Future<bool> updateSalesReturn(SalesReturn salesReturn) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      final result = await _salesReturnService.updateSalesReturn(salesReturn);
      currentSalesReturn.value = result;

      // 更新列表中对应项
      final index = salesReturnList.indexWhere((item) => item.id == result.id);
      if (index != -1) {
        salesReturnList[index] = result;
      }

      return true;
    } catch (e) {
      errorMessage.value = e.toString();
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// 审核退货单
  Future<bool> approveSalesReturn(int id, String remark) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      final result = await _salesReturnService.approveSalesReturn(id, remark);
      if (result) {
        // 重新获取详情
        await fetchSalesReturnDetail(id);

        // 更新列表中对应项
        await fetchSalesReturnList(refresh: true);
      }

      return result;
    } catch (e) {
      errorMessage.value = e.toString();
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// 取消退货单
  Future<bool> cancelSalesReturn(int id, String remark) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      final result = await _salesReturnService.cancelSalesReturn(id, remark);
      if (result) {
        // 重新获取详情
        await fetchSalesReturnDetail(id);

        // 更新列表中对应项
        await fetchSalesReturnList(refresh: true);
      }

      return result;
    } catch (e) {
      errorMessage.value = e.toString();
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// 删除退货单
  Future<bool> deleteSalesReturn(int id) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      final result = await _salesReturnService.deleteSalesReturn(id);
      if (result) {
        // 移除列表中对应项
        salesReturnList.removeWhere((item) => item.id == id);

        // 如果当前选中的是被删除的
        if (currentSalesReturn.value?.id == id) {
          currentSalesReturn.value = null;
        }
      }

      return result;
    } catch (e) {
      errorMessage.value = e.toString();
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// 设置筛选条件
  void setFilter(String key, dynamic value) {
    if (value == null || (value is String && value.isEmpty)) {
      filters.remove(key);
    } else {
      filters[key] = value;
    }
  }

  /// 清除筛选条件
  void clearFilters() {
    filters.clear();
  }

  /// 应用筛选条件并刷新
  Future<void> applyFilters() async {
    await fetchSalesReturnList(refresh: true);
  }
}