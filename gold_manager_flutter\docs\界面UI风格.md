# 黄金珠宝管理系统 - 界面UI风格指南

> 基于出库管理页面和新建出库单页面的UI风格分析，为整个系统建立统一的界面设计规范。

## 总体设计原则

### 1. 简洁高效
- 采用清晰的层次结构，信息密度适中
- 以功能为导向，避免过度装饰
- 紧凑型布局，充分利用屏幕空间

### 2. 一致性
- 统一的颜色方案和组件样式
- 规范化的间距和圆角设计
- 标准化的交互模式

### 3. 响应式设计
- 支持桌面、平板和移动设备
- 自适应布局和字体大小
- 优化的表格显示方案

## 颜色系统

### 主色调
```dart
// 主色调 - 蓝色系，用于主要操作和强调元素
static const Color primaryColor = Color(0xFF1E88E5);  // 主蓝色

// 功能性颜色
static const Color successColor = Color(0xFF388E3C);   // 成功/新建 - 绿色
static const Color warningColor = Color(0xFFFFA000);   // 警告/编辑 - 橙色
static const Color errorColor = Color(0xFFD32F2F);     // 错误/删除 - 红色
static const Color infoColor = Color(0xFF2196F3);      // 信息 - 蓝色

// 特殊功能颜色
static const Color recyclingColor = Color(0xFF9C27B0); // 回收功能 - 紫色
static const Color workFeeColor = Color(0xFF2196F3);   // 工费功能 - 蓝色
```

### 背景色系
```dart
// 页面背景
static const Color backgroundColor = Color(0xFFF5F5F5);        // 主背景
static const Color cardBackgroundColor = Color(0xFFFFFFFF);   // 卡片背景

// 表格背景
static const Color tableHeaderBackground = Color(0xFFF5F5F5); // 表头背景
static const Color tableOddRowBackground = Colors.white;       // 奇数行
static const Color tableEvenRowBackground = Color(0xFFF9F9F9); // 偶数行

// 特殊行背景（用于区分不同类型的数据）
static const Color recyclingRowBackground = Colors.purple[50]; // 回收商品行
static const Color returnRowBackground = Colors.red[50];       // 退换货商品行
static const Color workFeeRowBackground = Colors.blue[50];     // 工费项目行
```

### 文本颜色
```dart
static const Color primaryTextColor = Color(0xFF212121);   // 主要文本
static const Color secondaryTextColor = Color(0xFF757575); // 次要文本
static const Color disabledTextColor = Color(0xFFBDBDBD);  // 禁用文本
```

## 圆角规范

### 统一圆角值
```dart
// 来自 AppBorderStyles
static const double smallBorderRadius = 4.0;   // 小圆角 - 输入框内部
static const double borderRadius = 6.0;        // 标准圆角 - 按钮、输入框
static const double mediumBorderRadius = 8.0;  // 中等圆角 - 卡片
static const double largeBorderRadius = 12.0;  // 大圆角 - 对话框、标签
```

### 应用场景
- **4px**: 表格单元格内的输入框
- **6px**: 标准按钮、下拉框、搜索框
- **8px**: 容器卡片、信息标签
- **12px**: 对话框、大型容器

## 边框与分割线

### 边框规范
```dart
// 标准边框
static const BorderSide standardBorder = BorderSide(
  color: Color(0xFFE0E0E0),  // 浅灰色
  width: 1.0,
);

// 表格边框
static const BorderSide tableBorder = BorderSide(
  color: Color(0xFFEEEEEE),  // 更浅的灰色
  width: 0.5,
);

// 焦点边框
static const BorderSide focusBorder = BorderSide(
  color: Color(0xFF1E88E5),  // 蓝色
  width: 2.0,
);
```

### 分割线
- **水平分割线**: 用于页面区域分隔，1px高度
- **表格分割线**: 用于表格行列分隔，0.5px宽度
- **内容分割线**: 用于内容区域分隔，使用`Divider(height: 1)`

## 间距系统

### 标准间距
```dart
// 页面级间距
static const double pageHorizontalPadding = 16.0;  // 页面水平内边距
static const double pageVerticalPadding = 16.0;    // 页面垂直内边距

// 组件级间距
static const double componentSpacing = 24.0;       // 组件间距
static const double elementSpacing = 16.0;         // 元素间距
static const double smallSpacing = 8.0;            // 小间距
static const double tinySpacing = 4.0;             // 微小间距

// 表格间距
static const double tableHorizontalPadding = 8.0;  // 表格单元格水平内边距
static const double tableVerticalPadding = 8.0;    // 表格单元格垂直内边距
```

## 按钮系统

### 按钮尺寸
```dart
// 标准按钮 (用于重要操作)
height: 32px
padding: EdgeInsets.symmetric(horizontal: 12, vertical: 0)

// 表格操作按钮 (紧凑型)
height: 30px
minimumSize: Size(70, 30)
maximumSize: Size(70, 30)
```

### 按钮颜色方案
```dart
// 主要操作按钮
ElevatedButton.styleFrom(
  backgroundColor: Color(0xFF1E88E5),  // 蓝色
  foregroundColor: Colors.white,
)

// 成功/新建按钮
ElevatedButton.styleFrom(
  backgroundColor: Colors.green[600],  // 绿色
  foregroundColor: Colors.white,
)

// 警告/编辑按钮
ElevatedButton.styleFrom(
  backgroundColor: Colors.orange[600], // 橙色
  foregroundColor: Colors.white,
)

// 危险/删除按钮
ElevatedButton.styleFrom(
  backgroundColor: Colors.red[600],    // 红色
  foregroundColor: Colors.white,
)

// 次要操作按钮
OutlinedButton.styleFrom(
  foregroundColor: Colors.grey[600],
  side: BorderSide(color: AppBorderStyles.borderColor),
)
```

### 特殊功能按钮
```dart
// 旧料回收按钮
backgroundColor: Colors.purple[600]  // 紫色

// 工费相关按钮
backgroundColor: Colors.blue[600]    // 蓝色

// 退换货按钮
backgroundColor: Colors.red[600]     // 红色

// 批量改价按钮
backgroundColor: Colors.orange[600]  // 橙色
```

## 字体系统

### 字体大小
```dart
// 页面标题
static const double pageTitleSize = 18.0;      // 页面主标题
static const double sectionTitleSize = 16.0;   // 区域标题

// 表格相关
static const double tableHeaderSize = 14.0;    // 表头文字 (>= 1200px屏幕)
static const double tableHeaderSizeSmall = 12.0; // 表头文字 (< 1200px屏幕)
static const double tableCellSize = 13.0;      // 表格内容 (>= 1200px屏幕)
static const double tableCellSizeSmall = 11.0; // 表格内容 (< 1200px屏幕)

// 表单相关
static const double labelSize = 13.0;          // 表单标签
static const double inputSize = 13.0;          // 输入框文字
static const double buttonTextSize = 13.0;     // 按钮文字
static const double hintTextSize = 12.0;       // 提示文字

// 状态标签
static const double badgeTextSize = 11.0;      // 状态徽章文字
```

### 字体权重
```dart
// 标题权重
FontWeight.w600  // 页面标题、区域标题、表头
FontWeight.w500  // 重要文字、数值、标签

// 内容权重
FontWeight.normal  // 普通文字
FontWeight.w500    // 强调文字
```

## 表格设计

### 表格布局
```dart
// 表头设计
Container(
  height: 48,  // 固定表头高度
  color: AppBorderStyles.tableHeaderBackground,
  // 表头内容居中对齐
  alignment: Alignment.center,
)

// 数据行设计
Container(
  height: 48,  // 固定数据行高度 (列表页面)
  height: 52,  // 固定数据行高度 (表单页面)
  // 数据内容居中对齐
  alignment: Alignment.center,
)
```

### 表格列宽比例 (参考出库管理页面)
```dart
// 出库管理页面列宽分配
final stockOutNoWidth = availableWidth * 0.18;    // 18% - 出库单号
final storeWidth = availableWidth * 0.14;         // 14% - 门店
final timeWidth = availableWidth * 0.12;          // 12% - 时间
final quantityWidth = availableWidth * 0.08;      // 8%  - 件数
final amountWidth = availableWidth * 0.12;        // 12% - 金额
final statusWidth = availableWidth * 0.10;        // 10% - 状态
final actionWidth = availableWidth * 0.20;        // 20% - 操作
```

### 表格单元格样式
```dart
// 标准单元格
Container(
  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
  alignment: Alignment.center,  // 居中对齐
  child: Text(...),
)

// 可编辑单元格
TextFormField(
  textAlign: TextAlign.center,  // 文字居中
  style: TextStyle(
    fontSize: 13,
    fontFamily: 'monospace',  // 数字字段使用等宽字体
  ),
  decoration: InputDecoration(
    contentPadding: EdgeInsets.symmetric(horizontal: 10, vertical: 8),
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(6),
      borderSide: BorderSide(color: Colors.grey[300]!, width: 1),
    ),
  ),
)
```

## 输入框设计

### 标准输入框
```dart
// 紧凑型输入框 (高度32px)
Container(
  height: 32,
  decoration: AppBorderStyles.standardBoxDecoration,
  child: TextField(
    decoration: InputDecoration(
      contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      border: InputBorder.none,
      hintStyle: TextStyle(fontSize: 13, color: Colors.grey),
    ),
    style: TextStyle(fontSize: 13),
  ),
)
```

### 下拉框设计
```dart
// 标准下拉框
Container(
  height: 32,
  decoration: AppBorderStyles.standardBoxDecoration,
  child: DropdownButtonHideUnderline(
    child: DropdownButton<T>(
      isExpanded: true,
      style: TextStyle(fontSize: 13, color: Colors.black87),
      icon: Icon(Icons.arrow_drop_down, size: 20),
      padding: EdgeInsets.symmetric(horizontal: 8),
      // ... 其他属性
    ),
  ),
)
```

## 状态徽章设计

### 徽章样式
```dart
Container(
  padding: EdgeInsets.symmetric(horizontal: 5, vertical: 2),
  decoration: BoxDecoration(
    color: statusColor.withOpacity(0.1),  // 10%透明度背景
    borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
    border: Border.all(color: statusColor, width: 1),
  ),
  child: Text(
    statusText,
    style: TextStyle(
      color: statusColor,
      fontSize: 11,
      fontWeight: FontWeight.w500,
    ),
    overflow: TextOverflow.ellipsis,
    maxLines: 1,
  ),
)
```

## 信息标签设计

### 操作员信息标签
```dart
Container(
  padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
  decoration: BoxDecoration(
    color: Colors.blue[50],
    borderRadius: BorderRadius.circular(AppBorderStyles.largeBorderRadius),
    border: Border.all(color: Colors.blue[200]!, width: AppBorderStyles.borderWidth),
  ),
  child: Row(
    mainAxisSize: MainAxisSize.min,
    children: [
      Icon(Icons.person, size: 14, color: Colors.blue[600]),
      SizedBox(width: 4),
      Text(
        '操作员: $operatorName',
        style: TextStyle(
          fontSize: 12,
          color: Colors.blue[700],
          fontWeight: FontWeight.w500,
        ),
      ),
    ],
  ),
)
```

### 数据汇总标签
```dart
// 件数标签
Container(
  padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
  decoration: BoxDecoration(
    color: Colors.blue[50],
    borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
    border: Border.all(color: Colors.blue[200]!, width: 1),
  ),
  child: Row(
    mainAxisSize: MainAxisSize.min,
    children: [
      Icon(Icons.inventory_2, size: 14, color: Colors.blue[600]),
      SizedBox(width: 4),
      Text('总件数: ', style: TextStyle(fontSize: 13, color: Colors.black87)),
      Text(
        '$count件',
        style: TextStyle(
          fontSize: 13,
          fontWeight: FontWeight.w600,
          color: Colors.blue[700],
        ),
      ),
    ],
  ),
)

// 重量标签 (金重 - 琥珀色, 银重 - 灰色, 总重 - 橙色)
// 金额标签 (绿色)
```

## 对话框设计

### 标准对话框
```dart
AlertDialog(
  shape: RoundedRectangleBorder(
    borderRadius: BorderRadius.circular(AppBorderStyles.largeBorderRadius),
  ),
  title: Row(
    children: [
      Icon(dialogIcon, color: dialogColor, size: 20),
      SizedBox(width: 8),
      Text(dialogTitle, style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
    ],
  ),
  content: SizedBox(
    width: 400,  // 固定宽度
    child: Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 对话框内容
      ],
    ),
  ),
  actions: [
    TextButton(...),  // 取消按钮
    ElevatedButton(...),  // 确认按钮
  ],
)
```

## 图标系统

### 功能图标映射
```dart
// 页面级图标
Icons.outbox           // 出库管理
Icons.logout           // 新建出库
Icons.edit             // 编辑出库

// 操作图标
Icons.visibility       // 查看
Icons.edit            // 编辑
Icons.delete          // 删除
Icons.check_circle    // 审核通过
Icons.cancel          // 拒绝

// 功能图标
Icons.qr_code_scanner // 扫描
Icons.price_change    // 批量改价
Icons.recycling       // 旧料回收
Icons.keyboard_return // 退换货
Icons.build           // 工费

// 数据图标
Icons.person          // 操作员
Icons.store           // 门店
Icons.inventory_2     // 商品件数
Icons.star            // 金重
Icons.circle          // 银重
Icons.scale           // 总重量
Icons.attach_money    // 金额
```

## 响应式设计

### 屏幕断点
```dart
// 字体大小响应式
final fontSize = availableWidth < 1200 ? 11.0 : 13.0;           // 表格内容
final headingFontSize = availableWidth < 1200 ? 12.0 : 14.0;    // 表头
final buttonSize = isSmallScreen ? 32 : 40;                     // 按钮尺寸
final iconSize = isSmallScreen ? 18 : 20;                       // 图标尺寸

// 布局响应式
ScreenTypeLayout(
  mobile: _buildListView(),      // 移动端 - 卡片列表
  tablet: _buildDataTable(),     // 平板 - 数据表格
  desktop: _buildDataTable(),    // 桌面 - 数据表格
)
```

## 特殊功能样式

### 特殊商品行样式
```dart
// 回收商品行 - 紫色背景
BoxDecoration(
  color: Colors.purple[50],
  border: Border(...),
)

// 退换货商品行 - 红色背景
BoxDecoration(
  color: Colors.red[50],
  border: Border(...),
)

// 工费项目行 - 蓝色背景
BoxDecoration(
  color: Colors.blue[50],
  border: Border(...),
)
```

### 特殊输入框样式
```dart
// 针对特殊商品的输入框边框颜色
border: OutlineInputBorder(
  borderSide: BorderSide(
    color: isRecyclingItem ? Colors.purple[300]! :
           isReturnExchangeItem ? Colors.red[300]! :
           isWorkFeeItem ? Colors.blue[300]! : Colors.grey[300]!,
    width: 1
  ),
)

// 焦点状态
focusedBorder: OutlineInputBorder(
  borderSide: BorderSide(
    color: isRecyclingItem ? Colors.purple[600]! :
           isReturnExchangeItem ? Colors.red[600]! :
           isWorkFeeItem ? Colors.blue[600]! : Color(0xFF1E88E5),
    width: 2
  ),
)
```

## 页面布局结构

### 标准页面结构
```dart
Column(
  children: [
    _buildHeader(),      // 页面头部 - 标题、筛选、操作按钮
    Divider(height: 1),  // 分割线
    Expanded(
      child: _buildContent(),  // 主要内容区域
    ),
    _buildFooter(),      // 页面底部 - 分页、汇总信息
  ],
)
```

### 表单页面结构
```dart
Column(
  children: [
    _buildFormHeader(),    // 表单头部 - 标题、基本信息
    _buildBarcodeSection(), // 扫描区域 - 条码输入、功能按钮
    Expanded(
      child: _buildItemList(),  // 明细列表 - 表格形式
    ),
    _buildFormFooter(),    // 表单底部 - 汇总信息、操作按钮
  ],
)
```

## 总结

本UI风格指南基于出库管理和新建出库单页面的实际实现，确保整个系统的界面风格统一和用户体验一致。在开发新页面时，请严格按照本指南的规范进行设计和实现。

### 关键要点
1. **统一的颜色系统** - 主蓝色 #1E88E5，功能性颜色明确
2. **标准化组件尺寸** - 32px按钮高度，48/52px表格行高
3. **一致的圆角规范** - 6px标准圆角，12px大圆角
4. **响应式字体** - 根据屏幕宽度调整字体大小
5. **特殊功能区分** - 使用颜色区分不同类型的数据和操作