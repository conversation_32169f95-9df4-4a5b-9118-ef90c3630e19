import 'package:dio/dio.dart';
import 'package:get/get.dart';
import '../../utils/logger_service.dart';
import '../../../services/auth_service.dart';

/// 认证拦截器
/// 用于在请求头中添加认证令牌
class AuthInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    LoggerService.d('AuthInterceptor: 添加认证头');

    try {
      // 从AuthService中获取认证令牌
      final AuthService authService = Get.find<AuthService>();
      final String token = authService.token.value;

      if (token.isNotEmpty) {
        options.headers['Authorization'] = 'Bearer $token';
        LoggerService.d('AuthInterceptor: 已添加认证头');
      } else {
        LoggerService.w('AuthInterceptor: Token为空，跳过认证头添加');
      }
    } catch (e) {
      LoggerService.e('AuthInterceptor: 获取token失败', e);
    }

    super.onRequest(options, handler);
  }
}