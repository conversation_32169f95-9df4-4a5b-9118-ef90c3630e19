/// 应用权限常量
/// 统一管理所有权限标识符
class AppPermissions {
  // 私有构造函数，防止实例化
  AppPermissions._();

  // ==================== 商品管理权限 ====================
  /// 查看商品
  static const String jewelryView = 'jewelry.view';
  /// 创建商品
  static const String jewelryCreate = 'jewelry.create';
  /// 更新商品
  static const String jewelryUpdate = 'jewelry.update';
  /// 删除商品
  static const String jewelryDelete = 'jewelry.delete';

  // ==================== 门店管理权限 ====================
  /// 查看门店
  static const String storeView = 'store.view';
  /// 创建门店
  static const String storeCreate = 'store.create';
  /// 更新门店
  static const String storeUpdate = 'store.update';
  /// 删除门店
  static const String storeDelete = 'store.delete';

  // ==================== 会员管理权限 ====================
  /// 查看会员
  static const String memberView = 'member.view';
  /// 创建会员
  static const String memberCreate = 'member.create';
  /// 更新会员
  static const String memberUpdate = 'member.update';
  /// 删除会员
  static const String memberDelete = 'member.delete';

  // ==================== 管理员权限 ====================
  /// 查看管理员
  static const String adminView = 'admin.view';
  /// 创建管理员
  static const String adminCreate = 'admin.create';
  /// 更新管理员
  static const String adminUpdate = 'admin.update';
  /// 删除管理员
  static const String adminDelete = 'admin.delete';

  // ==================== 库存管理权限 ====================
  /// 查看库存
  static const String stockView = 'stock.view';
  /// 入库操作
  static const String stockIn = 'stock.in';
  /// 出库操作
  static const String stockOut = 'stock.out';
  /// 退货操作
  static const String stockReturn = 'stock.return';
  /// 库存盘点
  static const String stockCheck = 'stock.check';
  /// 门店调拨
  static const String stockTransfer = 'stock.transfer';
  /// 回收管理
  static const String stockRecycle = 'stock.recycle';
  /// 库存审核
  static const String stockAudit = 'stock.audit';

  // ==================== 销售管理权限 ====================
  /// 查看销售
  static const String salesView = 'sales.view';
  /// 创建销售
  static const String salesCreate = 'sales.create';
  /// 更新销售
  static const String salesUpdate = 'sales.update';
  /// 删除销售
  static const String salesDelete = 'sales.delete';

  // ==================== 系统管理权限 ====================
  /// 仪表板
  static const String systemDashboard = 'system.dashboard';
  /// 数据导出
  static const String systemExport = 'system.export';
  /// 数据导入
  static const String systemImport = 'system.import';
  /// 数据备份
  static const String systemBackup = 'system.backup';
  /// 系统设置
  static const String systemSettings = 'system.settings';

  // ==================== 超级管理员权限 ====================
  /// 超级管理员
  static const String superAdmin = 'super.admin';

  // ==================== 权限组 ====================
  
  /// 所有商品管理权限
  static const List<String> jewelryPermissions = [
    jewelryView,
    jewelryCreate,
    jewelryUpdate,
    jewelryDelete,
  ];

  /// 所有门店管理权限
  static const List<String> storePermissions = [
    storeView,
    storeCreate,
    storeUpdate,
    storeDelete,
  ];

  /// 所有会员管理权限
  static const List<String> memberPermissions = [
    memberView,
    memberCreate,
    memberUpdate,
    memberDelete,
  ];

  /// 所有库存管理权限
  static const List<String> stockPermissions = [
    stockView,
    stockIn,
    stockOut,
    stockReturn,
    stockCheck,
    stockTransfer,
    stockRecycle,
    stockAudit,
  ];

  /// 所有销售管理权限
  static const List<String> salesPermissions = [
    salesView,
    salesCreate,
    salesUpdate,
    salesDelete,
  ];

  /// 所有系统管理权限
  static const List<String> systemPermissions = [
    systemDashboard,
    systemExport,
    systemImport,
    systemBackup,
    systemSettings,
  ];

  /// 所有管理员权限
  static const List<String> adminPermissions = [
    adminView,
    adminCreate,
    adminUpdate,
    adminDelete,
  ];

  // ==================== 角色权限映射 ====================
  
  /// 超级管理员权限
  static const List<String> superAdminPermissions = [superAdmin];

  /// 管理员权限
  static const List<String> managerPermissions = [
    ...jewelryPermissions,
    ...storePermissions,
    ...memberPermissions,
    ...stockPermissions,
    ...salesPermissions,
    systemDashboard,
    systemExport,
  ];

  /// 店长权限
  static const List<String> storeManagerPermissions = [
    jewelryView,
    jewelryCreate,
    jewelryUpdate,
    storeView,
    ...memberPermissions,
    ...stockPermissions,
    salesView,
    salesCreate,
    salesUpdate,
    systemDashboard,
  ];

  /// 经理权限
  static const List<String> supervisorPermissions = [
    jewelryView,
    jewelryCreate,
    jewelryUpdate,
    memberView,
    memberCreate,
    memberUpdate,
    stockView,
    stockIn,
    stockOut,
    salesView,
    salesCreate,
    systemDashboard,
  ];

  /// 员工权限
  static const List<String> staffPermissions = [
    jewelryView,
    memberView,
    stockView,
    salesView,
    systemDashboard,
  ];

  // ==================== 权限检查方法 ====================
  
  /// 检查是否有权限
  static bool hasPermission(List<String> userPermissions, String permission) {
    // 超级管理员拥有所有权限
    if (userPermissions.contains(superAdmin)) {
      return true;
    }
    
    return userPermissions.contains(permission);
  }

  /// 检查是否有任一权限
  static bool hasAnyPermission(List<String> userPermissions, List<String> permissions) {
    // 超级管理员拥有所有权限
    if (userPermissions.contains(superAdmin)) {
      return true;
    }
    
    return permissions.any((permission) => userPermissions.contains(permission));
  }

  /// 检查是否有所有权限
  static bool hasAllPermissions(List<String> userPermissions, List<String> permissions) {
    // 超级管理员拥有所有权限
    if (userPermissions.contains(superAdmin)) {
      return true;
    }
    
    return permissions.every((permission) => userPermissions.contains(permission));
  }

  /// 获取权限显示名称
  static String getPermissionDisplayName(String permission) {
    const Map<String, String> permissionNames = {
      // 商品管理
      jewelryView: '查看商品',
      jewelryCreate: '创建商品',
      jewelryUpdate: '更新商品',
      jewelryDelete: '删除商品',
      
      // 门店管理
      storeView: '查看门店',
      storeCreate: '创建门店',
      storeUpdate: '更新门店',
      storeDelete: '删除门店',
      
      // 会员管理
      memberView: '查看会员',
      memberCreate: '创建会员',
      memberUpdate: '更新会员',
      memberDelete: '删除会员',
      
      // 管理员权限
      adminView: '查看管理员',
      adminCreate: '创建管理员',
      adminUpdate: '更新管理员',
      adminDelete: '删除管理员',
      
      // 库存管理
      stockView: '查看库存',
      stockIn: '入库操作',
      stockOut: '出库操作',
      stockReturn: '退货操作',
      stockCheck: '库存盘点',
      stockTransfer: '门店调拨',
      stockRecycle: '回收管理',
      stockAudit: '库存审核',
      
      // 销售管理
      salesView: '查看销售',
      salesCreate: '创建销售',
      salesUpdate: '更新销售',
      salesDelete: '删除销售',
      
      // 系统管理
      systemDashboard: '仪表板',
      systemExport: '数据导出',
      systemImport: '数据导入',
      systemBackup: '数据备份',
      systemSettings: '系统设置',
      
      // 超级管理员
      superAdmin: '超级管理员',
    };
    
    return permissionNames[permission] ?? permission;
  }
}
