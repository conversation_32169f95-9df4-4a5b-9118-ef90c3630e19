import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../models/print/print_template_config.dart';
import '../../../core/constants/border_styles.dart';

/// 字段位置控制组件
/// 
/// 提供精确的坐标输入控制
class FieldPositionControl extends StatelessWidget {
  /// 字段名称
  final String fieldName;
  
  /// 字段显示名称
  final String displayName;
  
  /// 当前位置
  final FieldPosition position;
  
  /// 位置变更回调
  final Function(FieldPosition position) onPositionChanged;
  
  /// 是否启用
  final bool enabled;

  const FieldPositionControl({
    super.key,
    required this.fieldName,
    required this.displayName,
    required this.position,
    required this.onPositionChanged,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: enabled ? Colors.white : Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppBorderStyles.borderColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 字段名称
          Text(
            displayName,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: enabled ? Colors.black87 : Colors.grey,
            ),
          ),
          const SizedBox(height: 8),
          
          // 坐标输入
          Row(
            children: [
              // X坐标
              Expanded(
                child: _buildCoordinateInput(
                  label: 'X (mm)',
                  value: position.x,
                  onChanged: (value) {
                    onPositionChanged(position.copyWith(x: value));
                  },
                ),
              ),
              const SizedBox(width: 12),
              
              // Y坐标
              Expanded(
                child: _buildCoordinateInput(
                  label: 'Y (mm)',
                  value: position.y,
                  onChanged: (value) {
                    onPositionChanged(position.copyWith(y: value));
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建坐标输入框
  Widget _buildCoordinateInput({
    required String label,
    required double value,
    required Function(double) onChanged,
  }) {
    final controller = TextEditingController(text: value.toStringAsFixed(1));
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: enabled ? Colors.grey[600] : Colors.grey,
          ),
        ),
        const SizedBox(height: 4),
        SizedBox(
          height: 32,
          child: TextField(
            controller: controller,
            enabled: enabled,
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
            ],
            decoration: InputDecoration(
              border: const OutlineInputBorder(),
              contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              suffixText: 'mm',
              suffixStyle: TextStyle(
                fontSize: 10,
                color: Colors.grey[500],
              ),
            ),
            style: const TextStyle(fontSize: 12),
            onChanged: (text) {
              final newValue = double.tryParse(text) ?? value;
              if (newValue != value) {
                onChanged(newValue);
              }
            },
            onSubmitted: (text) {
              final newValue = double.tryParse(text) ?? value;
              controller.text = newValue.toStringAsFixed(1);
              onChanged(newValue);
            },
          ),
        ),
      ],
    );
  }
}

/// 字段位置控制面板
class FieldPositionPanel extends StatelessWidget {
  /// 模板配置
  final PrintTemplateConfig template;
  
  /// 位置变更回调
  final Function(String fieldKey, FieldPosition position) onPositionChanged;

  const FieldPositionPanel({
    super.key,
    required this.template,
    required this.onPositionChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppBorderStyles.borderColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // 标题
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.control_point, color: Colors.blue[600], size: 20),
                const SizedBox(width: 8),
                const Text(
                  '字段位置控制',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          ),

          // 控制列表
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // 公司信息
                _buildSectionHeader('公司信息'),
                const SizedBox(height: 8),
                FieldPositionControl(
                  fieldName: 'company_name',
                  displayName: '公司名称',
                  position: template.companyInfo.namePosition,
                  onPositionChanged: (position) {
                    onPositionChanged('company_name', position);
                  },
                ),
                const SizedBox(height: 8),
                FieldPositionControl(
                  fieldName: 'company_address',
                  displayName: '公司地址',
                  position: template.companyInfo.addressPosition,
                  enabled: template.companyInfo.showAddress,
                  onPositionChanged: (position) {
                    onPositionChanged('company_address', position);
                  },
                ),
                const SizedBox(height: 8),
                FieldPositionControl(
                  fieldName: 'company_phone',
                  displayName: '公司电话',
                  position: template.companyInfo.phonePosition,
                  enabled: template.companyInfo.showPhone,
                  onPositionChanged: (position) {
                    onPositionChanged('company_phone', position);
                  },
                ),
                
                const SizedBox(height: 24),
                
                // 表头信息
                _buildSectionHeader('表头信息'),
                const SizedBox(height: 8),
                FieldPositionControl(
                  fieldName: 'order_no',
                  displayName: '单号',
                  position: template.headerConfig.orderNoPosition,
                  enabled: template.headerConfig.showOrderNo,
                  onPositionChanged: (position) {
                    onPositionChanged('order_no', position);
                  },
                ),
                const SizedBox(height: 8),
                FieldPositionControl(
                  fieldName: 'customer',
                  displayName: '客户',
                  position: template.headerConfig.customerPosition,
                  enabled: template.headerConfig.showCustomer,
                  onPositionChanged: (position) {
                    onPositionChanged('customer', position);
                  },
                ),
                const SizedBox(height: 8),
                FieldPositionControl(
                  fieldName: 'sale_type',
                  displayName: '销售类型',
                  position: template.headerConfig.saleTypePosition,
                  enabled: template.headerConfig.showSaleType,
                  onPositionChanged: (position) {
                    onPositionChanged('sale_type', position);
                  },
                ),
                const SizedBox(height: 8),
                FieldPositionControl(
                  fieldName: 'date_time',
                  displayName: '日期时间',
                  position: template.headerConfig.dateTimePosition,
                  enabled: template.headerConfig.showDateTime,
                  onPositionChanged: (position) {
                    onPositionChanged('date_time', position);
                  },
                ),
                
                const SizedBox(height: 24),
                
                // 表格位置
                _buildSectionHeader('表格位置'),
                const SizedBox(height: 8),
                FieldPositionControl(
                  fieldName: 'item_table',
                  displayName: '商品明细表格',
                  position: template.itemTableConfig.tablePosition,
                  onPositionChanged: (position) {
                    onPositionChanged('item_table', position);
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建分组标题
  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w600,
        color: Colors.black87,
      ),
    );
  }
}
