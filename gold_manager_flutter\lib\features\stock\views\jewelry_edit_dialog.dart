import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/constants/border_styles.dart';
import '../../../core/utils/logger.dart';
import '../../../models/jewelry/jewelry.dart';
import '../../../models/common/enums.dart';
import '../../../widgets/responsive_builder.dart';
import '../controllers/jewelry_edit_controller.dart';

/// 商品编辑弹窗
class JewelryEditDialog extends GetView<JewelryEditController> {
  final Jewelry jewelry;
  final VoidCallback? onUpdated;

  const JewelryEditDialog({
    super.key,
    required this.jewelry,
    this.onUpdated,
  });

  /// 显示商品编辑弹窗
  static Future<void> show(BuildContext context, Jewelry jewelry, {VoidCallback? onUpdated}) {
    LoggerService.d('🎯 编辑弹窗：开始显示弹窗，商品ID: ${jewelry.id}');

    // 注册控制器
    Get.put(JewelryEditController(jewelry: jewelry));

    return showDialog(
      context: context,
      barrierDismissible: false, // 编辑时不允许点击外部关闭
      builder: (context) => JewelryEditDialog(
        jewelry: jewelry,
        onUpdated: onUpdated,
      ),
    ).then((result) {
      LoggerService.d('🎯 编辑弹窗：弹窗已关闭，结果: $result');

      // 延迟删除控制器，确保所有异步操作完成
      Future.delayed(const Duration(milliseconds: 300), () {
        try {
          if (Get.isRegistered<JewelryEditController>()) {
            Get.delete<JewelryEditController>();
            LoggerService.d('🗑️ 编辑弹窗：控制器已删除');
          }
        } catch (e) {
          LoggerService.e('❌ 编辑弹窗：删除控制器失败', e);
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: EdgeInsets.symmetric(
        horizontal: MediaQuery.of(context).size.width > 800 ? 80 : 16,
        vertical: 20,
      ),
      child: Container(
        constraints: const BoxConstraints(
          maxWidth: 1000,
          maxHeight: 800,
        ),
        decoration: AppBorderStyles.elevatedBoxDecoration.copyWith(
          color: Colors.white,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(context),
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: Form(
                  key: controller.formKey,
                  child: _buildForm(),
                ),
              ),
            ),
            _buildFooter(context),
          ],
        ),
      ),
    );
  }

  /// 构建弹窗头部
  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(AppBorderStyles.borderRadius),
          topRight: Radius.circular(AppBorderStyles.borderRadius),
        ),
        border: Border(
          bottom: AppBorderStyles.tableBorder,
        ),
      ),
      child: Row(
        children: [
          Icon(Icons.edit, color: Colors.blue[600], size: 24),
          const SizedBox(width: 12),
          const Text(
            '编辑商品',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const Spacer(),
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () => _cancelEdit(context),
            tooltip: '取消',
            style: IconButton.styleFrom(
              backgroundColor: Colors.grey[100],
              foregroundColor: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建表单
  Widget _buildForm() {
    return ScreenTypeLayout(
      mobile: _buildMobileForm(),
      tablet: _buildDesktopForm(),
      desktop: _buildDesktopForm(),
    );
  }

  /// 构建移动端表单
  Widget _buildMobileForm() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 基本信息区域
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: Colors.blue[600], size: 20),
                const SizedBox(width: 8),
                const Text(
                  '基本信息',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildBasicInfoSection(),
          ],
        ),
        const SizedBox(height: 24),
        // 重量价格区域
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.scale, color: Colors.amber[600], size: 20),
                const SizedBox(width: 8),
                const Text(
                  '重量价格',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildWeightPriceSection(),
          ],
        ),
        const SizedBox(height: 24),
        // 其他信息区域
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.store, color: Colors.purple[600], size: 20),
                const SizedBox(width: 8),
                const Text(
                  '其他信息',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildOtherInfoSection(),
          ],
        ),
      ],
    );
  }

  /// 构建桌面端表单
  Widget _buildDesktopForm() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 基本信息区域
            Expanded(
              flex: 1,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.blue[600], size: 20),
                      const SizedBox(width: 8),
                      const Text(
                        '基本信息',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildBasicInfoSection(),
                ],
              ),
            ),
            const SizedBox(width: 24),
            // 重量价格区域
            Expanded(
              flex: 1,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.scale, color: Colors.amber[600], size: 20),
                      const SizedBox(width: 8),
                      const Text(
                        '重量价格',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildWeightPriceSection(),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 24),
        // 其他信息区域
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.store, color: Colors.purple[600], size: 20),
                const SizedBox(width: 8),
                const Text(
                  '其他信息',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildOtherInfoSection(),
          ],
        ),
      ],
    );
  }

  /// 构建基本信息区域
  Widget _buildBasicInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildTextField(
          label: '商品条码',
          controller: controller.barcodeController,
          validator: controller.validateBarcode,
        ),
        const SizedBox(height: 16),
        _buildTextField(
          label: '商品名称',
          controller: controller.nameController,
          validator: controller.validateName,
        ),
        const SizedBox(height: 16),
        _buildCategoryDropdown(),
        const SizedBox(height: 16),
        _buildTextField(
          label: '圈口号',
          controller: controller.ringSizeController,
        ),
        const SizedBox(height: 16),
        _buildStatusDropdown(),
      ],
    );
  }

  /// 构建重量价格区域
  Widget _buildWeightPriceSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: _buildTextField(
                label: '金重(g)',
                controller: controller.goldWeightController,
                keyboardType: TextInputType.number,
                validator: controller.validateGoldWeight,
                onChanged: (value) => controller.updateTotalWeight(),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildTextField(
                label: '金价(元/g)',
                controller: controller.goldPriceController,
                keyboardType: TextInputType.number,
                validator: controller.validateGoldPrice,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildTextField(
                label: '银重(g)',
                controller: controller.silverWeightController,
                keyboardType: TextInputType.number,
                validator: controller.validateSilverWeight,
                onChanged: (value) => controller.updateTotalWeight(),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildTextField(
                label: '银价(元/g)',
                controller: controller.silverPriceController,
                keyboardType: TextInputType.number,
                validator: controller.validateSilverPrice,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildTextField(
                label: '总重(g)',
                controller: controller.totalWeightController,
                keyboardType: TextInputType.number,
                isReadOnly: true,
                validator: controller.validateTotalWeight,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildTextField(
                label: '工费(元)',
                controller: controller.workPriceController,
                keyboardType: TextInputType.number,
                validator: controller.validateWorkPrice,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建其他信息区域
  Widget _buildOtherInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildStoreDropdown(),
      ],
    );
  }

  /// 构建文本输入框
  Widget _buildTextField({
    required String label,
    required TextEditingController controller,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
    bool isReadOnly = false,
    void Function(String)? onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          validator: validator,
          readOnly: isReadOnly,
          onChanged: onChanged,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
              borderSide: const BorderSide(
                color: AppBorderStyles.borderColor,
                width: AppBorderStyles.borderWidth,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
              borderSide: const BorderSide(
                color: AppBorderStyles.borderColor,
                width: AppBorderStyles.borderWidth,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
              borderSide: const BorderSide(
                color: AppBorderStyles.focusBorderColor,
                width: AppBorderStyles.focusBorderWidth,
              ),
            ),
            filled: true,
            fillColor: isReadOnly ? Colors.grey[100] : Colors.white,
            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
            isDense: true,
          ),
          style: TextStyle(
            fontSize: 14,
            color: isReadOnly ? Colors.grey[600] : Colors.black87,
          ),
        ),
      ],
    );
  }

  /// 构建分类下拉框
  Widget _buildCategoryDropdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '商品分类',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Obx(() => DropdownButtonFormField<int>(
          value: controller.selectedCategoryId.value == 0 ? null : controller.selectedCategoryId.value,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
              borderSide: const BorderSide(
                color: AppBorderStyles.borderColor,
                width: AppBorderStyles.borderWidth,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
              borderSide: const BorderSide(
                color: AppBorderStyles.borderColor,
                width: AppBorderStyles.borderWidth,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
              borderSide: const BorderSide(
                color: AppBorderStyles.focusBorderColor,
                width: AppBorderStyles.focusBorderWidth,
              ),
            ),
            filled: true,
            fillColor: Colors.white,
            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
            isDense: true,
          ),
          hint: const Text('请选择分类', style: TextStyle(fontSize: 14)),
          items: controller.categoryList.map((category) => DropdownMenuItem<int>(
            value: category.id,
            child: Text(category.name, style: const TextStyle(fontSize: 14)),
          )).toList(),
          onChanged: (value) => controller.selectedCategoryId.value = value ?? 0,
          validator: controller.validateCategory,
        )),
      ],
    );
  }

  /// 构建状态下拉框
  Widget _buildStatusDropdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '商品状态',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Obx(() => DropdownButtonFormField<JewelryStatus>(
          value: controller.selectedStatus.value,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
              borderSide: const BorderSide(
                color: AppBorderStyles.borderColor,
                width: AppBorderStyles.borderWidth,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
              borderSide: const BorderSide(
                color: AppBorderStyles.borderColor,
                width: AppBorderStyles.borderWidth,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
              borderSide: const BorderSide(
                color: AppBorderStyles.focusBorderColor,
                width: AppBorderStyles.focusBorderWidth,
              ),
            ),
            filled: true,
            fillColor: Colors.white,
            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
            isDense: true,
          ),
          items: JewelryStatus.values.map((status) => DropdownMenuItem<JewelryStatus>(
            value: status,
            child: Text(status.label, style: const TextStyle(fontSize: 14)),
          )).toList(),
          onChanged: (value) => controller.selectedStatus.value = value ?? JewelryStatus.onShelf,
        )),
      ],
    );
  }

  /// 构建门店下拉框
  Widget _buildStoreDropdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '所属门店',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Obx(() => DropdownButtonFormField<int>(
          value: controller.selectedStoreId.value == 0 ? null : controller.selectedStoreId.value,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
              borderSide: const BorderSide(
                color: AppBorderStyles.borderColor,
                width: AppBorderStyles.borderWidth,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
              borderSide: const BorderSide(
                color: AppBorderStyles.borderColor,
                width: AppBorderStyles.borderWidth,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
              borderSide: const BorderSide(
                color: AppBorderStyles.focusBorderColor,
                width: AppBorderStyles.focusBorderWidth,
              ),
            ),
            filled: true,
            fillColor: Colors.white,
            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
            isDense: true,
          ),
          hint: const Text('请选择门店', style: TextStyle(fontSize: 14)),
          items: controller.storeList.map((store) => DropdownMenuItem<int>(
            value: store.id,
            child: Text(store.name, style: const TextStyle(fontSize: 14)),
          )).toList(),
          onChanged: (value) => controller.selectedStoreId.value = value ?? 0,
          validator: controller.validateStore,
        )),
      ],
    );
  }

  /// 构建弹窗底部操作按钮
  Widget _buildFooter(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(AppBorderStyles.borderRadius),
          bottomRight: Radius.circular(AppBorderStyles.borderRadius),
        ),
        border: Border(
          top: AppBorderStyles.tableBorder,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          // 取消按钮
          OutlinedButton(
            onPressed: () => _cancelEdit(context),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
              ),
              side: const BorderSide(color: AppBorderStyles.borderColor),
            ),
            child: const Text('取消'),
          ),

          const SizedBox(width: 12),

          // 保存按钮
          Obx(() => ElevatedButton.icon(
            icon: controller.isLoading.value
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.save, size: 18),
            label: Text(controller.isLoading.value ? '保存中...' : '保存'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue[600],
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
              ),
            ),
            onPressed: controller.isLoading.value ? null : () => _saveChanges(context),
          )),
        ],
      ),
    );
  }

  /// 取消编辑
  void _cancelEdit(BuildContext context) {
    // 简化逻辑，直接关闭弹窗
    Navigator.of(context).pop();
  }

  /// 保存更改
  void _saveChanges(BuildContext context) async {
    try {
      LoggerService.d('🚀 编辑弹窗：开始保存流程');

      // 调用控制器的保存方法
      await controller.saveJewelry();

      // 如果执行到这里，说明保存成功
      LoggerService.d('✅ 编辑弹窗：保存成功，准备关闭弹窗');

      // 保存回调函数引用，避免弹窗关闭后访问问题
      final updateCallback = onUpdated;

      // 检查组件是否仍然挂载，然后关闭弹窗
      if (context.mounted) {
        LoggerService.d('🔒 编辑弹窗：关闭弹窗');
        Navigator.of(context).pop(true);

        // 使用延迟确保弹窗完全关闭后再调用回调
        if (updateCallback != null) {
          LoggerService.d('⏰ 编辑弹窗：延迟调用列表刷新回调');
          Future.delayed(const Duration(milliseconds: 200), () {
            try {
              LoggerService.d('🔄 编辑弹窗：执行列表刷新回调');
              updateCallback();
              LoggerService.d('✅ 编辑弹窗：列表刷新回调执行完成');
            } catch (e) {
              LoggerService.e('❌ 编辑弹窗：列表刷新回调执行失败', e);
            }
          });
        }
      } else {
        LoggerService.w('⚠️ 编辑弹窗：组件已卸载，跳过关闭操作');
      }
    } catch (e) {
      // 保存失败时不调用回调，让用户可以继续编辑
      LoggerService.e('❌ 编辑弹窗：保存商品信息失败', e);
      // 错误消息已经在控制器中显示了，这里不需要额外处理
    }
  }
}
