"""
入库管理API路由
提供入库单的增删改查和业务操作接口
"""

from typing import List, Any, Dict
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body
from sqlalchemy.orm import Session

from ....core.database import get_db
from ....core.dependencies import get_current_user, require_permission, require_permissions
from ....schemas.auth import CurrentUserResponse
from ....services.stock_in_service import StockInService
from ....schemas.stock_in import (
    StockInCreate, StockInUpdate, StockInResponse, StockInStatistics,
    StockInStatusUpdate, StockInQueryParams
)
from ....schemas.common import PaginatedResponse, StandardResponse


router = APIRouter()


def get_stock_in_service(db: Session = Depends(get_db)) -> StockInService:
    """获取入库管理服务实例"""
    return StockInService(db)


@router.post("", response_model=StandardResponse[StockInResponse], summary="创建入库单")
async def create_stock_in(
    stock_in_data: StockInCreate = Body(..., description="入库单创建数据"),
    operator_id: int = Query(..., description="操作员ID"),
    service: StockInService = Depends(get_stock_in_service),
    current_user: CurrentUserResponse = Depends(require_permission("stock.create"))
) -> Any:
    """
    创建新的入库单

    **功能说明:**
    - 自动生成入库单号(格式: INYYYYMMDD0001)
    - 验证门店和商品是否存在
    - 自动计算总金额
    - 初始状态为正常(1)

    **业务规则:**
    - 入库商品明细不能为空
    - 所有商品必须存在于系统中
    - 门店必须存在且状态正常
    - 操作员必须存在且状态正常

    **状态说明:**
    - 1: 正常 (可编辑、可删除)
    - 0: 禁用 (不可编辑、不可删除)
    """
    try:
        stock_in = service.create_stock_in(stock_in_data, operator_id)
        return StandardResponse(
            success=True,
            message="入库单创建成功",
            data=service.convert_stock_in_to_dict(stock_in)
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建入库单失败: {str(e)}")


@router.get("", response_model=PaginatedResponse[StockInResponse], summary="获取入库单列表")
async def get_stock_in_list(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    keyword: str = Query(None, description="关键词搜索(单号、备注)"),
    store_id: int = Query(None, description="门店ID筛选"),
    status: int = Query(None, ge=0, le=4, description="状态筛选(0=草稿,1=待审核,2=已审核,3=已拒绝,4=已取消)"),
    start_date: str = Query(None, regex=r'^\d{4}-\d{2}-\d{2}$', description="开始日期(YYYY-MM-DD)"),
    end_date: str = Query(None, regex=r'^\d{4}-\d{2}-\d{2}$', description="结束日期(YYYY-MM-DD)"),
    operator_id: int = Query(None, description="操作员ID筛选"),
    supplier: str = Query(None, description="供应商筛选"),
    service: StockInService = Depends(get_stock_in_service),
    current_user: CurrentUserResponse = Depends(get_current_user)
) -> Any:
    """
    获取入库单列表，支持分页和多条件筛选

    **查询功能:**
    - 📄 分页查询，支持自定义页码和每页数量
    - 🔍 关键词搜索，支持按入库单号、备注模糊搜索
    - 🏪 门店筛选，查看指定门店的入库单
    - 📊 状态筛选，按入库单状态过滤
    - 📅 日期范围筛选，查看指定时间段的入库单
    - 👤 操作员筛选，查看指定操作员的入库单
    - 🏭 供应商筛选，查看指定供应商的入库单

    **返回信息:**
    - 入库单基本信息(单号、门店、金额、状态等)
    - 关联门店信息
    - 关联操作员信息
    - 审核相关信息
    - 入库商品明细列表
    - 分页统计信息
    """
    try:
        # 🔒 门店级别数据过滤逻辑
        # 如果当前用户有指定门店，则只能查看该门店的数据
        # 如果是管理员账号（没有指定门店），则可以查看所有门店数据
        filtered_store_id = store_id
        if current_user.store_id:
            # 普通员工账号，强制过滤为用户所属门店
            filtered_store_id = current_user.store_id
        else:
            # 管理员账号，可以查看所有门店或指定门店
            pass

        params = StockInQueryParams(
            page=page,
            page_size=page_size,
            keyword=keyword,
            store_id=filtered_store_id,  # 使用过滤后的门店ID
            status=status,
            start_date=start_date,
            end_date=end_date,
            operator_id=operator_id,
            supplier=supplier
        )

        stock_ins, total = service.get_stock_in_list(params)

        # 转换数据
        converted_data = []
        for stock_in in stock_ins:
            converted_item = service.convert_stock_in_to_dict(stock_in)
            converted_data.append(converted_item)

        return PaginatedResponse(
            success=True,
            message="获取入库单列表成功",
            data=converted_data,
            pagination={
                "page": page,
                "page_size": page_size,
                "total": total,
                "pages": (total + page_size - 1) // page_size
            }
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取入库单列表失败: {str(e)}")


@router.get("/{stock_in_id}", response_model=StandardResponse[StockInResponse], summary="获取入库单详情")
async def get_stock_in_detail(
    stock_in_id: int = Path(..., description="入库单ID"),
    service: StockInService = Depends(get_stock_in_service),
    current_user: CurrentUserResponse = Depends(require_permission("stock.view"))
) -> Any:
    """
    根据ID获取入库单详细信息

    **返回信息:**
    - 📋 入库单完整信息
    - 🏪 关联门店详细信息
    - 👤 关联操作员信息
    - 👨‍💼 关联审核员信息
    - 📦 入库商品明细列表
    - 💎 每个明细的完整珠宝信息
    """
    stock_in = service.get_stock_in_by_id(stock_in_id)
    if not stock_in:
        raise HTTPException(status_code=404, detail="入库单不存在")

    # 转换数据
    converted_data = service.convert_stock_in_to_dict(stock_in)

    return StandardResponse(
        success=True,
        message="获取入库单详情成功",
        data=converted_data
    )


@router.get("/by-no/{order_no}", response_model=StandardResponse[StockInResponse], summary="根据单号获取入库单")
async def get_stock_in_by_no(
    order_no: str = Path(..., description="入库单号"),
    service: StockInService = Depends(get_stock_in_service),
    current_user: CurrentUserResponse = Depends(require_permission("stock.view"))
) -> Any:
    """
    根据入库单号获取入库单详细信息

    **使用场景:**
    - 📱 扫码查询入库单
    - 🔍 快速单号搜索
    - 📋 单号关联查询
    """
    stock_in = service.get_stock_in_by_no(order_no)
    if not stock_in:
        raise HTTPException(status_code=404, detail="入库单不存在")

    return StandardResponse(
        success=True,
        message="获取入库单详情成功",
        data=service.convert_stock_in_to_dict(stock_in)
    )


@router.put("/{stock_in_id}", response_model=StandardResponse[StockInResponse], summary="更新入库单")
async def update_stock_in(
    stock_in_id: int = Path(..., description="入库单ID"),
    stock_in_data: StockInUpdate = Body(..., description="入库单更新数据"),
    service: StockInService = Depends(get_stock_in_service),
    current_user: CurrentUserResponse = Depends(require_permission("stock.update"))
) -> Any:
    """
    更新入库单信息

    **更新内容:**
    - 门店信息
    - 供应商信息
    - 备注信息
    - 入库商品明细(完全替换)
    - 自动重新计算总金额
    """
    try:
        stock_in = service.update_stock_in(stock_in_id, stock_in_data)
        if not stock_in:
            raise HTTPException(status_code=404, detail="入库单不存在")

        return StandardResponse(
            success=True,
            message="入库单更新成功",
            data=service.convert_stock_in_to_dict(stock_in)
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新入库单失败: {str(e)}")


@router.delete("/{stock_in_id}", response_model=StandardResponse[str], summary="删除入库单")
async def delete_stock_in(
    stock_in_id: int = Path(..., description="入库单ID"),
    service: StockInService = Depends(get_stock_in_service),
    current_user: CurrentUserResponse = Depends(require_permission("stock.delete"))
) -> Any:
    """
    删除入库单

    **删除效果:**
    - 🗑️ 删除入库单主表记录
    - 🗑️ 自动删除所有入库商品明细
    - ⚠️ 此操作不可恢复，请谨慎操作
    """
    try:
        success = service.delete_stock_in(stock_in_id)
        if not success:
            raise HTTPException(status_code=404, detail="入库单不存在")

        return StandardResponse(
            success=True,
            message="入库单删除成功",
            data="删除操作已完成"
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除入库单失败: {str(e)}")


@router.patch("/{stock_in_id}/status", response_model=StandardResponse[StockInResponse], summary="更新入库单状态")
async def update_stock_in_status(
    stock_in_id: int = Path(..., description="入库单ID"),
    status_data: StockInStatusUpdate = Body(..., description="状态更新数据"),
    auditor_id: int = Query(..., description="审核员ID"),
    service: StockInService = Depends(get_stock_in_service),
    current_user: CurrentUserResponse = Depends(require_permission("stock.audit"))
) -> Any:
    """
    更新入库单状态

    **状态说明:**
    - 0: 草稿 - 可编辑状态
    - 1: 待审核 - 提交审核状态
    - 2: 已通过 - 审核通过状态
    - 3: 已拒绝 - 审核拒绝状态

    **审核功能:**
    - 记录审核员ID
    - 记录审核时间
    - 记录审核备注
    """
    print(f"🚀 [审核API] 收到审核请求")
    print(f"   入库单ID: {stock_in_id}")
    print(f"   审核员ID: {auditor_id}")
    print(f"   状态数据: {status_data.dict()}")
    print(f"   当前用户: {current_user.username} (ID: {current_user.id})")

    try:
        # 验证审核员ID是否与当前用户匹配
        if auditor_id != current_user.id:
            print(f"⚠️ [审核API] 审核员ID不匹配: 请求={auditor_id}, 当前用户={current_user.id}")
            # 注意：这里可以选择使用当前用户ID或者报错
            # 为了安全起见，使用当前用户ID
            auditor_id = current_user.id
            print(f"🔧 [审核API] 已修正审核员ID为当前用户: {auditor_id}")

        print(f"📡 [审核API] 调用服务层更新状态...")
        stock_in = service.update_stock_in_status(stock_in_id, status_data, auditor_id)

        if not stock_in:
            print(f"❌ [审核API] 入库单不存在: ID={stock_in_id}")
            raise HTTPException(status_code=404, detail="入库单不存在")

        status_names = {0: "草稿", 1: "待审核", 2: "已通过", 3: "已拒绝"}
        status_name = status_names.get(status_data.status, "未知状态")

        print(f"✅ [审核API] 审核成功")
        print(f"   入库单: {stock_in.order_no}")
        print(f"   状态: {status_name}")
        print(f"   审核员: {stock_in.audit_user_id}")

        response_data = service.convert_stock_in_to_dict(stock_in)
        print(f"📤 [审核API] 返回响应数据")

        return StandardResponse(
            success=True,
            message=f"入库单状态更新为【{status_name}】成功",
            data=response_data
        )
    except ValueError as e:
        print(f"❌ [审核API] 参数错误: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        print(f"❌ [审核API] 系统错误: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"更新入库单状态失败: {str(e)}")


@router.get("/statistics/summary", response_model=StandardResponse[StockInStatistics], summary="获取入库单统计信息")
async def get_stock_in_statistics(
    service: StockInService = Depends(get_stock_in_service),
    current_user: CurrentUserResponse = Depends(require_permission("stock.view"))
) -> Any:
    """
    获取入库单统计信息

    **统计内容:**
    - 📊 **基础统计**: 总数量、正常/禁用数量分布
    - 💰 **金额统计**: 所有入库单的总金额
    - 🏪 **门店分布**: 各门店的入库单数量和金额统计

    **应用场景:**
    - 📈 数据报表展示
    - 📊 业务分析统计
    - 🎯 运营决策支持
    - 📋 管理驾驶舱
    """
    try:
        statistics = service.get_stock_in_statistics()
        return StandardResponse(
            success=True,
            message="获取入库单统计信息成功",
            data=statistics
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")