import 'package:get/get.dart';

import '../../../core/config/app_config.dart';
import '../../../core/services/api_service.dart';
import '../../../core/services/storage_service.dart';
import '../../../core/utils/logger_service.dart';
import '../../../models/recycling/recycling_model.dart';
import '../../../models/recycling/recycling_pagination_result.dart';

/// 回收服务类，处理回收相关业务逻辑
/// 支持真实API调用和模拟数据后备
class RecyclingService {
  final ApiService _apiService = Get.find<ApiService>();

  // 本地模拟数据作为后备
  final List<RecyclingOrder> _mockOrders = [];

  // 构造函数 - 初始化模拟数据
  RecyclingService() {
    _initMockData();
  }

  /// 获取回收单列表（带分页）
  Future<RecyclingPaginationResult> getRecyclingOrdersWithPagination(
    Map<String, dynamic> params,
  ) async {
    try {
      LoggerService.d('开始获取回收单列表，参数: $params');

      // 构建查询参数
      final Map<String, dynamic> queryParams = {};

      // 状态过滤
      if (params.containsKey('status') && params['status'] != -1) {
        queryParams['status'] = params['status'];
      }

      // 搜索过滤
      if (params.containsKey('search') && params['search'].isNotEmpty) {
        queryParams['keyword'] = params['search'];
      }

      // 分页参数
      queryParams['page'] = params['page'] ?? 1;
      queryParams['page_size'] =
          params['page_size'] ?? AppConfig.defaultPageSize;

      final response = await _apiService.get(
        '${AppConfig.apiEndpoint['recycling']}',
        queryParameters: queryParams,
      );

      LoggerService.d('API响应: ${response.data}');

      // 处理API响应
      if (response.data['success'] == true) {
        final result = RecyclingPaginationResult.fromApiResponse(response.data);
        LoggerService.d(
          '成功获取${result.orders.length}条回收单记录，总共${result.totalRecords}条，第${result.currentPage}页/共${result.totalPages}页',
        );
        return result;
      } else {
        throw Exception(response.data['message'] ?? '获取回收单列表失败');
      }
    } catch (e) {
      LoggerService.e('获取回收单列表失败', e);

      // 如果API调用失败，返回模拟数据作为后备
      LoggerService.w('API调用失败，使用模拟数据');
      return _getMockOrdersWithPagination(params);
    }
  }

  /// 获取回收单列表（兼容旧版本，不带分页）
  Future<List<RecyclingOrder>> getRecyclingOrders(
    Map<String, dynamic> params,
  ) async {
    final result = await getRecyclingOrdersWithPagination(params);
    return result.orders;
  }

  /// 获取回收单详情
  Future<RecyclingOrder> getRecyclingOrderDetail(int id) async {
    try {
      LoggerService.d('🔍 RecyclingService.getRecyclingOrderDetail 开始，ID: $id');

      // 检查认证状态
      final storageService = Get.find<StorageService>();
      final token = storageService.getToken();
      if (token != null && token.isNotEmpty) {
        LoggerService.d('🔐 JWT Token存在: ${token.substring(0, 20)}...');
      } else {
        LoggerService.w('⚠️ JWT Token不存在，API调用可能失败');
      }

      final apiUrl = '${AppConfig.apiEndpoint['recycling']}/$id';
      LoggerService.d('🚀 准备调用API: $apiUrl');

      final response = await _apiService.get(apiUrl);

      LoggerService.d('📡 API响应状态码: ${response.statusCode}');
      LoggerService.d('📡 API完整响应: ${response.data}');

      // 处理API响应
      if (response.data['success'] == true) {
        final orderData = response.data['data'];
        LoggerService.d('📊 回收单原始数据: $orderData');

        // 特别检查items字段
        if (orderData['items'] != null) {
          LoggerService.d('📦 API返回的items字段: ${orderData['items']}');
          LoggerService.d('📦 items类型: ${orderData['items'].runtimeType}');
          LoggerService.d('📦 items长度: ${(orderData['items'] as List?)?.length ?? 0}');

          // 打印每个item的详细信息
          if (orderData['items'] is List) {
            final itemsList = orderData['items'] as List;
            for (int i = 0; i < itemsList.length; i++) {
              LoggerService.d('📦 Item $i: ${itemsList[i]}');
            }
          }
        } else {
          LoggerService.w('⚠️ API返回的数据中没有items字段');
        }

        LoggerService.d('🔄 开始解析RecyclingOrder.fromJson');
        final order = RecyclingOrder.fromJson(orderData);

        LoggerService.d('✅ 成功解析回收单: ${order.orderNo}, 明细数量: ${order.items.length}');
        return order;
      } else {
        LoggerService.e('❌ API返回失败: ${response.data['message']}');
        throw Exception(response.data['message'] ?? '获取回收单详情失败');
      }
    } catch (e) {
      LoggerService.e('❌ 获取回收单详情异常', e);

      // 如果API调用失败，从模拟数据中查找
      LoggerService.w('🔄 API调用失败，使用模拟数据');
      final order = _mockOrders.firstWhere(
        (order) => order.id == id,
        orElse: () => throw Exception('回收单不存在'),
      );
      LoggerService.d('📋 使用模拟数据: ${order.orderNo}, 明细数量: ${order.items.length}');
      return order;
    }
  }

  /// 创建回收单
  Future<void> createRecyclingOrder(Map<String, dynamic> data) async {
    try {
      LoggerService.d('开始创建回收单，数据: $data');

      // 添加operator_id查询参数
      final response = await _apiService.post(
        '${AppConfig.apiEndpoint['recycling']}?operator_id=1', // TODO: 从用户认证服务获取真实的operator_id
        data: data,
      );

      LoggerService.d('API响应: ${response.data}');

      // 处理API响应
      if (response.data['success'] == true) {
        LoggerService.d('回收单创建成功');
        return;
      } else {
        throw Exception(response.data['message'] ?? '创建回收单失败');
      }
    } catch (e) {
      LoggerService.e('创建回收单失败', e);

      // 如果API调用失败，添加到模拟数据作为后备
      LoggerService.w('API调用失败，使用模拟数据');
      _createMockOrder(data);
    }
  }

  /// 更新回收单
  Future<void> updateRecyclingOrder(Map<String, dynamic> data) async {
    try {
      LoggerService.d('开始更新回收单，数据: $data');

      final id = data['id'];
      if (id == null) {
        throw Exception('更新回收单时缺少ID');
      }

      final response = await _apiService.put(
        '${AppConfig.apiEndpoint['recycling']}/$id',
        data: data,
      );

      LoggerService.d('API响应: ${response.data}');

      // 处理API响应
      if (response.data['success'] == true) {
        LoggerService.d('回收单更新成功');
        return;
      } else {
        throw Exception(response.data['message'] ?? '更新回收单失败');
      }
    } catch (e) {
      LoggerService.e('更新回收单失败', e);

      // 如果API调用失败，更新模拟数据作为后备
      LoggerService.w('API调用失败，使用模拟数据');
      _updateMockOrder(data);
    }
  }

  /// 创建模拟回收单（作为API调用失败时的后备）
  void _createMockOrder(Map<String, dynamic> data) {
    final newId = _mockOrders.isEmpty ? 1001 : _mockOrders.last.id + 1;
    final newOrderNo =
        'RC${DateTime.now().toString().replaceAll(RegExp(r'[^0-9]'), '').substring(0, 10)}$newId';

    final newOrder = RecyclingOrder(
      id: newId,
      orderNo: newOrderNo,
      orderDate: DateTime.now(),
      customerId: data['customer_id'] ?? 100,
      customerName: data['customer_name'] ?? '演示客户',
      customerPhone: data['customer_phone'] ?? '13800138000',
      totalWeight: data['total_weight'] ?? 0.0,
      totalAmount: data['total_amount'] ?? 0.0,
      itemCount: data['item_count'] ?? 1, // 默认1件
      remark: data['remark'] ?? '',
      status: 0, // 待处理
      createdAt: DateTime.now(),
      createdBy: 1,
      creatorName: '系统用户',
      storeId: data['store_id'] ?? 1, // 默认门店ID
      storeName: data['store_name'] ?? '总店', // 默认门店名称
      items: [], // 暂不处理子项
    );

    _mockOrders.add(newOrder);
  }

  /// 更新回收单状态
  Future<void> updateRecyclingOrderStatus(int id, int status) async {
    try {
      LoggerService.d('开始更新回收单状态，ID: $id, 状态: $status');

      final response = await _apiService.put(
        '${AppConfig.apiEndpoint['recycling']}/$id/status',
        data: {'status': status},
      );

      LoggerService.d('API响应: ${response.data}');

      // 处理API响应
      if (response.data['success'] == true) {
        LoggerService.d('回收单状态更新成功');
        return;
      } else {
        throw Exception(response.data['message'] ?? '更新回收单状态失败');
      }
    } catch (e) {
      LoggerService.e('更新回收单状态失败', e);

      // 如果API调用失败，更新模拟数据作为后备
      LoggerService.w('API调用失败，使用模拟数据');
      _updateMockOrderStatus(id, status);
    }
  }

  /// 更新模拟回收单状态（作为API调用失败时的后备）
  void _updateMockOrderStatus(int id, int status) {
    final index = _mockOrders.indexWhere((order) => order.id == id);
    if (index == -1) {
      throw Exception('回收单不存在');
    }

    // 由于RecyclingOrder是不可变的，我们需要创建一个新对象
    final oldOrder = _mockOrders[index];
    final updatedOrder = RecyclingOrder(
      id: oldOrder.id,
      orderNo: oldOrder.orderNo,
      orderDate: oldOrder.orderDate,
      customerId: oldOrder.customerId,
      customerName: oldOrder.customerName,
      customerPhone: oldOrder.customerPhone,
      totalWeight: oldOrder.totalWeight,
      totalAmount: oldOrder.totalAmount,
      itemCount: oldOrder.itemCount,
      remark: oldOrder.remark,
      status: status, // 更新状态
      createdAt: oldOrder.createdAt,
      createdBy: oldOrder.createdBy,
      creatorName: oldOrder.creatorName,
      storeId: oldOrder.storeId,
      storeName: oldOrder.storeName,
      items: oldOrder.items,
    );

    _mockOrders[index] = updatedOrder;
  }

  /// 更新模拟数据中的回收单
  void _updateMockOrder(Map<String, dynamic> data) {
    final id = data['id'];
    if (id == null) return;

    final index = _mockOrders.indexWhere((order) => order.id == id);
    if (index == -1) {
      throw Exception('回收单不存在');
    }

    // 由于RecyclingOrder是不可变的，我们需要创建一个新对象
    final oldOrder = _mockOrders[index];
    final updatedOrder = RecyclingOrder(
      id: oldOrder.id,
      orderNo: oldOrder.orderNo,
      orderDate: oldOrder.orderDate,
      customerId: oldOrder.customerId,
      customerName: data['customer_name'] ?? oldOrder.customerName,
      customerPhone: data['customer_phone'] ?? oldOrder.customerPhone,
      totalWeight: oldOrder.totalWeight,
      totalAmount: oldOrder.totalAmount,
      itemCount: oldOrder.itemCount,
      remark: data['remark'] ?? oldOrder.remark,
      status: oldOrder.status,
      createdAt: oldOrder.createdAt,
      createdBy: oldOrder.createdBy,
      creatorName: oldOrder.creatorName,
      storeId: oldOrder.storeId,
      storeName: oldOrder.storeName,
      items: oldOrder.items,
    );

    _mockOrders[index] = updatedOrder;
  }

  /// 删除回收单
  Future<void> deleteRecyclingOrder(int id) async {
    try {
      LoggerService.d('开始删除回收单，ID: $id');

      final response = await _apiService.delete(
        '${AppConfig.apiEndpoint['recycling']}/$id',
      );

      LoggerService.d('API响应: ${response.data}');

      // 处理API响应
      if (response.data['success'] == true) {
        LoggerService.d('回收单删除成功');
        return;
      } else {
        throw Exception(response.data['message'] ?? '删除回收单失败');
      }
    } catch (e) {
      LoggerService.e('删除回收单失败', e);

      // 如果API调用失败，从模拟数据中删除作为后备
      LoggerService.w('API调用失败，使用模拟数据');
      _mockOrders.removeWhere((order) => order.id == id);
    }
  }

  /// 获取模拟数据（带分页，作为API调用失败时的后备）
  RecyclingPaginationResult _getMockOrdersWithPagination(
    Map<String, dynamic> params,
  ) {
    // 根据查询参数过滤数据
    List<RecyclingOrder> result = List.from(_mockOrders);

    // 状态过滤
    if (params.containsKey('status') && params['status'] != -1) {
      final status = params['status'] as int;
      result = result.where((order) => order.status == status).toList();
    }

    // 搜索过滤
    if (params.containsKey('search') && params['search'].isNotEmpty) {
      final search = params['search'] as String;
      result = result
          .where(
            (order) =>
                order.orderNo.contains(search) ||
                order.customerName.contains(search) ||
                order.customerPhone.contains(search),
          )
          .toList();
    }

    // 分页处理
    final page = params['page'] ?? 1;
    final pageSize = params['page_size'] ?? 20;
    final totalRecords = result.length;
    final totalPages = (totalRecords / pageSize).ceil();

    final startIndex = (page - 1) * pageSize;
    final endIndex = (startIndex + pageSize).clamp(0, totalRecords);

    final pagedResult = result.sublist(startIndex, endIndex);

    return RecyclingPaginationResult(
      orders: pagedResult,
      currentPage: page,
      totalPages: totalPages,
      totalRecords: totalRecords,
      pageSize: pageSize,
    );
  }

  /// 获取模拟数据（作为API调用失败时的后备）
  List<RecyclingOrder> _getMockOrders(Map<String, dynamic> params) {
    final result = _getMockOrdersWithPagination(params);
    return result.orders;
  }

  /// 初始化模拟数据 - 生成足够的数据用于测试分页功能
  void _initMockData() {
    final now = DateTime.now();

    // 生成50条模拟数据，用于测试分页功能
    for (int i = 1; i <= 50; i++) {
      final orderDate = now.subtract(Duration(days: i));
      final orderId = 1000 + i;
      final orderNo =
          'RC${orderDate.year}${orderDate.month.toString().padLeft(2, '0')}${orderDate.day.toString().padLeft(2, '0')}${i.toString().padLeft(3, '0')}';

      // 随机生成客户信息
      final customers = [
        '张三',
        '李四',
        '王五',
        '赵六',
        '钱七',
        '孙八',
        '周九',
        '吴十',
        '郑十一',
        '王十二',
      ];
      final customerName = customers[i % customers.length];
      final customerPhone =
          '138${(i % 100).toString().padLeft(2, '0')}${(i % 1000).toString().padLeft(3, '0')}${(i % 10000).toString().padLeft(4, '0')}';

      // 随机生成重量和金额
      final weight = 10.0 + (i % 30);
      final amount = weight * 400.0;

      // 随机生成状态
      final status = i % 4; // 0-3的状态

      // 随机生成门店信息
      final stores = [
        {'id': 1, 'name': '总店'},
        {'id': 2, 'name': '分店A'},
        {'id': 3, 'name': '分店B'},
        {'id': 4, 'name': '分店C'},
      ];
      final storeInfo = stores[i % stores.length];

      _mockOrders.add(
        RecyclingOrder(
          id: orderId,
          orderNo: orderNo,
          orderDate: orderDate,
          customerId: 100 + i,
          customerName: customerName,
          customerPhone: customerPhone,
          totalWeight: weight,
          totalAmount: amount,
          itemCount: 1 + (i % 3), // 1-3件
          remark: '回收单 #$i - 测试数据',
          status: status,
          createdAt: orderDate,
          createdBy: 1,
          creatorName: '系统管理员',
          storeId: storeInfo['id'] as int,
          storeName: storeInfo['name'] as String,
          items: [
            RecyclingItem(
              id: 2000 + i,
              orderId: orderId,
              categoryId: 1,
              categoryName: '黄金首饰',
              itemName: '金饰品 #$i',
              weight: weight,
              price: 400.0,
              amount: amount,
              goldWeight: weight * 0.8, // 80%为金重
              goldPrice: 450.0,
              goldAmount: weight * 0.8 * 450.0,
              silverWeight: weight * 0.2, // 20%为银重
              silverPrice: 8.0,
              silverAmount: weight * 0.2 * 8.0,
              discountRate: 95.0, // 95%折扣率
              discountAmount: amount * 0.05, // 5%折扣金额
              photoUrl: 'assets/images/samples/gold_item_$i.jpg',
              status: status,
            ),
          ],
        ),
      );
    }

    LoggerService.d('初始化了${_mockOrders.length}条模拟回收单数据');
  }
}
