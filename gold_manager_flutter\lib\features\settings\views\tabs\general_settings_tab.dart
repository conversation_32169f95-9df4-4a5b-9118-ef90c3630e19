import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../controllers/settings_controller.dart';
import '../../models/setting_model.dart';

/// 基本设置页面
class GeneralSettingsTab extends StatelessWidget {
  const GeneralSettingsTab({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<SettingsController>();
    
    return Obx(() {
      final settings = controller.settings.value;
      
      return SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionHeader('基本配置'),
            _buildGeneralSettings(context, controller, settings.generalSettings),
            const SizedBox(height: 24),
            
            _buildSectionHeader('价格配置'),
            _buildPriceSettings(context, controller, settings.priceSettings),
            const SizedBox(height: 24),
            
            _buildSectionHeader('显示配置'),
            _buildDisplaySettings(context, controller, settings.displaySettings),
            const SizedBox(height: 24),
            
            _buildSectionHeader('数据同步配置'),
            _buildSyncSettings(context, controller, settings.syncSettings),
            const SizedBox(height: 24),
            
            _buildSectionHeader('备份配置'),
            _buildBackupSettings(context, controller, settings.backupSettings),
            const SizedBox(height: 32),
            
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                OutlinedButton.icon(
                  onPressed: () => _onResetSettings(context, controller),
                  icon: const Icon(Icons.refresh),
                  label: const Text('重置为默认设置'),
                ),
                const SizedBox(width: 16),
                ElevatedButton.icon(
                  onPressed: () => _onSaveSettings(controller),
                  icon: const Icon(Icons.save),
                  label: const Text('保存设置'),
                ),
              ],
            ),
          ],
        ),
      );
    });
  }
  
  /// 构建模块标题
  Widget _buildSectionHeader(String title) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const Divider(),
        const SizedBox(height: 16),
      ],
    );
  }
  
  /// 构建基本设置选项
  Widget _buildGeneralSettings(BuildContext context, SettingsController controller, GeneralSettings generalSettings) {
    // 门店选项
    final storeOptions = [
      {'id': 0, 'name': '未选择'},
      {'id': 1, 'name': '总店'},
      {'id': 2, 'name': '分店一'},
      {'id': 3, 'name': '分店二'},
    ];
    
    // 语言选项
    final languageOptions = [
      {'code': 'zh_CN', 'name': '简体中文'},
      {'code': 'en_US', 'name': 'English'},
    ];
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // 默认门店
            DropdownButtonFormField<int>(
              value: generalSettings.defaultStoreId,
              decoration: const InputDecoration(
                labelText: '默认门店',
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              items: storeOptions
                  .map((store) => DropdownMenuItem<int>(
                        value: store['id'] as int,
                        child: Text(store['name'] as String),
                      ))
                  .toList(),
              onChanged: (value) {
                if (value != null) {
                  final newSettings = generalSettings.copyWith(defaultStoreId: value);
                  controller.updateGeneralSettings(newSettings);
                }
              },
            ),
            const SizedBox(height: 16),
            
            // 默认语言
            DropdownButtonFormField<String>(
              value: generalSettings.language,
              decoration: const InputDecoration(
                labelText: '默认语言',
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              items: languageOptions
                  .map((language) => DropdownMenuItem<String>(
                        value: language['code'] as String,
                        child: Text(language['name'] as String),
                      ))
                  .toList(),
              onChanged: (value) {
                if (value != null) {
                  final newSettings = generalSettings.copyWith(language: value);
                  controller.updateGeneralSettings(newSettings);
                }
              },
            ),
            const SizedBox(height: 16),
            
            // 每页显示记录数
            TextFormField(
              initialValue: generalSettings.pageSize.toString(),
              decoration: const InputDecoration(
                labelText: '默认每页显示记录数',
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              keyboardType: TextInputType.number,
              onChanged: (value) {
                if (value.isNotEmpty) {
                  final newSize = int.tryParse(value) ?? generalSettings.pageSize;
                  if (newSize > 0) {
                    final newSettings = generalSettings.copyWith(pageSize: newSize);
                    controller.updateGeneralSettings(newSettings);
                  }
                }
              },
            ),
            const SizedBox(height: 16),
            
            // 启用离线模式
            SwitchListTile(
              title: const Text('启用离线模式'),
              subtitle: const Text('允许在无网络环境下使用基本功能'),
              value: generalSettings.enableOfflineMode,
              onChanged: (value) {
                final newSettings = generalSettings.copyWith(enableOfflineMode: value);
                controller.updateGeneralSettings(newSettings);
              },
            ),
          ],
        ),
      ),
    );
  }
  
  /// 构建价格设置选项
  Widget _buildPriceSettings(BuildContext context, SettingsController controller, PriceSettings priceSettings) {
    // 工费计算方式
    final feeTypes = [
      {'id': 0, 'name': '按比例计算 (%)'},
      {'id': 1, 'name': '按重量计算 (元/克)'},
      {'id': 2, 'name': '固定金额 (元)'},
    ];
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // 默认金价
            TextFormField(
              initialValue: priceSettings.defaultGoldPrice.toString(),
              decoration: const InputDecoration(
                labelText: '默认金价 (元/克)',
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              onChanged: (value) {
                if (value.isNotEmpty) {
                  final newPrice = double.tryParse(value) ?? priceSettings.defaultGoldPrice;
                  if (newPrice > 0) {
                    final newSettings = priceSettings.copyWith(defaultGoldPrice: newPrice);
                    controller.updatePriceSettings(newSettings);
                  }
                }
              },
            ),
            const SizedBox(height: 16),
            
            // 默认银价
            TextFormField(
              initialValue: priceSettings.defaultSilverPrice.toString(),
              decoration: const InputDecoration(
                labelText: '默认银价 (元/克)',
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              onChanged: (value) {
                if (value.isNotEmpty) {
                  final newPrice = double.tryParse(value) ?? priceSettings.defaultSilverPrice;
                  if (newPrice > 0) {
                    final newSettings = priceSettings.copyWith(defaultSilverPrice: newPrice);
                    controller.updatePriceSettings(newSettings);
                  }
                }
              },
            ),
            const SizedBox(height: 16),
            
            // 工费计算方式
            DropdownButtonFormField<int>(
              value: priceSettings.workingFeeType,
              decoration: const InputDecoration(
                labelText: '工费计算方式',
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              items: feeTypes
                  .map((type) => DropdownMenuItem<int>(
                        value: type['id'] as int,
                        child: Text(type['name'] as String),
                      ))
                  .toList(),
              onChanged: (value) {
                if (value != null) {
                  final newSettings = priceSettings.copyWith(workingFeeType: value);
                  controller.updatePriceSettings(newSettings);
                }
              },
            ),
            const SizedBox(height: 16),
            
            // 工费相关字段，根据选择的计算方式显示
            if (priceSettings.workingFeeType == 0) // 按比例计算
              TextFormField(
                initialValue: priceSettings.defaultWorkingFeeRate.toString(),
                decoration: const InputDecoration(
                  labelText: '默认工费比例 (%)',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                ),
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                onChanged: (value) {
                  if (value.isNotEmpty) {
                    final newRate = double.tryParse(value) ?? priceSettings.defaultWorkingFeeRate;
                    if (newRate >= 0) {
                      final newSettings = priceSettings.copyWith(defaultWorkingFeeRate: newRate);
                      controller.updatePriceSettings(newSettings);
                    }
                  }
                },
              ),
            
            if (priceSettings.workingFeeType == 1) // 按重量计算
              TextFormField(
                initialValue: priceSettings.defaultWorkingFeePerGram.toString(),
                decoration: const InputDecoration(
                  labelText: '默认克工费 (元/克)',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                ),
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                onChanged: (value) {
                  if (value.isNotEmpty) {
                    final newFee = double.tryParse(value) ?? priceSettings.defaultWorkingFeePerGram;
                    if (newFee >= 0) {
                      final newSettings = priceSettings.copyWith(defaultWorkingFeePerGram: newFee);
                      controller.updatePriceSettings(newSettings);
                    }
                  }
                },
              ),
            
            const SizedBox(height: 16),
            
            // 启用价格预警
            SwitchListTile(
              title: const Text('启用价格预警'),
              subtitle: const Text('当金银价格波动超过阈值时显示提醒'),
              value: priceSettings.enablePriceAlert,
              onChanged: (value) {
                final newSettings = priceSettings.copyWith(enablePriceAlert: value);
                controller.updatePriceSettings(newSettings);
              },
            ),
          ],
        ),
      ),
    );
  }
  
  /// 构建显示设置选项
  Widget _buildDisplaySettings(BuildContext context, SettingsController controller, DisplaySettings displaySettings) {
    // 主题模式选项
    final themeModes = [
      {'id': 0, 'name': '跟随系统'},
      {'id': 1, 'name': '深色模式'},
      {'id': 2, 'name': '浅色模式'},
    ];
    
    // 表格密度选项
    final tableDensities = [
      {'id': 0, 'name': '紧凑'},
      {'id': 1, 'name': '标准'},
      {'id': 2, 'name': '宽松'},
    ];
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // 主题模式
            DropdownButtonFormField<int>(
              value: displaySettings.themeMode,
              decoration: const InputDecoration(
                labelText: '主题模式',
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              items: themeModes
                  .map((mode) => DropdownMenuItem<int>(
                        value: mode['id'] as int,
                        child: Text(mode['name'] as String),
                      ))
                  .toList(),
              onChanged: (value) {
                if (value != null) {
                  final newSettings = displaySettings.copyWith(themeMode: value);
                  controller.updateDisplaySettings(newSettings);
                }
              },
            ),
            const SizedBox(height: 16),
            
            // 字体大小
            Slider(
              value: displaySettings.fontSizeScale,
              min: 0.8,
              max: 1.5,
              divisions: 7,
              label: '${(displaySettings.fontSizeScale * 100).toInt()}%',
              onChanged: (value) {
                final newSettings = displaySettings.copyWith(fontSizeScale: value);
                controller.updateDisplaySettings(newSettings);
              },
            ),
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('字体大小'),
                  Text('A', style: TextStyle(fontSize: 12)),
                  Text('A', style: TextStyle(fontSize: 16)),
                  Text('A', style: TextStyle(fontSize: 20)),
                ],
              ),
            ),
            const SizedBox(height: 16),
            
            // 表格密度
            DropdownButtonFormField<int>(
              value: displaySettings.tableDensity,
              decoration: const InputDecoration(
                labelText: '表格密度',
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              items: tableDensities
                  .map((density) => DropdownMenuItem<int>(
                        value: density['id'] as int,
                        child: Text(density['name'] as String),
                      ))
                  .toList(),
              onChanged: (value) {
                if (value != null) {
                  final newSettings = displaySettings.copyWith(tableDensity: value);
                  controller.updateDisplaySettings(newSettings);
                }
              },
            ),
            const SizedBox(height: 16),
            
            // 显示库存预警
            SwitchListTile(
              title: const Text('显示库存预警'),
              subtitle: const Text('当库存低于阈值时显示提醒'),
              value: displaySettings.showStockAlert,
              onChanged: (value) {
                final newSettings = displaySettings.copyWith(showStockAlert: value);
                controller.updateDisplaySettings(newSettings);
              },
            ),
            
            // 启用声音提示
            SwitchListTile(
              title: const Text('启用声音提示'),
              subtitle: const Text('操作完成或出错时播放提示音'),
              value: displaySettings.enableSoundNotification,
              onChanged: (value) {
                final newSettings = displaySettings.copyWith(enableSoundNotification: value);
                controller.updateDisplaySettings(newSettings);
              },
            ),
          ],
        ),
      ),
    );
  }
  
  /// 构建同步设置选项
  Widget _buildSyncSettings(BuildContext context, SettingsController controller, SyncSettings syncSettings) {
    // 同步间隔选项
    final syncIntervals = [
      {'value': 5, 'name': '5分钟'},
      {'value': 15, 'name': '15分钟'},
      {'value': 30, 'name': '30分钟'},
      {'value': 60, 'name': '1小时'},
      {'value': 120, 'name': '2小时'},
    ];
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // 启用自动同步
            SwitchListTile(
              title: const Text('启用自动同步'),
              subtitle: const Text('定期自动同步本地数据和服务器数据'),
              value: syncSettings.enableAutoSync,
              onChanged: (value) {
                final newSettings = syncSettings.copyWith(enableAutoSync: value);
                controller.updateSyncSettings(newSettings);
              },
            ),
            
            if (syncSettings.enableAutoSync) ...[
              const SizedBox(height: 16),
              
              // 同步间隔
              DropdownButtonFormField<int>(
                value: syncSettings.syncInterval,
                decoration: const InputDecoration(
                  labelText: '同步间隔',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                ),
                items: syncIntervals
                    .map((interval) => DropdownMenuItem<int>(
                          value: interval['value'] as int,
                          child: Text(interval['name'] as String),
                        ))
                    .toList(),
                onChanged: (value) {
                  if (value != null) {
                    final newSettings = syncSettings.copyWith(syncInterval: value);
                    controller.updateSyncSettings(newSettings);
                  }
                },
              ),
              
              const SizedBox(height: 16),
              
              // 仅在WiFi下同步
              SwitchListTile(
                title: const Text('仅在WiFi下同步'),
                subtitle: const Text('避免在移动数据网络下消耗流量'),
                value: syncSettings.syncOnlyOnWifi,
                onChanged: (value) {
                  final newSettings = syncSettings.copyWith(syncOnlyOnWifi: value);
                  controller.updateSyncSettings(newSettings);
                },
              ),
              
              // 同步通知
              SwitchListTile(
                title: const Text('同步进行中通知'),
                subtitle: const Text('显示同步进度通知'),
                value: syncSettings.notifyOnSync,
                onChanged: (value) {
                  final newSettings = syncSettings.copyWith(notifyOnSync: value);
                  controller.updateSyncSettings(newSettings);
                },
              ),
            ],
          ],
        ),
      ),
    );
  }
  
  /// 构建备份设置选项
  Widget _buildBackupSettings(BuildContext context, SettingsController controller, BackupSettings backupSettings) {
    // 备份间隔选项
    final backupIntervals = [
      {'value': 1, 'name': '每天'},
      {'value': 3, 'name': '每3天'},
      {'value': 7, 'name': '每周'},
      {'value': 14, 'name': '每两周'},
      {'value': 30, 'name': '每月'},
    ];
    
    // 保留备份数选项
    final keepBackupCounts = [
      {'value': 3, 'name': '3个'},
      {'value': 5, 'name': '5个'},
      {'value': 10, 'name': '10个'},
      {'value': 20, 'name': '20个'},
    ];
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // 启用自动备份
            SwitchListTile(
              title: const Text('启用自动备份'),
              subtitle: const Text('定期自动备份数据库'),
              value: backupSettings.enableAutoBackup,
              onChanged: (value) {
                final newSettings = backupSettings.copyWith(enableAutoBackup: value);
                controller.updateBackupSettings(newSettings);
              },
            ),
            
            if (backupSettings.enableAutoBackup) ...[
              const SizedBox(height: 16),
              
              // 备份间隔
              DropdownButtonFormField<int>(
                value: backupSettings.backupInterval,
                decoration: const InputDecoration(
                  labelText: '备份间隔',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                ),
                items: backupIntervals
                    .map((interval) => DropdownMenuItem<int>(
                          value: interval['value'] as int,
                          child: Text(interval['name'] as String),
                        ))
                    .toList(),
                onChanged: (value) {
                  if (value != null) {
                    final newSettings = backupSettings.copyWith(backupInterval: value);
                    controller.updateBackupSettings(newSettings);
                  }
                },
              ),
              
              const SizedBox(height: 16),
              
              // 保留备份数量
              DropdownButtonFormField<int>(
                value: backupSettings.keepBackupCount,
                decoration: const InputDecoration(
                  labelText: '保留备份数量',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                ),
                items: keepBackupCounts
                    .map((count) => DropdownMenuItem<int>(
                          value: count['value'] as int,
                          child: Text(count['name'] as String),
                        ))
                    .toList(),
                onChanged: (value) {
                  if (value != null) {
                    final newSettings = backupSettings.copyWith(keepBackupCount: value);
                    controller.updateBackupSettings(newSettings);
                  }
                },
              ),
              
              const SizedBox(height: 16),
              
              // 包含图片
              SwitchListTile(
                title: const Text('备份包含图片'),
                subtitle: const Text('备份时包含图片文件(备份文件会较大)'),
                value: backupSettings.includeImages,
                onChanged: (value) {
                  final newSettings = backupSettings.copyWith(includeImages: value);
                  controller.updateBackupSettings(newSettings);
                },
              ),
              
              const SizedBox(height: 16),
              
              // 备份路径
              TextFormField(
                initialValue: backupSettings.backupPath,
                decoration: const InputDecoration(
                  labelText: '备份存储路径',
                  hintText: '留空使用默认路径',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  suffixIcon: Icon(Icons.folder_open),
                ),
                onChanged: (value) {
                  final newSettings = backupSettings.copyWith(backupPath: value);
                  controller.updateBackupSettings(newSettings);
                },
              ),
            ],
          ],
        ),
      ),
    );
  }
  
  /// 处理保存设置
  Future<void> _onSaveSettings(SettingsController controller) async {
    final success = await controller.saveSettings();
    
    if (success) {
      Get.snackbar(
        '保存成功',
        '系统设置已成功保存',
        snackPosition: SnackPosition.BOTTOM,
      );
    } else {
      Get.snackbar(
        '保存失败',
        '系统设置保存失败，请稍后重试',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }
  
  /// 处理重置设置
  Future<void> _onResetSettings(BuildContext context, SettingsController controller) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('重置设置'),
        content: const Text('确定要将所有设置重置为默认值吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('确定重置'),
          ),
        ],
      ),
    );
    
    if (confirmed == true) {
      await controller.resetSettings();
    }
  }
} 