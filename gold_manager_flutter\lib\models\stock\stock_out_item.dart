import '../common/date_utils.dart';
import '../jewelry/jewelry.dart';

/// 出库单明细模型
class StockOutItem {
  final int id;
  final int stockOutId;
  final int jewelryId;
  final String? barcode;
  final double amount;
  final DateTime? createTime;

  // 出库时的价格信息（实际售价）
  final double goldPrice;
  final double silverPrice;
  final double workPrice;
  final double pieceWorkPrice;

  // 重量信息
  final double goldWeight;
  final double silverWeight;
  final double totalWeight;

  // 关联对象
  final Jewelry? jewelry;

  const StockOutItem({
    required this.id,
    required this.stockOutId,
    required this.jewelryId,
    this.barcode,
    required this.amount,
    this.createTime,
    this.goldPrice = 0.0,
    this.silverPrice = 0.0,
    this.workPrice = 0.0,
    this.pieceWorkPrice = 0.0,
    this.goldWeight = 0.0,
    this.silverWeight = 0.0,
    this.totalWeight = 0.0,
    this.jewelry,
  });
  
  /// 从JSON构造
  factory StockOutItem.fromJson(Map<String, dynamic> json) {
    return StockOutItem(
      id: json['id'],
      stockOutId: json['stock_out_id'],
      jewelryId: json['jewelry_id'],
      barcode: json['barcode'],
      amount: _parseDouble(json['total_amount'] ?? json['amount']),
      createTime: DateUtil.fromUnixTimestamp(json['createtime']),
      goldPrice: _parseDouble(json['gold_price']),
      silverPrice: _parseDouble(json['silver_price']),
      workPrice: _parseDouble(json['work_price']),
      pieceWorkPrice: _parseDouble(json['piece_work_price']),
      goldWeight: _parseDouble(json['gold_weight']),
      silverWeight: _parseDouble(json['silver_weight']),
      totalWeight: _parseDouble(json['total_weight']),
      jewelry: json['jewelry'] != null ? Jewelry.fromJson(json['jewelry']) : null,
    );
  }

  /// 安全解析双精度浮点数
  static double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value) ?? 0.0;
    }
    return 0.0;
  }
  
  /// 转为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'stock_out_id': stockOutId,
      'jewelry_id': jewelryId,
      'barcode': barcode,
      'amount': amount,
      'createtime': DateUtil.toUnixTimestamp(createTime),
      'gold_price': goldPrice,
      'silver_price': silverPrice,
      'work_price': workPrice,
      'piece_work_price': pieceWorkPrice,
      'gold_weight': goldWeight,
      'silver_weight': silverWeight,
      'total_weight': totalWeight,
    };
  }
  
  /// 创建一个新实例，但使用部分属性
  StockOutItem copyWith({
    int? id,
    int? stockOutId,
    int? jewelryId,
    String? barcode,
    double? amount,
    DateTime? createTime,
    double? goldPrice,
    double? silverPrice,
    double? workPrice,
    double? pieceWorkPrice,
    double? goldWeight,
    double? silverWeight,
    double? totalWeight,
    Jewelry? jewelry,
  }) {
    return StockOutItem(
      id: id ?? this.id,
      stockOutId: stockOutId ?? this.stockOutId,
      jewelryId: jewelryId ?? this.jewelryId,
      barcode: barcode ?? this.barcode,
      amount: amount ?? this.amount,
      createTime: createTime ?? this.createTime,
      goldPrice: goldPrice ?? this.goldPrice,
      silverPrice: silverPrice ?? this.silverPrice,
      workPrice: workPrice ?? this.workPrice,
      pieceWorkPrice: pieceWorkPrice ?? this.pieceWorkPrice,
      goldWeight: goldWeight ?? this.goldWeight,
      silverWeight: silverWeight ?? this.silverWeight,
      totalWeight: totalWeight ?? this.totalWeight,
      jewelry: jewelry ?? this.jewelry,
    );
  }
} 