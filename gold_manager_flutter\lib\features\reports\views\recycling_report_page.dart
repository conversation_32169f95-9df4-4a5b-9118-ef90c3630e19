import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../core/theme/app_colors.dart';
import '../../../widgets/empty_state_widget.dart';
import '../controllers/reports_controller.dart';
import '../models/report_model.dart';

/// 回收报表页面
class RecyclingReportPage extends StatelessWidget {
  const RecyclingReportPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<ReportsController>();
    
    return Obx(() {
      final report = controller.recyclingReport.value;
      
      // 检查是否有数据
      if (report.items.isEmpty && report.chartData.isEmpty) {
        return const EmptyStateWidget(
          icon: Icons.recycling,
          title: '暂无回收数据',
          message: '所选时间范围内没有回收记录',
        );
      }
      
      return SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSummaryCards(report),
            const SizedBox(height: 24),
            _buildTrendSection(report.chartData),
            const SizedBox(height: 24),
            _buildProcessTypeSection(report.processTypeData),
            const SizedBox(height: 24),
            _buildRecyclingTable(report.items),
          ],
        ),
      );
    });
  }
  
  /// 构建概览卡片
  Widget _buildSummaryCards(RecyclingReportData report) {
    return Wrap(
      spacing: 16,
      runSpacing: 16,
      children: [
        _buildSummaryCard(
          title: '回收总笔数',
          value: '${NumberFormat('#,##0').format(report.totalCount)}笔',
          icon: Icons.receipt_long,
          color: AppColors.primary,
        ),
        _buildSummaryCard(
          title: '回收总金额',
          value: '¥${NumberFormat('#,##0.00').format(report.totalAmount)}',
          icon: Icons.attach_money,
          color: Colors.green,
        ),
        _buildSummaryCard(
          title: '回收总重量',
          value: '${report.totalWeight.toStringAsFixed(2)}g',
          icon: Icons.scale,
          color: Colors.orange,
        ),
        _buildSummaryCard(
          title: '回收金重',
          value: '${report.totalGoldWeight.toStringAsFixed(2)}g',
          icon: Icons.monetization_on,
          color: Colors.amber,
        ),
        _buildSummaryCard(
          title: '回收银重',
          value: '${report.totalSilverWeight.toStringAsFixed(2)}g',
          icon: Icons.monetization_on,
          color: Colors.blueGrey,
        ),
      ],
    );
  }
  
  /// 构建单个概览卡片
  Widget _buildSummaryCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return SizedBox(
      width: 250,
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(icon, color: color, size: 24),
                  const SizedBox(width: 8),
                  Text(title, style: const TextStyle(
                    fontSize: 16, 
                    fontWeight: FontWeight.w500,
                  )),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                value,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  /// 构建趋势数据部分
  Widget _buildTrendSection(List<ChartData> chartData) {
    if (chartData.isEmpty) {
      return const SizedBox.shrink();
    }
    
    // 准备趋势数据
    final Map<String, dynamic> trendData = {};
    
    // 获取最近几天的数据趋势
    for (var data in chartData) {
      final dateStr = DateFormat('MM-dd').format(data.date);
      trendData[dateStr] = data.value;
    }
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '回收趋势',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 24),
            SizedBox(
              height: 200,
              child: ListView(
                scrollDirection: Axis.horizontal,
                children: trendData.entries.map((entry) {
                  final dateStr = entry.key;
                  final value = entry.value as double;
                  
                  // 计算条形高度
                  final maxValue = trendData.values
                      .map((v) => v as double)
                      .reduce((a, b) => a > b ? a : b);
                  final height = value / maxValue * 150;
                  
                  return Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Text(
                          '¥${NumberFormat('#,##0').format(value)}',
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          width: 40,
                          height: height,
                          decoration: BoxDecoration(
                            color: AppColors.primary,
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          dateStr,
                          style: const TextStyle(fontSize: 12),
                        ),
                      ],
                    ),
                  );
                }).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  /// 构建处理方式分布
  Widget _buildProcessTypeSection(List<ProcessTypeData> processTypeData) {
    if (processTypeData.isEmpty) {
      return const SizedBox.shrink();
    }
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '处理方式分布',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 24),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: processTypeData.length,
              separatorBuilder: (context, index) => const Divider(),
              itemBuilder: (context, index) {
                final process = processTypeData[index];
                final color = _getProcessTypeColor(process.type);
                
                return ListTile(
                  leading: Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: color,
                      shape: BoxShape.circle,
                    ),
                  ),
                  title: Text(_getProcessTypeName(process.type)),
                  subtitle: LinearProgressIndicator(
                    value: process.percentage / 100,
                    backgroundColor: Colors.grey.withOpacity(0.2),
                    valueColor: AlwaysStoppedAnimation<Color>(color),
                  ),
                  trailing: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        '${process.count}笔',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '${process.percentage.toStringAsFixed(1)}%',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
  
  /// 获取处理类型名称
  String _getProcessTypeName(String processType) {
    switch (processType) {
      case 'pending':
        return '待处理';
      case 'sell':
        return '直接卖出';
      case 'separate':
        return '金银分离';
      case 'repair':
        return '维修再销售';
      case 'resell':
        return '直接二次销售';
      default:
        return '未知处理方式';
    }
  }
  
  /// 获取处理类型颜色
  Color _getProcessTypeColor(String processType) {
    switch (processType) {
      case 'pending':
        return Colors.grey;
      case 'sell':
        return Colors.blue;
      case 'separate':
        return Colors.amber;
      case 'repair':
        return Colors.green;
      case 'resell':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }
  
  /// 构建回收明细表格
  Widget _buildRecyclingTable(List<RecyclingReportItem> items) {
    if (items.isEmpty) {
      return const SizedBox.shrink();
    }
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  '回收明细',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                _buildStatusLegend(),
              ],
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                headingTextStyle: const TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
                dataTextStyle: const TextStyle(
                  color: Colors.black87,
                ),
                columnSpacing: 24,
                columns: const [
                  DataColumn(label: Text('回收单号')),
                  DataColumn(label: Text('日期')),
                  DataColumn(label: Text('客户姓名')),
                  DataColumn(label: Text('物品名称')),
                  DataColumn(label: Text('分类')),
                  DataColumn(label: Text('重量(g)')),
                  DataColumn(label: Text('单价(元/g)')),
                  DataColumn(label: Text('金额(元)')),
                  DataColumn(label: Text('处理方式')),
                  DataColumn(label: Text('处理状态')),
                  DataColumn(label: Text('处理收益(元)')),
                ],
                rows: items.map((item) {
                  return DataRow(
                    cells: [
                      DataCell(Text(item.orderNo)),
                      DataCell(Text(DateFormat('yyyy-MM-dd').format(item.date))),
                      DataCell(Text(item.customerName)),
                      DataCell(Text(item.itemName)),
                      DataCell(Text(item.categoryName)),
                      DataCell(Text(item.weight.toStringAsFixed(2))),
                      DataCell(Text('¥${item.price.toStringAsFixed(2)}')),
                      DataCell(Text('¥${item.amount.toStringAsFixed(2)}')),
                      DataCell(Text(_getProcessTypeName(item.processType))),
                      DataCell(_buildProcessStatusCell(item.processStatus)),
                      DataCell(Text('¥${item.processResult.toStringAsFixed(2)}')),
                    ],
                  );
                }).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  /// 构建处理状态单元格
  Widget _buildProcessStatusCell(int status) {
    Color color;
    String text;
    
    switch (status) {
      case 0:
        color = Colors.grey;
        text = '未处理';
        break;
      case 1:
        color = Colors.blue;
        text = '处理中';
        break;
      case 2:
        color = Colors.green;
        text = '已处理';
        break;
      default:
        color = Colors.grey;
        text = '未知状态';
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontWeight: FontWeight.bold,
          fontSize: 12,
        ),
      ),
    );
  }
  
  /// 构建状态图例
  Widget _buildStatusLegend() {
    return Row(
      children: [
        _buildLegendItem(Colors.grey, '未处理'),
        const SizedBox(width: 8),
        _buildLegendItem(Colors.blue, '处理中'),
        const SizedBox(width: 8),
        _buildLegendItem(Colors.green, '已处理'),
      ],
    );
  }
  
  /// 构建图例项
  Widget _buildLegendItem(Color color, String text) {
    return Row(
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color.withOpacity(0.2),
            border: Border.all(color: color),
            borderRadius: BorderRadius.circular(3),
          ),
        ),
        const SizedBox(width: 4),
        Text(
          text,
          style: TextStyle(
            color: color,
            fontSize: 12,
          ),
        ),
      ],
    );
  }
} 