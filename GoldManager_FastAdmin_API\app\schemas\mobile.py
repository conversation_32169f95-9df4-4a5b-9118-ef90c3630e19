"""
移动端适配优化 - 数据验证模型

老板，这个模块专门为移动端优化设计，提供精简的数据传输和响应格式。
主要功能：
1. 精简响应数据结构
2. 优化字段选择
3. 压缩数据传输
4. 移动端专用格式
"""

from typing import Optional, List, Dict, Any, Union
from pydantic import BaseModel, Field
from decimal import Decimal
from datetime import datetime

# ================================
# 移动端基础响应模型
# ================================

class MobileBaseResponse(BaseModel):
    """移动端基础响应模型 - 精简版"""
    success: bool = Field(True, description="请求是否成功")
    message: str = Field("", description="响应消息")
    timestamp: int = Field(description="响应时间戳")
    
    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "message": "操作成功",
                "timestamp": 1640995200
            }
        }

class MobilePaginationInfo(BaseModel):
    """移动端分页信息 - 精简版"""
    page: int = Field(description="当前页码")
    size: int = Field(description="每页数量")
    total: int = Field(description="总记录数")
    has_more: bool = Field(description="是否有更多数据")
    
    class Config:
        json_schema_extra = {
            "example": {
                "page": 1,
                "size": 20,
                "total": 100,
                "has_more": True
            }
        }

class MobileListResponse(MobileBaseResponse):
    """移动端列表响应模型"""
    data: List[Dict[str, Any]] = Field(default_factory=list, description="数据列表")
    pagination: Optional[MobilePaginationInfo] = Field(None, description="分页信息")

class MobileDetailResponse(MobileBaseResponse):
    """移动端详情响应模型"""
    data: Optional[Dict[str, Any]] = Field(None, description="详情数据")

# ================================
# 移动端商品相关模型
# ================================

class MobileJewelrySimple(BaseModel):
    """移动端商品简化模型"""
    id: int = Field(description="商品ID")
    barcode: str = Field(description="条码")
    name: str = Field(description="商品名称")
    category_name: str = Field(description="分类名称")
    ring_size: Optional[str] = Field(None, description="圈口号")
    total_weight: Decimal = Field(Decimal('0.00'), description="总重(克)")
    total_cost: Decimal = Field(Decimal('0.00'), description="总成本")
    store_name: str = Field(description="门店名称")
    status: int = Field(description="状态")
    status_text: str = Field(description="状态文本")
    
    class Config:
        json_schema_extra = {
            "example": {
                "id": 1,
                "barcode": "JW001",
                "name": "黄金戒指",
                "category_name": "戒指",
                "ring_size": "15",
                "total_weight": "5.20",
                "total_cost": "2600.00",
                "store_name": "旗舰店",
                "status": 1,
                "status_text": "上架"
            }
        }

class MobileJewelryDetail(BaseModel):
    """移动端商品详情模型"""
    id: int = Field(description="商品ID")
    barcode: str = Field(description="条码")
    name: str = Field(description="商品名称")
    category_id: int = Field(description="分类ID")
    category_name: str = Field(description="分类名称")
    ring_size: Optional[str] = Field(None, description="圈口号")
    
    # 重量信息
    gold_weight: Decimal = Field(Decimal('0.00'), description="金重(克)")
    silver_weight: Decimal = Field(Decimal('0.00'), description="银重(克)")
    total_weight: Decimal = Field(Decimal('0.00'), description="总重(克)")
    
    # 价格信息
    gold_price: Decimal = Field(Decimal('0.00'), description="金价(克价)")
    silver_price: Decimal = Field(Decimal('0.00'), description="银价(克价)")
    total_cost: Decimal = Field(Decimal('0.00'), description="总成本")
    
    # 工费信息
    wholesale_work_price: Decimal = Field(Decimal('0.00'), description="批发工费")
    retail_work_price: Decimal = Field(Decimal('0.00'), description="零售工费")
    piece_work_price: Decimal = Field(Decimal('0.00'), description="件工费")
    
    # 门店信息
    store_id: int = Field(description="门店ID")
    store_name: str = Field(description="门店名称")
    
    # 状态信息
    status: int = Field(description="状态")
    status_text: str = Field(description="状态文本")
    
    # 时间信息
    createtime: Optional[int] = Field(None, description="创建时间")
    updatetime: Optional[int] = Field(None, description="更新时间")
    
    class Config:
        json_schema_extra = {
            "example": {
                "id": 1,
                "barcode": "JW001",
                "name": "黄金戒指",
                "category_id": 1,
                "category_name": "戒指",
                "ring_size": "15",
                "gold_weight": "5.20",
                "silver_weight": "0.00",
                "total_weight": "5.20",
                "gold_price": "500.00",
                "silver_price": "0.00",
                "total_cost": "2600.00",
                "wholesale_work_price": "50.00",
                "retail_work_price": "80.00",
                "piece_work_price": "100.00",
                "store_id": 1,
                "store_name": "旗舰店",
                "status": 1,
                "status_text": "上架",
                "createtime": 1640995200,
                "updatetime": 1640995200
            }
        }

# ================================
# 移动端门店相关模型
# ================================

class MobileStoreSimple(BaseModel):
    """移动端门店简化模型"""
    id: int = Field(description="门店ID")
    name: str = Field(description="门店名称")
    address: Optional[str] = Field(None, description="门店地址")
    phone: Optional[str] = Field(None, description="联系电话")
    status: int = Field(description="状态")
    status_text: str = Field(description="状态文本")
    jewelry_count: int = Field(0, description="商品数量")
    
    class Config:
        json_schema_extra = {
            "example": {
                "id": 1,
                "name": "旗舰店",
                "address": "北京市朝阳区xxx街道",
                "phone": "010-12345678",
                "status": 1,
                "status_text": "正常",
                "jewelry_count": 150
            }
        }

# ================================
# 移动端会员相关模型
# ================================

class MobileMemberSimple(BaseModel):
    """移动端会员简化模型"""
    id: int = Field(description="会员ID")
    card_no: str = Field(description="会员卡号")
    name: str = Field(description="会员姓名")
    phone: Optional[str] = Field(None, description="联系电话")
    points: int = Field(0, description="积分")
    level: int = Field(1, description="会员等级")
    level_text: str = Field(description="等级文本")
    status: int = Field(description="状态")
    status_text: str = Field(description="状态文本")
    
    class Config:
        json_schema_extra = {
            "example": {
                "id": 1,
                "card_no": "VIP001",
                "name": "张三",
                "phone": "13800138000",
                "points": 1500,
                "level": 3,
                "level_text": "金卡会员",
                "status": 1,
                "status_text": "正常"
            }
        }

# ================================
# 移动端库存相关模型
# ================================

class MobileStockSimple(BaseModel):
    """移动端库存简化模型"""
    id: int = Field(description="单据ID")
    order_no: str = Field(description="单据号")
    store_name: str = Field(description="门店名称")
    total_amount: Decimal = Field(Decimal('0.00'), description="总金额")
    status: int = Field(description="状态")
    status_text: str = Field(description="状态文本")
    operator_name: str = Field(description="操作员")
    createtime: int = Field(description="创建时间")
    
    class Config:
        json_schema_extra = {
            "example": {
                "id": 1,
                "order_no": "IN202412200001",
                "store_name": "旗舰店",
                "total_amount": "15000.00",
                "status": 1,
                "status_text": "已入库",
                "operator_name": "张管理员",
                "createtime": 1640995200
            }
        }

# ================================
# 移动端统计相关模型
# ================================

class MobileDashboardSummary(BaseModel):
    """移动端仪表板摘要模型"""
    jewelry_count: int = Field(0, description="商品总数")
    store_count: int = Field(0, description="门店总数")
    member_count: int = Field(0, description="会员总数")
    today_sales: Decimal = Field(Decimal('0.00'), description="今日销售")
    month_sales: Decimal = Field(Decimal('0.00'), description="本月销售")
    low_stock_count: int = Field(0, description="低库存商品数")
    
    class Config:
        json_schema_extra = {
            "example": {
                "jewelry_count": 1500,
                "store_count": 5,
                "member_count": 800,
                "today_sales": "25000.00",
                "month_sales": "680000.00",
                "low_stock_count": 12
            }
        }

# ================================
# 移动端搜索相关模型
# ================================

class MobileSearchRequest(BaseModel):
    """移动端搜索请求模型"""
    keyword: str = Field(description="搜索关键词")
    type: str = Field("jewelry", description="搜索类型: jewelry/member/store")
    page: int = Field(1, description="页码")
    size: int = Field(20, description="每页数量")
    
    class Config:
        json_schema_extra = {
            "example": {
                "keyword": "戒指",
                "type": "jewelry",
                "page": 1,
                "size": 20
            }
        }

class MobileSearchResult(BaseModel):
    """移动端搜索结果模型"""
    type: str = Field(description="结果类型")
    title: str = Field(description="标题")
    subtitle: str = Field(description="副标题")
    data: Dict[str, Any] = Field(description="详细数据")
    
    class Config:
        json_schema_extra = {
            "example": {
                "type": "jewelry",
                "title": "黄金戒指",
                "subtitle": "条码: JW001 | 门店: 旗舰店",
                "data": {
                    "id": 1,
                    "barcode": "JW001",
                    "store_name": "旗舰店"
                }
            }
        }

# ================================
# 移动端同步相关模型
# ================================

class MobileSyncRequest(BaseModel):
    """移动端同步请求模型"""
    last_sync_time: int = Field(0, description="上次同步时间戳")
    sync_types: List[str] = Field(default_factory=list, description="同步类型列表")
    
    class Config:
        json_schema_extra = {
            "example": {
                "last_sync_time": 1640995200,
                "sync_types": ["jewelry", "member", "store"]
            }
        }

class MobileSyncResponse(MobileBaseResponse):
    """移动端同步响应模型"""
    sync_time: int = Field(description="同步时间戳")
    changes: Dict[str, List[Dict[str, Any]]] = Field(default_factory=dict, description="变更数据")
    has_more: bool = Field(False, description="是否有更多数据")
    
    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "message": "同步成功",
                "timestamp": 1640995200,
                "sync_time": 1640995200,
                "changes": {
                    "jewelry": [{"id": 1, "action": "update"}],
                    "member": [{"id": 1, "action": "create"}]
                },
                "has_more": False
            }
        }
