import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/constants/border_styles.dart';
import '../../../models/print/print_template_config.dart';
import '../controllers/print_template_management_controller.dart';

/// 打印模板管理页面
/// 
/// 提供模板的列表显示、新建、编辑、删除、复制等功能
class PrintTemplateManagementView extends StatelessWidget {
  const PrintTemplateManagementView({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(PrintTemplateManagementController());

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Row(
          children: [
            Icon(Icons.print, size: 24),
            SizedBox(width: 8),
            Text('打印模板管理'),
          ],
        ),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        elevation: 1,
        actions: [
          // 新建模板按钮
          Padding(
            padding: const EdgeInsets.only(right: 16),
            child: ElevatedButton.icon(
              onPressed: () => controller.createNewTemplate(),
              icon: const Icon(Icons.add, size: 18),
              label: const Text('新建模板'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue[600],
                foregroundColor: Colors.white,
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 页面标题和描述
            _buildPageHeader(),
            const SizedBox(height: 24),
            
            // 模板列表
            Expanded(
              child: Obx(() => _buildTemplateList(controller)),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建页面头部
  Widget _buildPageHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppBorderStyles.borderColor),
      ),
      child: Row(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: Colors.blue[100],
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              Icons.description,
              color: Colors.blue[600],
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          const Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '打印模板管理',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  '管理收款凭证的打印模板，支持自定义字段显示、格式设置和布局配置',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.black54,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建模板列表
  Widget _buildTemplateList(PrintTemplateManagementController controller) {
    if (controller.isLoading.value) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (controller.templates.isEmpty) {
      return _buildEmptyState(controller);
    }

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppBorderStyles.borderColor),
      ),
      child: Column(
        children: [
          // 列表头部
          _buildListHeader(controller),
          const Divider(height: 1),
          
          // 模板列表
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.zero,
              itemCount: controller.templates.length,
              itemBuilder: (context, index) {
                final template = controller.templates[index];
                return _buildTemplateItem(template, controller, index);
              },
            ),
          ),
        ],
      ),
    );
  }

  /// 构建列表头部
  Widget _buildListHeader(PrintTemplateManagementController controller) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      child: Row(
        children: [
          Text(
            '共 ${controller.templates.length} 个模板',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const Spacer(),
          // 刷新按钮
          IconButton(
            onPressed: () => controller.refreshTemplates(),
            icon: const Icon(Icons.refresh),
            tooltip: '刷新列表',
          ),
        ],
      ),
    );
  }

  /// 构建单个模板项
  Widget _buildTemplateItem(
    PrintTemplateConfig template,
    PrintTemplateManagementController controller,
    int index,
  ) {
    final isDefault = controller.defaultTemplate.value?.id == template.id;
    
    return Container(
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: index == controller.templates.length - 1
                ? Colors.transparent
                : AppBorderStyles.borderColor,
          ),
        ),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
        leading: Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: isDefault ? Colors.orange[100] : Colors.blue[100],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            isDefault ? Icons.star : Icons.description,
            color: isDefault ? Colors.orange[600] : Colors.blue[600],
            size: 24,
          ),
        ),
        title: Row(
          children: [
            Expanded(
              child: Text(
                template.name,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ),
            ),
            if (isDefault)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.orange[100],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '默认',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.orange[700],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (template.description?.isNotEmpty == true) ...[
              const SizedBox(height: 4),
              Text(
                template.description!,
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.black54,
                ),
              ),
            ],
            const SizedBox(height: 8),
            Row(
              children: [
                _buildInfoChip('创建时间', _formatDateTime(template.createTime)),
                const SizedBox(width: 12),
                _buildInfoChip('更新时间', _formatDateTime(template.updateTime)),
              ],
            ),
          ],
        ),
        trailing: _buildActionButtons(template, controller, isDefault),
        onTap: () => controller.editTemplate(template),
      ),
    );
  }

  /// 构建信息标签
  Widget _buildInfoChip(String label, String value) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(6),
      ),
      child: Text(
        '$label: $value',
        style: const TextStyle(
          fontSize: 12,
          color: Colors.black54,
        ),
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons(
    PrintTemplateConfig template,
    PrintTemplateManagementController controller,
    bool isDefault,
  ) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 预览按钮
        IconButton(
          onPressed: () => controller.previewTemplate(template),
          icon: const Icon(Icons.preview),
          tooltip: '预览模板',
          iconSize: 20,
        ),
        // 更多操作按钮
        PopupMenuButton<String>(
          onSelected: (value) => _handleMenuAction(value, template, controller, isDefault),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit, size: 16),
                  SizedBox(width: 8),
                  Text('编辑'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'copy',
              child: Row(
                children: [
                  Icon(Icons.copy, size: 16),
                  SizedBox(width: 8),
                  Text('复制'),
                ],
              ),
            ),
            if (!isDefault)
              const PopupMenuItem(
                value: 'setDefault',
                child: Row(
                  children: [
                    Icon(Icons.star, size: 16),
                    SizedBox(width: 8),
                    Text('设为默认'),
                  ],
                ),
              ),
            if (!isDefault)
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, size: 16, color: Colors.red),
                    SizedBox(width: 8),
                    Text('删除', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
          ],
          icon: const Icon(Icons.more_vert, size: 20),
        ),
      ],
    );
  }

  /// 处理菜单操作
  void _handleMenuAction(
    String action,
    PrintTemplateConfig template,
    PrintTemplateManagementController controller,
    bool isDefault,
  ) {
    switch (action) {
      case 'edit':
        controller.editTemplate(template);
        break;
      case 'copy':
        controller.copyTemplate(template);
        break;
      case 'setDefault':
        controller.setDefaultTemplate(template);
        break;
      case 'delete':
        controller.deleteTemplate(template);
        break;
    }
  }

  /// 构建空状态
  Widget _buildEmptyState(PrintTemplateManagementController controller) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppBorderStyles.borderColor),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.description_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              '暂无打印模板',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '点击上方"新建模板"按钮创建您的第一个打印模板',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () => controller.createNewTemplate(),
              icon: const Icon(Icons.add),
              label: const Text('新建模板'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue[600],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 格式化日期时间
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
