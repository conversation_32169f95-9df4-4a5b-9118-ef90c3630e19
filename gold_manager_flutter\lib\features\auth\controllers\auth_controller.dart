import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:window_manager/window_manager.dart';

import '../../../core/routes/app_pages.dart';
import '../../../services/auth_service.dart';
import '../../../services/window_service.dart';
import '../../../core/utils/logger.dart';

/// 认证控制器
/// 处理登录、登出等认证相关操作
class AuthController extends GetxController {
  AuthService? _authService;

  final GlobalKey<FormState> loginFormKey = GlobalKey<FormState>();

  // 状态变量
  final RxBool isLoading = false.obs;
  final RxBool isPasswordVisible = false.obs;
  final RxString errorMessage = ''.obs;

  // 自动登录标志
  final RxBool rememberMe = false.obs;

  /// 获取AuthService实例
  AuthService get authService {
    _authService ??= Get.find<AuthService>();
    return _authService!;
  }

  @override
  void onInit() {
    super.onInit();
    LoggerService.d('AuthController 初始化');
  }

  @override
  void onClose() {
    LoggerService.d('AuthController 销毁');
    super.onClose();
  }

  /// 切换密码可见性
  void togglePasswordVisibility() {
    isPasswordVisible.value = !isPasswordVisible.value;
  }

  /// 切换记住我选项
  void toggleRememberMe() {
    rememberMe.value = !rememberMe.value;
  }

  /// 登录方法
  Future<void> login(String username, String password, {Function? onClearForm}) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      final result = await authService.login(
        username.trim(),
        password,
        rememberMe: rememberMe.value,
      );

      if (result) {
        // 登录成功，重置表单
        if (!rememberMe.value && onClearForm != null) {
          onClearForm();
        }

        // 登录成功后窗口管理（重新启用，添加调试信息）
        LoggerService.i('🔧 登录成功，开始窗口最大化流程...');

        // 检查登录前的窗口状态
        try {
          final beforeLoginSize = await windowManager.getSize();
          final beforeLoginMaximized = await windowManager.isMaximized();
          LoggerService.i('📊 登录前窗口状态: ${beforeLoginSize.width}x${beforeLoginSize.height}, 最大化: $beforeLoginMaximized');
        } catch (e) {
          LoggerService.w('获取登录前窗口状态失败', e);
        }

        try {
          final windowService = Get.find<WindowService>();
          await windowService.maximizeWindow();
          LoggerService.i('✅ 登录成功，WindowService窗口最大化完成');

          // 检查最大化后的窗口状态
          await Future.delayed(const Duration(milliseconds: 200));
          final afterMaxSize = await windowManager.getSize();
          final afterMaxMaximized = await windowManager.isMaximized();
          LoggerService.i('📊 最大化后窗口状态: ${afterMaxSize.width}x${afterMaxSize.height}, 最大化: $afterMaxMaximized');

        } catch (e) {
          LoggerService.w('WindowService最大化失败', e);
          // 暂时不使用原生服务，避免冲突
          // try {
          //   final nativeWindowService = Get.find<NativeWindowService>();
          //   await nativeWindowService.maximizeWindow();
          //   LoggerService.i('登录成功，原生窗口服务最大化完成');
          // } catch (e2) {
          //   LoggerService.w('所有窗口最大化方案都失败，但不影响登录流程', e2);
          // }
        }

        // 导航到主页
        Get.offAllNamed(Routes.DASHBOARD);
      } else {
        // 登录失败
        errorMessage.value = 'login_fail'.tr;
      }
    } catch (e) {
      LoggerService.e('登录失败', e);
      errorMessage.value = e.toString();
    } finally {
      isLoading.value = false;
    }
  }

  /// 登出方法
  Future<void> logout() async {
    try {
      isLoading.value = true;

      await authService.logout();

      // 登出时窗口管理（暂时禁用，使用main.dart中的窗口管理）
      LoggerService.i('登出成功，窗口管理由main.dart处理');
      // TODO: 窗口问题解决后可以重新启用
      // try {
      //   final windowService = Get.find<WindowService>();
      //   await windowService.setLoginWindowSize();
      //   LoggerService.i('登出成功，窗口已恢复登录尺寸');
      // } catch (e) {
      //   LoggerService.w('窗口尺寸恢复失败，但不影响登出流程', e);
      // }

      // 导航到登录页
      Get.offAllNamed(Routes.LOGIN);
    } catch (e) {
      LoggerService.e('登出失败', e);
      Get.snackbar(
        'error'.tr,
        e.toString(),
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// 检查认证状态
  bool isAuthenticated() {
    return authService.checkLoggedIn();
  }

  /// 检查用户是否有特定权限
  bool hasPermission(String permission) {
    return authService.hasPermission(permission);
  }

  /// 用户名验证
  String? validateUsername(String? value) {
    if (value == null || value.isEmpty) {
      return 'enter_username'.tr;
    }
    return null;
  }

  /// 密码验证
  String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'enter_password'.tr;
    }
    return null;
  }
}