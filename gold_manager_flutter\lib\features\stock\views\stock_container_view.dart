import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/widgets/tab_manager.dart';
import '../controllers/stock_tab_controller.dart';

/// 库存管理容器页面
/// 负责管理库存管理的各个子页面切换，支持多标签页功能
class StockContainerView extends StatefulWidget {
  const StockContainerView({super.key});

  @override
  State<StockContainerView> createState() => _StockContainerViewState();
}

class _StockContainerViewState extends State<StockContainerView> {
  late final StockTabController stockTabController;

  @override
  void initState() {
    super.initState();
    // 尝试获取已注册的StockTabController，如果不存在则创建
    try {
      stockTabController = Get.find<StockTabController>();
    } catch (e) {
      // 如果没有找到，则创建一个新的（备用方案）
      stockTabController = Get.put(StockTabController());
    }
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: AppTheme.backgroundColor,
      child: Column(
        children: [
          // 标签页管理器
          Expanded(
            child: TabManager(
              controller: stockTabController.tabManager,
              backgroundColor: AppTheme.backgroundColor,
              activeTabColor: Colors.white,
              inactiveTabColor: Colors.grey[100],
            ),
          ),
        ],
      ),
    );
  }
}
