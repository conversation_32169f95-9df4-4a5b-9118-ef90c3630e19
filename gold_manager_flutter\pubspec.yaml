name: gold_manager_flutter
description: "金包银首饰管理系统"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.8.0 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # 状态管理
  get: ^4.6.6
  provider: ^6.0.5

  # 网络请求
  dio: ^5.4.0
  http: ^1.1.0
  # connectivity_plus: ^5.0.2  # Windows桌面不支持，使用网络状态检测替代方案

  # 本地存储
  shared_preferences: ^2.2.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  sqflite: ^2.3.0
  path_provider: ^2.1.2

  # UI组件
  cupertino_icons: ^1.0.6
  flutter_screenutil: ^5.9.0
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.1
  shimmer: ^3.0.0
  flutter_spinkit: ^5.2.0
  animations: ^2.0.7
  flutter_easyloading: ^3.0.5
  flutter_colorpicker: ^1.0.3

  # 工具
  intl: ^0.20.2
  logger: ^1.4.0
  url_launcher: ^6.2.4
  package_info_plus: ^4.2.0
  device_info_plus: ^9.0.3
  uuid: ^3.0.7
  equatable: ^2.0.5

  # 音频播放
  audioplayers: ^5.1.0  # 使用更稳定的版本

  # 文件处理
  file_picker: ^6.1.1
  excel: ^2.1.0
  csv: ^5.0.2

  # 打印功能 - 暂时注释掉以解决Windows编译问题
  # printing: ^5.12.0
  # pdf: ^3.10.7
  # pdf_widget_wrapper: ^1.0.4

  # 图片处理
  image_picker: ^1.0.7
  # image_cropper: ^5.0.0  # 暂时移除，有Web兼容性问题
  photo_view: ^0.14.0

  # 窗口管理 (桌面平台)
  window_manager: ^0.3.9  # 使用更稳定的版本

dev_dependencies:
  flutter_test:
    sdk: flutter
  hive_generator: ^2.0.0
  build_runner: ^2.4.6
  flutter_lints: ^3.0.1
  mockito: ^5.4.2

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/icons/
    - assets/data/
    - assets/sounds/

  # 生成本地化代码
  generate: true