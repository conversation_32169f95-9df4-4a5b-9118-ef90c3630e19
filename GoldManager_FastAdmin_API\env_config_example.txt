# 环境配置文件示例
# 请将此文件复制为 .env 并修改相应配置

# 项目基本配置
PROJECT_NAME=黄金珠宝管理系统 API
PROJECT_VERSION=1.0.0
DEBUG=true

# 服务器配置
HOST=0.0.0.0
PORT=8000

# 数据库配置 (请修改为您的FastAdmin数据库配置)
DATABASE_HOST=localhost
DATABASE_PORT=3306
DATABASE_USER=root
DATABASE_PASSWORD=your_password_here
DATABASE_NAME=shgsgueobavnspo
DATABASE_CHARSET=utf8mb4

# JWT配置 (生产环境请修改SECRET_KEY)
SECRET_KEY=your-very-secret-key-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=10080

# Redis配置 (可选)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# 文件上传配置
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760

# 跨域配置 (生产环境请设置具体域名)
ALLOWED_HOSTS=["*"] 