"""
回收处理相关数据模型
包含处理工单、处理结果、转库存记录等
"""

from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Text, DECIMAL, Boolean
from sqlalchemy.orm import relationship
from ..core.database import Base


class RecyclingProcess(Base):
    """回收处理工单表"""
    __tablename__ = "fa_recycling_process"
    
    id = Column(Integer, primary_key=True, index=True)
    process_no = Column(String(50), unique=True, nullable=False, comment="处理单号")
    store_id = Column(Integer, ForeignKey("fa_store.id"), nullable=False, comment="处理门店ID")
    source_store_id = Column(Integer, ForeignKey("fa_store.id"), nullable=False, comment="来源门店ID")
    recycling_id = Column(Integer, ForeignKey("fa_recycling.id"), nullable=False, comment="关联回收单ID")
    recycling_item_ids = Column(Text, comment="关联回收明细IDs（JSON格式）")
    process_type = Column(Integer, default=1, comment="处理类型：1=金银分离，2=翻新加工，3=直接熔炼")
    total_weight = Column(DECIMAL(10, 2), default=0.00, comment="总重量（克）")
    estimated_gold_weight = Column(DECIMAL(10, 2), default=0.00, comment="预估金重（克）")
    estimated_silver_weight = Column(DECIMAL(10, 2), default=0.00, comment="预估银重（克）")
    status = Column(Integer, default=1, comment="状态：0=已取消，1=待处理，2=处理中，3=已完成")
    start_time = Column(Integer, comment="开始处理时间")
    end_time = Column(Integer, comment="完成时间")
    operator_id = Column(Integer, ForeignKey("fa_admin.id"), nullable=False, comment="操作员ID")
    processor_id = Column(Integer, ForeignKey("fa_admin.id"), comment="处理人员ID")
    remark = Column(String(500), comment="备注")
    createtime = Column(Integer, nullable=False, comment="创建时间")
    updatetime = Column(Integer, nullable=False, comment="更新时间")
    
    # 关联关系
    store = relationship("Store", foreign_keys=[store_id], backref="processing_processes")
    source_store = relationship("Store", foreign_keys=[source_store_id], backref="source_processes")
    recycling = relationship("Recycling", backref="processes")
    operator = relationship("Admin", foreign_keys=[operator_id], backref="created_processes")
    processor = relationship("Admin", foreign_keys=[processor_id], backref="processed_processes")
    results = relationship("RecyclingProcessResult", back_populates="process", cascade="all, delete-orphan")


class RecyclingProcessResult(Base):
    """处理结果表"""
    __tablename__ = "fa_recycling_process_result"
    
    id = Column(Integer, primary_key=True, index=True)
    process_id = Column(Integer, ForeignKey("fa_recycling_process.id"), nullable=False, comment="处理工单ID")
    result_type = Column(Integer, nullable=False, comment="结果类型：1=纯金，2=纯银，3=成品首饰，4=金银锭")
    name = Column(String(100), nullable=False, comment="产品名称")
    weight = Column(DECIMAL(10, 2), nullable=False, comment="重量（克）")
    purity = Column(DECIMAL(5, 2), comment="纯度（如99.9）")
    loss_weight = Column(DECIMAL(10, 2), default=0.00, comment="损耗重量（克）")
    loss_rate = Column(DECIMAL(5, 2), default=0.00, comment="损耗率（%）")
    process_cost = Column(DECIMAL(10, 2), default=0.00, comment="加工成本")
    labor_cost = Column(DECIMAL(10, 2), default=0.00, comment="人工成本")
    other_cost = Column(DECIMAL(10, 2), default=0.00, comment="其他成本")
    total_cost = Column(DECIMAL(10, 2), default=0.00, comment="总成本")
    target_type = Column(Integer, comment="目标类型：1=原料入库，2=商品入库，3=直接销售")
    target_id = Column(Integer, comment="目标ID（入库单ID或销售单ID）")
    is_converted = Column(Boolean, default=False, comment="是否已转换")
    convert_time = Column(Integer, comment="转换时间")
    createtime = Column(Integer, nullable=False, comment="创建时间")
    
    # 关联关系
    process = relationship("RecyclingProcess", back_populates="results")
    conversions = relationship("RecyclingToStock", back_populates="process_result")


class RecyclingToStock(Base):
    """回收转库存记录表"""
    __tablename__ = "fa_recycling_to_stock"
    
    id = Column(Integer, primary_key=True, index=True)
    convert_no = Column(String(50), unique=True, nullable=False, comment="转换单号")
    store_id = Column(Integer, ForeignKey("fa_store.id"), nullable=False, comment="入库门店ID")
    process_result_id = Column(Integer, ForeignKey("fa_recycling_process_result.id"), nullable=False, comment="处理结果ID")
    stock_type = Column(Integer, nullable=False, comment="库存类型：1=原料库存，2=商品库存")
    stock_in_id = Column(Integer, ForeignKey("fa_stock_in.id"), comment="关联的入库单ID")
    jewelry_id = Column(Integer, ForeignKey("fa_jewelry.id"), comment="新建的商品ID")
    material_id = Column(Integer, ForeignKey("fa_material.id"), comment="原料ID")
    quantity = Column(Integer, default=1, comment="数量")
    weight = Column(DECIMAL(10, 2), nullable=False, comment="重量（克）")
    unit_cost = Column(DECIMAL(10, 2), nullable=False, comment="单位成本")
    total_cost = Column(DECIMAL(10, 2), nullable=False, comment="总成本")
    cost_detail = Column(Text, comment="成本明细（JSON格式）")
    status = Column(Integer, default=1, comment="状态：0=已取消，1=已转换")
    operator_id = Column(Integer, ForeignKey("fa_admin.id"), nullable=False, comment="操作员ID")
    remark = Column(String(500), comment="备注")
    createtime = Column(Integer, nullable=False, comment="创建时间")
    
    # 关联关系
    store = relationship("Store", backref="stock_conversions")
    process_result = relationship("RecyclingProcessResult", back_populates="conversions")
    stock_in = relationship("StockIn", backref="recycling_conversions")
    jewelry = relationship("Jewelry", backref="recycling_conversions")
    material = relationship("Material", backref="stock_conversions")
    operator = relationship("Admin", backref="stock_conversions")


class Material(Base):
    """原料管理表"""
    __tablename__ = "fa_material"
    
    id = Column(Integer, primary_key=True, index=True)
    material_no = Column(String(50), unique=True, nullable=False, comment="原料编号")
    name = Column(String(100), nullable=False, comment="原料名称")
    type = Column(Integer, nullable=False, comment="原料类型：1=黄金，2=白银，3=其他")
    purity = Column(DECIMAL(5, 2), comment="纯度")
    unit = Column(String(20), default="克", comment="单位")
    store_id = Column(Integer, ForeignKey("fa_store.id"), nullable=False, comment="所属门店ID")
    current_stock = Column(DECIMAL(10, 2), default=0.00, comment="当前库存量")
    unit_cost = Column(DECIMAL(10, 2), default=0.00, comment="单位成本")
    status = Column(Integer, default=1, comment="状态：0=停用，1=启用")
    createtime = Column(Integer, nullable=False, comment="创建时间")
    updatetime = Column(Integer, nullable=False, comment="更新时间")
    
    # 关联关系
    store = relationship("Store", backref="materials")