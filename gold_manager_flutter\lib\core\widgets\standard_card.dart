import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

/// 标准化卡片组件
/// 遵循UI设计规范：白色背景、圆角12px、轻微阴影、内边距16px
class StandardCard extends StatelessWidget {
  /// 卡片内容
  final Widget child;
  
  /// 内边距，默认为16px
  final EdgeInsetsGeometry? padding;
  
  /// 外边距
  final EdgeInsetsGeometry? margin;
  
  /// 点击事件
  final VoidCallback? onTap;
  
  /// 是否启用阴影，默认为true
  final bool enableShadow;
  
  /// 自定义圆角，默认为12px
  final double? borderRadius;
  
  /// 自定义背景色，默认为白色
  final Color? backgroundColor;
  
  /// 自定义边框
  final Border? border;
  
  /// 是否启用涟漪效果，默认为true
  final bool enableRipple;

  const StandardCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.onTap,
    this.enableShadow = true,
    this.borderRadius,
    this.backgroundColor,
    this.border,
    this.enableRipple = true,
  });

  @override
  Widget build(BuildContext context) {
    final cardRadius = borderRadius ?? AppTheme.borderRadius;
    final cardPadding = padding ?? const EdgeInsets.all(AppTheme.padding);
    
    Widget cardContent = Container(
      margin: margin,
      decoration: BoxDecoration(
        color: backgroundColor ?? AppTheme.cardBackgroundColor,
        borderRadius: BorderRadius.circular(cardRadius),
        border: border,
        boxShadow: enableShadow ? AppTheme.cardShadow : null,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(cardRadius),
        child: Material(
          color: Colors.transparent,
          child: onTap != null && enableRipple
              ? InkWell(
                  onTap: onTap,
                  borderRadius: BorderRadius.circular(cardRadius),
                  child: Padding(
                    padding: cardPadding,
                    child: child,
                  ),
                )
              : GestureDetector(
                  onTap: onTap,
                  child: Padding(
                    padding: cardPadding,
                    child: child,
                  ),
                ),
        ),
      ),
    );

    return cardContent;
  }
}

/// 信息卡片组件
/// 用于显示统计信息、状态等
class InfoCard extends StatelessWidget {
  /// 标题
  final String title;
  
  /// 数值
  final String value;
  
  /// 图标
  final IconData? icon;
  
  /// 图标颜色
  final Color? iconColor;
  
  /// 趋势指示器（可选）
  final Widget? trend;
  
  /// 点击事件
  final VoidCallback? onTap;

  const InfoCard({
    super.key,
    required this.title,
    required this.value,
    this.icon,
    this.iconColor,
    this.trend,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return StandardCard(
      onTap: onTap,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // 标题行
          Row(
            children: [
              if (icon != null) ...[
                Icon(
                  icon,
                  size: 20,
                  color: iconColor ?? AppTheme.primaryColor,
                ),
                const SizedBox(width: AppTheme.paddingSmall),
              ],
              Expanded(
                child: Text(
                  title,
                  style: const TextStyle(
                    fontSize: AppTheme.captionSize,
                    color: AppTheme.secondaryTextColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              if (trend != null) trend!,
            ],
          ),
          const SizedBox(height: AppTheme.paddingSmall),
          
          // 数值
          Text(
            value,
            style: const TextStyle(
              fontSize: AppTheme.headingMediumSize,
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryTextColor,
            ),
          ),
        ],
      ),
    );
  }
}

/// 列表项卡片组件
/// 用于列表中的项目展示
class ListItemCard extends StatelessWidget {
  /// 标题
  final String title;
  
  /// 副标题
  final String? subtitle;
  
  /// 描述文本
  final String? description;
  
  /// 前导图标或图片
  final Widget? leading;
  
  /// 尾部操作按钮
  final Widget? trailing;
  
  /// 点击事件
  final VoidCallback? onTap;
  
  /// 是否显示分隔线
  final bool showDivider;

  const ListItemCard({
    super.key,
    required this.title,
    this.subtitle,
    this.description,
    this.leading,
    this.trailing,
    this.onTap,
    this.showDivider = false,
  });

  @override
  Widget build(BuildContext context) {
    return StandardCard(
      margin: const EdgeInsets.symmetric(
        horizontal: AppTheme.padding,
        vertical: AppTheme.paddingSmall / 2,
      ),
      onTap: onTap,
      child: Column(
        children: [
          Row(
            children: [
              // 前导内容
              if (leading != null) ...[
                leading!,
                const SizedBox(width: AppTheme.paddingMedium),
              ],
              
              // 主要内容
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 标题
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: AppTheme.bodySize,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.primaryTextColor,
                      ),
                    ),
                    
                    // 副标题
                    if (subtitle != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        subtitle!,
                        style: const TextStyle(
                          fontSize: AppTheme.captionSize,
                          color: AppTheme.secondaryTextColor,
                        ),
                      ),
                    ],
                    
                    // 描述
                    if (description != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        description!,
                        style: const TextStyle(
                          fontSize: AppTheme.smallSize,
                          color: AppTheme.secondaryTextColor,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ],
                ),
              ),
              
              // 尾部内容
              if (trailing != null) ...[
                const SizedBox(width: AppTheme.paddingMedium),
                trailing!,
              ],
            ],
          ),
          
          // 分隔线
          if (showDivider) ...[
            const SizedBox(height: AppTheme.paddingMedium),
            const Divider(height: 1),
          ],
        ],
      ),
    );
  }
}
