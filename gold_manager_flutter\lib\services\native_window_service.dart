import 'dart:io';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../core/utils/logger.dart';

/// 原生窗口控制服务
/// 使用平台通道直接调用Windows API来控制窗口
class NativeWindowService extends GetxService {
  static const MethodChannel _channel = MethodChannel('com.goldmanager.window_control');
  
  // 窗口状态
  final RxBool isMaximized = false.obs;
  final RxBool isMinimized = false.obs;
  final Rx<Size> windowSize = const Size(1400, 900).obs;

  /// 服务初始化
  @override
  Future<void> onInit() async {
    super.onInit();
    LoggerService.i('🪟 原生窗口服务初始化开始...');
    
    if (_isDesktopPlatform()) {
      await _updateWindowState();
      LoggerService.i('✅ 原生窗口服务初始化完成');
    } else {
      LoggerService.i('ℹ️ 非桌面平台，跳过原生窗口服务初始化');
    }
  }

  /// 检查是否为桌面平台
  bool _isDesktopPlatform() {
    return Platform.isWindows || Platform.isMacOS || Platform.isLinux;
  }

  /// 最大化窗口（登录成功后调用）
  Future<void> maximizeWindow() async {
    if (!_isDesktopPlatform()) return;

    try {
      LoggerService.i('🚀 开始原生窗口最大化...');
      
      final result = await _channel.invokeMethod('maximizeWindow');
      
      if (result == true) {
        LoggerService.i('✅ 原生窗口最大化调用成功');
        
        // 等待窗口状态更新
        await Future.delayed(const Duration(milliseconds: 500));
        await _updateWindowState();
        
        LoggerService.i('📊 最大化后窗口状态:');
        LoggerService.i('   - 最大化状态: ${isMaximized.value}');
        LoggerService.i('   - 窗口尺寸: ${windowSize.value.width.toInt()}x${windowSize.value.height.toInt()}');
        
      } else {
        LoggerService.w('⚠️ 原生窗口最大化返回失败');
      }
      
    } catch (e) {
      LoggerService.e('❌ 原生窗口最大化失败', e);
    }
  }

  /// 最小化窗口
  Future<void> minimizeWindow() async {
    if (!_isDesktopPlatform()) return;

    try {
      LoggerService.i('📉 最小化窗口...');
      
      final result = await _channel.invokeMethod('minimizeWindow');
      
      if (result == true) {
        LoggerService.i('✅ 窗口最小化成功');
        await _updateWindowState();
      }
      
    } catch (e) {
      LoggerService.e('❌ 窗口最小化失败', e);
    }
  }

  /// 恢复窗口
  Future<void> restoreWindow() async {
    if (!_isDesktopPlatform()) return;

    try {
      LoggerService.i('🔄 恢复窗口...');
      
      final result = await _channel.invokeMethod('restoreWindow');
      
      if (result == true) {
        LoggerService.i('✅ 窗口恢复成功');
        await _updateWindowState();
      }
      
    } catch (e) {
      LoggerService.e('❌ 窗口恢复失败', e);
    }
  }

  /// 设置窗口大小
  Future<void> setWindowSize(int width, int height) async {
    if (!_isDesktopPlatform()) return;

    try {
      LoggerService.i('📏 设置窗口大小: ${width}x$height');
      
      final result = await _channel.invokeMethod('setWindowSize', {
        'width': width,
        'height': height,
      });
      
      if (result == true) {
        LoggerService.i('✅ 窗口大小设置成功');
        await _updateWindowState();
      }
      
    } catch (e) {
      LoggerService.e('❌ 设置窗口大小失败', e);
    }
  }

  /// 获取当前窗口大小
  Future<Size> getWindowSize() async {
    if (!_isDesktopPlatform()) return const Size(1400, 900);

    try {
      final result = await _channel.invokeMethod('getWindowSize');
      
      if (result is Map) {
        final width = (result['width'] as num).toDouble();
        final height = (result['height'] as num).toDouble();
        return Size(width, height);
      }
      
    } catch (e) {
      LoggerService.e('❌ 获取窗口大小失败', e);
    }
    
    return const Size(1400, 900);
  }

  /// 检查窗口是否最大化
  Future<bool> checkIsMaximized() async {
    if (!_isDesktopPlatform()) return false;

    try {
      final result = await _channel.invokeMethod('isMaximized');
      return result == true;
    } catch (e) {
      LoggerService.e('❌ 检查最大化状态失败', e);
      return false;
    }
  }

  /// 更新窗口状态
  Future<void> _updateWindowState() async {
    try {
      // 获取窗口大小
      final size = await getWindowSize();
      windowSize.value = size;
      
      // 检查最大化状态
      final maximized = await checkIsMaximized();
      isMaximized.value = maximized;
      
      LoggerService.d('🔄 窗口状态已更新: ${size.width.toInt()}x${size.height.toInt()}, 最大化: $maximized');
      
    } catch (e) {
      LoggerService.e('❌ 更新窗口状态失败', e);
    }
  }

  /// 智能最大化（如果未最大化则最大化，如果已最大化则恢复）
  Future<void> toggleMaximize() async {
    if (!_isDesktopPlatform()) return;

    try {
      await _updateWindowState();
      
      if (isMaximized.value) {
        await restoreWindow();
      } else {
        await maximizeWindow();
      }
      
    } catch (e) {
      LoggerService.e('❌ 切换最大化状态失败', e);
    }
  }

  /// 设置登录窗口大小
  Future<void> setLoginWindowSize() async {
    await setWindowSize(1400, 900);
  }

  /// 设置管理界面窗口大小（最大化）
  Future<void> setManagementWindowSize() async {
    await maximizeWindow();
  }
}
