import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/routes/app_pages.dart';
import '../../../core/theme/app_theme.dart';
import '../controllers/auth_controller.dart';

/// 启动页面
class SplashView extends StatefulWidget {
  const SplashView({super.key});

  @override
  State<SplashView> createState() => _SplashViewState();
}

class _SplashViewState extends State<SplashView> {
  @override
  void initState() {
    super.initState();
    // 在initState中进行导航，确保页面已经构建完成
    _checkAuthAndNavigate();
  }

  /// 检查认证状态并导航到对应页面
  Future<void> _checkAuthAndNavigate() async {
    // 延迟一段时间，展示启动页
    await Future.delayed(const Duration(seconds: 2));
    
    // 安全地获取控制器
    try {
      final authController = Get.find<AuthController>();
      
      // 根据认证状态导航
      if (authController.isAuthenticated()) {
        Get.offAllNamed(Routes.DASHBOARD);
      } else {
        Get.offAllNamed(Routes.LOGIN);
      }
    } catch (e) {
      // 如果控制器未找到，直接导航到登录页
      Get.offAllNamed(Routes.LOGIN);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 应用Logo
            const Icon(
              Icons.diamond_outlined,
              size: 120,
              color: AppTheme.primaryColor,
            ),
            const SizedBox(height: 24),
            
            // 应用名称
            Text(
              'app_name'.tr,
              style: const TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryColor,
              ),
            ),
            const SizedBox(height: 48),
            
            // 加载指示器
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
            ),
          ],
        ),
      ),
    );
  }
} 