import 'package:dio/dio.dart';
import '../../utils/logger_service.dart';

/// 错误拦截器
/// 用于处理和转换API错误
class ErrorInterceptor extends Interceptor {
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    LoggerService.e('ErrorInterceptor: API错误', err);
    
    switch (err.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        err = _handleTimeoutError(err);
        break;
      case DioExceptionType.badResponse:
        err = _handleResponseError(err);
        break;
      case DioExceptionType.cancel:
        err = DioException(
          requestOptions: err.requestOptions,
          message: '请求已取消',
          type: err.type
        );
        break;
      default:
        err = DioException(
          requestOptions: err.requestOptions,
          message: '网络错误，请检查网络连接',
          type: err.type
        );
    }
    
    super.onError(err, handler);
  }
  
  /// 处理超时错误
  DioException _handleTimeoutError(DioException err) {
    return DioException(
      requestOptions: err.requestOptions,
      message: '连接超时，请检查网络连接',
      type: err.type
    );
  }
  
  /// 处理响应错误
  DioException _handleResponseError(DioException err) {
    final int? statusCode = err.response?.statusCode;
    
    String message;
    switch (statusCode) {
      case 400:
        message = '请求参数错误';
        break;
      case 401:
        message = '登录状态已过期，请重新登录';
        // 这里可以触发登出逻辑
        // AuthService.instance.logout();
        break;
      case 403:
        message = '没有权限访问该资源';
        break;
      case 404:
        message = '请求的资源不存在';
        break;
      case 500:
      case 501:
      case 502:
      case 503:
        message = '服务器错误';
        break;
      default:
        message = '未知错误，状态码：$statusCode';
    }
    
    // 尝试从响应体中获取更详细的错误信息
    try {
      final Map<String, dynamic>? data = err.response?.data;
      if (data != null && data.containsKey('message')) {
        message = data['message'] as String;
      }
    } catch (e) {
      LoggerService.w('解析错误信息失败', e);
    }
    
    return DioException(
      requestOptions: err.requestOptions,
      response: err.response,
      message: message,
      type: err.type
    );
  }
} 