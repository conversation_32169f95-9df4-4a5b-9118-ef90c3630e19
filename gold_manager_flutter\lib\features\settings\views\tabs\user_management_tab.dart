import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../controllers/settings_controller.dart';
import '../../models/setting_model.dart';

/// 用户管理Tab页面
class UserManagementTab extends StatelessWidget {
  const UserManagementTab({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<SettingsController>();
    
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSearchBar(controller),
          const SizedBox(height: 16),
          _buildUserToolbar(context, controller),
          const SizedBox(height: 16),
          Expanded(
            child: _buildUserList(context, controller),
          ),
        ],
      ),
    );
  }
  
  /// 构建搜索栏
  Widget _buildSearchBar(SettingsController controller) {
    // 角色选项
    final roleOptions = [
      {'id': 0, 'name': '全部角色'},
      {'id': 1, 'name': '管理员'},
      {'id': 2, 'name': '店长'},
      {'id': 3, 'name': '销售员'},
    ];
    
    // 门店选项
    final storeOptions = [
      {'id': 0, 'name': '全部门店'},
      {'id': 1, 'name': '总店'},
      {'id': 2, 'name': '分店一'},
      {'id': 3, 'name': '分店二'},
    ];
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: TextField(
                    decoration: const InputDecoration(
                      hintText: '搜索用户名、姓名或电话',
                      prefixIcon: Icon(Icons.search),
                      border: OutlineInputBorder(),
                      contentPadding: EdgeInsets.symmetric(vertical: 0, horizontal: 16),
                    ),
                    onChanged: (value) {
                      if (value.length > 2 || value.isEmpty) {
                        controller.searchUsers(value);
                      }
                    },
                  ),
                ),
                const SizedBox(width: 16),
                ElevatedButton.icon(
                  onPressed: () {
                    controller.loadUserList();
                  },
                  icon: const Icon(Icons.refresh),
                  label: const Text('刷新'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Obx(() => DropdownButtonFormField<int>(
                    value: controller.selectedRoleId.value,
                    decoration: const InputDecoration(
                      labelText: '角色',
                      border: OutlineInputBorder(),
                      contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    ),
                    items: roleOptions
                        .map((role) => DropdownMenuItem<int>(
                              value: role['id'] as int,
                              child: Text(role['name'] as String),
                            ))
                        .toList(),
                    onChanged: (value) {
                      if (value != null) {
                        controller.filterUsers(roleId: value);
                      }
                    },
                  )),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Obx(() => DropdownButtonFormField<int>(
                    value: controller.selectedStoreId.value,
                    decoration: const InputDecoration(
                      labelText: '门店',
                      border: OutlineInputBorder(),
                      contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    ),
                    items: storeOptions
                        .map((store) => DropdownMenuItem<int>(
                              value: store['id'] as int,
                              child: Text(store['name'] as String),
                            ))
                        .toList(),
                    onChanged: (value) {
                      if (value != null) {
                        controller.filterUsers(storeId: value);
                      }
                    },
                  )),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  /// 构建用户工具栏
  Widget _buildUserToolbar(BuildContext context, SettingsController controller) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        OutlinedButton.icon(
          onPressed: () {
            // 导出用户列表
            _showMessage(context, '导出用户列表功能即将上线');
          },
          icon: const Icon(Icons.download),
          label: const Text('导出'),
        ),
        const SizedBox(width: 16),
        ElevatedButton.icon(
          onPressed: () {
            // 添加用户
            _showAddUserDialog(context, controller);
          },
          icon: const Icon(Icons.add),
          label: const Text('添加用户'),
        ),
      ],
    );
  }
  
  /// 构建用户列表
  Widget _buildUserList(BuildContext context, SettingsController controller) {
    return Obx(() {
      final users = controller.userList;
      
      if (users.isEmpty) {
        return const Center(
          child: Text('没有找到用户', style: TextStyle(fontSize: 16)),
        );
      }
      
      return Card(
        child: SingleChildScrollView(
          scrollDirection: Axis.vertical,
          child: DataTable(
            headingTextStyle: const TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
            dataTextStyle: const TextStyle(
              color: Colors.black87,
            ),
            columnSpacing: 24,
            columns: const [
              DataColumn(label: Text('用户名')),
              DataColumn(label: Text('姓名')),
              DataColumn(label: Text('角色')),
              DataColumn(label: Text('门店')),
              DataColumn(label: Text('电话')),
              DataColumn(label: Text('状态')),
              DataColumn(label: Text('最后登录')),
              DataColumn(label: Text('操作')),
            ],
            rows: users.map((user) {
              return DataRow(
                cells: [
                  DataCell(Text(user.username)),
                  DataCell(Text(user.displayName)),
                  DataCell(Text(user.roleName)),
                  DataCell(Text(user.storeName)),
                  DataCell(Text(user.phone)),
                  DataCell(_buildStatusBadge(user.status)),
                  DataCell(Text(user.lastLoginAt != null
                      ? DateFormat('yyyy-MM-dd HH:mm').format(user.lastLoginAt!)
                      : '-')),
                  DataCell(Row(
                    children: [
                      IconButton(
                        tooltip: '编辑',
                        icon: const Icon(Icons.edit, size: 20),
                        onPressed: () {
                          _showEditUserDialog(context, controller, user);
                        },
                      ),
                      IconButton(
                        tooltip: '重置密码',
                        icon: const Icon(Icons.key, size: 20),
                        onPressed: () {
                          _showResetPasswordDialog(context, controller, user);
                        },
                      ),
                      IconButton(
                        tooltip: '删除',
                        icon: const Icon(Icons.delete, color: Colors.red, size: 20),
                        onPressed: () {
                          _showDeleteUserDialog(context, controller, user);
                        },
                      ),
                    ],
                  )),
                ],
              );
            }).toList(),
          ),
        ),
      );
    });
  }
  
  /// 构建用户状态标签
  Widget _buildStatusBadge(int status) {
    final isActive = status == 1;
    final color = isActive ? Colors.green : Colors.grey;
    final text = isActive ? '正常' : '禁用';
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontWeight: FontWeight.bold,
          fontSize: 12,
        ),
      ),
    );
  }
  
  /// 显示添加用户对话框
  void _showAddUserDialog(BuildContext context, SettingsController controller) {
    final formKey = GlobalKey<FormState>();
    
    String username = '';
    String password = '';
    String displayName = '';
    String email = '';
    String phone = '';
    int roleId = 3; // 默认为销售员
    int storeId = 1; // 默认为总店
    
    // 角色选项
    final roleOptions = [
      {'id': 1, 'name': '管理员'},
      {'id': 2, 'name': '店长'},
      {'id': 3, 'name': '销售员'},
    ];
    
    // 门店选项
    final storeOptions = [
      {'id': 1, 'name': '总店'},
      {'id': 2, 'name': '分店一'},
      {'id': 3, 'name': '分店二'},
    ];
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('添加用户'),
        content: SizedBox(
          width: 400,
          child: Form(
            key: formKey,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: '用户名',
                      hintText: '登录用的用户名',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return '请输入用户名';
                      }
                      if (value.length < 3) {
                        return '用户名至少需要3个字符';
                      }
                      return null;
                    },
                    onSaved: (value) {
                      username = value ?? '';
                    },
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: '密码',
                      hintText: '登录密码',
                      border: OutlineInputBorder(),
                    ),
                    obscureText: true,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return '请输入密码';
                      }
                      if (value.length < 6) {
                        return '密码至少需要6个字符';
                      }
                      return null;
                    },
                    onSaved: (value) {
                      password = value ?? '';
                    },
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: '姓名',
                      hintText: '用户真实姓名',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return '请输入姓名';
                      }
                      return null;
                    },
                    onSaved: (value) {
                      displayName = value ?? '';
                    },
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: '电子邮件',
                      hintText: '可选',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.emailAddress,
                    onSaved: (value) {
                      email = value ?? '';
                    },
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: '手机号',
                      hintText: '可选',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.phone,
                    onSaved: (value) {
                      phone = value ?? '';
                    },
                  ),
                  const SizedBox(height: 16),
                  DropdownButtonFormField<int>(
                    value: roleId,
                    decoration: const InputDecoration(
                      labelText: '角色',
                      border: OutlineInputBorder(),
                    ),
                    items: roleOptions
                        .map((role) => DropdownMenuItem<int>(
                              value: role['id'] as int,
                              child: Text(role['name'] as String),
                            ))
                        .toList(),
                    onChanged: (value) {
                      if (value != null) {
                        roleId = value;
                      }
                    },
                  ),
                  const SizedBox(height: 16),
                  DropdownButtonFormField<int>(
                    value: storeId,
                    decoration: const InputDecoration(
                      labelText: '所属门店',
                      border: OutlineInputBorder(),
                    ),
                    items: storeOptions
                        .map((store) => DropdownMenuItem<int>(
                              value: store['id'] as int,
                              child: Text(store['name'] as String),
                            ))
                        .toList(),
                    onChanged: (value) {
                      if (value != null) {
                        storeId = value;
                      }
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (formKey.currentState?.validate() ?? false) {
                formKey.currentState?.save();
                
                // 创建用户
                final userData = {
                  'username': username,
                  'password': password,
                  'display_name': displayName,
                  'email': email,
                  'phone': phone,
                  'role_id': roleId,
                  'store_id': storeId,
                  'status': 1, // 启用状态
                };
                
                Navigator.pop(context);
                
                final success = await controller.createUser(userData);
                
                if (success) {
                  _showMessage(context, '用户创建成功');
                } else {
                  _showMessage(context, '用户创建失败，请稍后重试');
                }
              }
            },
            child: const Text('添加'),
          ),
        ],
      ),
    );
  }
  
  /// 显示编辑用户对话框
  void _showEditUserDialog(BuildContext context, SettingsController controller, User user) {
    final formKey = GlobalKey<FormState>();
    
    String displayName = user.displayName;
    String email = user.email;
    String phone = user.phone;
    int roleId = user.roleId;
    int storeId = user.storeId;
    int status = user.status;
    
    // 角色选项
    final roleOptions = [
      {'id': 1, 'name': '管理员'},
      {'id': 2, 'name': '店长'},
      {'id': 3, 'name': '销售员'},
    ];
    
    // 门店选项
    final storeOptions = [
      {'id': 1, 'name': '总店'},
      {'id': 2, 'name': '分店一'},
      {'id': 3, 'name': '分店二'},
    ];
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('编辑用户: ${user.username}'),
        content: SizedBox(
          width: 400,
          child: Form(
            key: formKey,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextFormField(
                    initialValue: displayName,
                    decoration: const InputDecoration(
                      labelText: '姓名',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return '请输入姓名';
                      }
                      return null;
                    },
                    onSaved: (value) {
                      displayName = value ?? '';
                    },
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    initialValue: email,
                    decoration: const InputDecoration(
                      labelText: '电子邮件',
                      hintText: '可选',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.emailAddress,
                    onSaved: (value) {
                      email = value ?? '';
                    },
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    initialValue: phone,
                    decoration: const InputDecoration(
                      labelText: '手机号',
                      hintText: '可选',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.phone,
                    onSaved: (value) {
                      phone = value ?? '';
                    },
                  ),
                  const SizedBox(height: 16),
                  DropdownButtonFormField<int>(
                    value: roleId,
                    decoration: const InputDecoration(
                      labelText: '角色',
                      border: OutlineInputBorder(),
                    ),
                    items: roleOptions
                        .map((role) => DropdownMenuItem<int>(
                              value: role['id'] as int,
                              child: Text(role['name'] as String),
                            ))
                        .toList(),
                    onChanged: (value) {
                      if (value != null) {
                        roleId = value;
                      }
                    },
                  ),
                  const SizedBox(height: 16),
                  DropdownButtonFormField<int>(
                    value: storeId,
                    decoration: const InputDecoration(
                      labelText: '所属门店',
                      border: OutlineInputBorder(),
                    ),
                    items: storeOptions
                        .map((store) => DropdownMenuItem<int>(
                              value: store['id'] as int,
                              child: Text(store['name'] as String),
                            ))
                        .toList(),
                    onChanged: (value) {
                      if (value != null) {
                        storeId = value;
                      }
                    },
                  ),
                  const SizedBox(height: 16),
                  DropdownButtonFormField<int>(
                    value: status,
                    decoration: const InputDecoration(
                      labelText: '状态',
                      border: OutlineInputBorder(),
                    ),
                    items: const [
                      DropdownMenuItem<int>(
                        value: 1,
                        child: Text('正常'),
                      ),
                      DropdownMenuItem<int>(
                        value: 0,
                        child: Text('禁用'),
                      ),
                    ],
                    onChanged: (value) {
                      if (value != null) {
                        status = value;
                      }
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (formKey.currentState?.validate() ?? false) {
                formKey.currentState?.save();
                
                // 更新用户
                final userData = {
                  'display_name': displayName,
                  'email': email,
                  'phone': phone,
                  'role_id': roleId,
                  'store_id': storeId,
                  'status': status,
                };
                
                Navigator.pop(context);
                
                final success = await controller.updateUser(user.id, userData);
                
                if (success) {
                  _showMessage(context, '用户更新成功');
                } else {
                  _showMessage(context, '用户更新失败，请稍后重试');
                }
              }
            },
            child: const Text('保存'),
          ),
        ],
      ),
    );
  }
  
  /// 显示重置密码对话框
  void _showResetPasswordDialog(BuildContext context, SettingsController controller, User user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('重置密码'),
        content: Text('确定要重置用户"${user.username}"的密码吗？重置后将生成一个临时密码。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              
              final success = await controller.resetUserPassword(user.id);
              
              if (success) {
                _showMessage(context, '密码重置成功，临时密码已发送给用户');
              } else {
                _showMessage(context, '密码重置失败，请稍后重试');
              }
            },
            child: const Text('确定重置'),
          ),
        ],
      ),
    );
  }
  
  /// 显示删除用户对话框
  void _showDeleteUserDialog(BuildContext context, SettingsController controller, User user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除用户'),
        content: Text('确定要删除用户"${user.username}"吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            onPressed: () async {
              Navigator.pop(context);
              
              final success = await controller.deleteUser(user.id);
              
              if (success) {
                _showMessage(context, '用户删除成功');
              } else {
                _showMessage(context, '用户删除失败，请稍后重试');
              }
            },
            child: const Text('确定删除'),
          ),
        ],
      ),
    );
  }
  
  /// 显示消息提示
  void _showMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
} 