import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../core/constants/border_styles.dart';
import '../../../core/theme/app_theme.dart';
import '../../../services/auth_service.dart';
import '../../../widgets/responsive_builder.dart';
import '../controllers/inventory_check_controller.dart';

/// 库存盘点列表页面
class InventoryCheckView extends StatelessWidget {
  const InventoryCheckView({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<InventoryCheckController>();

    return Material(
      color: AppTheme.backgroundColor,
      child: Column(
        children: [
          // 筛选区域
          _buildFilterSection(controller),
          const Divider(height: 1),
          // 统计信息区域
          _buildStatisticsSection(controller),
          // 主要内容区域
          Expanded(child: _buildInventoryCheckList(controller)),
          // 分页区域
          _buildPagination(controller),
        ],
      ),
    );
  }

  /// 构建筛选区域 - 复用入库管理页面的单行布局结构
  Widget _buildFilterSection(InventoryCheckController controller) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: AppBorderStyles.tableBorder,
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center, // 确保所有控件垂直居中对齐
        children: [
          // 图标和标题 - 复用入库管理页面的样式
          Icon(
            Icons.inventory_2,
            color: Colors.blue[600],
            size: 20
          ),
          const SizedBox(width: 8),
          const Text(
            '库存盘点',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(width: 24),

          // 操作员信息标签 - 复用入库管理页面的样式
          Obx(() {
            final authService = Get.find<AuthService>();
            final operatorName = authService.userNickname.value.isNotEmpty
                ? authService.userNickname.value
                : authService.userName.value;
            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(AppBorderStyles.largeBorderRadius), // 使用统一的大圆角
                border: Border.all(color: Colors.blue[200]!, width: AppBorderStyles.borderWidth), // 使用统一边框宽度
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.person, size: 14, color: Colors.blue[600]),
                  const SizedBox(width: 4),
                  Text(
                    '操作员: $operatorName',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.blue[700],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            );
          }),
          const SizedBox(width: 24),

          // 门店标签和选择器 - 复用入库管理页面的样式
          const Text(
            '门店:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(width: 8),
          SizedBox(
            width: 150,
            child: _buildCompactStoreSelector(controller),
          ),
          const SizedBox(width: 24),

          // 状态标签和选择器
          const Text(
            '状态:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(width: 8),
          SizedBox(
            width: 120,
            child: _buildCompactStatusSelector(controller),
          ),
          const SizedBox(width: 24),

          // 搜索框 - 紧凑型设计
          const Text(
            '搜索:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(width: 8),
          SizedBox(
            width: 200,
            child: _buildCompactSearchField(controller),
          ),
          const SizedBox(width: 24),

          // 日期范围选择器 - 紧凑型设计
          const Text(
            '日期:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(width: 8),
          SizedBox(
            width: 200,
            child: _buildCompactDateRangePicker(controller),
          ),
          const SizedBox(width: 24),

          // 操作按钮组
          _buildResetButton(controller),
          const SizedBox(width: 8),
          _buildSearchButton(controller),
          const SizedBox(width: 8),
          _buildCreateButton(controller),
        ],
      ),
    );
  }

  /// 构建紧凑型搜索字段（单行布局）
  Widget _buildCompactSearchField(InventoryCheckController controller) {
    return Container(
      height: 32, // 紧凑高度
      decoration: AppBorderStyles.standardBoxDecoration,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: TextField(
          controller: controller.searchController,
          decoration: const InputDecoration(
            hintText: '盘点单号、备注',
            border: InputBorder.none,
            enabledBorder: InputBorder.none,
            focusedBorder: InputBorder.none,
            disabledBorder: InputBorder.none,
            errorBorder: InputBorder.none,
            focusedErrorBorder: InputBorder.none,
            contentPadding: EdgeInsets.zero,
            hintStyle: TextStyle(fontSize: 13, color: Colors.grey),
            isDense: true,
          ),
          style: const TextStyle(fontSize: 13),
          onSubmitted: (_) => controller.searchInventoryChecks(),
        ),
      ),
    );
  }

  /// 构建紧凑型门店选择器（单行布局）
  Widget _buildCompactStoreSelector(InventoryCheckController controller) {
    return Obx(() {
      // 检查用户权限
      final authService = Get.find<AuthService>();
      final isAdmin = authService.userRole.value == 'admin' || authService.hasPermission('super.admin');
      final currentUserStoreId = authService.storeId.value;
      final currentStoreName = authService.storeName.value;

      if (!isAdmin && currentUserStoreId > 0) {
        // 普通员工：显示当前用户所属门店，不可选择
        return Container(
          height: 32, // 紧凑高度
          decoration: BoxDecoration(
            border: Border.all(color: AppBorderStyles.borderColor),
            borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
            color: Colors.grey[50],
          ),
          child: Row(
            children: [
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  currentStoreName,
                  style: const TextStyle(
                    fontSize: 13,
                    color: Colors.black87,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const Icon(Icons.lock, size: 14, color: Colors.grey),
              const SizedBox(width: 8),
            ],
          ),
        );
      } else {
        // 管理员：可以选择门店
        return Container(
          height: 32, // 紧凑高度
          decoration: AppBorderStyles.standardBoxDecoration,
          child: DropdownButtonHideUnderline(
            child: DropdownButton<int?>(
              value: controller.selectedStoreId.value,
              hint: const Text('全部门店', style: TextStyle(fontSize: 13, color: Colors.grey)),
              isExpanded: true,
              items: [
                const DropdownMenuItem<int?>(
                  value: null,
                  child: Text('全部门店', style: TextStyle(fontSize: 13)),
                ),
                ...controller.storeList.map((store) => DropdownMenuItem<int?>(
                  value: store.id,
                  child: Text(store.name, style: const TextStyle(fontSize: 13)),
                )),
              ],
              onChanged: (value) {
                controller.selectedStoreId.value = value;
                controller.searchInventoryChecks();
              },
              style: const TextStyle(fontSize: 13, color: Colors.black87),
              icon: const Icon(Icons.arrow_drop_down, size: 20),
              iconSize: 20,
              menuMaxHeight: 300,
              padding: const EdgeInsets.symmetric(horizontal: 8),
            ),
          ),
        );
      }
    });
  }

  /// 构建紧凑型状态选择器（单行布局）
  Widget _buildCompactStatusSelector(InventoryCheckController controller) {
    return Obx(() => Container(
      height: 32, // 紧凑高度
      decoration: AppBorderStyles.standardBoxDecoration,
      child: DropdownButtonHideUnderline(
        child: DropdownButton<int?>(
          value: controller.selectedStatus.value,
          hint: const Text('全部状态', style: TextStyle(fontSize: 13, color: Colors.grey)),
          isExpanded: true,
          items: const [
            DropdownMenuItem<int?>(
              value: null,
              child: Text('全部状态', style: TextStyle(fontSize: 13)),
            ),
            DropdownMenuItem<int?>(
              value: 0,
              child: Text('进行中', style: TextStyle(fontSize: 13)),
            ),
            DropdownMenuItem<int?>(
              value: 1,
              child: Text('已完成', style: TextStyle(fontSize: 13)),
            ),
            DropdownMenuItem<int?>(
              value: 2,
              child: Text('已取消', style: TextStyle(fontSize: 13)),
            ),
          ],
          onChanged: (value) {
            controller.selectedStatus.value = value;
            controller.searchInventoryChecks();
          },
          style: const TextStyle(fontSize: 13, color: Colors.black87),
          icon: const Icon(Icons.arrow_drop_down, size: 20),
          iconSize: 20,
          menuMaxHeight: 300,
          padding: const EdgeInsets.symmetric(horizontal: 8),
        ),
      ),
    ));
  }

  /// 构建紧凑型日期范围选择器（单行布局）
  Widget _buildCompactDateRangePicker(InventoryCheckController controller) {
    final dateFormat = DateFormat('MM-dd');

    return Obx(() {
      final startDateText = controller.startDate.value != null
          ? dateFormat.format(controller.startDate.value!)
          : '开始';

      final endDateText = controller.endDate.value != null
          ? dateFormat.format(controller.endDate.value!)
          : '结束';

      return Container(
        height: 32, // 紧凑高度
        decoration: AppBorderStyles.standardBoxDecoration,
        child: Row(
          children: [
            Expanded(
              child: InkWell(
                onTap: () => _selectStartDate(Get.context!, controller),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(AppBorderStyles.borderRadius),
                  bottomLeft: Radius.circular(AppBorderStyles.borderRadius),
                ),
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 8),
                  child: Row(
                    children: [
                      Icon(Icons.calendar_today, size: 14, color: Colors.grey[600]),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          startDateText,
                          style: TextStyle(
                            fontSize: 13,
                            color: controller.startDate.value != null
                                ? Colors.black87
                                : Colors.grey[600],
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            Container(
              width: 1,
              height: 20,
              color: Colors.grey[300],
            ),
            Expanded(
              child: InkWell(
                onTap: () => _selectEndDate(Get.context!, controller),
                borderRadius: const BorderRadius.only(
                  topRight: Radius.circular(AppBorderStyles.borderRadius),
                  bottomRight: Radius.circular(AppBorderStyles.borderRadius),
                ),
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 8),
                  child: Row(
                    children: [
                      Icon(Icons.calendar_today, size: 14, color: Colors.grey[600]),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          endDateText,
                          style: TextStyle(
                            fontSize: 13,
                            color: controller.endDate.value != null
                                ? Colors.black87
                                : Colors.grey[600],
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      );
    });
  }

  /// 选择开始日期
  Future<void> _selectStartDate(BuildContext context, InventoryCheckController controller) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: controller.startDate.value ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      controller.startDate.value = picked;
      controller.searchInventoryChecks();
    }
  }

  /// 选择结束日期
  Future<void> _selectEndDate(BuildContext context, InventoryCheckController controller) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: controller.endDate.value ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      controller.endDate.value = picked;
      controller.searchInventoryChecks();
    }
  }

  /// 构建重置按钮 - 紧凑型设计
  Widget _buildResetButton(InventoryCheckController controller) {
    return SizedBox(
      height: 32, // 强制高度为32px，与其他控件保持一致
      child: OutlinedButton.icon(
        icon: const Icon(Icons.refresh, size: 14),
        label: const Text('重置'),
        style: OutlinedButton.styleFrom(
          foregroundColor: Colors.grey[600],
          padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 12), // 移除垂直内边距，由SizedBox控制高度
          minimumSize: const Size(0, 32), // 固定高度32px
          maximumSize: const Size(double.infinity, 32), // 限制最大高度
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
          ),
          side: const BorderSide(color: AppBorderStyles.borderColor),
          textStyle: const TextStyle(fontSize: 13), // 紧凑字体
        ),
        onPressed: controller.resetFilters,
      ),
    );
  }

  /// 构建搜索按钮 - 紧凑型设计
  Widget _buildSearchButton(InventoryCheckController controller) {
    return SizedBox(
      height: 32, // 强制高度为32px，与其他控件保持一致
      child: ElevatedButton.icon(
        icon: const Icon(Icons.search, size: 14),
        label: const Text('搜索'),
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF1E88E5), // 使用UI规范主色调
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 12), // 移除垂直内边距，由SizedBox控制高度
          minimumSize: const Size(0, 32), // 固定高度32px
          maximumSize: const Size(double.infinity, 32), // 限制最大高度
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
          ),
          textStyle: const TextStyle(fontSize: 13), // 紧凑字体
        ),
        onPressed: controller.searchInventoryChecks,
      ),
    );
  }

  /// 构建新建盘点按钮 - 紧凑型设计
  Widget _buildCreateButton(InventoryCheckController controller) {
    return Obx(() => SizedBox(
      height: 32, // 强制高度为32px，与其他控件保持一致
      child: ElevatedButton.icon(
        icon: const Icon(Icons.add_box, size: 14),
        label: const Text('新建盘点'),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.green[600], // 绿色背景
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 12), // 移除垂直内边距，由SizedBox控制高度
          minimumSize: const Size(0, 32), // 固定高度32px
          maximumSize: const Size(double.infinity, 32), // 限制最大高度
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
          ),
          textStyle: const TextStyle(fontSize: 13), // 紧凑字体
        ),
        onPressed: controller.canCreate ? controller.createInventoryCheck : null,
      ),
    ));
  }

  /// 构建统计信息区域
  Widget _buildStatisticsSection(InventoryCheckController controller) {
    return Obx(() => Container(
      padding: const EdgeInsets.all(16),
      decoration: AppBorderStyles.standardBoxDecoration.copyWith(
        color: Colors.blue[50],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem('总盘点单', controller.totalCount.toString(), Icons.inventory),
          _buildStatItem('进行中', controller.ongoingCount.toString(), Icons.play_circle),
          _buildStatItem('已完成', controller.completedCount.toString(), Icons.check_circle),
          _buildStatItem('差异商品', controller.differenceCount.toString(), Icons.warning),
        ],
      ),
    ));
  }

  /// 构建统计项
  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, size: 24, color: Colors.blue[600]),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  /// 构建盘点单列表
  Widget _buildInventoryCheckList(InventoryCheckController controller) {
    return ResponsiveBuilder(
      builder: (context, sizingInformation) {
        if (sizingInformation.isMobile) {
          return _buildMobileList(controller);
        } else {
          return _buildDataTable(controller);
        }
      },
    );
  }

  /// 构建数据表格（桌面端和平板端）
  Widget _buildDataTable(InventoryCheckController controller) {
    return Obx(() {
      if (controller.isLoading.value) {
        return const Center(child: CircularProgressIndicator());
      }

      if (controller.inventoryChecks.isEmpty) {
        return const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.inventory_2_outlined, size: 64, color: Colors.grey),
              SizedBox(height: 16),
              Text('暂无盘点单数据', style: TextStyle(color: Colors.grey)),
            ],
          ),
        );
      }

      return LayoutBuilder(
        builder: (context, constraints) {
          // 获取可用宽度
          final availableWidth = constraints.maxWidth;

          // 优化列宽分配：确保总宽度为99%（保留1%缓冲）
          final indexWidth = availableWidth * 0.05;        // 5% - 序号
          final checkNoWidth = availableWidth * 0.12;      // 12% - 盘点单号
          final storeWidth = availableWidth * 0.10;        // 10% - 门店名称
          final statusWidth = availableWidth * 0.06;       // 6% - 状态（从8%减少到6%）
          final totalCountWidth = availableWidth * 0.07;   // 7% - 应盘数量（从8%减少到7%）
          final checkedCountWidth = availableWidth * 0.07; // 7% - 已盘数量（从8%减少到7%）
          final diffCountWidth = availableWidth * 0.06;    // 6% - 差异数量（从8%减少到6%）
          final operatorWidth = availableWidth * 0.10;     // 10% - 操作员
          final startTimeWidth = availableWidth * 0.12;    // 12% - 开始时间
          final endTimeWidth = availableWidth * 0.12;      // 12% - 结束时间
          final actionWidth = availableWidth * 0.10;       // 10% - 操作（从7%增加到10%）

          // 根据可用宽度调整字体大小
          final fontSize = availableWidth < 800 ? 12.0 : 14.0;
          final headingFontSize = availableWidth < 800 ? 13.0 : 15.0;

          return Container(
            margin: const EdgeInsets.all(4),
            decoration: AppBorderStyles.elevatedBoxDecoration.copyWith(
              color: Colors.white,
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
              child: SingleChildScrollView(
                scrollDirection: Axis.vertical,
                child: SizedBox(
                  width: availableWidth,
                  child: DataTable(
                    columnSpacing: 0, // 移除列间距，让列宽完全由我们控制
                    horizontalMargin: 0, // 移除水平边距
                    headingRowHeight: 44, // 减少表头高度
                    dataRowMinHeight: 48, // 减少数据行高度
                    dataRowMaxHeight: 48,
                    headingTextStyle: TextStyle(
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                      fontSize: headingFontSize,
                    ),
                    dataTextStyle: TextStyle(
                      fontSize: fontSize,
                      color: Colors.black87,
                    ),
                    headingRowColor: WidgetStateProperty.all(AppBorderStyles.tableHeaderBackground),
                    border: AppBorderStyles.tableStandardBorder,
                    columns: [
                      DataColumn(
                        label: Container(
                          width: indexWidth,
                          padding: const EdgeInsets.symmetric(horizontal: 4),
                          child: const Text('序号', textAlign: TextAlign.center),
                        ),
                      ),
                      DataColumn(
                        label: Container(
                          width: checkNoWidth,
                          padding: const EdgeInsets.symmetric(horizontal: 4),
                          child: const Text('盘点单号', textAlign: TextAlign.center),
                        ),
                      ),
                      DataColumn(
                        label: Container(
                          width: storeWidth,
                          padding: const EdgeInsets.symmetric(horizontal: 4),
                          child: const Text('门店名称', textAlign: TextAlign.center),
                        ),
                      ),
                      DataColumn(
                        label: Container(
                          width: statusWidth,
                          padding: const EdgeInsets.symmetric(horizontal: 4),
                          child: const Text('状态', textAlign: TextAlign.center),
                        ),
                      ),
                      DataColumn(
                        label: Container(
                          width: totalCountWidth,
                          padding: const EdgeInsets.symmetric(horizontal: 4),
                          child: const Text('应盘数量', textAlign: TextAlign.center),
                        ),
                      ),
                      DataColumn(
                        label: Container(
                          width: checkedCountWidth,
                          padding: const EdgeInsets.symmetric(horizontal: 4),
                          child: const Text('已盘数量', textAlign: TextAlign.center),
                        ),
                      ),
                      DataColumn(
                        label: Container(
                          width: diffCountWidth,
                          padding: const EdgeInsets.symmetric(horizontal: 4),
                          child: const Text('差异数量', textAlign: TextAlign.center),
                        ),
                      ),
                      DataColumn(
                        label: Container(
                          width: operatorWidth,
                          padding: const EdgeInsets.symmetric(horizontal: 4),
                          child: const Text('操作员', textAlign: TextAlign.center),
                        ),
                      ),
                      DataColumn(
                        label: Container(
                          width: startTimeWidth,
                          padding: const EdgeInsets.symmetric(horizontal: 4),
                          child: const Text('开始时间', textAlign: TextAlign.center),
                        ),
                      ),
                      DataColumn(
                        label: Container(
                          width: endTimeWidth,
                          padding: const EdgeInsets.symmetric(horizontal: 4),
                          child: const Text('结束时间', textAlign: TextAlign.center),
                        ),
                      ),
                      DataColumn(
                        label: Container(
                          width: actionWidth,
                          padding: const EdgeInsets.symmetric(horizontal: 4),
                          child: const Text('操作', textAlign: TextAlign.center),
                        ),
                      ),
                    ],
                    rows: controller.inventoryChecks.asMap().entries.map((entry) {
                      final index = entry.key;
                      final check = entry.value;
                      return DataRow(
                        cells: [
                          DataCell(
                            Container(
                              width: indexWidth,
                              height: 48,
                              padding: const EdgeInsets.symmetric(horizontal: 4),
                              alignment: Alignment.center,
                              child: Text(
                                '${index + 1}',
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                            ),
                          ),
                          DataCell(
                            Container(
                              width: checkNoWidth,
                              height: 48,
                              padding: const EdgeInsets.symmetric(horizontal: 4),
                              alignment: Alignment.center,
                              child: InkWell(
                                onTap: () => controller.viewInventoryCheck(check),
                                child: Text(
                                  check.checkNo,
                                  style: const TextStyle(
                                    color: AppTheme.primaryColor,
                                    fontWeight: FontWeight.w500,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 1,
                                ),
                              ),
                            ),
                          ),
                          DataCell(
                            Container(
                              width: storeWidth,
                              height: 48,
                              padding: const EdgeInsets.symmetric(horizontal: 4),
                              alignment: Alignment.center,
                              child: Text(
                                check.storeName ?? '未知门店',
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                            ),
                          ),
                          DataCell(
                            Container(
                              width: statusWidth,
                              height: 48,
                              padding: const EdgeInsets.symmetric(horizontal: 4),
                              alignment: Alignment.center,
                              child: _buildStatusChip(check.status, check.statusText),
                            ),
                          ),
                          DataCell(
                            Container(
                              width: totalCountWidth,
                              height: 48,
                              padding: const EdgeInsets.symmetric(horizontal: 4),
                              alignment: Alignment.center,
                              child: Text(
                                check.totalCount.toString(),
                                style: const TextStyle(
                                  fontWeight: FontWeight.w500,
                                  color: AppTheme.primaryColor,
                                ),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                            ),
                          ),
                          DataCell(
                            Container(
                              width: checkedCountWidth,
                              height: 48,
                              padding: const EdgeInsets.symmetric(horizontal: 4),
                              alignment: Alignment.center,
                              child: Text(
                                check.checkedCount.toString(),
                                style: const TextStyle(
                                  fontWeight: FontWeight.w500,
                                  color: Colors.green,
                                ),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                            ),
                          ),
                          DataCell(
                            Container(
                              width: diffCountWidth,
                              height: 48,
                              padding: const EdgeInsets.symmetric(horizontal: 4),
                              alignment: Alignment.center,
                              child: Text(
                                check.differenceCount.toString(),
                                style: TextStyle(
                                  fontWeight: FontWeight.w500,
                                  color: check.differenceCount > 0 ? Colors.red : Colors.grey,
                                ),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                            ),
                          ),
                          DataCell(
                            Container(
                              width: operatorWidth,
                              height: 48,
                              padding: const EdgeInsets.symmetric(horizontal: 4),
                              alignment: Alignment.center,
                              child: Text(
                                check.operatorName ?? '未知',
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                            ),
                          ),
                          DataCell(
                            Container(
                              width: startTimeWidth,
                              height: 48,
                              padding: const EdgeInsets.symmetric(horizontal: 4),
                              alignment: Alignment.center,
                              child: Text(
                                check.startTime?.toString().split(' ')[0] ?? '-',
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                            ),
                          ),
                          DataCell(
                            Container(
                              width: endTimeWidth,
                              height: 48,
                              padding: const EdgeInsets.symmetric(horizontal: 4),
                              alignment: Alignment.center,
                              child: Text(
                                check.endTime?.toString().split(' ')[0] ?? '-',
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                            ),
                          ),
                          DataCell(
                            Container(
                              width: actionWidth,
                              height: 48,
                              padding: const EdgeInsets.symmetric(horizontal: 4),
                              alignment: Alignment.center,
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                mainAxisSize: MainAxisSize.min,
                                children: _buildActionButtonsList(controller, check, availableWidth < 800),
                              ),
                            ),
                          ),
                        ],
                      );
                    }).toList(),
                  ),
                ),
              ),
            ),
          );
        },
      );
    });
  }

  /// 构建移动端列表
  Widget _buildMobileList(InventoryCheckController controller) {
    return Obx(() {
      if (controller.isLoading.value) {
        return const Center(child: CircularProgressIndicator());
      }

      if (controller.inventoryChecks.isEmpty) {
        return const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.inventory_2_outlined, size: 64, color: Colors.grey),
              SizedBox(height: 16),
              Text('暂无盘点单数据', style: TextStyle(color: Colors.grey)),
            ],
          ),
        );
      }

      return ListView.builder(
        padding: const EdgeInsets.all(8),
        itemCount: controller.inventoryChecks.length,
        itemBuilder: (context, index) {
          final check = controller.inventoryChecks[index];
          return Card(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppBorderStyles.largeBorderRadius),
            ),
            elevation: 2,
            margin: const EdgeInsets.only(bottom: 8),
            child: InkWell(
              borderRadius: BorderRadius.circular(AppBorderStyles.largeBorderRadius),
              onTap: () => controller.viewInventoryCheck(check),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          check.checkNo,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        _buildStatusChip(check.status, check.statusText),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text('门店: ${check.storeName ?? '未知门店'}'),
                    Text('进度: ${check.checkedCount}/${check.totalCount} (${(check.progress * 100).toStringAsFixed(1)}%)'),
                    Text('差异: ${check.differenceCount} 件'),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        _buildActionButtons(controller, check),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      );
    });
  }

  /// 构建状态标签
  Widget _buildStatusChip(int status, String statusText) {
    Color color;
    switch (status) {
      case 0:
        color = Colors.blue;
        break;
      case 1:
        color = Colors.green;
        break;
      case 2:
        color = Colors.red;
        break;
      default:
        color = Colors.grey;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
        border: Border.all(color: color, width: 1),
      ),
      child: Text(
        statusText,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  /// 构建操作按钮列表
  List<Widget> _buildActionButtonsList(InventoryCheckController controller, dynamic check, [bool isSmallScreen = false]) {
    final List<Widget> buttons = [];
    final double iconSize = isSmallScreen ? 16 : 18;
    final double buttonSize = isSmallScreen ? 28 : 32;

    // 查看按钮
    buttons.add(
      SizedBox(
        width: buttonSize,
        height: buttonSize,
        child: IconButton(
          icon: Icon(Icons.visibility, color: Colors.blue, size: iconSize),
          tooltip: '查看',
          padding: EdgeInsets.zero,
          onPressed: () => controller.viewInventoryCheck(check),
        ),
      ),
    );

    // 编辑按钮
    if (check.canEdit && controller.canEdit) {
      buttons.add(
        SizedBox(
          width: buttonSize,
          height: buttonSize,
          child: IconButton(
            icon: Icon(Icons.edit, color: Colors.orange, size: iconSize),
            tooltip: '编辑',
            padding: EdgeInsets.zero,
            onPressed: () => controller.editInventoryCheck(check),
          ),
        ),
      );
    }

    // 删除按钮
    if (check.canDelete && controller.canDelete) {
      buttons.add(
        SizedBox(
          width: buttonSize,
          height: buttonSize,
          child: IconButton(
            icon: Icon(Icons.delete, color: Colors.red, size: iconSize),
            tooltip: '删除',
            padding: EdgeInsets.zero,
            onPressed: () => controller.deleteInventoryCheck(check),
          ),
        ),
      );
    }

    // 盘点按钮 - 只有状态为"进行中"(status=0)的盘点单才显示
    if (check.status == 0 && controller.canCheck(check)) {
      buttons.add(
        SizedBox(
          width: buttonSize,
          height: buttonSize,
          child: IconButton(
            icon: Icon(Icons.inventory_2, color: Colors.blue[600], size: iconSize),
            tooltip: '盘点',
            padding: EdgeInsets.zero,
            onPressed: () => controller.startInventoryCheck(check),
          ),
        ),
      );
    }

    return buttons;
  }

  /// 构建操作按钮（保持兼容性）
  Widget _buildActionButtons(InventoryCheckController controller, dynamic check) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: _buildActionButtonsList(controller, check),
    );
  }

  /// 构建分页组件
  Widget _buildPagination(InventoryCheckController controller) {
    return Obx(() {
      if (controller.totalPages.value <= 1) return const SizedBox.shrink();

      return Container(
        padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 16), // 减少垂直内边距
        color: Colors.white,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            IconButton(
              icon: const Icon(Icons.first_page),
              tooltip: '第一页',
              onPressed: controller.currentPage.value > 1
                  ? () => controller.goToPage(1)
                  : null,
            ),
            IconButton(
              icon: const Icon(Icons.chevron_left),
              tooltip: '上一页',
              onPressed: controller.currentPage.value > 1
                  ? () => controller.goToPage(controller.currentPage.value - 1)
                  : null,
            ),
            Text(
              '${controller.currentPage.value} / ${controller.totalPages.value}',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            IconButton(
              icon: const Icon(Icons.chevron_right),
              tooltip: '下一页',
              onPressed: controller.currentPage.value < controller.totalPages.value
                  ? () => controller.goToPage(controller.currentPage.value + 1)
                  : null,
            ),
            IconButton(
              icon: const Icon(Icons.last_page),
              tooltip: '最后一页',
              onPressed: controller.currentPage.value < controller.totalPages.value
                  ? () => controller.goToPage(controller.totalPages.value)
                  : null,
            ),
          ],
        ),
      );
    });
  }
}
