#!/usr/bin/env python3
"""
超简化的FastAPI测试，不连接任何数据库
"""

from fastapi import FastAPI
import uvicorn

# 创建最简单的FastAPI应用
app = FastAPI(title="简化测试API")

@app.get("/")
async def root():
    return {"message": "测试成功！API正常响应"}

@app.get("/health")
async def health():
    return {"status": "ok", "message": "无数据库连接的健康检查"}

@app.get("/test")
async def test():
    return {"test": "这是测试端点", "data": [1, 2, 3]}

if __name__ == "__main__":
    print("🚀 启动超简化API测试...")
    uvicorn.run(
        "test_simple:app",
        host="0.0.0.0",
        port=8001,  # 使用不同端口避免冲突
        reload=False,
        log_level="info"
    )