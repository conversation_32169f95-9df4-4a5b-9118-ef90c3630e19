# 销售管理功能开发计划

## 📋 项目概述

为黄金珠宝管理系统添加完整的销售管理功能，专注于展示商品级别的销售明细和利润分析。

## 🎯 核心需求

### 主要功能
1. **商品销售明细管理** - 显示每一件已售商品的详细信息
2. **销售类型区分** - 区分零售、批发、店间调拨、回收变现
3. **利润分析** - 显示成本价、销售价、利润、利润率
4. **多维度筛选** - 支持时间、门店、类型、关键词等筛选
5. **统计汇总** - 实时计算筛选结果的汇总数据

### 页面设计要求
- 表格形式展示商品明细
- 同一出库单商品使用颜色区分
- 默认显示零售类型数据
- 遵循《界面UI风格.md》规范

## ✅ 已完成任务

### 后端API开发
- [x] **销售管理API** - 完整的RESTful API
  - `GET /api/v1/sales/items` - 获取销售商品明细列表
  - `GET /api/v1/sales/statistics` - 获取销售统计信息
  - `GET /api/v1/sales/order/{id}/items` - 获取单据商品明细

- [x] **数据模型** - 完整的Pydantic模型
  - `SalesItemDetailResponse` - 销售明细响应模型
  - `SalesQueryParams` - 查询参数模型
  - `SalesStatistics` - 统计信息模型
  - `SalesType` - 销售类型枚举

- [x] **业务服务** - 核心业务逻辑
  - `SalesService` - 销售管理服务类
  - 数据源整合（出库单、调拨单、回收单）
  - 利润计算逻辑
  - 多类型数据查询

- [x] **安全控制**
  - JWT认证验证
  - `sales.view` 权限控制
  - 防止未授权访问

- [x] **API测试**
  - 基本功能测试
  - 导入验证测试

### 文档编写
- [x] **UI风格指南** - `docs/界面UI风格.md`
  - 基于出库管理页面的完整UI规范
  - 颜色系统、字体系统、组件规范
  - 响应式设计要求

## 🔄 待完成任务

### 1. 前端页面开发 (高优先级)

#### 1.1 销售管理控制器
- [ ] 创建 `SalesController`
  - 实现销售数据获取逻辑
  - 支持销售类型筛选
  - 实现分页和搜索功能
  - 添加统计数据计算

#### 1.2 销售管理主页面
- [ ] 创建 `SalesManagementView`
  - 实现表格显示商品明细
  - 添加筛选栏（销售类型、时间、门店等）
  - 实现同一出库单商品颜色区分
  - 添加统计汇总栏

#### 1.3 UI组件开发
- [ ] 创建 `SalesTypeSelector` - 销售类型选择器
- [ ] 创建 `SalesFilterBar` - 完整筛选栏
- [ ] 创建 `SalesStatisticsBar` - 统计汇总栏
- [ ] 创建 `SalesDetailDialog` - 出库单详情对话框

#### 1.4 数据模型
- [ ] 创建 `SalesItemDetail` - 前端销售明细模型
- [ ] 创建 `SalesStatistics` - 前端统计模型

### 2. 功能增强 (中优先级)

#### 2.1 高级筛选功能
- [ ] 利润范围筛选
- [ ] 商品分类筛选
- [ ] 操作员业绩筛选
- [ ] 自定义时间段快捷选择

#### 2.2 数据导出功能
- [ ] Excel导出销售明细
- [ ] PDF打印销售报表
- [ ] 数据可视化图表

#### 2.3 性能优化
- [ ] 大数据量分页优化
- [ ] 缓存机制实现
- [ ] 查询性能优化

### 3. 系统集成 (中优先级)

#### 3.1 路由配置
- [ ] 添加销售管理路由到 `app_pages.dart`
- [ ] 更新仪表板菜单项
- [ ] 配置权限验证

#### 3.2 服务集成
- [ ] 创建 `SalesService` 前端服务
- [ ] 集成API调用逻辑
- [ ] 添加错误处理机制

### 4. 测试与调优 (低优先级)

#### 4.1 功能测试
- [ ] 单元测试编写
- [ ] 集成测试验证
- [ ] 用户体验测试

#### 4.2 性能测试
- [ ] 大数据量加载测试
- [ ] 筛选功能性能测试
- [ ] 内存使用优化

## 📁 文件结构规划

### 前端文件结构
```
lib/features/sales/
├── controllers/
│   └── sales_controller.dart           # 销售管理控制器
├── views/
│   ├── sales_management_view.dart      # 销售管理主页面
│   └── sales_detail_view.dart          # 销售详情页面
├── widgets/
│   ├── sales_type_selector.dart        # 销售类型选择器
│   ├── sales_filter_bar.dart           # 筛选栏组件
│   ├── sales_statistics_bar.dart       # 统计栏组件
│   └── sales_detail_dialog.dart        # 销售详情对话框
├── models/
│   ├── sales_item_detail.dart          # 销售明细模型
│   └── sales_statistics.dart           # 销售统计模型
└── services/
    └── sales_service.dart              # 销售管理服务
```

### 后端文件结构 (已完成)
```
GoldManager_FastAdmin_API/
├── app/api/api_v1/endpoints/
│   └── sales.py                        # ✅ 销售管理API路由
├── app/schemas/
│   └── sales.py                        # ✅ 销售相关数据模型
├── app/services/
│   └── sales_service.py                # ✅ 销售管理业务服务
└── test_sales_api.py                   # ✅ API测试文件
```

## 🎨 UI设计要求

### 页面布局
```
┌─────────────────────────────────────────────────────────────┐
│ 📊 销售管理                                                   │
├─────────────────────────────────────────────────────────────┤
│ [零售▼] [时间范围] [门店] [搜索框] [重置] [搜索]                │
├─────────────────────────────────────────────────────────────┤
│ 出库单号  │ 类型 │ 条码    │ 商品名   │ 销售价 │ 利润 │ 操作   │
│ CK001    │ 零售 │ 1001   │ 金戒指   │ 1200  │ 200  │ 查看   │
│ CK001    │ 零售 │ 1002   │ 银项链   │ 800   │ 150  │ 查看   │
│ CK002    │ 批发 │ 1003   │ 金手镯   │ 2000  │ 300  │ 查看   │
├─────────────────────────────────────────────────────────────┤
│ 📈 总计: 件数:150 销售额:¥50,000 利润:¥8,500 利润率:17%      │
└─────────────────────────────────────────────────────────────┘
```

### 颜色区分方案
- **同一出库单商品**：使用相同浅色背景（5种颜色循环）
- **零售商品**：白色背景
- **批发商品**：浅蓝色背景
- **调拨商品**：浅绿色背景
- **回收变现**：浅紫色背景

### 核心交互
1. **默认选择零售**：页面加载时自动选择零售类型
2. **出库单号点击**：显示完整单据详情
3. **实时筛选**：筛选条件改变时自动更新数据
4. **分页加载**：支持大数据量分页显示

## 🚀 开发优先级

### Phase 1: 核心功能 (1-2周)
1. 销售管理控制器开发
2. 主页面UI实现
3. 基础筛选功能
4. 数据展示表格

### Phase 2: 功能完善 (1周)
1. 销售类型筛选器
2. 统计汇总功能
3. 详情对话框
4. 路由集成

### Phase 3: 优化增强 (1周)
1. 性能优化
2. 用户体验改进
3. 数据导出功能
4. 测试完善

## 📝 开发注意事项

### 技术要求
1. **严格遵循** `docs/界面UI风格.md` 规范
2. **复用现有组件** 保持系统一致性
3. **响应式设计** 支持多设备显示
4. **权限控制** 确保数据安全

### 数据处理
1. **利润计算** 使用统一的成本计算逻辑
2. **日期处理** 统一时间格式和时区
3. **分页优化** 处理大数据量场景
4. **缓存策略** 提升查询性能

### 用户体验
1. **加载状态** 显示数据加载进度
2. **错误处理** 友好的错误提示
3. **操作反馈** 及时的操作结果反馈
4. **数据刷新** 支持手动和自动刷新

## 🎯 验收标准

### 功能验收
- [ ] 能够正确显示各类型销售明细
- [ ] 筛选功能工作正常
- [ ] 利润计算准确无误
- [ ] 同一出库单商品正确区分
- [ ] 统计数据实时更新

### 性能验收
- [ ] 页面加载时间 < 3秒
- [ ] 筛选响应时间 < 1秒
- [ ] 支持1000+条记录流畅显示
- [ ] 内存使用合理

### UI验收
- [ ] 完全符合UI风格指南
- [ ] 响应式布局正常
- [ ] 颜色区分清晰可见
- [ ] 交互体验流畅

## 📞 技术支持

如开发过程中遇到问题，可参考：
1. **API文档**: `http://localhost:8000/docs`
2. **UI规范**: `docs/界面UI风格.md`
3. **现有实现**: 出库管理页面参考
4. **数据结构**: 后端schemas定义

---

**最后更新**: 2025年7月31日  
**文档版本**: v1.0  
**负责人**: Claude Code Assistant