import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:printing/printing.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:pdf/pdf.dart';
import 'dart:async';
import '../../../core/theme/app_theme.dart';
import '../../../core/utils/logger_service.dart';

/// PDF预览对话框
class PdfPreviewDialog extends StatefulWidget {
  final pw.Document pdfDocument;
  final String title;
  final VoidCallback? onPrint;

  const PdfPreviewDialog({
    super.key,
    required this.pdfDocument,
    required this.title,
    this.onPrint,
  });

  @override
  State<PdfPreviewDialog> createState() => _PdfPreviewDialogState();
}

class _PdfPreviewDialogState extends State<PdfPreviewDialog> {
  bool _isLoading = true;
  String? _errorMessage;
  Uint8List? _pdfBytes;

  @override
  void initState() {
    super.initState();
    _loadPdfBytes();
  }

  Future<void> _loadPdfBytes() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final bytes = await widget.pdfDocument.save();
      
      setState(() {
        _pdfBytes = bytes;
        _isLoading = false;
      });
      
      LoggerService.i('PDF预览数据加载成功');
    } catch (e) {
      setState(() {
        _errorMessage = '加载PDF预览失败: $e';
        _isLoading = false;
      });
      LoggerService.e('PDF预览数据加载失败', e);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: const EdgeInsets.all(20),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.9,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          children: [
            _buildHeader(),
            Expanded(
              child: _buildContent(),
            ),
            _buildFooter(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        color: AppTheme.primaryColor,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.picture_as_pdf,
            color: Colors.white,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              widget.title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
          IconButton(
            onPressed: () => Get.back(),
            icon: const Icon(
              Icons.close,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('正在加载PDF预览...'),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: const TextStyle(color: Colors.red),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadPdfBytes,
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    if (_pdfBytes == null) {
      return const Center(
        child: Text('PDF数据为空'),
      );
    }

    return Container(
      padding: const EdgeInsets.all(16),
      child: PdfPreview(
        build: (format) => _pdfBytes!,
        allowPrinting: false,  // 禁用默认打印按钮，使用自定义actions
        allowSharing: false,   // 禁用默认分享按钮，使用自定义actions
        canChangePageFormat: false,
        canChangeOrientation: false,
        canDebug: false,
        maxPageWidth: 700,
        pdfFileName: '${widget.title}.pdf',
        actions: [
          PdfPreviewAction(
            icon: const Icon(Icons.print),
            onPressed: _handlePrintAction,
          ),
          PdfPreviewAction(
            icon: const Icon(Icons.save_alt),
            onPressed: _handleSaveAction,
          ),
        ],
      ),
    );
  }

  Widget _buildFooter() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        color: Color(0xFFF5F5F5),
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(12),
          bottomRight: Radius.circular(12),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  /// PdfPreviewAction 的打印处理方法
  void _handlePrintAction(BuildContext context, FutureOr<Uint8List> Function(PdfPageFormat) build, PdfPageFormat format) {
    _handlePrint();
  }

  /// PdfPreviewAction 的保存处理方法
  void _handleSaveAction(BuildContext context, FutureOr<Uint8List> Function(PdfPageFormat) build, PdfPageFormat format) {
    _handleSave();
  }

  Future<void> _handlePrint() async {
    try {
      if (_pdfBytes == null) {
        Get.snackbar('错误', 'PDF数据未准备好');
        return;
      }

      LoggerService.i('开始打印PDF文档');

      // 根据文档类型选择合适的页面格式
      PdfPageFormat pageFormat = PdfPageFormat.a4;
      if (widget.title.contains('收款凭证')) {
        // 收款凭证使用210mm × 101.6mm格式
        pageFormat = const PdfPageFormat(210 * PdfPageFormat.mm, 101.6 * PdfPageFormat.mm);
      }

      await Printing.layoutPdf(
        onLayout: (format) => _pdfBytes!,
        name: widget.title,
        format: pageFormat,
      );

      LoggerService.i('打印对话框已打开');

      // 调用回调
      widget.onPrint?.call();

      Get.snackbar(
        '成功',
        '打印对话框已打开，请选择打印机进行打印',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      LoggerService.e('打印失败', e);
      Get.snackbar(
        '打印失败',
        '无法打开打印对话框: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  Future<void> _handleSave() async {
    try {
      if (_pdfBytes == null) {
        Get.snackbar('错误', 'PDF数据未准备好');
        return;
      }

      await Printing.sharePdf(
        bytes: _pdfBytes!,
        filename: '${widget.title}.pdf',
      );
      
      LoggerService.i('PDF文件分享/保存成功');
    } catch (e) {
      LoggerService.e('保存PDF失败', e);
      Get.snackbar(
        '保存失败',
        '无法保存PDF文件: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
}
