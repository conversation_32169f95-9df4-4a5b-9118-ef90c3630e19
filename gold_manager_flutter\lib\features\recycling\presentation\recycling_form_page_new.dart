import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../models/recycling/recycling_model.dart';
import '../controllers/recycling_controller.dart';
import '../../../services/auth_service.dart';

/// 回收单表单页面 - 重新设计的UI界面，参考出库管理页面
class RecyclingFormPage extends StatefulWidget {
  /// 回收单ID，新增时为null
  final int? id;

  const RecyclingFormPage({super.key, this.id});

  @override
  State<RecyclingFormPage> createState() => _RecyclingFormPageState();
}

class _RecyclingFormPageState extends State<RecyclingFormPage> {
  late final RecyclingController _controller;

  final _formKey = GlobalKey<FormState>();

  // 表单控制器
  final _customerNameController = TextEditingController();
  final _customerPhoneController = TextEditingController();
  final _remarkController = TextEditingController();

  // 回收物品列表
  final List<RecyclingItemForm> _items = [];

  // 编辑模式
  bool get _isEditMode => widget.id != null;

  // 是否正在加载
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _controller = Get.find<RecyclingController>();

    // 如果是编辑模式，加载回收单数据
    if (_isEditMode) {
      _loadRecyclingOrder();
    } else {
      // 新增模式，确保表单为初始状态
      _initializeNewForm();
    }
  }

  /// 初始化新建表单
  void _initializeNewForm() {
    // 确保表单为空白状态，但不添加默认物品
    _resetFormToEmpty();
  }

  @override
  void dispose() {
    _customerNameController.dispose();
    _customerPhoneController.dispose();
    _remarkController.dispose();

    // 清理物品表单控制器
    for (var item in _items) {
      item.dispose();
    }

    super.dispose();
  }

  /// 加载回收单数据
  Future<void> _loadRecyclingOrder() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await _controller.loadRecyclingOrderDetail(widget.id!);
      final order = _controller.currentOrder.value;

      if (order != null) {
        // 填充表单数据
        _customerNameController.text = order.customerName;
        _customerPhoneController.text = order.customerPhone;
        _remarkController.text = order.remark;

        // 填充物品数据
        setState(() {
          _items.clear();
          if (order.items.isNotEmpty) {
            for (var item in order.items) {
              _items.add(RecyclingItemForm.fromItem(item));
            }
          } else {
            _addEmptyItem();
          }
        });
      }
    } catch (e) {
      Get.snackbar(
        '加载失败',
        '加载回收单数据失败: $e',
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 添加空白物品
  void _addEmptyItem() {
    setState(() {
      _items.add(RecyclingItemForm());
    });
  }

  /// 移除物品
  void _removeItem(int index) {
    setState(() {
      final item = _items.removeAt(index);
      item.dispose();
    });
  }

  /// 重置表单到初始状态（保留一个空白物品，用于提交后重置）
  void _resetForm() {
    setState(() {
      // 清空表单控制器
      _customerNameController.clear();
      _customerPhoneController.clear();
      _remarkController.clear();

      // 清空并重置物品列表
      for (var item in _items) {
        item.dispose();
      }
      _items.clear();

      // 添加一个空白物品
      _addEmptyItem();
    });
  }

  /// 重置表单到完全空白状态（用于页面初始化）
  void _resetFormToEmpty() {
    setState(() {
      // 清空表单控制器
      _customerNameController.clear();
      _customerPhoneController.clear();
      _remarkController.clear();

      // 清空物品列表，不添加默认物品
      for (var item in _items) {
        item.dispose();
      }
      _items.clear();
    });
  }

  /// 保存表单
  Future<void> _saveForm() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // 验证是否有至少一个物品
    if (_items.isEmpty) {
      Get.snackbar('验证失败', '请至少添加一个回收物品', snackPosition: SnackPosition.BOTTOM);
      return;
    }

    // 准备回收单数据 - 符合后端API格式
    final Map<String, dynamic> orderData = {
      'store_id': 1, // TODO: 从用户选择的门店获取
      'customer_name': _customerNameController.text,
      'phone': _customerPhoneController.text,
      'remark': _remarkController.text,
      'items': _items
          .map(
            (item) => {
              'name': item.nameController.text,
              'category_id': item.categoryId,
              // 修复字段映射：使用贵金属专用字段
              'gold_weight':
                  double.tryParse(item.goldWeightController.text) ??
                  double.tryParse(item.weightController.text) ??
                  0.0,
              'gold_price':
                  double.tryParse(item.goldPriceController.text) ??
                  double.tryParse(item.priceController.text) ??
                  0.0,
              'silver_weight':
                  double.tryParse(item.silverWeightController.text) ?? 0.0,
              'silver_price':
                  double.tryParse(item.silverPriceController.text) ?? 0.0,
              'discount_rate':
                  double.tryParse(item.discountController.text) ?? 100.0,
              'remark': item.remarkController.text,
            },
          )
          .toList(),
    };

    setState(() {
      _isLoading = true;
    });

    try {
      if (_isEditMode) {
        orderData['id'] = widget.id;
        await _controller.updateRecyclingOrder(orderData);
      } else {
        await _controller.createRecyclingOrder(orderData);
      }

      Get.snackbar(
        '保存成功',
        _isEditMode ? '回收单修改成功' : '回收单创建成功',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green[600],
        colorText: Colors.white,
        duration: const Duration(seconds: 2),
      );

      if (!_isEditMode) {
        // 新增模式下，延迟重置表单以便继续添加新的回收单
        Future.delayed(const Duration(milliseconds: 500), () {
          _resetFormToEmpty();
        });
      }
    } catch (e) {
      Get.snackbar(
        '保存失败',
        '保存回收单数据失败: $e',
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Form(
        key: _formKey,
        child: Column(
          children: [
            _buildFormHeader(),
            _buildBarcodeSection(),
            Expanded(child: _buildItemList()),
            _buildFormFooter(),
          ],
        ),
      ),
    );
  }

  /// 构建表单头部区域 - 参考出库管理页面的单行布局
  Widget _buildFormHeader() {
    final authService = Get.find<AuthService>();

    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(bottom: BorderSide(color: Colors.grey, width: 1.0)),
      ),
      child: Row(
        children: [
          // 🔷 新建回收单（图标+标题）
          Icon(Icons.recycling, color: Colors.green[600], size: 20),
          const SizedBox(width: 8),
          Text(
            _isEditMode ? '编辑回收单' : '新建回收单',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),

          const SizedBox(width: 24),

          // 👤 操作员信息标签
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(12.0),
              border: Border.all(color: Colors.blue[200]!, width: 1.0),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.person, size: 14, color: Colors.blue[600]),
                const SizedBox(width: 4),
                Text(
                  '操作员: ${authService.userNickname.value.isNotEmpty ? authService.userNickname.value : authService.userName.value}',
                  style: TextStyle(
                    fontSize: 13,
                    color: Colors.blue[700],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),

          const Spacer(),

          // 保存按钮
          SizedBox(
            height: 32,
            child: ElevatedButton.icon(
              icon: const Icon(Icons.save, size: 14),
              label: const Text('保存'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green[600],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 0,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.0),
                ),
                textStyle: const TextStyle(fontSize: 13),
              ),
              onPressed: _isLoading ? null : _saveForm,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建条码扫描区域 - 客户信息输入
  Widget _buildBarcodeSection() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(bottom: BorderSide(color: Colors.grey, width: 1.0)),
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            // 📦 客户信息标题
            Icon(Icons.person, color: Colors.blue[600], size: 20),
            const SizedBox(width: 8),
            const Text(
              '客户信息',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),

            const SizedBox(width: 24),

            // 客户姓名输入框
            const Text(
              '客户姓名:',
              style: TextStyle(fontSize: 13, fontWeight: FontWeight.w500),
            ),
            const SizedBox(width: 8),
            SizedBox(
              width: 150,
              height: 32,
              child: Container(
                height: 32,
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border.all(color: Colors.grey[300]!, width: 1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 0,
                  ),
                  child: TextField(
                    controller: _customerNameController,
                    decoration: const InputDecoration(
                      hintText: '输入客户姓名',
                      hintStyle: TextStyle(fontSize: 13, color: Colors.grey),
                      border: InputBorder.none,
                      enabledBorder: InputBorder.none,
                      focusedBorder: InputBorder.none,
                      errorBorder: InputBorder.none,
                      focusedErrorBorder: InputBorder.none,
                      disabledBorder: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(vertical: 8),
                      isDense: true,
                    ),
                    style: const TextStyle(fontSize: 13),
                  ),
                ),
              ),
            ),

            const SizedBox(width: 24),

            // 客户电话输入框
            const Text(
              '客户电话:',
              style: TextStyle(fontSize: 13, fontWeight: FontWeight.w500),
            ),
            const SizedBox(width: 8),
            SizedBox(
              width: 150,
              height: 32,
              child: Container(
                height: 32,
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border.all(color: Colors.grey[300]!, width: 1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 0,
                  ),
                  child: TextField(
                    controller: _customerPhoneController,
                    decoration: const InputDecoration(
                      hintText: '输入客户电话',
                      hintStyle: TextStyle(fontSize: 13, color: Colors.grey),
                      border: InputBorder.none,
                      enabledBorder: InputBorder.none,
                      focusedBorder: InputBorder.none,
                      errorBorder: InputBorder.none,
                      focusedErrorBorder: InputBorder.none,
                      disabledBorder: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(vertical: 8),
                      isDense: true,
                    ),
                    style: const TextStyle(fontSize: 13),
                    keyboardType: TextInputType.phone,
                  ),
                ),
              ),
            ),

            const SizedBox(width: 24),

            // 备注输入框
            const Text(
              '备注:',
              style: TextStyle(fontSize: 13, fontWeight: FontWeight.w500),
            ),
            const SizedBox(width: 8),
            SizedBox(
              width: 200,
              height: 32,
              child: Container(
                height: 32,
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border.all(color: Colors.grey[300]!, width: 1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 0,
                  ),
                  child: TextField(
                    controller: _remarkController,
                    decoration: const InputDecoration(
                      hintText: '输入备注信息',
                      hintStyle: TextStyle(fontSize: 13, color: Colors.grey),
                      border: InputBorder.none,
                      enabledBorder: InputBorder.none,
                      focusedBorder: InputBorder.none,
                      errorBorder: InputBorder.none,
                      focusedErrorBorder: InputBorder.none,
                      disabledBorder: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(vertical: 8),
                      isDense: true,
                    ),
                    style: const TextStyle(fontSize: 13),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建物品列表区域
  Widget _buildItemList() {
    return Container(
      margin: const EdgeInsets.all(4),
      child: Column(
        children: [
          // 商品明细标题栏
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: const BoxDecoration(
              color: Colors.white,
              border: Border(
                bottom: BorderSide(color: Colors.grey, width: 1.0),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.inventory_2, color: Colors.green[600], size: 20),
                const SizedBox(width: 8),
                const Text(
                  '回收物品',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.green[50],
                    borderRadius: BorderRadius.circular(8.0),
                    border: Border.all(color: Colors.green[200]!, width: 1),
                  ),
                  child: Text(
                    '共 ${_items.length} 件物品',
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.green[700],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                SizedBox(
                  height: 32,
                  child: ElevatedButton.icon(
                    icon: const Icon(Icons.add, size: 14),
                    label: const Text('添加物品'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green[600],
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 0,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                      textStyle: const TextStyle(fontSize: 13),
                    ),
                    onPressed: _addEmptyItem,
                  ),
                ),
              ],
            ),
          ),
          // 商品列表内容
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _items.isEmpty
                ? _buildEmptyItemList()
                : _buildRecyclingItemTable(),
          ),
        ],
      ),
    );
  }

  /// 构建空物品列表
  Widget _buildEmptyItemList() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.inventory_2_outlined, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            '暂无回收物品',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.grey,
            ),
          ),
          SizedBox(height: 8),
          Text(
            '点击"添加物品"按钮录入回收物品信息',
            style: TextStyle(fontSize: 14, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  /// 构建回收物品表格 - 参考出库管理页面的表格实现
  Widget _buildRecyclingItemTable() {
    return LayoutBuilder(
      builder: (context, constraints) {
        final availableWidth = constraints.maxWidth;

        // 使用比例宽度 - 贵金属回收专用布局
        final indexWidth = availableWidth * 0.04; // 4% - 序号
        final nameWidth = availableWidth * 0.12; // 12% - 品名
        final categoryWidth = availableWidth * 0.08; // 8% - 分类
        final unitTypeWidth = availableWidth * 0.06; // 6% - 单位类型
        final goldWeightWidth = availableWidth * 0.08; // 8% - 金重量
        final goldPriceWidth = availableWidth * 0.08; // 8% - 金价格
        final silverWeightWidth = availableWidth * 0.08; // 8% - 银重量
        final silverPriceWidth = availableWidth * 0.08; // 8% - 银价格
        final discountWidth = availableWidth * 0.06; // 6% - 折扣率
        final amountWidth = availableWidth * 0.08; // 8% - 金额
        final finalAmountWidth = availableWidth * 0.08; // 8% - 折后金额
        final remarkWidth = availableWidth * 0.10; // 10% - 备注
        final actionWidth = availableWidth * 0.06; // 6% - 操作

        return Container(
          width: double.infinity,
          height: double.infinity,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.03),
                blurRadius: 3,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Column(
            children: [
              // 表格标题行
              Container(
                width: availableWidth,
                color: Colors.grey[100],
                height: 48,
                child: Row(
                  children: [
                    Container(
                      width: indexWidth,
                      decoration: const BoxDecoration(
                        border: Border(
                          right: BorderSide(color: Colors.grey, width: 1.0),
                        ),
                      ),
                      child: const Center(
                        child: Text(
                          '序号',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ),
                    Container(
                      width: nameWidth,
                      decoration: const BoxDecoration(
                        border: Border(
                          right: BorderSide(color: Colors.grey, width: 1.0),
                        ),
                      ),
                      child: const Center(
                        child: Text(
                          '品名',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ),
                    Container(
                      width: categoryWidth,
                      decoration: const BoxDecoration(
                        border: Border(
                          right: BorderSide(color: Colors.grey, width: 1.0),
                        ),
                      ),
                      child: const Center(
                        child: Text(
                          '分类',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ),
                    Container(
                      width: unitTypeWidth,
                      decoration: const BoxDecoration(
                        border: Border(
                          right: BorderSide(color: Colors.grey, width: 1.0),
                        ),
                      ),
                      child: const Center(
                        child: Text(
                          '单位',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ),
                    Container(
                      width: goldWeightWidth,
                      decoration: const BoxDecoration(
                        border: Border(
                          right: BorderSide(color: Colors.grey, width: 1.0),
                        ),
                      ),
                      child: const Center(
                        child: Text(
                          '金重(g)',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ),
                    Container(
                      width: goldPriceWidth,
                      decoration: const BoxDecoration(
                        border: Border(
                          right: BorderSide(color: Colors.grey, width: 1.0),
                        ),
                      ),
                      child: const Center(
                        child: Text(
                          '金价(元/g)',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ),
                    Container(
                      width: silverWeightWidth,
                      decoration: const BoxDecoration(
                        border: Border(
                          right: BorderSide(color: Colors.grey, width: 1.0),
                        ),
                      ),
                      child: const Center(
                        child: Text(
                          '银重(g)',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ),
                    Container(
                      width: silverPriceWidth,
                      decoration: const BoxDecoration(
                        border: Border(
                          right: BorderSide(color: Colors.grey, width: 1.0),
                        ),
                      ),
                      child: const Center(
                        child: Text(
                          '银价(元/g)',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ),
                    Container(
                      width: discountWidth,
                      decoration: const BoxDecoration(
                        border: Border(
                          right: BorderSide(color: Colors.grey, width: 1.0),
                        ),
                      ),
                      child: const Center(
                        child: Text(
                          '折扣%',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ),
                    Container(
                      width: amountWidth,
                      decoration: const BoxDecoration(
                        border: Border(
                          right: BorderSide(color: Colors.grey, width: 1.0),
                        ),
                      ),
                      child: const Center(
                        child: Text(
                          '金额(元)',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ),
                    Container(
                      width: finalAmountWidth,
                      decoration: const BoxDecoration(
                        border: Border(
                          right: BorderSide(color: Colors.grey, width: 1.0),
                        ),
                      ),
                      child: const Center(
                        child: Text(
                          '折后金额(元)',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ),
                    Container(
                      width: remarkWidth,
                      decoration: const BoxDecoration(
                        border: Border(
                          right: BorderSide(color: Colors.grey, width: 1.0),
                        ),
                      ),
                      child: const Center(
                        child: Text(
                          '备注',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(
                      width: actionWidth,
                      child: const Center(
                        child: Text(
                          '操作',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              // 表格数据行
              Expanded(
                child: ListView.builder(
                  itemCount: _items.length,
                  itemBuilder: (context, index) {
                    final item = _items[index];
                    return Container(
                      height: 52,
                      decoration: BoxDecoration(
                        color: index % 2 == 0 ? Colors.white : Colors.grey[50],
                        border: const Border(
                          bottom: BorderSide(color: Colors.grey, width: 1.0),
                        ),
                      ),
                      child: Row(
                        children: [
                          _buildDataCell('${index + 1}', indexWidth, false),
                          _buildEditableCell(
                            width: nameWidth,
                            controller: item.remarkController,
                            hintText: '输入品名',
                          ),
                          _buildCategoryCell(item, categoryWidth),
                          _buildUnitTypeCell(item, unitTypeWidth),
                          _buildEditableCell(
                            width: goldWeightWidth,
                            controller: item.goldWeightController,
                            hintText: '金重',
                            keyboardType: TextInputType.number,
                            onChanged: (value) => item.updateAmount(),
                          ),
                          _buildEditableCell(
                            width: goldPriceWidth,
                            controller: item.goldPriceController,
                            hintText: '金价',
                            keyboardType: TextInputType.number,
                            onChanged: (value) => item.updateAmount(),
                          ),
                          _buildEditableCell(
                            width: silverWeightWidth,
                            controller: item.silverWeightController,
                            hintText: '银重',
                            keyboardType: TextInputType.number,
                            onChanged: (value) => item.updateAmount(),
                          ),
                          _buildEditableCell(
                            width: silverPriceWidth,
                            controller: item.silverPriceController,
                            hintText: '银价',
                            keyboardType: TextInputType.number,
                            onChanged: (value) => item.updateAmount(),
                          ),
                          _buildEditableCell(
                            width: discountWidth,
                            controller: item.discountController,
                            hintText: '折扣',
                            keyboardType: TextInputType.number,
                            onChanged: (value) => item.updateAmount(),
                          ),
                          _buildDataCell(
                            item.amountController.text,
                            amountWidth,
                            true,
                          ),
                          _buildDataCell(
                            item.finalAmountController.text,
                            finalAmountWidth,
                            true,
                          ),
                          _buildEditableCell(
                            width: remarkWidth,
                            controller: item.nameController,
                            hintText: '备注',
                          ),
                          _buildActionCell(index, actionWidth),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 构建数据单元格
  Widget _buildDataCell(String text, double width, bool isAmount) {
    return Container(
      width: width,
      height: 52,
      padding: const EdgeInsets.symmetric(horizontal: 4),
      decoration: const BoxDecoration(
        border: Border(right: BorderSide(color: Colors.grey, width: 1.0)),
      ),
      alignment: Alignment.center,
      child: Text(
        text,
        textAlign: TextAlign.center,
        style: TextStyle(
          fontSize: 13,
          color: isAmount ? Colors.red[600] : Colors.black87,
          fontWeight: isAmount ? FontWeight.w600 : FontWeight.normal,
        ),
        overflow: TextOverflow.ellipsis,
        maxLines: 1,
      ),
    );
  }

  /// 构建可编辑单元格
  Widget _buildEditableCell({
    required double width,
    required TextEditingController controller,
    required String hintText,
    TextInputType? keyboardType,
    Function(String)? onChanged,
  }) {
    return Container(
      width: width,
      height: 52,
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 6),
      decoration: const BoxDecoration(
        border: Border(right: BorderSide(color: Colors.grey, width: 1.0)),
      ),
      child: TextField(
        controller: controller,
        keyboardType: keyboardType,
        style: const TextStyle(fontSize: 13),
        textAlign: TextAlign.center,
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: const TextStyle(fontSize: 12, color: Colors.grey),
          border: InputBorder.none,
          enabledBorder: InputBorder.none,
          focusedBorder: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            vertical: 8,
            horizontal: 4,
          ),
          isDense: true,
        ),
        onChanged: onChanged,
      ),
    );
  }

  /// 构建分类下拉框单元格
  Widget _buildCategoryCell(RecyclingItemForm item, double width) {
    return Container(
      width: width,
      height: 52,
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 6),
      decoration: const BoxDecoration(
        border: Border(right: BorderSide(color: Colors.grey, width: 1.0)),
      ),
      child: DropdownButtonFormField<int>(
        value: item.categoryId,
        decoration: const InputDecoration(
          border: InputBorder.none,
          enabledBorder: InputBorder.none,
          focusedBorder: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(vertical: 8, horizontal: 4),
          isDense: true,
        ),
        style: const TextStyle(fontSize: 13, color: Colors.black87),
        items: const [
          DropdownMenuItem(value: 1, child: Text('AU999')),
          DropdownMenuItem(value: 2, child: Text('AU750')),
          DropdownMenuItem(value: 3, child: Text('AG999')),
          DropdownMenuItem(value: 4, child: Text('金包银')),
        ],
        onChanged: (value) {
          // 使用 addPostFrameCallback 避免在 build 过程中调用 setState
          WidgetsBinding.instance.addPostFrameCallback((_) {
            setState(() {
              item.categoryId = value!;
              // 根据分类设置默认价格
              switch (value) {
                case 1: // AU999
                  item.goldPriceController.text = '400.00';
                  item.silverPriceController.text = '0.00';
                  break;
                case 2: // AU750
                  item.goldPriceController.text = '300.00';
                  item.silverPriceController.text = '0.00';
                  break;
                case 3: // AG999
                  item.goldPriceController.text = '0.00';
                  item.silverPriceController.text = '6.00';
                  break;
                case 4: // 金包银
                  item.goldPriceController.text = '200.00';
                  item.silverPriceController.text = '6.00';
                  break;
              }
              item.updateAmount();
            });
          });
        },
      ),
    );
  }

  /// 构建单位类型下拉框单元格
  Widget _buildUnitTypeCell(RecyclingItemForm item, double width) {
    return Container(
      width: width,
      height: 52,
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 6),
      decoration: const BoxDecoration(
        border: Border(right: BorderSide(color: Colors.grey, width: 1.0)),
      ),
      child: DropdownButtonFormField<String>(
        value: item.type,
        decoration: const InputDecoration(
          border: InputBorder.none,
          enabledBorder: InputBorder.none,
          focusedBorder: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(vertical: 8, horizontal: 4),
          isDense: true,
        ),
        style: const TextStyle(fontSize: 13, color: Colors.black87),
        items: const [
          DropdownMenuItem(value: 'by_weight', child: Text('按克')),
          DropdownMenuItem(value: 'by_piece', child: Text('按件')),
        ],
        onChanged: (value) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            setState(() {
              item.type = value!;
            });
          });
        },
      ),
    );
  }

  /// 构建操作单元格
  Widget _buildActionCell(int index, double width) {
    return Container(
      width: width,
      height: 52,
      padding: const EdgeInsets.symmetric(horizontal: 4),
      alignment: Alignment.center,
      child: IconButton(
        icon: Icon(Icons.delete, color: Colors.red[600], size: 18),
        onPressed: () => _removeItem(index),
        tooltip: '删除',
      ),
    );
  }

  /// 构建表单底部汇总区域
  Widget _buildFormFooter() {
    // 计算总金额和总重量
    double totalFinalAmount = 0.0;
    double totalGoldWeight = 0.0;
    double totalSilverWeight = 0.0;

    for (var item in _items) {
      final goldWeight = double.tryParse(item.goldWeightController.text) ?? 0.0;
      final silverWeight =
          double.tryParse(item.silverWeightController.text) ?? 0.0;
      final finalAmount =
          double.tryParse(item.finalAmountController.text) ?? 0.0;

      totalGoldWeight += goldWeight;
      totalSilverWeight += silverWeight;
      totalFinalAmount += finalAmount;
    }

    final totalWeight = totalGoldWeight + totalSilverWeight;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(top: BorderSide(color: Colors.grey[200]!, width: 1)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            blurRadius: 3,
            offset: const Offset(0, -1),
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(Icons.calculate, color: Colors.blue[600], size: 20),
          const SizedBox(width: 8),
          const Text(
            '汇总信息',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(width: 24),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.amber[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.amber[200]!, width: 1),
            ),
            child: Text(
              '金重: ${totalGoldWeight.toStringAsFixed(2)}g',
              style: TextStyle(
                fontSize: 13,
                color: Colors.amber[700],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey[300]!, width: 1),
            ),
            child: Text(
              '银重: ${totalSilverWeight.toStringAsFixed(2)}g',
              style: TextStyle(
                fontSize: 13,
                color: Colors.grey[700],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue[200]!, width: 1),
            ),
            child: Text(
              '总重: ${totalWeight.toStringAsFixed(2)}g',
              style: TextStyle(
                fontSize: 13,
                color: Colors.blue[700],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.green[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.green[200]!, width: 1),
            ),
            child: Text(
              '应付: ¥${totalFinalAmount.toStringAsFixed(2)}',
              style: TextStyle(
                fontSize: 13,
                color: Colors.green[700],
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// 回收物品表单数据类
class RecyclingItemForm {
  final TextEditingController nameController = TextEditingController();
  final TextEditingController weightController = TextEditingController();
  final TextEditingController priceController = TextEditingController();
  final TextEditingController amountController = TextEditingController();
  final TextEditingController remarkController = TextEditingController();

  // 贵金属回收专用控制器
  final TextEditingController goldWeightController = TextEditingController();
  final TextEditingController goldPriceController = TextEditingController();
  final TextEditingController silverWeightController = TextEditingController();
  final TextEditingController silverPriceController = TextEditingController();
  final TextEditingController discountController = TextEditingController();
  final TextEditingController finalAmountController = TextEditingController();

  int categoryId = 1; // 默认为黄金首饰
  int id = 0; // 用于编辑模式
  String type = 'by_weight'; // 按件/按克类型，默认按克
  String? category; // 贵金属分类

  /// 默认构造函数
  RecyclingItemForm() {
    // 设置默认值
    priceController.text = '400.00'; // 默认黄金首饰价格
    amountController.text = '0.00';
    goldPriceController.text = '400.00'; // 默认金价
    silverPriceController.text = '6.00'; // 默认银价
    discountController.text = '100.00'; // 默认折扣率100%
    finalAmountController.text = '0.00';

    // 监听重量和价格变化，自动计算金额
    weightController.addListener(updateAmount);
    priceController.addListener(updateAmount);
    goldWeightController.addListener(updateAmount);
    goldPriceController.addListener(updateAmount);
    silverWeightController.addListener(updateAmount);
    silverPriceController.addListener(updateAmount);
    discountController.addListener(updateAmount);
  }

  /// 从回收物品构建表单
  RecyclingItemForm.fromItem(RecyclingItem item) {
    id = item.id;
    nameController.text = item.itemName;
    categoryId = item.categoryId;
    weightController.text = item.weight.toString();
    priceController.text = item.price.toString();
    amountController.text = item.amount.toString();
    remarkController.text = item.remark ?? '';

    // 监听重量和价格变化，自动计算金额
    weightController.addListener(updateAmount);
    priceController.addListener(updateAmount);
  }

  /// 更新金额
  void updateAmount() {
    // 获取输入值
    final goldWeight = double.tryParse(goldWeightController.text) ?? 0.0;
    final goldPrice = double.tryParse(goldPriceController.text) ?? 0.0;
    final silverWeight = double.tryParse(silverWeightController.text) ?? 0.0;
    final silverPrice = double.tryParse(silverPriceController.text) ?? 0.0;
    final discountRate = double.tryParse(discountController.text) ?? 100.0;

    // 计算金额：金重 × 金价 + 银重 × 银价
    final amount = goldWeight * goldPrice + silverWeight * silverPrice;
    amountController.text = amount.toStringAsFixed(2);

    // 计算折后金额：金额 × 折扣率(%)
    final finalAmount = amount * (discountRate / 100);
    finalAmountController.text = finalAmount.toStringAsFixed(2);

    // 更新总重量（兼容旧的weight字段）
    final totalWeight = goldWeight + silverWeight;
    weightController.text = totalWeight.toStringAsFixed(2);

    // 更新平均价格（兼容旧的price字段）
    final avgPrice = totalWeight > 0 ? amount / totalWeight : 0.0;
    priceController.text = avgPrice.toStringAsFixed(2);
  }

  /// 获取重量
  double get weight => double.tryParse(weightController.text) ?? 0.0;

  /// 获取价格
  double get price => double.tryParse(priceController.text) ?? 0.0;

  /// 获取金额
  double get amount => double.tryParse(amountController.text) ?? 0.0;

  /// 释放资源
  void dispose() {
    nameController.dispose();
    weightController.dispose();
    priceController.dispose();
    amountController.dispose();
    remarkController.dispose();
    goldWeightController.dispose();
    goldPriceController.dispose();
    silverWeightController.dispose();
    silverPriceController.dispose();
    discountController.dispose();
    finalAmountController.dispose();
  }
}
