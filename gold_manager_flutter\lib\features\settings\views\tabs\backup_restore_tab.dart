import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../controllers/settings_controller.dart';
import '../../models/setting_model.dart';

/// 备份恢复Tab页面
class BackupRestoreTab extends StatelessWidget {
  const BackupRestoreTab({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<SettingsController>();
    
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildBackupActions(context, controller),
          const SizedBox(height: 16),
          Expanded(
            child: _buildBackupList(context, controller),
          ),
        ],
      ),
    );
  }
  
  /// 构建备份操作区
  Widget _buildBackupActions(BuildContext context, SettingsController controller) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '数据备份与恢复',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              '在这里您可以手动创建备份、恢复备份或上传之前导出的备份文件。系统会根据设置自动创建定期备份。',
              style: TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                ElevatedButton.icon(
                  onPressed: () {
                    _showCreateBackupDialog(context, controller);
                  },
                  icon: const Icon(Icons.backup),
                  label: const Text('创建备份'),
                ),
                const SizedBox(width: 16),
                OutlinedButton.icon(
                  onPressed: () {
                    _showMessage(context, '上传备份文件功能即将上线');
                  },
                  icon: const Icon(Icons.upload_file),
                  label: const Text('上传备份文件'),
                ),
                const SizedBox(width: 16),
                OutlinedButton.icon(
                  onPressed: () {
                    controller.loadBackupList();
                  },
                  icon: const Icon(Icons.refresh),
                  label: const Text('刷新'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  /// 构建备份列表
  Widget _buildBackupList(BuildContext context, SettingsController controller) {
    return Obx(() {
      final backups = controller.backupList;
      
      if (backups.isEmpty) {
        return const Center(
          child: Text('没有发现备份记录，请创建一个新备份', style: TextStyle(fontSize: 16)),
        );
      }
      
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '备份记录',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              Expanded(
                child: ListView.separated(
                  itemCount: backups.length,
                  separatorBuilder: (context, index) => const Divider(),
                  itemBuilder: (context, index) {
                    final backup = backups[index];
                    
                    return ListTile(
                      leading: const Icon(Icons.storage),
                      title: Text(backup.fileName),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(backup.description),
                          Text(
                            '${_formatCreationTime(backup.createdAt)} · ${_formatFileSize(backup.fileSize)} · ${backup.username}',
                            style: const TextStyle(fontSize: 12, color: Colors.grey),
                          ),
                        ],
                      ),
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(
                            tooltip: '下载',
                            icon: const Icon(Icons.download, color: Colors.blue),
                            onPressed: () {
                              _downloadBackup(context, controller, backup);
                            },
                          ),
                          IconButton(
                            tooltip: '恢复',
                            icon: const Icon(Icons.restore, color: Colors.orange),
                            onPressed: () {
                              _showRestoreBackupDialog(context, controller, backup);
                            },
                          ),
                          IconButton(
                            tooltip: '删除',
                            icon: const Icon(Icons.delete, color: Colors.red),
                            onPressed: () {
                              _showDeleteBackupDialog(context, controller, backup);
                            },
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      );
    });
  }
  
  /// 显示创建备份对话框
  void _showCreateBackupDialog(BuildContext context, SettingsController controller) {
    final descriptionController = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('创建备份'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('创建一个数据库备份。备份过程可能需要几分钟时间，取决于数据量大小。'),
            const SizedBox(height: 16),
            TextField(
              controller: descriptionController,
              decoration: const InputDecoration(
                labelText: '备份描述',
                hintText: '输入备份说明，便于以后识别',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () async {
              final description = descriptionController.text;
              Navigator.pop(context);
              
              final success = await controller.createBackup(description);
              
              if (success) {
                _showMessage(context, '备份创建成功');
              } else {
                _showMessage(context, '备份创建失败，请稍后重试');
              }
            },
            child: const Text('创建'),
          ),
        ],
      ),
    );
  }
  
  /// 下载备份
  Future<void> _downloadBackup(BuildContext context, SettingsController controller, BackupRecord backup) async {
    _showMessage(context, '正在下载备份文件...');
    
    final filePath = await controller.downloadBackup(backup.id);
    
    if (filePath != null) {
      _showMessage(context, '备份下载成功: $filePath');
    } else {
      _showMessage(context, '备份下载失败，请稍后重试');
    }
  }
  
  /// 显示恢复备份对话框
  void _showRestoreBackupDialog(BuildContext context, SettingsController controller, BackupRecord backup) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('恢复备份'),
        content: Text('确定要从备份"${backup.fileName}"恢复数据吗？\n\n警告：这将覆盖当前的所有数据，此操作不可撤销！'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
            ),
            onPressed: () async {
              Navigator.pop(context);
              
              _showMessage(context, '正在恢复备份...');
              
              final success = await controller.restoreBackup(backup.id);
              
              if (success) {
                _showMessage(context, '备份恢复成功，将在5秒后重启应用');
                
                // 这里应该添加重启应用的逻辑
                Future.delayed(const Duration(seconds: 5), () {
                  // 重启应用
                });
              } else {
                _showMessage(context, '备份恢复失败，请稍后重试');
              }
            },
            child: const Text('确定恢复'),
          ),
        ],
      ),
    );
  }
  
  /// 显示删除备份对话框
  void _showDeleteBackupDialog(BuildContext context, SettingsController controller, BackupRecord backup) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除备份'),
        content: Text('确定要删除备份"${backup.fileName}"吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            onPressed: () async {
              Navigator.pop(context);
              
              final success = await controller.deleteBackup(backup.id);
              
              if (success) {
                _showMessage(context, '备份删除成功');
              } else {
                _showMessage(context, '备份删除失败，请稍后重试');
              }
            },
            child: const Text('确定删除'),
          ),
        ],
      ),
    );
  }
  
  /// 格式化创建时间
  String _formatCreationTime(DateTime dateTime) {
    return DateFormat('yyyy-MM-dd HH:mm').format(dateTime);
  }
  
  /// 格式化文件大小
  String _formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(2)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(2)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(2)} GB';
    }
  }
  
  /// 显示消息提示
  void _showMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
} 