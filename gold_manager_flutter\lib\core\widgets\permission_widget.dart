import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../constants/app_permissions.dart';
import '../../services/auth_service.dart';

/// 权限控制组件
/// 根据用户权限决定是否显示子组件
class PermissionWidget extends StatelessWidget {
  /// 需要的权限
  final String? permission;
  
  /// 需要的权限列表（任一权限）
  final List<String>? anyPermissions;
  
  /// 需要的权限列表（所有权限）
  final List<String>? allPermissions;
  
  /// 有权限时显示的组件
  final Widget child;
  
  /// 无权限时显示的组件（可选）
  final Widget? fallback;
  
  /// 是否在无权限时显示空白
  final bool showEmpty;

  const PermissionWidget({
    super.key,
    this.permission,
    this.anyPermissions,
    this.allPermissions,
    required this.child,
    this.fallback,
    this.showEmpty = true,
  }) : assert(
         (permission != null) || 
         (anyPermissions != null) || 
         (allPermissions != null),
         'Must provide at least one permission check'
       );

  /// 单权限构造函数
  const PermissionWidget.single({
    Key? key,
    required String permission,
    required Widget child,
    Widget? fallback,
    bool showEmpty = true,
  }) : this(
         key: key,
         permission: permission,
         child: child,
         fallback: fallback,
         showEmpty: showEmpty,
       );

  /// 任一权限构造函数
  const PermissionWidget.any({
    Key? key,
    required List<String> permissions,
    required Widget child,
    Widget? fallback,
    bool showEmpty = true,
  }) : this(
         key: key,
         anyPermissions: permissions,
         child: child,
         fallback: fallback,
         showEmpty: showEmpty,
       );

  /// 所有权限构造函数
  const PermissionWidget.all({
    Key? key,
    required List<String> permissions,
    required Widget child,
    Widget? fallback,
    bool showEmpty = true,
  }) : this(
         key: key,
         allPermissions: permissions,
         child: child,
         fallback: fallback,
         showEmpty: showEmpty,
       );

  @override
  Widget build(BuildContext context) {
    final authService = Get.find<AuthService>();
    
    return Obx(() {
      final userPermissions = authService.permissions;
      bool hasRequiredPermission = false;

      // 检查单个权限
      if (permission != null) {
        hasRequiredPermission = AppPermissions.hasPermission(userPermissions, permission!);
      }
      
      // 检查任一权限
      else if (anyPermissions != null) {
        hasRequiredPermission = AppPermissions.hasAnyPermission(userPermissions, anyPermissions!);
      }
      
      // 检查所有权限
      else if (allPermissions != null) {
        hasRequiredPermission = AppPermissions.hasAllPermissions(userPermissions, allPermissions!);
      }

      // 有权限时显示子组件
      if (hasRequiredPermission) {
        return child;
      }

      // 无权限时的处理
      if (fallback != null) {
        return fallback!;
      }

      // 显示空白
      if (showEmpty) {
        return const SizedBox.shrink();
      }

      // 显示无权限提示
      return Container(
        padding: const EdgeInsets.all(8),
        child: const Text(
          '无权限访问',
          style: TextStyle(
            color: Colors.grey,
            fontSize: 12,
          ),
        ),
      );
    });
  }
}

/// 权限按钮组件
/// 根据权限控制按钮的显示和启用状态
class PermissionButton extends StatelessWidget {
  /// 需要的权限
  final String permission;
  
  /// 按钮文本
  final String text;
  
  /// 按钮图标
  final IconData? icon;
  
  /// 点击回调
  final VoidCallback? onPressed;
  
  /// 按钮样式
  final ButtonStyle? style;
  
  /// 是否为主要按钮
  final bool isPrimary;
  
  /// 是否为图标按钮
  final bool isIconButton;
  
  /// 工具提示
  final String? tooltip;

  const PermissionButton({
    super.key,
    required this.permission,
    required this.text,
    this.icon,
    this.onPressed,
    this.style,
    this.isPrimary = false,
    this.isIconButton = false,
    this.tooltip,
  });

  /// 主要按钮构造函数
  const PermissionButton.primary({
    Key? key,
    required String permission,
    required String text,
    IconData? icon,
    VoidCallback? onPressed,
    ButtonStyle? style,
    String? tooltip,
  }) : this(
         key: key,
         permission: permission,
         text: text,
         icon: icon,
         onPressed: onPressed,
         style: style,
         isPrimary: true,
         tooltip: tooltip,
       );

  /// 图标按钮构造函数
  const PermissionButton.icon({
    Key? key,
    required String permission,
    required IconData icon,
    VoidCallback? onPressed,
    String? tooltip,
  }) : this(
         key: key,
         permission: permission,
         text: '',
         icon: icon,
         onPressed: onPressed,
         isIconButton: true,
         tooltip: tooltip,
       );

  @override
  Widget build(BuildContext context) {
    return PermissionWidget.single(
      permission: permission,
      showEmpty: true,
      child: _buildButton(),
    );
  }

  Widget _buildButton() {
    if (isIconButton) {
      return IconButton(
        icon: Icon(icon),
        onPressed: onPressed,
        tooltip: tooltip,
      );
    }

    if (isPrimary) {
      if (icon != null) {
        return ElevatedButton.icon(
          icon: Icon(icon),
          label: Text(text),
          onPressed: onPressed,
          style: style,
        );
      } else {
        return ElevatedButton(
          onPressed: onPressed,
          style: style,
          child: Text(text),
        );
      }
    } else {
      if (icon != null) {
        return OutlinedButton.icon(
          icon: Icon(icon),
          label: Text(text),
          onPressed: onPressed,
          style: style,
        );
      } else {
        return OutlinedButton(
          onPressed: onPressed,
          style: style,
          child: Text(text),
        );
      }
    }
  }
}

/// 权限菜单项组件
/// 根据权限控制菜单项的显示
class PermissionMenuItem extends StatelessWidget {
  /// 需要的权限
  final String permission;
  
  /// 菜单项
  final Widget child;

  const PermissionMenuItem({
    super.key,
    required this.permission,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return PermissionWidget.single(
      permission: permission,
      showEmpty: true,
      child: child,
    );
  }
}

/// 权限路由守卫
/// 检查路由访问权限
class PermissionGuard {
  static final AuthService _authService = Get.find<AuthService>();

  /// 检查路由权限
  static bool checkRoutePermission(String route) {
    final userPermissions = _authService.permissions;
    
    // 路由权限映射
    const Map<String, String> routePermissions = {
      '/jewelry': AppPermissions.jewelryView,
      '/stock': AppPermissions.stockView,
      '/sales': AppPermissions.salesView,
      '/members': AppPermissions.memberView,
      '/stores': AppPermissions.storeView,
      '/admin': AppPermissions.adminView,
      '/settings': AppPermissions.systemSettings,
    };

    final requiredPermission = routePermissions[route];
    if (requiredPermission == null) {
      return true; // 没有权限要求的路由默认允许访问
    }

    return AppPermissions.hasPermission(userPermissions, requiredPermission);
  }

  /// 获取无权限访问的回调页面
  static Widget getNoPermissionPage() {
    return Scaffold(
      appBar: AppBar(
        title: const Text('无权限访问'),
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.lock,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              '您没有权限访问此页面',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 8),
            Text(
              '请联系管理员获取相应权限',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
