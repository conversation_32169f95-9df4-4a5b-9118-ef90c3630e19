import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gold_manager_flutter/core/constants/app_permissions.dart';
import 'package:gold_manager_flutter/core/utils/logger_service.dart';
import 'package:gold_manager_flutter/core/utils/api_response_handler.dart';
import 'package:gold_manager_flutter/models/store/store.dart';
import 'package:gold_manager_flutter/services/auth_service.dart';
import 'package:gold_manager_flutter/services/store_service.dart';
import '../models/sales_item_detail.dart';
import '../models/sales_statistics.dart';
import '../services/sales_service.dart';

/// 销售管理控制器
class SalesController extends GetxController {
  // 服务
  final SalesService _salesService = Get.find<SalesService>();
  final StoreService _storeService = Get.find<StoreService>();
  final AuthService _authService = Get.find<AuthService>();

  // 状态变量
  final RxBool isLoading = false.obs;
  final RxBool isLoadingMore = false.obs;
  final RxList<SalesItemDetail> salesItemList = <SalesItemDetail>[].obs;
  final RxList<Store> storeList = <Store>[].obs;
  final Rx<SalesStatistics> statistics = SalesStatistics.empty().obs;

  // 筛选条件
  final TextEditingController searchController = TextEditingController();
  final Rx<DateTime?> startDate = Rx<DateTime?>(null);
  final Rx<DateTime?> endDate = Rx<DateTime?>(null);
  final RxInt selectedStoreId = 0.obs;
  final Rx<SalesType> selectedSalesType = SalesType.all.obs; // 默认选择全部

  // 分页
  final RxInt currentPage = 1.obs;
  final RxInt totalPages = 1.obs;
  final RxInt totalCount = 0.obs;
  final RxBool hasMore = true.obs;
  final int pageSize = 20;

  // 颜色区分映射（为同一出库单的商品分配相同颜色）
  final RxMap<String, int> orderColorMap = <String, int>{}.obs;
  final List<Color> orderColors = [
    Colors.blue.shade50,
    Colors.green.shade50,
    Colors.orange.shade50,
    Colors.purple.shade50,
    Colors.teal.shade50,
  ];

  // 兼容性属性（为视图提供别名）
  List<SalesItemDetail> get salesItems => salesItemList;
  List<Store> get stores => storeList;
  Store? get selectedStore => storeList.firstWhereOrNull((store) => store.id == selectedStoreId.value);
  List<SalesType> get salesTypes => SalesType.allTypes;
  double get todaySales => statistics.value.todayAmount;
  double get monthSales => statistics.value.monthAmount;
  int get totalOrders => statistics.value.totalOrders;

  @override
  void onInit() {
    super.onInit();
    LoggerService.d('SalesController 初始化');

    // 延迟初始化，确保权限已加载
    Future.delayed(const Duration(milliseconds: 100), () {
      _checkPermissions();
      _initializeData();
    });
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }

  /// 检查权限
  void _checkPermissions() {
    LoggerService.i('=== 销售管理权限调试信息 ===');
    LoggerService.i('用户名: ${_authService.userName.value}');
    LoggerService.i('用户角色: ${_authService.userRole.value}');
    LoggerService.i('销售查看权限: ${_authService.hasPermission(AppPermissions.salesView)}');
    LoggerService.i('门店查看权限: ${_authService.hasPermission(AppPermissions.storeView)}');
    LoggerService.i('=== 权限调试信息结束 ===');
  }

  /// 初始化数据
  Future<void> _initializeData() async {
    try {
      if (_authService.hasPermission(AppPermissions.storeView)) {
        await fetchStores();
      } else {
        LoggerService.w('用户没有门店查看权限，跳过获取门店列表');
      }

      if (_authService.hasPermission(AppPermissions.salesView)) {
        await fetchSalesData();
        // 确保初始化完成后统计信息正确
        _calculateStatistics();
      } else {
        LoggerService.w('用户没有销售查看权限，跳过获取销售数据');
        Get.snackbar(
          '权限不足',
          '您没有销售查看权限',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      LoggerService.e('初始化销售数据失败', e);
      Get.snackbar(
        '初始化失败',
        '销售数据初始化失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 获取门店列表
  Future<void> fetchStores() async {
    try {
      final stores = await _storeService.getAllStores();
      storeList.value = stores;

      // 如果用户不是管理员，自动选择用户所属门店
      if (_authService.userRole.value != 'admin') {
        selectedStoreId.value = _authService.storeId.value;
      }
    } catch (e) {
      LoggerService.e('获取门店列表失败', e);
      Get.snackbar(
        '错误',
        '获取门店列表失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 获取销售数据（包括明细和统计）
  Future<void> fetchSalesData({bool refresh = false}) async {
    if (refresh) {
      currentPage.value = 1;
      hasMore.value = true;
      orderColorMap.clear();
    }

    try {
      if (refresh) {
        isLoading.value = true;
      } else {
        isLoadingMore.value = true;
      }

      // 构建筛选参数
      final filters = _buildFilterParams();

      // 分别获取明细和统计数据，避免一个失败影响另一个
      List<SalesItemDetail> newItems = [];
      SalesStatistics newStatistics = SalesStatistics.empty();

      // 获取销售明细数据（带分页信息）
      try {
        final paginationResult = await _salesService.getSalesItemListWithPagination(
          page: currentPage.value,
          limit: pageSize,
          salesType: selectedSalesType.value.value,
          startDate: startDate.value,
          endDate: endDate.value,
          storeId: selectedStoreId.value > 0 ? selectedStoreId.value : null,
          search: searchController.text.isNotEmpty ? searchController.text : null,
          additionalFilters: filters,
        );
        
        newItems = paginationResult['items'] ?? [];
        totalCount.value = paginationResult['total_count'] ?? 0;
        totalPages.value = paginationResult['total_pages'] ?? 1;
        
        LoggerService.d('API返回分页信息：总数=${totalCount.value}, 总页数=${totalPages.value}, 当前页=${currentPage.value}, 返回条数=${newItems.length}');
        
        // 如果API没有返回正确的分页信息，我们基于数据长度进行推算
        if (newItems.isNotEmpty && totalPages.value <= 1) {
          // 如果返回的数据等于pageSize，说明可能还有更多页
          if (newItems.length == pageSize) {
            totalPages.value = currentPage.value + 1; // 至少还有下一页
            LoggerService.w('API未返回正确分页信息，基于数据量推算至少有${totalPages.value}页');
          } else {
            totalPages.value = currentPage.value; // 这是最后一页
            LoggerService.d('推算这是最后一页：${totalPages.value}');
          }
        }
        
        LoggerService.d('获取销售明细成功: ${newItems.length} 条，共 ${totalCount.value} 条，总页数: ${totalPages.value}');
      } catch (e) {
        LoggerService.e('获取销售明细失败', e);
        // 明细获取失败时显示错误，但不阻止统计数据获取
        if (refresh) {
          Get.snackbar(
            '警告',
            '获取销售明细失败: ${_getErrorMessage(e)}',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.orange,
            colorText: Colors.white,
            duration: const Duration(seconds: 3),
          );
        }
      }

      // 获取销售统计数据
      try {
        newStatistics = await _salesService.getSalesStatistics(
          salesType: selectedSalesType.value.value,
          startDate: startDate.value,
          endDate: endDate.value,
          storeId: selectedStoreId.value > 0 ? selectedStoreId.value : null,
          search: searchController.text.isNotEmpty ? searchController.text : null,
          additionalFilters: filters,
        );
        LoggerService.d('获取销售统计成功: 总计 ${newStatistics.totalItems} 件');
      } catch (e) {
        LoggerService.e('获取销售统计失败', e);
        // 统计获取失败时显示错误，但使用空统计数据
        if (refresh) {
          Get.snackbar(
            '警告',
            '获取销售统计失败: ${_getErrorMessage(e)}',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.orange,
            colorText: Colors.white,
            duration: const Duration(seconds: 3),
          );
        }
      }

      // 更新统计数据
      statistics.value = newStatistics;

      // 处理明细数据（分页模式总是替换数据）
      salesItemList.clear(); // 分页切换时总是清空重新加载

      if (newItems.isNotEmpty) {
        // 为新的出库单分配颜色
        _assignColorsToOrders(newItems);
        salesItemList.addAll(newItems);
      }

      // 更新分页状态
      hasMore.value = currentPage.value < totalPages.value;

      // 确保统计信息正确计算
      _calculateStatistics();

      LoggerService.d('销售数据更新完成: ${salesItemList.length} 条明细, 第${currentPage.value}页/${totalPages.value}页, 统计: ${newStatistics.totalItems} 件');

    } catch (e) {
      LoggerService.e('获取销售数据过程中发生未预期错误', e);
      Get.snackbar(
        '错误',
        '获取销售数据失败: ${_getErrorMessage(e)}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        duration: const Duration(seconds: 5),
      );
    } finally {
      isLoading.value = false;
      isLoadingMore.value = false;
    }
  }

  /// 提取错误信息的辅助方法
  String _getErrorMessage(dynamic error) {
    return ApiResponseHandler.getNetworkErrorMessage(error);
  }

  /// 构建筛选参数
  Map<String, dynamic> _buildFilterParams() {
    final Map<String, dynamic> filters = {};
    
    // 这里可以添加更多的筛选条件
    // 例如：利润范围、商品分类等
    
    return filters;
  }

  /// 为出库单分配颜色
  void _assignColorsToOrders(List<SalesItemDetail> items) {
    for (final item in items) {
      if (!orderColorMap.containsKey(item.orderNo)) {
        final colorIndex = orderColorMap.length % orderColors.length;
        orderColorMap[item.orderNo] = colorIndex;
      }
    }
  }

  /// 获取出库单的背景颜色
  Color getOrderBackgroundColor(String orderNo) {
    final colorIndex = orderColorMap[orderNo] ?? 0;
    return orderColors[colorIndex % orderColors.length];
  }

  /// 设置销售类型筛选
  void setSalesTypeFilter(SalesType salesType) {
    selectedSalesType.value = salesType;
    applyFilters();
  }

  /// 设置门店筛选
  void setStoreFilter(int storeId) {
    selectedStoreId.value = storeId;
    applyFilters();
  }

  /// 设置时间范围筛选
  void setDateRangeFilter(DateTime? start, DateTime? end) {
    startDate.value = start;
    endDate.value = end;
  }

  /// 搜索
  void search() {
    applyFilters();
  }

  /// 搜索销售项目（兼容性方法）
  void searchSalesItems(String query) {
    searchController.text = query;
    search();
  }

  /// 重置筛选条件
  void resetFilters() {
    searchController.clear();
    startDate.value = null;
    endDate.value = null;
    selectedStoreId.value = 0;
    selectedSalesType.value = SalesType.all; // 重置为默认全部类型
    applyFilters();
  }

  /// 应用筛选条件
  Future<void> applyFilters() async {
    await fetchSalesData(refresh: true);
  }

  /// 查看单据详情
  Future<void> viewOrderDetail(String orderNo) async {
    try {
      isLoading.value = true;
      final orderItems = await _salesService.getOrderItemDetails(orderNo);
      
      // 显示单据详情对话框
      Get.dialog(
        AlertDialog(
          title: Text('单据详情 - $orderNo'),
          content: SizedBox(
            width: double.maxFinite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('共 ${orderItems.length} 件商品'),
                const SizedBox(height: 10),
                Expanded(
                  child: ListView.builder(
                    shrinkWrap: true,
                    itemCount: orderItems.length,
                    itemBuilder: (context, index) {
                      final item = orderItems[index];
                      return ListTile(
                        title: Text(item.itemName),
                        subtitle: Text('条码: ${item.itemBarcode}'),
                        trailing: Text('¥${item.salePrice.toStringAsFixed(2)}'),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('关闭'),
            ),
          ],
        ),
      );
    } catch (e) {
      LoggerService.e('查看单据详情失败', e);
      Get.snackbar(
        '错误',
        '查看单据详情失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// 导出销售数据
  Future<void> exportData() async {
    try {
      isLoading.value = true;
      
      final downloadUrl = await _salesService.exportSalesData(
        salesType: selectedSalesType.value.value,
        startDate: startDate.value,
        endDate: endDate.value,
        storeId: selectedStoreId.value > 0 ? selectedStoreId.value : null,
        search: searchController.text.isNotEmpty ? searchController.text : null,
      );

      if (downloadUrl.isNotEmpty) {
        Get.snackbar(
          '成功',
          '数据导出成功，正在下载...',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
        // 这里可以集成文件下载功能
      } else {
        throw Exception('导出链接为空');
      }
    } catch (e) {
      LoggerService.e('导出销售数据失败', e);
      Get.snackbar(
        '错误',
        '导出数据失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// 刷新数据
  Future<void> refreshData() async {
    await fetchSalesData(refresh: true);
  }

  /// 加载更多数据
  Future<void> loadMore() async {
    if (!isLoadingMore.value && hasMore.value) {
      await fetchSalesData();
    }
  }

  /// 跳转到指定页面
  Future<void> goToPage(int page) async {
    LoggerService.d('尝试跳转到第 $page 页，当前页: ${currentPage.value}，总页数: ${totalPages.value}');
    if (page >= 1 && page <= totalPages.value && page != currentPage.value) {
      LoggerService.d('页面跳转条件满足，开始跳转到第 $page 页');
      currentPage.value = page;
      // 注意：不使用refresh=true，避免重置页码
      await fetchSalesData(refresh: false);
    } else {
      LoggerService.w('页面跳转条件不满足：page=$page, currentPage=${currentPage.value}, totalPages=${totalPages.value}');
    }
  }

  /// 上一页
  Future<void> previousPage() async {
    if (currentPage.value > 1) {
      await goToPage(currentPage.value - 1);
    }
  }

  /// 下一页
  Future<void> nextPage() async {
    if (currentPage.value < totalPages.value) {
      await goToPage(currentPage.value + 1);
    }
  }

  /// 第一页
  Future<void> firstPage() async {
    await goToPage(1);
  }

  /// 最后一页
  Future<void> lastPage() async {
    await goToPage(totalPages.value);
  }

  /// 计算统计信息
  /// 基于当前销售数据计算统计信息
  void _calculateStatistics() {
    LoggerService.d('🔄 开始计算销售统计信息...');
    
    // 基于当前销售数据计算统计
    int totalItems = salesItemList.length;
    double totalSalesAmount = salesItemList.fold(0, (sum, item) => sum + item.totalAmount);
    double totalCostAmount = salesItemList.fold(0, (sum, item) => sum + item.costPrice);
    double totalProfit = totalSalesAmount - totalCostAmount;
    double profitRate = totalCostAmount > 0 ? (totalProfit / totalCostAmount * 100) : 0.0;
    
    // 更新统计信息
    statistics.value = statistics.value.copyWith(
      totalItems: totalItems,
      totalSalesAmount: totalSalesAmount,
      totalCostAmount: totalCostAmount,
      totalProfit: totalProfit,
      profitRate: profitRate,
    );
    
    LoggerService.d('📊 销售统计计算完成: 总计=$totalItems件, 销售额=¥${totalSalesAmount.toStringAsFixed(2)}, 利润=¥${totalProfit.toStringAsFixed(2)}, 利润率=${profitRate.toStringAsFixed(1)}%');
  }
}