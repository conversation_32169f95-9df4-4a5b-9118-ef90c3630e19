import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:hive_flutter/hive_flutter.dart';

import '../utils/logger_service.dart';
import '../../services/auth_service.dart';
import 'api_client.dart';
import 'storage_service.dart';
import '../../services/stock_service.dart';
import '../../services/store_service.dart';
import '../../features/jewelry/services/jewelry_service.dart';
import '../../features/recycling/services/category_service.dart';

/// 服务初始化管理器
/// 负责在应用启动时初始化各种服务
class ServicesInitializer {

  /// 初始化所有服务
  static Future<void> init() async {
    try {
      LoggerService.i('开始初始化服务...');

      // 初始化本地存储
      await _initStorage();

      // 注册服务
      await _registerServices();

      LoggerService.i('所有服务初始化完成');
    } catch (e, stackTrace) {
      LoggerService.e('服务初始化失败', e, stackTrace);
      rethrow;
    }
  }

  /// 初始化本地存储
  static Future<void> _initStorage() async {
    try {
      // 初始化SharedPreferences
      final prefs = await SharedPreferences.getInstance();

      // 创建存储服务实例
      final storageService = StorageService();
      // 手动设置SharedPreferences实例
      storageService.setPrefs(prefs);

      // 注册存储服务
      Get.put(storageService, permanent: true);
      LoggerService.i('StorageService initialized');

      // 初始化Hive
      await Hive.initFlutter();

      // 注册Hive适配器 (如需要)
      // Hive.registerAdapter(UserAdapter());
      // Hive.registerAdapter(JewelryAdapter());

      // 打开需要的Box
      // await Hive.openBox('settings');
    } catch (e) {
      LoggerService.e('初始化存储服务失败', e);
      rethrow;
    }
  }

  /// 注册所有服务为单例
  static Future<void> _registerServices() async {
    try {
      // API客户端服务
      final apiClient = ApiClient();
      Get.put(apiClient, permanent: true);
      LoggerService.i('ApiClient initialized');

      // 认证服务 - 确保在StorageService之后初始化
      final authService = AuthService();
      Get.put(authService, permanent: true);
      LoggerService.i('AuthService initialized');

      // 库存服务
      Get.put(StockService(), permanent: true);
      LoggerService.i('StockService initialized');

      // 门店服务
      Get.put(StoreService(), permanent: true);
      LoggerService.i('StoreService initialized');

      // 首饰服务
      Get.put(JewelryService(), permanent: true);
      LoggerService.i('JewelryService initialized');

      // 旧料分类服务
      Get.put(CategoryService(), permanent: true);
      LoggerService.i('CategoryService initialized');

      // 其他服务可以在这里注册
    } catch (e) {
      LoggerService.e('注册服务失败', e);
      rethrow;
    }
  }
}