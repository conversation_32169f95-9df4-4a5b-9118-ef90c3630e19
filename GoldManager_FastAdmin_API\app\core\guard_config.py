"""
FastAPI-Guard 安全配置
提供高级安全防护功能
"""

from guard import SecurityConfig
from typing import List, Dict, Any
import os
from .config import settings


def get_guard_config() -> SecurityConfig:
    """获取FastAPI-Guard配置"""

    # 开发环境判断
    is_development = settings.ENVIRONMENT.lower() in ["development", "dev", "local"]
    
    # 创建配置对象
    config = SecurityConfig(
        # 基础配置
        passive_mode=False,
        
        # IP白名单 - 开发和测试环境
        whitelist=[
            "127.0.0.1",
            "::1",
            # 内网IP段
            "***********/16",
            "10.0.0.0/8",
            "**********/12",
        ],

        # IP黑名单 - 已知恶意IP
        blacklist=[
            # 这里可以添加已知的恶意IP
        ],

        # 国家/地区白名单 - 暂时禁用，需要配置 geo_ip_handler
        whitelist_countries=[],

        # 用户代理检测 - 阻止已知的攻击工具（开发环境放宽限制）
        blocked_user_agents=[] if is_development else [
            r"(?i)(sqlmap|nmap|nikto|dirb|dirbuster|gobuster|wpscan)",
            r"(?i)(burp|owasp|zap|w3af|metasploit)",
            r"(?i)(python-requests|curl|wget)",  # 可以根据需要调整
            r"(?i)(bot|crawler|spider|scraper)",
            r"^$",  # 空User-Agent
        ],

        # 速率限制配置
        enable_rate_limiting=True,
        rate_limit=200 if is_development else 60,  # 开发环境放宽限制
        rate_limit_window=60,  # 时间窗口60秒

        # 自动封禁配置
        enable_ip_banning=True,
        auto_ban_threshold=500 if is_development else 100,  # 开发环境放宽限制
        auto_ban_duration=1800,  # 封禁30分钟

        # 日志配置
        custom_log_file="logs/security.log",
        log_suspicious_level="WARNING",
        log_request_level=None,

        # HTTPS强制（暂时禁用，解决连接问题）
        enforce_https=False,

        # 排除路径
        exclude_paths=[
            "/docs",
            "/redoc", 
            "/openapi.json",
            "/openapi.yaml",
            "/api/v1/openapi.json",  # API文档路径
            "/favicon.ico",
            "/static",
            "/health",
            "/metrics",
        ],

        # 自定义错误响应
        custom_error_responses={
            403: "访问被拒绝：检测到可疑活动",
            429: "请求过于频繁，请稍后再试",
        },

        # 渗透测试检测（开发环境暂时禁用）
        enable_penetration_detection=not is_development,

        # Redis配置（可选）
        enable_redis=False,  # 简化配置，不使用Redis
        
        # 代理配置
        trusted_proxies=[],
        trust_x_forwarded_proto=False,
    )

    return config


# 导出配置实例
guard_config = get_guard_config()