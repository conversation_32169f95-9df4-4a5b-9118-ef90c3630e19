import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../theme/app_theme.dart';

/// 标准输入框组件
/// 遵循UI设计规范：圆角12px，灰色边框，聚焦时主色调边框
class StandardTextField extends StatelessWidget {
  /// 控制器
  final TextEditingController? controller;
  
  /// 标签文本
  final String? label;
  
  /// 提示文本
  final String? hintText;
  
  /// 帮助文本
  final String? helperText;
  
  /// 错误文本
  final String? errorText;
  
  /// 前缀图标
  final IconData? prefixIcon;
  
  /// 后缀图标
  final IconData? suffixIcon;
  
  /// 后缀图标点击事件
  final VoidCallback? onSuffixIconPressed;
  
  /// 是否为密码输入框
  final bool obscureText;
  
  /// 是否只读
  final bool readOnly;
  
  /// 是否启用
  final bool enabled;
  
  /// 最大行数
  final int? maxLines;
  
  /// 最小行数
  final int? minLines;
  
  /// 最大长度
  final int? maxLength;
  
  /// 键盘类型
  final TextInputType? keyboardType;
  
  /// 输入格式化器
  final List<TextInputFormatter>? inputFormatters;
  
  /// 验证器
  final String? Function(String?)? validator;
  
  /// 输入变化回调
  final ValueChanged<String>? onChanged;
  
  /// 提交回调
  final ValueChanged<String>? onSubmitted;
  
  /// 点击回调
  final VoidCallback? onTap;
  
  /// 文本输入动作
  final TextInputAction? textInputAction;
  
  /// 自动聚焦
  final bool autofocus;

  const StandardTextField({
    super.key,
    this.controller,
    this.label,
    this.hintText,
    this.helperText,
    this.errorText,
    this.prefixIcon,
    this.suffixIcon,
    this.onSuffixIconPressed,
    this.obscureText = false,
    this.readOnly = false,
    this.enabled = true,
    this.maxLines = 1,
    this.minLines,
    this.maxLength,
    this.keyboardType,
    this.inputFormatters,
    this.validator,
    this.onChanged,
    this.onSubmitted,
    this.onTap,
    this.textInputAction,
    this.autofocus = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标签
        if (label != null) ...[
          Padding(
            padding: const EdgeInsets.only(
              left: AppTheme.paddingSmall,
              bottom: AppTheme.paddingSmall,
            ),
            child: Text(
              label!,
              style: const TextStyle(
                fontSize: AppTheme.captionSize,
                fontWeight: FontWeight.w500,
                color: AppTheme.primaryTextColor,
              ),
            ),
          ),
        ],
        
        // 输入框
        TextFormField(
          controller: controller,
          obscureText: obscureText,
          readOnly: readOnly,
          enabled: enabled,
          maxLines: maxLines,
          minLines: minLines,
          maxLength: maxLength,
          keyboardType: keyboardType,
          inputFormatters: inputFormatters,
          validator: validator,
          onChanged: onChanged,
          onFieldSubmitted: onSubmitted,
          onTap: onTap,
          textInputAction: textInputAction,
          autofocus: autofocus,
          style: const TextStyle(
            fontSize: AppTheme.bodySize,
            color: AppTheme.primaryTextColor,
          ),
          decoration: InputDecoration(
            hintText: hintText,
            helperText: helperText,
            errorText: errorText,
            prefixIcon: prefixIcon != null
                ? Icon(
                    prefixIcon,
                    color: AppTheme.secondaryTextColor,
                    size: 20,
                  )
                : null,
            suffixIcon: suffixIcon != null
                ? IconButton(
                    icon: Icon(
                      suffixIcon,
                      color: AppTheme.secondaryTextColor,
                      size: 20,
                    ),
                    onPressed: onSuffixIconPressed,
                  )
                : null,
            filled: true,
            fillColor: enabled 
                ? AppTheme.cardBackgroundColor 
                : AppTheme.backgroundColor,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: AppTheme.padding,
              vertical: AppTheme.paddingMedium,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppTheme.borderRadius),
              borderSide: const BorderSide(
                color: AppTheme.disabledTextColor,
                width: 1,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppTheme.borderRadius),
              borderSide: const BorderSide(
                color: AppTheme.disabledTextColor,
                width: 1,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppTheme.borderRadius),
              borderSide: const BorderSide(
                color: AppTheme.primaryColor,
                width: 2,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppTheme.borderRadius),
              borderSide: const BorderSide(
                color: AppTheme.errorColor,
                width: 1,
              ),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppTheme.borderRadius),
              borderSide: const BorderSide(
                color: AppTheme.errorColor,
                width: 2,
              ),
            ),
            disabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppTheme.borderRadius),
              borderSide: BorderSide(
                color: AppTheme.disabledTextColor.withValues(alpha: 0.5),
                width: 1,
              ),
            ),
            hintStyle: const TextStyle(
              color: AppTheme.disabledTextColor,
              fontSize: AppTheme.bodySize,
            ),
            helperStyle: const TextStyle(
              color: AppTheme.secondaryTextColor,
              fontSize: AppTheme.smallSize,
            ),
            errorStyle: const TextStyle(
              color: AppTheme.errorColor,
              fontSize: AppTheme.smallSize,
            ),
            counterStyle: const TextStyle(
              color: AppTheme.secondaryTextColor,
              fontSize: AppTheme.smallSize,
            ),
          ),
        ),
      ],
    );
  }
}

/// 搜索输入框组件
class SearchTextField extends StatelessWidget {
  /// 控制器
  final TextEditingController? controller;
  
  /// 提示文本
  final String? hintText;
  
  /// 搜索回调
  final ValueChanged<String>? onSearch;
  
  /// 输入变化回调
  final ValueChanged<String>? onChanged;
  
  /// 清除回调
  final VoidCallback? onClear;
  
  /// 是否自动聚焦
  final bool autofocus;

  const SearchTextField({
    super.key,
    this.controller,
    this.hintText,
    this.onSearch,
    this.onChanged,
    this.onClear,
    this.autofocus = false,
  });

  @override
  Widget build(BuildContext context) {
    return StandardTextField(
      controller: controller,
      hintText: hintText ?? '搜索...',
      prefixIcon: Icons.search,
      suffixIcon: controller?.text.isNotEmpty == true ? Icons.clear : null,
      onSuffixIconPressed: () {
        controller?.clear();
        onClear?.call();
      },
      onChanged: onChanged,
      onSubmitted: onSearch,
      textInputAction: TextInputAction.search,
      autofocus: autofocus,
    );
  }
}

/// 密码输入框组件
class PasswordTextField extends StatefulWidget {
  /// 控制器
  final TextEditingController? controller;
  
  /// 标签文本
  final String? label;
  
  /// 提示文本
  final String? hintText;
  
  /// 错误文本
  final String? errorText;
  
  /// 验证器
  final String? Function(String?)? validator;
  
  /// 输入变化回调
  final ValueChanged<String>? onChanged;
  
  /// 提交回调
  final ValueChanged<String>? onSubmitted;
  
  /// 文本输入动作
  final TextInputAction? textInputAction;

  const PasswordTextField({
    super.key,
    this.controller,
    this.label,
    this.hintText,
    this.errorText,
    this.validator,
    this.onChanged,
    this.onSubmitted,
    this.textInputAction,
  });

  @override
  State<PasswordTextField> createState() => _PasswordTextFieldState();
}

class _PasswordTextFieldState extends State<PasswordTextField> {
  bool _obscureText = true;

  void _toggleVisibility() {
    setState(() {
      _obscureText = !_obscureText;
    });
  }

  @override
  Widget build(BuildContext context) {
    return StandardTextField(
      controller: widget.controller,
      label: widget.label,
      hintText: widget.hintText,
      errorText: widget.errorText,
      prefixIcon: Icons.lock,
      suffixIcon: _obscureText ? Icons.visibility : Icons.visibility_off,
      onSuffixIconPressed: _toggleVisibility,
      obscureText: _obscureText,
      validator: widget.validator,
      onChanged: widget.onChanged,
      onSubmitted: widget.onSubmitted,
      textInputAction: widget.textInputAction,
    );
  }
}
