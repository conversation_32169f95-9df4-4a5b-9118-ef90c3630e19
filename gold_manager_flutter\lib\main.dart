import 'dart:io' show Platform;
import 'package:flutter/foundation.dart' show kIsWeb;

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:window_manager/window_manager.dart';

import 'services/stock_service.dart';
import 'services/store_service.dart';
import 'services/sales_order_service.dart';
import 'services/sales_return_service.dart';
import 'services/auth_service.dart';
import 'services/draft_service.dart';
import 'services/batch_import_service.dart';
import 'services/window_service.dart';
import 'services/jewelry_service.dart';
import 'services/category_service.dart';
import 'services/print_template_service.dart';
import 'services/excel_service.dart';
import 'features/stock/services/store_transfer_service.dart';
// 移除旧版本的inventory_check_service导入，避免依赖注入冲突
import 'core/utils/logger.dart';
import 'core/services/audio_service.dart';

import 'core/services/api_client.dart';
import 'core/services/api_service.dart';
import 'core/services/storage_service.dart';
import 'core/utils/api_provider.dart';
import 'core/routes/app_pages.dart';
import 'core/theme/app_theme.dart';
import 'core/translations/app_translations.dart';
// 导入控制器
import 'features/stock/controllers/stock_in_controller.dart';
import 'features/sales/controllers/sales_return_controller.dart';
import 'features/recycling/controllers/recycling_controller.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 🎯 全新架构解决方案：彻底重新设计窗口管理策略
  if (!kIsWeb && Platform.isWindows) {
    await _initializeWindowsUltimate();
  }

  // 初始化服务
  await _initServices();

  // 🔑 关键：延迟runApp，确保窗口状态完全稳定
  if (!kIsWeb && Platform.isWindows) {
    await _runAppWithWindowStabilization();
  } else {
    runApp(const MyApp());
  }
}

/// 🎯 全新架构：终极窗口管理解决方案
///
/// 核心策略：
/// 1. 完全隐藏窗口直到最大化完成
/// 2. 使用强制锁定机制防止任何重置
/// 3. 实时监控并立即纠正任何窗口变化
/// 4. 延迟runApp直到窗口状态完全稳定
Future<void> _initializeWindowsUltimate() async {
  try {
    print('🎯 启动终极窗口管理解决方案...');

    // 1. 基础初始化
    await windowManager.ensureInitialized();
    print('✅ 窗口管理器初始化完成');

    // 2. 🔑 关键：完全隐藏窗口，避免用户看到任何闪烁
    const windowOptions = WindowOptions(
      skipTaskbar: false,
      titleBarStyle: TitleBarStyle.normal,
      minimumSize: Size(800, 600),
      alwaysOnTop: false,
      fullScreen: false,
      // 🔑 不显示窗口，避免闪烁
    );

    // 3. 准备窗口但不显示
    await windowManager.waitUntilReadyToShow(windowOptions, () async {
      // 不调用show()，保持隐藏状态
      print('📱 窗口准备完成（隐藏状态）...');
    });

    // 4. 设置窗口功能属性
    await windowManager.setResizable(true);
    await windowManager.setMaximizable(true);
    await windowManager.setMinimizable(true);
    await windowManager.setClosable(true);

    // 5. 🔑 关键：简化窗口设置，减少操作步骤
    print('🚀 设置窗口为全屏尺寸...');

    // 直接设置为标准全屏尺寸，避免复杂的检测过程
    await windowManager.setSize(const Size(1920, 1080));
    await windowManager.setPosition(const Offset(0, 0));

    // 6. 短暂延迟后显示窗口
    await Future.delayed(const Duration(milliseconds: 100));
    print('📱 显示窗口...');
    await windowManager.show();
    await windowManager.focus();

    // 8. 最终验证
    final finalSize = await windowManager.getSize();
    final finalMaximized = await windowManager.isMaximized();
    print(
      '✅ 最终窗口状态: ${finalSize.width}x${finalSize.height}, 最大化: $finalMaximized',
    );

    print('🎯 终极窗口管理解决方案完成');
  } catch (e) {
    print('❌ 终极窗口管理失败: $e');
    // 降级到基本窗口显示
    await windowManager.show();
  }
}

/// 🎯 延迟runApp并实施窗口稳定化机制
///
/// 核心策略：
/// 1. 添加实时窗口监控
/// 2. 延迟runApp直到窗口状态稳定
/// 3. runApp后立即锁定窗口状态
Future<void> _runAppWithWindowStabilization() async {
  try {
    print('🎯 开始窗口稳定化流程...');

    // 1. 添加窗口事件监听器，实时监控窗口变化
    final windowListener = _UltimateWindowListener();
    windowManager.addListener(windowListener);
    print('✅ 窗口监听器已启动');

    // 2. 确保当前窗口状态稳定
    await _ensureWindowStable();

    // 3. 延迟一段时间确保系统稳定
    await Future.delayed(const Duration(milliseconds: 500));
    print('🚀 启动Flutter应用...');

    // 4. 运行应用
    runApp(const MyApp());

    // 5. runApp后立即检查并修复窗口状态
    await _postRunAppWindowFix();

    print('✅ 窗口稳定化流程完成');
  } catch (e) {
    print('❌ 窗口稳定化失败: $e');
    // 降级方案
    runApp(const MyApp());
  }
}

/// 确保窗口状态稳定
Future<void> _ensureWindowStable() async {
  for (int i = 0; i < 3; i++) {
    final isMaximized = await windowManager.isMaximized();
    final size = await windowManager.getSize();

    print(
      '🔍 稳定性检查 ${i + 1}/3: ${size.width}x${size.height}, 最大化: $isMaximized',
    );

    if (!isMaximized) {
      print('⚠️ 检测到窗口未最大化，立即修复...');
      await windowManager.maximize();
      await Future.delayed(const Duration(milliseconds: 300));
    } else {
      break; // 窗口状态稳定
    }
  }
}

/// runApp后的窗口修复机制
Future<void> _postRunAppWindowFix() async {
  // 等待MaterialApp初始化
  await Future.delayed(const Duration(milliseconds: 800));

  final postSize = await windowManager.getSize();
  final postMaximized = await windowManager.isMaximized();
  print(
    '📊 runApp后状态: ${postSize.width}x${postSize.height}, 最大化: $postMaximized',
  );

  if (!postMaximized || postSize.width < 1900) {
    print('🔧 检测到runApp导致窗口重置，立即修复...');
    await windowManager.maximize();

    // 强制等待并验证
    await Future.delayed(const Duration(milliseconds: 300));
    final fixedSize = await windowManager.getSize();
    final fixedMaximized = await windowManager.isMaximized();
    print(
      '✅ 修复后状态: ${fixedSize.width}x${fixedSize.height}, 最大化: $fixedMaximized',
    );
  }
}

/// 初始化所有必要的服务
Future<void> _initServices() async {
  try {
    LoggerService.i('开始初始化服务...');

    // 注册存储服务（必须最先注册）
    final storageService = StorageService();
    await storageService.onInit();
    Get.put(storageService);
    LoggerService.i('StorageService initialized');

    // 注册API Service（核心API服务）
    Get.put(ApiService(), permanent: true);
    LoggerService.i('ApiService initialized');

    // 注册API Provider（用于兼容旧代码）
    Get.put(ApiProvider());
    LoggerService.i('ApiProvider initialized');

    // 注册API Client（依赖StorageService）
    Get.put(ApiClient());
    LoggerService.i('ApiClient initialized');

    // 重新启用窗口服务，但添加调试信息追踪窗口操作
    LoggerService.i('🔧 开始注册窗口服务...');

    final windowService = WindowService();
    await windowService.init();
    Get.put(windowService);
    LoggerService.i('✅ WindowService initialized');

    // 检查WindowService初始化后的窗口状态
    if (!kIsWeb && Platform.isWindows) {
      final afterServiceSize = await windowManager.getSize();
      final afterServiceMaximized = await windowManager.isMaximized();
      LoggerService.i(
        '📊 WindowService初始化后: ${afterServiceSize.width}x${afterServiceSize.height}, 最大化: $afterServiceMaximized',
      );
    }

    // 暂时不启用NativeWindowService，避免冲突
    // final nativeWindowService = NativeWindowService();
    // await nativeWindowService.onInit();
    // Get.put(nativeWindowService);
    // LoggerService.i('NativeWindowService initialized');

    // 注册认证服务（依赖ApiClient和StorageService）
    final authService = AuthService();
    await authService.init();
    Get.put(authService);
    LoggerService.i('AuthService initialized');

    // 注册库存服务
    final stockService = StockService();
    await stockService.init();
    Get.put(stockService);
    LoggerService.i('StockService initialized');

    // 注册门店服务
    final storeService = StoreService();
    await storeService.init();
    Get.put(storeService);
    LoggerService.i('StoreService initialized');

    // 注册库存调拨服务
    final storeTransferService = StoreTransferService();
    await storeTransferService.init();
    Get.put(storeTransferService);
    LoggerService.i('StoreTransferService initialized');

    // 注册首饰服务
    Get.put(JewelryService());
    LoggerService.i('JewelryService initialized');

    // 注册分类服务
    Get.put(CategoryService());
    LoggerService.i('CategoryService initialized');

    // 注册草稿服务
    Get.put(DraftService());
    LoggerService.i('DraftService initialized');

    // 注册批量导入服务
    Get.put(BatchImportService());
    LoggerService.i('BatchImportService initialized');

    // 注册销售订单服务
    Get.put(SalesOrderService());
    LoggerService.i('SalesOrderService initialized');

    Get.put(SalesReturnService());
    LoggerService.i('SalesReturnService initialized');

    // 注册打印模板服务
    Get.put(PrintTemplateService());
    LoggerService.i('PrintTemplateService initialized');

    // 注册Excel导出服务
    Get.put(ExcelService());
    LoggerService.i('ExcelService initialized');

    // 注册音频服务
    final audioService = AudioService();
    await audioService.initialize();
    Get.put(audioService);
    LoggerService.i('AudioService initialized');

    // 注册库存盘点服务 - 移除全局注册，改为在对应的Binding中注册
    // 避免与features/stock/services/inventory_check_service.dart冲突
    LoggerService.i('InventoryCheckService将在对应的Binding中注册');

    // 使用懒加载注册控制器，避免初始化时的依赖问题
    // JewelryController 通过 JewelryBinding 注册

    Get.lazyPut<StockInController>(() => StockInController());
    LoggerService.i('StockInController lazy initialized');

    Get.lazyPut<SalesReturnController>(() => SalesReturnController());
    LoggerService.i('SalesReturnController lazy initialized');

    Get.lazyPut<RecyclingController>(() => RecyclingController());
    LoggerService.i('RecyclingController lazy initialized');

    LoggerService.i('所有服务初始化完成');
  } catch (e) {
    LoggerService.e('服务初始化失败', e);
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: '金包银首饰管理系统',
      theme: AppTheme.lightTheme.copyWith(
        // 🎨 改善视觉效果：设置更好的默认背景色
        scaffoldBackgroundColor: const Color(0xFFF5F5F5), // 浅灰色背景
        colorScheme: AppTheme.lightTheme.colorScheme.copyWith(
          surface: const Color(0xFFF5F5F5),
        ),
      ),
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.light,
      debugShowCheckedModeBanner: false,

      // 🎨 添加启动画面，避免白色空白
      home: const _SplashScreen(),

      // 使用真实的路由配置
      initialRoute: AppPages.INITIAL,
      getPages: AppPages.routes,

      // 国际化配置
      translations: AppTranslations(),
      locale: const Locale('zh', 'CN'), // 默认中文
      fallbackLocale: const Locale('en', 'US'),

      // 本地化委托
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('zh', 'CN'), // 简体中文
        Locale('en', 'US'), // 英文
      ],
    );
  }
}

/// 🎨 启动画面 - 改善视觉效果，避免白色空白
class _SplashScreen extends StatefulWidget {
  const _SplashScreen();

  @override
  State<_SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<_SplashScreen> {
  @override
  void initState() {
    super.initState();
    // 🎯 延长启动画面显示时间，确保窗口调整完全完成
    _initializeAppWithDelay();
  }

  /// 🎯 延迟初始化应用，确保窗口调整完成
  Future<void> _initializeAppWithDelay() async {
    try {
      // 等待足够长的时间确保所有窗口调整完成
      await Future.delayed(const Duration(milliseconds: 2000));

      // 在Windows平台进行最终的窗口状态验证和调整
      if (!kIsWeb && Platform.isWindows) {
        await _finalWindowAdjustment();
      }

      // 再等待一点时间确保调整完成
      await Future.delayed(const Duration(milliseconds: 500));

      // 跳转到实际应用
      if (mounted) {
        Get.offAllNamed(AppPages.INITIAL);
      }
    } catch (e) {
      print('❌ 启动画面初始化失败: $e');
      // 降级方案：直接跳转
      if (mounted) {
        Get.offAllNamed(AppPages.INITIAL);
      }
    }
  }

  /// 🎯 最终窗口调整（在启动画面期间静默进行）
  Future<void> _finalWindowAdjustment() async {
    try {
      final currentSize = await windowManager.getSize();
      final isMaximized = await windowManager.isMaximized();

      print(
        '🔍 启动画面期间窗口状态: ${currentSize.width}x${currentSize.height}, 最大化: $isMaximized',
      );

      // 如果窗口尺寸不理想，进行最终调整
      if (currentSize.width < 1900) {
        print('🔧 启动画面期间进行最终窗口调整...');

        // 尝试设置为理想尺寸
        await windowManager.setSize(const Size(1920, 1080));
        await windowManager.setPosition(const Offset(0, 0));

        // 验证调整结果
        await Future.delayed(const Duration(milliseconds: 200));
        final adjustedSize = await windowManager.getSize();
        print('✅ 最终调整结果: ${adjustedSize.width}x${adjustedSize.height}');
      }
    } catch (e) {
      print('❌ 最终窗口调整失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5), // 与主题一致的背景色
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 应用图标或Logo
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: Colors.blue.shade600,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: const Icon(Icons.diamond, color: Colors.white, size: 40),
            ),
            const SizedBox(height: 24),
            // 应用标题
            Text(
              '金包银首饰管理系统',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade800,
              ),
            ),
            const SizedBox(height: 16),
            // 加载指示器
            SizedBox(
              width: 24,
              height: 24,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.blue.shade600),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 🎯 终极窗口监听器 - 实时监控并立即纠正窗口变化
class _UltimateWindowListener extends WindowListener {
  @override
  void onWindowResize() {
    super.onWindowResize();
    _handleWindowChange('onWindowResize');
  }

  @override
  void onWindowMaximize() {
    super.onWindowMaximize();
    _handleWindowChange('onWindowMaximize');
  }

  @override
  void onWindowUnmaximize() {
    super.onWindowUnmaximize();
    _handleWindowChange('onWindowUnmaximize');
    // 🔑 关键：立即重新最大化
    _forceMaximize();
  }

  @override
  void onWindowMinimize() {
    super.onWindowMinimize();
    _handleWindowChange('onWindowMinimize');
  }

  @override
  void onWindowRestore() {
    super.onWindowRestore();
    _handleWindowChange('onWindowRestore');
    // 🔑 关键：恢复后立即最大化
    _forceMaximize();
  }

  void _handleWindowChange(String event) async {
    try {
      final size = await windowManager.getSize();
      final isMaximized = await windowManager.isMaximized();
      print(
        '🎯 窗口事件: $event - ${size.width}x${size.height}, 最大化: $isMaximized',
      );
    } catch (e) {
      print('❌ 获取窗口状态失败: $e');
    }
  }

  void _forceMaximize() async {
    try {
      // 延迟一点时间确保事件处理完成
      await Future.delayed(const Duration(milliseconds: 100));

      final isMaximized = await windowManager.isMaximized();
      if (!isMaximized) {
        print('🔧 强制重新最大化窗口...');
        await windowManager.maximize();

        // 验证结果
        await Future.delayed(const Duration(milliseconds: 100));
        final finalSize = await windowManager.getSize();
        final finalMaximized = await windowManager.isMaximized();
        print(
          '✅ 强制最大化结果: ${finalSize.width}x${finalSize.height}, 最大化: $finalMaximized',
        );
      }
    } catch (e) {
      print('❌ 强制最大化失败: $e');
    }
  }
}
