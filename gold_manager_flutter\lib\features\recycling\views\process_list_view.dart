/// 回收处理列表页面
/// 显示所有处理工单，支持筛选和操作

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../../core/constants/border_styles.dart';
import '../../../core/widgets/custom_card.dart';
import '../../../core/widgets/custom_dropdown.dart';
import '../../../core/widgets/custom_text_field.dart';
import '../../../core/widgets/empty_state.dart';
import '../../../core/widgets/loading_indicator.dart';
import '../../../models/recycling/recycling_process_new.dart';
import '../controllers/recycling_process_new_controller.dart';
import 'process_form_dialog.dart';
import 'process_detail_dialog.dart';
import 'process_complete_dialog.dart';
import 'convert_to_stock_dialog.dart';

class ProcessListView extends StatelessWidget {
  ProcessListView({Key? key}) : super(key: key);

  final controller = Get.put(RecyclingProcessNewController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      body: Column(
        children: [
          _buildFilterSection(),
          Expanded(
            child: Obx(() {
              if (controller.isLoading.value && controller.processList.isEmpty) {
                return const LoadingIndicator();
              }

              if (controller.processList.isEmpty) {
                return const EmptyState(
                  message: '暂无处理工单',
                  icon: Icons.engineering,
                );
              }

              return _buildProcessList();
            }),
          ),
        ],
      ),
    );
  }

  /// 构建筛选区域
  Widget _buildFilterSection() {
    return CustomCard(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: CustomTextField(
                  controller: controller.searchController,
                  hintText: '搜索处理单号、备注',
                  prefixIcon: Icons.search,
                  onSubmitted: (_) => controller.search(),
                ),
              ),
              const SizedBox(width: 16),
              Obx(() => CustomDropdown<int>(
                value: controller.selectedStoreId.value,
                items: [
                  const DropdownMenuItem(value: 0, child: Text('全部门店')),
                  ...controller.storeList.map((store) => DropdownMenuItem(
                    value: store.id,
                    child: Text(store.name),
                  )),
                ],
                onChanged: (value) {
                  if (value != null) {
                    controller.selectedStoreId.value = value;
                    controller.search();
                  }
                },
                hint: '选择门店',
              )),
              const SizedBox(width: 16),
              Obx(() => CustomDropdown<ProcessStatus?>(
                value: controller.selectedStatus.value,
                items: [
                  const DropdownMenuItem(value: null, child: Text('全部状态')),
                  ...ProcessStatus.values.map((status) => DropdownMenuItem(
                    value: status,
                    child: Text(status.label),
                  )),
                ],
                onChanged: (value) {
                  controller.selectedStatus.value = value;
                  controller.search();
                },
                hint: '选择状态',
              )),
              const SizedBox(width: 16),
              Obx(() => CustomDropdown<ProcessType?>(
                value: controller.selectedProcessType.value,
                items: [
                  const DropdownMenuItem(value: null, child: Text('全部类型')),
                  ...ProcessType.values.map((type) => DropdownMenuItem(
                    value: type,
                    child: Text(type.label),
                  )),
                ],
                onChanged: (value) {
                  controller.selectedProcessType.value = value;
                  controller.search();
                },
                hint: '处理类型',
              )),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: Row(
                  children: [
                    const Text('日期范围：'),
                    const SizedBox(width: 8),
                    _buildDatePicker(
                      label: '开始日期',
                      date: controller.startDate,
                      onDateSelected: (date) {
                        controller.startDate.value = date;
                        controller.search();
                      },
                    ),
                    const SizedBox(width: 16),
                    _buildDatePicker(
                      label: '结束日期',
                      date: controller.endDate,
                      onDateSelected: (date) {
                        controller.endDate.value = date;
                        controller.search();
                      },
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              ElevatedButton(
                onPressed: controller.resetFilters,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey,
                ),
                child: const Text('重置'),
              ),
              const SizedBox(width: 8),
              ElevatedButton(
                onPressed: controller.search,
                child: const Text('搜索'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建日期选择器
  Widget _buildDatePicker({
    required String label,
    required Rx<DateTime?> date,
    required Function(DateTime?) onDateSelected,
  }) {
    return Obx(() => InkWell(
      onTap: () async {
        final picked = await showDatePicker(
          context: Get.context!,
          initialDate: date.value ?? DateTime.now(),
          firstDate: DateTime(2020),
          lastDate: DateTime.now().add(const Duration(days: 365)),
        );
        if (picked != null) {
          onDateSelected(picked);
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          border: AppBorderStyles.inputBorder,
          borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.calendar_today, size: 18, color: Colors.grey),
            const SizedBox(width: 8),
            Text(
              date.value != null
                  ? DateFormat('yyyy-MM-dd').format(date.value!)
                  : label,
              style: TextStyle(
                color: date.value != null ? Colors.black87 : Colors.grey,
              ),
            ),
          ],
        ),
      ),
    ));
  }

  /// 构建处理工单列表
  Widget _buildProcessList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: controller.processList.length + 1,
      itemBuilder: (context, index) {
        if (index == controller.processList.length) {
          return _buildPagination();
        }
        return _buildProcessCard(controller.processList[index]);
      },
    );
  }

  /// 构建处理工单卡片
  Widget _buildProcessCard(RecyclingProcessNew process) {
    return CustomCard(
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _showProcessDetail(process),
        borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: controller.getProcessTypeColor(process.processType).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(AppBorderStyles.smallBorderRadius),
                        ),
                        child: Text(
                          process.processType.label,
                          style: TextStyle(
                            color: controller.getProcessTypeColor(process.processType),
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: Color(int.parse(process.getStatusColor().replaceAll('#', '0xFF'))).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(AppBorderStyles.smallBorderRadius),
                        ),
                        child: Text(
                          process.status.label,
                          style: TextStyle(
                            color: Color(int.parse(process.getStatusColor().replaceAll('#', '0xFF'))),
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                  Text(
                    process.processNo,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  _buildInfoItem(Icons.store, '处理门店', process.store?.name ?? '-'),
                  const SizedBox(width: 24),
                  _buildInfoItem(Icons.source, '来源门店', process.sourceStore?.name ?? '-'),
                  const SizedBox(width: 24),
                  _buildInfoItem(Icons.receipt_long, '回收单号', process.recyclingNo ?? '-'),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  _buildInfoItem(Icons.scale, '总重量', '${process.totalWeight.toStringAsFixed(2)}g'),
                  const SizedBox(width: 24),
                  _buildInfoItem(Icons.auto_awesome, '预估金重', '${process.estimatedGoldWeight.toStringAsFixed(2)}g'),
                  const SizedBox(width: 24),
                  _buildInfoItem(Icons.circle, '预估银重', '${process.estimatedSilverWeight.toStringAsFixed(2)}g'),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  _buildInfoItem(Icons.person, '操作员', process.operatorName ?? '-'),
                  const SizedBox(width: 24),
                  _buildInfoItem(Icons.engineering, '处理人', process.processorName ?? '-'),
                  const SizedBox(width: 24),
                  _buildInfoItem(Icons.access_time, '创建时间', DateFormat('yyyy-MM-dd HH:mm').format(process.createTime)),
                ],
              ),
              if (process.remark != null && process.remark!.isNotEmpty) ...[
                const SizedBox(height: 8),
                Text(
                  '备注：${process.remark}',
                  style: const TextStyle(color: Colors.grey),
                ),
              ],
              const SizedBox(height: 12),
              _buildActionButtons(process),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建信息项
  Widget _buildInfoItem(IconData icon, String label, String value) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 16, color: Colors.grey),
        const SizedBox(width: 4),
        Text(
          '$label：',
          style: const TextStyle(color: Colors.grey, fontSize: 14),
        ),
        Text(
          value,
          style: const TextStyle(fontSize: 14),
        ),
      ],
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons(RecyclingProcessNew process) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        if (process.canStart) ...[
          OutlinedButton.icon(
            onPressed: () => controller.startProcess(process.id),
            icon: const Icon(Icons.play_arrow, size: 18),
            label: const Text('开始处理'),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.blue,
            ),
          ),
          const SizedBox(width: 8),
        ],
        if (process.canComplete) ...[
          OutlinedButton.icon(
            onPressed: () => _showCompleteDialog(process),
            icon: const Icon(Icons.check_circle, size: 18),
            label: const Text('完成处理'),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.green,
            ),
          ),
          const SizedBox(width: 8),
        ],
        if (process.results != null && process.results!.isNotEmpty) ...[
          OutlinedButton.icon(
            onPressed: () => _showConvertToStockDialog(process),
            icon: const Icon(Icons.inventory, size: 18),
            label: const Text('转库存'),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.purple,
            ),
          ),
          const SizedBox(width: 8),
        ],
        if (process.canCancel) ...[
          OutlinedButton.icon(
            onPressed: () => controller.cancelProcess(process.id),
            icon: const Icon(Icons.cancel, size: 18),
            label: const Text('取消'),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.red,
            ),
          ),
        ],
      ],
    );
  }

  /// 构建分页控件
  Widget _buildPagination() {
    return Obx(() => CustomCard(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          IconButton(
            onPressed: controller.currentPage.value > 1
                ? () => controller.goToPage(controller.currentPage.value - 1)
                : null,
            icon: const Icon(Icons.chevron_left),
          ),
          const SizedBox(width: 16),
          Text(
            '第 ${controller.currentPage.value} / ${controller.totalPages.value} 页',
            style: const TextStyle(fontSize: 16),
          ),
          const SizedBox(width: 16),
          IconButton(
            onPressed: controller.currentPage.value < controller.totalPages.value
                ? () => controller.goToPage(controller.currentPage.value + 1)
                : null,
            icon: const Icon(Icons.chevron_right),
          ),
        ],
      ),
    ));
  }

  /// 显示处理工单详情
  void _showProcessDetail(RecyclingProcessNew process) {
    Get.dialog(
      ProcessDetailDialog(process: process),
      barrierDismissible: true,
    );
  }

  /// 显示完成处理对话框
  void _showCompleteDialog(RecyclingProcessNew process) {
    Get.dialog(
      ProcessCompleteDialog(
        process: process,
        onConfirm: (results) => controller.completeProcess(process.id, results),
      ),
      barrierDismissible: false,
    );
  }

  /// 显示转库存对话框
  void _showConvertToStockDialog(RecyclingProcessNew process) {
    Get.dialog(
      ConvertToStockDialog(
        process: process,
        onConfirm: (params) => controller.convertToStock(
          processResultId: params['resultId'],
          storeId: params['storeId'],
          stockType: params['stockType'],
          quantity: params['quantity'],
          remark: params['remark'],
          jewelryInfo: params['jewelryInfo'],
          materialType: params['materialType'],
        ),
      ),
      barrierDismissible: false,
    );
  }
}