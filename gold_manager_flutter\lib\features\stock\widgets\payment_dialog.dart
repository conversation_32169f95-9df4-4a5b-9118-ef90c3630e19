import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

import '../../../core/constants/border_styles.dart';
import '../../../core/routes/app_pages.dart';
import '../controllers/payment_controller.dart';
import '../controllers/stock_out_form_controller.dart';
import '../controllers/stock_tab_controller.dart';
import '../../../services/print_service.dart';
import '../../../services/print_template_service.dart';
import '../../../services/excel_service.dart';
import '../../../core/utils/logger_service.dart';
import '../../../models/print/print_template_config.dart';

/// 收款结算对话框
///
/// 根据收款方案.md文档设计，实现简化的收款流程：
/// 1. 支持现金、刷卡、微信、支付宝四种支付方式
/// 2. 自动计算抹零金额和找零金额
/// 3. 实时计算总付款金额和实收金额
/// 4. 严格遵循AppBorderStyles设计标准
class PaymentDialog extends StatelessWidget {
  final double totalAmount; // 应付金额
  final Function(Map<String, dynamic>) onPaymentConfirmed; // 收款确认回调
  final Map<String, dynamic>? stockOutData; // 出库单数据
  final List<Map<String, dynamic>>? itemsData; // 商品明细数据

  const PaymentDialog({
    super.key,
    required this.totalAmount,
    required this.onPaymentConfirmed,
    this.stockOutData,
    this.itemsData,
  });

  @override
  Widget build(BuildContext context) {
    // 创建收款控制器实例
    final controller = Get.put(PaymentController(totalAmount: totalAmount));

    return Dialog(
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppBorderStyles.mediumBorderRadius),
      ),
      child: Container(
        width: 600, // 固定宽度，适合内容显示
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildDialogHeader(),
            const SizedBox(height: 20),
            _buildPaymentAmountDisplay(controller),
            const SizedBox(height: 24),
            _buildPaymentInputSection(controller),
            const SizedBox(height: 16),
            _buildQuickPaymentButtons(controller),
            const SizedBox(height: 20),
            _buildCalculationResults(controller),
            const SizedBox(height: 24),
            _buildActionButtons(controller),
          ],
        ),
      ),
    );
  }

  /// 构建对话框标题
  Widget _buildDialogHeader() {
    return Row(
      children: [
        Icon(Icons.payment, color: Colors.blue[600], size: 24),
        const SizedBox(width: 12),
        const Text(
          '收款结算',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const Spacer(),
        IconButton(
          onPressed: () => Get.back(),
          icon: const Icon(Icons.close, color: Colors.grey),
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
        ),
      ],
    );
  }

  /// 构建应付金额显示
  Widget _buildPaymentAmountDisplay(PaymentController controller) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(AppBorderStyles.mediumBorderRadius),
        border: Border.all(color: Colors.blue[200]!, width: AppBorderStyles.borderWidth),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.account_balance_wallet, color: Colors.blue[600], size: 20),
          const SizedBox(width: 8),
          const Text(
            '应付金额: ',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          Text(
            '¥${totalAmount.toStringAsFixed(2)}',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w700,
              color: Colors.blue[700],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建支付方式输入区域 - 使用垂直分层布局
  Widget _buildPaymentInputSection(PaymentController controller) {
    return Column(
      children: [
        // 第一行：现金、刷卡、微信
        Row(
          children: [
            Expanded(child: _buildPaymentInputField(
              controller, 
              '现金', 
              controller.cashController, 
              Icons.money,
              Colors.green,
            )),
            const SizedBox(width: 16),
            Expanded(child: _buildPaymentInputField(
              controller, 
              '刷卡', 
              controller.cardController, 
              Icons.credit_card,
              Colors.blue,
            )),
            const SizedBox(width: 16),
            Expanded(child: _buildPaymentInputField(
              controller, 
              '微信', 
              controller.wechatController, 
              Icons.chat,
              Colors.green,
            )),
          ],
        ),
        const SizedBox(height: 16),
        // 第二行：支付宝、抹零金额、找零金额
        Row(
          children: [
            Expanded(child: _buildPaymentInputField(
              controller, 
              '支付宝', 
              controller.alipayController, 
              Icons.account_balance,
              Colors.blue,
            )),
            const SizedBox(width: 16),
            Expanded(child: _buildDiscountInputField(controller)),
            const SizedBox(width: 16),
            Expanded(child: _buildCalculationResultField(
              '找零金额',
              controller.changeAmount,
              Icons.change_circle,
              Colors.purple,
            )),
          ],
        ),
      ],
    );
  }

  /// 构建单个支付方式输入框
  Widget _buildPaymentInputField(
    PaymentController controller,
    String label,
    TextEditingController textController,
    IconData icon,
    Color iconColor,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 16, color: iconColor),
            const SizedBox(width: 4),
            Text(
              label,
              style: const TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
          ],
        ),
        const SizedBox(height: 6),
        Container(
          height: 32,
          decoration: AppBorderStyles.standardBoxDecoration,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
            child: TextField(
              controller: textController,
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
              ],
              decoration: const InputDecoration(
                hintText: '¥0.00',
                hintStyle: TextStyle(fontSize: 13, color: Colors.grey),
                border: InputBorder.none,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                disabledBorder: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(vertical: 8),
                isDense: true,
              ),
              style: const TextStyle(fontSize: 13),
              onChanged: (value) => controller.calculateTotals(),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建抹零金额输入框（带自动计算提示）
  Widget _buildDiscountInputField(PaymentController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Icon(Icons.discount, size: 16, color: Colors.orange),
            const SizedBox(width: 4),
            const Text(
              '抹零金额',
              style: TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
            const SizedBox(width: 4),
            // 自动计算提示图标
            Obx(() => controller.isDiscountAutoFilled.value
                ? Tooltip(
                    message: '自动计算的抹零金额，可手动修改',
                    child: Icon(
                      Icons.auto_awesome,
                      size: 12,
                      color: Colors.orange[600],
                    ),
                  )
                : const SizedBox.shrink()),
          ],
        ),
        const SizedBox(height: 6),
        Obx(() => Container(
          height: 32,
          decoration: controller.isDiscountAutoFilled.value
              ? AppBorderStyles.standardBoxDecoration.copyWith(
                  border: Border.all(
                    color: Colors.orange[300]!,
                    width: 1.5,
                  ),
                )
              : AppBorderStyles.standardBoxDecoration,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
            child: TextField(
              controller: controller.discountController,
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
              ],
              decoration: InputDecoration(
                hintText: '¥0.00',
                hintStyle: TextStyle(
                  fontSize: 13,
                  color: controller.isDiscountAutoFilled.value
                      ? Colors.orange[300]
                      : Colors.grey,
                ),
                border: InputBorder.none,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                disabledBorder: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(vertical: 8),
                isDense: true,
              ),
              style: TextStyle(
                fontSize: 13,
                color: controller.isDiscountAutoFilled.value
                    ? Colors.orange[700]
                    : Colors.black87,
              ),
              onChanged: (value) {
                // 标记为手动输入
                if (!controller.isAutoCalculating.value) {
                  controller.isManualDiscount.value = value.isNotEmpty;
                  controller.isDiscountAutoFilled.value = false;
                }
                controller.calculateTotals();
              },
            ),
          ),
        )),
      ],
    );
  }

  /// 构建计算结果显示字段（只读）
  Widget _buildCalculationResultField(
    String label,
    RxDouble value,
    IconData icon,
    Color iconColor,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 16, color: iconColor),
            const SizedBox(width: 4),
            Text(
              label,
              style: const TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
          ],
        ),
        const SizedBox(height: 6),
        Container(
          height: 32,
          decoration: AppBorderStyles.standardBoxDecoration.copyWith(
            color: Colors.grey[50], // 只读字段使用浅灰色背景
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
            child: Obx(() => Container(
              alignment: Alignment.centerLeft,
              child: Text(
                '¥${value.value.toStringAsFixed(2)}',
                style: TextStyle(
                  fontSize: 13,
                  color: value.value >= 0 ? Colors.black87 : Colors.red[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
            )),
          ),
        ),
      ],
    );
  }

  /// 构建快速支付按钮
  Widget _buildQuickPaymentButtons(PaymentController controller) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
        border: Border.all(color: AppBorderStyles.borderColor, width: 0.5),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.flash_on, size: 14, color: Colors.orange[600]),
              const SizedBox(width: 4),
              const Text(
                '快速支付',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Colors.black54,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(child: _buildQuickPayButton(
                '现金',
                Icons.money,
                Colors.green[600]!,
                () => controller.setFullCashPayment(),
              )),
              const SizedBox(width: 8),
              Expanded(child: _buildQuickPayButton(
                '刷卡',
                Icons.credit_card,
                Colors.blue[600]!,
                () => controller.setFullCardPayment(),
              )),
              const SizedBox(width: 8),
              Expanded(child: _buildQuickPayButton(
                '微信',
                Icons.chat,
                Colors.green[600]!,
                () => controller.setFullWechatPayment(),
              )),
              const SizedBox(width: 8),
              Expanded(child: _buildQuickPayButton(
                '支付宝',
                Icons.account_balance,
                Colors.blue[600]!,
                () => controller.setFullAlipayPayment(),
              )),
              const SizedBox(width: 8),
              Expanded(child: _buildQuickPayButton(
                '清空',
                Icons.clear,
                Colors.grey[600]!,
                () => controller.clearAll(),
              )),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建单个快速支付按钮
  Widget _buildQuickPayButton(String label, IconData icon, Color color, VoidCallback onPressed) {
    return SizedBox(
      height: 28,
      child: OutlinedButton.icon(
        icon: Icon(icon, size: 12),
        label: Text(label),
        onPressed: onPressed,
        style: OutlinedButton.styleFrom(
          foregroundColor: color,
          side: BorderSide(color: color, width: 1),
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
          ),
          textStyle: const TextStyle(fontSize: 11),
        ),
      ),
    );
  }

  /// 构建计算结果汇总区域
  Widget _buildCalculationResults(PaymentController controller) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(AppBorderStyles.mediumBorderRadius),
        border: Border.all(color: AppBorderStyles.borderColor, width: AppBorderStyles.borderWidth),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildSummaryItem('总付款金额', controller.totalPayment, Colors.blue[600]!),
          Container(width: 1, height: 30, color: AppBorderStyles.borderColor),
          _buildSummaryItem('实收金额', controller.actualAmount, Colors.green[600]!),
        ],
      ),
    );
  }

  /// 构建汇总项目
  Widget _buildSummaryItem(String label, RxDouble value, Color color) {
    return Column(
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 13,
            color: Colors.black54,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 4),
        Obx(() => Text(
          '¥${value.value.toStringAsFixed(2)}',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w700,
            color: color,
          ),
        )),
      ],
    );
  }

  /// 构建操作按钮
  ///
  /// 根据收款方案.md文档设计，包含5个按钮：
  /// 保存、打印、预览、取消、电子单
  Widget _buildActionButtons(PaymentController controller) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // 保存按钮（原确认收款功能）
        Obx(() => _buildActionButton(
          label: controller.isSaving.value ? '保存中...' : '保存',
          icon: controller.isSaving.value ? Icons.hourglass_empty : Icons.save,
          color: Colors.blue[600]!,
          onPressed: controller.isSaveDisabled.value || controller.isSaving.value
              ? null
              : () => _handlePaymentConfirm(controller),
          isPrimary: true,
        )),
        const SizedBox(width: 12),
        // 打印按钮
        _buildActionButton(
          label: '打印',
          icon: Icons.print,
          color: Colors.green[600]!,
          onPressed: () => _handlePrint(controller),
          isPrimary: false,
        ),
        const SizedBox(width: 12),
        // 预览按钮
        _buildActionButton(
          label: '预览',
          icon: Icons.preview,
          color: Colors.orange[600]!,
          onPressed: () => _handlePreview(controller),
          isPrimary: false,
        ),
        const SizedBox(width: 12),
        // 取消按钮
        _buildActionButton(
          label: '取消',
          icon: Icons.close,
          color: Colors.grey[600]!,
          onPressed: () => _handleCancel(controller),
          isPrimary: false,
        ),
        const SizedBox(width: 12),
        // 电子单按钮
        _buildActionButton(
          label: '电子单',
          icon: Icons.receipt_long,
          color: Colors.purple[600]!,
          onPressed: () => _handleElectronicReceipt(controller),
          isPrimary: false,
        ),
      ],
    );
  }

  /// 构建单个操作按钮
  Widget _buildActionButton({
    required String label,
    required IconData icon,
    required Color color,
    required VoidCallback? onPressed, // 改为可选参数
    required bool isPrimary,
  }) {
    return SizedBox(
      height: 32,
      width: 90, // 增加宽度避免"保存中..."文字换行
      child: isPrimary
          ? ElevatedButton.icon(
              icon: Icon(icon, size: 14),
              label: Text(label),
              onPressed: onPressed,
              style: ElevatedButton.styleFrom(
                backgroundColor: color,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
                ),
                textStyle: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
              ),
            )
          : OutlinedButton.icon(
              icon: Icon(icon, size: 14),
              label: Text(label),
              onPressed: onPressed,
              style: OutlinedButton.styleFrom(
                foregroundColor: color,
                side: BorderSide(color: color, width: 1),
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
                ),
                textStyle: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
              ),
            ),
    );
  }

  /// 处理收款确认（保存按钮）
  Future<void> _handlePaymentConfirm(PaymentController controller) async {
    // 验证收款金额
    final isValid = await controller.validatePayment();
    if (!isValid) {
      return;
    }

    // 禁用保存按钮，防止重复点击
    controller.isSaving.value = true;

    try {
      // 构建收款数据
      final paymentData = controller.getPaymentData();

      // 调用回调函数
      onPaymentConfirmed(paymentData);

      // 显示保存成功提示，但不关闭对话框
      Get.snackbar(
        '保存成功',
        '收款信息已保存，商品状态已更新',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green[600],
        colorText: Colors.white,
        duration: const Duration(seconds: 2),
      );

      // 保存成功后重置状态并禁用保存按钮
      controller.isSaving.value = false; // 重置保存中状态
      controller.isSaveDisabled.value = true; // 禁用保存按钮

    } catch (e) {
      // 保存失败时重新启用按钮
      controller.isSaving.value = false;
      Get.snackbar(
        '保存失败',
        '收款保存失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red[600],
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );
    }
  }

  /// 处理打印功能
  void _handlePrint(PaymentController controller) {
    try {
      // 检查是否已保存
      if (!controller.isSaveDisabled.value) {
        Get.snackbar(
          '提示',
          '请先保存收款信息再进行打印',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.orange[600],
          colorText: Colors.white,
          duration: const Duration(seconds: 2),
        );
        return;
      }

      // 构建打印数据
      final printData = _buildPrintData(controller);

      // 调用打印服务
      _printReceipt(printData);

    } catch (e) {
      Get.snackbar(
        '打印失败',
        '打印过程中发生错误: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red[600],
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );
    }
  }

  /// 处理预览功能
  Future<void> _handlePreview(PaymentController controller) async {
    try {
      // 检查是否已保存
      if (!controller.isSaveDisabled.value) {
        Get.snackbar(
          '提示',
          '请先保存收款信息再进行预览',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.orange[600],
          colorText: Colors.white,
          duration: const Duration(seconds: 2),
        );
        return;
      }

      // 显示模板选择对话框
      final selectedTemplate = await _showTemplateSelectionDialog();

      // 构建打印数据
      final printData = _buildPrintData(controller);

      // 使用PrintService进行预览
      final printService = PrintService();

      await printService.showReceiptPreview(
        stockOutData: printData['stockOutData'] as Map<String, dynamic>,
        paymentData: printData['paymentData'] as Map<String, dynamic>,
        items: printData['items'] as List<Map<String, dynamic>>,
        template: selectedTemplate, // 传递选择的模板
      );

    } catch (e) {
      Get.snackbar(
        '预览失败',
        '预览过程中发生错误: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red[600],
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );
    }
  }

  /// 显示模板选择对话框
  Future<PrintTemplateConfig?> _showTemplateSelectionDialog() async {
    try {
      final templateService = Get.find<PrintTemplateService>();
      final templates = templateService.getAllTemplates();
      final defaultTemplate = templateService.getDefaultTemplate();

      if (templates.isEmpty) {
        Get.snackbar(
          '提示',
          '没有可用的打印模板，将使用默认设置',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.orange[600],
          colorText: Colors.white,
        );
        return null;
      }

      return await Get.dialog<PrintTemplateConfig?>(
        Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppBorderStyles.mediumBorderRadius),
          ),
          child: Container(
            width: 400,
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 标题
                Row(
                  children: [
                    Icon(Icons.print, color: Colors.blue[600]),
                    const SizedBox(width: 8),
                    const Text(
                      '选择打印模板',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () => Get.back(),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                // 模板列表
                Container(
                  constraints: const BoxConstraints(maxHeight: 300),
                  child: ListView.builder(
                    shrinkWrap: true,
                    itemCount: templates.length,
                    itemBuilder: (context, index) {
                      final template = templates[index];
                      final isDefault = template.id == defaultTemplate?.id;

                      return Card(
                        margin: const EdgeInsets.only(bottom: 8),
                        child: ListTile(
                          leading: Icon(
                            isDefault ? Icons.star : Icons.description,
                            color: isDefault ? Colors.orange : Colors.blue,
                          ),
                          title: Text(template.name),
                          subtitle: Text(template.description ?? '无描述'),
                          trailing: isDefault
                              ? Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                  decoration: BoxDecoration(
                                    color: Colors.orange[100],
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Text(
                                    '默认',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.orange[700],
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                )
                              : null,
                          onTap: () => Get.back(result: template),
                        ),
                      );
                    },
                  ),
                ),
                const SizedBox(height: 16),
                // 底部按钮
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => Get.back(result: defaultTemplate),
                        child: const Text('使用默认模板'),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => _openTemplateManagement(),
                        child: const Text('管理模板'),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () => Get.back(),
                        child: const Text('取消'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      );
    } catch (e) {
      Get.snackbar(
        '错误',
        '获取打印模板失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red[600],
        colorText: Colors.white,
      );
      return null;
    }
  }

  /// 打开模板管理页面
  void _openTemplateManagement() async {
    try {
      // 关闭当前对话框
      Get.back();

      // 跳转到模板管理页面
      await Get.toNamed(Routes.PRINT_TEMPLATE);

      // 可以在这里添加刷新逻辑，如果需要的话
    } catch (e) {
      Get.snackbar(
        '错误',
        '无法打开模板管理页面: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red[600],
        colorText: Colors.white,
      );
    }
  }

  /// 处理取消按钮
  void _handleCancel(PaymentController controller) {
    // 🔧 修复错误2：保存成功后点击取消应该清空新建出库单页面
    if (controller.isSaveDisabled.value) {
      // 如果已经保存成功，需要清空新建出库单页面
      LoggerService.d('🔄 收款已完成，准备重置出库单页面...');

      // 先关闭收款对话框
      Get.back();

      // 延迟执行重置，确保对话框关闭后再重置
      Future.delayed(const Duration(milliseconds: 200), () {
        try {
          // 🔧 修复错误2：使用更安全的控制器获取方式，支持标签页模式
          StockOutFormController? stockOutController;

          // 方案1：尝试获取默认控制器
          if (Get.isRegistered<StockOutFormController>()) {
            stockOutController = Get.find<StockOutFormController>();
            LoggerService.d('✅ 成功获取默认StockOutFormController');
          } else {
            // 方案2：尝试通过标签页系统获取控制器
            LoggerService.d('🔄 默认控制器未找到，尝试通过标签页系统获取...');

            try {
              final stockTabController = Get.find<StockTabController>();
              final currentTab = stockTabController.tabManager.currentTab;

              if (currentTab != null && currentTab.id.startsWith('stock_out_form_')) {
                // 尝试使用标签ID获取控制器
                if (Get.isRegistered<StockOutFormController>(tag: currentTab.id)) {
                  stockOutController = Get.find<StockOutFormController>(tag: currentTab.id);
                  LoggerService.d('✅ 通过标签页获取StockOutFormController: ${currentTab.id}');
                } else {
                  LoggerService.w('⚠️ 标签页控制器未注册: ${currentTab.id}');
                }
              }
            } catch (e) {
              LoggerService.w('⚠️ 无法通过标签页系统获取控制器: $e');
            }
          }

          if (stockOutController != null) {
            stockOutController.resetForm();

            // 强制刷新UI
            stockOutController.itemList.refresh();

            LoggerService.d('✅ 出库单表单重置完成');

            Get.snackbar(
              '提示',
              '收款已完成，出库单页面已重置',
              snackPosition: SnackPosition.BOTTOM,
              backgroundColor: Colors.blue[600],
              colorText: Colors.white,
              duration: const Duration(seconds: 2),
            );
          } else {
            // 如果所有方案都失败，使用备用方案
            LoggerService.w('⚠️ 所有控制器获取方案都失败，使用备用方案');
            _handleControllerNotFound();
          }
        } catch (e) {
          // 如果获取控制器失败，使用备用方案
          LoggerService.e('❌ 获取StockOutFormController失败: $e');
          _handleControllerNotFound();
        }
      });

    } else {
      // 如果还没有保存，直接关闭对话框
      Get.back();
    }
  }

  /// 🔧 处理控制器未找到的情况
  void _handleControllerNotFound() {
    try {
      // 备用方案1：尝试通过路由返回并重新进入页面
      LoggerService.d('🔄 使用备用方案：通过路由重置页面');

      // 显示提示信息
      Get.snackbar(
        '提示',
        '收款已完成，请手动清空商品明细或重新进入页面',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange[600],
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
        mainButton: TextButton(
          onPressed: () {
            // 关闭提示
            Get.back();
            // 尝试返回到出库单列表页面
            Get.offAllNamed('/stock-out');
          },
          child: const Text('返回列表', style: TextStyle(color: Colors.white)),
        ),
      );

    } catch (e) {
      LoggerService.e('❌ 备用方案也失败: $e');

      // 最后的备用方案：只显示简单提示
      Get.snackbar(
        '提示',
        '收款已完成，请手动清空商品明细',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.blue[600],
        colorText: Colors.white,
        duration: const Duration(seconds: 2),
      );
    }
  }

  /// 处理电子单功能 - Excel导出
  Future<void> _handleElectronicReceipt(PaymentController controller) async {
    try {
      LoggerService.d('🔄 开始处理Excel导出请求...');

      // 检查是否有商品明细数据
      if (itemsData == null || itemsData!.isEmpty) {
        LoggerService.w('⚠️ 商品明细数据为空，无法导出Excel');
        Get.snackbar(
          '提示',
          '没有商品明细数据，无法导出Excel文件',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.orange[600],
          colorText: Colors.white,
          duration: const Duration(seconds: 2),
        );
        return;
      }

      // 🔧 数据验证和调试信息
      LoggerService.d('📊 商品明细数据验证:');
      LoggerService.d('   - 数据数量: ${itemsData!.length}');
      LoggerService.d('   - 出库单数据: ${stockOutData != null ? "存在" : "不存在"}');
      LoggerService.d('   - 收款数据: ${controller.getPaymentData() != null ? "存在" : "不存在"}');

      // 验证每个商品的数据结构
      for (int i = 0; i < itemsData!.length; i++) {
        final item = itemsData![i];
        LoggerService.d('   - 第${i + 1}个商品: ${item['barcode'] ?? 'N/A'} - ${item['name'] ?? 'N/A'}');

        // 检查关键字段
        final missingFields = <String>[];
        if (!item.containsKey('barcode') || item['barcode'] == null) missingFields.add('barcode');
        if (!item.containsKey('name') || item['name'] == null) missingFields.add('name');
        if (!item.containsKey('total_amount') || item['total_amount'] == null) missingFields.add('total_amount');

        if (missingFields.isNotEmpty) {
          LoggerService.w('⚠️ 第${i + 1}个商品缺少字段: ${missingFields.join(", ")}');
        }
      }

      // 显示导出进度对话框
      Get.dialog(
        const Center(
          child: Card(
            child: Padding(
              padding: EdgeInsets.all(20),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('正在生成Excel文件...'),
                ],
              ),
            ),
          ),
        ),
        barrierDismissible: false,
      );

      // 调用Excel导出服务
      LoggerService.d('📊 正在调用ExcelService...');
      final excelService = Get.find<ExcelService>();

      // 🔧 增强错误处理：包装Excel导出调用
      bool success = false;
      try {
        success = await excelService.exportStockOutItems(
          items: itemsData!,
          stockOutData: stockOutData,
          paymentData: controller.getPaymentData(),
        );
        LoggerService.d('📊 ExcelService调用完成，结果: $success');
      } catch (excelError) {
        LoggerService.e('❌ ExcelService调用失败: $excelError');
        success = false;
      }

      // 关闭进度对话框
      if (Get.isDialogOpen == true) {
        Get.back();
      }

      if (success) {
        Get.snackbar(
          '导出成功',
          'Excel文件已保存到下载文件夹',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green[600],
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
        );
      } else {
        Get.snackbar(
          '导出失败',
          '无法生成Excel文件，请检查数据完整性',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red[600],
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
        );
      }

    } catch (e) {
      // 确保关闭进度对话框
      if (Get.isDialogOpen == true) {
        Get.back();
      }

      Get.snackbar(
        '导出失败',
        '导出过程中发生错误: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red[600],
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );
    }
  }

  /// 构建打印数据
  Map<String, dynamic> _buildPrintData(PaymentController controller) {
    // 使用真实的出库单数据，如果没有则使用默认值
    final realStockOutData = stockOutData ?? {
      'order_no': 'CK${DateTime.now().millisecondsSinceEpoch}',
      'customer': '客户',
      'sale_type': 'retail',
    };

    // 使用真实的商品明细数据，如果没有则使用示例数据
    final realItemsData = itemsData ?? [
      {
        'barcode': 'DEMO001',
        'name': '示例商品',
        'gold_weight': '10.00',
        'silver_weight': '0.00',
        'total_weight': '10.00',
        'work_price': '50.00',
        'total_amount': totalAmount.toString(),
      }
    ];

    return {
      'stockOutData': realStockOutData,
      'paymentData': controller.getPaymentData(),
      'items': realItemsData,
      'totalAmount': totalAmount,
    };
  }

  /// 打印收款凭证
  Future<void> _printReceipt(Map<String, dynamic> printData) async {
    try {
      // 显示加载对话框
      Get.dialog(
        const Center(
          child: Card(
            child: Padding(
              padding: EdgeInsets.all(20),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('正在生成收款凭证...'),
                ],
              ),
            ),
          ),
        ),
        barrierDismissible: false,
      );

      // 使用PrintService进行打印
      final printService = PrintService();

      final success = await printService.printReceipt(
        stockOutData: printData['stockOutData'] as Map<String, dynamic>,
        paymentData: printData['paymentData'] as Map<String, dynamic>,
        items: printData['items'] as List<Map<String, dynamic>>,
      );

      // 关闭加载对话框
      if (Get.isDialogOpen == true) {
        Get.back();
      }

      if (success) {
        Get.snackbar(
          '打印成功',
          'PDF预览已打开，您可以在预览窗口中直接打印或保存文件',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green[600],
          colorText: Colors.white,
          duration: const Duration(seconds: 4),
        );
      } else {
        Get.snackbar(
          '打印失败',
          '无法生成收款凭证，请检查数据完整性',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red[600],
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
        );
      }
    } catch (e) {
      // 确保关闭加载对话框
      if (Get.isDialogOpen == true) {
        Get.back();
      }

      Get.snackbar(
        '打印失败',
        '打印过程中发生错误: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red[600],
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );
    }
  }

}
