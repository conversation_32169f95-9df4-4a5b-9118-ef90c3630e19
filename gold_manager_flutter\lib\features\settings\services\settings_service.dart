import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart' as dio;
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';

import '../../../core/config/app_config.dart';
import '../../../core/services/api_service.dart';
import '../../../core/services/storage_service.dart';
import '../models/setting_model.dart';

/// 系统设置服务类
class SettingsService {
  final ApiService _apiService = Get.find<ApiService>();
  final StorageService _storageService = Get.find<StorageService>();
  
  // 本地存储键
  static const String _settingsKey = 'system_settings';
  
  /// 获取系统设置
  Future<SystemSettings> getSettings() async {
    try {
      // 先尝试从API获取
      final response = await _apiService.get('${AppConfig.apiEndpoint['settings']}/config');
      
      // 解析返回数据
      final settings = SystemSettings.fromJson(response.data);
      
      // 保存到本地存储
      await _saveSettingsToLocal(settings);
      
      return settings;
    } on dio.DioException catch (e) {
      print('获取系统设置失败: $e');
      
      // 如果API请求失败，尝试从本地获取
      return await _getSettingsFromLocal();
    } catch (e) {
      print('解析系统设置失败: $e');
      return SystemSettings.defaultSettings();
    }
  }
  
  /// 保存系统设置
  Future<bool> saveSettings(SystemSettings settings) async {
    try {
      // 发送到API
      await _apiService.post(
        '${AppConfig.apiEndpoint['settings']}/config', 
        data: settings.toJson(),
      );
      
      // 保存到本地存储
      await _saveSettingsToLocal(settings);
      
      return true;
    } catch (e) {
      print('保存系统设置失败: $e');
      return false;
    }
  }
  
  /// 从本地存储获取设置
  Future<SystemSettings> _getSettingsFromLocal() async {
    try {
      final String? jsonString = _storageService.getString(_settingsKey);
      
      if (jsonString != null && jsonString.isNotEmpty) {
        return SystemSettings.fromJson(jsonDecode(jsonString));
      } else {
        return SystemSettings.defaultSettings();
      }
    } catch (e) {
      print('从本地获取系统设置失败: $e');
      return SystemSettings.defaultSettings();
    }
  }
  
  /// 保存设置到本地存储
  Future<void> _saveSettingsToLocal(SystemSettings settings) async {
    try {
      final String jsonString = jsonEncode(settings.toJson());
      await _storageService.setString(_settingsKey, jsonString);
    } catch (e) {
      print('保存系统设置到本地失败: $e');
    }
  }
  
  /// 重置系统设置为默认值
  Future<SystemSettings> resetSettings() async {
    final defaultSettings = SystemSettings.defaultSettings();
    
    try {
      // 发送到API
      await _apiService.post(
        '${AppConfig.apiEndpoint['settings']}/reset', 
        data: {},
      );
      
      // 保存到本地存储
      await _saveSettingsToLocal(defaultSettings);
      
      return defaultSettings;
    } catch (e) {
      print('重置系统设置失败: $e');
      return defaultSettings;
    }
  }
  
  /// 获取用户列表
  Future<List<User>> getUserList({
    int page = 1,
    int pageSize = 20,
    String? keyword,
    int? roleId,
    int? storeId,
  }) async {
    try {
      final Map<String, dynamic> params = {
        'page': page,
        'page_size': pageSize,
      };
      
      if (keyword != null && keyword.isNotEmpty) {
        params['keyword'] = keyword;
      }
      
      if (roleId != null) {
        params['role_id'] = roleId;
      }
      
      if (storeId != null) {
        params['store_id'] = storeId;
      }
      
      final response = await _apiService.get(
        '${AppConfig.apiEndpoint['user']}',
        queryParameters: params,
      );
      
      final List<dynamic> userList = response.data['data'];
      
      return userList
          .map((json) => User.fromJson(json))
          .toList();
    } catch (e) {
      print('获取用户列表失败: $e');
      return [];
    }
  }
  
  /// 获取用户详情
  Future<User?> getUserDetail(int userId) async {
    try {
      final response = await _apiService.get('${AppConfig.apiEndpoint['user']}/$userId');
      
      return User.fromJson(response.data);
    } catch (e) {
      print('获取用户详情失败: $e');
      return null;
    }
  }
  
  /// 创建用户
  Future<bool> createUser(Map<String, dynamic> userData) async {
    try {
      await _apiService.post(
        '${AppConfig.apiEndpoint['user']}',
        data: userData,
      );
      
      return true;
    } catch (e) {
      print('创建用户失败: $e');
      return false;
    }
  }
  
  /// 更新用户
  Future<bool> updateUser(int userId, Map<String, dynamic> userData) async {
    try {
      await _apiService.put(
        '${AppConfig.apiEndpoint['user']}/$userId',
        data: userData,
      );
      
      return true;
    } catch (e) {
      print('更新用户失败: $e');
      return false;
    }
  }
  
  /// 删除用户
  Future<bool> deleteUser(int userId) async {
    try {
      await _apiService.delete('${AppConfig.apiEndpoint['user']}/$userId');
      
      return true;
    } catch (e) {
      print('删除用户失败: $e');
      return false;
    }
  }
  
  /// 重置用户密码
  Future<bool> resetUserPassword(int userId) async {
    try {
      await _apiService.post(
        '${AppConfig.apiEndpoint['user']}/$userId/reset-password',
        data: {},
      );
      
      return true;
    } catch (e) {
      print('重置用户密码失败: $e');
      return false;
    }
  }
  
  /// 获取角色列表
  Future<List<Role>> getRoleList() async {
    try {
      final response = await _apiService.get('${AppConfig.apiEndpoint['settings']}/roles');
      
      final List<dynamic> roleList = response.data;
      
      return roleList
          .map((json) => Role.fromJson(json))
          .toList();
    } catch (e) {
      print('获取角色列表失败: $e');
      return [];
    }
  }
  
  /// 获取权限列表
  Future<List<Permission>> getPermissionList() async {
    try {
      final response = await _apiService.get('${AppConfig.apiEndpoint['settings']}/permissions');
      
      final List<dynamic> permissionList = response.data;
      
      return permissionList
          .map((json) => Permission.fromJson(json))
          .toList();
    } catch (e) {
      print('获取权限列表失败: $e');
      return [];
    }
  }
  
  /// 创建角色
  Future<bool> createRole(String name, String description, List<int> permissionIds) async {
    try {
      await _apiService.post(
        '${AppConfig.apiEndpoint['settings']}/roles',
        data: {
          'name': name,
          'description': description,
          'permission_ids': permissionIds,
        },
      );
      
      return true;
    } catch (e) {
      print('创建角色失败: $e');
      return false;
    }
  }
  
  /// 更新角色
  Future<bool> updateRole(int roleId, String name, String description, List<int> permissionIds) async {
    try {
      await _apiService.put(
        '${AppConfig.apiEndpoint['settings']}/roles/$roleId',
        data: {
          'name': name,
          'description': description,
          'permission_ids': permissionIds,
        },
      );
      
      return true;
    } catch (e) {
      print('更新角色失败: $e');
      return false;
    }
  }
  
  /// 删除角色
  Future<bool> deleteRole(int roleId) async {
    try {
      await _apiService.delete('${AppConfig.apiEndpoint['settings']}/roles/$roleId');
      
      return true;
    } catch (e) {
      print('删除角色失败: $e');
      return false;
    }
  }
  
  /// 创建数据库备份
  Future<bool> createBackup(String description) async {
    try {
      await _apiService.post(
        '${AppConfig.apiEndpoint['settings']}/backup',
        data: {
          'description': description,
          'include_images': true,
        },
      );
      
      return true;
    } catch (e) {
      print('创建备份失败: $e');
      return false;
    }
  }
  
  /// 获取备份列表
  Future<List<BackupRecord>> getBackupList() async {
    try {
      final response = await _apiService.get('${AppConfig.apiEndpoint['settings']}/backup');
      
      final List<dynamic> backupList = response.data;
      
      return backupList
          .map((json) => BackupRecord.fromJson(json))
          .toList();
    } catch (e) {
      print('获取备份列表失败: $e');
      return [];
    }
  }
  
  /// 下载备份文件
  Future<String?> downloadBackup(int backupId) async {
    try {
      // 获取下载链接
      final response = await _apiService.get(
        '${AppConfig.apiEndpoint['settings']}/backup/$backupId/download'
      );
      
      final String downloadUrl = response.data['download_url'];
      
      // 获取临时目录
      final directory = await getTemporaryDirectory();
      final savePath = '${directory.path}/backup_$backupId.zip';
      
      // 下载文件
      await dio.Dio().download(
        downloadUrl,
        savePath,
        onReceiveProgress: (received, total) {
          if (total != -1) {
            final progress = received / total;
            print('下载进度: ${(progress * 100).toStringAsFixed(0)}%');
          }
        },
      );
      
      return savePath;
    } catch (e) {
      print('下载备份失败: $e');
      return null;
    }
  }
  
  /// 恢复备份
  Future<bool> restoreBackup(int backupId) async {
    try {
      await _apiService.post(
        '${AppConfig.apiEndpoint['settings']}/backup/$backupId/restore',
        data: {},
      );
      
      return true;
    } catch (e) {
      print('恢复备份失败: $e');
      return false;
    }
  }
  
  /// 上传备份文件
  Future<bool> uploadBackup(File backupFile, String description) async {
    try {
      // 创建FormData对象
      final formData = dio.FormData.fromMap({
        'backup_file': await dio.MultipartFile.fromFile(
          backupFile.path,
          filename: backupFile.path.split('/').last,
        ),
        'description': description,
      });
      
      // 上传文件
      await _apiService.post(
        '${AppConfig.apiEndpoint['settings']}/backup/upload',
        data: formData,
      );
      
      return true;
    } catch (e) {
      print('上传备份失败: $e');
      return false;
    }
  }
  
  /// 删除备份
  Future<bool> deleteBackup(int backupId) async {
    try {
      await _apiService.delete('${AppConfig.apiEndpoint['settings']}/backup/$backupId');
      
      return true;
    } catch (e) {
      print('删除备份失败: $e');
      return false;
    }
  }
  
  /// 获取系统日志
  Future<List<SystemLog>> getSystemLogs({
    int page = 1,
    int pageSize = 20,
    String? actionType,
    String? keyword,
    String? startDate,
    String? endDate,
  }) async {
    try {
      final Map<String, dynamic> params = {
        'page': page,
        'page_size': pageSize,
      };
      
      if (actionType != null && actionType.isNotEmpty) {
        params['action_type'] = actionType;
      }
      
      if (keyword != null && keyword.isNotEmpty) {
        params['keyword'] = keyword;
      }
      
      if (startDate != null && startDate.isNotEmpty) {
        params['start_date'] = startDate;
      }
      
      if (endDate != null && endDate.isNotEmpty) {
        params['end_date'] = endDate;
      }
      
      final response = await _apiService.get(
        '${AppConfig.apiEndpoint['settings']}/logs',
        queryParameters: params,
      );
      
      final List<dynamic> logList = response.data['data'];
      
      return logList
          .map((json) => SystemLog.fromJson(json))
          .toList();
    } catch (e) {
      print('获取系统日志失败: $e');
      return [];
    }
  }
  
  /// 清除系统日志
  Future<bool> clearSystemLogs() async {
    try {
      await _apiService.delete('${AppConfig.apiEndpoint['settings']}/logs');
      
      return true;
    } catch (e) {
      print('清除系统日志失败: $e');
      return false;
    }
  }
} 