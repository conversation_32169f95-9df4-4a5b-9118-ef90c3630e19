import 'recycling_model.dart';

/// 回收单分页结果模型
class RecyclingPaginationResult {
  final List<RecyclingOrder> orders;
  final int currentPage;
  final int totalPages;
  final int totalRecords;
  final int pageSize;

  const RecyclingPaginationResult({
    required this.orders,
    required this.currentPage,
    required this.totalPages,
    required this.totalRecords,
    required this.pageSize,
  });

  /// 从API响应创建分页结果
  factory RecyclingPaginationResult.fromApiResponse(Map<String, dynamic> json) {
    final List<dynamic> dataList = json['data'] ?? [];
    final List<RecyclingOrder> orders = dataList
        .map((item) => RecyclingOrder.fromJson(item))
        .toList();

    final pagination = json['pagination'] ?? {};
    
    return RecyclingPaginationResult(
      orders: orders,
      currentPage: pagination['page'] ?? 1,
      totalPages: pagination['pages'] ?? 1,
      totalRecords: pagination['total'] ?? 0,
      pageSize: pagination['page_size'] ?? 20,
    );
  }

  /// 创建空的分页结果
  factory RecyclingPaginationResult.empty() {
    return const RecyclingPaginationResult(
      orders: [],
      currentPage: 1,
      totalPages: 1,
      totalRecords: 0,
      pageSize: 20,
    );
  }
}
