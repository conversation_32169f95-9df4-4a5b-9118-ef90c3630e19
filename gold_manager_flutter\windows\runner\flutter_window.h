#ifndef RUNNER_FLUTTER_WINDOW_H_
#define RUNNER_FLUTTER_WINDOW_H_

#include <flutter/dart_project.h>
#include <flutter/flutter_view_controller.h>
#include <flutter/method_channel.h>
#include <flutter/encodable_value.h>

#include <memory>

#include "win32_window.h"

// A window that does nothing but host a Flutter view.
class FlutterWindow : public Win32Window {
 public:
  // Creates a new FlutterWindow hosting a Flutter view running |project|.
  explicit FlutterWindow(const flutter::DartProject& project);
  virtual ~FlutterWindow();

 protected:
  // Win32Window:
  bool OnCreate() override;
  void OnDestroy() override;
  LRESULT MessageHandler(HWND window, UINT const message, WPARAM const wparam,
                         LPARAM const lparam) noexcept override;

 private:
  // The project to run.
  flutter::DartProject project_;

  // The Flutter instance hosted by this window.
  std::unique_ptr<flutter::FlutterViewController> flutter_controller_;

  // Window control method channel
  std::unique_ptr<flutter::MethodChannel<flutter::EncodableValue>> window_control_channel_;

  // Setup window control method channel
  void SetupWindowControlChannel();

  // Handle window control method calls
  void HandleWindowControlMethod(
      const flutter::MethodCall<flutter::EncodableValue>& method_call,
      std::unique_ptr<flutter::MethodResult<flutter::EncodableValue>> result);

  // Native window control methods
  void MaximizeWindowNative();
  void MinimizeWindowNative();
  void RestoreWindowNative();
  void SetWindowSizeNative(int width, int height);
  std::pair<int, int> GetWindowSizeNative();
  bool IsWindowMaximizedNative();
};

#endif  // RUNNER_FLUTTER_WINDOW_H_
