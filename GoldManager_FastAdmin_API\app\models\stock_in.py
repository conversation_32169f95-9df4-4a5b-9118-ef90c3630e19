"""
入库管理数据模型
对应FastAdmin数据库中的入库相关表结构
"""

from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey, DECIMAL
from sqlalchemy.orm import relationship
from ..core.database import Base


class StockIn(Base):
    """入库单主表 - 对应 fa_stock_in"""
    __tablename__ = "fa_stock_in"
    
    id = Column(Integer, primary_key=True, index=True, comment="入库单ID")
    order_no = Column(String(50), unique=True, nullable=False, comment="入库单号")
    store_id = Column(Integer, ForeignKey("fa_store.id"), nullable=False, comment="门店ID")
    supplier = Column(String(100), comment="供应商")
    total_amount = Column(DECIMAL(10, 2), default=0.00, comment="总金额")
    remark = Column(Text, comment="备注")
    operator_id = Column(Integer, ForeignKey("fa_admin.id"), nullable=False, comment="操作员ID")
    audit_user_id = Column(Integer, ForeignKey("fa_admin.id"), comment="审核员ID")
    audit_time = Column(Integer, comment="审核时间")
    audit_note = Column(String(255), comment="审核备注")
    status = Column(Integer, default=0, comment="状态:0=草稿,1=待审核,2=已通过,3=已拒绝")
    createtime = Column(Integer, comment="创建时间")
    updatetime = Column(Integer, comment="更新时间")
    
    # 关联关系
    store = relationship("Store", back_populates="stock_ins")
    operator = relationship("Admin", foreign_keys=[operator_id], back_populates="stock_ins")
    auditor = relationship("Admin", foreign_keys=[audit_user_id])
    items = relationship("StockInItem", back_populates="stock_in", cascade="all, delete-orphan")


class StockInItem(Base):
    """入库单明细表 - 对应 fa_stock_in_item"""  
    __tablename__ = "fa_stock_in_item"
    
    id = Column(Integer, primary_key=True, index=True, comment="明细ID")
    stock_in_id = Column(Integer, ForeignKey("fa_stock_in.id"), nullable=False, comment="入库单ID")
    jewelry_id = Column(Integer, ForeignKey("fa_jewelry.id"), nullable=False, comment="首饰ID")
    barcode = Column(String(50), nullable=False, comment="商品条码")
    name = Column(String(100), nullable=False, comment="商品名称")
    category_id = Column(Integer, nullable=False, comment="分类ID")
    ring_size = Column(String(20), comment="圈口号")
    
    # 金重相关
    gold_weight = Column(DECIMAL(10, 2), default=0.00, comment="金重(克)")
    gold_price = Column(DECIMAL(10, 2), default=0.00, comment="金进价(克价)")
    gold_cost = Column(DECIMAL(10, 2), default=0.00, comment="金成本")
    
    # 银重相关
    silver_weight = Column(DECIMAL(10, 2), default=0.00, comment="银重(克)")
    total_weight = Column(DECIMAL(10, 2), default=0.00, comment="总重")
    silver_price = Column(DECIMAL(10, 2), default=0.00, comment="银进价(克价)")
    silver_cost = Column(DECIMAL(10, 2), default=0.00, comment="银成本")
    
    # 工费相关
    silver_work_type = Column(Integer, default=0, comment="银工费方式:0=按克,1=按件")
    silver_work_price = Column(DECIMAL(10, 2), default=0.00, comment="银工费(按克为克价,按件为件价)")
    silver_work_cost = Column(DECIMAL(10, 2), default=0.00, comment="银工费成本")
    plating_cost = Column(DECIMAL(10, 2), default=0.00, comment="电铸费")
    
    # 成本和定价
    total_cost = Column(DECIMAL(10, 2), default=0.00, comment="进货总成本")
    wholesale_work_price = Column(DECIMAL(10, 2), default=0.00, comment="批发工费")
    retail_work_price = Column(DECIMAL(10, 2), default=0.00, comment="零售工费")
    piece_work_price = Column(DECIMAL(10, 2), default=0.00, comment="件工费")
    
    createtime = Column(Integer, comment="创建时间")
    
    # 关联关系
    stock_in = relationship("StockIn", back_populates="items")
    jewelry = relationship("Jewelry", back_populates="stock_in_items") 