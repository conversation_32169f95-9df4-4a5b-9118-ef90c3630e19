"""
出库管理数据模型
对应FastAdmin数据库中的出库相关表结构
"""

from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey, DECIMAL
from sqlalchemy.orm import relationship
from ..core.database import Base


class StockOut(Base):
    """出库单主表 - 对应 fa_stock_out"""
    __tablename__ = "fa_stock_out"
    
    id = Column(Integer, primary_key=True, index=True, comment="出库单ID")
    order_no = Column(String(30), unique=True, nullable=False, comment="出库单号")
    store_id = Column(Integer, ForeignKey("fa_store.id"), nullable=False, comment="出库门店ID")
    customer = Column(String(50), comment="客户")
    recycling_id = Column(Integer, default=0, comment="关联的回收单ID")
    total_weight = Column(DECIMAL(10, 2), default=0.00, comment="总重量(克)")
    sale_type = Column(String(20), default='retail', comment="销售类型:retail=零售,wholesale=批发")
    total_amount = Column(DECIMAL(10, 2), default=0.00, comment="总金额")
    operator_id = Column(Integer, ForeignKey("fa_admin.id"), nullable=False, comment="操作员ID")
    remark = Column(String(255), comment="备注")
    status = Column(Integer, default=1, comment="状态:1=待审核,2=已通过,3=未通过,4=已作废")
    
    # 时间字段
    createtime = Column(Integer, comment="创建时间")
    updatetime = Column(Integer, comment="更新时间")
    audit_time = Column(Integer, comment="审核时间")
    
    # 审核相关
    audit_user_id = Column(Integer, ForeignKey("fa_admin.id"), comment="审核人ID")
    audit_remark = Column(String(255), comment="审核备注")
    
    # 收款相关
    payment_status = Column(Integer, default=0, comment="收款状态:0=未收款,1=已收款")
    payment_time = Column(Integer, comment="收款时间")
    payment_method = Column(String(20), comment="收款方式")
    payment_remark = Column(String(255), comment="支付备注")
    
    # 各种支付金额
    cash_amount = Column(DECIMAL(10, 2), default=0.00, comment="现金金额")
    wechat_amount = Column(DECIMAL(10, 2), default=0.00, comment="微信金额")
    alipay_amount = Column(DECIMAL(10, 2), default=0.00, comment="支付宝金额")
    card_amount = Column(DECIMAL(10, 2), default=0.00, comment="刷卡金额")
    discount_amount = Column(DECIMAL(10, 2), default=0.00, comment="抹零金额")
    actual_amount = Column(DECIMAL(10, 2), default=0.00, comment="实收金额")
    
    # 关联关系
    store = relationship("Store", back_populates="stock_outs")
    operator = relationship("Admin", foreign_keys=[operator_id], back_populates="stock_outs")
    auditor = relationship("Admin", foreign_keys=[audit_user_id])
    items = relationship("StockOutItem", back_populates="stock_out", cascade="all, delete-orphan")


class StockOutItem(Base):
    """出库单明细表 - 对应 fa_stock_out_item"""  
    __tablename__ = "fa_stock_out_item"
    
    id = Column(Integer, primary_key=True, index=True, comment="明细ID")
    stock_out_id = Column(Integer, ForeignKey("fa_stock_out.id"), nullable=False, comment="出库单ID")
    jewelry_id = Column(Integer, ForeignKey("fa_jewelry.id"), nullable=False, comment="首饰ID")
    barcode = Column(String(50), nullable=False, comment="商品条码")
    name = Column(String(100), nullable=False, comment="商品名称")
    category_id = Column(Integer, nullable=False, comment="分类ID")
    ring_size = Column(String(20), comment="圈口号")
    
    # 金重相关
    gold_weight = Column(DECIMAL(10, 2), default=0.00, comment="金重(克)")
    gold_price = Column(DECIMAL(10, 2), default=0.00, comment="金价(克价)")
    gold_cost = Column(DECIMAL(10, 2), default=0.00, comment="金成本")
    
    # 银重相关
    silver_weight = Column(DECIMAL(10, 2), default=0.00, comment="银重(克)")
    total_weight = Column(DECIMAL(10, 2), default=0.00, comment="总重(克)")
    silver_price = Column(DECIMAL(10, 2), default=0.00, comment="银价(克价)")
    silver_cost = Column(DECIMAL(10, 2), default=0.00, comment="银成本")
    
    # 工费相关
    silver_work_type = Column(Integer, default=0, comment="工费方式:0=按克,1=按件")
    silver_work_price = Column(DECIMAL(10, 2), default=0.00, comment="银工费")
    plating_cost = Column(DECIMAL(10, 2), default=0.00, comment="电铸费")
    piece_work_price = Column(DECIMAL(10, 2), default=0.00, comment="件工费")
    work_price = Column(DECIMAL(10, 2), default=0.00, comment="实际工费")
    
    # 成本和定价
    total_cost = Column(DECIMAL(10, 2), default=0.00, comment="总成本")
    total_amount = Column(DECIMAL(10, 2), default=0.00, comment="商品金额")
    sale_type = Column(String(20), default='retail', comment="销售类型:retail=零售,wholesale=批发")
    
    # 时间字段
    createtime = Column(Integer, comment="创建时间")
    updatetime = Column(Integer, comment="更新时间")
    
    # 关联关系
    stock_out = relationship("StockOut", back_populates="items")
    jewelry = relationship("Jewelry", back_populates="stock_out_items") 