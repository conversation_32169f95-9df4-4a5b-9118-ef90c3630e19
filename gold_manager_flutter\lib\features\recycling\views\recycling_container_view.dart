import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/theme/app_theme.dart';
import '../../../core/widgets/tab_manager.dart';
import '../controllers/recycling_tab_controller.dart';

/// 回收单管理容器页面
/// 负责管理回收单管理的各个子页面切换，支持多标签页功能
class RecyclingContainerView extends StatefulWidget {
  const RecyclingContainerView({super.key});

  @override
  State<RecyclingContainerView> createState() => _RecyclingContainerViewState();
}

class _RecyclingContainerViewState extends State<RecyclingContainerView> {
  late final RecyclingTabController recyclingTabController;

  @override
  void initState() {
    super.initState();
    // 尝试获取已注册的RecyclingTabController，如果不存在则创建
    try {
      recyclingTabController = Get.find<RecyclingTabController>();
    } catch (e) {
      // 如果没有找到，则创建一个新的（备用方案）
      recyclingTabController = Get.put(RecyclingTabController());
    }
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: AppTheme.backgroundColor,
      child: Column(
        children: [
          // 标签页管理器
          Expanded(
            child: TabManager(
              controller: recyclingTabController.tabManager,
              backgroundColor: AppTheme.backgroundColor,
              activeTabColor: Colors.white,
              inactiveTabColor: Colors.grey[100],
            ),
          ),
        ],
      ),
    );
  }
}
