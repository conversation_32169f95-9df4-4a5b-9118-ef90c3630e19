import 'package:get/get.dart';
import '../../../core/config/app_config.dart';
import '../../../core/services/api_service.dart';
import '../../../models/recycling/recycling_model.dart';

/// 旧料分类服务类，处理旧料分类相关业务逻辑
class CategoryService extends GetxService {
  final ApiService _apiService = Get.find<ApiService>();

  // 本地模拟数据
  final List<RecyclingCategory> _mockCategories = [
    const RecyclingCategory(id: 1, name: '黄金首饰', description: '黄金制作的首饰，包括项链、戒指、手镯等', isActive: true),
    const RecyclingCategory(id: 2, name: '银饰', description: '银制作的首饰，包括项链、戒指、手镯等', isActive: true),
    const RecyclingCategory(id: 3, name: '金银混合首饰', description: '同时含有金和银的首饰', isActive: true),
    const RecyclingCategory(id: 4, name: '铂金首饰', description: '铂金制作的首饰', isActive: true),
    const RecyclingCategory(id: 5, name: '钻石首饰', description: '含有钻石的首饰', isActive: false),
    const RecyclingCategory(id: 6, name: '其他宝石首饰', description: '含有其他宝石的首饰', isActive: true),
  ];

  /// 获取所有分类
  Future<List<RecyclingCategory>> getAllCategories() async {
    try {
      final response = await _apiService.get('${AppConfig.apiEndpoint['recycling']}/category/list');

      if (response.statusCode == 200) {
        final data = response.data;
        final List<dynamic> items = data['items'] ?? [];
        return items.map((item) => RecyclingCategory.fromJson(item)).toList();
      } else {
        throw Exception('获取分类列表失败: ${response.statusMessage}');
      }
    } catch (e) {
      Get.log('获取分类列表异常: $e', isError: true);
      rethrow;
    }
  }

  /// 获取活跃的分类
  Future<List<RecyclingCategory>> getActiveCategories() async {
    try {
      final response = await _apiService.get('${AppConfig.apiEndpoint['recycling']}/category/active');

      if (response.statusCode == 200) {
        final data = response.data;
        final List<dynamic> items = data['items'] ?? [];
        return items.map((item) => RecyclingCategory.fromJson(item)).toList();
      } else {
        throw Exception('获取活跃分类列表失败: ${response.statusMessage}');
      }
    } catch (e) {
      Get.log('获取活跃分类列表异常: $e', isError: true);
      rethrow;
    }
  }

  /// 获取分类详情
  Future<RecyclingCategory?> getCategoryDetail(int id) async {
    try {
      // 模拟网络延迟
      await Future.delayed(const Duration(milliseconds: 300));

      // 从模拟数据中找到对应ID的分类
      return _mockCategories.firstWhere(
        (category) => category.id == id,
        orElse: () => throw Exception('分类不存在'),
      );
    } catch (e) {
      Get.log('获取分类详情异常: $e', isError: true);
      return null;
    }
  }

  /// 添加分类
  Future<bool> addCategory(RecyclingCategory category) async {
    try {
      // 模拟网络延迟
      await Future.delayed(const Duration(milliseconds: 800));

      // 生成新ID
      final newId = _mockCategories.isEmpty ? 1 : _mockCategories.map((e) => e.id).reduce((a, b) => a > b ? a : b) + 1;

      // 创建新分类对象
      final newCategory = RecyclingCategory(
        id: newId,
        name: category.name,
        description: category.description,
        isActive: category.isActive,
      );

      // 添加到模拟数据
      _mockCategories.add(newCategory);

      return true;
    } catch (e) {
      Get.log('添加分类异常: $e', isError: true);
      return false;
    }
  }

  /// 更新分类
  Future<bool> updateCategory(RecyclingCategory category) async {
    try {
      // 模拟网络延迟
      await Future.delayed(const Duration(milliseconds: 800));

      // 查找现有分类
      final index = _mockCategories.indexWhere((c) => c.id == category.id);
      if (index == -1) {
        throw Exception('分类不存在');
      }

      // 更新分类
      _mockCategories[index] = category;

      return true;
    } catch (e) {
      Get.log('更新分类异常: $e', isError: true);
      return false;
    }
  }

  /// 删除分类
  Future<bool> deleteCategory(int id) async {
    try {
      // 模拟网络延迟
      await Future.delayed(const Duration(milliseconds: 800));

      // 查找现有分类
      final index = _mockCategories.indexWhere((c) => c.id == id);
      if (index == -1) {
        throw Exception('分类不存在');
      }

      // 删除分类
      _mockCategories.removeAt(index);

      return true;
    } catch (e) {
      Get.log('删除分类异常: $e', isError: true);
      return false;
    }
  }

  /// 更新分类状态
  Future<bool> updateCategoryStatus(int id, bool isActive) async {
    try {
      // 模拟网络延迟
      await Future.delayed(const Duration(milliseconds: 500));

      // 查找现有分类
      final index = _mockCategories.indexWhere((c) => c.id == id);
      if (index == -1) {
        throw Exception('分类不存在');
      }

      // 更新分类状态
      _mockCategories[index] = RecyclingCategory(
        id: _mockCategories[index].id,
        name: _mockCategories[index].name,
        description: _mockCategories[index].description,
        isActive: isActive,
      );

      return true;
    } catch (e) {
      Get.log('更新分类状态异常: $e', isError: true);
      return false;
    }
  }
}