# ♻️ 回收管理API文档

## 📖 功能概述

回收管理API提供完整的黄金珠宝回收业务流程管理，包括回收单创建、价格计算、折扣管理、状态控制等功能。

## 🎯 核心特性

### ✨ 主要功能
- **回收单管理**: 创建、查询、更新、删除回收单
- **智能单号生成**: 自动生成RECYYYYMMDD0001格式的回收单号
- **价格计算引擎**: 支持金重、银重、金价、银价的自动计算
- **折扣管理**: 支持折扣率计算和折后金额
- **客户管理**: 记录客户信息，支持会员关联
- **状态管理**: 正常 / 作废
- **统计分析**: 多维度业务统计和分析

### 🔄 业务流程
1. **创建回收单**: 录入客户信息和回收物品
2. **价格计算**: 系统自动计算各项金额
3. **折扣处理**: 根据折扣率计算最终金额
4. **完成回收**: 确认回收信息并生成单据
5. **统计分析**: 查看回收业务统计

## 📊 数据模型

### 回收单主表 (fa_recycling)
```sql
CREATE TABLE `fa_recycling` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '回收单ID',
  `recycle_no` varchar(50) NOT NULL COMMENT '回收单号',
  `store_id` int(11) NOT NULL COMMENT '门店ID',
  `member_id` int(11) DEFAULT NULL COMMENT '会员ID',
  `customer_name` varchar(50) DEFAULT NULL COMMENT '客户姓名',
  `phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `gold_weight` decimal(10,3) DEFAULT '0.000' COMMENT '金重(克)',
  `gold_price` decimal(10,2) DEFAULT '0.00' COMMENT '金价(克价)',
  `gold_amount` decimal(10,2) DEFAULT '0.00' COMMENT '金价金额',
  `silver_weight` decimal(10,3) DEFAULT '0.000' COMMENT '银重(克)',
  `silver_price` decimal(10,2) DEFAULT '0.00' COMMENT '银价(克价)',
  `silver_amount` decimal(10,2) DEFAULT '0.00' COMMENT '银价金额',
  `price` decimal(10,2) DEFAULT '0.00' COMMENT '回收价格',
  `total_amount` decimal(10,2) DEFAULT '0.00' COMMENT '总金额',
  `discount_amount` decimal(10,2) DEFAULT '0.00' COMMENT '折后总金额',
  `operator_id` int(11) NOT NULL COMMENT '操作员ID',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态:0=作废,1=正常',
  `remark` text COMMENT '备注',
  `createtime` int(10) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `recycle_no` (`recycle_no`)
);
```

### 回收明细表 (fa_recycling_item)
```sql
CREATE TABLE `fa_recycling_item` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '明细ID',
  `recycling_id` int(11) NOT NULL COMMENT '回收单ID',
  `name` varchar(100) NOT NULL COMMENT '物品名称',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `gold_weight` decimal(10,2) DEFAULT '0.00' COMMENT '金重(克)',
  `gold_price` decimal(10,2) DEFAULT '0.00' COMMENT '金价(克价)',
  `gold_amount` decimal(10,2) DEFAULT '0.00' COMMENT '金价金额',
  `silver_weight` decimal(10,2) DEFAULT '0.00' COMMENT '银重(克)',
  `silver_price` decimal(10,2) DEFAULT '0.00' COMMENT '银价(克价)',
  `silver_amount` decimal(10,2) DEFAULT '0.00' COMMENT '银价金额',
  `total_amount` decimal(10,2) DEFAULT '0.00' COMMENT '总金额',
  `discount_rate` decimal(10,2) DEFAULT '100.00' COMMENT '折扣率(%)',
  `discount_amount` decimal(10,2) DEFAULT '0.00' COMMENT '折后金额',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `createtime` int(10) DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`)
);
```

## 🔌 API接口

### 基础URL
```
http://localhost:8000/api/v1/recycling
```

### 1. 价格计算
```http
POST /api/v1/recycling/calculate-price
Content-Type: application/json

{
  "gold_weight": 10.5,
  "gold_price": 450.00,
  "silver_weight": 5.2,
  "silver_price": 8.50,
  "discount_rate": 95.00
}
```

### 2. 创建回收单
```http
POST /api/v1/recycling?operator_id=1
Content-Type: application/json

{
  "store_id": 1,
  "member_id": 1,
  "customer_name": "张三",
  "phone": "13800138000",
  "price": 5000.00,
  "remark": "黄金回收",
  "items": [
    {
      "name": "黄金戒指",
      "category_id": 1,
      "gold_weight": 10.5,
      "gold_price": 450.00,
      "silver_weight": 0.0,
      "silver_price": 0.0,
      "discount_rate": 95.00,
      "remark": "成色良好"
    }
  ]
}
```

### 3. 获取回收单列表
```http
GET /api/v1/recycling?page=1&page_size=20&status=1&keyword=REC20241220
```

**查询参数:**
- `page`: 页码 (默认: 1)
- `page_size`: 每页数量 (默认: 20)
- `keyword`: 关键词搜索(单号、客户姓名、备注)
- `store_id`: 门店ID筛选
- `status`: 状态筛选(0=作废,1=正常)
- `operator_id`: 操作员ID筛选
- `member_id`: 会员ID筛选
- `start_date`: 开始日期(YYYY-MM-DD)
- `end_date`: 结束日期(YYYY-MM-DD)
- `min_amount`: 最小金额
- `max_amount`: 最大金额

### 4. 获取回收单详情
```http
GET /api/v1/recycling/1
GET /api/v1/recycling/by-no/REC202412200001
```

### 5. 更新回收单
```http
PUT /api/v1/recycling/1
Content-Type: application/json

{
  "customer_name": "李四",
  "phone": "13900139000",
  "member_id": 2,
  "price": 5200.00,
  "remark": "更新客户信息"
}
```

### 6. 删除回收单
```http
DELETE /api/v1/recycling/1
```

### 7. 更新回收单状态
```http
PATCH /api/v1/recycling/1/status
Content-Type: application/json

{
  "status": 0,
  "remark": "作废原因"
}
```

### 8. 批量更新状态
```http
PATCH /api/v1/recycling/batch-status
Content-Type: application/json

{
  "recycling_ids": [1, 2, 3],
  "status": 0,
  "remark": "批量作废"
}
```

### 9. 获取回收统计
```http
GET /api/v1/recycling/statistics/summary
```

## 📈 状态说明

### 回收单状态
- **0 - 作废**: 回收单已作废
- **1 - 正常**: 回收单正常有效

## 🔒 业务规则

### 权限控制
1. **创建回收单**: 需要有效的操作员ID
2. **修改回收单**: 只有正常状态的回收单才能修改
3. **删除回收单**: 只有正常状态的回收单才能删除

### 数据验证
1. **门店验证**: 门店必须存在且状态正常
2. **分类验证**: 所有回收物品分类必须存在于系统中
3. **操作员验证**: 操作员必须存在且状态正常
4. **会员验证**: 如果关联会员，会员必须存在

### 自动计算
1. **金额计算**: 自动计算金价金额、银价金额、总金额
2. **折扣计算**: 自动计算折扣金额和折后金额
3. **重量统计**: 自动统计总金重、总银重

## 📊 统计功能

### 基础统计
- 总回收单数
- 正常/作废数量
- 总回收物品数
- 总金重、总银重
- 总金额、折后金额

### 分布统计
- 状态分布统计
- 门店分布统计
- 分类分布统计

### 业务指标
- 平均回收金额
- 最大/最小回收金额

## 🚀 使用示例

### 完整回收流程示例
```python
# 1. 价格计算
price_calc = {
    "gold_weight": 10.5,
    "gold_price": 450.00,
    "silver_weight": 5.2,
    "silver_price": 8.50,
    "discount_rate": 95.00
}
calc_result = requests.post("http://localhost:8000/api/v1/recycling/calculate-price", json=price_calc)

# 2. 创建回收单
recycling_data = {
    "store_id": 1,
    "customer_name": "张三",
    "phone": "13800138000",
    "items": [
        {
            "name": "黄金戒指",
            "category_id": 1,
            "gold_weight": 10.5,
            "gold_price": 450.00,
            "discount_rate": 95.00
        }
    ]
}
response = requests.post("http://localhost:8000/api/v1/recycling?operator_id=1", json=recycling_data)

# 3. 查看统计
stats = requests.get("http://localhost:8000/api/v1/recycling/statistics/summary")
```

## 🔧 技术特点

- **高性能**: 使用SQLAlchemy优化的数据库查询
- **数据安全**: 完善的权限控制和数据验证
- **易于集成**: RESTful API设计，便于前端集成
- **实时计算**: 价格和金额实时计算
- **中文化**: 完全中文化的API文档和错误信息

---

**文档版本**: v1.0  
**最后更新**: 2025-01-25  
**维护团队**: 黄金珠宝管理系统开发团队
