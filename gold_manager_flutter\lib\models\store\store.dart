
/// 门店模型
class Store {
  /// 门店ID
  final int id;
  
  /// 门店名称
  final String name;
  
  /// 门店编码
  final String? code;
  
  /// 门店地址
  final String? address;
  
  /// 联系电话
  final String? phone;
  
  /// 负责人
  final String? manager;
  
  /// 构造函数
  const Store({
    required this.id,
    required this.name,
    this.code,
    this.address,
    this.phone,
    this.manager,
  });
  
  /// 从JSON构造
  factory Store.fromJson(Map<String, dynamic> json) {
    return Store(
      id: json['id'],
      name: json['name'],
      code: json['code'],
      address: json['address'],
      phone: json['phone'],
      manager: json['manager'],
    );
  }
  
  /// 转为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'code': code,
      'address': address,
      'phone': phone,
      'manager': manager,
    };
  }
  
  /// 复制并修改属性
  Store copyWith({
    int? id,
    String? name,
    String? code,
    String? address,
    String? phone,
    String? manager,
  }) {
    return Store(
      id: id ?? this.id,
      name: name ?? this.name,
      code: code ?? this.code,
      address: address ?? this.address,
      phone: phone ?? this.phone,
      manager: manager ?? this.manager,
    );
  }
} 