import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../models/recycling/recycling_model.dart';
import '../../../../core/services/storage_service.dart';
import '../../services/recycling_service.dart';
import 'recycling_detail_dialog.dart';

/// API数据测试对话框
/// 用于测试API返回的数据结构解析
class ApiTestDialog extends StatelessWidget {
  const ApiTestDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('API数据结构测试'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            ElevatedButton(
              onPressed: _testApiDataStructure,
              child: const Text('测试API数据结构解析'),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _testMockDataStructure,
              child: const Text('测试模拟数据结构解析'),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _testRealApiCall,
              child: const Text('测试真实API调用'),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _checkAuthStatus,
              child: const Text('检查认证状态'),
            ),
          ],
        ),
      ),
    );
  }

  /// 测试API数据结构
  void _testApiDataStructure() {
    // 模拟API返回的数据结构（基于后端schema）
    final apiResponseData = {
      'id': 1,
      'recycle_no': 'REC202506190002',
      'store_id': 1,
      'member_id': null,
      'customer_name': '测试客户',
      'phone': '13812345678',
      'gold_weight': 10.5,
      'gold_price': 470.0,
      'gold_amount': 4935.0,
      'silver_weight': 0.0,
      'silver_price': 0.0,
      'silver_amount': 0.0,
      'price': 470.0,
      'total_amount': 470.0,
      'discount_amount': 470.0,
      'operator_id': 1,
      'status': 1,
      'remark': '测试一下',
      'createtime': 1734590649,
      'updatetime': 1734590649,
      'item_count': 1,
      'store_name': '齐齐哈尔总子',
      'member_name': null,
      'operator_name': 'Admin',
      'status_text': '已处理',
      'items': [
        {
          'id': 1,
          'recycling_id': 1,
          'name': '黄金项链',
          'category_id': 1,
          'gold_weight': 10.5,
          'gold_price': 470.0,
          'gold_amount': 4935.0,
          'silver_weight': 2.0,
          'silver_price': 8.0,
          'silver_amount': 16.0,
          'total_amount': 470.0,
          'discount_rate': 95.0,
          'discount_amount': 23.5,
          'remark': '成色不错',
          'createtime': 1734590649,
          'category_name': '黄金首饰'
        }
      ]
    };

    print('🧪 开始测试API数据结构解析');
    print('📊 API响应数据: $apiResponseData');

    try {
      final order = RecyclingOrder.fromJson(apiResponseData);
      print('✅ API数据解析成功');
      print('📦 回收单: ${order.orderNo}');
      print('📦 明细数量: ${order.items.length}');
      
      // 显示详情对话框
      Get.dialog(
        RecyclingDetailDialog(recyclingOrder: order),
        barrierDismissible: true,
      );
    } catch (e) {
      print('❌ API数据解析失败: $e');
      Get.snackbar(
        '解析失败',
        'API数据解析失败: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 测试模拟数据结构
  void _testMockDataStructure() {
    // 模拟前端模拟数据的结构
    final mockDataStructure = {
      'id': 1001,
      'order_no': 'RC20241219001',
      'order_date': DateTime.now().millisecondsSinceEpoch ~/ 1000,
      'customer_id': 101,
      'customer_name': '张三',
      'customer_phone': '13800138000',
      'total_weight': 15.5,
      'total_amount': 5890.0,
      'item_count': 1,
      'remark': '模拟数据测试',
      'status': 0,
      'created_at': DateTime.now().millisecondsSinceEpoch ~/ 1000,
      'created_by': 1,
      'creator_name': '系统管理员',
      'store_id': 1,
      'store_name': '总店',
      'items': [
        {
          'id': 2001,
          'order_id': 1001,
          'category_id': 1,
          'category_name': '黄金首饰',
          'name': '金饰品 #1',
          'gold_weight': 12.0,
          'gold_price': 450.0,
          'gold_amount': 5400.0,
          'silver_weight': 3.5,
          'silver_price': 8.0,
          'silver_amount': 28.0,
          'total_amount': 5890.0,
          'discount_rate': 95.0,
          'discount_amount': 294.5,
          'photo_url': 'assets/images/samples/gold_item_1.jpg',
          'status': 0,
        }
      ]
    };

    print('🧪 开始测试模拟数据结构解析');
    print('📊 模拟数据: $mockDataStructure');

    try {
      final order = RecyclingOrder.fromJson(mockDataStructure);
      print('✅ 模拟数据解析成功');
      print('📦 回收单: ${order.orderNo}');
      print('📦 明细数量: ${order.items.length}');
      
      // 显示详情对话框
      Get.dialog(
        RecyclingDetailDialog(recyclingOrder: order),
        barrierDismissible: true,
      );
    } catch (e) {
      print('❌ 模拟数据解析失败: $e');
      Get.snackbar(
        '解析失败',
        '模拟数据解析失败: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 测试真实API调用
  void _testRealApiCall() async {
    print('🧪 开始测试真实API调用');

    try {
      // 显示加载对话框
      Get.dialog(
        const Center(
          child: Card(
            child: Padding(
              padding: EdgeInsets.all(20),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('正在调用真实API...'),
                ],
              ),
            ),
          ),
        ),
        barrierDismissible: false,
      );

      final recyclingService = RecyclingService();

      // 测试获取回收单详情 - 使用ID 1
      print('🚀 调用getRecyclingOrderDetail(1)');
      final order = await recyclingService.getRecyclingOrderDetail(1);

      // 关闭加载对话框
      if (Get.isDialogOpen == true) {
        Get.back();
      }

      print('✅ API调用成功');
      print('📦 回收单: ${order.orderNo}');
      print('📦 明细数量: ${order.items.length}');

      // 显示详情对话框
      Get.dialog(
        RecyclingDetailDialog(recyclingOrder: order),
        barrierDismissible: true,
      );

    } catch (e) {
      print('❌ 真实API调用失败: $e');

      // 关闭加载对话框
      if (Get.isDialogOpen == true) {
        Get.back();
      }

      Get.snackbar(
        'API调用失败',
        '真实API调用失败: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 检查认证状态
  void _checkAuthStatus() {
    print('🔍 检查认证状态');

    final storageService = Get.find<StorageService>();
    final token = storageService.getToken();
    final refreshToken = storageService.getRefreshToken();
    final userId = storageService.getUserId();
    final userName = storageService.getUserName();
    final storeId = storageService.getStoreId();
    final storeName = storageService.getStoreName();

    print('🔐 认证信息:');
    print('   - Token: ${token != null ? '${token.substring(0, 20)}...' : 'null'}');
    print('   - RefreshToken: ${refreshToken != null ? '${refreshToken.substring(0, 20)}...' : 'null'}');
    print('   - UserId: $userId');
    print('   - UserName: $userName');
    print('   - StoreId: $storeId');
    print('   - StoreName: $storeName');

    String authStatus = '';
    if (token != null && token.isNotEmpty) {
      authStatus = '✅ 已认证\n';
      authStatus += 'Token: ${token.substring(0, 20)}...\n';
      authStatus += 'User: $userName (ID: $userId)\n';
      authStatus += 'Store: $storeName (ID: $storeId)';
    } else {
      authStatus = '❌ 未认证\nToken为空或不存在';
    }

    Get.dialog(
      AlertDialog(
        title: const Text('认证状态'),
        content: Text(authStatus),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
}
