import '../common/enums.dart';
import '../jewelry/jewelry_category.dart';
import '../jewelry/jewelry.dart';

/// 旧料回收明细模型
/// 用于在出库单中记录旧料回收信息
class OldMaterialItem {
  /// 旧料条码
  final String barcode;

  /// 回收方式
  final OldMaterialRecyclingType recyclingType;

  /// 成色
  final MetalPurity purity;

  /// 分类ID
  final int categoryId;

  /// 分类名称
  final String? categoryName;

  /// 金重(克)
  final double goldWeight;

  /// 回收金价(元/克)
  final double goldPrice;

  /// 银重(克)
  final double silverWeight;

  /// 回收银价(元/克)
  final double silverPrice;

  /// 折扣(%)
  final double discount;

  /// 备注
  final String? remark;

  /// 关联分类对象
  final JewelryCategory? category;

  /// 构造函数
  const OldMaterialItem({
    required this.barcode,
    required this.recyclingType,
    required this.purity,
    required this.categoryId,
    this.categoryName,
    this.goldWeight = 0.0,
    this.goldPrice = 0.0,
    this.silverWeight = 0.0,
    this.silverPrice = 0.0,
    this.discount = 100.0,
    this.remark,
    this.category,
  });

  /// 总重量(克) - 自动计算
  double get totalWeight => goldWeight + silverWeight;

  /// 应付金额 - 自动计算
  /// 计算公式：(金重 × 回收金价 + 银重 × 回收银价) × (折扣/100)
  double get payableAmount {
    final goldAmount = goldWeight * goldPrice;
    final silverAmount = silverWeight * silverPrice;
    final totalAmount = goldAmount + silverAmount;
    return totalAmount * (discount / 100);
  }

  /// 从JSON构造
  factory OldMaterialItem.fromJson(Map<String, dynamic> json) {
    // 安全的数字转换函数
    double parseDouble(dynamic value) {
      if (value == null) return 0.0;
      if (value is double) return value;
      if (value is int) return value.toDouble();
      if (value is String) {
        return double.tryParse(value) ?? 0.0;
      }
      return 0.0;
    }

    return OldMaterialItem(
      barcode: json['barcode'] ?? '',
      recyclingType: OldMaterialRecyclingType.fromValue(
        json['recycling_type'] ?? 'by_weight',
      ),
      purity: MetalPurity.fromValue(json['purity'] ?? '金包银'),
      categoryId: json['category_id'] ?? 0,
      categoryName: json['category_name'],
      goldWeight: parseDouble(json['gold_weight']),
      goldPrice: parseDouble(json['gold_price']),
      silverWeight: parseDouble(json['silver_weight']),
      silverPrice: parseDouble(json['silver_price']),
      discount: parseDouble(json['discount'] ?? 100.0),
      remark: json['remark'],
      category: json['category'] != null
          ? JewelryCategory.fromJson(json['category'])
          : null,
    );
  }

  /// 转为JSON
  Map<String, dynamic> toJson() {
    return {
      'barcode': barcode,
      'recycling_type': recyclingType.value,
      'purity': purity.value,
      'category_id': categoryId,
      'category_name': categoryName,
      'gold_weight': goldWeight,
      'gold_price': goldPrice,
      'silver_weight': silverWeight,
      'silver_price': silverPrice,
      'discount': discount,
      'remark': remark,
    };
  }

  /// 复制并修改属性
  OldMaterialItem copyWith({
    String? barcode,
    OldMaterialRecyclingType? recyclingType,
    MetalPurity? purity,
    int? categoryId,
    String? categoryName,
    double? goldWeight,
    double? goldPrice,
    double? silverWeight,
    double? silverPrice,
    double? discount,
    String? remark,
    JewelryCategory? category,
  }) {
    return OldMaterialItem(
      barcode: barcode ?? this.barcode,
      recyclingType: recyclingType ?? this.recyclingType,
      purity: purity ?? this.purity,
      categoryId: categoryId ?? this.categoryId,
      categoryName: categoryName ?? this.categoryName,
      goldWeight: goldWeight ?? this.goldWeight,
      goldPrice: goldPrice ?? this.goldPrice,
      silverWeight: silverWeight ?? this.silverWeight,
      silverPrice: silverPrice ?? this.silverPrice,
      discount: discount ?? this.discount,
      remark: remark ?? this.remark,
      category: category ?? this.category,
    );
  }

  /// 转换为Jewelry对象用于出库单显示
  /// 旧料回收在出库单中显示为负价格
  Jewelry toJewelry() {
    // 🎯 优化商品名称格式生成逻辑
    // 优先级：备注内容 > 成色 > 默认名称
    String displayName;

    if (remark != null && remark!.trim().isNotEmpty) {
      // 场景1：有备注内容，显示"回收-{备注内容}"
      displayName = '回收-${remark!.trim()}';
    } else if (purity.label.isNotEmpty) {
      // 场景2：无备注但有成色，显示"回收-{成色}"
      displayName = '回收-${purity.label}';
    } else {
      // 场景3：备注和成色都为空，使用默认名称
      displayName = '回收-旧料回收';
    }

    return Jewelry(
      id: 0, // 旧料回收项没有真实的首饰ID
      barcode: barcode,
      name: displayName,
      categoryId: categoryId,
      goldWeight: goldWeight,
      goldPrice: goldPrice,
      silverWeight: silverWeight,
      silverPrice: silverPrice,
      totalWeight: totalWeight,
      workPrice: 0.0, // 旧料回收没有工费
      retailWorkPrice: 0.0,
      wholesaleWorkPrice: 0.0,
      pieceWorkPrice: 0.0,
      salePrice: -payableAmount, // 负价格表示回收支出
      storeId: 0,
      status: JewelryStatus.offShelf, // 旧料回收项标记为下架状态
      createTime: DateTime.now(),
      category: category,
    );
  }

  @override
  String toString() {
    return 'OldMaterialItem{barcode: $barcode, purity: ${purity.label}, '
        'goldWeight: ${goldWeight}g, silverWeight: ${silverWeight}g, '
        'payableAmount: ${payableAmount.toStringAsFixed(2)}元}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is OldMaterialItem && other.barcode == barcode;
  }

  @override
  int get hashCode => barcode.hashCode;
}
