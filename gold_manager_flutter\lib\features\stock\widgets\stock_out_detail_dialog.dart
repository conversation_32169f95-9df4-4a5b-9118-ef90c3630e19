import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../core/theme/app_theme.dart';
import '../../../core/utils/logger.dart';
import '../../../core/utils/number_formatter.dart';
import '../../../models/stock/stock_out.dart';
import '../../../models/stock/stock_out_item.dart';
import '../../../models/common/document_status.dart';
import '../../../core/widgets/responsive_layout.dart';
import '../../../services/stock_out_service.dart';
import '../../../widgets/loading_state.dart';

/// 出库单详情查看对话框
/// 参考旧料回收详情界面设计，保持界面一致性
class StockOutDetailDialog extends StatefulWidget {
  final int stockOutId;

  const StockOutDetailDialog({
    super.key,
    required this.stockOutId,
  });

  @override
  State<StockOutDetailDialog> createState() => _StockOutDetailDialogState();
}

class _StockOutDetailDialogState extends State<StockOutDetailDialog> {
  late final StockOutService _stockOutService;
  StockOut? _stockOut;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    // 确保服务已注册
    if (!Get.isRegistered<StockOutService>()) {
      Get.put<StockOutService>(StockOutService());
    }
    _stockOutService = Get.find<StockOutService>();
    _loadStockOutDetail();
  }

  /// 加载出库单详情
  Future<void> _loadStockOutDetail() async {
    try {
      LoggerService.d('🔍 加载出库单详情: ID=${widget.stockOutId}');
      final stockOut = await _stockOutService.getStockOutDetail(widget.stockOutId);
      setState(() {
        _stockOut = stockOut;
        _isLoading = false;
      });
      LoggerService.d('📊 出库单数据: 单号=${stockOut.stockOutNo}, 总金额=${stockOut.totalAmount}');
      LoggerService.d('📦 明细数量: ${stockOut.items?.length ?? 0}');
    } catch (e) {
      LoggerService.e('加载出库单详情失败', e);
      setState(() {
        _isLoading = false;
      });
      Get.snackbar(
        '加载失败',
        '无法加载出库单详情: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: const EdgeInsets.all(16),
      child: Container(
        constraints: const BoxConstraints(
          maxWidth: 1200,
          maxHeight: 800,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: _isLoading
            ? const LoadingState(text: '加载中...', timeoutSeconds: 30)
            : _stockOut == null
                ? _buildErrorState()
                : Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      _buildHeader(context),
                      const Divider(height: 1),
                      Expanded(
                        child: _buildContent(context),
                      ),
                      const Divider(height: 1),
                      _buildFooter(context),
                    ],
                  ),
      ),
    );
  }

  /// 构建错误状态
  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          const Text(
            '加载出库单详情失败',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          ElevatedButton(
            onPressed: () => Get.back(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  /// 构建对话框头部
  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          Icon(
            Icons.outbox,
            color: Colors.blue[600],
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '出库单详情',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _stockOut!.stockOutNo,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => Get.back(),
            icon: const Icon(Icons.close),
            tooltip: '关闭',
          ),
        ],
      ),
    );
  }

  /// 构建主要内容区域
  Widget _buildContent(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildBasicInfo(),
          const SizedBox(height: 24),
          _buildItemsList(),
          const SizedBox(height: 24),
          _buildPaymentInfo(),
          const SizedBox(height: 24),
          _buildSummaryInfo(),
        ],
      ),
    );
  }

  /// 构建基本信息卡片
  Widget _buildBasicInfo() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
                SizedBox(width: 8),
                Text(
                  '基本信息',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ScreenTypeLayout(
              mobile: _buildBasicInfoMobile(),
              tablet: _buildBasicInfoDesktop(),
              desktop: _buildBasicInfoDesktop(),
            ),
          ],
        ),
      ),
    );
  }

  /// 移动端基本信息布局
  Widget _buildBasicInfoMobile() {
    return Column(
      children: [
        _buildInfoRow('出库单号', _stockOut!.stockOutNo, isHighlight: true),
        _buildInfoRow('客户', _stockOut!.customer ?? '散客'),
        _buildInfoRow('销售类型', _getSaleTypeText(_stockOut!.saleType)),
        _buildInfoRow('门店', _stockOut!.store?.name ?? '未知门店'),
        _buildInfoRow('出库时间', _formatDateTime(_stockOut!.createTime)),
        _buildInfoRow('操作员', _stockOut!.operatorName ?? '未知'),
        _buildInfoRow('状态', _stockOut!.status.label,
                     statusColor: _getStatusColor(_stockOut!.status)),
        if (_stockOut!.remark?.isNotEmpty == true)
          _buildInfoRow('备注', _stockOut!.remark!),
      ],
    );
  }

  /// 桌面端基本信息布局
  Widget _buildBasicInfoDesktop() {
    return Row(
      children: [
        Expanded(
          child: Column(
            children: [
              _buildInfoRow('出库单号', _stockOut!.stockOutNo, isHighlight: true),
              _buildInfoRow('客户', _stockOut!.customer ?? '散客'),
              _buildInfoRow('销售类型', _getSaleTypeText(_stockOut!.saleType)),
              _buildInfoRow('门店', _stockOut!.store?.name ?? '未知门店'),
            ],
          ),
        ),
        Expanded(
          child: Column(
            children: [
              _buildInfoRow('出库时间', _formatDateTime(_stockOut!.createTime)),
              _buildInfoRow('操作员', _stockOut!.operatorName ?? '未知'),
              _buildInfoRow('状态', _stockOut!.status.label,
                           statusColor: _getStatusColor(_stockOut!.status)),
              if (_stockOut!.remark?.isNotEmpty == true)
                _buildInfoRow('备注', _stockOut!.remark!),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建信息行
  Widget _buildInfoRow(String label, String value, {bool isHighlight = false, Color? statusColor}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: statusColor != null
                ? Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color: statusColor.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          value,
                          style: TextStyle(
                            fontSize: 14,
                            color: statusColor,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  )
                : Text(
                    value,
                    style: TextStyle(
                      fontSize: 14,
                      color: isHighlight ? AppTheme.primaryColor : Colors.black87,
                      fontWeight: isHighlight ? FontWeight.w600 : FontWeight.normal,
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  /// 构建商品明细列表
  Widget _buildItemsList() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.inventory_2_outlined,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Text(
                  '商品明细',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '共 ${_stockOut!.items?.length ?? 0} 件',
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppTheme.primaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ScreenTypeLayout(
              mobile: _buildItemsListMobile(),
              tablet: _buildItemsTable(),
              desktop: _buildItemsTable(),
            ),
          ],
        ),
      ),
    );
  }

  /// 移动端商品列表
  Widget _buildItemsListMobile() {
    if (_stockOut!.items == null || _stockOut!.items!.isEmpty) {
      return _buildEmptyItems();
    }

    return Column(
      children: _stockOut!.items!.map((item) {
        return _buildItemCard(item);
      }).toList(),
    );
  }

  /// 桌面端商品表格
  Widget _buildItemsTable() {
    if (_stockOut!.items == null || _stockOut!.items!.isEmpty) {
      return _buildEmptyItems();
    }

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          _buildTableHeader(),
          ..._stockOut!.items!.map((item) {
            return _buildTableRow(item);
          }),
        ],
      ),
    );
  }

  /// 构建表格头部
  Widget _buildTableHeader() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(8),
          topRight: Radius.circular(8),
        ),
      ),
      child: Row(
        children: [
          Expanded(flex: 2, child: _buildHeaderCell('条码')),
          Expanded(flex: 3, child: _buildHeaderCell('商品名称')),
          Expanded(flex: 1, child: _buildHeaderCell('分类')),
          Expanded(flex: 1, child: _buildHeaderCell('金重(g)')),
          Expanded(flex: 1, child: _buildHeaderCell('金价(¥)')),
          Expanded(flex: 1, child: _buildHeaderCell('银重(g)')),
          Expanded(flex: 1, child: _buildHeaderCell('银价(¥)')),
          Expanded(flex: 1, child: _buildHeaderCell('工费(¥)')),
          Expanded(flex: 1, child: _buildHeaderCell('件工费(¥)')),
          Expanded(flex: 2, child: _buildHeaderCell('金额(¥)')),
        ],
      ),
    );
  }

  /// 构建表格头部单元格
  Widget _buildHeaderCell(String text) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
      child: Text(
        text,
        style: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          color: Colors.black87,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  /// 构建表格数据行
  Widget _buildTableRow(StockOutItem item) {
    return Container(
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(color: Colors.grey[300]!),
        ),
      ),
      child: Row(
        children: [
          Expanded(flex: 2, child: _buildDataCell(item.barcode ?? '-')),
          Expanded(flex: 3, child: _buildDataCell(item.jewelry?.name ?? '未知商品')),
          Expanded(flex: 1, child: _buildDataCell(item.jewelry?.categoryName ?? '-')),
          Expanded(flex: 1, child: _buildDataCell(NumberFormatter.formatWeight(item.goldWeight))),
          Expanded(flex: 1, child: _buildDataCell(item.goldPrice.toStringAsFixed(0))),
          Expanded(flex: 1, child: _buildDataCell(NumberFormatter.formatWeight(item.silverWeight))),
          Expanded(flex: 1, child: _buildDataCell(item.silverPrice.toStringAsFixed(0))),
          Expanded(flex: 1, child: _buildDataCell(NumberFormatter.formatCurrency(item.workPrice, showSymbol: false))),
          Expanded(flex: 1, child: _buildDataCell(NumberFormatter.formatCurrency(item.pieceWorkPrice, showSymbol: false))),
          Expanded(flex: 2, child: _buildDataCell(NumberFormatter.formatCurrency(item.amount))),
        ],
      ),
    );
  }

  /// 构建数据单元格
  Widget _buildDataCell(String text) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
      child: Text(
        text,
        style: const TextStyle(
          fontSize: 14,
          color: Colors.black87,
        ),
        textAlign: TextAlign.center,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  /// 构建商品卡片（移动端）
  Widget _buildItemCard(StockOutItem item) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            item.jewelry?.name ?? '未知商品',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: _buildItemInfo('条码', item.barcode ?? '-'),
              ),
              Expanded(
                child: _buildItemInfo('分类', item.jewelry?.categoryName ?? '-'),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              Expanded(
                child: _buildItemInfo('重量', NumberFormatter.formatWeight(item.totalWeight)),
              ),
              Expanded(
                child: _buildItemInfo('金价', '¥${item.goldPrice.toStringAsFixed(0)}'),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              Expanded(
                child: _buildItemInfo('工费', NumberFormatter.formatCurrency(item.workPrice + item.pieceWorkPrice)),
              ),
              Expanded(
                child: _buildItemInfo('金额', NumberFormatter.formatCurrency(item.amount)),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建物品信息项
  Widget _buildItemInfo(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Text(
            '$label: ',
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.black87,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建空物品状态
  Widget _buildEmptyItems() {
    return Container(
      padding: const EdgeInsets.all(40),
      child: const Column(
        children: [
          Icon(
            Icons.inventory_2_outlined,
            size: 48,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text(
            '暂无商品明细',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建支付信息
  Widget _buildPaymentInfo() {
    if (_stockOut!.paymentStatus != 1) {
      return const SizedBox.shrink();
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(
                  Icons.payment,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
                SizedBox(width: 8),
                Text(
                  '支付信息',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ScreenTypeLayout(
              mobile: _buildPaymentInfoMobile(),
              tablet: _buildPaymentInfoDesktop(),
              desktop: _buildPaymentInfoDesktop(),
            ),
          ],
        ),
      ),
    );
  }

  /// 移动端支付信息布局
  Widget _buildPaymentInfoMobile() {
    return Column(
      children: [
        if (_stockOut!.paymentTime != null)
          _buildInfoRow('支付时间', _formatDateTime(_stockOut!.paymentTime!)),
        if (_stockOut!.cashAmount != null && _stockOut!.cashAmount! > 0)
          _buildInfoRow('现金', NumberFormatter.formatCurrency(_stockOut!.cashAmount!)),
        if (_stockOut!.wechatAmount != null && _stockOut!.wechatAmount! > 0)
          _buildInfoRow('微信', NumberFormatter.formatCurrency(_stockOut!.wechatAmount!)),
        if (_stockOut!.alipayAmount != null && _stockOut!.alipayAmount! > 0)
          _buildInfoRow('支付宝', NumberFormatter.formatCurrency(_stockOut!.alipayAmount!)),
        if (_stockOut!.cardAmount != null && _stockOut!.cardAmount! > 0)
          _buildInfoRow('刷卡', NumberFormatter.formatCurrency(_stockOut!.cardAmount!)),
        if (_stockOut!.discountAmount != null && _stockOut!.discountAmount! > 0)
          _buildInfoRow('抹零', NumberFormatter.formatCurrency(_stockOut!.discountAmount!)),
        _buildInfoRow('应收', NumberFormatter.formatCurrency(_stockOut!.totalAmount)),
        if (_stockOut!.actualAmount != null)
          _buildInfoRow('实收', NumberFormatter.formatCurrency(_stockOut!.actualAmount!), isHighlight: true),
        if (_stockOut!.paymentRemark?.isNotEmpty == true)
          _buildInfoRow('支付备注', _stockOut!.paymentRemark!),
      ],
    );
  }

  /// 桌面端支付信息布局
  Widget _buildPaymentInfoDesktop() {
    return Row(
      children: [
        Expanded(
          child: Column(
            children: [
              if (_stockOut!.paymentTime != null)
                _buildInfoRow('支付时间', _formatDateTime(_stockOut!.paymentTime!)),
              if (_stockOut!.cashAmount != null && _stockOut!.cashAmount! > 0)
                _buildInfoRow('现金', NumberFormatter.formatCurrency(_stockOut!.cashAmount!)),
              if (_stockOut!.wechatAmount != null && _stockOut!.wechatAmount! > 0)
                _buildInfoRow('微信', NumberFormatter.formatCurrency(_stockOut!.wechatAmount!)),
              if (_stockOut!.alipayAmount != null && _stockOut!.alipayAmount! > 0)
                _buildInfoRow('支付宝', NumberFormatter.formatCurrency(_stockOut!.alipayAmount!)),
            ],
          ),
        ),
        Expanded(
          child: Column(
            children: [
              if (_stockOut!.cardAmount != null && _stockOut!.cardAmount! > 0)
                _buildInfoRow('刷卡', NumberFormatter.formatCurrency(_stockOut!.cardAmount!)),
              if (_stockOut!.discountAmount != null && _stockOut!.discountAmount! > 0)
                _buildInfoRow('抹零', NumberFormatter.formatCurrency(_stockOut!.discountAmount!)),
              _buildInfoRow('应收', NumberFormatter.formatCurrency(_stockOut!.totalAmount)),
              if (_stockOut!.actualAmount != null)
                _buildInfoRow('实收', NumberFormatter.formatCurrency(_stockOut!.actualAmount!), isHighlight: true),
              if (_stockOut!.paymentRemark?.isNotEmpty == true)
                _buildInfoRow('支付备注', _stockOut!.paymentRemark!),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建汇总信息
  Widget _buildSummaryInfo() {
    // 计算总重量
    double totalWeight = 0.0;
    if (_stockOut!.items != null) {
      for (var item in _stockOut!.items!) {
        totalWeight += item.totalWeight;
      }
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(
                  Icons.calculate_outlined,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
                SizedBox(width: 8),
                Text(
                  '汇总信息',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ScreenTypeLayout(
              mobile: _buildSummaryMobile(totalWeight),
              tablet: _buildSummaryDesktop(totalWeight),
              desktop: _buildSummaryDesktop(totalWeight),
            ),
          ],
        ),
      ),
    );
  }

  /// 移动端汇总信息
  Widget _buildSummaryMobile(double totalWeight) {
    return Column(
      children: [
        _buildSummaryItem('总件数', '${_stockOut!.items?.length ?? 0}件', Icons.inventory),
        _buildSummaryItem('总金额', NumberFormatter.formatCurrency(_stockOut!.actualAmount ?? _stockOut!.totalAmount), Icons.attach_money),
        _buildSummaryItem('总重量', '${totalWeight.toStringAsFixed(2)}g', Icons.scale),
      ],
    );
  }

  /// 桌面端汇总信息
  Widget _buildSummaryDesktop(double totalWeight) {
    return Row(
      children: [
        Expanded(child: _buildSummaryItem('总件数', '${_stockOut!.items?.length ?? 0}件', Icons.inventory)),
        Expanded(child: _buildSummaryItem('总金额', NumberFormatter.formatCurrency(_stockOut!.actualAmount ?? _stockOut!.totalAmount), Icons.attach_money)),
        Expanded(child: _buildSummaryItem('总重量', '${totalWeight.toStringAsFixed(2)}g', Icons.scale)),
      ],
    );
  }

  /// 构建汇总项
  Widget _buildSummaryItem(String label, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            color: AppTheme.primaryColor,
            size: 20,
          ),
          const SizedBox(width: 8),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                label,
                style: const TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                ),
              ),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建底部按钮区域
  Widget _buildFooter(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('关闭'),
          ),
          const SizedBox(width: 12),
          ElevatedButton.icon(
            onPressed: _copyToClipboard,
            icon: const Icon(Icons.copy, size: 18),
            label: const Text('复制信息'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 复制出库单信息到剪贴板
  void _copyToClipboard() {
    final buffer = StringBuffer();
    buffer.writeln('=== 出库单详情 ===');
    buffer.writeln('出库单号: ${_stockOut!.stockOutNo}');
    buffer.writeln('客户: ${_stockOut!.customer ?? '散客'}');
    buffer.writeln('销售类型: ${_stockOut!.saleType ?? '零售'}');
    buffer.writeln('门店: ${_stockOut!.store?.name ?? '未知门店'}');
    buffer.writeln('出库时间: ${_formatDateTime(_stockOut!.createTime)}');
    buffer.writeln('操作员: ${_stockOut!.operatorName ?? '未知'}');
    buffer.writeln('状态: ${_stockOut!.status.label}');
    if (_stockOut!.remark?.isNotEmpty == true) {
      buffer.writeln('备注: ${_stockOut!.remark}');
    }
    buffer.writeln();
    buffer.writeln('=== 商品明细 ===');
    if (_stockOut!.items != null) {
      for (int i = 0; i < _stockOut!.items!.length; i++) {
        final item = _stockOut!.items![i];
        buffer.writeln('${i + 1}. ${item.jewelry?.name ?? '未知商品'}');
        buffer.writeln('   条码: ${item.barcode ?? '-'}');
        buffer.writeln('   分类: ${item.jewelry?.categoryName ?? '-'}');
        buffer.writeln('   重量: ${NumberFormatter.formatWeight(item.totalWeight)}');
        buffer.writeln('   金价: ¥${item.goldPrice.toStringAsFixed(0)}');
        buffer.writeln('   工费: ${NumberFormatter.formatCurrency(item.workPrice + item.pieceWorkPrice)}');
        buffer.writeln('   金额: ${NumberFormatter.formatCurrency(item.amount)}');
        buffer.writeln();
      }
    }
    
    // 计算总重量
    double totalWeight = 0.0;
    if (_stockOut!.items != null) {
      for (var item in _stockOut!.items!) {
        totalWeight += item.totalWeight;
      }
    }
    
    buffer.writeln('=== 汇总信息 ===');
    buffer.writeln('总件数: ${_stockOut!.items?.length ?? 0}件');
    buffer.writeln('总金额: ${NumberFormatter.formatCurrency(_stockOut!.totalAmount)}');
    buffer.writeln('总重量: ${totalWeight.toStringAsFixed(2)}g');

    if (_stockOut!.paymentStatus == 1) {
      buffer.writeln();
      buffer.writeln('=== 支付信息 ===');
      if (_stockOut!.paymentTime != null) {
        buffer.writeln('支付时间: ${_formatDateTime(_stockOut!.paymentTime!)}');
      }
      if (_stockOut!.cashAmount != null && _stockOut!.cashAmount! > 0) {
        buffer.writeln('现金: ${NumberFormatter.formatCurrency(_stockOut!.cashAmount!)}');
      }
      if (_stockOut!.wechatAmount != null && _stockOut!.wechatAmount! > 0) {
        buffer.writeln('微信: ${NumberFormatter.formatCurrency(_stockOut!.wechatAmount!)}');
      }
      if (_stockOut!.alipayAmount != null && _stockOut!.alipayAmount! > 0) {
        buffer.writeln('支付宝: ${NumberFormatter.formatCurrency(_stockOut!.alipayAmount!)}');
      }
      if (_stockOut!.cardAmount != null && _stockOut!.cardAmount! > 0) {
        buffer.writeln('刷卡: ${NumberFormatter.formatCurrency(_stockOut!.cardAmount!)}');
      }
      if (_stockOut!.discountAmount != null && _stockOut!.discountAmount! > 0) {
        buffer.writeln('抹零: ${NumberFormatter.formatCurrency(_stockOut!.discountAmount!)}');
      }
      if (_stockOut!.actualAmount != null) {
        buffer.writeln('实收: ${NumberFormatter.formatCurrency(_stockOut!.actualAmount!)}');
      }
    }

    Clipboard.setData(ClipboardData(text: buffer.toString()));

    Get.snackbar(
      '成功',
      '出库单信息已复制到剪贴板',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.green,
      colorText: Colors.white,
      duration: const Duration(seconds: 2),
    );
  }

  /// 格式化日期时间
  String _formatDateTime(DateTime dateTime) {
    return DateFormat('yyyy-MM-dd HH:mm:ss').format(dateTime);
  }

  /// 获取销售类型文本
  String _getSaleTypeText(String? saleType) {
    switch (saleType) {
      case 'retail':
        return '零售';
      case 'wholesale':
        return '批发';
      default:
        return '零售';
    }
  }

  /// 获取状态颜色
  Color _getStatusColor(DocumentStatus status) {
    switch (status) {
      case DocumentStatus.draft:
        return Colors.grey;
      case DocumentStatus.pending:
        return Colors.orange;
      case DocumentStatus.approved:
        return Colors.green;
      case DocumentStatus.rejected:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}