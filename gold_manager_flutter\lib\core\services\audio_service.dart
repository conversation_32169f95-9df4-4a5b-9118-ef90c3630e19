import 'dart:io' show Platform;
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/services.dart';
import '../utils/logger_service.dart';

/// 音频服务
/// 
/// 提供系统音效播放功能，包括成功、失败、警告等提示音
class AudioService {
  static final AudioService _instance = AudioService._internal();
  factory AudioService() => _instance;
  AudioService._internal();

  late final AudioPlayer _audioPlayer;
  bool _isInitialized = false;
  bool _isEnabled = true; // 音效开关

  /// 初始化音频服务
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _audioPlayer = AudioPlayer();
      _isInitialized = true;
      LoggerService.i('音频服务初始化成功');
    } catch (e) {
      LoggerService.e('音频服务初始化失败', e);
    }
  }

  /// 播放成功提示音
  Future<void> playSuccessSound() async {
    if (!_isEnabled) {
      LoggerService.d('🔇 音效已禁用，跳过播放成功提示音');
      return;
    }

    try {
      LoggerService.d('🔊 开始播放成功提示音...');

      // 🔑 Windows平台特定处理
      await _playSystemSoundWithFallback(SystemSoundType.click, '成功提示音');

      LoggerService.d('✅ 成功播放提示音');
    } catch (e) {
      LoggerService.e('❌ 播放成功提示音失败', e);
      // 最后的备用方案：使用AudioPlayer播放简单音效
      await _playFallbackSound();
    }
  }

  /// 播放失败提示音
  Future<void> playErrorSound() async {
    if (!_isEnabled) {
      LoggerService.d('🔇 音效已禁用，跳过播放失败提示音');
      return;
    }

    try {
      LoggerService.d('🔊 开始播放失败提示音...');

      // 🔑 Windows平台特定处理
      await _playSystemSoundWithFallback(SystemSoundType.alert, '失败提示音');

      LoggerService.d('✅ 失败提示音播放完成');
    } catch (e) {
      LoggerService.e('❌ 播放失败提示音失败', e);
      // 最后的备用方案：使用AudioPlayer播放简单音效
      await _playFallbackSound();
    }
  }

  /// 播放警告提示音
  Future<void> playWarningSound() async {
    if (!_isEnabled) {
      LoggerService.d('🔇 音效已禁用，跳过播放警告提示音');
      return;
    }

    try {
      LoggerService.d('🔊 开始播放警告提示音...');

      // 🔑 Windows平台特定处理
      await _playSystemSoundWithFallback(SystemSoundType.alert, '警告提示音');

      LoggerService.d('✅ 警告提示音播放完成');
    } catch (e) {
      LoggerService.e('❌ 播放警告提示音失败', e);
      // 最后的备用方案：使用AudioPlayer播放简单音效
      await _playFallbackSound();
    }
  }

  /// 播放点击音效
  Future<void> playClickSound() async {
    if (!_isEnabled || !_isInitialized) return;

    try {
      await SystemSound.play(SystemSoundType.click);
      LoggerService.d('播放点击音效');
    } catch (e) {
      LoggerService.e('播放点击音效失败', e);
    }
  }

  /// 设置音效开关
  void setEnabled(bool enabled) {
    _isEnabled = enabled;
    LoggerService.i('音效${enabled ? '开启' : '关闭'}');
  }

  /// 获取音效开关状态
  bool get isEnabled => _isEnabled;

  /// 释放资源
  Future<void> dispose() async {
    if (_isInitialized) {
      await _audioPlayer.dispose();
      _isInitialized = false;
      LoggerService.i('音频服务已释放');
    }
  }

  /// 播放自定义音频文件
  Future<void> playCustomSound(String assetPath) async {
    if (!_isEnabled || !_isInitialized) return;

    try {
      await _audioPlayer.play(AssetSource(assetPath));
      LoggerService.d('播放自定义音频: $assetPath');
    } catch (e) {
      LoggerService.e('播放自定义音频失败: $assetPath', e);
      // 如果自定义音频播放失败，回退到系统音效
      await SystemSound.play(SystemSoundType.click);
    }
  }

  /// 设置音量
  Future<void> setVolume(double volume) async {
    if (!_isInitialized) return;

    try {
      await _audioPlayer.setVolume(volume.clamp(0.0, 1.0));
      LoggerService.d('设置音量: $volume');
    } catch (e) {
      LoggerService.e('设置音量失败', e);
    }
  }

  /// 停止播放
  Future<void> stop() async {
    if (!_isInitialized) return;

    try {
      await _audioPlayer.stop();
      LoggerService.d('停止音频播放');
    } catch (e) {
      LoggerService.e('停止音频播放失败', e);
    }
  }

  /// 🔑 Windows平台特定的系统音效播放方法
  /// 包含平台检测和多重备用方案
  Future<void> _playSystemSoundWithFallback(SystemSoundType soundType, String soundName) async {
    // 检测平台信息
    String platformInfo = 'Unknown';
    if (kIsWeb) {
      platformInfo = 'Web';
    } else if (Platform.isWindows) {
      platformInfo = 'Windows';
    } else if (Platform.isMacOS) {
      platformInfo = 'macOS';
    } else if (Platform.isLinux) {
      platformInfo = 'Linux';
    } else if (Platform.isAndroid) {
      platformInfo = 'Android';
    } else if (Platform.isIOS) {
      platformInfo = 'iOS';
    }

    LoggerService.d('🖥️ 当前平台: $platformInfo');
    LoggerService.d('🔊 尝试播放系统音效: $soundName (${soundType.toString()})');

    try {
      // 第一次尝试：直接使用系统音效
      await SystemSound.play(soundType);
      LoggerService.d('✅ 系统音效播放成功: $soundName');

      // 🔑 Windows平台特殊处理：等待音效播放完成
      if (Platform.isWindows) {
        await Future.delayed(const Duration(milliseconds: 150));
        LoggerService.d('🔊 Windows平台音效播放等待完成');
      }
      return;
    } catch (e) {
      LoggerService.w('⚠️ 系统音效播放失败: $soundName, 错误: $e');

      // Windows平台特殊处理
      if (Platform.isWindows) {
        LoggerService.d('🔧 Windows平台，尝试备用音效方案...');

        try {
          // 尝试不同的系统音效类型
          if (soundType == SystemSoundType.click) {
            await SystemSound.play(SystemSoundType.alert);
            LoggerService.d('✅ Windows备用音效播放成功: alert代替click');
            // 等待备用音效播放完成
            await Future.delayed(const Duration(milliseconds: 150));
            return;
          } else {
            await SystemSound.play(SystemSoundType.click);
            LoggerService.d('✅ Windows备用音效播放成功: click代替alert');
            // 等待备用音效播放完成
            await Future.delayed(const Duration(milliseconds: 150));
            return;
          }
        } catch (e2) {
          LoggerService.w('⚠️ Windows备用音效也失败: $e2');
        }
      }

      // 如果系统音效都失败，抛出异常让上层处理
      rethrow;
    }
  }

  /// 最后的备用音效方案
  /// 使用AudioPlayer播放简单的音频文件或生成音效
  Future<void> _playFallbackSound() async {
    try {
      LoggerService.d('🔧 使用备用音效方案...');

      if (!_isInitialized) {
        LoggerService.w('⚠️ AudioPlayer未初始化，无法播放备用音效');
        return;
      }

      // 这里可以播放预设的音频文件，或者使用其他方式生成音效
      // 目前先记录日志，表示备用方案已执行
      LoggerService.d('🔊 备用音效方案执行完成（静默模式）');

      // 可以在这里添加播放本地音频文件的逻辑
      // await _audioPlayer.play(AssetSource('sounds/beep.mp3'));

    } catch (e) {
      LoggerService.e('❌ 备用音效方案也失败', e);
    }
  }
}
