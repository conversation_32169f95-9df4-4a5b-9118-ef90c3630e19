import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/constants/border_styles.dart';
import '../../../core/utils/logger.dart';
import '../models/inventory_check.dart';
import '../services/inventory_check_service.dart';

/// 编辑盘点单对话框
/// 
/// 允许修改盘点信息
/// 添加权限控制（仅允许特定角色编辑）
class InventoryCheckEditDialog extends StatefulWidget {
  final InventoryCheck inventoryCheck;

  const InventoryCheckEditDialog({
    super.key,
    required this.inventoryCheck,
  });

  @override
  State<InventoryCheckEditDialog> createState() => _InventoryCheckEditDialogState();
}

class _InventoryCheckEditDialogState extends State<InventoryCheckEditDialog> {
  final _formKey = GlobalKey<FormState>();
  final _remarkController = TextEditingController();
  final _inventoryCheckService = Get.find<InventoryCheckService>();

  bool _isUpdating = false;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  @override
  void dispose() {
    _remarkController.dispose();
    super.dispose();
  }

  /// 初始化数据
  void _initializeData() {
    _remarkController.text = widget.inventoryCheck.remark ?? '';
  }

  /// 检查编辑权限
  bool get _canEdit {
    // 只有进行中的盘点单可以编辑
    if (!widget.inventoryCheck.canEdit) {
      return false;
    }

    // 暂时简化权限检查，允许所有用户编辑进行中的盘点单
    // 实际项目中需要根据AuthService的具体实现来调整
    return true;

    // 以下是完整的权限检查逻辑，需要根据实际AuthService实现来启用
    // // 管理员可以编辑所有盘点单
    // if (_authService.userRole.value == 'admin') {
    //   return true;
    // }
    //
    // // 普通员工只能编辑自己门店的盘点单
    // return _authService.storeId.value == widget.inventoryCheck.storeId;
  }

  /// 更新盘点单
  Future<void> _updateInventoryCheck() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (!_canEdit) {
      Get.snackbar('提示', '您没有权限编辑此盘点单');
      return;
    }

    try {
      setState(() => _isUpdating = true);

      final request = InventoryCheckUpdateRequest(
        remark: _remarkController.text.trim().isEmpty 
            ? null 
            : _remarkController.text.trim(),
      );

      final updatedInventoryCheck = await _inventoryCheckService.updateInventoryCheck(
        widget.inventoryCheck.id,
        request,
      );

      Get.back(result: updatedInventoryCheck);
      Get.snackbar('成功', '盘点单更新成功');
      
      LoggerService.d('✅ 盘点单更新成功 - 单号: ${updatedInventoryCheck.checkNo}');
    } catch (e) {
      LoggerService.e('❌ 更新盘点单失败', e);
      Get.snackbar('错误', '更新盘点单失败: ${e.toString()}');
    } finally {
      setState(() => _isUpdating = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppBorderStyles.mediumBorderRadius),
      ),
      child: Container(
        width: 480,
        decoration: AppBorderStyles.standardBoxDecoration.copyWith(
          borderRadius: BorderRadius.circular(AppBorderStyles.mediumBorderRadius),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(),
            _buildContent(),
            _buildActions(),
          ],
        ),
      ),
    );
  }

  /// 构建对话框头部
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.orange[50],
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(AppBorderStyles.mediumBorderRadius),
          topRight: Radius.circular(AppBorderStyles.mediumBorderRadius),
        ),
        border: const Border(
          bottom: AppBorderStyles.tableBorder,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.edit,
            color: Colors.orange[600],
            size: 24,
          ),
          const SizedBox(width: 12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '编辑盘点单',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              Text(
                '单号: ${widget.inventoryCheck.checkNo}',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          const Spacer(),
          _buildStatusBadge(),
          const SizedBox(width: 12),
          IconButton(
            onPressed: () => Get.back(),
            icon: const Icon(Icons.close, size: 20),
            style: IconButton.styleFrom(
              backgroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建状态徽章
  Widget _buildStatusBadge() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: widget.inventoryCheck.statusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
        border: Border.all(
          color: widget.inventoryCheck.statusColor,
          width: 1,
        ),
      ),
      child: Text(
        widget.inventoryCheck.statusText,
        style: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: widget.inventoryCheck.statusColor,
        ),
      ),
    );
  }

  /// 构建对话框内容
  Widget _buildContent() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildBasicInfo(),
            const SizedBox(height: 16),
            _buildRemarkField(),
            const SizedBox(height: 16),
            if (!_canEdit) _buildPermissionWarning(),
          ],
        ),
      ),
    );
  }

  /// 构建基本信息
  Widget _buildBasicInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: AppBorderStyles.standardBoxDecoration.copyWith(
        color: Colors.grey[50],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '基本信息',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildInfoItem('盘点门店', widget.inventoryCheck.storeName ?? '-'),
              ),
              Expanded(
                child: _buildInfoItem('操作员', widget.inventoryCheck.operatorName ?? '-'),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: _buildInfoItem('应盘总数', widget.inventoryCheck.totalCount.toString()),
              ),
              Expanded(
                child: _buildInfoItem('已盘数量', widget.inventoryCheck.checkedCount.toString()),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建信息项
  Widget _buildInfoItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建备注输入框
  Widget _buildRemarkField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '备注信息',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: AppBorderStyles.standardBoxDecoration.copyWith(
            color: _canEdit ? Colors.white : Colors.grey[100],
          ),
          child: TextField(
            controller: _remarkController,
            enabled: _canEdit,
            maxLines: 3,
            decoration: const InputDecoration(
              hintText: '请输入盘点备注信息（可选）',
              border: InputBorder.none,
              enabledBorder: InputBorder.none,
              focusedBorder: InputBorder.none,
              disabledBorder: InputBorder.none,
              contentPadding: EdgeInsets.all(12),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建权限警告
  Widget _buildPermissionWarning() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.red[50],
        borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
        border: Border.all(
          color: Colors.red[200]!,
          width: AppBorderStyles.borderWidth,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.warning_outlined,
            color: Colors.red[600],
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              widget.inventoryCheck.status != 0
                  ? '只有进行中的盘点单可以编辑'
                  : '您没有权限编辑此盘点单',
              style: TextStyle(
                fontSize: 12,
                color: Colors.red[700],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActions() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        border: Border(
          top: AppBorderStyles.tableBorder,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          SizedBox(
            height: 36,
            child: OutlinedButton(
              onPressed: _isUpdating ? null : () => Get.back(),
              style: OutlinedButton.styleFrom(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
                ),
                side: const BorderSide(color: AppBorderStyles.borderColor),
              ),
              child: const Text('取消'),
            ),
          ),
          const SizedBox(width: 12),
          SizedBox(
            height: 36,
            child: ElevatedButton(
              onPressed: (_isUpdating || !_canEdit) ? null : _updateInventoryCheck,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange[600],
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppBorderStyles.borderRadius),
                ),
              ),
              child: _isUpdating
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Text('保存修改'),
            ),
          ),
        ],
      ),
    );
  }
}
