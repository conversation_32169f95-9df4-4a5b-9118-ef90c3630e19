"""
商品业务逻辑服务
处理商品相关的业务逻辑
"""

import time
from typing import List, Optional
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import or_
from ..models.jewelry import Jewelry, JewelryCategory
from ..models.store import Store
from ..schemas.jewelry import (
    JewelryCreate,
    JewelryUpdate,
    JewelryResponse,
    JewelryCategoryResponse,
    JewelryListResponse,
    JewelryCostBreakdown
)


class JewelryService:
    """商品服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    # 商品分类相关方法
    async def get_categories(self, status: Optional[int] = None) -> List[JewelryCategoryResponse]:
        """获取商品分类列表"""
        query = self.db.query(JewelryCategory)
        
        if status is not None:
            query = query.filter(JewelryCategory.status == status)
        
        categories = query.order_by(JewelryCategory.weigh.desc(), JewelryCategory.id.asc()).all()
        return [JewelryCategoryResponse.from_orm(cat) for cat in categories]
    
    async def get_category_by_id(self, category_id: int) -> Optional[JewelryCategoryResponse]:
        """根据ID获取商品分类"""
        category = self.db.query(JewelryCategory).filter(JewelryCategory.id == category_id).first()
        return JewelryCategoryResponse.from_orm(category) if category else None
    
    # 商品相关方法
    async def get_jewelry_list(
        self,
        page: int = 1,
        page_size: int = 20,
        category_id: Optional[int] = None,
        store_id: Optional[int] = None,
        status: Optional[int] = None,
        keyword: Optional[str] = None
    ) -> JewelryListResponse:
        """获取商品列表 - 修复门店名称和分类名称显示问题"""

        try:
            # 🔧 修复：使用显式JOIN查询确保关联数据完整性

            # 构建基础查询，使用显式JOIN确保关联数据
            query = self.db.query(Jewelry).join(
                Store, Jewelry.store_id == Store.id
            ).join(
                JewelryCategory, Jewelry.category_id == JewelryCategory.id
            ).options(
                joinedload(Jewelry.category),
                joinedload(Jewelry.store)
            )

            # 添加筛选条件
            if category_id:
                query = query.filter(Jewelry.category_id == category_id)

            if store_id:
                query = query.filter(Jewelry.store_id == store_id)

            if status is not None:
                query = query.filter(Jewelry.status == status)

            if keyword:
                query = query.filter(
                    or_(
                        Jewelry.name.like(f"%{keyword}%"),
                        Jewelry.barcode.like(f"%{keyword}%")
                    )
                )

            # 按创建时间倒序排列
            query = query.order_by(Jewelry.createtime.desc())

            # 计算总数
            total = query.count()

            # 分页
            offset = (page - 1) * page_size
            jewelry_items = query.offset(offset).limit(page_size).all()

            # 🔧 修复：改进数据转换逻辑，确保关联数据正确映射
            items = []
            for jewelry in jewelry_items:
                try:
                    # 获取关联的门店名称
                    store_name = None
                    if jewelry.store:
                        store_name = jewelry.store.name
                    else:
                        # 如果关联查询失败，手动查询门店信息
                        store = self.db.query(Store).filter(Store.id == jewelry.store_id).first()
                        store_name = store.name if store else None

                    # 获取关联的分类名称
                    category_name = None
                    if jewelry.category:
                        category_name = jewelry.category.name
                    else:
                        # 如果关联查询失败，手动查询分类信息
                        category = self.db.query(JewelryCategory).filter(JewelryCategory.id == jewelry.category_id).first()
                        category_name = category.name if category else None

                    # 构建响应数据
                    jewelry_dict = {
                        'id': jewelry.id,
                        'barcode': jewelry.barcode,
                        'name': jewelry.name,
                        'category_id': jewelry.category_id,
                        'ring_size': jewelry.ring_size,
                        'gold_weight': jewelry.gold_weight,
                        'gold_price': jewelry.gold_price,
                        'gold_cost': jewelry.gold_cost,
                        'silver_weight': jewelry.silver_weight,
                        'total_weight': jewelry.total_weight,
                        'silver_price': jewelry.silver_price,
                        'silver_cost': jewelry.silver_cost,
                        'silver_work_type': jewelry.silver_work_type,
                        'silver_work_price': jewelry.silver_work_price,
                        'plating_cost': jewelry.plating_cost,
                        'total_cost': jewelry.total_cost,
                        'wholesale_work_price': jewelry.wholesale_work_price,
                        'retail_work_price': jewelry.retail_work_price,
                        'piece_work_price': jewelry.piece_work_price,
                        'store_id': jewelry.store_id,
                        'status': jewelry.status,
                        'createtime': jewelry.createtime,
                        'updatetime': jewelry.updatetime,
                        'category_name': category_name,
                        'store_name': store_name
                    }

                    items.append(JewelryResponse.from_orm(type('obj', (object,), jewelry_dict)()))

                except Exception as e:
                    print(f"处理商品数据时出错 (ID: {jewelry.id}): {e}")
                    # 即使单个商品处理失败，也继续处理其他商品
                    continue

            # 计算总页数
            total_pages = (total + page_size - 1) // page_size

            return JewelryListResponse(
                items=items,
                total=total,
                page=page,
                page_size=page_size,
                total_pages=total_pages
            )

        except Exception as e:
            print(f"获取商品列表时出错: {e}")
            # 返回空结果而不是抛出异常
            return JewelryListResponse(
                items=[],
                total=0,
                page=page,
                page_size=page_size,
                total_pages=0
            )
    
    async def get_jewelry_by_id(self, jewelry_id: int) -> Optional[JewelryResponse]:
        """根据ID获取商品"""
        jewelry = self.db.query(Jewelry).filter(Jewelry.id == jewelry_id).first()
        if not jewelry:
            return None
        
        jewelry_dict = {
            **jewelry.__dict__,
            'category_name': jewelry.category.name if jewelry.category else None,
            'store_name': jewelry.store.name if jewelry.store else None
        }
        return JewelryResponse.from_orm(type('obj', (object,), jewelry_dict)())
    
    async def get_jewelry_by_barcode(self, barcode: str, store_id: Optional[int] = None) -> Optional[JewelryResponse]:
        """根据条码获取商品"""
        query = self.db.query(Jewelry).filter(Jewelry.barcode == barcode)

        # 如果指定了门店ID，则只查询该门店的商品
        if store_id is not None:
            query = query.filter(Jewelry.store_id == store_id)

        jewelry = query.first()
        if not jewelry:
            return None

        jewelry_dict = {
            **jewelry.__dict__,
            'category_name': jewelry.category.name if jewelry.category else None,
            'store_name': jewelry.store.name if jewelry.store else None
        }
        return JewelryResponse.from_orm(type('obj', (object,), jewelry_dict)())
    
    async def create_jewelry(self, jewelry_data: JewelryCreate) -> JewelryResponse:
        """创建新商品"""
        current_time = int(time.time())
        
        # 创建商品对象
        jewelry = Jewelry(
            **jewelry_data.dict(),
            createtime=current_time,
            updatetime=current_time
        )
        
        # 保存到数据库
        self.db.add(jewelry)
        self.db.commit()
        self.db.refresh(jewelry)
        
        return await self.get_jewelry_by_id(jewelry.id)
    
    async def update_jewelry(self, jewelry_id: int, jewelry_data: JewelryUpdate) -> JewelryResponse:
        """更新商品信息"""
        jewelry = self.db.query(Jewelry).filter(Jewelry.id == jewelry_id).first()
        if not jewelry:
            return None
        
        # 更新字段
        update_data = jewelry_data.dict(exclude_unset=True)
        update_data['updatetime'] = int(time.time())
        
        for field, value in update_data.items():
            setattr(jewelry, field, value)
        
        self.db.commit()
        self.db.refresh(jewelry)
        
        return await self.get_jewelry_by_id(jewelry_id)
    
    async def delete_jewelry(self, jewelry_id: int) -> bool:
        """删除商品"""
        jewelry = self.db.query(Jewelry).filter(Jewelry.id == jewelry_id).first()
        if not jewelry:
            return False
        
        self.db.delete(jewelry)
        self.db.commit()
        return True
    
    async def update_jewelry_status(self, jewelry_id: int, status: int) -> JewelryResponse:
        """更新商品状态"""
        jewelry = self.db.query(Jewelry).filter(Jewelry.id == jewelry_id).first()
        if not jewelry:
            return None
        
        jewelry.status = status
        jewelry.updatetime = int(time.time())
        
        self.db.commit()
        self.db.refresh(jewelry)
        
        return await self.get_jewelry_by_id(jewelry_id)
    
    async def calculate_cost_breakdown(self, jewelry_id: int) -> JewelryCostBreakdown:
        """计算商品成本明细"""
        jewelry = self.db.query(Jewelry).filter(Jewelry.id == jewelry_id).first()
        if not jewelry:
            return None
        
        # 计算工费成本
        if jewelry.silver_work_type == 0:  # 按克
            silver_work_cost = jewelry.silver_weight * jewelry.silver_work_price
        else:  # 按件
            silver_work_cost = jewelry.silver_work_price
        
        # 计算总成本
        total_material_cost = jewelry.gold_cost + jewelry.silver_cost
        total_work_cost = silver_work_cost + jewelry.plating_cost
        total_cost = total_material_cost + total_work_cost
        
        # 计算定价
        wholesale_price = total_cost + jewelry.wholesale_work_price
        retail_price = total_cost + jewelry.retail_work_price
        piece_price = total_cost + jewelry.piece_work_price
        
        return JewelryCostBreakdown(
            jewelry_id=jewelry.id,
            jewelry_name=jewelry.name,
            
            # 金成本
            gold_weight=jewelry.gold_weight,
            gold_price=jewelry.gold_price,
            gold_cost=jewelry.gold_cost,
            
            # 银成本
            silver_weight=jewelry.silver_weight,
            silver_price=jewelry.silver_price,
            silver_cost=jewelry.silver_cost,
            
            # 工费成本
            silver_work_cost=silver_work_cost,
            plating_cost=jewelry.plating_cost,
            
            # 总成本
            total_material_cost=total_material_cost,
            total_work_cost=total_work_cost,
            total_cost=total_cost,
            
            # 定价
            wholesale_price=wholesale_price,
            retail_price=retail_price,
            piece_price=piece_price
        ) 