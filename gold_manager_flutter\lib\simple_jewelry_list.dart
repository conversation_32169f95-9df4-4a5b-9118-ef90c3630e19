import 'package:flutter/material.dart';
import 'models/common/enums.dart';

class JewelryListPage extends StatefulWidget {
  const JewelryListPage({super.key});

  @override
  State<JewelryListPage> createState() => _JewelryListPageState();
}

class _JewelryListPageState extends State<JewelryListPage> {
  // 模拟数据
  final List<Map<String, dynamic>> _jewelryList = [
    {
      'id': 1,
      'barcode': 'J001',
      'name': '简约款黄金戒指',
      'category': '戒指',
      'goldWeight': 3.5,
      'silverWeight': 0.0,
      'price': 1280.0,
      'status': JewelryStatus.onShelf,
      'store': '总店'
    },
    {
      'id': 2,
      'barcode': 'J002',
      'name': '复古花纹银项链',
      'category': '项链',
      'goldWeight': 0.0,
      'silverWeight': 9.8,
      'price': 680.0,
      'status': JewelryStatus.onShelf,
      'store': '分店一'
    },
    {
      'id': 3,
      'barcode': 'J003',
      'name': '镶钻金银手镯',
      'category': '手镯',
      'goldWeight': 5.2,
      'silverWeight': 3.6,
      'price': 2680.0,
      'status': JewelryStatus.onShelf,
      'store': '总店'
    },
    {
      'id': 4,
      'barcode': 'J004',
      'name': '传统花纹金耳环',
      'category': '耳环',
      'goldWeight': 2.7,
      'silverWeight': 0.0,
      'price': 980.0,
      'status': JewelryStatus.pendingOut,
      'store': '分店二'
    },
    {
      'id': 5,
      'barcode': 'J005',
      'name': '现代设计银手链',
      'category': '手链',
      'goldWeight': 0.0,
      'silverWeight': 7.5,
      'price': 520.0,
      'status': JewelryStatus.onShelf,
      'store': '分店一'
    }
  ];

  String _searchText = '';

  List<Map<String, dynamic>> get filteredList {
    if (_searchText.isEmpty) {
      return _jewelryList;
    }
    return _jewelryList.where((item) {
      return item['name'].toString().contains(_searchText) ||
             item['barcode'].toString().contains(_searchText) ||
             item['category'].toString().contains(_searchText);
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('金包银首饰管理系统 - 库存管理'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: '刷新',
            onPressed: () {
              setState(() {});
            },
          ),
        ],
      ),
      drawer: _buildDrawer(),
      body: Column(
        children: [
          _buildFilterBar(),
          Expanded(
            child: _buildJewelryList(),
          ),
        ],
      ),
      // 移除FloatingActionButton，使用界面内的具体操作按钮
    );
  }

  Widget _buildDrawer() {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          const DrawerHeader(
            decoration: BoxDecoration(
              color: Colors.blue,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '金包银首饰管理系统',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                  ),
                ),
                SizedBox(height: 16),
                Text(
                  '管理员',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
          ListTile(
            leading: const Icon(Icons.inventory),
            title: const Text('首饰库存'),
            selected: true,
            onTap: () {
              Navigator.pop(context);
            },
          ),
          ListTile(
            leading: const Icon(Icons.point_of_sale),
            title: const Text('销售管理'),
            onTap: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('销售管理功能正在开发中...')),
              );
            },
          ),
          ListTile(
            leading: const Icon(Icons.recycling),
            title: const Text('旧料回收'),
            onTap: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('旧料回收功能正在开发中...')),
              );
            },
          ),
          ListTile(
            leading: const Icon(Icons.store),
            title: const Text('门店管理'),
            onTap: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('门店管理功能正在开发中...')),
              );
            },
          ),
          ListTile(
            leading: const Icon(Icons.analytics),
            title: const Text('统计分析'),
            onTap: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('统计分析功能正在开发中...')),
              );
            },
          ),
          ListTile(
            leading: const Icon(Icons.settings),
            title: const Text('系统设置'),
            onTap: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('系统设置功能正在开发中...')),
              );
            },
          ),
          const Divider(),
          ListTile(
            leading: const Icon(Icons.logout),
            title: const Text('退出登录'),
            onTap: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('退出登录功能正在开发中...')),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildFilterBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: Column(
        children: [
          TextField(
            decoration: InputDecoration(
              hintText: '搜索条码、名称或分类',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              contentPadding: const EdgeInsets.symmetric(vertical: 0, horizontal: 16),
            ),
            onChanged: (value) {
              setState(() {
                _searchText = value;
              });
            },
          ),
          const SizedBox(height: 16),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildFilterChip('全部', true),
                _buildFilterChip('在库', false),
                _buildFilterChip('在售', false),
                _buildFilterChip('已售', false),
                _buildFilterChip('总店', false),
                _buildFilterChip('分店一', false),
                _buildFilterChip('分店二', false),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, bool selected) {
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(label),
        selected: selected,
        onSelected: (value) {
          // 实际应用中这里会应用筛选
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('筛选条件: $label')),
          );
        },
      ),
    );
  }

  Widget _buildJewelryList() {
    return filteredList.isEmpty
      ? const Center(
          child: Text(
            '没有找到匹配的首饰',
            style: TextStyle(fontSize: 16),
          ),
        )
      : ListView.builder(
          itemCount: filteredList.length,
          itemBuilder: (context, index) {
            final jewelry = filteredList[index];
            return _buildJewelryCard(jewelry);
          },
        );
  }

  Widget _buildJewelryCard(Map<String, dynamic> jewelry) {
    final JewelryStatus status = jewelry['status'] as JewelryStatus;

    // 状态文本和颜色
    String statusText;
    Color statusColor;

    switch (status) {
      case JewelryStatus.offShelf:
        statusText = '已下架';
        statusColor = Colors.grey;
        break;
      case JewelryStatus.onShelf:
        statusText = '上架在售';
        statusColor = Colors.green;
        break;
      case JewelryStatus.pendingOut:
        statusText = '待出库';
        statusColor = Colors.orange;
        break;
    }

    return Card(
      margin: const EdgeInsets.fromLTRB(16, 8, 16, 8),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('查看首饰: ${jewelry['name']}')),
          );
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    jewelry['name'],
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: statusColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: statusColor,
                        width: 1,
                      ),
                    ),
                    child: Text(
                      statusText,
                      style: TextStyle(
                        color: statusColor,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  const Icon(Icons.qr_code, size: 16, color: Colors.grey),
                  const SizedBox(width: 4),
                  Text(
                    jewelry['barcode'],
                    style: const TextStyle(color: Colors.grey),
                  ),
                  const SizedBox(width: 16),
                  const Icon(Icons.category, size: 16, color: Colors.grey),
                  const SizedBox(width: 4),
                  Text(
                    jewelry['category'],
                    style: const TextStyle(color: Colors.grey),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('金重', style: TextStyle(fontSize: 12, color: Colors.grey)),
                        Text('${jewelry['goldWeight']}g', style: const TextStyle(fontWeight: FontWeight.bold)),
                      ],
                    ),
                  ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('银重', style: TextStyle(fontSize: 12, color: Colors.grey)),
                        Text('${jewelry['silverWeight']}g', style: const TextStyle(fontWeight: FontWeight.bold)),
                      ],
                    ),
                  ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('价格', style: TextStyle(fontSize: 12, color: Colors.grey)),
                        Text('¥${jewelry['price']}',
                            style: const TextStyle(fontWeight: FontWeight.bold, color: Colors.blue)),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '所属门店: ${jewelry['store']}',
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                  Row(
                    children: [
                      IconButton(
                        icon: const Icon(Icons.edit, size: 20),
                        tooltip: '编辑',
                        onPressed: () {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(content: Text('编辑首饰: ${jewelry['name']}')),
                          );
                        },
                        constraints: const BoxConstraints(),
                        padding: const EdgeInsets.all(8),
                      ),
                      IconButton(
                        icon: const Icon(Icons.qr_code_scanner, size: 20),
                        tooltip: '条码',
                        onPressed: () {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(content: Text('查看条码: ${jewelry['barcode']}')),
                          );
                        },
                        constraints: const BoxConstraints(),
                        padding: const EdgeInsets.all(8),
                      ),
                      IconButton(
                        icon: const Icon(Icons.history, size: 20),
                        tooltip: '历史',
                        onPressed: () {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(content: Text('查看历史: ${jewelry['name']}')),
                          );
                        },
                        constraints: const BoxConstraints(),
                        padding: const EdgeInsets.all(8),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}