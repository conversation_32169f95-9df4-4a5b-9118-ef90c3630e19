/// 商品分类模型
class Category {
  final int id;
  final String name;
  final int parentId;
  final String? description;
  final DateTime? createTime;
  final DateTime? updateTime;

  const Category({
    required this.id,
    required this.name,
    required this.parentId,
    this.description,
    this.createTime,
    this.updateTime,
  });

  /// 从JSON创建Category对象
  factory Category.fromJson(Map<String, dynamic> json) {
    return Category(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      parentId: json['parent_id'] ?? 0,
      description: json['description'],
      createTime: json['createtime'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(json['createtime'] * 1000)
          : null,
      updateTime: json['updatetime'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(json['updatetime'] * 1000)
          : null,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'parent_id': parentId,
      'description': description,
      'createtime': createTime?.millisecondsSinceEpoch != null 
          ? (createTime!.millisecondsSinceEpoch / 1000).round()
          : null,
      'updatetime': updateTime?.millisecondsSinceEpoch != null 
          ? (updateTime!.millisecondsSinceEpoch / 1000).round()
          : null,
    };
  }

  /// 复制并修改部分属性
  Category copyWith({
    int? id,
    String? name,
    int? parentId,
    String? description,
    DateTime? createTime,
    DateTime? updateTime,
  }) {
    return Category(
      id: id ?? this.id,
      name: name ?? this.name,
      parentId: parentId ?? this.parentId,
      description: description ?? this.description,
      createTime: createTime ?? this.createTime,
      updateTime: updateTime ?? this.updateTime,
    );
  }

  @override
  String toString() {
    return 'Category{id: $id, name: $name, parentId: $parentId, description: $description}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Category && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
