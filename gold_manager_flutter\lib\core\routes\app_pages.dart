import 'package:get/get.dart';

import '../../features/auth/bindings/auth_binding.dart';
import '../../features/auth/views/splash_view.dart';
import '../../features/auth/views/login_view.dart';
import '../../features/dashboard/views/dashboard_view.dart';
import '../../features/dashboard/bindings/dashboard_binding.dart';
import '../../features/recycling/presentation/recycling_list_page.dart';
// import '../../features/recycling/presentation/recycling_detail_page.dart';
import '../../features/recycling/presentation/pages/category_list_page.dart';
import '../../features/recycling/presentation/pages/category_form_page.dart';
import '../../features/stock/bindings/stock_in_binding.dart';
import '../../features/stock/views/stock_in_view.dart';
import '../../features/stock/bindings/stock_in_form_binding.dart';
import '../../features/stock/views/stock_in_form_view.dart';
import '../../features/stock/bindings/stock_out_binding.dart';
import '../../features/stock/views/stock_out_view.dart';
import '../../features/stock/bindings/stock_out_form_binding.dart';
import '../../features/stock/views/stock_out_form_view.dart';
import '../../features/stock/bindings/stock_query_binding.dart';
import '../../features/stock/views/stock_query_view.dart';
import '../../features/stock/bindings/inventory_check_binding.dart';
import '../../features/stock/views/inventory_check_view.dart';
import '../../features/stock/views/inventory_check_operation_view.dart';
import '../../features/stock/controllers/inventory_check_operation_controller.dart';
import '../../features/stock/views/store_transfer_form_view.dart';
import '../../features/stock/bindings/store_transfer_form_binding.dart';

import '../../features/sales/bindings/sales_binding.dart';
import '../../features/sales/views/sales_management_view.dart';
import '../../features/sales/views/sales_return_list_view.dart';
import '../../features/stock/views/stock_list_view.dart';
import '../../features/sales/views/sales_order_select_view.dart';
import '../../features/sales/views/sales_return_form_view.dart';
import '../../features/jewelry/screens/category/jewelry_category_list_screen.dart';
import '../../features/print/views/print_template_management_view.dart';
import '../../features/print/views/print_template_editor_view.dart';

// 注意: 部分视图和绑定文件尚未创建，后续开发中会逐步添加

/// 路由名称常量
abstract class Routes {
  // 基础路由
  static const SPLASH = '/splash';
  static const LOGIN = '/login';
  static const DASHBOARD = '/dashboard';
  static const HOME = '/home';

  // 首饰管理
  static const JEWELRY = '/jewelry';
  static const JEWELRY_DETAIL = '/jewelry/detail';
  static const JEWELRY_FORM = '/jewelry/form';
  static const JEWELRY_EDIT = '/jewelry/edit';
  static const JEWELRY_CATEGORY = '/jewelry/category';

  // 库存管理
  static const STOCK = '/stock';
  static const STOCK_IN = '/stock/in';
  static const STOCK_OUT = '/stock/out';
  static const STOCK_QUERY = '/stock/query';
  static const STOCK_IN_FORM = '/stock/in/form';
  static const STOCK_IN_DETAIL = '/stock/in/detail';
  static const STOCK_OUT_FORM = '/stock/out/form';
  static const STOCK_OUT_DETAIL = '/stock/out/detail';
  static const INVENTORY_CHECK = '/stock/inventory-check';
  static const INVENTORY_CHECK_DETAIL = '/stock/inventory-check/detail';
  static const INVENTORY_CHECK_FORM = '/stock/inventory-check/form';
  static const INVENTORY_CHECK_OPERATION = '/stock/inventory-check/operation';
  static const STORE_TRANSFER_FORM = '/stock/store-transfer/form';

  // 销售管理
  static const SALES = '/sales';
  static const SALES_DETAIL = '/sales/detail';

  // 回收管理
  static const RECYCLING = '/recycling';
  static const RECYCLING_DETAIL = '/recycling/detail';
  static const RECYCLING_CATEGORY = '/recycling/category';
  static const RECYCLING_CATEGORY_FORM = '/recycling/category/form';

  // 系统设置
  static const SETTINGS = '/settings';

  // 打印模板管理
  static const PRINT_TEMPLATE = '/print/template';
  static const PRINT_TEMPLATE_EDITOR = '/print/template/editor';
}

/// 应用页面路由配置
class AppPages {
  // 初始路由
  static const INITIAL = Routes.SPLASH;

  // 定义所有路由
  static final routes = [
    // 基础路由
    GetPage(
      name: Routes.HOME,
      page: () => const RecyclingListPage(),
    ),

    GetPage(
      name: Routes.SPLASH,
      page: () => const SplashView(),
      binding: AuthBinding(),
    ),

    GetPage(
      name: Routes.LOGIN,
      page: () => const LoginView(),
      binding: AuthBinding(),
    ),

    GetPage(
      name: Routes.DASHBOARD,
      page: () => const DashboardView(),
      binding: DashboardBinding(),
    ),

    // 回收管理路由
    GetPage(
      name: Routes.RECYCLING,
      page: () => const RecyclingListPage(),
    ),

    // 分类管理路由
    GetPage(
      name: Routes.RECYCLING_CATEGORY,
      page: () => const CategoryListPage(),
    ),

    GetPage(
      name: Routes.RECYCLING_CATEGORY_FORM,
      page: () => const CategoryFormPage(),
    ),

    // 入库管理路由
    GetPage(
      name: Routes.STOCK_IN,
      page: () => const StockInView(),
      binding: StockInBinding(),
    ),

    // 入库单表单路由
    GetPage(
      name: Routes.STOCK_IN_FORM,
      page: () => const StockInFormView(),
      binding: StockInFormBinding(),
    ),

    // 出库管理路由
    GetPage(
      name: Routes.STOCK_OUT,
      page: () => const StockOutView(),
      binding: StockOutBinding(),
    ),

    // 出库单表单路由
    GetPage(
      name: Routes.STOCK_OUT_FORM,
      page: () => const StockOutFormView(),
      binding: StockOutFormBinding(),
    ),

    // 库存查询路由
    GetPage(
      name: Routes.STOCK_QUERY,
      page: () => const StockQueryView(),
      binding: StockQueryBinding(),
    ),

    // 库存盘点路由
    GetPage(
      name: Routes.INVENTORY_CHECK,
      page: () => const InventoryCheckView(),
      binding: InventoryCheckBinding(),
    ),

    // 库存盘点操作路由
    GetPage(
      name: '${Routes.INVENTORY_CHECK_OPERATION}/:id',
      page: () => const InventoryCheckOperationView(),
      binding: BindingsBuilder(() {
        Get.lazyPut<InventoryCheckOperationController>(
          () => InventoryCheckOperationController(),
        );
      }),
    ),

    // 新建库存调拨表单路由
    GetPage(
      name: Routes.STORE_TRANSFER_FORM,
      page: () => const StoreTransferFormView(),
      binding: StoreTransferFormBinding(),
    ),

    // 商品编辑现在使用弹窗形式，不再需要路由

    // 注意：首饰管理现在是仪表板内的一个标签页，不再需要独立路由
    // 如果需要独立访问，可以通过仪表板路由并设置初始标签页索引

    // 首饰分类管理页
    GetPage(
      name: Routes.JEWELRY_CATEGORY,
      page: () => const JewelryCategoryListScreen(),
    ),

    // 库存管理页
    GetPage(
      name: Routes.STOCK,
      page: () => const StockListView(),
    ),

    // 销售模块页面
    GetPage(
      name: Routes.SALES,
      page: () => const SalesManagementView(),  // 改为销售明细管理页面
      binding: SalesBinding(),
    ),

    // 销售退货列表
    GetPage(
      name: '/sales/return',
      page: () => const SalesReturnListView(),
      binding: SalesBinding(),
    ),

    // 创建销售退货
    GetPage(
      name: '/sales/return/create',
      page: () => const SalesReturnFormView(),
      binding: SalesBinding(),
    ),

    // 编辑销售退货
    GetPage(
      name: '/sales/return/edit/:id',
      page: () => const SalesReturnFormView(),
      binding: SalesBinding(),
    ),

    // 选择销售订单
    GetPage(
      name: '/sales/order/select',
      page: () => const SalesOrderSelectView(),
      binding: SalesBinding(),
    ),

    // 打印模板管理路由
    GetPage(
      name: Routes.PRINT_TEMPLATE,
      page: () => const PrintTemplateManagementView(),
    ),

    // 打印模板编辑器路由
    GetPage(
      name: Routes.PRINT_TEMPLATE_EDITOR,
      page: () => const PrintTemplateEditorView(),
    ),
  ];
}