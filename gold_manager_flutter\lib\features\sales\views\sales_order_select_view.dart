import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gold_manager_flutter/services/sales_order_service.dart';
import 'package:gold_manager_flutter/widgets/app_bar.dart';
import 'package:gold_manager_flutter/widgets/loading_state.dart';
import 'package:intl/intl.dart';

/// 销售订单选择页面
class SalesOrderSelectView extends StatefulWidget {
  /// 构造函数
  const SalesOrderSelectView({super.key});

  @override
  State<SalesOrderSelectView> createState() => _SalesOrderSelectViewState();
}

class _SalesOrderSelectViewState extends State<SalesOrderSelectView> {
  final SalesOrderService _salesService = Get.find<SalesOrderService>();
  
  bool _isLoading = true;
  List<dynamic> _orderList = [];
  String _searchQuery = '';
  
  @override
  void initState() {
    super.initState();
    _loadSalesOrders();
  }
  
  /// 加载销售订单列表
  Future<void> _loadSalesOrders() async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      final filter = <String, dynamic>{};
      if (_searchQuery.isNotEmpty) {
        filter['search'] = _searchQuery;
      }
      
      final list = await _salesService.getSalesOrderList(
        filters: filter,
      );
      
      setState(() {
        _orderList = list;
      });
    } catch (e) {
      Get.snackbar('错误', '加载销售单列表失败: $e',
          backgroundColor: Colors.red.shade100);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
  
  /// 搜索订单
  void _searchOrders(String query) {
    setState(() {
      _searchQuery = query;
    });
    _loadSalesOrders();
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: '选择销售单',
      ),
      body: Column(
        children: [
          _buildSearchBar(),
          Expanded(
            child: _isLoading
                ? const LoadingState(text: '加载中...', timeoutSeconds: 30)
                : _buildOrderList(),
          ),
        ],
      ),
    );
  }
  
  /// 构建搜索栏
  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: TextField(
        decoration: InputDecoration(
          hintText: '搜索销售单号、客户名称',
          prefixIcon: const Icon(Icons.search),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12.0),
          ),
          contentPadding: const EdgeInsets.symmetric(vertical: 12.0),
        ),
        onChanged: (value) {
          if (value.isEmpty || value.length > 2) {
            _searchOrders(value);
          }
        },
      ),
    );
  }
  
  /// 构建订单列表
  Widget _buildOrderList() {
    if (_orderList.isEmpty) {
      return const Center(
        child: Text('没有找到符合条件的销售单'),
      );
    }
    
    return ListView.builder(
      itemCount: _orderList.length,
      itemBuilder: (context, index) {
        final order = _orderList[index];
        return _buildOrderItem(order);
      },
    );
  }
  
  /// 构建订单项
  Widget _buildOrderItem(dynamic order) {
    final dateFormat = DateFormat('yyyy-MM-dd HH:mm');
    final DateTime createTime = DateTime.fromMillisecondsSinceEpoch(
        (order['created_at'] as int) * 1000);
    
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: InkWell(
        onTap: () {
          Get.back(result: {
            'id': order['id'],
            'order_no': order['order_no'],
            'customer_name': order['customer_name'],
            'store_id': order['store_id'],
            'store_name': order['store_name'],
          });
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '订单号: ${order['order_no']}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 8.0, vertical: 4.0),
                    decoration: BoxDecoration(
                      color: _getStatusColor(order['status'] as int)
                          .withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      _getStatusText(order['status'] as int),
                      style: TextStyle(
                        color: _getStatusColor(order['status'] as int),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text('客户: ${order['customer_name']}'),
              const SizedBox(height: 4),
              Text('门店: ${order['store_name']}'),
              const SizedBox(height: 4),
              Text('金额: ¥${order['total_amount'].toStringAsFixed(2)}'),
              const SizedBox(height: 8),
              Text(
                '创建时间: ${dateFormat.format(createTime)}',
                style: const TextStyle(color: Colors.grey, fontSize: 12),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  /// 获取状态文本
  String _getStatusText(int status) {
    switch (status) {
      case 0:
        return '待付款';
      case 1:
        return '已付款';
      case 2:
        return '已完成';
      case 3:
        return '已取消';
      default:
        return '未知状态';
    }
  }
  
  /// 获取状态颜色
  Color _getStatusColor(int status) {
    switch (status) {
      case 0:
        return Colors.orange;
      case 1:
        return Colors.blue;
      case 2:
        return Colors.green;
      case 3:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
} 