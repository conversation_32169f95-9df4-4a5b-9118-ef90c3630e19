/// API响应基础模型
/// 根据API文档定义的标准响应格式
class ApiResponse<T> {
  final bool success;
  final int code;
  final String message;
  final T? data;
  final String? detail;

  const ApiResponse({
    required this.success,
    required this.code,
    required this.message,
    this.data,
    this.detail,
  });

  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(dynamic)? fromJsonT,
  ) {
    return ApiResponse<T>(
      success: json['success'] ?? false,
      code: json['code'] ?? 0,
      message: json['message'] ?? '',
      data: json['data'] != null && fromJsonT != null
          ? fromJsonT(json['data'])
          : json['data'],
      detail: json['detail'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'code': code,
      'message': message,
      'data': data,
      if (detail != null) 'detail': detail,
    };
  }

  /// 是否成功
  bool get isSuccess => success && code == 200;

  /// 是否失败
  bool get isFailure => !success || code != 200;

  /// 获取错误信息
  String get errorMessage {
    if (detail != null && detail!.isNotEmpty) {
      return detail!;
    }
    return message;
  }
}

/// 登录请求模型
class LoginRequest {
  final String username;
  final String password;
  final bool rememberMe;

  const LoginRequest({
    required this.username,
    required this.password,
    this.rememberMe = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'username': username,
      'password': password,
      'remember_me': rememberMe,
    };
  }
}

/// 用户信息模型
class UserInfo {
  final int id;
  final String username;
  final String nickname;
  final String email;
  final int? storeId;
  final String? storeName;

  const UserInfo({
    required this.id,
    required this.username,
    required this.nickname,
    required this.email,
    this.storeId,
    this.storeName,
  });

  factory UserInfo.fromJson(Map<String, dynamic> json) {
    return UserInfo(
      id: json['id'] ?? 0,
      username: json['username'] ?? '',
      nickname: json['nickname'] ?? '',
      email: json['email'] ?? '',
      storeId: json['store_id'],
      storeName: json['store_name'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'nickname': nickname,
      'email': email,
      if (storeId != null) 'store_id': storeId,
      if (storeName != null) 'store_name': storeName,
    };
  }
}

/// 登录响应数据模型（匹配后端实际格式）
class LoginResponseData {
  final String accessToken;
  final String refreshToken;
  final String tokenType;
  final int expiresIn;
  final AdminInfo adminInfo;

  const LoginResponseData({
    required this.accessToken,
    required this.refreshToken,
    required this.tokenType,
    required this.expiresIn,
    required this.adminInfo,
  });

  factory LoginResponseData.fromJson(Map<String, dynamic> json) {
    return LoginResponseData(
      accessToken: json['access_token'] ?? '',
      refreshToken: json['refresh_token'] ?? '',
      tokenType: json['token_type'] ?? 'bearer',
      expiresIn: json['expires_in'] ?? 28800,
      adminInfo: AdminInfo.fromJson(json['admin_info'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'access_token': accessToken,
      'refresh_token': refreshToken,
      'token_type': tokenType,
      'expires_in': expiresIn,
      'admin_info': adminInfo.toJson(),
    };
  }
}

/// 管理员信息模型（匹配后端格式）
class AdminInfo {
  final int id;
  final String username;
  final String nickname;
  final String? email;
  final String? mobile;
  final String? avatar;
  final int? storeId;
  final String? storeName;
  final String status;
  final List<String> permissions;

  const AdminInfo({
    required this.id,
    required this.username,
    required this.nickname,
    this.email,
    this.mobile,
    this.avatar,
    this.storeId,
    this.storeName,
    required this.status,
    required this.permissions,
  });

  factory AdminInfo.fromJson(Map<String, dynamic> json) {
    return AdminInfo(
      id: json['id'] ?? 0,
      username: json['username'] ?? '',
      nickname: json['nickname'] ?? '',
      email: json['email'],
      mobile: json['mobile'],
      avatar: json['avatar'],
      storeId: json['store_id'],
      storeName: json['store_name'],
      status: json['status'] ?? 'normal',
      permissions: List<String>.from(json['permissions'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'nickname': nickname,
      if (email != null) 'email': email,
      if (mobile != null) 'mobile': mobile,
      if (avatar != null) 'avatar': avatar,
      if (storeId != null) 'store_id': storeId,
      if (storeName != null) 'store_name': storeName,
      'status': status,
      'permissions': permissions,
    };
  }
}

/// 刷新Token请求模型
class RefreshTokenRequest {
  final String refreshToken;

  const RefreshTokenRequest({
    required this.refreshToken,
  });

  Map<String, dynamic> toJson() {
    return {
      'refresh_token': refreshToken,
    };
  }
}

/// 刷新Token响应数据模型
class RefreshTokenResponseData {
  final String accessToken;
  final String refreshToken;
  final String tokenType;
  final int expiresIn;

  const RefreshTokenResponseData({
    required this.accessToken,
    required this.refreshToken,
    required this.tokenType,
    required this.expiresIn,
  });

  factory RefreshTokenResponseData.fromJson(Map<String, dynamic> json) {
    return RefreshTokenResponseData(
      accessToken: json['access_token'] ?? '',
      refreshToken: json['refresh_token'] ?? '',
      tokenType: json['token_type'] ?? 'bearer',
      expiresIn: json['expires_in'] ?? 28800,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'access_token': accessToken,
      'refresh_token': refreshToken,
      'token_type': tokenType,
      'expires_in': expiresIn,
    };
  }
}

/// 修改密码请求模型
class ChangePasswordRequest {
  final String oldPassword;
  final String newPassword;

  const ChangePasswordRequest({
    required this.oldPassword,
    required this.newPassword,
  });

  Map<String, dynamic> toJson() {
    return {
      'old_password': oldPassword,
      'new_password': newPassword,
    };
  }
}
