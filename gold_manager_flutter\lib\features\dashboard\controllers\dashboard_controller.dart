import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../services/auth_service.dart';
import '../../../core/utils/logger_service.dart';
import '../../../core/routes/app_pages.dart';
import '../models/menu_item.dart';

/// 仪表盘控制器
/// 负责处理仪表盘页面的业务逻辑
class DashboardController extends GetxController {
  final AuthService _authService = Get.find<AuthService>();

  // 当前选中的导航项
  final RxInt selectedIndex = 0.obs;

  // 页面控制器
  final pageController = PageController();

  // 库存管理子页面状态
  final RxInt stockSubPageIndex = 0.obs; // 0: 总览, 1: 入库管理, 2: 出库管理, 3: 库存查询

  // 导航菜单项
  final RxList<DashboardMenuItem> menuItems = <DashboardMenuItem>[].obs;

  // 侧边栏状态
  final RxBool isSidebarExpanded = true.obs;
  // 用户信息
  final RxString userName = ''.obs;
  final RxString userNickname = ''.obs;
  final RxString userRole = ''.obs;
  final RxString storeName = ''.obs;
  // 强制UI刷新触发器（用于确保侧边栏UI更新）
  final RxInt uiRefreshTrigger = 0.obs;
  @override
  void onInit() {
    super.onInit();
    _loadUserInfo();
    _initializeMenuItems();
    // 注释掉路由更新逻辑，因为使用PageView而不是真正的路由切换
    // _updateSelectedIndexFromRoute();
    _setupAuthServiceListeners();
    // 🔥 暂时移除PageController监听器，避免与手动导航冲突
    // _setupPageControllerListener();

    // 监听选中状态变化，用于调试
    ever(selectedIndex, (int newIndex) {
      print(
        '🔍 选中状态变化: $newIndex -> ${newIndex < menuItems.length ? menuItems[newIndex].title : "未知"}',
      );
    });

    LoggerService.d('DashboardController 初始化');
  }

  @override
  void onClose() {
    pageController.dispose();
    super.onClose();
  }

  /// 加载用户信息
  void _loadUserInfo() {
    userName.value = _authService.userName.value;
    userNickname.value = _authService.userNickname.value;
    userRole.value = _authService.userRole.value;

    // 使用智能门店名称设置逻辑
    _updateStoreNameDisplay();

    LoggerService.i('DashboardController 用户信息加载完成:');
    LoggerService.i('   - userName: ${userName.value}');
    LoggerService.i('   - userNickname: ${userNickname.value}');
    LoggerService.i('   - userRole: ${userRole.value}');
    LoggerService.i('   - storeName: ${storeName.value}');
  }

  /// 设置AuthService监听器
  void _setupAuthServiceListeners() {
    // 监听AuthService中storeName的变化
    ever(_authService.storeName, (String newStoreName) {
      LoggerService.i('AuthService storeName 变化: $newStoreName');
      _updateStoreNameDisplay();
      LoggerService.i('DashboardController storeName 已更新: ${storeName.value}');
    });

    // 监听权限变化，因为权限影响门店名称显示
    ever(_authService.permissions, (List<String> newPermissions) {
      LoggerService.i('AuthService permissions 变化: $newPermissions');
      _updateStoreNameDisplay();
    });

    // 监听其他用户信息的变化
    ever(_authService.userName, (String newUserName) {
      userName.value = newUserName;
      LoggerService.i('DashboardController userName 已更新: ${userName.value}');
    });

    ever(_authService.userNickname, (String newUserNickname) {
      userNickname.value = newUserNickname;
      LoggerService.i(
        'DashboardController userNickname 已更新: ${userNickname.value}',
      );
    });

    ever(_authService.userRole, (String newUserRole) {
      userRole.value = newUserRole;
      LoggerService.i('DashboardController userRole 已更新: ${userRole.value}');
    });

    LoggerService.i('AuthService 监听器设置完成');
  }

  /// 更新门店名称显示逻辑（Windows平台兼容）
  void _updateStoreNameDisplay() {
    final authStoreName = _authService.storeName.value;
    final hasSuperAdmin = _authService.hasPermission('super.admin');
    final userName = _authService.userName.value;

    LoggerService.i('🏪 更新门店名称显示 (Windows兼容):');
    LoggerService.i('   - authStoreName: "$authStoreName"');
    LoggerService.i('   - hasSuperAdmin: $hasSuperAdmin');
    LoggerService.i('   - userName: "$userName"');

    // 🔧 Windows平台兼容：增强智能设置门店名称
    if (authStoreName.isNotEmpty) {
      storeName.value = authStoreName;
      LoggerService.i('   ✅ 使用实际门店名称: ${storeName.value}');
    } else if (hasSuperAdmin) {
      storeName.value = '所有门店';
      LoggerService.i('   ✅ 超级管理员显示: ${storeName.value}');
    } else if (userName == 'admin') {
      // 🔧 Windows兜底逻辑：如果权限检查失败但用户名是admin
      storeName.value = '所有门店';
      LoggerService.i('   ✅ admin用户兜底显示: ${storeName.value}');
    } else {
      storeName.value = '未知门店';
      LoggerService.i('   ⚠️  默认显示: ${storeName.value}');
    }
  }

  /// 初始化菜单项
  void _initializeMenuItems() {
    LoggerService.d('开始初始化菜单项...');

    menuItems.value = [
      DashboardMenuItem(
        title: '仪表盘', // 直接使用中文，避免翻译问题
        icon: Icons.dashboard,
        route: Routes.DASHBOARD,
        permission: 'dashboard_view',
      ),
      DashboardMenuItem(
        title: '首饰管理',
        icon: Icons.diamond,
        route: Routes.JEWELRY,
        permission: 'jewelry_view',
      ),
      DashboardMenuItem(
        title: '库存管理',
        icon: Icons.inventory,
        route: Routes.STOCK,
        permission: 'stock_view',
      ),
      DashboardMenuItem(
        title: '销售管理',
        icon: Icons.point_of_sale,
        route: Routes.SALES,
        permission: 'sales_view',
      ),
      DashboardMenuItem(
        title: '旧料回收',
        icon: Icons.recycling,
        route: Routes.RECYCLING,
        permission: 'recycling_view',
      ),
      DashboardMenuItem(
        title: '统计分析',
        icon: Icons.bar_chart,
        route: Routes.SETTINGS, // 临时替代STATISTIC_SALES常量
        permission: 'statistics_view',
      ),
      DashboardMenuItem(
        title: '系统设置',
        icon: Icons.settings,
        route: Routes.SETTINGS,
        permission: 'settings_view',
      ),
    ];

    LoggerService.d('菜单项初始化完成，共${menuItems.length}个项目'); // 暂时注释掉权限过滤，确保所有菜单都显示
    // _filterMenuItems();
  }

  /// 刷新用户信息（供外部调用）
  void refreshUserInfo() {
    _loadUserInfo();
    LoggerService.i('DashboardController 用户信息已刷新');
  }

  /// 切换导航项
  void changeTabIndex(int index) {
    if (index < 0 || index >= menuItems.length) {
      LoggerService.w('无效的导航索引: $index (总数: ${menuItems.length})');
      return;
    }

    final menuItem = menuItems[index];
    LoggerService.d('🎯 开始切换导航: ${menuItem.title} (索引: $index)');
    LoggerService.d('   当前selectedIndex: ${selectedIndex.value}');

    // 🔥 关键：先更新选中状态，再切换页面
    selectedIndex.value = index;
    LoggerService.d('✅ selectedIndex已更新为: $index');

    // 所有页面都在仪表板内通过PageView切换
    try {
      if (pageController.hasClients) {
        pageController.jumpToPage(index);
        LoggerService.d('✅ PageView 切换成功到索引: $index');
      } else {
        LoggerService.w('⚠️ PageController 未准备好');
      }
    } catch (e) {
      LoggerService.e('❌ PageView 切换失败', e);
    } // 确保UI立即更新
    update(); // 立即触发GetX UI更新
    triggerUIRefresh(); // 强制UI刷新
    LoggerService.d('🔄 UI更新触发完成');

    LoggerService.d(
      '🎯 导航完成: ${menuItem.title} (索引: $index), 最终selectedIndex: ${selectedIndex.value}',
    );
  }

  /// 直接设置选中索引（供内部使用，避免路由干扰）
  void setSelectedIndex(int index) {
    if (index >= 0 && index < menuItems.length) {
      selectedIndex.value = index;
      LoggerService.d('直接设置选中索引: $index (${menuItems[index].title})');
    }
  }

  /// 更新选中状态（供外部调用）
  void updateSelectedIndex(String route) {
    LoggerService.d('🎯 开始外部更新选中状态，路由: $route');
    printFullDebugState(); // 打印更新前状态

    // 根据路由映射到正确的页面索引
    int targetIndex;
    switch (route) {
      case '/dashboard':
        targetIndex = 0; // 仪表盘
        break;
      case '/jewelry':
        targetIndex = 1; // 首饰管理
        break;
      case '/stock':
      case '/stock/in':
      case '/stock/out':
      case '/stock/query':
        targetIndex = 2; // 库存管理（各种子页面都映射到库存管理）
        break;
      case '/sales':
        targetIndex = 3; // 销售管理
        break;
      case '/recycling':
        targetIndex = 4; // 旧料回收
        break;
      case '/statistics':
        targetIndex = 5; // 统计分析
        break;
      case '/settings':
        targetIndex = 6; // 系统设置
        break;
      default:
        LoggerService.w('未知路由: $route，无法更新选中状态');
        return;
    }

    if (targetIndex < menuItems.length) {
      LoggerService.d(
        '🎯 映射路由 $route 到索引 $targetIndex (${menuItems[targetIndex].title})',
      );

      // 🔥 直接调用 changeTabIndex，确保完整的导航流程
      changeTabIndex(targetIndex);

      // 再次打印状态确认
      WidgetsBinding.instance.addPostFrameCallback((_) {
        LoggerService.d('📊 更新完成后状态:');
        printFullDebugState();
      });

      LoggerService.d('✅ 外部导航完成: $route -> ${menuItems[targetIndex].title}');
    }
  }

  /// 切换侧边栏展开/收缩状态
  void toggleSidebar() {
    isSidebarExpanded.value = !isSidebarExpanded.value;
  }

  /// 切换库存管理子页面
  void changeStockSubPage(int subPageIndex) {
    LoggerService.d('切换库存子页面到索引: $subPageIndex');
    stockSubPageIndex.value = subPageIndex;
  }

  /// 导航到库存管理的特定子页面
  void navigateToStockSubPage(String subPageType) {
    // 首先切换到库存管理主页面
    changeTabIndex(2); // 库存管理是索引2

    // 然后切换到对应的子页面
    switch (subPageType) {
      case 'overview':
        changeStockSubPage(0);
        break;
      case 'in':
        changeStockSubPage(1);
        break;
      case 'out':
        changeStockSubPage(2);
        break;
      case 'query':
        changeStockSubPage(3);
        break;
      default:
        changeStockSubPage(0);
    }
    LoggerService.d('导航到库存子页面: $subPageType');
  }

  /// 退出登录
  Future<void> logout() async {
    final confirmed =
        await Get.dialog<bool>(
          AlertDialog(
            title: Text('logout'.tr),
            content: Text('logout_confirm'.tr),
            actions: [
              TextButton(
                onPressed: () => Get.back(result: false),
                child: Text('cancel'.tr),
              ),
              TextButton(
                onPressed: () => Get.back(result: true),
                child: Text('confirm'.tr),
              ),
            ],
          ),
        ) ??
        false;

    if (confirmed) {
      await _authService.logout();
      Get.offAllNamed(Routes.LOGIN);
    }
  }

  /// 强制同步侧边栏选中状态（用于调试和边缘情况处理）
  void forceSyncSidebarSelection() {
    if (pageController.hasClients && pageController.page != null) {
      final currentPage = pageController.page!.round();
      if (currentPage != selectedIndex.value &&
          currentPage >= 0 &&
          currentPage < menuItems.length) {
        selectedIndex.value = currentPage;
        LoggerService.d(
          '🔄 强制同步侧边栏选中状态到: $currentPage (${menuItems[currentPage].title})',
        );
      }
    }
  }

  /// 获取当前页面索引（用于调试）
  int getCurrentPageIndex() {
    if (pageController.hasClients && pageController.page != null) {
      return pageController.page!.round();
    }
    return selectedIndex.value;
  }

  /// 触发UI强制刷新（在状态更新后调用）
  void triggerUIRefresh() {
    uiRefreshTrigger.value++;
    LoggerService.d('🔄 触发UI强制刷新: ${uiRefreshTrigger.value}');
  }

  /// 调试方法：检查当前导航状态
  void debugNavigationState() {
    LoggerService.d('🔍 当前导航状态检查:');
    LoggerService.d('   selectedIndex: ${selectedIndex.value}');
    LoggerService.d(
      '   pageController.page: ${pageController.hasClients ? pageController.page : "未初始化"}',
    );
    LoggerService.d('   menuItems长度: ${menuItems.length}');
    if (selectedIndex.value < menuItems.length) {
      LoggerService.d('   当前应选中: ${menuItems[selectedIndex.value].title}');
    }
  }

  /// 调试方法：打印当前完整状态
  void printFullDebugState() {
    LoggerService.d('========== 完整调试状态 ==========');
    LoggerService.d('selectedIndex.value: ${selectedIndex.value}');
    LoggerService.d('pageController.hasClients: ${pageController.hasClients}');
    if (pageController.hasClients && pageController.page != null) {
      LoggerService.d('pageController.page: ${pageController.page}');
    } else {
      LoggerService.d('pageController.page: 未初始化');
    }
    LoggerService.d('menuItems.length: ${menuItems.length}');
    for (int i = 0; i < menuItems.length; i++) {
      final isSelected = i == selectedIndex.value;
      LoggerService.d('  [$i] ${menuItems[i].title} ${isSelected ? "✅" : ""}');
    }
    LoggerService.d('===========================');
  }
}
