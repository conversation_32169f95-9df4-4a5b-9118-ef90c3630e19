import '../common/date_utils.dart';
import '../jewelry/jewelry.dart';

/// 销售明细模型
class SaleItem {
  final int id;
  final int saleId;
  final int jewelryId;
  final String? barcode;
  final double originalPrice; // 原价
  final double actualPrice; // 实际售价
  final double discountAmount; // 折扣金额
  final String? discountReason; // 折扣原因
  final DateTime? createTime;
  
  // 关联对象
  final Jewelry? jewelry;
  
  const SaleItem({
    required this.id,
    required this.saleId,
    required this.jewelryId,
    this.barcode,
    required this.originalPrice,
    required this.actualPrice,
    this.discountAmount = 0.0,
    this.discountReason,
    this.createTime,
    this.jewelry,
  });
  
  /// 从JSON构造
  factory SaleItem.fromJson(Map<String, dynamic> json) {
    return SaleItem(
      id: json['id'],
      saleId: json['sale_id'],
      jewelryId: json['jewelry_id'],
      barcode: json['barcode'],
      originalPrice: (json['original_price'] ?? 0.0).toDouble(),
      actualPrice: (json['actual_price'] ?? 0.0).toDouble(),
      discountAmount: (json['discount_amount'] ?? 0.0).toDouble(),
      discountReason: json['discount_reason'],
      createTime: DateUtil.fromUnixTimestamp(json['createtime']),
      jewelry: json['jewelry'] != null ? Jewelry.fromJson(json['jewelry']) : null,
    );
  }
  
  /// 转为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'sale_id': saleId,
      'jewelry_id': jewelryId,
      'barcode': barcode,
      'original_price': originalPrice,
      'actual_price': actualPrice,
      'discount_amount': discountAmount,
      'discount_reason': discountReason,
      'createtime': DateUtil.toUnixTimestamp(createTime),
    };
  }
  
  /// 计算折扣率
  double get discountRate {
    if (originalPrice <= 0) return 0;
    return (discountAmount / originalPrice) * 100;
  }
  
  /// 创建一个新实例，但使用部分属性
  SaleItem copyWith({
    int? id,
    int? saleId,
    int? jewelryId,
    String? barcode,
    double? originalPrice,
    double? actualPrice,
    double? discountAmount,
    String? discountReason,
    DateTime? createTime,
    Jewelry? jewelry,
  }) {
    return SaleItem(
      id: id ?? this.id,
      saleId: saleId ?? this.saleId,
      jewelryId: jewelryId ?? this.jewelryId,
      barcode: barcode ?? this.barcode,
      originalPrice: originalPrice ?? this.originalPrice,
      actualPrice: actualPrice ?? this.actualPrice,
      discountAmount: discountAmount ?? this.discountAmount,
      discountReason: discountReason ?? this.discountReason,
      createTime: createTime ?? this.createTime,
      jewelry: jewelry ?? this.jewelry,
    );
  }
} 