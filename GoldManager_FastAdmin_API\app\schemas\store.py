"""
门店管理相关的数据验证模型
定义门店的创建、更新、响应等数据结构
"""

from typing import Optional, List
from pydantic import BaseModel, Field, validator
from datetime import datetime


class StoreBase(BaseModel):
    """门店基础模型"""
    name: str = Field(..., min_length=1, max_length=100, description="门店名称")
    address: Optional[str] = Field(None, max_length=255, description="门店地址")
    phone: Optional[str] = Field(None, max_length=20, description="联系电话")
    status: int = Field(1, ge=0, le=1, description="状态:0=关闭,1=正常")


class StoreCreate(StoreBase):
    """创建门店的请求模型"""
    
    @validator('name')
    def validate_name(cls, v):
        if not v or not v.strip():
            raise ValueError('门店名称不能为空')
        return v.strip()
    
    @validator('phone')
    def validate_phone(cls, v):
        if v and len(v.strip()) > 0:
            # 简单的电话号码验证
            phone = v.strip()
            if not phone.replace('-', '').replace(' ', '').isdigit():
                raise ValueError('电话号码格式不正确')
            return phone
        return v


class StoreUpdate(BaseModel):
    """更新门店的请求模型"""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="门店名称")
    address: Optional[str] = Field(None, max_length=255, description="门店地址")
    phone: Optional[str] = Field(None, max_length=20, description="联系电话")
    status: Optional[int] = Field(None, ge=0, le=1, description="状态:0=关闭,1=正常")
    
    @validator('name')
    def validate_name(cls, v):
        if v is not None and (not v or not v.strip()):
            raise ValueError('门店名称不能为空')
        return v.strip() if v else v
    
    @validator('phone')
    def validate_phone(cls, v):
        if v and len(v.strip()) > 0:
            phone = v.strip()
            if not phone.replace('-', '').replace(' ', '').isdigit():
                raise ValueError('电话号码格式不正确')
            return phone
        return v


class StoreResponse(StoreBase):
    """门店响应模型"""
    id: int = Field(..., description="门店ID")
    createtime: Optional[int] = Field(None, description="创建时间戳")
    updatetime: Optional[int] = Field(None, description="更新时间戳")
    
    # 扩展信息
    jewelry_count: Optional[int] = Field(0, description="商品数量")
    admin_count: Optional[int] = Field(0, description="管理员数量")
    
    class Config:
        from_attributes = True


class StoreListResponse(BaseModel):
    """门店列表响应模型"""
    items: List[StoreResponse] = Field(..., description="门店列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页")
    page_size: int = Field(..., description="每页数量")
    total_pages: int = Field(..., description="总页数")


class StoreStatistics(BaseModel):
    """门店统计信息模型"""
    store_id: int = Field(..., description="门店ID")
    store_name: str = Field(..., description="门店名称")
    total_jewelry: int = Field(0, description="商品总数")
    active_jewelry: int = Field(0, description="上架商品数")
    total_admins: int = Field(0, description="管理员总数")
    active_admins: int = Field(0, description="活跃管理员数")
    # 为第二阶段库存管理预留字段
    # total_stock_value: Optional[Decimal] = Field(None, description="库存总价值")
    # pending_stock_in: Optional[int] = Field(None, description="待入库数量")
    # pending_stock_out: Optional[int] = Field(None, description="待出库数量") 