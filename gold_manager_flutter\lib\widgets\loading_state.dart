import 'package:flutter/material.dart';
import 'dart:async';

/// 加载状态组件
/// 用于显示数据加载中的状态，带有超时保护机制
class LoadingState extends StatefulWidget {
  /// 加载文字
  final String? text;

  /// 背景颜色
  final Color? backgroundColor;

  /// 是否显示白色卡片
  final bool showCard;

  /// 超时时间（秒），默认60秒
  final int timeoutSeconds;

  /// 超时回调
  final VoidCallback? onTimeout;

  /// 构造函数
  const LoadingState({
    super.key,
    this.text,
    this.backgroundColor,
    this.showCard = true,
    required this.timeoutSeconds,
    this.onTimeout,
  });

  @override
  State<LoadingState> createState() => _LoadingStateState();
}

class _LoadingStateState extends State<LoadingState> {
  Timer? _timeoutTimer;
  bool _isTimedOut = false;

  @override
  void initState() {
    super.initState();
    _startTimeoutTimer();
  }

  @override
  void dispose() {
    _timeoutTimer?.cancel();
    super.dispose();
  }

  void _startTimeoutTimer() {
    _timeoutTimer = Timer(Duration(seconds: widget.timeoutSeconds), () {
      if (mounted) {
        setState(() {
          _isTimedOut = true;
        });
        widget.onTimeout?.call();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_isTimedOut) {
      return Container(
        color: widget.backgroundColor ?? Colors.black12,
        width: double.infinity,
        height: double.infinity,
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.error_outline,
                size: 48,
                color: Colors.red[600],
              ),
              const SizedBox(height: 16),
              const Text(
                '加载超时',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                '请检查网络连接或稍后重试',
                style: TextStyle(
                  fontSize: 13,
                  color: Colors.black54,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    final loadingContent = Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Colors.blue[600]!),
          strokeWidth: 3,
        ),
        if (widget.text != null) ...[
          const SizedBox(height: 16),
          Text(
            widget.text!,
            style: const TextStyle(
              fontSize: 13,
              color: Colors.black87,
              fontWeight: FontWeight.normal,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );

    return Container(
      color: widget.backgroundColor ?? Colors.black12,
      width: double.infinity,
      height: double.infinity,
      child: Center(
        child: widget.showCard ? Container(
          width: 180,
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            boxShadow: const [
              BoxShadow(
                color: Colors.black26,
                blurRadius: 10,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: loadingContent,
        ) : loadingContent,
      ),
    );
  }
}