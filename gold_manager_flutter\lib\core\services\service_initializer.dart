import 'package:get/get.dart';

import '../utils/logger_service.dart';
import 'api_service.dart';
import 'api_client.dart';
import 'storage_service.dart';
import '../../services/auth_service.dart';
import 'theme_service.dart';
import '../../services/jewelry_category_service.dart';
import '../../services/stock_out_service.dart';

/// 服务初始化器
/// 负责初始化和注册应用所有服务
class ServiceInitializer {
  /// 初始化所有服务
  static Future<void> init() async {
    try {
      LoggerService.init();
      LoggerService.i('开始初始化服务...');

      // 注册存储服务(需优先初始化)
      await Get.putAsync<StorageService>(() async {
        final service = StorageService();
        await service.onInit();
        return service;
      }, permanent: true);

      // 注册API服务
      Get.put<ApiService>(ApiService(), permanent: true);

      // 注册API客户端
      Get.put<ApiClient>(ApiClient(), permanent: true);

      // 注册认证服务
      Get.put<AuthService>(AuthService(), permanent: true);

      // 注册主题服务
      Get.put<ThemeService>(ThemeService(), permanent: true);

      // 注册首饰分类服务
      Get.put(JewelryCategoryService(apiService: Get.find<ApiService>()), permanent: true);

      // 注册出库单服务
      Get.put<StockOutService>(StockOutService(), permanent: true);

      // 在此处注册其他服务

      LoggerService.i('所有服务初始化完成');
    } catch (e, stackTrace) {
      LoggerService.e('服务初始化失败', e, stackTrace);
      rethrow;
    }
  }
}