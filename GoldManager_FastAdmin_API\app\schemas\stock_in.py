"""
入库管理数据验证模型
定义入库单相关的Pydantic模型用于API数据验证
"""

from typing import List, Optional
from decimal import Decimal
from datetime import datetime
from pydantic import BaseModel, Field, validator


# ================= 入库单明细模型 =================

class StockInItemBase(BaseModel):
    """入库单明细基础模型"""
    jewelry_id: Optional[int] = Field(None, description="首饰ID（审核通过后才设置）")
    barcode: str = Field(..., max_length=50, description="商品条码")
    name: str = Field(..., max_length=100, description="商品名称")
    category_id: int = Field(..., description="分类ID")
    ring_size: Optional[str] = Field(None, max_length=20, description="圈口号")

    # 金重相关
    gold_weight: Optional[Decimal] = Field(default=Decimal('0.00'), ge=0, description="金重(克)")
    gold_price: Optional[Decimal] = Field(default=Decimal('0.00'), ge=0, description="金进价(克价)")
    gold_cost: Optional[Decimal] = Field(default=Decimal('0.00'), ge=0, description="金成本")

    # 银重相关
    silver_weight: Optional[Decimal] = Field(default=Decimal('0.00'), ge=0, description="银重(克)")
    total_weight: Optional[Decimal] = Field(default=Decimal('0.00'), ge=0, description="总重")
    silver_price: Optional[Decimal] = Field(default=Decimal('0.00'), ge=0, description="银进价(克价)")
    silver_cost: Optional[Decimal] = Field(default=Decimal('0.00'), ge=0, description="银成本")

    # 工费相关
    silver_work_type: Optional[int] = Field(default=0, description="银工费方式:0=按克,1=按件")
    silver_work_price: Optional[Decimal] = Field(default=Decimal('0.00'), ge=0, description="银工费")
    silver_work_cost: Optional[Decimal] = Field(default=Decimal('0.00'), ge=0, description="银工费成本")
    plating_cost: Optional[Decimal] = Field(default=Decimal('0.00'), ge=0, description="电铸费")

    # 成本和定价
    total_cost: Optional[Decimal] = Field(default=Decimal('0.00'), ge=0, description="进货总成本")
    wholesale_work_price: Optional[Decimal] = Field(default=Decimal('0.00'), ge=0, description="批发工费")
    retail_work_price: Optional[Decimal] = Field(default=Decimal('0.00'), ge=0, description="零售工费")
    piece_work_price: Optional[Decimal] = Field(default=Decimal('0.00'), ge=0, description="件工费")


class StockInItemCreate(StockInItemBase):
    """创建入库单明细模型"""
    pass


class StockInItemUpdate(BaseModel):
    """更新入库单明细模型"""
    jewelry_id: Optional[int] = Field(None, description="首饰ID")
    barcode: Optional[str] = Field(None, max_length=50, description="商品条码")
    name: Optional[str] = Field(None, max_length=100, description="商品名称")
    category_id: Optional[int] = Field(None, description="分类ID")
    ring_size: Optional[str] = Field(None, max_length=20, description="圈口号")

    # 成本相关字段可选更新
    gold_weight: Optional[Decimal] = Field(None, ge=0, description="金重(克)")
    gold_price: Optional[Decimal] = Field(None, ge=0, description="金进价(克价)")
    total_cost: Optional[Decimal] = Field(None, ge=0, description="进货总成本")


class StockInItemResponse(StockInItemBase):
    """入库单明细响应模型"""
    id: int = Field(..., description="明细ID")
    stock_in_id: int = Field(..., description="入库单ID")
    createtime: Optional[int] = Field(None, description="创建时间")

    # 关联对象
    jewelry: Optional[dict] = Field(None, description="关联首饰信息")

    class Config:
        from_attributes = True


# ================= 入库单主表模型 =================

class StockInBase(BaseModel):
    """入库单基础模型"""
    store_id: int = Field(..., description="门店ID")
    supplier: Optional[str] = Field(None, max_length=100, description="供应商")
    remark: Optional[str] = Field(None, description="备注")


class StockInCreate(StockInBase):
    """创建入库单模型"""
    items: List[StockInItemCreate] = Field(..., description="入库商品明细")

    @validator('items')
    def validate_items_not_empty(cls, v):
        if not v:
            raise ValueError('入库商品明细不能为空')
        return v


class StockInUpdate(BaseModel):
    """更新入库单模型"""
    store_id: Optional[int] = Field(None, description="门店ID")
    supplier: Optional[str] = Field(None, max_length=100, description="供应商")
    remark: Optional[str] = Field(None, description="备注")
    items: Optional[List[StockInItemCreate]] = Field(None, description="入库商品明细")


class StockInResponse(StockInBase):
    """入库单响应模型"""
    id: int = Field(..., description="入库单ID")
    order_no: str = Field(..., description="入库单号")
    total_amount: Decimal = Field(..., description="总金额")
    operator_id: int = Field(..., description="操作员ID")
    audit_user_id: Optional[int] = Field(None, description="审核员ID")
    audit_time: Optional[int] = Field(None, description="审核时间")
    audit_note: Optional[str] = Field(None, description="审核备注")
    status: int = Field(..., description="状态:1=正常,0=禁用")
    createtime: Optional[int] = Field(None, description="创建时间")
    updatetime: Optional[int] = Field(None, description="更新时间")

    # 关联对象的简化信息（只包含ID和名称）
    store_name: Optional[str] = Field(None, description="门店名称")
    operator_name: Optional[str] = Field(None, description="操作员姓名")
    auditor_name: Optional[str] = Field(None, description="审核员姓名")

    # 添加商品件数字段
    item_count: int = Field(0, description="入库商品件数")

    # 🔑 关键修复：添加商品明细列表字段
    items: Optional[List[dict]] = Field(None, description="入库商品明细列表")

    class Config:
        from_attributes = True


# ================= 入库单统计模型 =================

class StockInStatistics(BaseModel):
    """入库单统计模型"""
    total_count: int = Field(..., description="总数量")
    normal_count: int = Field(..., description="正常数量")
    disabled_count: int = Field(..., description="禁用数量")
    total_amount: Decimal = Field(..., description="总金额")
    store_distribution: List[dict] = Field(..., description="门店分布")


# ================= 状态更新模型 =================

class StockInStatusUpdate(BaseModel):
    """入库单状态更新模型"""
    status: int = Field(..., ge=0, le=3, description="状态:0=草稿,1=待审核,2=已通过,3=已拒绝")
    audit_note: Optional[str] = Field(None, max_length=255, description="审核备注")


# ================= 查询参数模型 =================

class StockInQueryParams(BaseModel):
    """入库单查询参数模型"""
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(20, ge=1, le=100, description="每页数量")
    keyword: Optional[str] = Field(None, description="关键词搜索(单号、备注)")
    store_id: Optional[int] = Field(None, description="门店ID筛选")
    status: Optional[int] = Field(None, ge=0, le=4, description="状态筛选(0=草稿,1=待审核,2=已审核,3=已拒绝,4=已取消)")
    start_date: Optional[str] = Field(None, description="开始日期(YYYY-MM-DD)")
    end_date: Optional[str] = Field(None, description="结束日期(YYYY-MM-DD)")
    operator_id: Optional[int] = Field(None, description="操作员ID筛选")
    supplier: Optional[str] = Field(None, description="供应商筛选")