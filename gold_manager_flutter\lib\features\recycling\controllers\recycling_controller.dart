import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../models/recycling/recycling_model.dart';
import '../services/recycling_service.dart';
import '../../dashboard/controllers/dashboard_controller.dart';
import '../../../core/utils/logger_service.dart';

/// 回收管理控制器
class RecyclingController extends GetxController {
  final RecyclingService _recyclingService = RecyclingService();

  // 可观察变量
  final RxBool isLoading = false.obs;
  final RxList<RecyclingOrder> recyclingOrders = <RecyclingOrder>[].obs;
  final RxInt statusFilter = (-1).obs; // -1 表示全部
  final RxString searchQuery = ''.obs;
  final TextEditingController searchController = TextEditingController();

  // 分页相关变量
  final RxInt currentPage = 1.obs;
  final RxInt totalPages = 1.obs;
  final RxInt totalRecords = 0.obs;
  final int pageSize = 20;

  // 详情页使用的变量
  final Rx<RecyclingOrder?> currentOrder = Rx<RecyclingOrder?>(null);
  final RxBool isDetailLoading = false.obs;

  @override
  void onInit() {
    super.onInit();
    _updateDashboardNavigation();
    loadRecyclingOrders();

    // 监听搜索查询变化
    debounce(
      searchQuery,
      (_) => loadRecyclingOrders(),
      time: const Duration(milliseconds: 500),
    );
  }

  /// 更新仪表盘导航状态
  void _updateDashboardNavigation() {
    try {
      final dashboardController = Get.find<DashboardController>();
      dashboardController.updateSelectedIndex('/recycling');
    } catch (e) {
      LoggerService.w('无法找到DashboardController: $e');
    }
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }

  /// 加载回收单列表
  Future<void> loadRecyclingOrders() async {
    try {
      isLoading.value = true;

      // 构建查询参数
      final Map<String, dynamic> params = {
        'page': currentPage.value,
        'page_size': pageSize,
      };

      if (statusFilter.value >= 0) {
        params['status'] = statusFilter.value;
      }
      if (searchQuery.value.isNotEmpty) {
        params['search'] = searchQuery.value;
      }

      final result = await _recyclingService.getRecyclingOrdersWithPagination(
        params,
      );
      recyclingOrders.assignAll(result.orders);
      totalPages.value = result.totalPages;
      totalRecords.value = result.totalRecords;
    } catch (e) {
      LoggerService.e('加载回收单列表失败', e);
      Get.snackbar(
        '加载失败',
        '加载回收单列表失败，请稍后重试',
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// 加载回收单详情
  Future<void> loadRecyclingOrderDetail(int id) async {
    try {
      LoggerService.d('🔍 RecyclingController.loadRecyclingOrderDetail 开始，ID: $id');
      isDetailLoading.value = true;

      // 清空当前详情数据
      currentOrder.value = null;

      LoggerService.d('🚀 调用RecyclingService.getRecyclingOrderDetail');
      final order = await _recyclingService.getRecyclingOrderDetail(id);

      LoggerService.d('✅ 成功获取回收单详情');
      LoggerService.d('📊 回收单信息: ${order.orderNo}, 明细数量: ${order.items.length}');

      // 详细记录每个明细项
      for (int i = 0; i < order.items.length; i++) {
        final item = order.items[i];
        LoggerService.d('📦 明细${i + 1}: ${item.itemName}, 分类: ${item.categoryName}, 金额: ${item.amount}');
      }

      currentOrder.value = order;
      LoggerService.d('✅ 回收单详情已设置到currentOrder');

    } catch (e) {
      LoggerService.e('❌ 加载回收单详情失败', e);
      Get.snackbar(
        '加载失败',
        '加载回收单详情失败，请稍后重试: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      rethrow; // 重新抛出异常，让调用方知道失败了
    } finally {
      isDetailLoading.value = false;
      LoggerService.d('🔄 isDetailLoading 设置为 false');
    }
  }

  /// 创建回收单
  Future<bool> createRecyclingOrder(Map<String, dynamic> data) async {
    try {
      isLoading.value = true;
      await _recyclingService.createRecyclingOrder(data);
      Get.snackbar('创建成功', '回收单创建成功', snackPosition: SnackPosition.BOTTOM);
      await loadRecyclingOrders(); // 重新加载列表
      return true;
    } catch (e) {
      print('创建回收单失败: $e');
      Get.snackbar(
        '创建失败',
        '创建回收单失败，请稍后重试',
        snackPosition: SnackPosition.BOTTOM,
      );
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// 更新回收单
  Future<bool> updateRecyclingOrder(Map<String, dynamic> data) async {
    try {
      isLoading.value = true;
      await _recyclingService.updateRecyclingOrder(data);
      Get.snackbar('更新成功', '回收单更新成功', snackPosition: SnackPosition.BOTTOM);
      await loadRecyclingOrders(); // 重新加载列表
      return true;
    } catch (e) {
      print('更新回收单失败: $e');
      Get.snackbar(
        '更新失败',
        '更新回收单失败，请稍后重试',
        snackPosition: SnackPosition.BOTTOM,
      );
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// 更新回收单状态
  Future<bool> updateRecyclingOrderStatus(int id, int status) async {
    try {
      isLoading.value = true;
      await _recyclingService.updateRecyclingOrderStatus(id, status);

      // 更新本地列表中的状态
      await loadRecyclingOrders();

      // 如果当前正在查看的是这个订单，也更新其状态
      if (currentOrder.value?.id == id) {
        loadRecyclingOrderDetail(id);
      }

      Get.snackbar('更新成功', '回收单状态更新成功', snackPosition: SnackPosition.BOTTOM);
      return true;
    } catch (e) {
      print('更新回收单状态失败: $e');
      Get.snackbar(
        '更新失败',
        '更新回收单状态失败，请稍后重试',
        snackPosition: SnackPosition.BOTTOM,
      );
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// 删除回收单
  Future<bool> deleteRecyclingOrder(int id) async {
    try {
      isLoading.value = true;
      await _recyclingService.deleteRecyclingOrder(id);

      // 从列表中移除
      recyclingOrders.removeWhere((order) => order.id == id);

      Get.snackbar('删除成功', '回收单删除成功', snackPosition: SnackPosition.BOTTOM);
      return true;
    } catch (e) {
      LoggerService.e('删除回收单失败', e);
      Get.snackbar(
        '删除失败',
        '删除回收单失败，请稍后重试',
        snackPosition: SnackPosition.BOTTOM,
      );
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// 分页相关方法

  /// 跳转到指定页面
  void goToPage(int page) {
    if (page < 1 || page > totalPages.value) return;
    currentPage.value = page;
    loadRecyclingOrders();
  }

  /// 搜索时重置到第一页
  void search() {
    currentPage.value = 1;
    loadRecyclingOrders();
  }

  /// 重置筛选条件
  void resetFilters() {
    statusFilter.value = -1;
    searchQuery.value = '';
    searchController.clear();
    currentPage.value = 1;
    loadRecyclingOrders();
  }
}
