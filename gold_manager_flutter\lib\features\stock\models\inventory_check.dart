import 'package:flutter/material.dart';

/// 库存盘点数据模型
/// 
/// 基于fa_inventory_check表结构设计
/// 支持盘点单的完整生命周期管理
class InventoryCheck {
  final int id;
  final String checkNo;
  final int storeId;
  final String? storeName;
  final int status; // 0=进行中,1=已完成,2=已取消
  final DateTime? startTime;
  final DateTime? endTime;
  final int operatorId;
  final String? operatorName;
  final int totalCount;
  final int checkedCount;
  final int differenceCount;
  final String? remark;
  final DateTime createTime;
  final DateTime updateTime;

  InventoryCheck({
    required this.id,
    required this.checkNo,
    required this.storeId,
    this.storeName,
    required this.status,
    this.startTime,
    this.endTime,
    required this.operatorId,
    this.operatorName,
    required this.totalCount,
    required this.checkedCount,
    required this.differenceCount,
    this.remark,
    required this.createTime,
    required this.updateTime,
  });

  /// 状态显示文本
  String get statusText {
    switch (status) {
      case 0:
        return '进行中';
      case 1:
        return '已完成';
      case 2:
        return '已取消';
      default:
        return '未知';
    }
  }

  /// 状态颜色
  Color get statusColor {
    switch (status) {
      case 0:
        return Colors.orange;
      case 1:
        return Colors.green;
      case 2:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  /// 盘点进度百分比
  double get progress {
    if (totalCount == 0) return 0.0;
    return checkedCount / totalCount;
  }

  /// 是否可以编辑
  bool get canEdit {
    return status == 0; // 只有进行中的盘点单可以编辑
  }

  /// 是否可以删除
  bool get canDelete {
    return status == 0; // 只有进行中的盘点单可以删除
  }

  /// 从JSON创建对象
  factory InventoryCheck.fromJson(Map<String, dynamic> json) {
    return InventoryCheck(
      id: json['id'] ?? 0,
      checkNo: json['check_no'] ?? '',
      storeId: json['store_id'] ?? 0,
      storeName: json['store_name'],
      status: json['status'] ?? 0,
      startTime: json['start_time'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(json['start_time'] * 1000)
          : null,
      endTime: json['end_time'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(json['end_time'] * 1000)
          : null,
      operatorId: json['operator_id'] ?? 0,
      operatorName: json['operator_name'],
      totalCount: json['total_count'] ?? 0,
      checkedCount: json['checked_count'] ?? 0,
      differenceCount: json['difference_count'] ?? 0,
      remark: json['remark'],
      createTime: DateTime.fromMillisecondsSinceEpoch(
        (json['createtime'] ?? 0) * 1000,
      ),
      updateTime: DateTime.fromMillisecondsSinceEpoch(
        (json['updatetime'] ?? 0) * 1000,
      ),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'check_no': checkNo,
      'store_id': storeId,
      'store_name': storeName,
      'status': status,
      'start_time': startTime != null ? startTime!.millisecondsSinceEpoch ~/ 1000 : null,
      'end_time': endTime != null ? endTime!.millisecondsSinceEpoch ~/ 1000 : null,
      'operator_id': operatorId,
      'operator_name': operatorName,
      'total_count': totalCount,
      'checked_count': checkedCount,
      'difference_count': differenceCount,
      'remark': remark,
      'createtime': createTime.millisecondsSinceEpoch ~/ 1000,
      'updatetime': updateTime.millisecondsSinceEpoch ~/ 1000,
    };
  }

  /// 复制对象并修改部分属性
  InventoryCheck copyWith({
    int? id,
    String? checkNo,
    int? storeId,
    String? storeName,
    int? status,
    DateTime? startTime,
    DateTime? endTime,
    int? operatorId,
    String? operatorName,
    int? totalCount,
    int? checkedCount,
    int? differenceCount,
    String? remark,
    DateTime? createTime,
    DateTime? updateTime,
  }) {
    return InventoryCheck(
      id: id ?? this.id,
      checkNo: checkNo ?? this.checkNo,
      storeId: storeId ?? this.storeId,
      storeName: storeName ?? this.storeName,
      status: status ?? this.status,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      operatorId: operatorId ?? this.operatorId,
      operatorName: operatorName ?? this.operatorName,
      totalCount: totalCount ?? this.totalCount,
      checkedCount: checkedCount ?? this.checkedCount,
      differenceCount: differenceCount ?? this.differenceCount,
      remark: remark ?? this.remark,
      createTime: createTime ?? this.createTime,
      updateTime: updateTime ?? this.updateTime,
    );
  }
}

/// 盘点明细数据模型
class InventoryCheckItem {
  final int id;
  final int checkId;
  final int jewelryId;
  final String barcode;
  final String name;
  final int categoryId;
  final String? categoryName;
  final String? ringSize;
  final int status; // 0=未盘点,1=已盘点
  final int systemStock;
  final int? actualStock;
  final int? difference;
  final DateTime? checkTime;
  final int? checkUserId;
  final String? checkUserName;
  final String? remark;
  final DateTime createTime;

  InventoryCheckItem({
    required this.id,
    required this.checkId,
    required this.jewelryId,
    required this.barcode,
    required this.name,
    required this.categoryId,
    this.categoryName,
    this.ringSize,
    required this.status,
    required this.systemStock,
    this.actualStock,
    this.difference,
    this.checkTime,
    this.checkUserId,
    this.checkUserName,
    this.remark,
    required this.createTime,
  });

  /// 盘点状态文本
  String get statusText {
    switch (status) {
      case 0:
        return '未盘点';
      case 1:
        return '已盘点';
      default:
        return '未知';
    }
  }

  /// 盘点状态颜色
  Color get statusColor {
    switch (status) {
      case 0:
        return Colors.orange;
      case 1:
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  /// 是否有差异
  bool get hasDifference {
    return difference != null && difference != 0;
  }

  /// 差异类型文本
  String get differenceText {
    if (difference == null) return '-';
    if (difference! > 0) return '+$difference';
    if (difference! < 0) return '$difference';
    return '0';
  }

  /// 从JSON创建对象
  factory InventoryCheckItem.fromJson(Map<String, dynamic> json) {
    return InventoryCheckItem(
      id: json['id'] ?? 0,
      checkId: json['check_id'] ?? 0,
      jewelryId: json['jewelry_id'] ?? 0,
      barcode: json['barcode'] ?? '',
      name: json['name'] ?? '',
      categoryId: json['category_id'] ?? 0,
      categoryName: json['category_name'],
      ringSize: json['ring_size'],
      status: json['status'] ?? 0,
      systemStock: json['system_stock'] ?? 1,
      actualStock: json['actual_stock'],
      difference: json['difference'],
      checkTime: json['check_time'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(json['check_time'] * 1000)
          : null,
      checkUserId: json['check_user_id'],
      checkUserName: json['check_user_name'],
      remark: json['remark'],
      createTime: DateTime.fromMillisecondsSinceEpoch(
        (json['createtime'] ?? 0) * 1000,
      ),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'check_id': checkId,
      'jewelry_id': jewelryId,
      'barcode': barcode,
      'name': name,
      'category_id': categoryId,
      'category_name': categoryName,
      'ring_size': ringSize,
      'status': status,
      'system_stock': systemStock,
      'actual_stock': actualStock,
      'difference': difference,
      'check_time': checkTime != null ? checkTime!.millisecondsSinceEpoch ~/ 1000 : null,
      'check_user_id': checkUserId,
      'check_user_name': checkUserName,
      'remark': remark,
      'createtime': createTime.millisecondsSinceEpoch ~/ 1000,
    };
  }
}

/// 创建盘点单请求模型
class InventoryCheckCreateRequest {
  final int storeId;
  final String? remark;
  final List<InventoryCheckItemCreate>? items; // 盘点商品明细

  InventoryCheckCreateRequest({
    required this.storeId,
    this.remark,
    this.items,
  });

  Map<String, dynamic> toJson() {
    return {
      'store_id': storeId,
      'remark': remark,
      'items': items?.map((item) => item.toJson()).toList() ?? [],
    };
  }
}

/// 创建盘点明细项目模型
class InventoryCheckItemCreate {
  final int jewelryId;
  final String barcode;
  final String name;
  final int categoryId;
  final String? ringSize;
  final int systemStock;
  final String? remark;

  InventoryCheckItemCreate({
    required this.jewelryId,
    required this.barcode,
    required this.name,
    required this.categoryId,
    this.ringSize,
    required this.systemStock,
    this.remark,
  });

  Map<String, dynamic> toJson() {
    return {
      'jewelry_id': jewelryId,
      'barcode': barcode,
      'name': name,
      'category_id': categoryId,
      'ring_size': ringSize,
      'system_stock': systemStock,
      'remark': remark,
    };
  }
}

/// 更新盘点单请求模型
class InventoryCheckUpdateRequest {
  final String? remark;
  final int? status;

  InventoryCheckUpdateRequest({
    this.remark,
    this.status,
  });

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    if (remark != null) data['remark'] = remark;
    if (status != null) data['status'] = status;
    return data;
  }
}

/// 分页响应模型
class PaginatedInventoryCheckResponse {
  final List<InventoryCheck> items;
  final int total;
  final int page;
  final int pageSize;
  final int totalPages;

  PaginatedInventoryCheckResponse({
    required this.items,
    required this.total,
    required this.page,
    required this.pageSize,
    required this.totalPages,
  });

  factory PaginatedInventoryCheckResponse.fromJson(Map<String, dynamic> json) {
    return PaginatedInventoryCheckResponse(
      items: (json['data'] as List<dynamic>?)
          ?.map((item) => InventoryCheck.fromJson(item))
          .toList() ?? [],
      total: json['pagination']?['total'] ?? 0,
      page: json['pagination']?['page'] ?? 1,
      pageSize: json['pagination']?['page_size'] ?? 20,
      totalPages: json['pagination']?['pages'] ?? 1,
    );
  }
}
