import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/widgets/tab_manager.dart';
import '../../../core/utils/logger_service.dart';
import '../controllers/recycling_form_controller.dart';
import '../views/recycling_form_view.dart';
import '../presentation/recycling_list_page.dart';

/// 回收单管理标签页控制器
/// 负责管理回收单相关页面的标签页切换和生命周期
class RecyclingTabController extends GetxController {
  /// 标签页管理器
  late final TabManagerController tabManager;

  /// 缓存的标签页内容，避免重复创建
  final Map<String, Widget> _cachedTabContents = {};

  @override
  void onInit() {
    super.onInit();

    // 初始化标签页管理器
    tabManager = TabManagerController();

    // 添加默认的回收单列表标签页
    _addDefaultRecyclingListTab();

    LoggerService.d('RecyclingTabController 初始化完成');
  }

  @override
  void onClose() {
    // 清理缓存的标签页内容
    _cachedTabContents.clear();
    super.onClose();
  }

  /// 添加默认的回收单列表标签页
  void _addDefaultRecyclingListTab() {
    final tab = TabData(
      id: 'recycling_list',
      title: '回收单列表',
      icon: Icons.list_alt,
      content: const RecyclingListPage(),
      canClose: false, // 默认标签页不可关闭
    );
    tabManager.addTab(tab);
  }

  /// 打开新建回收单表单（在新标签页中）
  void openRecyclingForm() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final formTabId = 'recycling_form_$timestamp';

    try {
      // 为这个标签页创建专用的控制器实例
      final recyclingFormController = RecyclingFormController();
      Get.put<RecyclingFormController>(
        recyclingFormController,
        tag: formTabId,
        permanent: true,
      );

      LoggerService.d('✅ 成功注册回收单表单控制器: $formTabId');

      final tab = TabData(
        id: formTabId,
        title: '新建回收单',
        icon: Icons.recycling,
        content: _buildRecyclingFormWrapper(formTabId),
        canClose: true,
        onClose: () {
          // 标签页关闭时清理控制器
          try {
            if (Get.isRegistered<RecyclingFormController>(tag: formTabId)) {
              Get.delete<RecyclingFormController>(tag: formTabId);
              LoggerService.d('🗑️ 清理回收单表单控制器: $formTabId');
            }
          } catch (e) {
            LoggerService.w('清理回收单表单控制器失败: $e');
          }
        },
      );
      tabManager.addTab(tab);
    } catch (e) {
      LoggerService.e('❌ 注册回收单表单控制器失败: $e');

      Get.snackbar(
        '错误',
        '创建新建回收单失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 打开编辑回收单表单标签页
  void openRecyclingEditForm(int id) {
    final editFormTabId = 'recycling_edit_$id';

    // 检查是否已经存在该标签页
    final existingTabIndex = tabManager.tabs.indexWhere(
      (tab) => tab.id == editFormTabId,
    );
    if (existingTabIndex != -1) {
      // 如果标签页已存在，切换到该标签页
      tabManager.switchToTab(existingTabIndex);
      return;
    }

    try {
      // 为这个标签页创建专用的控制器实例
      final recyclingFormController = RecyclingFormController();
      Get.put<RecyclingFormController>(
        recyclingFormController,
        tag: editFormTabId,
        permanent: true,
      );

      // 设置编辑模式
      recyclingFormController.isEditing.value = true;

      LoggerService.d('✅ 成功注册回收单编辑表单控制器: $editFormTabId');

      final tab = TabData(
        id: editFormTabId,
        title: '编辑回收单',
        icon: Icons.edit_document,
        content: _buildRecyclingFormWrapper(editFormTabId),
        canClose: true,
        onClose: () {
          // 标签页关闭时清理控制器
          try {
            if (Get.isRegistered<RecyclingFormController>(tag: editFormTabId)) {
              Get.delete<RecyclingFormController>(tag: editFormTabId);
              LoggerService.d('🗑️ 清理回收单编辑表单控制器: $editFormTabId');
            }
          } catch (e) {
            LoggerService.w('清理回收单编辑表单控制器失败: $e');
          }
        },
      );
      tabManager.addTab(tab);

      // 如果需要，可以在这里加载编辑数据
      // recyclingFormController.loadRecyclingById(id);
    } catch (e) {
      LoggerService.e('❌ 注册回收单编辑表单控制器失败: $e');

      Get.snackbar(
        '错误',
        '打开编辑回收单失败: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// 构建回收单表单包装器
  Widget _buildRecyclingFormWrapper(String formTabId) {
    return Container(
      color: Colors.grey[50],
      child: RecyclingFormView(tag: formTabId),
    );
  }
}
