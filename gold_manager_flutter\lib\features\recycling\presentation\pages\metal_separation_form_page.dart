import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../../../../core/theme/app_colors.dart';
import '../../controllers/metal_separation_controller.dart';
import '../widgets/image_picker_widget.dart';

/// 金属分离记录表单页面
class MetalSeparationFormPage extends StatelessWidget {
  final MetalSeparationController controller;
  
  const MetalSeparationFormPage({
    super.key,
    required this.controller,
  });
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Obx(() => Text(controller.currentRecord.value?.id != null
            ? '编辑金属分离记录'
            : '新增金属分离记录')),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: '重置表单',
            onPressed: controller.resetForm,
          ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }
        
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SectionTitle(title: '原始重量'),
                Row(
                  children: [
                    Expanded(
                      child: WeightInputField(
                        label: '原金重(g)',
                        value: controller.originalGoldWeight.value,
                        onChanged: (value) {
                          controller.originalGoldWeight.value = value;
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: WeightInputField(
                        label: '原银重(g)',
                        value: controller.originalSilverWeight.value,
                        onChanged: (value) {
                          controller.originalSilverWeight.value = value;
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                
                const SectionTitle(title: '分离后重量'),
                Row(
                  children: [
                    Expanded(
                      child: WeightInputField(
                        label: '分离后金重(g)',
                        value: controller.separatedGoldWeight.value,
                        onChanged: (value) {
                          controller.separatedGoldWeight.value = value;
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: WeightInputField(
                        label: '分离后银重(g)',
                        value: controller.separatedSilverWeight.value,
                        onChanged: (value) {
                          controller.separatedSilverWeight.value = value;
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                
                const SectionTitle(title: '价格信息'),
                Row(
                  children: [
                    Expanded(
                      child: PriceInputField(
                        label: '金价(元/g)',
                        value: controller.goldPrice.value,
                        onChanged: (value) {
                          controller.goldPrice.value = value;
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: PriceInputField(
                        label: '银价(元/g)',
                        value: controller.silverPrice.value,
                        onChanged: (value) {
                          controller.silverPrice.value = value;
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                
                PriceInputField(
                  label: '分离成本(元)',
                  value: controller.separationCost.value,
                  onChanged: (value) {
                    controller.separationCost.value = value;
                  },
                ),
                const SizedBox(height: 16),
                
                TextFormField(
                  decoration: const InputDecoration(
                    labelText: '备注',
                    border: OutlineInputBorder(),
                    hintText: '可选',
                  ),
                  maxLines: 3,
                  onChanged: (value) {
                    controller.remark.value = value;
                  },
                  initialValue: controller.remark.value,
                ),
                const SizedBox(height: 24),

                // 添加图片选择器
                Container(
                  decoration: BoxDecoration(
                    color: AppColors.backgroundLight,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: AppColors.border),
                  ),
                  padding: const EdgeInsets.all(16),
                  child: Obx(() => ImagePickerWidget(
                    imageUrls: controller.imageUrls,
                    tempImages: controller.tempImages,
                    onPickImage: controller.pickImage,
                    onTakePhoto: controller.takePhoto,
                    onDeleteTempImage: controller.removeTempImage,
                    onDeleteUploadedImage: controller.removeUploadedImage,
                    isLoading: controller.isUploadingImage.value,
                  )),
                ),
                const SizedBox(height: 24),
                
                InfoCard(
                  title: '计算结果',
                  items: [
                    InfoItem(
                      label: '总损耗率',
                      value: '${controller.lossRate.toStringAsFixed(2)}%',
                    ),
                    InfoItem(
                      label: '分离后金值',
                      value: '¥${controller.goldValue.toStringAsFixed(2)}',
                      valueColor: AppColors.success,
                    ),
                    InfoItem(
                      label: '分离后银值',
                      value: '¥${controller.silverValue.toStringAsFixed(2)}',
                      valueColor: AppColors.info,
                    ),
                    InfoItem(
                      label: '分离后总价值',
                      value: '¥${controller.totalValue.toStringAsFixed(2)}',
                      valueColor: AppColors.primary,
                      isBold: true,
                    ),
                    InfoItem(
                      label: '利润',
                      value: '¥${controller.profit.toStringAsFixed(2)}',
                      valueColor: controller.profit >= 0 ? AppColors.success : AppColors.error,
                      isBold: true,
                    ),
                  ],
                ),
                const SizedBox(height: 32),
                
                SizedBox(
                  width: double.infinity,
                  height: 48,
                  child: ElevatedButton(
                    onPressed: controller.isSubmitting.value
                        ? null
                        : () => _submitForm(context),
                    child: controller.isSubmitting.value
                        ? const SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : Text(
                            controller.currentRecord.value?.id != null
                                ? '保存修改'
                                : '提交记录',
                            style: const TextStyle(fontSize: 16),
                          ),
                  ),
                ),
              ],
            ),
          ),
        );
      }),
    );
  }
  
  void _submitForm(BuildContext context) async {
    // 表单验证
    if (controller.originalGoldWeight.value <= 0 && controller.originalSilverWeight.value <= 0) {
      Get.snackbar('错误', '原金重和原银重不能同时为0', snackPosition: SnackPosition.BOTTOM);
      return;
    }
    
    if (controller.separatedGoldWeight.value < 0 || controller.separatedSilverWeight.value < 0) {
      Get.snackbar('错误', '分离后金重和银重不能为负数', snackPosition: SnackPosition.BOTTOM);
      return;
    }
    
    if (controller.goldPrice.value <= 0) {
      Get.snackbar('错误', '金价必须大于0', snackPosition: SnackPosition.BOTTOM);
      return;
    }
    
    if (controller.silverPrice.value <= 0) {
      Get.snackbar('错误', '银价必须大于0', snackPosition: SnackPosition.BOTTOM);
      return;
    }
    
    final success = await controller.submitSeparationRecord();
    if (success) {
      Get.back(result: true);
      Get.snackbar(
        '成功',
        controller.currentRecord.value?.id != null
            ? '金属分离记录已更新'
            : '金属分离记录已添加',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.success.withOpacity(0.8),
        colorText: Colors.white,
      );
    } else {
      Get.snackbar(
        '失败',
        '操作失败，请重试',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error.withOpacity(0.8),
        colorText: Colors.white,
      );
    }
  }
}

/// 分节标题
class SectionTitle extends StatelessWidget {
  final String title;
  
  const SectionTitle({
    super.key,
    required this.title,
  });
  
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: AppColors.textPrimary,
        ),
      ),
    );
  }
}

/// 重量输入字段
class WeightInputField extends StatelessWidget {
  final String label;
  final double value;
  final ValueChanged<double> onChanged;
  
  const WeightInputField({
    super.key,
    required this.label,
    required this.value,
    required this.onChanged,
  });
  
  @override
  Widget build(BuildContext context) {
    return TextFormField(
      decoration: InputDecoration(
        labelText: label,
        border: const OutlineInputBorder(
          borderRadius: BorderRadius.all(Radius.circular(12)),
        ),
        suffixText: 'g',
      ),
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,3}')),
      ],
      initialValue: value.toString(),
      onChanged: (value) {
        if (value.isEmpty) {
          onChanged(0.0);
        } else {
          onChanged(double.tryParse(value) ?? 0.0);
        }
      },
    );
  }
}

/// 价格输入字段
class PriceInputField extends StatelessWidget {
  final String label;
  final double value;
  final ValueChanged<double> onChanged;
  
  const PriceInputField({
    super.key,
    required this.label,
    required this.value,
    required this.onChanged,
  });
  
  @override
  Widget build(BuildContext context) {
    return TextFormField(
      decoration: InputDecoration(
        labelText: label,
        border: const OutlineInputBorder(
          borderRadius: BorderRadius.all(Radius.circular(12)),
        ),
        prefixText: '¥',
      ),
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
      ],
      initialValue: value.toString(),
      onChanged: (value) {
        if (value.isEmpty) {
          onChanged(0.0);
        } else {
          onChanged(double.tryParse(value) ?? 0.0);
        }
      },
    );
  }
}

/// 信息卡片
class InfoCard extends StatelessWidget {
  final String title;
  final List<InfoItem> items;
  
  const InfoCard({
    super.key,
    required this.title,
    required this.items,
  });
  
  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            const Divider(),
            ...items.map((item) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 4.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    item.label,
                    style: const TextStyle(
                      color: AppColors.textSecondary,
                    ),
                  ),
                  Text(
                    item.value,
                    style: TextStyle(
                      color: item.valueColor,
                      fontWeight: item.isBold ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                ],
              ),
            )),
          ],
        ),
      ),
    );
  }
}

/// 信息项
class InfoItem {
  final String label;
  final String value;
  final Color valueColor;
  final bool isBold;
  
  InfoItem({
    required this.label,
    required this.value,
    this.valueColor = AppColors.textPrimary,
    this.isBold = false,
  });
} 