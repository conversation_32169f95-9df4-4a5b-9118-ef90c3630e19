"""
库存盘点业务服务类

老板，这个服务类提供库存盘点的核心业务逻辑：
1. 盘点单的CRUD操作
2. 盘点流程管理
3. 商品盘点操作
4. 统计分析功能
5. 智能单号生成

完整的库存盘点业务逻辑实现。
"""

import time
from datetime import datetime
from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc

from ..models.inventory_check import InventoryCheck, InventoryCheckItem
from ..models.jewelry import Jewelry, JewelryCategory
from ..models.store import Store
from ..models.admin import Admin
from ..schemas.inventory_check import (
    InventoryCheckCreate, InventoryCheckUpdate, InventoryCheckResponse,
    InventoryCheckDetailResponse, InventoryCheckItemResponse,
    InventoryCheckStatistics, InventoryCheckQueryParams,
    InventoryCheckStatusUpdate, InventoryCheckItemCheck,
    InventoryCheckBatchCheck
)


class InventoryCheckService:
    """库存盘点服务类"""

    def __init__(self, db: Session):
        self.db = db

    def generate_check_no(self) -> str:
        """生成盘点单号"""
        now = datetime.now()
        prefix = f"CHK{now.year}{now.month:02d}{now.day:02d}"

        # 查询当天已有的盘点单数量
        today_start = int(datetime(now.year, now.month, now.day).timestamp())
        today_end = today_start + 86400  # 24小时

        count = self.db.query(InventoryCheck).filter(
            and_(
                InventoryCheck.createtime >= today_start,
                InventoryCheck.createtime < today_end
            )
        ).count()

        return f"{prefix}{count + 1:04d}"

    def create_inventory_check(self, check_data: InventoryCheckCreate, operator_id: int) -> InventoryCheckResponse:
        """创建盘点单"""
        # 验证门店是否存在
        store = self.db.query(Store).filter(Store.id == check_data.store_id).first()
        if not store:
            raise ValueError(f"门店ID {check_data.store_id} 不存在")

        # 验证操作员是否存在
        operator = self.db.query(Admin).filter(Admin.id == operator_id).first()
        if not operator:
            raise ValueError(f"操作员ID {operator_id} 不存在")

        # 验证商品是否存在
        jewelry_ids = [item.jewelry_id for item in check_data.items]
        existing_jewelry = self.db.query(Jewelry).filter(Jewelry.id.in_(jewelry_ids)).all()
        existing_ids = {j.id for j in existing_jewelry}

        for item in check_data.items:
            if item.jewelry_id not in existing_ids:
                raise ValueError(f"商品ID {item.jewelry_id} 不存在")

        # 创建盘点单
        current_time = int(time.time())
        check_no = self.generate_check_no()

        inventory_check = InventoryCheck(
            check_no=check_no,
            store_id=check_data.store_id,
            operator_id=operator_id,
            total_count=len(check_data.items),
            checked_count=0,
            difference_count=0,
            remark=check_data.remark,
            start_time=current_time,
            createtime=current_time,
            updatetime=current_time
        )

        self.db.add(inventory_check)
        self.db.flush()  # 获取ID

        # 创建盘点明细
        for item_data in check_data.items:
            jewelry = next(j for j in existing_jewelry if j.id == item_data.jewelry_id)

            check_item = InventoryCheckItem(
                check_id=inventory_check.id,
                jewelry_id=item_data.jewelry_id,
                barcode=item_data.barcode or jewelry.barcode,
                name=item_data.name or jewelry.name,
                category_id=item_data.category_id or jewelry.category_id,
                ring_size=item_data.ring_size or jewelry.ring_size,
                system_stock=item_data.system_stock,
                remark=item_data.remark,
                createtime=current_time
            )
            self.db.add(check_item)

        self.db.commit()
        return self._convert_to_response(inventory_check)

    def get_inventory_check_list(
        self,
        page: int = 1,
        page_size: int = 20,
        params: Optional[InventoryCheckQueryParams] = None
    ) -> Tuple[List[InventoryCheckResponse], int]:
        """获取盘点单列表"""
        try:
            # 🔧 修复：使用显式JOIN查询确保关联数据完整性
            from ..models.store import Store
            from ..models.admin import Admin

            # 基础查询，使用显式JOIN确保关联数据
            query = self.db.query(InventoryCheck).join(
                Store, InventoryCheck.store_id == Store.id
            ).join(
                Admin, InventoryCheck.operator_id == Admin.id
            ).options(
                joinedload(InventoryCheck.store),
                joinedload(InventoryCheck.operator)
            )

            # 应用筛选条件
            if params:
                if params.keyword:
                    query = query.filter(
                        or_(
                            InventoryCheck.check_no.like(f"%{params.keyword}%"),
                            InventoryCheck.remark.like(f"%{params.keyword}%")
                        )
                    )

                if params.store_id is not None:
                    query = query.filter(InventoryCheck.store_id == params.store_id)

                if params.status is not None:
                    query = query.filter(InventoryCheck.status == params.status)

                if params.operator_id is not None:
                    query = query.filter(InventoryCheck.operator_id == params.operator_id)

                if params.start_date:
                    try:
                        start_timestamp = int(datetime.strptime(params.start_date, "%Y-%m-%d").timestamp())
                        query = query.filter(InventoryCheck.createtime >= start_timestamp)
                    except Exception as e:
                        print(f"解析开始日期失败: {e}")

                if params.end_date:
                    try:
                        end_timestamp = int(datetime.strptime(params.end_date, "%Y-%m-%d").timestamp()) + 86400
                        query = query.filter(InventoryCheck.createtime < end_timestamp)
                    except Exception as e:
                        print(f"解析结束日期失败: {e}")

            # 获取总数
            total = query.count()

            # 分页查询
            checks = query.order_by(desc(InventoryCheck.createtime)).offset((page - 1) * page_size).limit(page_size).all()

            # 转换为响应模型
            check_responses = []
            for check in checks:
                try:
                    response = self._convert_to_response(check)
                    check_responses.append(response)
                except Exception as e:
                    print(f"转换盘点单响应失败: {e}")
                    continue

            return check_responses, total

        except Exception as e:
            print(f"获取盘点单列表失败: {e}")
            # 返回空结果而不是抛出异常
            return [], 0

    def get_inventory_check_by_id(self, check_id: int) -> Optional[InventoryCheckDetailResponse]:
        """根据ID获取盘点单详情"""
        check = self.db.query(InventoryCheck).options(
            joinedload(InventoryCheck.store),
            joinedload(InventoryCheck.operator),
            joinedload(InventoryCheck.items).joinedload(InventoryCheckItem.category),
            joinedload(InventoryCheck.items).joinedload(InventoryCheckItem.check_user)
        ).filter(InventoryCheck.id == check_id).first()

        if not check:
            return None

        return self._convert_to_detail_response(check)

    def get_inventory_check_by_no(self, check_no: str) -> Optional[InventoryCheckDetailResponse]:
        """根据单号获取盘点单详情"""
        check = self.db.query(InventoryCheck).options(
            joinedload(InventoryCheck.store),
            joinedload(InventoryCheck.operator),
            joinedload(InventoryCheck.items).joinedload(InventoryCheckItem.category),
            joinedload(InventoryCheck.items).joinedload(InventoryCheckItem.check_user)
        ).filter(InventoryCheck.check_no == check_no).first()

        if not check:
            return None

        return self._convert_to_detail_response(check)

    def get_inventory_check_items(
        self,
        check_id: int,
        page: int = 1,
        page_size: int = 100,
        status: Optional[int] = None
    ) -> Tuple[List[Dict[str, Any]], int]:
        """获取盘点明细列表"""
        try:
            # 基础查询，包含关联的商品分类和盘点员信息
            query = self.db.query(InventoryCheckItem).options(
                joinedload(InventoryCheckItem.category),
                joinedload(InventoryCheckItem.check_user)
            ).filter(InventoryCheckItem.check_id == check_id)

            # 状态筛选
            if status is not None:
                query = query.filter(InventoryCheckItem.status == status)

            # 获取总数
            total = query.count()

            # 分页查询
            items = query.order_by(InventoryCheckItem.id).offset((page - 1) * page_size).limit(page_size).all()

            # 转换为字典格式
            result_items = []
            for item in items:
                item_dict = {
                    "id": item.id,
                    "check_id": item.check_id,
                    "jewelry_id": item.jewelry_id,
                    "barcode": item.barcode,
                    "name": item.name,
                    "category_id": item.category_id,
                    "category_name": item.category.name if item.category else None,
                    "ring_size": item.ring_size,
                    "system_stock": item.system_stock,
                    "actual_stock": item.actual_stock,
                    "difference": item.difference,
                    "status": item.status,
                    "check_time": item.check_time,
                    "check_user_id": item.check_user_id,
                    "check_user_name": item.check_user.nickname if item.check_user else None,
                    "remark": item.remark,
                    "createtime": item.createtime
                }
                result_items.append(item_dict)

            return result_items, total

        except Exception as e:
            print(f"❌ 获取盘点明细列表失败: {e}")
            import traceback
            print(f"❌ 详细错误: {traceback.format_exc()}")
            return [], 0

    def update_inventory_check(self, check_id: int, update_data: InventoryCheckUpdate) -> Optional[InventoryCheckResponse]:
        """更新盘点单"""
        check = self.db.query(InventoryCheck).filter(InventoryCheck.id == check_id).first()
        if not check:
            return None

        # 只有进行中状态才能修改
        if check.status != 0:
            raise ValueError("只有进行中状态的盘点单才能修改")

        # 更新字段
        if update_data.remark is not None:
            check.remark = update_data.remark

        check.updatetime = int(time.time())

        self.db.commit()
        return self._convert_to_response(check)

    def delete_inventory_check(self, check_id: int) -> bool:
        """删除盘点单"""
        check = self.db.query(InventoryCheck).filter(InventoryCheck.id == check_id).first()
        if not check:
            return False

        # 只有进行中状态才能删除
        if check.status != 0:
            raise ValueError("只有进行中状态的盘点单才能删除")

        self.db.delete(check)
        self.db.commit()
        return True

    def update_check_status(self, check_id: int, status_data: InventoryCheckStatusUpdate) -> Optional[InventoryCheckResponse]:
        """更新盘点单状态"""
        check = self.db.query(InventoryCheck).filter(InventoryCheck.id == check_id).first()
        if not check:
            return None

        current_time = int(time.time())

        # 状态流转验证
        if status_data.status == 1:  # 完成盘点
            if check.status != 0:
                raise ValueError("只有进行中状态的盘点单才能完成")
            check.end_time = current_time
        elif status_data.status == 2:  # 取消盘点
            if check.status != 0:
                raise ValueError("只有进行中状态的盘点单才能取消")
            check.end_time = current_time

        check.status = status_data.status
        if status_data.remark:
            check.remark = status_data.remark
        check.updatetime = current_time

        self.db.commit()
        return self._convert_to_response(check)

    def check_item(self, check_id: int, item_id: int, check_data: InventoryCheckItemCheck, checker_id: int) -> bool:
        """盘点单个商品"""
        # 验证盘点单
        check = self.db.query(InventoryCheck).filter(InventoryCheck.id == check_id).first()
        if not check:
            raise ValueError(f"盘点单ID {check_id} 不存在")

        if check.status != 0:
            raise ValueError("只有进行中状态的盘点单才能进行盘点")

        # 验证盘点明细
        item = self.db.query(InventoryCheckItem).filter(
            and_(
                InventoryCheckItem.id == item_id,
                InventoryCheckItem.check_id == check_id
            )
        ).first()

        if not item:
            raise ValueError(f"盘点明细ID {item_id} 不存在")

        # 验证盘点员
        checker = self.db.query(Admin).filter(Admin.id == checker_id).first()
        if not checker:
            raise ValueError(f"盘点员ID {checker_id} 不存在")

        # 更新盘点明细
        current_time = int(time.time())
        item.actual_stock = check_data.actual_stock
        item.difference = check_data.actual_stock - item.system_stock
        item.status = 1  # 已盘点
        item.check_time = current_time
        item.check_user_id = checker_id
        if check_data.remark:
            item.remark = check_data.remark

        # 更新盘点单统计
        checked_count = self.db.query(InventoryCheckItem).filter(
            and_(
                InventoryCheckItem.check_id == check_id,
                InventoryCheckItem.status == 1
            )
        ).count()

        difference_count = self.db.query(InventoryCheckItem).filter(
            and_(
                InventoryCheckItem.check_id == check_id,
                InventoryCheckItem.difference != 0
            )
        ).count()

        check.checked_count = checked_count
        check.difference_count = difference_count
        check.updatetime = current_time

        self.db.commit()
        return True

    def batch_check_items(self, check_id: int, batch_data: InventoryCheckBatchCheck, checker_id: int) -> bool:
        """批量盘点商品"""
        # 验证盘点单
        check = self.db.query(InventoryCheck).filter(InventoryCheck.id == check_id).first()
        if not check:
            raise ValueError(f"盘点单ID {check_id} 不存在")

        if check.status != 0:
            raise ValueError("只有进行中状态的盘点单才能进行盘点")

        # 验证盘点员
        checker = self.db.query(Admin).filter(Admin.id == checker_id).first()
        if not checker:
            raise ValueError(f"盘点员ID {checker_id} 不存在")

        current_time = int(time.time())

        # 批量更新盘点明细
        for item_data in batch_data.items:
            item_id = item_data.get("item_id")
            actual_stock = item_data.get("actual_stock")
            remark = item_data.get("remark")

            if item_id is None or actual_stock is None:
                continue

            item = self.db.query(InventoryCheckItem).filter(
                and_(
                    InventoryCheckItem.id == item_id,
                    InventoryCheckItem.check_id == check_id
                )
            ).first()

            if item:
                item.actual_stock = actual_stock
                item.difference = actual_stock - item.system_stock
                item.status = 1  # 已盘点
                item.check_time = current_time
                item.check_user_id = checker_id
                if remark:
                    item.remark = remark

        # 更新盘点单统计
        checked_count = self.db.query(InventoryCheckItem).filter(
            and_(
                InventoryCheckItem.check_id == check_id,
                InventoryCheckItem.status == 1
            )
        ).count()

        difference_count = self.db.query(InventoryCheckItem).filter(
            and_(
                InventoryCheckItem.check_id == check_id,
                InventoryCheckItem.difference != 0
            )
        ).count()

        check.checked_count = checked_count
        check.difference_count = difference_count
        check.updatetime = current_time

        self.db.commit()
        return True

    def scan_check_item(self, check_id: int, barcode: str, remark: str, checker_id: int) -> Dict[str, Any]:
        """扫码盘点商品"""
        # 验证盘点单
        check = self.db.query(InventoryCheck).filter(InventoryCheck.id == check_id).first()
        if not check:
            raise ValueError(f"盘点单ID {check_id} 不存在")

        if check.status != 0:
            raise ValueError("只有进行中状态的盘点单才能进行盘点")

        # 验证盘点员
        checker = self.db.query(Admin).filter(Admin.id == checker_id).first()
        if not checker:
            raise ValueError(f"盘点员ID {checker_id} 不存在")

        # 在盘点明细中查找匹配的条码
        item = self.db.query(InventoryCheckItem).filter(
            and_(
                InventoryCheckItem.check_id == check_id,
                InventoryCheckItem.barcode == barcode
            )
        ).first()

        if not item:
            raise ValueError(f"条码 {barcode} 不在当前盘点单中")

        # 验证商品库存状态（检查fa_jewelry表中的status字段）
        from ..models.jewelry import Jewelry
        jewelry = self.db.query(Jewelry).filter(Jewelry.id == item.jewelry_id).first()

        if not jewelry:
            raise ValueError(f"商品不存在")

        if jewelry.status != 1:
            status_text = {0: "下架", 1: "上架在售", 2: "待出库"}.get(jewelry.status, "未知状态")
            raise ValueError(f"商品状态为 {status_text}，无法盘点。只能盘点上架在售的商品")

        # 检查是否已经盘点过
        if item.status == 1:
            # 已盘点，询问是否重新盘点
            pass  # 允许重新盘点

        # 更新盘点明细 - 扫码盘点自动设置实际库存为1
        current_time = int(time.time())
        item.actual_stock = 1  # 扫码盘点自动设置为1
        item.difference = 1 - item.system_stock
        item.status = 1  # 已盘点
        item.check_time = current_time
        item.check_user_id = checker_id
        item.remark = remark

        # 更新盘点单统计
        checked_count = self.db.query(InventoryCheckItem).filter(
            and_(
                InventoryCheckItem.check_id == check_id,
                InventoryCheckItem.status == 1
            )
        ).count()

        difference_count = self.db.query(InventoryCheckItem).filter(
            and_(
                InventoryCheckItem.check_id == check_id,
                InventoryCheckItem.difference != 0
            )
        ).count()

        check.checked_count = checked_count
        check.difference_count = difference_count
        check.updatetime = current_time

        self.db.commit()

        # 返回盘点结果
        return {
            "id": item.id,
            "check_id": item.check_id,
            "jewelry_id": item.jewelry_id,
            "barcode": item.barcode,
            "name": item.name,
            "category_id": item.category_id,
            "ring_size": item.ring_size,
            "system_stock": item.system_stock,
            "actual_stock": item.actual_stock,
            "difference": item.difference,
            "status": item.status,
            "check_time": item.check_time,
            "check_user_id": item.check_user_id,
            "remark": item.remark,
            "jewelry_status": jewelry.status,
            "jewelry_status_text": {0: "下架", 1: "上架在售", 2: "待出库"}.get(jewelry.status, "未知状态")
        }

    def get_statistics(self, store_id: Optional[int] = None) -> InventoryCheckStatistics:
        """获取盘点统计信息"""
        # 基础查询
        check_query = self.db.query(InventoryCheck)
        item_query = self.db.query(InventoryCheckItem).join(InventoryCheck)

        # 如果指定门店，添加筛选条件
        if store_id is not None:
            check_query = check_query.filter(InventoryCheck.store_id == store_id)
            item_query = item_query.filter(InventoryCheck.store_id == store_id)

        # 基础统计
        total_checks = check_query.count()
        in_progress_count = check_query.filter(InventoryCheck.status == 0).count()
        completed_count = check_query.filter(InventoryCheck.status == 1).count()
        cancelled_count = check_query.filter(InventoryCheck.status == 2).count()

        # 商品统计
        total_items = item_query.count()
        checked_items = item_query.filter(InventoryCheckItem.status == 1).count()
        difference_items = item_query.filter(InventoryCheckItem.difference != 0).count()

        # 状态分布
        status_distribution = {
            "进行中": in_progress_count,
            "已完成": completed_count,
            "已取消": cancelled_count
        }

        # 门店分布 - 简化查询，避免复杂JOIN
        try:
            if store_id is not None:
                # 如果指定门店，只返回该门店的统计
                store_distribution = [{"store_name": f"门店{store_id}", "count": total_checks}]
            else:
                # 简化门店分布查询，避免JOIN操作
                store_stats = self.db.query(
                    InventoryCheck.store_id,
                    func.count(InventoryCheck.id).label('count')
                ).group_by(InventoryCheck.store_id).all()

                store_distribution = [
                    {"store_name": f"门店{stat.store_id}", "count": stat.count}
                    for stat in store_stats
                ]
        except Exception as e:
            print(f"❌ 门店分布查询失败: {e}")
            store_distribution = []

        # 进度统计 - 修复查询问题
        if total_checks > 0:
            try:
                # 避免使用filter查询，直接获取所有记录然后在Python中过滤
                all_checks = check_query.all()
                checks_with_progress = [
                    check for check in all_checks
                    if check.total_count and check.total_count > 0
                ]

                if checks_with_progress:
                    total_progress = sum(
                        (check.checked_count / check.total_count * 100) if check.total_count > 0 else 0
                        for check in checks_with_progress
                    )
                    average_progress = total_progress / len(checks_with_progress)
                else:
                    average_progress = 0.0
            except Exception as e:
                print(f"❌ 进度计算失败: {e}")
                average_progress = 0.0

            completion_rate = (completed_count / total_checks) * 100
        else:
            average_progress = 0.0
            completion_rate = 0.0

        return InventoryCheckStatistics(
            total_checks=total_checks,
            in_progress_count=in_progress_count,
            completed_count=completed_count,
            cancelled_count=cancelled_count,
            total_items=total_items,
            checked_items=checked_items,
            difference_items=difference_items,
            status_distribution=status_distribution,
            store_distribution=store_distribution,
            average_progress=round(average_progress, 2),
            completion_rate=round(completion_rate, 2)
        )

    def _convert_to_response(self, check: InventoryCheck) -> InventoryCheckResponse:
        """转换为响应模型"""
        try:
            # 状态描述
            status_map = {0: "进行中", 1: "已完成", 2: "已取消"}
            status_text = status_map.get(check.status, "未知")

            # 计算进度百分比
            progress_percent = 0.0
            if check.total_count and check.total_count > 0:
                progress_percent = (check.checked_count / check.total_count) * 100

            # 安全获取关联数据
            store_name = "未知门店"
            operator_name = "未知"

            try:
                if hasattr(check, 'store') and check.store:
                    store_name = check.store.name
                else:
                    # 如果关联查询失败，手动查询门店信息
                    from app.models.store import Store
                    store = self.db.query(Store).filter(Store.id == check.store_id).first()
                    if store:
                        store_name = store.name
            except Exception as e:
                print(f"获取门店名称失败: {e}")

            try:
                if hasattr(check, 'operator') and check.operator:
                    operator_name = check.operator.nickname
                else:
                    # 如果关联查询失败，手动查询操作员信息
                    from app.models.admin import Admin
                    operator = self.db.query(Admin).filter(Admin.id == check.operator_id).first()
                    if operator:
                        operator_name = operator.nickname or operator.username
            except Exception as e:
                print(f"获取操作员名称失败: {e}")

            return InventoryCheckResponse(
                id=check.id,
                check_no=check.check_no,
                store_id=check.store_id,
                status=check.status,
                start_time=check.start_time,
                end_time=check.end_time,
                operator_id=check.operator_id,
                total_count=check.total_count or 0,
                checked_count=check.checked_count or 0,
                difference_count=check.difference_count or 0,
                remark=check.remark,
                createtime=check.createtime,
                updatetime=check.updatetime,
                store_name=store_name,
                operator_name=operator_name,
                status_text=status_text,
                progress_percent=round(progress_percent, 2)
            )
        except Exception as e:
            print(f"转换响应模型失败: {e}")
            raise

    def _convert_to_detail_response(self, check: InventoryCheck) -> InventoryCheckDetailResponse:
        """转换为详情响应模型"""
        try:
            # 基础信息
            base_response = self._convert_to_response(check)

            # 转换明细
            items = []
            if hasattr(check, 'items') and check.items:
                for item in check.items:
                    try:
                        # 安全获取关联数据
                        category_name = None
                        check_user_name = None

                        try:
                            if hasattr(item, 'category') and item.category:
                                category_name = item.category.name
                        except Exception as e:
                            print(f"获取分类名称失败: {e}")

                        try:
                            if hasattr(item, 'check_user') and item.check_user:
                                check_user_name = item.check_user.nickname
                        except Exception as e:
                            print(f"获取盘点员名称失败: {e}")

                        item_response = InventoryCheckItemResponse(
                            id=item.id,
                            check_id=item.check_id,
                            jewelry_id=item.jewelry_id,
                            barcode=item.barcode,
                            name=item.name,
                            category_id=item.category_id,
                            ring_size=item.ring_size,
                            status=item.status or 0,
                            system_stock=item.system_stock or 1,
                            actual_stock=item.actual_stock,
                            difference=item.difference,
                            check_time=item.check_time,
                            check_user_id=item.check_user_id,
                            remark=item.remark,
                            createtime=item.createtime,
                            category_name=category_name,
                            check_user_name=check_user_name
                        )
                        items.append(item_response)
                    except Exception as e:
                        print(f"转换盘点明细失败: {e}")
                        continue

            return InventoryCheckDetailResponse(
                **base_response.model_dump(),
                items=items
            )
        except Exception as e:
            print(f"转换详情响应模型失败: {e}")
            raise
